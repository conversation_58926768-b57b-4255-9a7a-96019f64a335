package com.android.video.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.widget.Button;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.android.video.R;
import com.android.video.utils.CacheValidationUtils;
import com.android.video.utils.CacheConsistencyChecker;
import com.android.video.manager.FragmentCacheManager;
import com.android.video.cache.ImprovedDataCacheManager;
import java.util.List;

/**
 * 缓存测试Activity
 * 用于测试和验证缓存功能的正确性
 * <AUTHOR> Team
 */
public class CacheTestActivity extends AppCompatActivity {
    
    private static final String TAG = "CacheTestActivity";
    
    private TextView tvTestResults;
    private Button btnRunTests;
    private Button btnClearCache;
    private Button btnShowStats;
    private Button btnCheckConsistency;
    private Button btnTestFeaturedCache;
    private Button btnTestPaginationCache;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_cache_test);
        
        initViews();
        setupClickListeners();
        
        Log.d(TAG, "CacheTestActivity created");
    }
    
    private void initViews() {
        tvTestResults = findViewById(R.id.tv_test_results);
        btnRunTests = findViewById(R.id.btn_run_tests);
        btnClearCache = findViewById(R.id.btn_clear_cache);
        btnShowStats = findViewById(R.id.btn_show_stats);
        btnCheckConsistency = findViewById(R.id.btn_check_consistency);
        btnTestFeaturedCache = findViewById(R.id.btn_test_featured_cache);
        btnTestPaginationCache = findViewById(R.id.btn_test_pagination_cache);

        tvTestResults.setText("点击按钮开始测试缓存功能");
    }
    
    private void setupClickListeners() {
        btnRunTests.setOnClickListener(v -> runCacheTests());
        btnClearCache.setOnClickListener(v -> clearAllCache());
        btnShowStats.setOnClickListener(v -> showCacheStats());
        btnCheckConsistency.setOnClickListener(v -> checkCacheConsistency());
        btnTestFeaturedCache.setOnClickListener(v -> testFeaturedCacheStrategy());
        btnTestPaginationCache.setOnClickListener(v -> testMyListPaginationCache());
    }
    
    private void runCacheTests() {
        tvTestResults.setText("正在运行缓存测试...");
        
        new Thread(() -> {
            StringBuilder results = new StringBuilder();
            results.append("=== 缓存功能测试结果 ===\n\n");
            
            // 测试Fragment缓存
            CacheValidationUtils.CacheValidationResult fragmentResult = 
                CacheValidationUtils.validateFragmentCache(this);
            results.append("Fragment缓存测试: ")
                   .append(fragmentResult.isValid ? "通过" : "失败")
                   .append("\n")
                   .append("详情: ").append(fragmentResult.message)
                   .append("\n")
                   .append("耗时: ").append(fragmentResult.totalValidationTime).append("ms")
                   .append("\n\n");
            
            // 测试数据缓存
            CacheValidationUtils.CacheValidationResult dataResult = 
                CacheValidationUtils.validateDataCache(this);
            results.append("数据缓存测试: ")
                   .append(dataResult.isValid ? "通过" : "失败")
                   .append("\n")
                   .append("详情: ").append(dataResult.message)
                   .append("\n")
                   .append("耗时: ").append(dataResult.totalValidationTime).append("ms")
                   .append("\n\n");
            
            // 测试缓存性能
            CacheValidationUtils.CacheValidationResult perfResult = 
                CacheValidationUtils.validateCachePerformance(this);
            results.append("缓存性能测试: ")
                   .append(perfResult.isValid ? "通过" : "失败")
                   .append("\n")
                   .append("详情: ").append(perfResult.message)
                   .append("\n")
                   .append("缓存命中: ").append(perfResult.cacheHitCount)
                   .append(", 缓存未命中: ").append(perfResult.cacheMissCount)
                   .append("\n")
                   .append("耗时: ").append(perfResult.totalValidationTime).append("ms")
                   .append("\n\n");
            
            // 获取缓存统计
            String stats = CacheValidationUtils.getCacheStatistics(this);
            results.append("缓存统计: ").append(stats).append("\n\n");
            
            // 总结
            boolean allPassed = fragmentResult.isValid && dataResult.isValid && perfResult.isValid;
            results.append("=== 测试总结 ===\n");
            results.append("整体结果: ").append(allPassed ? "全部通过" : "存在问题").append("\n");
            results.append("建议: ").append(allPassed ? "缓存功能正常" : "需要检查缓存实现");
            
            runOnUiThread(() -> {
                tvTestResults.setText(results.toString());
                Log.i(TAG, "缓存测试完成: " + (allPassed ? "成功" : "失败"));
            });
        }).start();
    }
    
    private void clearAllCache() {
        tvTestResults.setText("正在清除所有缓存...");
        
        new Thread(() -> {
            try {
                // 清除Fragment缓存状态
                FragmentCacheManager.getInstance().clearAllStates();

                // 清除原始数据缓存
                com.android.video.cache.DataCacheManager.getInstance(this).clearAllCache();

                // 清除改进的数据缓存
                ImprovedDataCacheManager.getInstance(this).clearAllCache();
                
                runOnUiThread(() -> {
                    tvTestResults.setText("所有缓存已清除\n\n建议重新启动应用以验证缓存功能");
                    Log.i(TAG, "所有缓存已清除");
                });
                
            } catch (Exception e) {
                Log.e(TAG, "清除缓存失败", e);
                runOnUiThread(() -> {
                    tvTestResults.setText("清除缓存失败: " + e.getMessage());
                });
            }
        }).start();
    }
    
    private void showCacheStats() {
        tvTestResults.setText("正在获取缓存统计信息...");
        
        new Thread(() -> {
            StringBuilder stats = new StringBuilder();
            stats.append("=== 缓存统计信息 ===\n\n");
            
            try {
                // Fragment缓存统计
                FragmentCacheManager fragmentManager = FragmentCacheManager.getInstance();
                var fragmentStates = fragmentManager.getAllFragmentStates();
                stats.append("Fragment缓存状态数量: ").append(fragmentStates.size()).append("\n");
                
                for (var entry : fragmentStates.entrySet()) {
                    stats.append("- ").append(entry.getKey()).append(": ")
                         .append(entry.getValue().toString()).append("\n");
                }
                stats.append("\n");
                
                // 数据缓存统计
                String cacheStats = CacheValidationUtils.getCacheStatistics(this);
                stats.append(cacheStats).append("\n\n");
                
                // 系统信息
                Runtime runtime = Runtime.getRuntime();
                long maxMemory = runtime.maxMemory() / 1024 / 1024; // MB
                long totalMemory = runtime.totalMemory() / 1024 / 1024; // MB
                long freeMemory = runtime.freeMemory() / 1024 / 1024; // MB
                long usedMemory = totalMemory - freeMemory;
                
                stats.append("=== 系统内存信息 ===\n");
                stats.append("最大内存: ").append(maxMemory).append(" MB\n");
                stats.append("已分配内存: ").append(totalMemory).append(" MB\n");
                stats.append("已使用内存: ").append(usedMemory).append(" MB\n");
                stats.append("可用内存: ").append(freeMemory).append(" MB\n");
                
            } catch (Exception e) {
                Log.e(TAG, "获取缓存统计失败", e);
                stats.append("获取统计信息失败: ").append(e.getMessage());
            }
            
            runOnUiThread(() -> {
                tvTestResults.setText(stats.toString());
                Log.i(TAG, "缓存统计信息已显示");
            });
        }).start();
    }

    private void checkCacheConsistency() {
        tvTestResults.setText("正在检查缓存一致性...");

        new Thread(() -> {
            StringBuilder results = new StringBuilder();
            results.append("=== 缓存一致性检查结果 ===\n\n");

            try {
                // 检查首页缓存一致性
                CacheConsistencyChecker.ConsistencyCheckResult homeResult =
                    CacheConsistencyChecker.checkHomeCacheConsistency(this);
                results.append("首页缓存一致性: ")
                       .append(homeResult.isConsistent ? "通过" : "存在问题")
                       .append("\n")
                       .append("详情: ").append(homeResult.message)
                       .append("\n");

                if (!homeResult.issues.isEmpty()) {
                    results.append("发现的问题:\n");
                    for (String issue : homeResult.issues) {
                        results.append("- ").append(issue).append("\n");
                    }
                }

                if (!homeResult.fixedIssues.isEmpty()) {
                    results.append("已修复的问题:\n");
                    for (String fixed : homeResult.fixedIssues) {
                        results.append("- ").append(fixed).append("\n");
                    }
                }
                results.append("\n");

                // 检查MyList缓存一致性
                CacheConsistencyChecker.ConsistencyCheckResult myListResult =
                    CacheConsistencyChecker.checkMyListCacheConsistency(this);
                results.append("MyList缓存一致性: ")
                       .append(myListResult.isConsistent ? "通过" : "存在问题")
                       .append("\n")
                       .append("详情: ").append(myListResult.message)
                       .append("\n");

                if (!myListResult.issues.isEmpty()) {
                    results.append("发现的问题:\n");
                    for (String issue : myListResult.issues) {
                        results.append("- ").append(issue).append("\n");
                    }
                }

                if (!myListResult.fixedIssues.isEmpty()) {
                    results.append("已修复的问题:\n");
                    for (String fixed : myListResult.fixedIssues) {
                        results.append("- ").append(fixed).append("\n");
                    }
                }
                results.append("\n");

                // 总结
                boolean overallConsistent = homeResult.isConsistent && myListResult.isConsistent;
                results.append("=== 检查总结 ===\n");
                results.append("整体一致性: ").append(overallConsistent ? "通过" : "存在问题").append("\n");
                results.append("建议: ");
                if (overallConsistent) {
                    results.append("缓存数据一致性良好");
                } else {
                    results.append("建议重启应用以确保缓存正常工作");
                }

            } catch (Exception e) {
                Log.e(TAG, "缓存一致性检查失败", e);
                results.append("检查过程中发生异常: ").append(e.getMessage());
            }

            runOnUiThread(() -> {
                tvTestResults.setText(results.toString());
                Log.i(TAG, "缓存一致性检查完成");
            });
        }).start();
    }

    /**
     * 测试Featured精细化缓存策略
     */
    private void testFeaturedCacheStrategy() {
        tvTestResults.setText("正在测试Featured精细化缓存策略...");

        new Thread(() -> {
            StringBuilder result = new StringBuilder();
            result.append("=== Featured缓存策略测试 ===\n\n");

            try {
                ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(this);

                // 测试命名空间功能
                List<String> featuredKeys = cacheManager.getCacheKeysByNamespace("home_featured");
                result.append("Featured缓存键数量: ").append(featuredKeys.size()).append("\n");

                for (String key : featuredKeys) {
                    boolean isValid = cacheManager.isCacheValid(key);
                    result.append("- ").append(key).append(": ").append(isValid ? "有效" : "无效").append("\n");
                }

                // 测试一致性检查
                CacheConsistencyChecker.ConsistencyCheckResult featuredCheck =
                    CacheConsistencyChecker.checkFeaturedCacheConsistency(this);
                result.append("\nFeatured缓存一致性检查:\n");
                result.append("- 检查项目: ").append(featuredCheck.totalChecks).append("\n");
                result.append("- 发现问题: ").append(featuredCheck.issues.size()).append("\n");
                result.append("- 修复问题: ").append(featuredCheck.fixedIssues.size()).append("\n");
                result.append("- 检查结果: ").append(featuredCheck.isConsistent ? "通过" : "失败").append("\n");
                result.append("- 详细信息: ").append(featuredCheck.message).append("\n");

            } catch (Exception e) {
                result.append("测试失败: ").append(e.getMessage()).append("\n");
                Log.e(TAG, "Featured缓存策略测试失败", e);
            }

            runOnUiThread(() -> tvTestResults.setText(result.toString()));
        }).start();
    }

    /**
     * 测试MyList分页缓存
     */
    private void testMyListPaginationCache() {
        tvTestResults.setText("正在测试MyList分页缓存...");

        new Thread(() -> {
            StringBuilder result = new StringBuilder();
            result.append("=== MyList分页缓存测试 ===\n\n");

            try {
                ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(this);

                String[] tabs = {"following", "history", "interest"};
                for (String tab : tabs) {
                    String cacheKey = String.format("mylist_%s_accumulated_data", tab);
                    boolean isValid = cacheManager.isCacheValid(cacheKey);
                    result.append(tab.toUpperCase()).append("累积缓存: ").append(cacheKey).append(" - ").append(isValid ? "有效" : "无效").append("\n");
                }

                // 测试累积缓存一致性
                CacheConsistencyChecker.ConsistencyCheckResult paginationCheck =
                    CacheConsistencyChecker.checkMyListPaginationCache(this);
                result.append("\n累积缓存一致性检查:\n");
                result.append("- 检查项目: ").append(paginationCheck.totalChecks).append("\n");
                result.append("- 发现问题: ").append(paginationCheck.issues.size()).append("\n");
                result.append("- 修复问题: ").append(paginationCheck.fixedIssues.size()).append("\n");
                result.append("- 检查结果: ").append(paginationCheck.isConsistent ? "通过" : "失败").append("\n");
                result.append("- 详细信息: ").append(paginationCheck.message).append("\n");

            } catch (Exception e) {
                result.append("测试失败: ").append(e.getMessage()).append("\n");
                Log.e(TAG, "MyList分页缓存测试失败", e);
            }

            runOnUiThread(() -> tvTestResults.setText(result.toString()));
        }).start();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "CacheTestActivity destroyed");
    }
}
