<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <!-- 标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Message"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 下拉刷新 + 消息详情卡片 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="@dimen/message_detail_card_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/message_detail_card_margin_top"
            android:layout_marginBottom="32dp">

            <!-- 消息详情卡片 -->
            <LinearLayout
                android:id="@+id/ll_message_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@drawable/message_detail_card_bg"
                android:orientation="vertical"
                android:padding="16dp">

            <!-- 上半部分：通知图标、标题和日期 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 通知图标 -->
                <ImageView
                    android:id="@+id/iv_notification_icon"
                    android:layout_width="@dimen/message_notification_icon_size"
                    android:layout_height="@dimen/message_notification_icon_size"
                    android:layout_marginEnd="@dimen/message_notification_icon_margin_end"
                    android:background="@drawable/message_notification_icon_bg"
                    android:src="@drawable/mess_ic_notice"
                    android:scaleType="center" />

                <!-- 标题和日期 -->
                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <!-- 标题 -->
                    <TextView
                        android:id="@+id/tv_message_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/message_title_text_size"
                        android:fontFamily="sans-serif" />

                    <!-- 日期时间 -->
                    <TextView
                        android:id="@+id/tv_date_time"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="#80ffffff"
                        android:textSize="@dimen/message_date_text_size"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

            </LinearLayout>

            <!-- 分隔线 -->
            <View
                android:layout_width="@dimen/message_detail_divider_width"
                android:layout_height="@dimen/message_detail_divider_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="@dimen/message_detail_divider_margin_vertical"
                android:layout_marginBottom="@dimen/message_detail_divider_margin_vertical"
                android:background="@drawable/message_detail_divider" />

            <!-- 详细内容 -->
            <TextView
                android:id="@+id/tv_detail_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:textColor="#ffffffff"
                android:textSize="@dimen/message_content_text_size"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="4dp" />

            </LinearLayout>

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</FrameLayout>
