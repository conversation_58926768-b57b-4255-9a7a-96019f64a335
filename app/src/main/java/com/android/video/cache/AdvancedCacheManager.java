package com.android.video.cache;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Environment;
import android.os.StatFs;
import android.util.Log;

import com.android.video.manager.ContentProtectionManager;

import java.io.File;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 高级缓存管理器
 * 功能：
 * 1. 自动清理7天内未浏览的预加载内容
 * 2. 缓存空间上限管理（设备总存储的20%）
 * 3. FIFO原则删除最早缓存的内容
 */
public class AdvancedCacheManager {
    private static final String TAG = "AdvancedCacheManager";
    
    // 缓存配置常量
    private static final long CACHE_EXPIRY_DAYS = 7; // 7天过期
    private static final long CACHE_EXPIRY_MILLIS = CACHE_EXPIRY_DAYS * 24 * 60 * 60 * 1000L;
    private static final float STORAGE_LIMIT_PERCENTAGE = 0.20f; // 20%的存储空间
    private static final String CACHE_ACCESS_PREFS = "cache_access_tracking";
    private static final String LAST_ACCESS_SUFFIX = "_last_access";
    private static final String CACHE_SIZE_SUFFIX = "_cache_size";

    // 内容保护配置常量
    private static final String CONTENT_PROTECTION_ENABLED = "content_protection_enabled";
    private static final String CONTENT_PROTECTION_LEVEL = "content_protection_level";
    
    // 单例实例
    private static volatile AdvancedCacheManager instance;
    private final Context context;
    private final ImprovedDataCacheManager dataCacheManager;
    private final SharedPreferences accessTrackingPrefs;
    private final ScheduledExecutorService scheduler;

    // 内容保护管理
    private ContentProtectionManager contentProtectionManager;
    
    private AdvancedCacheManager(Context context) {
        this.context = context.getApplicationContext();
        this.dataCacheManager = ImprovedDataCacheManager.getInstance(context);
        this.accessTrackingPrefs = context.getSharedPreferences(CACHE_ACCESS_PREFS, Context.MODE_PRIVATE);
        this.scheduler = Executors.newSingleThreadScheduledExecutor();
        
        // 启动定期清理任务
        startPeriodicCleanup();
    }
    
    public static AdvancedCacheManager getInstance(Context context) {
        if (instance == null) {
            synchronized (AdvancedCacheManager.class) {
                if (instance == null) {
                    instance = new AdvancedCacheManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 启动定期清理任务
     * 每天执行一次自动清理
     */
    private void startPeriodicCleanup() {
        scheduler.scheduleAtFixedRate(() -> {
            try {
                Log.d(TAG, "开始执行定期缓存清理");
                performAutomaticCleanup();
            } catch (Exception e) {
                Log.e(TAG, "定期缓存清理失败", e);
            }
        }, 1, 24, TimeUnit.HOURS); // 1小时后开始，每24小时执行一次
    }
    
    /**
     * 执行自动清理
     */
    public void performAutomaticCleanup() {
        Log.d(TAG, "开始自动缓存清理");
        
        // 1. 清理过期的缓存（7天未访问）
        cleanExpiredCache();
        
        // 2. 检查存储空间限制
        enforceStorageLimit();
        
        Log.d(TAG, "自动缓存清理完成");
    }
    
    /**
     * 清理过期的缓存（7天未访问）
     */
    private void cleanExpiredCache() {
        Log.d(TAG, "开始清理过期缓存");
        
        long currentTime = System.currentTimeMillis();
        List<String> expiredKeys = new ArrayList<>();
        
        // 获取所有访问记录
        Map<String, ?> allAccess = accessTrackingPrefs.getAll();
        
        for (Map.Entry<String, ?> entry : allAccess.entrySet()) {
            String key = entry.getKey();
            if (key.endsWith(LAST_ACCESS_SUFFIX)) {
                String cacheKey = key.replace(LAST_ACCESS_SUFFIX, "");
                long lastAccess = (Long) entry.getValue();
                
                // 检查是否过期
                if (currentTime - lastAccess > CACHE_EXPIRY_MILLIS) {
                    expiredKeys.add(cacheKey);
                }
            }
        }
        
        // 清理过期的缓存
        for (String expiredKey : expiredKeys) {
            clearCacheEntry(expiredKey);
            Log.d(TAG, "清理过期缓存: " + expiredKey);
        }
        
        Log.d(TAG, "清理过期缓存完成，共清理 " + expiredKeys.size() + " 个条目");
    }
    
    /**
     * 强制执行存储空间限制
     */
    private void enforceStorageLimit() {
        Log.d(TAG, "开始检查存储空间限制");
        
        // 获取设备总存储空间
        long totalStorage = getTotalStorageSpace();
        long storageLimit = (long) (totalStorage * STORAGE_LIMIT_PERCENTAGE);
        
        // 获取当前缓存大小
        long currentCacheSize = getCurrentCacheSize();
        
        Log.d(TAG, "存储空间检查 - 总空间: " + formatFileSize(totalStorage) + 
                   ", 限制: " + formatFileSize(storageLimit) + 
                   ", 当前缓存: " + formatFileSize(currentCacheSize));
        
        if (currentCacheSize <= storageLimit) {
            Log.d(TAG, "缓存大小在限制范围内");
            return;
        }
        
        // 需要清理缓存
        long targetSize = (long) (storageLimit * 0.8); // 清理到80%的限制大小
        long sizeToFree = currentCacheSize - targetSize;
        
        Log.d(TAG, "需要清理缓存，目标大小: " + formatFileSize(targetSize) + 
                   ", 需要释放: " + formatFileSize(sizeToFree));
        
        // 按FIFO原则清理
        cleanCacheByFIFO(sizeToFree);
    }
    
    /**
     * 按FIFO原则清理缓存
     */
    private void cleanCacheByFIFO(long sizeToFree) {
        Log.d(TAG, "开始按FIFO原则清理缓存，需要释放: " + formatFileSize(sizeToFree));
        
        // 获取所有缓存条目并按时间排序
        List<CacheEntry> cacheEntries = getAllCacheEntries();
        
        // 按最后访问时间排序（最早的在前面）
        Collections.sort(cacheEntries, Comparator.comparingLong(entry -> entry.lastAccessTime));
        
        long freedSize = 0;
        int clearedCount = 0;
        
        for (CacheEntry entry : cacheEntries) {
            if (freedSize >= sizeToFree) {
                break;
            }
            
            clearCacheEntry(entry.key);
            freedSize += entry.size;
            clearedCount++;
            
            Log.d(TAG, "FIFO清理缓存: " + entry.key + " (大小: " + formatFileSize(entry.size) + ")");
        }
        
        Log.d(TAG, "FIFO清理完成，清理了 " + clearedCount + " 个条目，释放了 " + formatFileSize(freedSize));
    }
    
    /**
     * 获取所有缓存条目信息
     */
    private List<CacheEntry> getAllCacheEntries() {
        List<CacheEntry> entries = new ArrayList<>();

        // 从访问跟踪记录中获取所有缓存键
        Map<String, ?> allAccess = accessTrackingPrefs.getAll();

        for (Map.Entry<String, ?> entry : allAccess.entrySet()) {
            String key = entry.getKey();
            if (key.endsWith(LAST_ACCESS_SUFFIX)) {
                String cacheKey = key.replace(LAST_ACCESS_SUFFIX, "");

                // 检查缓存是否仍然存在
                if (dataCacheManager.isCacheValid(cacheKey)) {
                    long lastAccess = (Long) entry.getValue();
                    long size = accessTrackingPrefs.getLong(cacheKey + CACHE_SIZE_SUFFIX, 0);

                    entries.add(new CacheEntry(cacheKey, lastAccess, size));
                }
            }
        }

        return entries;
    }
    
    /**
     * 清理单个缓存条目
     */
    private void clearCacheEntry(String key) {
        // 清理数据缓存
        dataCacheManager.clearCachedData(key);
        
        // 清理访问记录
        SharedPreferences.Editor editor = accessTrackingPrefs.edit();
        editor.remove(key + LAST_ACCESS_SUFFIX);
        editor.remove(key + CACHE_SIZE_SUFFIX);
        editor.apply();
    }
    
    /**
     * 记录缓存访问
     */
    public void recordCacheAccess(String key, long dataSize) {
        long currentTime = System.currentTimeMillis();
        
        SharedPreferences.Editor editor = accessTrackingPrefs.edit();
        editor.putLong(key + LAST_ACCESS_SUFFIX, currentTime);
        editor.putLong(key + CACHE_SIZE_SUFFIX, dataSize);
        editor.apply();
        
        Log.d(TAG, "记录缓存访问: " + key + " (大小: " + formatFileSize(dataSize) + ")");
    }
    
    /**
     * 获取设备总存储空间
     */
    private long getTotalStorageSpace() {
        try {
            StatFs stat = new StatFs(Environment.getDataDirectory().getPath());
            return stat.getTotalBytes();
        } catch (Exception e) {
            Log.e(TAG, "获取存储空间失败", e);
            return Long.MAX_VALUE; // 返回最大值以避免误删
        }
    }
    
    /**
     * 获取当前缓存大小
     */
    private long getCurrentCacheSize() {
        try {
            // 计算应用缓存目录大小
            long totalSize = 0;
            
            File cacheDir = context.getCacheDir();
            if (cacheDir != null && cacheDir.exists()) {
                totalSize += getDirectorySize(cacheDir);
            }
            
            File externalCacheDir = context.getExternalCacheDir();
            if (externalCacheDir != null && externalCacheDir.exists()) {
                totalSize += getDirectorySize(externalCacheDir);
            }
            
            // 添加SharedPreferences缓存大小
            ImprovedDataCacheManager.CacheStats stats = dataCacheManager.getCacheStats();
            totalSize += stats.totalDataSize;
            
            return totalSize;
        } catch (Exception e) {
            Log.e(TAG, "计算缓存大小失败", e);
            return 0;
        }
    }
    
    /**
     * 计算目录大小
     */
    private long getDirectorySize(File directory) {
        long size = 0;
        if (directory != null && directory.exists()) {
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        size += getDirectorySize(file);
                    } else {
                        size += file.length();
                    }
                }
            }
        }
        return size;
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size <= 0) return "0.0B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f%s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 缓存条目信息
     */
    private static class CacheEntry {
        final String key;
        final long lastAccessTime;
        final long size;
        
        CacheEntry(String key, long lastAccessTime, long size) {
            this.key = key;
            this.lastAccessTime = lastAccessTime;
            this.size = size;
        }
    }
    
    // ==================== 内容保护管理功能 ====================

    /**
     * 启用内容保护功能
     * @param context 上下文
     */
    public void enableContentProtection(Context context) {
        try {
            if (contentProtectionManager == null) {
                contentProtectionManager = ContentProtectionManager.getInstance();
            }

            // 保存配置状态到SharedPreferences
            accessTrackingPrefs.edit()
                .putBoolean(CONTENT_PROTECTION_ENABLED, true)
                .putLong(CONTENT_PROTECTION_ENABLED + "_timestamp", System.currentTimeMillis())
                .apply();

            Log.d(TAG, "Content protection enabled successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable content protection", e);
        }
    }

    /**
     * 禁用内容保护功能
     */
    public void disableContentProtection() {
        try {
            if (contentProtectionManager != null) {
                contentProtectionManager.disableProtection();
            }

            // 更新配置状态
            accessTrackingPrefs.edit()
                .putBoolean(CONTENT_PROTECTION_ENABLED, false)
                .putLong(CONTENT_PROTECTION_ENABLED + "_disabled_timestamp", System.currentTimeMillis())
                .apply();

            Log.d(TAG, "Content protection disabled successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to disable content protection", e);
        }
    }

    /**
     * 检查内容保护是否已启用
     * @return true表示已启用，false表示未启用
     */
    public boolean isContentProtectionEnabled() {
        return accessTrackingPrefs.getBoolean(CONTENT_PROTECTION_ENABLED, false);
    }

    /**
     * 获取内容保护管理器实例
     * 仅在内容保护已启用时返回有效实例
     * @return ContentProtectionManager实例，如果未启用则返回null
     */
    public ContentProtectionManager getContentProtectionManager() {
        if (isContentProtectionEnabled()) {
            if (contentProtectionManager == null) {
                contentProtectionManager = ContentProtectionManager.getInstance();
            }
            return contentProtectionManager;
        }
        return null;
    }

    /**
     * 设置内容保护级别
     * @param level 保护级别（预留接口，可扩展不同保护级别）
     */
    public void setContentProtectionLevel(int level) {
        accessTrackingPrefs.edit()
            .putInt(CONTENT_PROTECTION_LEVEL, level)
            .apply();

        Log.d(TAG, "Content protection level set to: " + level);
    }

    /**
     * 获取内容保护级别
     * @return 当前保护级别，默认为1
     */
    public int getContentProtectionLevel() {
        return accessTrackingPrefs.getInt(CONTENT_PROTECTION_LEVEL, 1);
    }

    /**
     * 获取内容保护配置信息
     * @return 包含配置信息的字符串
     */
    public String getContentProtectionInfo() {
        boolean enabled = isContentProtectionEnabled();
        int level = getContentProtectionLevel();
        long enabledTimestamp = accessTrackingPrefs.getLong(CONTENT_PROTECTION_ENABLED + "_timestamp", 0);

        return String.format("Content Protection - Enabled: %s, Level: %d, Since: %d",
                           enabled, level, enabledTimestamp);
    }

    /**
     * 重置内容保护配置
     */
    public void resetContentProtectionConfig() {
        try {
            // 先禁用内容保护
            disableContentProtection();

            // 清除所有相关配置
            SharedPreferences.Editor editor = accessTrackingPrefs.edit();
            editor.remove(CONTENT_PROTECTION_ENABLED);
            editor.remove(CONTENT_PROTECTION_LEVEL);
            editor.remove(CONTENT_PROTECTION_ENABLED + "_timestamp");
            editor.remove(CONTENT_PROTECTION_ENABLED + "_disabled_timestamp");
            editor.apply();

            contentProtectionManager = null;

            Log.d(TAG, "Content protection configuration reset");
        } catch (Exception e) {
            Log.e(TAG, "Failed to reset content protection configuration", e);
        }
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        // 清理内容保护资源
        if (contentProtectionManager != null) {
            contentProtectionManager.disableProtection();
            contentProtectionManager = null;
        }

        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
        }
    }
}
