package com.android.video.network;

import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.DiscoverApiConstantsUtils;
import com.android.video.model.response.WaterfallRecommendDataModel;
import com.android.video.model.response.WaterfallRecommendResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 瀑布流推荐API服务类
 * <p>
 * 提供瀑布流推荐相关的网络请求功能，包括获取推荐列表等。
 * 使用OkHttp进行网络请求，Gson进行JSON解析。
 * </p>
 * 
 * <p>
 * 主要功能：
 * <ul>
 *   <li>获取瀑布流推荐列表（支持分页）</li>
 *   <li>异步请求处理</li>
 *   <li>错误处理和重试机制</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * WaterfallRecommendApiService apiService = new WaterfallRecommendApiService();
 * apiService.getWaterfallRecommend(1, 10, new WaterfallRecommendCallback() {
 *     {@literal @}Override
 *     public void onSuccess(WaterfallRecommendDataModel data) {
 *         // 处理成功响应
 *     }
 *     
 *     {@literal @}Override
 *     public void onError(String errorMessage) {
 *         // 处理错误
 *     }
 * });
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallRecommendApiService {

    private static final String TAG = "WaterfallRecommendApiService";

    private final OkHttpClient httpClient;
    private final Gson gson;
    private final ExecutorService executorService;

    /**
     * 构造函数
     */
    public WaterfallRecommendApiService() {
        this.httpClient = ApiClientUtils.getHttpClient();
        this.gson = new Gson();
        this.executorService = Executors.newCachedThreadPool();
    }

    /**
     * 获取瀑布流推荐列表
     * <p>
     * 异步获取瀑布流推荐的短剧列表，支持分页。
     * </p>
     *
     * @param page 页码（从1开始）
     * @param size 每页数量（1-100）
     * @param callback 回调接口
     */
    public void getWaterfallRecommend(int page, int size, WaterfallRecommendCallback callback) {
        Log.d(TAG, "getWaterfallRecommend called with page=" + page + ", size=" + size);

        if (callback == null) {
            Log.e(TAG, "WaterfallRecommendCallback is null");
            return;
        }

        // 参数验证
        if (page < 1) {
            callback.onError("Page must be greater than 0");
            return;
        }

        if (size < 1 || size > 100) {
            callback.onError("Size must be between 1 and 100");
            return;
        }

        // 异步执行网络请求
        executorService.execute(() -> {
            try {
                // 构建请求URL
                String apiUrl = DiscoverApiConstantsUtils.buildWaterfallRecommendUrl(
                    EnvironmentConfigUtils.getApiBaseUrl(),
                    page,
                    size
                );

                // 获取请求头
                Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

                // 构建请求
                Request.Builder requestBuilder = new Request.Builder()
                    .url(apiUrl)
                    .get();

                // 添加请求头
                for (Map.Entry<String, String> header : headers.entrySet()) {
                    requestBuilder.addHeader(header.getKey(), header.getValue());
                }

                Request request = requestBuilder.build();

                Log.d(TAG, "发起瀑布流推荐请求: " + apiUrl);
                Log.d(TAG, "请求头: " + headers.toString());

                // 执行请求
                try (Response response = httpClient.newCall(request).execute()) {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "瀑布流推荐请求成功，响应长度: " + responseBody.length());

                        // 解析响应
                        parseWaterfallRecommendResponse(responseBody, callback);
                    } else {
                        String errorMsg = "HTTP " + response.code() + ": " + response.message();
                        Log.e(TAG, "瀑布流推荐请求失败: " + errorMsg);
                        callback.onError(errorMsg);
                    }
                }

            } catch (IOException e) {
                Log.e(TAG, "瀑布流推荐网络请求异常", e);
                callback.onError("Network error: " + e.getMessage());
            } catch (Exception e) {
                Log.e(TAG, "瀑布流推荐请求异常", e);
                callback.onError("Request error: " + e.getMessage());
            }
        });
    }

    /**
     * 解析瀑布流推荐响应
     * 
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseWaterfallRecommendResponse(String responseBody, WaterfallRecommendCallback callback) {
        try {
            // 解析JSON响应
            JsonObject jsonObject = JsonParser.parseString(responseBody).getAsJsonObject();
            
            // 检查响应码
            String code = jsonObject.has(BaseApiConstantsUtils.FIELD_CODE) 
                ? jsonObject.get(BaseApiConstantsUtils.FIELD_CODE).getAsString() 
                : "";
            
            String message = jsonObject.has(BaseApiConstantsUtils.FIELD_MESSAGE) 
                ? jsonObject.get(BaseApiConstantsUtils.FIELD_MESSAGE).getAsString() 
                : "";

            Log.d(TAG, "瀑布流推荐响应码: " + code + ", 消息: " + message);

            if (BaseApiConstantsUtils.isSuccessCode(code)) {
                // 成功响应，解析数据
                if (jsonObject.has(BaseApiConstantsUtils.FIELD_DATA)) {
                    JsonObject dataObject = jsonObject.getAsJsonObject(BaseApiConstantsUtils.FIELD_DATA);
                    WaterfallRecommendDataModel waterfallData = gson.fromJson(dataObject, WaterfallRecommendDataModel.class);
                    
                    Log.d(TAG, "瀑布流推荐数据解析成功: " + waterfallData.toString());
                    callback.onSuccess(waterfallData);
                } else {
                    Log.e(TAG, "瀑布流推荐响应中缺少data字段");
                    callback.onError("Response missing data field");
                }
            } else {
                // 错误响应
                String errorMessage = !message.isEmpty() ? message : "Unknown server error";
                Log.e(TAG, "瀑布流推荐服务器错误: " + errorMessage);
                callback.onError(errorMessage);
            }

        } catch (Exception e) {
            Log.e(TAG, "瀑布流推荐响应解析异常", e);
            callback.onError("Response parsing error: " + e.getMessage());
        }
    }

    /**
     * 瀑布流推荐回调接口
     */
    public interface WaterfallRecommendCallback {
        /**
         * 获取瀑布流推荐成功
         * @param waterfallData 瀑布流推荐数据
         */
        void onSuccess(WaterfallRecommendDataModel waterfallData);

        /**
         * 获取瀑布流推荐失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 释放资源
     */
    public void destroy() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
