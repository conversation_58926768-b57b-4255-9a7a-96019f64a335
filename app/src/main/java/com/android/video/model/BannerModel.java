package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * Banner数据模型
 * <p>
 * 用于映射API返回的Banner信息JSON数据结构。
 * 包含Banner的ID、名称、位置、语言类型、分类、封面、短剧ID和宣传片URL等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class BannerModel {

    /**
     * Banner ID
     */
    @SerializedName("bannerId")
    private String bannerId;

    /**
     * Banner名称
     */
    @SerializedName("bannerName")
    private String bannerName;

    /**
     * 所处位置
     * 1=首页(home), 2=分类详情
     */
    @SerializedName("location")
    private Integer location;

    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;

    /**
     * 分类ID
     * "all"表示所有分类
     */
    @SerializedName("categoryId")
    private String categoryId;

    /**
     * Banner封面图片URL
     */
    @SerializedName("bannerCover")
    private String bannerCover;

    /**
     * 关联的短剧ID
     */
    @SerializedName("filmId")
    private String filmId;

    /**
     * 宣传片URL
     */
    @SerializedName("promotionalUrl")
    private String promotionalUrl;

    /**
     * 短剧语言信息ID
     * 注意：这个字段可能在API响应中存在但之前没有定义
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;

    /**
     * 默认构造函数
     */
    public BannerModel() {
    }

    /**
     * 完整构造函数
     */
    public BannerModel(String bannerId, String bannerName, Integer location, Integer languageType,
                      String categoryId, String bannerCover, String filmId, String promotionalUrl) {
        this.bannerId = bannerId;
        this.bannerName = bannerName;
        this.location = location;
        this.languageType = languageType;
        this.categoryId = categoryId;
        this.bannerCover = bannerCover;
        this.filmId = filmId;
        this.promotionalUrl = promotionalUrl;
    }

    // ========== Getter和Setter方法 ==========

    public String getBannerId() {
        return bannerId;
    }

    public void setBannerId(String bannerId) {
        this.bannerId = bannerId;
    }

    public String getBannerName() {
        return bannerName;
    }

    public void setBannerName(String bannerName) {
        this.bannerName = bannerName;
    }

    public Integer getLocation() {
        return location;
    }

    public void setLocation(Integer location) {
        this.location = location;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getBannerCover() {
        return bannerCover;
    }

    public void setBannerCover(String bannerCover) {
        this.bannerCover = bannerCover;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getPromotionalUrl() {
        return promotionalUrl;
    }

    public void setPromotionalUrl(String promotionalUrl) {
        this.promotionalUrl = promotionalUrl;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否为首页Banner
     *
     * @return 如果是首页Banner返回true，否则返回false
     */
    public boolean isHomeBanner() {
        return location != null && location == 1;
    }

    /**
     * 检查是否为分类详情Banner
     *
     * @return 如果是分类详情Banner返回true，否则返回false
     */
    public boolean isCategoryBanner() {
        return location != null && location == 2;
    }

    /**
     * 获取位置类型文本
     *
     * @return 位置类型文本
     */
    public String getLocationText() {
        if (location == null) return "未知";
        return location == 1 ? "首页" : "分类详情";
    }

    /**
     * 获取语言类型文本
     *
     * @return 语言类型文本
     */
    public String getLanguageTypeText() {
        if (languageType == null) return "未知";
        return languageType == 1 ? "英语" : "俄语";
    }

    /**
     * 检查是否为全分类Banner
     *
     * @return 如果是全分类Banner返回true，否则返回false
     */
    public boolean isAllCategoryBanner() {
        return categoryId != null && categoryId.trim().toLowerCase().equals("all");
    }

    /**
     * 检查是否有宣传片
     *
     * @return 如果有宣传片返回true，否则返回false
     */
    public boolean hasPromotionalVideo() {
        return promotionalUrl != null && !promotionalUrl.trim().isEmpty();
    }

    /**
     * 检查是否有封面图片
     *
     * @return 如果有封面图片返回true，否则返回false
     */
    public boolean hasCoverImage() {
        return bannerCover != null && !bannerCover.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "BannerModel{" +
                "bannerId='" + bannerId + '\'' +
                ", bannerName='" + bannerName + '\'' +
                ", location=" + location +
                ", languageType=" + languageType +
                ", categoryId='" + categoryId + '\'' +
                ", bannerCover='" + bannerCover + '\'' +
                ", filmId='" + filmId + '\'' +
                ", promotionalUrl='" + promotionalUrl + '\'' +
                '}';
    }
}
