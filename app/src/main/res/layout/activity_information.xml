<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <!-- 顶部导航栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="#ff000000"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/bill_title_height"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Information"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 内容区域 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/information_content_margin_horizontal"
            android:layout_marginEnd="@dimen/information_content_margin_horizontal"
            android:layout_marginTop="@dimen/information_content_margin_top"
            android:layout_marginBottom="@dimen/information_content_margin_bottom">

            <!-- 加载状态 -->
            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh_loading"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:layout_gravity="center"
                android:layout_marginTop="@dimen/information_loading_margin_top"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            <!-- 内容文本 -->
            <TextView
                android:id="@+id/tv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Loading..."
                android:textColor="#ffffffff"
                android:textSize="@dimen/information_content_text_size"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/information_content_line_spacing"
                android:gravity="start"
                android:layout_gravity="top"
                android:visibility="visible" />

        </FrameLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

</FrameLayout>
