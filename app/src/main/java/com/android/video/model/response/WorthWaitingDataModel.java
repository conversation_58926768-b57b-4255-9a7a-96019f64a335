package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.model.WorthWaitingModel;
import com.android.video.model.VideoModel;
import com.android.video.constants.HomeApiConstantsUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 即将来袭推荐位分页数据模型
 * <p>
 * 用于映射即将来袭推荐位API返回的分页数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_WORTH_WAITING}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>records: 即将来袭推荐位记录列表</li>
 *   <li>total: 总记录数</li>
 *   <li>size: 每页数量</li>
 *   <li>current: 当前页码</li>
 *   <li>pages: 总页数</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * WorthWaitingDataModel dataModel = gson.fromJson(jsonObject, WorthWaitingDataModel.class);
 *
 * // 获取即将来袭数据
 * List&lt;WorthWaitingModel&gt; worthWaitingList = dataModel.getRecords();
 * boolean hasMore = dataModel.hasMorePages();
 * 
 * // 转换为VideoModel列表
 * List&lt;VideoModel&gt; videoList = dataModel.toVideoModelList();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_WORTH_WAITING
 * @see WorthWaitingModel
 */
public class WorthWaitingDataModel {
    
    /**
     * 即将来袭推荐位记录列表
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_RECORDS)
    private List<WorthWaitingModel> records;
    
    /**
     * 总记录数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_TOTAL)
    private int total;
    
    /**
     * 每页数量
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_SIZE)
    private int size;
    
    /**
     * 当前页码
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CURRENT)
    private int current;
    
    /**
     * 排序信息
     */
    @SerializedName("orders")
    private List<Object> orders;
    
    /**
     * 是否优化count SQL
     */
    @SerializedName("optimizeCountSql")
    private boolean optimizeCountSql;
    
    /**
     * 是否命中count
     */
    @SerializedName("hitCount")
    private boolean hitCount;
    
    /**
     * count ID
     */
    @SerializedName("countId")
    private String countId;
    
    /**
     * 最大限制
     */
    @SerializedName("maxLimit")
    private String maxLimit;
    
    /**
     * 是否搜索count
     */
    @SerializedName("searchCount")
    private boolean searchCount;
    
    /**
     * 总页数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_PAGES)
    private int pages;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WorthWaitingDataModel() {
        this.records = new ArrayList<>();
        this.orders = new ArrayList<>();
    }

    /**
     * 简化构造函数
     */
    public WorthWaitingDataModel(List<WorthWaitingModel> records, int total, int size, int current, int pages) {
        this.records = records != null ? records : new ArrayList<>();
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = pages;
        this.orders = new ArrayList<>();
        this.optimizeCountSql = true;
        this.hitCount = false;
        this.searchCount = true;
    }

    // ========== Getter和Setter方法 ==========

    public List<WorthWaitingModel> getRecords() {
        return records;
    }

    public void setRecords(List<WorthWaitingModel> records) {
        this.records = records != null ? records : new ArrayList<>();
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public List<Object> getOrders() {
        return orders;
    }

    public void setOrders(List<Object> orders) {
        this.orders = orders != null ? orders : new ArrayList<>();
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql;
    }

    public void setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
    }

    public boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(boolean hitCount) {
        this.hitCount = hitCount;
    }

    public String getCountId() {
        return countId;
    }

    public void setCountId(String countId) {
        this.countId = countId;
    }

    public String getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(String maxLimit) {
        this.maxLimit = maxLimit;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否有更多页面
     * @return 如果有更多页面则返回true
     */
    public boolean hasMorePages() {
        return current < pages;
    }

    /**
     * 检查是否为空数据
     * @return 如果没有记录则返回true
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取记录数量
     * @return 当前页面的记录数量
     */
    public int getRecordCount() {
        return records != null ? records.size() : 0;
    }

    /**
     * 获取下一页页码
     * @return 下一页页码，如果没有更多页面则返回-1
     */
    public int getNextPage() {
        return hasMorePages() ? current + 1 : -1;
    }

    /**
     * 转换为VideoModel列表
     * @return VideoModel列表
     */
    public List<VideoModel> toVideoModelList() {
        List<VideoModel> videoModels = new ArrayList<>();
        if (records != null) {
            for (WorthWaitingModel worthWaitingModel : records) {
                VideoModel videoModel = worthWaitingModel.toVideoModel();
                videoModels.add(videoModel);
            }
        }
        return videoModels;
    }

    /**
     * 添加记录到列表
     * @param worthWaitingModel 要添加的即将来袭记录
     */
    public void addRecord(WorthWaitingModel worthWaitingModel) {
        if (records == null) {
            records = new ArrayList<>();
        }
        records.add(worthWaitingModel);
    }

    /**
     * 合并其他页面的数据
     * @param otherData 其他页面的数据
     */
    public void mergeData(WorthWaitingDataModel otherData) {
        if (otherData != null && otherData.getRecords() != null) {
            if (records == null) {
                records = new ArrayList<>();
            }
            records.addAll(otherData.getRecords());
        }
    }

    /**
     * 获取已订阅的记录数量
     * @return 已订阅的记录数量
     */
    public int getSubscribedCount() {
        int count = 0;
        if (records != null) {
            for (WorthWaitingModel model : records) {
                if (model.isSubscribed()) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public String toString() {
        return "WorthWaitingDataModel{" +
                "records=" + (records != null ? records.size() : 0) + " items" +
                ", total=" + total +
                ", size=" + size +
                ", current=" + current +
                ", pages=" + pages +
                ", hasMorePages=" + hasMorePages() +
                ", subscribedCount=" + getSubscribedCount() +
                '}';
    }
}
