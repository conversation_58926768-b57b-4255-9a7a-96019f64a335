package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 下载进度上报响应数据模型
 * <p>
 * 用于映射下载进度上报API返回的JSON数据结构。
 * 包含响应码、响应消息和上报结果信息。
 * </p>
 *
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": "Report success."
 * }
 * </pre>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * DownloadProgressReportResponseModel response = gson.fromJson(jsonString, DownloadProgressReportResponseModel.class);
 * 
 * if (response.isSuccess()) {
 *     Log.d(TAG, "下载进度上报成功: " + response.getData());
 * } else {
 *     Log.e(TAG, "下载进度上报失败: " + response.getErrorMessage());
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadProgressReportResponseModel {

    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;

    /**
     * 上报结果数据
     * 通常为 "Report success." 或其他结果信息
     */
    @SerializedName("data")
    private String data;

    /**
     * 默认构造函数
     */
    public DownloadProgressReportResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 上报结果数据
     */
    public DownloadProgressReportResponseModel(String code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    // ========== 便利方法 ==========

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取错误信息
     *
     * @return 如果请求失败，返回错误信息；否则返回null
     */
    public String getErrorMessage() {
        return isSuccess() ? null : message;
    }

    /**
     * 检查是否上报成功
     *
     * @return 如果上报成功返回true，否则返回false
     */
    public boolean isReportSuccess() {
        return isSuccess() && data != null && data.toLowerCase().contains("success");
    }

    /**
     * 获取上报结果描述
     *
     * @return 上报结果描述信息
     */
    public String getReportResult() {
        if (isSuccess()) {
            return data != null ? data : "Report completed";
        } else {
            return "Report failed: " + (message != null ? message : "Unknown error");
        }
    }

    /**
     * 检查响应是否有效
     *
     * @return 如果响应数据有效返回true，否则返回false
     */
    public boolean isValidResponse() {
        return code != null && !code.trim().isEmpty()
                && message != null && !message.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "DownloadProgressReportResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                ", isSuccess=" + isSuccess() +
                ", isReportSuccess=" + isReportSuccess() +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        DownloadProgressReportResponseModel that = (DownloadProgressReportResponseModel) obj;

        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return data != null ? data.equals(that.data) : that.data == null;
    }

    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (data != null ? data.hashCode() : 0);
        return result;
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建成功响应的便利方法
     *
     * @param data 响应数据
     * @return 成功的响应模型
     */
    public static DownloadProgressReportResponseModel createSuccessResponse(String data) {
        return new DownloadProgressReportResponseModel("200", "success", data);
    }

    /**
     * 创建失败响应的便利方法
     *
     * @param code 错误码
     * @param message 错误消息
     * @return 失败的响应模型
     */
    public static DownloadProgressReportResponseModel createErrorResponse(String code, String message) {
        return new DownloadProgressReportResponseModel(code, message, null);
    }

    /**
     * 创建默认成功响应的便利方法
     *
     * @return 默认的成功响应模型
     */
    public static DownloadProgressReportResponseModel createDefaultSuccessResponse() {
        return createSuccessResponse("Report success.");
    }
}
