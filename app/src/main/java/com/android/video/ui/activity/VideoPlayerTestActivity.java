package com.android.video.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.Button;
import android.widget.ProgressBar;
import android.widget.ScrollView;
import android.widget.TextView;
import androidx.appcompat.app.AppCompatActivity;
import com.android.video.R;
import com.android.video.utils.VideoPlayerIntegrationTest;
import com.android.video.utils.VideoPlayerPerformanceMonitor;
import com.android.video.utils.VideoPlayerTestRunner;
import com.android.video.utils.VideoPlayerOptimizer;
import java.util.List;

/**
 * 视频播放器测试Activity
 * 用于演示和执行各种测试功能
 * <AUTHOR> Team
 */
public class VideoPlayerTestActivity extends BaseFullScreenActivity {

    private static final String TAG = "VideoPlayerTestActivity";

    // UI组件
    private Button btnRunIntegrationTest;
    private Button btnRunFullTestSuite;
    private Button btnStartPerformanceMonitor;
    private Button btnStopPerformanceMonitor;
    private Button btnRunOptimization;
    private Button btnGetOptimizationSuggestions;
    private Button btnClearResults;
    private ProgressBar progressBar;
    private TextView tvResults;
    private ScrollView scrollViewResults;

    // 测试工具
    private VideoPlayerTestRunner testRunner;
    private VideoPlayerPerformanceMonitor performanceMonitor;
    private VideoPlayerOptimizer optimizer;
    private boolean isMonitoring = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_player_test);

        initializeViews();
        initializeTestTools();
        setupClickListeners();
    }

    /**
     * 初始化视图
     */
    private void initializeViews() {
        btnRunIntegrationTest = findViewById(R.id.btnRunIntegrationTest);
        btnRunFullTestSuite = findViewById(R.id.btnRunFullTestSuite);
        btnStartPerformanceMonitor = findViewById(R.id.btnStartPerformanceMonitor);
        btnStopPerformanceMonitor = findViewById(R.id.btnStopPerformanceMonitor);
        btnRunOptimization = findViewById(R.id.btnRunOptimization);
        btnGetOptimizationSuggestions = findViewById(R.id.btnGetOptimizationSuggestions);
        btnClearResults = findViewById(R.id.btnClearResults);
        progressBar = findViewById(R.id.progressBar);
        tvResults = findViewById(R.id.tvResults);
        scrollViewResults = findViewById(R.id.scrollViewResults);

        // 初始状态
        progressBar.setVisibility(View.GONE);
        btnStopPerformanceMonitor.setEnabled(false);
    }

    /**
     * 初始化测试工具
     */
    private void initializeTestTools() {
        testRunner = new VideoPlayerTestRunner(this);
        performanceMonitor = VideoPlayerPerformanceMonitor.getInstance(this);
        optimizer = new VideoPlayerOptimizer(this);
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        btnRunIntegrationTest.setOnClickListener(v -> runIntegrationTest());
        btnRunFullTestSuite.setOnClickListener(v -> runFullTestSuite());
        btnStartPerformanceMonitor.setOnClickListener(v -> startPerformanceMonitoring());
        btnStopPerformanceMonitor.setOnClickListener(v -> stopPerformanceMonitoring());
        btnRunOptimization.setOnClickListener(v -> runOptimization());
        btnGetOptimizationSuggestions.setOnClickListener(v -> getOptimizationSuggestions());
        btnClearResults.setOnClickListener(v -> clearResults());
    }

    /**
     * 运行集成测试
     */
    private void runIntegrationTest() {
        showProgress("运行集成测试...");
        
        new Thread(() -> {
            try {
                VideoPlayerIntegrationTest integrationTest = new VideoPlayerIntegrationTest(this);
                VideoPlayerIntegrationTest.TestResults results = integrationTest.runFullIntegrationTest();
                
                runOnUiThread(() -> {
                    hideProgress();
                    displayIntegrationTestResults(results);
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Integration test failed", e);
                runOnUiThread(() -> {
                    hideProgress();
                    appendResult("集成测试失败: " + e.getMessage());
                });
            }
        }).start();
    }

    /**
     * 运行完整测试套件
     */
    private void runFullTestSuite() {
        showProgress("运行完整测试套件...");
        
        testRunner.runFullTestSuite(result -> {
            runOnUiThread(() -> {
                hideProgress();
                displayFullTestResults(result);
            });
        });
    }

    /**
     * 开始性能监控
     */
    private void startPerformanceMonitoring() {
        if (!isMonitoring) {
            performanceMonitor.startMonitoring();
            isMonitoring = true;
            
            btnStartPerformanceMonitor.setEnabled(false);
            btnStopPerformanceMonitor.setEnabled(true);
            
            appendResult("性能监控已开始...");
        }
    }

    /**
     * 停止性能监控
     */
    private void stopPerformanceMonitoring() {
        if (isMonitoring) {
            performanceMonitor.stopMonitoring();
            isMonitoring = false;
            
            btnStartPerformanceMonitor.setEnabled(true);
            btnStopPerformanceMonitor.setEnabled(false);
            
            // 获取性能报告
            VideoPlayerPerformanceMonitor.PerformanceReport report = performanceMonitor.getPerformanceReport();
            displayPerformanceReport(report);
        }
    }

    /**
     * 运行优化
     */
    private void runOptimization() {
        showProgress("执行自动优化...");
        
        new Thread(() -> {
            try {
                VideoPlayerOptimizer.OptimizationResult result = optimizer.performAutoOptimization();
                
                runOnUiThread(() -> {
                    hideProgress();
                    displayOptimizationResults(result);
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Optimization failed", e);
                runOnUiThread(() -> {
                    hideProgress();
                    appendResult("优化失败: " + e.getMessage());
                });
            }
        }).start();
    }

    /**
     * 获取优化建议
     */
    private void getOptimizationSuggestions() {
        showProgress("获取优化建议...");
        
        new Thread(() -> {
            try {
                List<VideoPlayerOptimizer.OptimizationSuggestion> suggestions = optimizer.getOptimizationSuggestions();
                
                runOnUiThread(() -> {
                    hideProgress();
                    displayOptimizationSuggestions(suggestions);
                });
                
            } catch (Exception e) {
                Log.e(TAG, "Failed to get optimization suggestions", e);
                runOnUiThread(() -> {
                    hideProgress();
                    appendResult("获取优化建议失败: " + e.getMessage());
                });
            }
        }).start();
    }

    /**
     * 清空结果
     */
    private void clearResults() {
        tvResults.setText("");
    }

    /**
     * 显示集成测试结果
     */
    private void displayIntegrationTestResults(VideoPlayerIntegrationTest.TestResults results) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 集成测试结果 ===\n");
        sb.append(results.getSummary()).append("\n\n");
        
        if (!results.successes.isEmpty()) {
            sb.append("成功项目:\n");
            for (String success : results.successes) {
                sb.append("✓ ").append(success).append("\n");
            }
            sb.append("\n");
        }
        
        if (!results.warnings.isEmpty()) {
            sb.append("警告项目:\n");
            for (String warning : results.warnings) {
                sb.append("⚠ ").append(warning).append("\n");
            }
            sb.append("\n");
        }
        
        if (!results.errors.isEmpty()) {
            sb.append("错误项目:\n");
            for (String error : results.errors) {
                sb.append("✗ ").append(error).append("\n");
            }
            sb.append("\n");
        }
        
        appendResult(sb.toString());
    }

    /**
     * 显示完整测试结果
     */
    private void displayFullTestResults(VideoPlayerTestRunner.TestSuiteResult result) {
        String report = testRunner.generateTestReport(result);
        appendResult(report);
    }

    /**
     * 显示性能报告
     */
    private void displayPerformanceReport(VideoPlayerPerformanceMonitor.PerformanceReport report) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 性能监控报告 ===\n");
        sb.append(report.generateSummary()).append("\n");
        
        appendResult(sb.toString());
    }

    /**
     * 显示优化结果
     */
    private void displayOptimizationResults(VideoPlayerOptimizer.OptimizationResult result) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 优化结果 ===\n");
        sb.append(result.getSummary()).append("\n\n");
        
        if (!result.optimizations.isEmpty()) {
            sb.append("已应用的优化:\n");
            for (String optimization : result.optimizations) {
                sb.append("✓ ").append(optimization).append("\n");
            }
            sb.append("\n");
        }
        
        if (!result.errors.isEmpty()) {
            sb.append("优化错误:\n");
            for (String error : result.errors) {
                sb.append("✗ ").append(error).append("\n");
            }
            sb.append("\n");
        }
        
        appendResult(sb.toString());
    }

    /**
     * 显示优化建议
     */
    private void displayOptimizationSuggestions(List<VideoPlayerOptimizer.OptimizationSuggestion> suggestions) {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 优化建议 ===\n");
        
        if (suggestions.isEmpty()) {
            sb.append("暂无优化建议\n");
        } else {
            for (VideoPlayerOptimizer.OptimizationSuggestion suggestion : suggestions) {
                sb.append(suggestion.toString()).append("\n");
            }
        }
        sb.append("\n");
        
        appendResult(sb.toString());
    }

    /**
     * 显示进度
     */
    private void showProgress(String message) {
        progressBar.setVisibility(View.VISIBLE);
        appendResult(message + "\n");
        
        // 禁用所有按钮
        setButtonsEnabled(false);
    }

    /**
     * 隐藏进度
     */
    private void hideProgress() {
        progressBar.setVisibility(View.GONE);
        
        // 启用所有按钮
        setButtonsEnabled(true);
        
        // 性能监控按钮状态特殊处理
        btnStartPerformanceMonitor.setEnabled(!isMonitoring);
        btnStopPerformanceMonitor.setEnabled(isMonitoring);
    }

    /**
     * 设置按钮启用状态
     */
    private void setButtonsEnabled(boolean enabled) {
        btnRunIntegrationTest.setEnabled(enabled);
        btnRunFullTestSuite.setEnabled(enabled);
        btnRunOptimization.setEnabled(enabled);
        btnGetOptimizationSuggestions.setEnabled(enabled);
        btnClearResults.setEnabled(enabled);
    }

    /**
     * 追加结果文本
     */
    private void appendResult(String text) {
        tvResults.append(text);
        
        // 滚动到底部
        scrollViewResults.post(() -> scrollViewResults.fullScroll(View.FOCUS_DOWN));
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        
        // 清理资源
        if (testRunner != null) {
            testRunner.cleanup();
        }
        
        if (isMonitoring) {
            performanceMonitor.stopMonitoring();
        }
    }
}
