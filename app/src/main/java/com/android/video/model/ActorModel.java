package com.android.video.model;

import java.io.Serializable;

/**
 * 演员数据模型类
 * <AUTHOR>
 */
public class ActorModel implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private String name;
    private String avatarUrl;
    private String role;
    private String biography;

    /**
     * 默认构造函数
     */
    public ActorModel() {
        this.id = "";
        this.name = "";
        this.avatarUrl = "";
        this.role = "";
        this.biography = "";
    }

    /**
     * 完整构造函数
     */
    public ActorModel(String id, String name, String avatarUrl, String role, String biography) {
        this.id = id;
        this.name = name;
        this.avatarUrl = avatarUrl;
        this.role = role;
        this.biography = biography;
    }

    /**
     * 便利构造函数 - 基本信息
     */
    public ActorModel(String id, String name, String avatarUrl) {
        this.id = id;
        this.name = name;
        this.avatarUrl = avatarUrl;
        this.role = "";
        this.biography = "";
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getRole() {
        return role;
    }
    
    public void setRole(String role) {
        this.role = role;
    }
    
    public String getBiography() {
        return biography;
    }
    
    public void setBiography(String biography) {
        this.biography = biography;
    }

    /**
     * 便利方法
     */
    public String getDisplayName() {
        return name != null && !name.isEmpty() ? name : "Unknown Actor";
    }
    
    public boolean isEmpty() {
        return name == null || name.trim().isEmpty();
    }
    
    public boolean hasAvatar() {
        return avatarUrl != null && !avatarUrl.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "ActorModel{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", role='" + role + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        ActorModel that = (ActorModel) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
