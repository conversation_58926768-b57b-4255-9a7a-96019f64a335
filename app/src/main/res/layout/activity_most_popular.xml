<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FF000000">

    <!-- 顶部渐变背景阴影 -->
    <View
        android:id="@+id/view_gradient_background"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:background="@drawable/most_popular_gradient_background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:src="@drawable/login_btn_back"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:contentDescription="返回"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Most popular"
        android:textColor="#FFFFFF"
        android:textSize="18sp"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="@id/iv_back"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 下拉刷新容器 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginTop="30dp"
        app:layout_constraintTop_toBottomOf="@id/iv_back"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Most Popular列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_most_popular"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="16dp" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
