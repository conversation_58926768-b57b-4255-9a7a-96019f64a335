package com.android.video.model;

import java.io.Serializable;

/**
 * 下载视频数据模型
 * <AUTHOR>
 */
public class DownloadVideo implements Serializable {
    private static final long serialVersionUID = 1L;
    private String id;
    private String title;
    private String posterUrl;
    private String filmLanguageInfoId; // 短剧语言信息ID
    private int downloadedEpisodes;
    private int totalEpisodes;
    private String downloadDate;
    private long fileSize; // 文件大小（字节）
    private String filePath; // 本地文件路径
    private boolean isDownloading; // 是否正在下载
    private int downloadProgress; // 下载进度 (0-100)

    public DownloadVideo() {
    }

    public DownloadVideo(String id, String title, String posterUrl, int downloadedEpisodes, int totalEpisodes) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.downloadedEpisodes = downloadedEpisodes;
        this.totalEpisodes = totalEpisodes;
        this.downloadDate = "";
        this.fileSize = 0;
        this.filePath = "";
        this.isDownloading = false;
        this.downloadProgress = 100; // 默认已下载完成
    }

    public DownloadVideo(String id, String title, String posterUrl, int downloadedEpisodes,
                        int totalEpisodes, String downloadDate, long fileSize, String filePath) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.downloadedEpisodes = downloadedEpisodes;
        this.totalEpisodes = totalEpisodes;
        this.downloadDate = downloadDate;
        this.fileSize = fileSize;
        this.filePath = filePath;
        this.isDownloading = false;
        this.downloadProgress = 100;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public int getDownloadedEpisodes() {
        return downloadedEpisodes;
    }

    public void setDownloadedEpisodes(int downloadedEpisodes) {
        this.downloadedEpisodes = downloadedEpisodes;
    }

    public int getTotalEpisodes() {
        return totalEpisodes;
    }

    public void setTotalEpisodes(int totalEpisodes) {
        this.totalEpisodes = totalEpisodes;
    }

    public String getDownloadDate() {
        return downloadDate;
    }

    public void setDownloadDate(String downloadDate) {
        this.downloadDate = downloadDate;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public boolean isDownloading() {
        return isDownloading;
    }

    public void setDownloading(boolean downloading) {
        isDownloading = downloading;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSize < 1024) {
            return fileSize + " B";
        } else if (fileSize < 1024 * 1024) {
            return String.format("%.1f KB", fileSize / 1024.0);
        } else if (fileSize < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSize / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSize / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取下载进度文本
     */
    public String getDownloadProgressText() {
        return "EP." + downloadedEpisodes + "/EP." + totalEpisodes;
    }

    /**
     * 是否已完全下载
     */
    public boolean isFullyDownloaded() {
        return downloadedEpisodes >= totalEpisodes && downloadProgress >= 100;
    }

    /**
     * 获取视频URL（用于播放）
     */
    public String getVideoUrl() {
        return filePath; // 返回本地文件路径作为视频URL
    }

    /**
     * 获取缩略图URL
     */
    public String getThumbnailUrl() {
        return posterUrl;
    }

    /**
     * 获取视频描述
     */
    public String getDescription() {
        return "Downloaded video: " + title;
    }

    /**
     * 获取视频时长（默认值）
     */
    public String getDuration() {
        return "00:00"; // 默认时长，实际应该从文件中获取
    }

    /**
     * 获取观看进度（默认值）
     */
    public float getWatchProgress() {
        return 0.0f; // 默认观看进度
    }

    /**
     * 获取分类
     */
    public String getCategory() {
        return "Downloaded"; // 默认分类
    }
}
