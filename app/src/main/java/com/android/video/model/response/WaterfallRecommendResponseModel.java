package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 瀑布流推荐响应数据模型
 * <p>
 * 用于映射瀑布流推荐API返回的完整JSON响应结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "records": [...],
 *     "total": 4,
 *     "size": 10,
 *     "current": 1,
 *     "pages": 1
 *   }
 * }
 * </pre>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * WaterfallRecommendResponseModel response = gson.fromJson(jsonString, WaterfallRecommendResponseModel.class);
 *
 * // 检查响应是否成功
 * if (response.isSuccess()) {
 *     WaterfallRecommendDataModel data = response.getData();
 *     List&lt;VideoModel&gt; videoList = data.toVideoModelList();
 *     // 处理视频列表...
 * } else {
 *     // 处理错误...
 *     String errorMessage = response.getMessage();
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallRecommendResponseModel {
    
    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 响应数据
     * 包含瀑布流推荐的分页数据
     */
    @SerializedName("data")
    private WaterfallRecommendDataModel data;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallRecommendResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data 响应数据
     */
    public WaterfallRecommendResponseModel(String code, String message, WaterfallRecommendDataModel data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    /**
     * 获取响应码
     * @return 响应码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置响应码
     * @param code 响应码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取响应消息
     * @return 响应消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 设置响应消息
     * @param message 响应消息
     */
    public void setMessage(String message) {
        this.message = message;
    }

    /**
     * 获取响应数据
     * @return 响应数据
     */
    public WaterfallRecommendDataModel getData() {
        return data;
    }

    /**
     * 设置响应数据
     * @param data 响应数据
     */
    public void setData(WaterfallRecommendDataModel data) {
        this.data = data;
    }

    // ========== 工具方法 ==========

    /**
     * 检查响应是否成功
     * @return 如果响应成功则返回true
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 检查是否有数据
     * @return 如果有数据则返回true
     */
    public boolean hasData() {
        return data != null && !data.isEmpty();
    }

    /**
     * 获取错误信息
     * @return 错误信息，如果成功则返回null
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "Unknown error";
    }

    /**
     * 获取数据记录数量
     * @return 数据记录数量，如果没有数据则返回0
     */
    public int getRecordCount() {
        return data != null ? data.getRecordCount() : 0;
    }

    /**
     * 检查是否有更多页面
     * @return 如果有更多页面则返回true
     */
    public boolean hasMorePages() {
        return data != null && data.hasMorePages();
    }

    /**
     * 获取下一页页码
     * @return 下一页页码，如果没有更多页面则返回-1
     */
    public int getNextPage() {
        return data != null ? data.getNextPage() : -1;
    }

    /**
     * 获取当前页码
     * @return 当前页码，如果没有数据则返回0
     */
    public int getCurrentPage() {
        return data != null ? data.getCurrent() : 0;
    }

    /**
     * 获取总页数
     * @return 总页数，如果没有数据则返回0
     */
    public int getTotalPages() {
        return data != null ? data.getPages() : 0;
    }

    /**
     * 获取总记录数
     * @return 总记录数，如果没有数据则返回0
     */
    public int getTotalRecords() {
        return data != null ? data.getTotal() : 0;
    }

    @Override
    public String toString() {
        return "WaterfallRecommendResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", isSuccess=" + isSuccess() +
                ", hasData=" + hasData() +
                ", recordCount=" + getRecordCount() +
                '}';
    }
}
