package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.viewpager2.widget.CompositePageTransformer;
import androidx.viewpager2.widget.MarginPageTransformer;
import androidx.viewpager2.widget.ViewPager2;
import com.android.video.R;
import com.android.video.adapter.CarouselAdapter;
import com.android.video.adapter.CategoriesVideoAdapter;
import com.android.video.adapter.TagAdapter;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.model.BannerModel;
import com.android.video.model.TagModel;
import com.android.video.model.VideoModel;
import com.android.video.model.response.CategoryModel;
import com.android.video.model.response.CategoryListResponseModel;
import com.android.video.model.response.CategoryWithFilmsModel;
import com.android.video.model.response.CategoryFilmModel;
import com.android.video.network.HomeApiService;
import com.android.video.ui.transformer.CarouselPageTransformer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Categories页面
 * 包含分类标签、轮播图和视频列表
 * <AUTHOR>
 */
public class CategoriesActivity extends BaseFullScreenActivity {

    private static final String TAG = "CategoriesActivity";

    private ImageView ivBack;
    private TextView tvTitle;
    private RecyclerView rvCategoryTags;
    private ViewPager2 vpCarousel;
    private TextView tvListTitle;
    private RecyclerView rvList;

    private TagAdapter tagAdapter;
    private CarouselAdapter carouselAdapter;
    private CategoriesVideoAdapter videoAdapter;

    // API服务
    private HomeApiService homeApiService;

    // 分页相关
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private String selectedCategoryId = null; // 当前选中的分类ID

    // 分类名称到ID的映射
    private Map<String, String> categoryNameToIdMap = new HashMap<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_categories);

        // 初始化API服务
        homeApiService = HomeApiService.getInstance();

        initViews();
        setupTagsRecyclerView();
        setupCarousel();
        setupVideoList();
        loadRealData(); // 改为加载真实数据
        setupClickListeners();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        rvCategoryTags = findViewById(R.id.rv_category_tags);
        vpCarousel = findViewById(R.id.vp_carousel);
        tvListTitle = findViewById(R.id.tv_list_title);
        rvList = findViewById(R.id.rv_list);
    }

    /**
     * 设置分类标签RecyclerView
     */
    private void setupTagsRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false);
        rvCategoryTags.setLayoutManager(layoutManager);

        tagAdapter = new TagAdapter(new ArrayList<>());
        rvCategoryTags.setAdapter(tagAdapter);

        // 设置标签点击监听
        tagAdapter.setOnTagClickListener((tag, position) -> {
            Log.d(TAG, "选中分类标签: " + tag.getName());

            // 更新选中的分类ID
            if ("ALL".equals(tag.getName())) {
                selectedCategoryId = null; // ALL表示查询所有分类
            } else {
                // 这里需要根据tag.getName()找到对应的categoryId
                // 暂时使用tag名称，实际应该存储categoryId
                selectedCategoryId = findCategoryIdByName(tag.getName());
            }

            // 重新加载数据
            loadRealVideoListData(false); // false表示刷新

            Toast.makeText(this, "已切换到: " + tag.getName(), Toast.LENGTH_SHORT).show();
        });
    }

    /**
     * 设置轮播图
     */
    private void setupCarousel() {
        // 设置ViewPager2属性
        vpCarousel.setClipChildren(false);
        vpCarousel.setClipToPadding(false);
        vpCarousel.setOffscreenPageLimit(3);

        // 移除滚动效果
        vpCarousel.post(() -> {
            if (vpCarousel.getChildAt(0) instanceof RecyclerView) {
                ((RecyclerView) vpCarousel.getChildAt(0)).setOverScrollMode(RecyclerView.OVER_SCROLL_NEVER);
            }
        });

        // 创建适配器
        carouselAdapter = new CarouselAdapter(new ArrayList<>());
        vpCarousel.setAdapter(carouselAdapter);

        // 设置页面变换器
        CompositePageTransformer compositePageTransformer = new CompositePageTransformer();

        // 添加间距变换器（8dp间距）
        int marginPx = (int) (8 * getResources().getDisplayMetrics().density);
        compositePageTransformer.addTransformer(new MarginPageTransformer(marginPx));

        // 添加自定义缩放变换器
        compositePageTransformer.addTransformer(new CarouselPageTransformer());

        vpCarousel.setPageTransformer(compositePageTransformer);

        // 设置轮播图点击监听
        carouselAdapter.setOnItemClickListener(new CarouselAdapter.OnCarouselItemClickListener() {
            @Override
            public void onItemClick(VideoModel video, int position) {
                // 跳转到视频详情页
                Intent intent = new Intent(CategoriesActivity.this, VideoDetailActivity.class);

                // 优先使用filmLanguageInfoId调用新接口
                if (video.hasFilmLanguageInfoId()) {
                    intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
                    intent.putExtra("filmTitle", video.getTitle());
                    Log.d(TAG, "轮播图使用filmLanguageInfoId跳转详情页: " + video.getFilmLanguageInfoId());
                } else {
                    // 回退到旧模式
                    intent.putExtra("video_model", video);
                    Log.d(TAG, "轮播图回退到video_model模式跳转详情页: " + video.getTitle());
                }

                startActivity(intent);
            }

            @Override
            public void onPlayClick(VideoModel video, int position) {
                // 跳转到视频详情页
                Intent intent = new Intent(CategoriesActivity.this, VideoDetailActivity.class);

                // 优先使用filmLanguageInfoId调用新接口
                if (video.hasFilmLanguageInfoId()) {
                    intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
                    intent.putExtra("filmTitle", video.getTitle());
                    Log.d(TAG, "轮播图播放使用filmLanguageInfoId跳转详情页: " + video.getFilmLanguageInfoId());
                } else {
                    // 回退到旧模式
                    intent.putExtra("video_model", video);
                    Log.d(TAG, "轮播图播放回退到video_model模式跳转详情页: " + video.getTitle());
                }

                startActivity(intent);
            }
        });

        // 添加页面变化监听器实现无限循环
        setupInfiniteLoop();
    }

    /**
     * 设置无限循环逻辑
     */
    private void setupInfiniteLoop() {
        vpCarousel.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);

                // 检查是否需要进行循环跳转
                int jumpPosition = carouselAdapter.getLoopJumpPosition(position);
                if (jumpPosition != -1) {
                    // 延迟执行跳转，避免动画冲突
                    vpCarousel.post(() -> {
                        vpCarousel.setCurrentItem(jumpPosition, false);
                    });
                }

                Log.d(TAG, "轮播图页面变化: position=" + position +
                          ", realPosition=" + carouselAdapter.getRealPosition(position) +
                          ", jumpPosition=" + jumpPosition);
            }
        });
    }

    /**
     * 设置视频列表
     */
    private void setupVideoList() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvList.setLayoutManager(layoutManager);

        videoAdapter = new CategoriesVideoAdapter(new ArrayList<>());
        rvList.setAdapter(videoAdapter);

        // 设置视频点击监听
        videoAdapter.setOnItemClickListener((video, position) -> {
            // 跳转到视频详情页
            Intent intent = new Intent(this, VideoDetailActivity.class);

            // 优先使用filmLanguageInfoId调用新接口
            if (video.hasFilmLanguageInfoId()) {
                intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
                intent.putExtra("filmTitle", video.getTitle());
                Log.d(TAG, "使用filmLanguageInfoId跳转详情页: " + video.getFilmLanguageInfoId());
            } else {
                // 回退到旧模式
                intent.putExtra("video_model", video);
                Log.d(TAG, "回退到video_model模式跳转详情页: " + video.getTitle());
            }

            startActivity(intent);
        });
    }

    /**
     * 加载真实数据
     */
    private void loadRealData() {
        Log.d(TAG, "开始加载分类页面真实数据");

        // 加载分类标签数据
        loadRealCategoryTagsData();

        // 加载真实轮播图数据
        loadRealCarouselData();

        // 加载真实视频列表数据
        loadRealVideoListData();
    }

    /**
     * 加载测试数据
     */
    private void loadTestData() {
        // 加载分类标签数据
        loadCategoryTagsTestData();

        // 加载轮播图数据
        loadCarouselData();

        // 加载视频列表数据
        loadVideoListTestData();
    }

    /**
     * 加载真实分类标签数据
     */
    private void loadRealCategoryTagsData() {
        Log.d(TAG, "开始加载分类标签列表数据");

        homeApiService.getCategoryList(new HomeApiService.CategoryListCallback() {
            @Override
            public void onSuccess(List<CategoryModel> categories) {
                Log.d(TAG, "分类标签列表数据加载成功，数量: " + categories.size());

                // 将CategoryModel数据转换为TagModel
                List<TagModel> categoryTags = convertCategoriesToTagModels(categories);

                // 更新分类标签适配器
                if (tagAdapter != null) {
                    tagAdapter.updateTagList(categoryTags);
                    Log.d(TAG, "✅ 更新分类标签: " + categoryTags.size() + "个标签");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "分类标签列表数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                loadCategoryTagsTestData();

                Toast.makeText(CategoriesActivity.this,
                    "分类标签加载失败，使用测试数据", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 将CategoryModel数据转换为TagModel列表
     *
     * @param categories 分类标签列表
     * @return TagModel列表
     */
    private List<TagModel> convertCategoriesToTagModels(List<CategoryModel> categories) {
        List<TagModel> tagModels = new ArrayList<>();

        // 清空之前的映射
        categoryNameToIdMap.clear();

        // 添加默认的"ALL"标签
        tagModels.add(new TagModel("ALL", "category", true)); // 默认选中ALL
        categoryNameToIdMap.put("ALL", null); // ALL对应null，表示查询所有分类

        for (CategoryModel category : categories) {
            if (category != null && category.getCategoryName() != null && category.isActive()) {
                // 只显示正常状态的分类（isDelete = 1）
                String tagName = category.getCategoryName();
                String categoryId = category.getCategoryId();

                // 创建TagModel
                TagModel tagModel = new TagModel(tagName, "category");
                tagModels.add(tagModel);

                // 保存分类名称到ID的映射
                categoryNameToIdMap.put(tagName, categoryId);

                Log.d(TAG, "转换分类标签: " + category.getCategoryName() +
                          " (ID: " + category.getCategoryId() +
                          ", Weight: " + category.getWeight() +
                          ", Language: " + category.getLanguage() + ") -> " +
                          tagModel.getDisplayName());
            }
        }

        Log.d(TAG, "分类标签转换完成，共 " + tagModels.size() + " 个标签（包含ALL）");
        Log.d(TAG, "分类映射: " + categoryNameToIdMap.toString());
        return tagModels;
    }

    /**
     * 加载分类标签测试数据（作为后备）
     */
    private void loadCategoryTagsTestData() {
        List<TagModel> categoryTags = new ArrayList<>();
        categoryTags.add(new TagModel("ALL", "category", true)); // 默认选中ALL
        categoryTags.add(new TagModel("Drama", "category"));
        categoryTags.add(new TagModel("Comedy", "category"));
        categoryTags.add(new TagModel("Action Drama", "category"));

        tagAdapter.updateTagList(categoryTags);
    }

    /**
     * 加载真实视频列表数据
     */
    private void loadRealVideoListData() {
        loadRealVideoListData(false);
    }

    /**
     * 加载真实视频列表数据（支持分页）
     *
     * @param isLoadMore 是否为加载更多（true=加载更多，false=刷新）
     */
    private void loadRealVideoListData(boolean isLoadMore) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        if (isLoadMore && !hasMoreData) {
            Log.d(TAG, "没有更多数据，跳过加载");
            return;
        }

        isLoading = true;

        // 如果是刷新，重置页码
        if (!isLoadMore) {
            currentPage = 1;
            hasMoreData = true;
        }

        Log.d(TAG, "开始加载真实视频列表数据 - 页码: " + currentPage + ", 分类ID: " + selectedCategoryId);

        // 获取分类的短剧列表
        homeApiService.getCategoriesWithFilms(selectedCategoryId, currentPage, pageSize, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                isLoading = false;
                Log.d(TAG, "视频列表数据加载成功 - 页码: " + response.getCurrentPage() + "/" + response.getTotalPages());

                if (response.hasData()) {
                    List<CategoryWithFilmsModel> categories = response.getCategories();

                    // 将分类短剧数据转换为VideoModel列表
                    List<VideoModel> newVideoList = convertCategoriesWithFilmsToVideoModels(categories);

                    if (videoAdapter != null) {
                        if (isLoadMore && currentPage > 1) {
                            // 加载更多：追加数据
                            videoAdapter.addVideoList(newVideoList);
                            Log.d(TAG, "✅ 追加视频列表: " + newVideoList.size() + "个视频");
                        } else {
                            // 刷新：替换数据
                            videoAdapter.updateVideoList(newVideoList);
                            Log.d(TAG, "✅ 更新视频列表: " + newVideoList.size() + "个视频");
                        }
                    }

                    // 更新分页状态
                    hasMoreData = response.hasNextPage();
                    if (hasMoreData) {
                        currentPage++;
                    }

                    Log.d(TAG, "分页状态 - 当前页: " + currentPage + ", 还有更多: " + hasMoreData);
                } else {
                    Log.w(TAG, "视频列表响应无数据");
                    if (!isLoadMore) {
                        // 如果是刷新且无数据，使用测试数据
                        loadVideoListTestData();
                    }
                    hasMoreData = false;
                }
            }

            @Override
            public void onError(String errorMessage) {
                isLoading = false;
                Log.e(TAG, "视频列表数据加载失败: " + errorMessage);

                if (!isLoadMore) {
                    // 如果是刷新失败，使用测试数据作为后备
                    loadVideoListTestData();
                    Toast.makeText(CategoriesActivity.this,
                        "视频列表加载失败，使用测试数据", Toast.LENGTH_SHORT).show();
                } else {
                    // 如果是加载更多失败，只显示错误提示
                    Toast.makeText(CategoriesActivity.this,
                        "加载更多失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 将分类短剧数据转换为VideoModel列表
     *
     * @param categories 分类短剧列表
     * @return VideoModel列表
     */
    private List<VideoModel> convertCategoriesWithFilmsToVideoModels(List<CategoryWithFilmsModel> categories) {
        List<VideoModel> videoList = new ArrayList<>();

        for (CategoryWithFilmsModel category : categories) {
            // 检查分类是否有效且有短剧数据
            if (category != null && category.isValid() && category.hasFilms()) {
                List<CategoryFilmModel> films = category.getFilms();
                Log.d(TAG, "处理分类: " + category.getCategoryName() + ", 短剧数量: " + films.size());

                for (CategoryFilmModel film : films) {
                    if (film != null && film.isValid()) {
                        // 创建VideoModel
                        VideoModel video = new VideoModel(
                            film.getFilmId(),
                            film.getFilmTitle(),
                            film.getCover(),
                            category.getCategoryName()
                        );

                        // 设置filmLanguageInfoId（重要：用于调用详情接口）
                        if (film.getFilmLanguageInfoId() != null) {
                            video.setFilmLanguageInfoId(film.getFilmLanguageInfoId());
                        }

                        // 设置额外信息
                        if (film.getDetails() != null) {
                            video.setSynopsis(film.getDetails());
                        }

                        // 设置播放次数
                        if (film.getPlayNum() != null) {
                            video.setViewCount(film.getPlayNum());
                        }

                        // 设置章节信息
                        if (film.getTotalChaptersNum() != null) {
                            video.setTotalEpisodes(film.getTotalChaptersNum());
                        }

                        // 设置喜欢状态
                        if (film.getIsLove() != null) {
                            video.setLiked(film.getIsLove());
                        }

                        videoList.add(video);

                        Log.d(TAG, "转换短剧: " + film.getFilmTitle() +
                                  " (分类: " + category.getCategoryName() +
                                  ", 语言: " + film.getLanguageTypeDescription() + ")");
                    } else {
                        Log.d(TAG, "跳过无效短剧: " + (film != null ? film.getFilmTitle() : "null"));
                    }
                }
            } else {
                // 记录跳过的分类
                if (category != null) {
                    Log.d(TAG, "跳过分类: " + category.getCategoryName() +
                              " (有效: " + category.isValid() +
                              ", 有短剧: " + category.hasFilms() +
                              ", 短剧数量: " + category.getFilmCount() + ")");
                } else {
                    Log.d(TAG, "跳过null分类");
                }
            }
        }

        Log.d(TAG, "分类短剧转换完成，共 " + videoList.size() + " 个视频");
        return videoList;
    }

    /**
     * 根据分类名称查找分类ID
     *
     * @param categoryName 分类名称
     * @return 分类ID，如果找不到则返回null
     */
    private String findCategoryIdByName(String categoryName) {
        String categoryId = categoryNameToIdMap.get(categoryName);
        Log.d(TAG, "查找分类ID: " + categoryName + " -> " + categoryId);
        return categoryId;
    }

    /**
     * 加载更多数据
     */
    public void loadMoreData() {
        if (!isLoading && hasMoreData) {
            Log.d(TAG, "触发加载更多数据");
            loadRealVideoListData(true);
        }
    }

    /**
     * 刷新数据
     */
    public void refreshData() {
        Log.d(TAG, "触发刷新数据");
        loadRealVideoListData(false);
    }

    /**
     * 加载轮播图测试数据
     */
    private void loadCarouselData() {
        List<VideoModel> carouselVideos = new ArrayList<>();

        VideoModel video1 = new VideoModel("1", "Escorting the Heiress", "", "Drama");
        video1.setSynopsis("A captivating historical drama set in a grand palace, where love and power collide.");
        carouselVideos.add(video1);

        VideoModel video2 = new VideoModel("2", "Bienvenido S Eden", "", "Comedy");
        video2.setSynopsis("A surreal dark comedy set in a mysterious island paradise.");
        carouselVideos.add(video2);

        VideoModel video3 = new VideoModel("3", "The Crown", "", "Drama");
        video3.setSynopsis("The gripping, decades-spanning inside story of Her Majesty Queen Elizabeth II.");
        carouselVideos.add(video3);

        VideoModel video4 = new VideoModel("4", "Stranger Things", "", "Action Drama");
        video4.setSynopsis("When a young boy vanishes, a small town uncovers a mystery involving secret experiments.");
        carouselVideos.add(video4);

        VideoModel video5 = new VideoModel("5", "The Witcher", "", "Action Drama");
        video5.setSynopsis("Geralt of Rivia, a solitary monster hunter, struggles to find his place in a world.");
        carouselVideos.add(video5);

        carouselAdapter.updateVideoList(carouselVideos);

        // 设置初始位置（无限循环的中间位置）
        if (carouselVideos.size() > 0) {
            int initialPosition = carouselAdapter.getInitialPosition();
            vpCarousel.post(() -> vpCarousel.setCurrentItem(initialPosition, false));
            Log.d(TAG, "测试数据设置初始位置: " + initialPosition + ", 原始数据大小: " + carouselVideos.size());
        }
    }

    /**
     * 加载视频列表测试数据
     */
    private void loadVideoListTestData() {
        List<VideoModel> videoList = new ArrayList<>();

        VideoModel video1 = new VideoModel("1", "Escorting the Heiress", "", "Drama");
        video1.setSynopsis("A captivating historical drama set in a grand palace, where love and power collide. The story...");
        videoList.add(video1);

        VideoModel video2 = new VideoModel("2", "Bienvenido S Eden", "", "Comedy");
        video2.setSynopsis("A surreal dark comedy set in a mysterious island paradise where nothing is as it seems...");
        videoList.add(video2);

        videoAdapter.updateVideoList(videoList);
    }

    /**
     * 加载真实轮播图数据
     */
    private void loadRealCarouselData() {
        Log.d(TAG, "开始加载分类页面轮播图数据");

        homeApiService.getBanners(HomeApiConstantsUtils.LOCATION_CATEGORY_DETAIL, new HomeApiService.BannerCallback() {
            @Override
            public void onSuccess(List<BannerModel> banners) {
                Log.d(TAG, "分类页面轮播图数据加载成功，数量: " + banners.size());

                // 将BannerModel转换为VideoModel
                List<VideoModel> carouselVideos = convertBannersToVideoModels(banners);

                // 更新轮播图适配器
                if (carouselAdapter != null) {
                    runOnUiThread(() -> {
                        carouselAdapter.updateVideoList(carouselVideos);

                        // 设置初始位置（无限循环的中间位置）
                        if (carouselVideos.size() > 0) {
                            int initialPosition = carouselAdapter.getInitialPosition();
                            vpCarousel.post(() -> vpCarousel.setCurrentItem(initialPosition, false));
                            Log.d(TAG, "设置初始位置: " + initialPosition + ", 原始数据大小: " + carouselVideos.size());
                        }

                        Log.d(TAG, "✅ 更新分类页面轮播图: " + carouselVideos.size() + "个视频，循环数据大小: " + carouselAdapter.getItemCount());
                    });
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "分类页面轮播图数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                runOnUiThread(() -> {
                    loadCarouselData();
                    Toast.makeText(CategoriesActivity.this, "轮播图加载失败，使用测试数据", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * 将BannerModel列表转换为VideoModel列表
     *
     * @param banners Banner列表
     * @return VideoModel列表
     */
    private List<VideoModel> convertBannersToVideoModels(List<BannerModel> banners) {
        List<VideoModel> videoModels = new ArrayList<>();

        if (banners == null || banners.isEmpty()) {
            Log.w(TAG, "Banner列表为空，返回空的VideoModel列表");
            return videoModels;
        }

        for (BannerModel banner : banners) {
            if (banner != null) {
                // 创建VideoModel
                VideoModel videoModel = new VideoModel(
                    banner.getFilmId() != null ? banner.getFilmId() : banner.getBannerId(),
                    banner.getBannerName() != null ? banner.getBannerName() : "未知标题",
                    banner.getBannerCover(), // 使用bannerCover作为posterUrl
                    "分类轮播" // 默认分类
                );

                // 注意：Banner数据目前没有filmLanguageInfoId字段，无法使用新的详情接口
                // 如果将来API提供了filmLanguageInfoId，可以在这里设置：
                // if (banner.getFilmLanguageInfoId() != null) {
                //     videoModel.setFilmLanguageInfoId(banner.getFilmLanguageInfoId());
                // }

                // 设置描述信息
                if (banner.getPromotionalUrl() != null && !banner.getPromotionalUrl().isEmpty()) {
                    videoModel.setSynopsis("宣传片: " + banner.getPromotionalUrl());
                }

                videoModels.add(videoModel);

                Log.d(TAG, "转换Banner: " + banner.getBannerName() +
                          " -> VideoModel: " + videoModel.getTitle() +
                          ", 封面: " + videoModel.getPosterUrl());
            }
        }

        Log.d(TAG, "Banner转换完成，共转换 " + videoModels.size() + " 个VideoModel");
        return videoModels;
    }

    /**
     * 设置点击监听
     */
    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());
    }
}
