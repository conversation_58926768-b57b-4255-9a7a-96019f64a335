package com.android.video.manager;

import android.util.Log;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * Fragment缓存状态管理器
 * 用于跟踪和管理Fragment的缓存状态，避免重复加载数据
 * <AUTHOR> Team
 */
public class FragmentCacheManager {
    
    private static final String TAG = "FragmentCacheManager";
    private static FragmentCacheManager instance;
    
    // Fragment缓存状态映射
    private final Map<String, FragmentCacheState> fragmentStates;
    
    /**
     * Fragment缓存状态类
     */
    public static class FragmentCacheState {
        public boolean dataLoaded = false;
        public boolean isDataCached = false;
        public boolean isFirstLoad = true; // 新增：标记是否为首次加载
        public long lastLoadTime = 0;
        public long cacheExpiryTime = 0;

        public FragmentCacheState() {
            this.lastLoadTime = 0; // 首次创建时设为0
            // 默认缓存1小时
            this.cacheExpiryTime = 60 * 60 * 1000; // 1小时
        }
        
        public boolean isCacheExpired() {
            return System.currentTimeMillis() - lastLoadTime > cacheExpiryTime;
        }
        
        public void updateLoadTime() {
            this.lastLoadTime = System.currentTimeMillis();
        }
        
        @Override
        public String toString() {
            return "FragmentCacheState{" +
                    "dataLoaded=" + dataLoaded +
                    ", isDataCached=" + isDataCached +
                    ", isFirstLoad=" + isFirstLoad +
                    ", lastLoadTime=" + lastLoadTime +
                    ", cacheExpired=" + isCacheExpired() +
                    '}';
        }
    }
    
    private FragmentCacheManager() {
        this.fragmentStates = new ConcurrentHashMap<>();
    }
    
    public static synchronized FragmentCacheManager getInstance() {
        if (instance == null) {
            instance = new FragmentCacheManager();
        }
        return instance;
    }
    
    /**
     * 获取Fragment的缓存状态
     */
    public FragmentCacheState getFragmentState(String fragmentKey) {
        return fragmentStates.computeIfAbsent(fragmentKey, k -> {
            Log.d(TAG, "创建新的Fragment缓存状态: " + fragmentKey);
            return new FragmentCacheState();
        });
    }
    
    /**
     * 更新Fragment的数据加载状态
     */
    public void updateDataLoaded(String fragmentKey, boolean loaded) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        state.dataLoaded = loaded;
        if (loaded) {
            state.updateLoadTime();
            state.isFirstLoad = false; // 标记已不是首次加载
        }
        Log.d(TAG, "更新Fragment数据加载状态: " + fragmentKey + " -> " + loaded);
    }
    
    /**
     * 更新Fragment的缓存状态
     */
    public void updateCacheState(String fragmentKey, boolean cached) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        state.isDataCached = cached;
        Log.d(TAG, "更新Fragment缓存状态: " + fragmentKey + " -> " + cached);
    }
    
    /**
     * 检查Fragment是否需要重新加载数据
     */
    public boolean shouldReloadData(String fragmentKey) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        boolean shouldReload = state.isFirstLoad || !state.dataLoaded || !state.isDataCached || state.isCacheExpired();
        Log.d(TAG, "检查Fragment是否需要重新加载: " + fragmentKey + " -> " + shouldReload);
        Log.d(TAG, "Fragment状态: " + state.toString());
        return shouldReload;
    }

    /**
     * 检查是否为首次加载
     */
    public boolean isFirstLoad(String fragmentKey) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        return state.isFirstLoad;
    }
    
    /**
     * 清除Fragment的缓存状态
     */
    public void clearFragmentState(String fragmentKey) {
        fragmentStates.remove(fragmentKey);
        Log.d(TAG, "清除Fragment缓存状态: " + fragmentKey);
    }
    
    /**
     * 重置Fragment的缓存状态（用于下拉刷新等场景）
     */
    public void resetFragmentState(String fragmentKey) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        state.dataLoaded = false;
        state.isDataCached = false;
        state.lastLoadTime = 0;
        // 注意：不重置isFirstLoad，因为这不是真正的首次加载
        Log.d(TAG, "重置Fragment缓存状态: " + fragmentKey);
    }
    
    /**
     * 设置Fragment的缓存过期时间
     */
    public void setCacheExpiryTime(String fragmentKey, long expiryTimeMs) {
        FragmentCacheState state = getFragmentState(fragmentKey);
        state.cacheExpiryTime = expiryTimeMs;
        Log.d(TAG, "设置Fragment缓存过期时间: " + fragmentKey + " -> " + expiryTimeMs + "ms");
    }
    
    /**
     * 获取所有Fragment的缓存状态（用于调试）
     */
    public Map<String, FragmentCacheState> getAllFragmentStates() {
        return new ConcurrentHashMap<>(fragmentStates);
    }
    
    /**
     * 清除所有Fragment的缓存状态
     */
    public void clearAllStates() {
        fragmentStates.clear();
        Log.d(TAG, "清除所有Fragment缓存状态");
    }
    
    /**
     * 打印所有Fragment的缓存状态（用于调试）
     */
    public void debugPrintAllStates() {
        Log.d(TAG, "=== Fragment缓存状态调试信息 ===");
        for (Map.Entry<String, FragmentCacheState> entry : fragmentStates.entrySet()) {
            Log.d(TAG, entry.getKey() + ": " + entry.getValue().toString());
        }
        Log.d(TAG, "=== 调试信息结束 ===");
    }
}
