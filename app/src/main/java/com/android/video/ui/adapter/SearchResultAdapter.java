package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.SearchResultModel;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import java.util.List;

/**
 * 搜索结果适配器
 */
public class SearchResultAdapter extends RecyclerView.Adapter<SearchResultAdapter.SearchResultViewHolder> {
    
    private List<SearchResultModel> searchResults;
    private OnSearchResultClickListener clickListener;
    
    public interface OnSearchResultClickListener {
        void onSearchResultClick(SearchResultModel searchResult);
    }
    
    public SearchResultAdapter(List<SearchResultModel> searchResults, OnSearchResultClickListener clickListener) {
        this.searchResults = searchResults;
        this.clickListener = clickListener;
    }
    
    @NonNull
    @Override
    public SearchResultViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_search_result, parent, false);
        return new SearchResultViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull SearchResultViewHolder holder, int position) {
        SearchResultModel searchResult = searchResults.get(position);
        holder.bind(searchResult);
    }
    
    @Override
    public int getItemCount() {
        return searchResults.size();
    }
    
    class SearchResultViewHolder extends RecyclerView.ViewHolder {
        private TextView tvRank;
        private ImageView ivPoster;
        private TextView tvTitle;
        private ImageView ivSearchIcon;
        private TextView tvSearchCount;
        
        public SearchResultViewHolder(@NonNull View itemView) {
            super(itemView);
            tvRank = itemView.findViewById(R.id.tv_rank);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            ivSearchIcon = itemView.findViewById(R.id.iv_search_icon);
            tvSearchCount = itemView.findViewById(R.id.tv_search_count);
            
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        clickListener.onSearchResultClick(searchResults.get(position));
                    }
                }
            });
        }
        
        public void bind(SearchResultModel searchResult) {
            tvRank.setText(String.valueOf(searchResult.getRank()));

            // 设置海报图片 - 优先使用网络图片URL
            String posterUrl = searchResult.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty() && !posterUrl.equals("https://www.baidu.com")) {
                // 使用Glide加载网络图片
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(ivPoster);
            } else {
                // 如果没有URL或URL是测试数据，使用本地资源图片
                int posterResId = searchResult.getPosterResId();
                if (posterResId != 0) {
                    ivPoster.setImageResource(posterResId);
                } else {
                    ivPoster.setImageResource(R.drawable.movie_poster);
                }
            }

            tvTitle.setText(searchResult.getTitle());
            tvSearchCount.setText(searchResult.getSearchCount());
        }
    }
}
