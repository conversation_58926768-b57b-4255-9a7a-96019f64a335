package com.android.video.constants;

/**
 * My List相关API常量工具类
 * <p>
 * 继承BaseApiConstantsUtils，提供My List模块相关的API端点、参数名称等常量定义。
 * 包含下载列表、收藏列表、观看历史等相关API常量。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class MyListConstantsUtils extends BaseApiConstantsUtils {

    // ========== API端点常量 ==========

    /**
     * 获取下载列表API端点
     */
    public static final String API_GET_DOWNLOAD_LIST = "/app/myList/downloadList";

    /**
     * 获取收藏列表API端点
     */
    public static final String API_GET_FAVORITE_LIST = "/app/myList/favoriteList";

    /**
     * 获取观看历史列表API端点
     */
    public static final String API_GET_HISTORY_LIST = "/app/myList/historyList";

    /**
     * 获取订阅列表API端点
     */
    public static final String API_GET_SUBSCRIPTION_LIST = "/app/myList/subscriptionList";

    /**
     * 删除下载章节API端点
     */
    public static final String API_DELETE_DOWNLOAD_CHAPTER = "/app/myList/deleteDownloadChapter";

    /**
     * 删除下载短剧API端点
     */
    public static final String API_DELETE_DOWNLOAD_FILM = "/app/myList/deleteDownloadFilm";

    /**
     * 获取我的喜爱列表API端点
     */
    public static final String API_GET_MY_LOVE_LIST = "/app/myList/getMyLoveList";

    /**
     * 获取我的播放历史列表API端点
     */
    public static final String API_GET_MY_PLAY_HISTORY_LIST = "/app/myList/getMyPlayHistoryList";

    /**
     * 获取我的订阅列表API端点
     */
    public static final String API_GET_MY_SUBSCRIBE_LIST = "/app/myList/getMySubscribeList";

    // ========== 请求参数常量 ==========

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE_NUM = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_PAGE_SIZE = "size";

    /**
     * 列表类型参数名
     */
    public static final String PARAM_LIST_TYPE = "listType";

    /**
     * 排序方式参数名
     */
    public static final String PARAM_SORT_TYPE = "sortType";

    /**
     * 语言类型参数名
     */
    public static final String PARAM_LANGUAGE_TYPE = "languageType";

    /**
     * 下载记录ID参数名
     */
    public static final String PARAM_DOWNLOAD_RECORD_ID = "downloadRecordId";

    /**
     * 短剧语言信息ID参数名
     */
    public static final String PARAM_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    // ========== 列表类型常量 ==========

    /**
     * 下载列表类型
     */
    public static final int LIST_TYPE_DOWNLOAD = 1;

    /**
     * 收藏列表类型
     */
    public static final int LIST_TYPE_FAVORITE = 2;

    /**
     * 历史列表类型
     */
    public static final int LIST_TYPE_HISTORY = 3;

    /**
     * 订阅列表类型
     */
    public static final int LIST_TYPE_SUBSCRIPTION = 4;

    // ========== 排序类型常量 ==========

    /**
     * 按时间降序排序
     */
    public static final int SORT_TYPE_TIME_DESC = 1;

    /**
     * 按时间升序排序
     */
    public static final int SORT_TYPE_TIME_ASC = 2;

    /**
     * 按名称排序
     */
    public static final int SORT_TYPE_NAME = 3;

    /**
     * 按下载进度排序
     */
    public static final int SORT_TYPE_PROGRESS = 4;

    // ========== 语言类型常量 ==========

    /**
     * 英语
     */
    public static final int LANGUAGE_TYPE_ENGLISH = 1;

    /**
     * 俄语
     */
    public static final int LANGUAGE_TYPE_RUSSIAN = 2;

    // ========== 分页配置常量 ==========

    /**
     * 默认页码
     */
    public static final int DEFAULT_PAGE_NUM = 1;

    /**
     * 默认每页数量
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大每页数量
     */
    public static final int MAX_PAGE_SIZE = 50;

    // ========== 响应字段常量 ==========

    /**
     * 记录列表字段名
     */
    public static final String FIELD_RECORDS = "records";

    /**
     * 总数字段名
     */
    public static final String FIELD_TOTAL = "total";

    /**
     * 当前页字段名
     */
    public static final String FIELD_CURRENT = "current";

    /**
     * 每页数量字段名
     */
    public static final String FIELD_SIZE = "size";

    /**
     * 总页数字段名
     */
    public static final String FIELD_PAGES = "pages";

    /**
     * 短剧标题字段名
     */
    public static final String FIELD_FILM_TITLE = "filmTitle";

    /**
     * 封面字段名
     */
    public static final String FIELD_COVER = "cover";

    /**
     * 短剧ID字段名
     */
    public static final String FIELD_FILM_ID = "filmId";

    /**
     * 短剧语言信息ID字段名
     */
    public static final String FIELD_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    /**
     * 简介字段名
     */
    public static final String FIELD_DETAILS = "details";

    /**
     * 总章节数字段名
     */
    public static final String FIELD_TOTAL_CHAPTERS_NUM = "totalChaptersNum";

    /**
     * 已下载章节数字段名
     */
    public static final String FIELD_DOWNLOADED_CHAPTERS_NUM = "downloadedChaptersNum";

    // ========== 工具方法 ==========

    /**
     * 构建下载列表API URL
     * @param baseUrl 基础URL
     * @return 完整的API URL
     */
    public static String buildDownloadListApiUrl(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_DOWNLOAD_LIST);
    }

    /**
     * 构建收藏列表API URL
     * @param baseUrl 基础URL
     * @return 完整的API URL
     */
    public static String buildFavoriteListApiUrl(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_FAVORITE_LIST);
    }

    /**
     * 构建历史列表API URL
     * @param baseUrl 基础URL
     * @return 完整的API URL
     */
    public static String buildHistoryListApiUrl(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_HISTORY_LIST);
    }

    /**
     * 构建订阅列表API URL
     * @param baseUrl 基础URL
     * @return 完整的API URL
     */
    public static String buildSubscriptionListApiUrl(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_SUBSCRIPTION_LIST);
    }

    /**
     * 验证页码参数
     * @param pageNum 页码
     * @return 有效的页码
     */
    public static int validatePageNum(int pageNum) {
        return Math.max(pageNum, DEFAULT_PAGE_NUM);
    }

    /**
     * 验证每页数量参数
     * @param pageSize 每页数量
     * @return 有效的每页数量
     */
    public static int validatePageSize(int pageSize) {
        if (pageSize <= 0) {
            return DEFAULT_PAGE_SIZE;
        }
        return Math.min(pageSize, MAX_PAGE_SIZE);
    }

    /**
     * 获取语言类型描述
     * @param languageType 语言类型
     * @return 语言描述
     */
    public static String getLanguageDescription(int languageType) {
        switch (languageType) {
            case LANGUAGE_TYPE_ENGLISH:
                return "English";
            case LANGUAGE_TYPE_RUSSIAN:
                return "Russian";
            default:
                return "Unknown";
        }
    }
}
