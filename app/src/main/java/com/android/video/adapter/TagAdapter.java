package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.TagModel;
import java.util.ArrayList;
import java.util.List;

/**
 * 标签RecyclerView适配器
 * <AUTHOR>
 */
public class TagAdapter extends RecyclerView.Adapter<TagAdapter.TagViewHolder> {

    private List<TagModel> tagList;
    private OnTagClickListener onTagClickListener;
    private int selectedPosition = 0; // 当前选中位置，默认选中第一个（ALL）
    
    /**
     * 标签点击监听接口
     */
    public interface OnTagClickListener {
        void onTagClick(TagModel tag, int position);
    }
    
    /**
     * 构造函数
     */
    public TagAdapter() {
        this.tagList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param tagList 标签列表
     */
    public TagAdapter(List<TagModel> tagList) {
        this.tagList = tagList != null ? tagList : new ArrayList<>();
    }
    
    /**
     * 设置标签点击监听器
     * @param listener 点击监听器
     */
    public void setOnTagClickListener(OnTagClickListener listener) {
        this.onTagClickListener = listener;
    }
    
    /**
     * 更新标签列表
     * @param newTagList 新的标签列表
     */
    public void updateTagList(List<TagModel> newTagList) {
        this.tagList = newTagList != null ? newTagList : new ArrayList<>();
        // 确保只有selectedPosition位置的标签被选中
        updateSingleSelection();
        notifyDataSetChanged();
    }
    
    /**
     * 设置选中位置（单选模式）
     * @param position 要选中的位置
     */
    public void setSelectedPosition(int position) {
        if (position >= 0 && position < tagList.size()) {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            updateSingleSelection();

            // 只刷新变化的项目
            if (oldPosition != position) {
                notifyItemChanged(oldPosition);
                notifyItemChanged(position);
            }
        }
    }

    /**
     * 获取当前选中位置
     * @return 选中位置
     */
    public int getSelectedPosition() {
        return selectedPosition;
    }

    /**
     * 更新单选状态
     */
    private void updateSingleSelection() {
        for (int i = 0; i < tagList.size(); i++) {
            tagList.get(i).setSelected(i == selectedPosition);
        }
    }
    
    /**
     * 获取标签列表
     * @return 标签列表
     */
    public List<TagModel> getTagList() {
        return new ArrayList<>(tagList);
    }

    /**
     * 获取所有选中的标签
     * @return 选中的标签列表
     */
    public List<TagModel> getSelectedTags() {
        List<TagModel> selectedTags = new ArrayList<>();
        for (TagModel tag : tagList) {
            if (tag.isSelected()) {
                selectedTags.add(tag);
            }
        }
        return selectedTags;
    }
    
    @NonNull
    @Override
    public TagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_tag, parent, false);
        return new TagViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull TagViewHolder holder, int position) {
        TagModel tag = tagList.get(position);
        holder.bind(tag, position);
    }
    
    @Override
    public int getItemCount() {
        return tagList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class TagViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvTagName;
        
        public TagViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTagName = itemView.findViewById(R.id.tv_tag_name);
        }
        
        /**
         * 绑定数据
         * @param tag 标签数据
         * @param position 位置
         */
        public void bind(TagModel tag, int position) {
            if (tag == null) {
                return;
            }
            
            // 设置标签名称
            tvTagName.setText(tag.getDisplayName());

            // 设置选中状态和文本颜色
            tvTagName.setSelected(tag.isSelected());
            if (tag.isSelected()) {
                tvTagName.setTextColor(0xFF000000); // 选中时黑色文本
            } else {
                tvTagName.setTextColor(0xFFFFFFFF); // 未选中时白色文本
            }

            // 设置点击事件
            tvTagName.setOnClickListener(v -> {
                if (onTagClickListener != null && position != selectedPosition) {
                    // 单选模式：设置新的选中位置
                    setSelectedPosition(position);

                    // 回调点击事件
                    onTagClickListener.onTagClick(tag, position);
                }
            });
        }
    }
}
