package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 我的订阅列表项数据模型
 * <AUTHOR> Team
 */
public class MySubscribeItemModel {
    
    /**
     * 短剧订阅ID
     */
    @SerializedName("filmSubscriptionId")
    private String filmSubscriptionId;
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 语言类型 (1=英语, 2=俄语)
     */
    @SerializedName("languageType")
    private int languageType;
    
    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 简介
     */
    @SerializedName("details")
    private String details;
    
    /**
     * 发布时间
     */
    @SerializedName("releaseTime")
    private String releaseTime;

    public MySubscribeItemModel() {
    }

    public MySubscribeItemModel(String filmSubscriptionId, String filmTitle, String cover, 
                               int languageType, String filmId, String filmLanguageInfoId, 
                               String details, String releaseTime) {
        this.filmSubscriptionId = filmSubscriptionId;
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.details = details;
        this.releaseTime = releaseTime;
    }

    // Getters and Setters
    public String getFilmSubscriptionId() {
        return filmSubscriptionId;
    }

    public void setFilmSubscriptionId(String filmSubscriptionId) {
        this.filmSubscriptionId = filmSubscriptionId;
    }

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    /**
     * 获取语言类型描述
     * @return 语言类型描述
     */
    public String getLanguageTypeDescription() {
        switch (languageType) {
            case 1:
                return "English";
            case 2:
                return "Russian";
            default:
                return "Unknown";
        }
    }

    /**
     * 检查是否有发布时间
     * @return 是否有发布时间
     */
    public boolean hasReleaseTime() {
        return releaseTime != null && !releaseTime.trim().isEmpty() && !"null".equals(releaseTime);
    }

    /**
     * 转换为MyListVideo模型
     * @return MyListVideo对象
     */
    public MyListVideo toMyListVideo() {
        MyListVideo video = new MyListVideo();
        video.setId(filmId);
        video.setTitle(filmTitle != null ? filmTitle : "");
        video.setPosterUrl(cover != null ? cover : "");
        video.setLiked(false); // Interest列表默认不显示喜欢状态
        video.setCategory("interest"); // Interest标签页类别
        video.setDescription(details != null ? details : "");
        video.setReleaseDate(hasReleaseTime() ? releaseTime : "");
        video.setCurrentEpisode(1); // 订阅列表默认从第1集开始
        video.setTotalEpisodes(1); // API未提供总集数信息
        video.setDownloadedEpisodes(0); // Interest列表不涉及下载
        video.setSubscribed(true); // 订阅列表中的都是已订阅的
        video.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");
        return video;
    }

    @Override
    public String toString() {
        return "MySubscribeItemModel{" +
                "filmSubscriptionId='" + filmSubscriptionId + '\'' +
                ", filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType + " (" + getLanguageTypeDescription() + ")" +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", details='" + details + '\'' +
                ", releaseTime='" + releaseTime + '\'' +
                ", hasReleaseTime=" + hasReleaseTime() +
                '}';
    }
}
