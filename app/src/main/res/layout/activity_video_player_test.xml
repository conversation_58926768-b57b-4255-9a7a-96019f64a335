<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.activity.VideoPlayerTestActivity">

    <!-- Title -->
    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:text="视频播放器测试工具"
        android:textColor="@color/white"
        android:textSize="20sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Progress Bar -->
    <ProgressBar
        android:id="@+id/progressBar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:indeterminateTint="@color/video_player_accent"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tvTitle" />

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/buttonContainer"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/progressBar">

        <!-- Integration Test Button -->
        <Button
            android:id="@+id/btnRunIntegrationTest"
            style="@style/TestButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="运行集成测试" />

        <!-- Full Test Suite Button -->
        <Button
            android:id="@+id/btnRunFullTestSuite"
            style="@style/TestButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:text="运行完整测试套件" />

        <!-- Performance Monitor Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnStartPerformanceMonitor"
                style="@style/TestButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="开始性能监控" />

            <Button
                android:id="@+id/btnStopPerformanceMonitor"
                style="@style/TestButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="停止性能监控" />

        </LinearLayout>

        <!-- Optimization Buttons -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:orientation="horizontal">

            <Button
                android:id="@+id/btnRunOptimization"
                style="@style/TestButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginEnd="4dp"
                android:layout_weight="1"
                android:text="执行优化" />

            <Button
                android:id="@+id/btnGetOptimizationSuggestions"
                style="@style/TestButtonStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:layout_weight="1"
                android:text="获取建议" />

        </LinearLayout>

        <!-- Clear Results Button -->
        <Button
            android:id="@+id/btnClearResults"
            style="@style/TestButtonStyle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="清空结果"
            android:backgroundTint="@color/video_player_error_color" />

    </LinearLayout>

    <!-- Results Container -->
    <androidx.cardview.widget.CardView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="16dp"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="16dp"
        app:cardBackgroundColor="@color/video_player_background_dark"
        app:cardCornerRadius="8dp"
        app:cardElevation="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/buttonContainer">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- Results Title -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="测试结果"
                android:textColor="@color/video_player_primary_text"
                android:textSize="16sp"
                android:textStyle="bold" />

            <!-- Results ScrollView -->
            <ScrollView
                android:id="@+id/scrollViewResults"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@color/black"
                android:padding="8dp">

                <TextView
                    android:id="@+id/tvResults"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:fontFamily="monospace"
                    android:text="点击上方按钮开始测试..."
                    android:textColor="@color/video_player_secondary_text"
                    android:textSize="12sp" />

            </ScrollView>

        </LinearLayout>

    </androidx.cardview.widget.CardView>

</androidx.constraintlayout.widget.ConstraintLayout>
