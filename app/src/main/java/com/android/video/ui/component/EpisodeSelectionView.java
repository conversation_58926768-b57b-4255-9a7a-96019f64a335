package com.android.video.ui.component;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Rect;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.FrameLayout;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.adapter.EpisodeRangeAdapter;
import com.android.video.model.EpisodeModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 选集选择组件
 * 提供选集选择的弹出列表UI
 * <AUTHOR>
 */
public class EpisodeSelectionView extends LinearLayout {

    private RecyclerView recyclerViewEpisodes;
    private RecyclerView recyclerViewRanges;
    private EpisodeAdapter episodeAdapter;
    private EpisodeRangeAdapter rangeAdapter;
    private ImageView btnClose;
    private TextView tvTitle;
    private View divider;

    private List<EpisodeModel> episodeList;
    private List<EpisodeModel> currentRangeEpisodes;
    private List<String> episodeRanges;
    private EpisodeModel selectedEpisode;
    private OnEpisodeSelectedListener listener;
    private OnVipUnlockRequestListener vipUnlockListener;
    private boolean isVisible = false;
    private int selectedRangeIndex = 0;

    public interface OnEpisodeSelectedListener {
        void onEpisodeSelected(EpisodeModel episode);
    }

    public interface OnVipUnlockRequestListener {
        void onVipUnlockRequested();
    }
    
    public EpisodeSelectionView(Context context) {
        super(context);
        init();
    }
    
    public EpisodeSelectionView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public EpisodeSelectionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        setOrientation(VERTICAL);

        // 设置固定尺寸：宽度match_parent，高度420dp
        LayoutParams params = new LayoutParams(
            LayoutParams.MATCH_PARENT,
            getResources().getDimensionPixelSize(R.dimen.episode_selection_popup_height)
        );
        setLayoutParams(params);

        // 加载布局文件
        LayoutInflater.from(getContext()).inflate(R.layout.layout_episode_selection, this, true);

        // 初始化视图
        initViews();

        // 初始状态为隐藏
        setVisibility(GONE);
        setAlpha(0f);
    }

    private void initViews() {
        btnClose = findViewById(R.id.btn_close);
        tvTitle = findViewById(R.id.tv_title);
        recyclerViewRanges = findViewById(R.id.recycler_view_episode_ranges);
        divider = findViewById(R.id.divider);
        recyclerViewEpisodes = findViewById(R.id.recycler_view_episodes);

        // 设置关闭按钮点击事件
        btnClose.setOnClickListener(v -> hide());

        // 初始化选集区间RecyclerView
        recyclerViewRanges.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));

        // 初始化剧集RecyclerView - 使用网格布局，自动计算列数
        int screenWidth = getResources().getDisplayMetrics().widthPixels;
        int itemWidth = getResources().getDimensionPixelSize(R.dimen.video_detail_episode_tag_width);
        int padding = 15 * 2; // 左右各15dp边距
        int spacing = 10; // 剧集标签之间的间距10dp
        int columns = Math.max(1, (screenWidth - padding + spacing) / (itemWidth + spacing));

        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), columns);
        recyclerViewEpisodes.setLayoutManager(gridLayoutManager);

        // 添加间距装饰器
        recyclerViewEpisodes.addItemDecoration(new GridSpacingItemDecoration(columns, spacing, true));
    }
    
    /**
     * 设置选集列表
     */
    public void setEpisodeList(List<EpisodeModel> episodeList) {
        this.episodeList = episodeList;

        // 生成选集区间
        generateEpisodeRanges();

        // 初始化选集区间适配器
        if (rangeAdapter == null) {
            rangeAdapter = new EpisodeRangeAdapter(episodeRanges, this::onRangeClick);
            recyclerViewRanges.setAdapter(rangeAdapter);
        } else {
            rangeAdapter.updateRanges(episodeRanges);
        }

        // 加载第一个区间的剧集
        loadEpisodesInRange(0);

        // 初始化剧集适配器
        if (episodeAdapter == null) {
            episodeAdapter = new EpisodeAdapter();
            recyclerViewEpisodes.setAdapter(episodeAdapter);
        } else {
            episodeAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 生成选集区间
     */
    private void generateEpisodeRanges() {
        episodeRanges = new ArrayList<>();
        if (episodeList == null || episodeList.isEmpty()) {
            return;
        }

        int totalEpisodes = episodeList.size();
        int rangeSize = 30; // 每个区间30集

        for (int i = 0; i < totalEpisodes; i += rangeSize) {
            int start = i + 1;
            int end = Math.min(i + rangeSize, totalEpisodes);
            episodeRanges.add(start + "-" + end);
        }
    }

    /**
     * 区间点击事件
     */
    private void onRangeClick(int rangeIndex) {
        selectedRangeIndex = rangeIndex;
        loadEpisodesInRange(rangeIndex);
        if (episodeAdapter != null) {
            episodeAdapter.notifyDataSetChanged();
        }
    }

    /**
     * 加载指定区间的剧集
     */
    private void loadEpisodesInRange(int rangeIndex) {
        if (episodeList == null || episodeList.isEmpty()) {
            currentRangeEpisodes = new ArrayList<>();
            return;
        }

        int rangeSize = 30;
        int startIndex = rangeIndex * rangeSize;
        int endIndex = Math.min(startIndex + rangeSize, episodeList.size());

        currentRangeEpisodes = new ArrayList<>();
        for (int i = startIndex; i < endIndex; i++) {
            currentRangeEpisodes.add(episodeList.get(i));
        }
    }
    
    /**
     * 设置当前选中的选集
     */
    public void setSelectedEpisode(EpisodeModel episode) {
        this.selectedEpisode = episode;

        // 如果选中的剧集不在当前区间，切换到对应区间
        if (episode != null && episodeList != null) {
            // 使用剧集编号来查找索引，更可靠
            int episodeIndex = -1;
            for (int i = 0; i < episodeList.size(); i++) {
                if (episodeList.get(i).getEpisodeNumber() == episode.getEpisodeNumber()) {
                    episodeIndex = i;
                    break;
                }
            }

            if (episodeIndex >= 0) {
                int rangeIndex = episodeIndex / 30;
                if (rangeIndex != selectedRangeIndex) {
                    selectedRangeIndex = rangeIndex;
                    if (rangeAdapter != null) {
                        rangeAdapter.setSelectedPosition(rangeIndex);
                    }
                    loadEpisodesInRange(rangeIndex);
                }
            }
        }

        if (episodeAdapter != null) {
            episodeAdapter.notifyDataSetChanged();
        }
    }
    
    /**
     * 设置选择监听器
     */
    public void setOnEpisodeSelectedListener(OnEpisodeSelectedListener listener) {
        this.listener = listener;
    }

    public void setOnVipUnlockRequestListener(OnVipUnlockRequestListener listener) {
        this.vipUnlockListener = listener;
    }
    
    /**
     * 显示选择列表
     */
    public void show() {
        if (isVisible) return;

        setVisibility(VISIBLE);

        // 强制测量布局
        measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));

        // 确保面板位于底部，使用与SubtitlePanelView相同的动画方式
        post(() -> {
            ViewGroup parent = (ViewGroup) getParent();
            if (parent != null) {
                int panelHeight = getMeasuredHeight();
                if (panelHeight == 0) {
                    panelHeight = getResources().getDimensionPixelSize(R.dimen.episode_selection_popup_height);
                }

                // 设置初始位置在屏幕底部外
                setTranslationY(panelHeight);
                setAlpha(0f);

                // 滑入动画 - 使用更缓慢的动画
                ObjectAnimator slideIn = ObjectAnimator.ofFloat(this, "translationY", panelHeight, 0f);
                slideIn.setDuration(400);
                slideIn.setInterpolator(new android.view.animation.DecelerateInterpolator());

                ObjectAnimator fadeIn = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
                fadeIn.setDuration(400);
                fadeIn.setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator());

                slideIn.start();
                fadeIn.start();

                isVisible = true;
            }
        });
    }
    
    /**
     * 隐藏选择列表
     */
    public void hide() {
        if (!isVisible) return;

        int panelHeight = getMeasuredHeight();
        if (panelHeight == 0) {
            panelHeight = getResources().getDimensionPixelSize(R.dimen.episode_selection_popup_height);
        }

        // 滑出动画 - 使用更缓慢的动画
        ObjectAnimator slideOut = ObjectAnimator.ofFloat(this, "translationY", 0f, panelHeight);
        slideOut.setDuration(350);
        slideOut.setInterpolator(new android.view.animation.AccelerateInterpolator());

        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        fadeOut.setDuration(350);
        fadeOut.setInterpolator(new android.view.animation.AccelerateDecelerateInterpolator());
        fadeOut.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(GONE);
                setTranslationY(0f); // 重置位置
            }
        });

        slideOut.start();
        fadeOut.start();
        isVisible = false;
    }
    
    /**
     * 切换显示状态
     */
    public void toggle() {
        if (isVisible) {
            hide();
        } else {
            show();
        }
    }
    
    /**
     * 选集适配器
     */
    private class EpisodeAdapter extends RecyclerView.Adapter<EpisodeAdapter.ViewHolder> {

        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            // 使用与视频详情页面相同的布局
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_episode, parent, false);
            return new ViewHolder(view);
        }
        
        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            if (currentRangeEpisodes == null || position >= currentRangeEpisodes.size()) {
                return;
            }

            EpisodeModel episode = currentRangeEpisodes.get(position);
            holder.tvEpisodeNumber.setText(String.valueOf(episode.getEpisodeNumber()));

            // 判断是否为不可观看状态（付费章节必须解锁后才能观看）
            boolean isUnavailable = episode.isCharge() && !episode.isUnlock();
            // 使用剧集编号来判断是否选中，更可靠
            boolean isSelected = selectedEpisode != null &&
                episode.getEpisodeNumber() == selectedEpisode.getEpisodeNumber();

            if (isUnavailable) {
                // 不可观看状态
                holder.llEpisodeContent.setBackgroundResource(R.drawable.episode_unavailable_bg);
                holder.tvEpisodeNumber.setTextColor(getResources().getColor(R.color.episode_unavailable_text));
                holder.llEpisodeContent.setSelected(false);

                // 隐藏直播图标，显示锁定图标
                holder.ivLiveIcon.setVisibility(View.GONE);
                holder.ivLockIcon.setVisibility(View.VISIBLE);

                holder.itemView.setClickable(true);
                // 点击未解锁剧集时显示VIP解锁弹窗
                holder.itemView.setOnClickListener(v -> {
                    if (vipUnlockListener != null) {
                        vipUnlockListener.onVipUnlockRequested();
                    }
                    hide();
                });
            } else {
                // 可观看状态 - 使用视频详情页面的样式
                holder.llEpisodeContent.setBackgroundResource(R.drawable.detail_episode_selector);
                holder.llEpisodeContent.setSelected(isSelected); // 设置选中状态到FrameLayout

                // 隐藏锁定图标
                holder.ivLockIcon.setVisibility(View.GONE);

                // 设置直播图标显示状态：只有在选中时才显示图标
                if (isSelected && episode.isLive()) {
                    holder.ivLiveIcon.setVisibility(View.VISIBLE);
                } else {
                    holder.ivLiveIcon.setVisibility(View.GONE);
                }

                if (isSelected) {
                    holder.tvEpisodeNumber.setTextColor(0xFF000000); // 选中时黑色文本
                } else {
                    holder.tvEpisodeNumber.setTextColor(0xFFFFFFFF); // 未选中时白色文本
                }

                holder.itemView.setClickable(true);
                holder.itemView.setOnClickListener(v -> {
                    if (listener != null) {
                        // 更新选中的剧集
                        selectedEpisode = episode;
                        // 通知适配器更新
                        notifyDataSetChanged();
                        // 回调监听器
                        listener.onEpisodeSelected(episode);
                    }
                    hide();
                });
            }
        }
        
        @Override
        public int getItemCount() {
            return currentRangeEpisodes != null ? currentRangeEpisodes.size() : 0;
        }

        class ViewHolder extends RecyclerView.ViewHolder {
            TextView tvEpisodeNumber;
            ImageView ivLiveIcon;
            ImageView ivLockIcon;
            FrameLayout llEpisodeContent;

            ViewHolder(View itemView) {
                super(itemView);
                tvEpisodeNumber = itemView.findViewById(R.id.tv_episode_number);
                ivLiveIcon = itemView.findViewById(R.id.iv_live_icon);
                ivLockIcon = itemView.findViewById(R.id.iv_lock_icon);
                llEpisodeContent = itemView.findViewById(R.id.ll_episode_content);
            }
        }
    }

    /**
     * 网格间距装饰器
     */
    private static class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {
        private int spanCount;
        private int spacing;
        private boolean includeEdge;

        public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
            this.spanCount = spanCount;
            this.spacing = spacing;
            this.includeEdge = includeEdge;
        }

        @Override
        public void getItemOffsets(Rect outRect, View view, RecyclerView parent, RecyclerView.State state) {
            int position = parent.getChildAdapterPosition(view);
            int column = position % spanCount;

            if (includeEdge) {
                outRect.left = spacing - column * spacing / spanCount;
                outRect.right = (column + 1) * spacing / spanCount;

                if (position < spanCount) {
                    outRect.top = 10; // 上边距10dp
                }
                outRect.bottom = 10; // 下边距10dp
            } else {
                outRect.left = column * spacing / spanCount;
                outRect.right = spacing - (column + 1) * spacing / spanCount;
                if (position >= spanCount) {
                    outRect.top = spacing;
                }
            }
        }
    }
}
