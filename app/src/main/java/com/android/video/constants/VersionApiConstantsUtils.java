package com.android.video.constants;

/**
 * 版本检查模块API常量类 - 定义版本检查相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理版本检查相关的API常量，
 * 包括获取应用版本信息、检查更新等功能的API路径和参数定义。
 * </p>
 *
 * <p>
 * 包含的版本检查功能：
 * <ul>
 *   <li>获取最新版本信息</li>
 *   <li>检查是否需要更新</li>
 *   <li>获取更新包下载地址</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * // 构建版本检查API URL
 * String url = VersionApiConstantsUtils.buildVersionCheckUrl(baseUrl, 1);
 * 
 * // 或者使用通用方法
 * String apiUrl = VersionApiConstantsUtils.buildApiUrl(baseUrl, VersionApiConstantsUtils.API_GET_VERSION_INFO);
 * requestParams.put(VersionApiConstantsUtils.PARAM_CLIENT_TYPE, String.valueOf(VersionApiConstantsUtils.CLIENT_TYPE_ANDROID));
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class VersionApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 版本检查相关API路径常量 ==========

    /**
     * 获取应用版本信息API路径
     * <p>
     * 用于获取应用最新版本信息，包括版本号、更新描述、下载地址等。
     * 请求方法：GET
     * 认证要求：无需认证
     * </p>
     */
    public static final String API_GET_VERSION_INFO = "/app/customer/appVersion";

    // ========== 请求参数常量 ==========

    /**
     * 客户端类型参数名
     * <p>
     * 用于标识客户端平台类型，区分Android和iOS平台。
     * </p>
     */
    public static final String PARAM_CLIENT_TYPE = "clientType";

    // ========== 客户端类型常量 ==========

    /**
     * Android客户端类型
     * <p>
     * 表示Android平台的客户端类型值。
     * </p>
     */
    public static final int CLIENT_TYPE_ANDROID = 1;

    /**
     * iOS客户端类型
     * <p>
     * 表示iOS平台的客户端类型值。
     * </p>
     */
    public static final int CLIENT_TYPE_IOS = 2;

    // ========== 版本状态常量 ==========

    /**
     * 版本状态：待发布
     */
    public static final int VERSION_STATUS_PENDING = 0;

    /**
     * 版本状态：已发布
     */
    public static final int VERSION_STATUS_PUBLISHED = 1;

    // ========== 工具方法 ==========

    /**
     * 构建版本检查API URL
     * <p>
     * 便捷方法，用于构建包含客户端类型参数的版本检查API URL。
     * </p>
     * 
     * @param baseUrl API基础URL
     * @param clientType 客户端类型 (1=Android, 2=iOS)
     * @return 完整的版本检查API URL
     * 
     * @example
     * <pre>
     * String url = VersionApiConstantsUtils.buildVersionCheckUrl(
     *     "https://api.example.com", 
     *     VersionApiConstantsUtils.CLIENT_TYPE_ANDROID
     * );
     * // 返回: https://api.example.com/app/customer/appVersion?clientType=1
     * </pre>
     */
    public static String buildVersionCheckUrl(String baseUrl, int clientType) {
        String apiUrl = buildApiUrl(baseUrl, API_GET_VERSION_INFO);
        return apiUrl + "?" + PARAM_CLIENT_TYPE + "=" + clientType;
    }

    /**
     * 获取当前平台的客户端类型
     * <p>
     * 根据当前运行平台返回对应的客户端类型值。
     * 在Android平台上始终返回 {@link #CLIENT_TYPE_ANDROID}。
     * </p>
     * 
     * @return 当前平台的客户端类型
     */
    public static int getCurrentClientType() {
        return CLIENT_TYPE_ANDROID;
    }

    /**
     * 检查版本状态是否为已发布
     * <p>
     * 判断给定的版本状态是否表示版本已发布可用。
     * </p>
     * 
     * @param versionStatus 版本状态值
     * @return 如果版本已发布则返回true，否则返回false
     */
    public static boolean isVersionPublished(int versionStatus) {
        return versionStatus == VERSION_STATUS_PUBLISHED;
    }

    /**
     * 获取客户端类型描述
     * <p>
     * 将客户端类型数值转换为可读的描述文本。
     * </p>
     * 
     * @param clientType 客户端类型值
     * @return 客户端类型描述文本
     */
    public static String getClientTypeDescription(int clientType) {
        switch (clientType) {
            case CLIENT_TYPE_ANDROID:
                return "Android";
            case CLIENT_TYPE_IOS:
                return "iOS";
            default:
                return "Unknown";
        }
    }
}
