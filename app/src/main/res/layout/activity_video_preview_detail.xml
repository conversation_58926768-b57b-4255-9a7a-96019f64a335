<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:clipChildren="false"
    android:clipToPadding="false"
    tools:context=".ui.activity.VideoPreviewDetailActivity">

    <!-- 状态栏适配Guideline -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_status_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="@dimen/video_detail_nav_button_margin_top" />

    <!-- 整页滚动内容区域 -->
    <androidx.core.widget.NestedScrollView
        android:id="@+id/nested_scroll_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fillViewport="true"
        android:elevation="@dimen/elevation_medium"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <!-- 返回按钮 - 跟随滚动 -->
            <ImageButton
                android:id="@+id/button_back"
                android:layout_width="@dimen/video_detail_back_button_size"
                android:layout_height="@dimen/video_detail_back_button_size"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_nav_button_margin_top"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/login_btn_back"
                android:contentDescription="返回"
                android:elevation="@dimen/elevation_high"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- 分享按钮 - 跟随滚动 -->
            <ImageButton
                android:id="@+id/button_share"
                android:layout_width="@dimen/video_detail_share_button_size"
                android:layout_height="@dimen/video_detail_share_button_size"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_nav_button_margin_top"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:src="@drawable/movie_ic_share"
                android:contentDescription="分享"
                android:elevation="@dimen/elevation_high"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- 背景视频海报容器 -->
            <FrameLayout
                android:id="@+id/frame_video_preview_poster_scrollable"
                android:layout_width="0dp"
                android:layout_height="@dimen/video_detail_poster_height"
                android:background="@drawable/video_detail_poster_border"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

                <ImageView
                    android:id="@+id/image_video_preview_poster_scrollable"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/video_detail_poster_border_margin"
                    android:scaleType="centerCrop"
                    android:src="@drawable/movie_poster"
                    android:background="@drawable/video_detail_poster_inner" />

            </FrameLayout>

            <!-- 渐变遮罩 - 底层背景 -->
            <View
                android:id="@+id/view_gradient_overlay_scrollable"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/detail_background_gradient"
                android:elevation="@dimen/elevation_none"
                app:layout_constraintTop_toTopOf="@id/frame_video_preview_poster_scrollable"
                app:layout_constraintBottom_toBottomOf="@id/frame_video_preview_poster_scrollable"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- 小海报容器 - 内容层 -->
            <FrameLayout
                android:id="@+id/frame_small_poster_scrollable"
                android:layout_width="@dimen/video_detail_small_poster_width"
                android:layout_height="@dimen/video_detail_small_poster_height"
                android:layout_marginStart="@dimen/video_detail_small_poster_margin_start"
                android:layout_marginTop="@dimen/video_detail_small_poster_margin_top"
                android:background="@drawable/video_detail_small_poster_border"
                android:elevation="@dimen/elevation_low"
                app:layout_constraintTop_toBottomOf="@id/button_back"
                app:layout_constraintStart_toStartOf="parent">

                <ImageView
                    android:id="@+id/image_small_poster_scrollable"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_margin="@dimen/video_detail_poster_border_margin"
                    android:scaleType="centerCrop"
                    android:src="@drawable/movie_poster"
                    android:background="@drawable/video_detail_small_poster_background"
                    android:clipToOutline="true" />

            </FrameLayout>

            <!-- 视频标题 - 内容层 -->
            <TextView
                android:id="@+id/text_video_title_scrollable"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_title_margin_start"
                android:layout_marginTop="@dimen/video_detail_title_margin_top"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:text="视频预告标题"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_title_text_size"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end"
                android:elevation="@dimen/elevation_low"
                app:layout_constraintTop_toTopOf="@id/frame_small_poster_scrollable"
                app:layout_constraintStart_toEndOf="@id/frame_small_poster_scrollable"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- 预告上线时间区域 -->
            <TextView
                android:id="@+id/text_release_time_label_scrollable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_view_text_margin_start"
                android:layout_marginTop="@dimen/video_detail_view_text_margin_top"
                android:text="Release time "
                android:textColor="#8FFFFFFF"
                android:textSize="@dimen/video_detail_view_text_size"
                android:fontFamily="sans-serif"
                app:layout_constraintTop_toBottomOf="@id/text_video_title_scrollable"
                app:layout_constraintStart_toEndOf="@id/frame_small_poster_scrollable" />

            <TextView
                android:id="@+id/text_release_time_scrollable"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="2025-06-18"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_view_text_size"
                android:fontFamily="sans-serif"
                app:layout_constraintTop_toTopOf="@id/text_release_time_label_scrollable"
                app:layout_constraintBottom_toBottomOf="@id/text_release_time_label_scrollable"
                app:layout_constraintStart_toEndOf="@id/text_release_time_label_scrollable" />

            <!-- 订阅提醒按钮 -->
            <LinearLayout
                android:id="@+id/button_notify_scrollable"
                android:layout_width="@dimen/video_detail_like_button_width"
                android:layout_height="@dimen/video_detail_like_button_height"
                android:layout_marginStart="@dimen/video_detail_view_text_margin_start"
                android:layout_marginTop="@dimen/video_detail_like_button_margin_top"
                android:orientation="horizontal"
                android:gravity="center"
                android:background="@drawable/detail_like_button_background"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toBottomOf="@id/text_release_time_label_scrollable"
                app:layout_constraintStart_toEndOf="@id/frame_small_poster_scrollable">

                <ImageView
                    android:id="@+id/image_notify_icon_scrollable"
                    android:layout_width="@dimen/video_detail_like_icon_size"
                    android:layout_height="@dimen/video_detail_like_icon_size"
                    android:src="@drawable/movie_ic_notify1" />

                <TextView
                    android:id="@+id/text_notify_text_scrollable"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/video_detail_like_text_margin_start"
                    android:text="Notify me"
                    android:textColor="#000000"
                    android:textSize="@dimen/video_detail_like_text_size"
                    android:textStyle="bold" />

            </LinearLayout>

            <!-- Synopsis 模块 -->
            <TextView
                android:id="@+id/text_synopsis_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_synopsis_margin_top"
                android:layout_marginBottom="@dimen/video_detail_synopsis_title_margin_bottom"
                android:text="Synopsis"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_synopsis_title_text_size"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@id/button_notify_scrollable"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- 标签RecyclerView -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_tags"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_synopsis_element_spacing"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginBottom="@dimen/video_detail_synopsis_content_margin_top"
                android:clipToPadding="false"
                app:layout_constraintTop_toBottomOf="@id/text_synopsis_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- 概要内容 -->
            <TextView
                android:id="@+id/text_synopsis_content"
                style="@style/VideoDetailSynopsisTextStyle"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_synopsis_element_spacing"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginBottom="@dimen/video_detail_actor_margin_top"
                android:text="这里是视频预告的概要内容..."
                android:maxLines="3"
                android:ellipsize="end"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toBottomOf="@id/recycler_view_tags"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Actor 模块 -->
            <TextView
                android:id="@+id/text_actor_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_actor_margin_top"
                android:layout_marginBottom="@dimen/video_detail_synopsis_title_margin_bottom"
                android:text="Actor"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_synopsis_title_text_size"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@id/text_synopsis_content"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_actors"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_synopsis_element_spacing"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginBottom="@dimen/video_detail_director_margin_top"
                android:clipToPadding="false"
                app:layout_constraintTop_toBottomOf="@id/text_actor_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Director 模块 -->
            <TextView
                android:id="@+id/text_director_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_director_margin_top"
                android:layout_marginBottom="@dimen/video_detail_synopsis_title_margin_bottom"
                android:text="Director"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_synopsis_title_text_size"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@id/recycler_view_actors"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_directors"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_synopsis_element_spacing"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginBottom="@dimen/video_detail_more_like_this_margin_top"
                android:clipToPadding="false"
                app:layout_constraintTop_toBottomOf="@id/text_director_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- More Like This 模块 -->
            <TextView
                android:id="@+id/text_more_like_this_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_more_like_this_margin_top"
                android:layout_marginBottom="@dimen/video_detail_synopsis_title_margin_bottom"
                android:text="More Like This"
                android:textColor="#FFFFFF"
                android:textSize="@dimen/video_detail_more_like_this_title_text_size"
                android:textStyle="bold"
                app:layout_constraintTop_toBottomOf="@id/recycler_view_directors"
                app:layout_constraintStart_toStartOf="parent" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler_view_recommend_videos"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginEnd="@dimen/video_detail_nav_button_margin_horizontal"
                android:layout_marginTop="@dimen/video_detail_more_like_this_grid_margin_top"
                android:layout_marginBottom="@dimen/video_detail_nav_button_margin_horizontal"
                android:nestedScrollingEnabled="false"
                app:layout_constraintTop_toBottomOf="@id/text_more_like_this_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.core.widget.NestedScrollView>

    <!-- Continue Playing Button - Fixed at bottom -->
    <Button
        android:id="@+id/button_continue_playing"
        style="@style/ContinuePlayingButtonStyle"
        android:layout_width="@dimen/continue_playing_button_width"
        android:layout_height="@dimen/continue_playing_button_height"
        android:layout_marginBottom="@dimen/continue_playing_button_margin_bottom"
        android:text="Continue playing"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
