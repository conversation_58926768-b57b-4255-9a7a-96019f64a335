package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import com.android.video.model.VideoModel;
import java.util.ArrayList;
import java.util.List;

/**
 * 测试报告生成器
 * 用于生成完整的修复验证报告
 * 
 * <AUTHOR> Team
 */
public class TestReportGenerator {
    
    private static final String TAG = "TestReport";
    
    /**
     * 生成完整的修复验证报告
     */
    public static String generateCompleteReport(Activity mainActivity, List<VideoModel> testVideos) {
        List<TestValidationUtils.TestResult> results = new ArrayList<>();
        
        try {
            // 1. 系统信息检查
            Log.i(TAG, "开始生成测试报告...");
            TestValidationUtils.debugSystemInfo(mainActivity);
            
            // 2. MainActivity导航栏适配测试
            TestValidationUtils.TestResult navResult = 
                TestValidationUtils.validateMainActivityNavigationBar(mainActivity);
            results.add(navResult);
            
            // 3. VideoModel数据完整性测试
            if (testVideos != null && !testVideos.isEmpty()) {
                int validVideoCount = 0;
                for (VideoModel video : testVideos) {
                    TestValidationUtils.TestResult videoResult = 
                        TestValidationUtils.validateVideoModelData(video);
                    if (videoResult.success) {
                        validVideoCount++;
                    }
                }
                
                boolean allVideosValid = validVideoCount == testVideos.size();
                String videoMessage = String.format("VideoModel数据验证 - 总数: %d, 有效: %d", 
                    testVideos.size(), validVideoCount);
                String videoDetails = String.format("有效率: %.1f%%", 
                    (validVideoCount * 100.0 / testVideos.size()));
                
                results.add(new TestValidationUtils.TestResult(allVideosValid, videoMessage, videoDetails));
            }
            
            // 4. 配置变化适配测试
            android.content.res.Configuration config = mainActivity.getResources().getConfiguration();
            String orientation = config.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE ? "横屏" : "竖屏";
            TestValidationUtils.TestResult configResult = 
                TestValidationUtils.validateConfigurationChange(mainActivity, orientation);
            results.add(configResult);
            
            // 5. Android版本兼容性检查
            TestValidationUtils.TestResult compatResult = validateAndroidCompatibility(mainActivity);
            results.add(compatResult);
            
            // 6. 生成最终报告
            String report = TestValidationUtils.generateTestReport(results);
            Log.i(TAG, "测试报告生成完成");
            
            return report;
            
        } catch (Exception e) {
            Log.e(TAG, "生成测试报告异常", e);
            return "测试报告生成失败: " + e.getMessage();
        }
    }
    
    /**
     * 验证Android版本兼容性
     */
    private static TestValidationUtils.TestResult validateAndroidCompatibility(Context context) {
        try {
            int apiLevel = android.os.Build.VERSION.SDK_INT;
            StringBuilder details = new StringBuilder();
            
            details.append("API级别: ").append(apiLevel).append("\n");
            details.append("Android版本: ").append(android.os.Build.VERSION.RELEASE).append("\n");
            
            boolean isCompatible = true;
            String message = "Android版本兼容性检查";
            
            // 检查edge-to-edge支持（Android 15+）
            if (apiLevel >= 35) { // Android 15
                details.append("支持强制edge-to-edge模式\n");
            } else if (apiLevel >= 29) { // Android 10+
                details.append("支持手势导航\n");
            } else {
                details.append("仅支持按钮导航\n");
            }
            
            // 检查WindowInsets API支持
            if (apiLevel >= 30) { // Android 11+
                details.append("支持完整WindowInsets API\n");
            } else if (apiLevel >= 21) { // Android 5.0+
                details.append("支持基础WindowInsets API\n");
            } else {
                details.append("不支持WindowInsets API\n");
                isCompatible = false;
            }
            
            if (isCompatible) {
                message += " - 兼容";
            } else {
                message += " - 不兼容";
            }
            
            return new TestValidationUtils.TestResult(isCompatible, message, details.toString());
            
        } catch (Exception e) {
            return new TestValidationUtils.TestResult(false, 
                "Android兼容性检查异常: " + e.getMessage(), e.toString());
        }
    }
    
    /**
     * 生成修复前后对比报告
     */
    public static String generateBeforeAfterReport() {
        StringBuilder report = new StringBuilder();
        
        report.append("=== 修复前后对比报告 ===\n\n");
        
        report.append("## 问题1: 主页底部导航栏重叠问题\n");
        report.append("**修复前:**\n");
        report.append("- MainActivity继承BaseFullScreenActivity但存在实现问题\n");
        report.append("- 布局参数类型不匹配(ConstraintLayout.LayoutParams vs MarginLayoutParams)\n");
        report.append("- 手势导航检测不够准确\n");
        report.append("- 缺少调试和验证机制\n");
        report.append("- 不支持配置变化动态适配\n\n");
        
        report.append("**修复后:**\n");
        report.append("- 正确处理ConstraintLayout.LayoutParams类型\n");
        report.append("- 实现三种方法综合判断手势导航\n");
        report.append("- 集成NavigationBarUtils调试功能\n");
        report.append("- 添加异常处理和回退方案\n");
        report.append("- 支持配置变化监听和动态适配\n");
        report.append("- 符合Android 15 edge-to-edge要求\n\n");
        
        report.append("## 问题2: 视频详情页面渲染失败问题\n");
        report.append("**修复前:**\n");
        report.append("- HomeFragment使用简单VideoModel构造函数\n");
        report.append("- synopsis、actors、directors、tags等字段为空\n");
        report.append("- 剧集区间显示\"1-1\"而非正确区间\n");
        report.append("- 概要、演员、导演、标签模块无法渲染\n\n");
        
        report.append("**修复后:**\n");
        report.append("- 创建createDetailedVideoModel辅助方法\n");
        report.append("- 为所有VideoModel添加完整测试数据\n");
        report.append("- 根据类别智能生成演员、导演、标签信息\n");
        report.append("- 为多集内容添加完整剧集列表\n");
        report.append("- 所有详情页模块正常渲染\n\n");
        
        report.append("## 修复效果总结\n");
        report.append("- ✅ 底部导航栏在手势导航和按钮导航下都能正确显示\n");
        report.append("- ✅ 视频详情页面所有模块正常渲染\n");
        report.append("- ✅ 支持Android 15 edge-to-edge模式\n");
        report.append("- ✅ 添加完整的调试和验证机制\n");
        report.append("- ✅ 提高代码健壮性和可维护性\n");
        
        return report.toString();
    }
    
    /**
     * 输出测试报告到日志
     */
    public static void logTestReport(String report) {
        String[] lines = report.split("\n");
        for (String line : lines) {
            Log.i(TAG, line);
        }
    }
}
