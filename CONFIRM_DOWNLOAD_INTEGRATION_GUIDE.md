# 下载确认接口集成指南

## 概述

本文档介绍如何在Android视频播放器项目中集成下载确认接口。该接口用于向服务器确认文件下载完成，包含下载记录ID、文件校验和以及本地存储路径等信息。

## 接口信息

- **接口地址**: `https://short-play-api.gymooit.cn/v1/app/player/confirmDownload`
- **请求方法**: POST
- **Content-Type**: application/json

### 请求参数

```json
{
  "downloadRecordId": "2d9bf4f909fef05528998764daa94c60", // 下载记录ID
  "fileChecksum": "a1b2c3d4e5f6",                        // 文件校验和
  "localPath": "/storage/emulated/0/AppName/videos/101.mp4" // 客户端存储路径
}
```

### 响应格式

```json
{
  "code": "200",
  "message": "success",
  "data": "Download success."
}
```

## 已实现的组件

### 1. API常量定义

在 `DownloadApiConstantsUtils.java` 中添加了以下常量：

```java
// API路径
public static final String API_CONFIRM_DOWNLOAD = "/app/player/confirmDownload";

// 请求参数
public static final String PARAM_DOWNLOAD_RECORD_ID = "downloadRecordId";
public static final String PARAM_FILE_CHECKSUM = "fileChecksum";
public static final String PARAM_LOCAL_PATH = "localPath";
```

### 2. 数据模型

#### 请求模型 - `ConfirmDownloadRequestModel`
- 包含下载记录ID、文件校验和、本地路径
- 提供参数验证功能
- 支持序列化

#### 响应模型 - `ConfirmDownloadResponseModel`
- 包含响应码、消息、数据
- 提供成功/失败判断方法
- 提供错误信息获取方法

### 3. API服务类 - `ConfirmDownloadApiService`

- 单例模式设计
- 异步网络请求
- 完整的错误处理
- 主线程回调

## 使用方法

### 基本用法

```java
// 1. 创建请求模型
ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
    "2d9bf4f909fef05528998764daa94c60", // 下载记录ID
    "a1b2c3d4e5f6",                      // 文件校验和
    "/storage/emulated/0/AppName/videos/101.mp4" // 本地路径
);

// 2. 获取API服务实例
ConfirmDownloadApiService apiService = ConfirmDownloadApiService.getInstance();

// 3. 发送确认下载请求
apiService.confirmDownload(requestModel, new ConfirmDownloadApiService.ConfirmDownloadCallback() {
    @Override
    public void onSuccess(ConfirmDownloadResponseModel response) {
        // 处理成功情况
        Log.d(TAG, "确认下载成功: " + response.getSuccessMessage());
        // 更新UI、数据库等
    }

    @Override
    public void onError(String errorMessage) {
        // 处理失败情况
        Log.e(TAG, "确认下载失败: " + errorMessage);
        // 显示错误提示
    }
});
```

### 参数验证

```java
ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(...);

// 验证参数有效性
if (!requestModel.isValid()) {
    String error = requestModel.getValidationError();
    Log.e(TAG, "参数验证失败: " + error);
    return;
}

// 参数有效，继续发送请求
apiService.confirmDownload(requestModel, callback);
```

## 集成到现有下载流程

### 1. 在下载完成后调用确认接口

建议在以下位置集成确认下载接口：

```java
// 在下载完成的回调中
public void onDownloadComplete(String downloadRecordId, String filePath, String checksum) {
    // 创建确认下载请求
    ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
        downloadRecordId,
        checksum,
        filePath
    );
    
    // 发送确认请求
    ConfirmDownloadApiService.getInstance().confirmDownload(requestModel, 
        new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                // 更新下载状态为已确认
                updateDownloadStatus(downloadRecordId, "CONFIRMED");
            }
            
            @Override
            public void onError(String errorMessage) {
                // 记录错误，可能需要重试
                Log.e(TAG, "确认下载失败: " + errorMessage);
            }
        });
}
```

### 2. 与现有下载服务集成

可以在以下现有服务中集成确认下载功能：

- `DownloadProgressApiService` - 在下载进度达到100%时调用
- `DownloadChapterApiService` - 在章节下载完成时调用
- 自定义下载管理器 - 在文件下载完成时调用

### 3. 错误处理和重试机制

```java
private void confirmDownloadWithRetry(ConfirmDownloadRequestModel requestModel, int maxRetries) {
    ConfirmDownloadApiService.getInstance().confirmDownload(requestModel, 
        new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                // 成功处理
                handleConfirmSuccess(response);
            }
            
            @Override
            public void onError(String errorMessage) {
                if (maxRetries > 0) {
                    // 延迟重试
                    new Handler().postDelayed(() -> {
                        confirmDownloadWithRetry(requestModel, maxRetries - 1);
                    }, 2000);
                } else {
                    // 重试次数用完，记录失败
                    handleConfirmFailure(errorMessage);
                }
            }
        });
}
```

## 最佳实践

### 1. 参数验证
- 始终在发送请求前验证参数
- 提供清晰的错误提示

### 2. 错误处理
- 实现适当的重试机制
- 记录详细的错误日志
- 向用户提供友好的错误提示

### 3. 性能优化
- 避免在主线程中进行网络请求
- 合理使用线程池
- 考虑请求去重

### 4. 安全性
- 验证文件校验和的准确性
- 确保本地路径的安全性
- 保护敏感信息

## 测试

使用 `ConfirmDownloadExample.java` 中的示例代码进行测试：

```java
// 运行所有测试示例
ConfirmDownloadExample.runAllExamples();

// 或者运行特定测试
ConfirmDownloadExample.completeConfirmDownloadFlow(
    "test_record_id",
    "test_checksum", 
    "/test/path"
);
```

## 注意事项

1. **网络权限**: 确保应用具有网络访问权限
2. **存储权限**: 确保应用具有文件读取权限
3. **线程安全**: 所有回调都在主线程中执行
4. **内存管理**: 注意避免内存泄漏，特别是在Activity/Fragment中使用时
5. **网络状态**: 建议在网络可用时才发送请求

## 故障排除

### 常见问题

1. **参数验证失败**: 检查downloadRecordId、fileChecksum、localPath是否为空
2. **网络请求失败**: 检查网络连接和API地址
3. **JSON解析失败**: 检查服务器响应格式是否正确
4. **回调未执行**: 确保在主线程中处理UI更新

### 调试技巧

- 启用详细日志记录
- 使用网络抓包工具检查请求
- 验证服务器响应格式
- 检查权限配置

## 更新日志

- v1.0: 初始版本，实现基本的确认下载功能
- 支持参数验证、错误处理、异步回调
- 提供完整的使用示例和集成指南
