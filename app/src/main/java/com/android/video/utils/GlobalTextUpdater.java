package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.android.video.R;
import com.android.video.manager.LocalizationManager;
import java.util.HashMap;
import java.util.Map;

/**
 * 全局文本更新器
 * 负责更新整个应用中的所有静态文本
 * 支持英语、俄语、Kaza语三种语言
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class GlobalTextUpdater {
    
    private static final String TAG = "GlobalTextUpdater";
    
    // 文本映射表 - 将英文文本映射到翻译键
    private static final Map<String, String> TEXT_KEY_MAP = new HashMap<>();
    
    static {
        // 登录相关
        TEXT_KEY_MAP.put("Log in", "login");
        TEXT_KEY_MAP.put("Log in Phone Number", "login_phone");
        TEXT_KEY_MAP.put("Login with VK", "login_vk");
        TEXT_KEY_MAP.put("Log in Tiktok", "login_tiktok");
        TEXT_KEY_MAP.put("Mobile Number", "mobile_number");
        TEXT_KEY_MAP.put("Send Code", "send_code");
        TEXT_KEY_MAP.put("Verification Code", "verification_code");
        TEXT_KEY_MAP.put("Guest", "guest");
        
        // 主页相关
        TEXT_KEY_MAP.put("Home", "home");
        TEXT_KEY_MAP.put("For you", "for_you");
        TEXT_KEY_MAP.put("Discover", "discover");
        TEXT_KEY_MAP.put("My list", "my_list");
        TEXT_KEY_MAP.put("Profile", "profile");
        TEXT_KEY_MAP.put("Categories", "categories");
        TEXT_KEY_MAP.put("See All", "see_all");
        TEXT_KEY_MAP.put("Continue Watching", "continue_watching");
        TEXT_KEY_MAP.put("Coming Soon", "coming_soon");
        TEXT_KEY_MAP.put("Notify me", "notify_me");
        TEXT_KEY_MAP.put("Best For You", "best_for_you");
        TEXT_KEY_MAP.put("Trending Now", "trending_now");
        TEXT_KEY_MAP.put("Today's Hot", "todays_hot");
        TEXT_KEY_MAP.put("Most popular", "most_popular");
        TEXT_KEY_MAP.put("Views", "views");
        TEXT_KEY_MAP.put("List", "list");
        
        // 搜索相关
        TEXT_KEY_MAP.put("Recent searches", "recent_searches");
        TEXT_KEY_MAP.put("Trending searches", "trending_searches");
        TEXT_KEY_MAP.put("3 related contents", "related_contents");
        TEXT_KEY_MAP.put("No related content", "no_related_content");
        
        // 播放相关
        TEXT_KEY_MAP.put("Share", "share");
        TEXT_KEY_MAP.put("Comments", "comments");
        TEXT_KEY_MAP.put("Subtitles", "subtitles");
        TEXT_KEY_MAP.put("Do you want to enable subtitles", "enable_subtitles");
        TEXT_KEY_MAP.put("Episodes", "episodes");
        TEXT_KEY_MAP.put("Add to Like", "add_to_like");
        TEXT_KEY_MAP.put("Synopsis", "synopsis");
        TEXT_KEY_MAP.put("Actor", "actor");
        TEXT_KEY_MAP.put("Director", "director");
        TEXT_KEY_MAP.put("More Like This", "more_like_this");
        TEXT_KEY_MAP.put("Release time", "release_time");
        TEXT_KEY_MAP.put("Continue playing", "continue_playing");
        TEXT_KEY_MAP.put("Play", "play");
        
        // VIP相关
        TEXT_KEY_MAP.put("My points", "my_points");
        TEXT_KEY_MAP.put("Subscribe for free to see all ?", "subscribe_free");
        TEXT_KEY_MAP.put("Unlock all series for 7 days", "unlock_7_days");
        TEXT_KEY_MAP.put("Unlock and receive 300 bonus points", "bonus_points");
        TEXT_KEY_MAP.put("This episode is a VIP chapter, please unlock it first", "vip_chapter");
        TEXT_KEY_MAP.put("Unlock now", "unlock_now");
        TEXT_KEY_MAP.put("Watch AD to unlock", "watch_ad");
        TEXT_KEY_MAP.put("Open VIP for free viewing", "open_vip");
        TEXT_KEY_MAP.put("Watching AD only requires", "ad_required");
        TEXT_KEY_MAP.put("VIP", "vip");
        TEXT_KEY_MAP.put("Automatic renewal", "automatic_renewal");
        TEXT_KEY_MAP.put("Last purchase", "last_purchase");
        TEXT_KEY_MAP.put("Watch videos and enjoy 1080P", "watch_1080p");
        TEXT_KEY_MAP.put("Expiration date 06/12/2025", "expiration_date");
        TEXT_KEY_MAP.put("Subscribe", "subscribe");
        TEXT_KEY_MAP.put("Watch more episodes", "watch_more");
        TEXT_KEY_MAP.put("4 Privileges", "privileges");
        TEXT_KEY_MAP.put("Unlimited viewing", "unlimited_viewing");
        TEXT_KEY_MAP.put("1080p quality", "quality_1080p");
        TEXT_KEY_MAP.put("Daily points reward", "daily_points");
        TEXT_KEY_MAP.put("AD-free", "ad_free");
        TEXT_KEY_MAP.put("Tips", "tips");
        TEXT_KEY_MAP.put("Subscribe now", "subscribe_now");
        
        // 个人中心相关
        TEXT_KEY_MAP.put("Log out", "log_out");
        TEXT_KEY_MAP.put("Become VIP - Enjoy all benefits", "become_vip");
        TEXT_KEY_MAP.put("Unlimited access to all series", "unlimited_access_to_all_series");
        TEXT_KEY_MAP.put("Details", "details");
        TEXT_KEY_MAP.put("Language", "language");
        TEXT_KEY_MAP.put("Message", "message");
        TEXT_KEY_MAP.put("Bill", "bill");
        TEXT_KEY_MAP.put("Setting", "setting");
        TEXT_KEY_MAP.put("Edit", "edit");
        TEXT_KEY_MAP.put("Go", "go");
        TEXT_KEY_MAP.put("GO", "go");
        TEXT_KEY_MAP.put("Renewal by Week", "renewal_by_week");
        TEXT_KEY_MAP.put("My point", "my_point");
        TEXT_KEY_MAP.put("details", "details");
        
        // 我的列表相关
        TEXT_KEY_MAP.put("Following", "following");
        TEXT_KEY_MAP.put("History", "history");
        TEXT_KEY_MAP.put("Reserved", "reserved");
        TEXT_KEY_MAP.put("Cancel Notify", "cancel_notify");
        TEXT_KEY_MAP.put("downloaded", "downloaded");
        TEXT_KEY_MAP.put("Downloaded / Total episodes", "downloaded_total");
        
        // 设置相关
        TEXT_KEY_MAP.put("Terms of Service", "terms_of_service");
        TEXT_KEY_MAP.put("Privacy Policy", "privacy_policy");
        TEXT_KEY_MAP.put("Clear Cache", "clear_cache");
        TEXT_KEY_MAP.put("About Us", "about_us");
        TEXT_KEY_MAP.put("Feedback", "feedback");
        TEXT_KEY_MAP.put("Version", "version");
        TEXT_KEY_MAP.put("Log Out", "log_out");
        TEXT_KEY_MAP.put("Delete Account", "delete_account");
        TEXT_KEY_MAP.put("English", "english");
        TEXT_KEY_MAP.put("Russian", "russian");
        TEXT_KEY_MAP.put("Kazakh", "kazakh");
        
        // 积分相关
        TEXT_KEY_MAP.put("Redeem Code", "redeem_code");
        TEXT_KEY_MAP.put("Please enter the redemption code to redeem the membership duration", "enter_code");
        TEXT_KEY_MAP.put("Please enter", "please_enter");
        TEXT_KEY_MAP.put("Exchange", "exchange");
        TEXT_KEY_MAP.put("Reward points history", "reward_history");
        TEXT_KEY_MAP.put("Transaction history", "transaction_history");
        TEXT_KEY_MAP.put("New user registration", "new_user");
        TEXT_KEY_MAP.put("Daily login", "daily_login");
        TEXT_KEY_MAP.put("Episodes unlocked", "episodes_unlocked");
        TEXT_KEY_MAP.put("Top up", "top_up");
        TEXT_KEY_MAP.put("VIP Gifts", "vip_gifts");
        TEXT_KEY_MAP.put("VIP Record", "vip_record");
        TEXT_KEY_MAP.put("Points purchase", "points_purchase");
        TEXT_KEY_MAP.put("Watching Record", "watching_record");
        TEXT_KEY_MAP.put("Unlock 1 Episode", "unlock_episode");
        
        // 消息相关
        TEXT_KEY_MAP.put("Message Notification", "message_notification");
        TEXT_KEY_MAP.put("The new drama you are following has been launched", "new_drama");
        
        // 编辑个人信息相关
        TEXT_KEY_MAP.put("Edit profile", "edit_profile");
        TEXT_KEY_MAP.put("Modify avatar", "modify_avatar");
        TEXT_KEY_MAP.put("Change nickname", "change_nickname");
        TEXT_KEY_MAP.put("Contact information", "contact_info");
        TEXT_KEY_MAP.put("Current IP location", "current_ip");
        TEXT_KEY_MAP.put("Save", "save");

        // 添加更多常见文本
        TEXT_KEY_MAP.put("9:41", "time_941"); // 状态栏时间
        TEXT_KEY_MAP.put("UID:43310879", "uid_sample");
        TEXT_KEY_MAP.put("0", "zero");
        TEXT_KEY_MAP.put("My points", "my_points");
        TEXT_KEY_MAP.put("Unlimited access to all series", "unlimited_access_to_all_series");
        TEXT_KEY_MAP.put("Automatic renewal", "automatic_renewal");
        TEXT_KEY_MAP.put("Renewal by Week", "renewal_by_week");
        TEXT_KEY_MAP.put("Expiration date 06/12/2025", "expiration_date");

        // 底部导航
        TEXT_KEY_MAP.put("Home", "home");
        TEXT_KEY_MAP.put("For you", "for_you");
        TEXT_KEY_MAP.put("My list", "my_list");
        TEXT_KEY_MAP.put("Profile", "profile");

        // 底部导航的其他可能显示文本
        TEXT_KEY_MAP.put("Discover", "discover"); // Discover页面使用discover翻译

        // My List页面
        TEXT_KEY_MAP.put("My List", "my_list");
        TEXT_KEY_MAP.put("Following", "following");
        TEXT_KEY_MAP.put("History", "history");
        TEXT_KEY_MAP.put("Interest", "interest");

        // Setting页面
        TEXT_KEY_MAP.put("Setting", "setting");
        TEXT_KEY_MAP.put("Terms of Service", "terms_service"); // 修正为正确的键
        TEXT_KEY_MAP.put("Privacy Policy", "privacy_policy");
        TEXT_KEY_MAP.put("Clear Cache", "clear_cache");
        TEXT_KEY_MAP.put("208.2M", "cache_size_sample");
        TEXT_KEY_MAP.put("About Us", "about_us");
        TEXT_KEY_MAP.put("Language", "language");
        TEXT_KEY_MAP.put("English", "english");
        TEXT_KEY_MAP.put("Feedback", "feedback");
        TEXT_KEY_MAP.put("Version", "version");
        TEXT_KEY_MAP.put("V1.0.0", "version_number");
        TEXT_KEY_MAP.put("Log Out", "log_out");
        TEXT_KEY_MAP.put("Delete Account", "delete_account");

        // Bill页面
        TEXT_KEY_MAP.put("Bill", "bill");
        TEXT_KEY_MAP.put("VIP Record", "vip_record");
        TEXT_KEY_MAP.put("Points purchase", "points_purchase");
        TEXT_KEY_MAP.put("Video Record", "video_record");
        TEXT_KEY_MAP.put("There are no relevant records yet...", "no_records_yet");

        // Subscribe页面
        TEXT_KEY_MAP.put("Redeem Code", "redeem_code");
        TEXT_KEY_MAP.put("Subscribe", "subscribe");
        TEXT_KEY_MAP.put("Watch more episodes", "watch_more"); // 修正为正确的键
        TEXT_KEY_MAP.put("4 Privileges", "privileges"); // 修正为正确的键
        TEXT_KEY_MAP.put("Unlimited viewing", "unlimited_viewing");
        TEXT_KEY_MAP.put("1080p quality", "quality_1080p");
        TEXT_KEY_MAP.put("Daily points reward", "daily_points"); // 修正为正确的键
        TEXT_KEY_MAP.put("AD-free", "ad_free");
        TEXT_KEY_MAP.put("Tips", "tips");
        TEXT_KEY_MAP.put("VIP Privileges", "vip_privileges");
        TEXT_KEY_MAP.put("• Ad-free streaming – Watch without interruptions", "privilege_ad_free_streaming");
        TEXT_KEY_MAP.put("• Early access – New episodes 1 week before free users", "privilege_early_access");
        TEXT_KEY_MAP.put("• Exclusive content – Members-only short films & behind-the-scenes", "privilege_exclusive_content");
        TEXT_KEY_MAP.put("• HD/4K streaming – Enhanced video quality", "privilege_hd_streaming");
        TEXT_KEY_MAP.put("• Download offline – Save videos without WiFi", "privilege_download_offline");
        TEXT_KEY_MAP.put("Subscribe now", "subscribe_now");

        // 登录页面
        TEXT_KEY_MAP.put("Log In", "log_in");
        TEXT_KEY_MAP.put("+1", "country_code_us");
        TEXT_KEY_MAP.put("Send Code", "send_code");

        // 搜索页面
        TEXT_KEY_MAP.put("Recent searches", "recent_searches");
        TEXT_KEY_MAP.put("Trending searches", "trending_searches");

        // 标签相关
        TEXT_KEY_MAP.put("Romance", "romance");
        TEXT_KEY_MAP.put("Action", "action");
        TEXT_KEY_MAP.put("Comedy", "comedy");
        TEXT_KEY_MAP.put("Drama", "drama");
        TEXT_KEY_MAP.put("Horror", "horror");
        TEXT_KEY_MAP.put("Thriller", "thriller");
        TEXT_KEY_MAP.put("Adventure", "adventure");
        TEXT_KEY_MAP.put("Fantasy", "fantasy");
        TEXT_KEY_MAP.put("Sci-Fi", "sci_fi");
        TEXT_KEY_MAP.put("Mystery", "mystery");

        // 通用按钮和操作
        TEXT_KEY_MAP.put("OK", "ok");
        TEXT_KEY_MAP.put("Cancel", "cancel");
        TEXT_KEY_MAP.put("Confirm", "confirm");
        TEXT_KEY_MAP.put("Back", "back");
        TEXT_KEY_MAP.put("Next", "next");
        TEXT_KEY_MAP.put("Done", "done");
        TEXT_KEY_MAP.put("Edit", "edit");
        TEXT_KEY_MAP.put("Delete", "delete");
        TEXT_KEY_MAP.put("Share", "share");
        TEXT_KEY_MAP.put("Download", "download");
        TEXT_KEY_MAP.put("Play", "play");
        TEXT_KEY_MAP.put("Pause", "pause");
        TEXT_KEY_MAP.put("Stop", "stop");
    }
    
    /**
     * 更新Activity中的所有文本
     */
    public static void updateActivityTexts(Activity activity) {
        if (activity == null) return;

        try {
            View rootView = activity.findViewById(android.R.id.content);
            // 清除所有翻译键标签，强制重新匹配
            clearTranslationTags(rootView);
            // 强制重新匹配所有文本
            forceUpdateAllTexts(rootView, activity);
            Log.d(TAG, "Updated texts for activity: " + activity.getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error updating activity texts", e);
        }
    }
    
    /**
     * 更新View中的所有文本
     */
    public static void updateViewTexts(View rootView, Context context) {
        if (rootView == null || context == null) return;

        try {
            // 清除所有翻译键标签，强制重新匹配
            clearTranslationTags(rootView);
            // 强制重新匹配所有文本
            forceUpdateAllTexts(rootView, context);
            Log.d(TAG, "Updated texts for view");
        } catch (Exception e) {
            Log.e(TAG, "Error updating view texts", e);
        }
    }
    
    /**
     * 递归更新所有文本
     */
    private static void updateTextsRecursively(View view, Context context) {
        if (view == null || context == null) return;

        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            String currentText = textView.getText().toString().trim();

            if (!currentText.isEmpty()) {
                // 首先检查是否已经有保存的翻译键
                String translationKey = (String) textView.getTag(R.id.translation_key_tag);

                // 如果没有保存的翻译键，尝试从当前文本查找
                if (translationKey == null) {
                    translationKey = TEXT_KEY_MAP.get(currentText);

                    // 如果找到了翻译键，保存到tag中
                    if (translationKey != null) {
                        textView.setTag(R.id.translation_key_tag, translationKey);
                    }
                }

                // 如果有翻译键，更新文本
                if (translationKey != null) {
                    String translatedText = LocalizationUtils.getString(context, translationKey);
                    if (!translatedText.equals(translationKey) && !translatedText.equals(currentText)) {
                        textView.setText(translatedText);
                        Log.d(TAG, "Updated: " + currentText + " -> " + translatedText + " (key: " + translationKey + ")");
                    }
                }
            }
        } else if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                updateTextsRecursively(viewGroup.getChildAt(i), context);
            }
        }
    }

    
    /**
     * 强制更新所有文本（更激进的方法）
     */
    private static void forceUpdateAllTexts(View view, Context context) {
        if (view == null || context == null) return;

        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            String currentText = textView.getText().toString().trim();

            if (!currentText.isEmpty()) {
                // 尝试所有可能的翻译键匹配
                String translationKey = findBestTranslationKey(currentText);

                if (translationKey != null) {
                    String translatedText = LocalizationUtils.getString(context, translationKey);
                    if (!translatedText.equals(translationKey)) {
                        textView.setText(translatedText);
                        // 保存翻译键以便下次使用
                        textView.setTag(R.id.translation_key_tag, translationKey);
                        Log.d(TAG, "Force updated: " + currentText + " -> " + translatedText + " (key: " + translationKey + ")");
                    }
                }
            }
        } else if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                forceUpdateAllTexts(viewGroup.getChildAt(i), context);
            }
        }
    }

    /**
     * 寻找最佳的翻译键匹配
     */
    private static String findBestTranslationKey(String text) {
        if (text == null || text.trim().isEmpty()) {
            return null;
        }

        // 直接匹配
        String key = TEXT_KEY_MAP.get(text);
        if (key != null) {
            return key;
        }

        // 忽略大小写匹配
        for (Map.Entry<String, String> entry : TEXT_KEY_MAP.entrySet()) {
            if (entry.getKey().equalsIgnoreCase(text)) {
                return entry.getValue();
            }
        }

        // 部分匹配（去除空格和标点）
        String cleanText = text.replaceAll("[\\s\\p{Punct}]", "").toLowerCase();
        for (Map.Entry<String, String> entry : TEXT_KEY_MAP.entrySet()) {
            String cleanKey = entry.getKey().replaceAll("[\\s\\p{Punct}]", "").toLowerCase();
            if (cleanKey.equals(cleanText)) {
                return entry.getValue();
            }
        }

        return null;
    }

    /**
     * 清除所有翻译键标签
     * 在语言切换时调用，强制重新匹配文本
     */
    private static void clearTranslationTags(View view) {
        if (view == null) return;

        if (view instanceof TextView) {
            view.setTag(R.id.translation_key_tag, null);
        } else if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                clearTranslationTags(viewGroup.getChildAt(i));
            }
        }
    }

    /**
     * 添加新的文本映射
     */
    public static void addTextMapping(String originalText, String translationKey) {
        TEXT_KEY_MAP.put(originalText, translationKey);
    }

    /**
     * 获取所有文本映射
     */
    public static Map<String, String> getAllTextMappings() {
        return new HashMap<>(TEXT_KEY_MAP);
    }
}
