package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.MyListVideo;
import java.util.List;

/**
 * 搜索结果视频适配器
 * 用于显示搜索结果中的视频列表
 */
public class SearchResultVideoAdapter extends RecyclerView.Adapter<SearchResultVideoAdapter.SearchResultVideoViewHolder> {

    private List<MyListVideo> videos;
    private OnVideoClickListener listener;

    public interface OnVideoClickListener {
        void onVideoClick(MyListVideo video);
        void onPlayClick(MyListVideo video);
        void onLikeClick(MyListVideo video);
    }

    public SearchResultVideoAdapter(List<MyListVideo> videos, OnVideoClickListener listener) {
        this.videos = videos;
        this.listener = listener;
    }

    @NonNull
    @Override
    public SearchResultVideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_search_result_video, parent, false);
        return new SearchResultVideoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull SearchResultVideoViewHolder holder, int position) {
        MyListVideo video = videos.get(position);
        holder.bind(video);
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public void updateVideos(List<MyListVideo> newVideos) {
        this.videos = newVideos;
        notifyDataSetChanged();
    }

    class SearchResultVideoViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private TextView tvTitle;
        private TextView tvCurrentEpisode;
        private TextView tvTotalEpisode;
        private TextView tvDescription;
        private ImageView btnPlay;
        private ImageView ivLike;

        public SearchResultVideoViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvCurrentEpisode = itemView.findViewById(R.id.tv_current_episode);
            tvTotalEpisode = itemView.findViewById(R.id.tv_total_episode);
            tvDescription = itemView.findViewById(R.id.tv_description);
            btnPlay = itemView.findViewById(R.id.btn_play);
            ivLike = itemView.findViewById(R.id.iv_like);
        }

        public void bind(MyListVideo video) {
            // 设置标题
            tvTitle.setText(video.getTitle());

            // 设置剧集信息
            tvCurrentEpisode.setText("EP." + video.getCurrentEpisode());

            // 设置描述信息
            if (video.getDescription() != null && !video.getDescription().isEmpty()) {
                tvDescription.setText(video.getDescription());
            } else {
                tvDescription.setText("a captivating historical drama set in a grand palace, where love and power collide. The story …");
            }

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onVideoClick(video);
                }
            });
        }
    }
}
