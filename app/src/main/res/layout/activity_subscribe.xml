<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             xmlns:app="http://schemas.android.com/apk/res-auto"
             xmlns:tools="http://schemas.android.com/tools"
             android:layout_width="match_parent"
             android:layout_height="match_parent"
             android:background="@color/black"
             tools:context=".ui.activity.SubscribeActivity">

    <!-- 可滚动的内容区域 -->
    <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="false">

        <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingBottom="100dp">

            <!-- 头部区域 - 带背景和导航栏 -->
            <FrameLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/vip_bg_height"
                android:clipChildren="false"
                android:clipToPadding="false">

                <!-- VIP背景图片 -->
                <ImageView
                    android:id="@+id/iv_vip_background"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/vip_bg_height"
                    android:src="@drawable/vip_bg"
                    android:scaleType="centerCrop" />

                <!-- VIP背景边缘模糊遮罩 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/vip_bg_height"
                    android:background="@drawable/vip_bg_edge_blur" />

                <!-- 导航栏 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:paddingTop="@dimen/subscribe_header_back_button_margin_top"
                    android:paddingStart="@dimen/subscribe_header_back_button_margin_start"
                    android:paddingEnd="@dimen/subscribe_header_redeem_code_margin_end">

                    <!-- 返回按钮 -->
                    <ImageView
                        android:id="@+id/btn_back"
                        android:layout_width="@dimen/subscribe_header_back_button_size"
                        android:layout_height="@dimen/subscribe_header_back_button_size"
                        android:src="@drawable/login_btn_back"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true" />

                    <!-- 中间空白区域 -->
                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_weight="1" />

                    <!-- Redeem Code -->
                    <LinearLayout
                        android:id="@+id/layout_redeem_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:gravity="center"
                        android:clickable="true"
                        android:focusable="true"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:padding="8dp">

                        <!-- 图标和文字水平布局 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <ImageView
                                android:layout_width="@dimen/subscribe_header_redeem_icon_size"
                                android:layout_height="@dimen/subscribe_header_redeem_icon_size"
                                android:src="@drawable/vip_ic_redeem_code" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="4dp"
                                android:text="Redeem Code"
                                android:textColor="#fcffffff"
                                android:textSize="@dimen/subscribe_header_redeem_text_size"
                                android:fontFamily="sans-serif-medium"
                                android:gravity="center_vertical" />
                        </LinearLayout>

                        <!-- 双下划线 -->
                        <LinearLayout
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="2dp"
                            android:layout_marginStart="4dp"
                            android:orientation="vertical">

                            <!-- 第一条下划线 -->
                            <View
                                android:layout_width="@dimen/subscribe_header_redeem_underline_width"
                                android:layout_height="@dimen/subscribe_header_redeem_underline_height"
                                android:background="#ffffd388" />

                            <!-- 第二条下划线 -->
                            <View
                                android:layout_width="@dimen/subscribe_header_redeem_underline_width"
                                android:layout_height="@dimen/subscribe_header_redeem_underline_height"
                                android:layout_marginTop="@dimen/subscribe_header_redeem_underline_spacing"
                                android:background="#ffffd388" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- 头部内容 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:layout_gravity="center"
                    android:paddingTop="20dp"
                    android:paddingBottom="20dp">

                    <!-- Subscribe标题 -->
                    <TextView
                        android:id="@+id/tv_subscribe_title"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="35dp"
                        android:text="Subscribe"
                        android:textColor="#FFF8E6"
                        android:textSize="@dimen/subscribe_main_title_text_size"
                        android:fontFamily="sans-serif-medium"
                        android:gravity="center" />

                    <!-- Watch more episodes 区域 - 水平布局 -->
                    <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="30dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                        <!-- 左装饰图标 -->
                        <ImageView
                            android:id="@+id/iv_left_decoration"
                            android:layout_width="@dimen/subscribe_decoration_icon_size"
                            android:layout_height="@dimen/subscribe_decoration_icon_size"
                            android:layout_marginEnd="@dimen/subscribe_decoration_margin_horizontal"
                            android:src="@drawable/vip_left" />

                        <!-- Watch more episodes文字 -->
                        <TextView
                            android:id="@+id/tv_watch_more"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="Watch more episodes"
                            android:textColor="#fcfffaf0"
                            android:textSize="@dimen/subscribe_subtitle_text_size"
                            android:fontFamily="sans-serif-light"
                            android:gravity="center" />

                        <!-- 右装饰图标 -->
                        <ImageView
                            android:id="@+id/iv_right_decoration"
                            android:layout_width="@dimen/subscribe_decoration_icon_size"
                            android:layout_height="@dimen/subscribe_decoration_icon_size"
                            android:layout_marginStart="@dimen/subscribe_decoration_margin_horizontal"
                            android:src="@drawable/vip_right" />

                    </LinearLayout>

                </LinearLayout>

            </FrameLayout>

            <!-- VIP卡片容器 -->
            <LinearLayout
                    android:id="@+id/vip_cards_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginTop="@dimen/subscribe_card_margin_top">

            </LinearLayout>

            <!-- Loading状态 -->
            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_loading"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginTop="@dimen/subscribe_card_margin_top"
                    android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>



            <!-- 4 Privileges 标题 -->
            <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="@dimen/subscribe_privileges_title_margin_top"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                <ImageView
                        android:layout_width="@dimen/subscribe_privileges_crown_icon_size"
                        android:layout_height="@dimen/subscribe_privileges_crown_icon_size"
                        android:src="@drawable/vip_ic_huangguan"/>

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/subscribe_privileges_title_text_margin_start"
                        android:text="4 Privileges"
                        android:textColor="#fcffffff"
                        android:textSize="@dimen/subscribe_privileges_section_title_text_size"
                        android:fontFamily="sans-serif-medium"/>
            </LinearLayout>

            <!-- Privileges Grid -->
            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical">

                <!-- First Row -->
                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginBottom="@dimen/subscribe_privilege_row_margin_bottom">

                    <!-- All Free -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="@dimen/subscribe_privilege_card_height"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/subscribe_privilege_card_margin"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/subscribe_privilege_card_bg"
                            android:padding="@dimen/subscribe_privilege_card_padding">

                        <ImageView
                                android:layout_width="@dimen/subscribe_privilege_icon_size"
                                android:layout_height="@dimen/subscribe_privilege_icon_size"
                                android:src="@drawable/vip_ic_allfree"
                                android:layout_marginBottom="@dimen/subscribe_privilege_icon_margin_bottom"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Unlimited viewing"
                                android:textColor="#fcffffff"
                                android:textSize="@dimen/subscribe_privilege_card_text_size"
                                android:gravity="center"/>
                    </LinearLayout>

                    <!-- Support 1080p -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="@dimen/subscribe_privilege_card_height"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/subscribe_privilege_card_margin"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/subscribe_privilege_card_bg"
                            android:padding="@dimen/subscribe_privilege_card_padding">

                        <ImageView
                                android:layout_width="@dimen/subscribe_privilege_icon_size"
                                android:layout_height="@dimen/subscribe_privilege_icon_size"
                                android:src="@drawable/vip_ic_1080p"
                                android:layout_marginBottom="@dimen/subscribe_privilege_icon_margin_bottom"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="1080p quality"
                                android:textColor="#fcffffff"
                                android:textSize="@dimen/subscribe_privilege_card_text_size"
                                android:gravity="center"/>
                    </LinearLayout>
                </LinearLayout>

                <!-- Second Row -->
                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal">

                    <!-- Extra Points -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="@dimen/subscribe_privilege_card_height"
                            android:layout_weight="1"
                            android:layout_marginEnd="@dimen/subscribe_privilege_card_margin"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/subscribe_privilege_card_bg"
                            android:padding="@dimen/subscribe_privilege_card_padding">

                        <ImageView
                                android:layout_width="@dimen/subscribe_privilege_icon_size"
                                android:layout_height="@dimen/subscribe_privilege_icon_size"
                                android:src="@drawable/vip_ic_jinbi"
                                android:layout_marginBottom="@dimen/subscribe_privilege_icon_margin_bottom"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="Daily points reward"
                                android:textColor="#fcffffff"
                                android:textSize="@dimen/subscribe_privilege_card_text_size"
                                android:gravity="center"/>
                    </LinearLayout>

                    <!-- No advertising required -->
                    <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="@dimen/subscribe_privilege_card_height"
                            android:layout_weight="1"
                            android:layout_marginStart="@dimen/subscribe_privilege_card_margin"
                            android:orientation="vertical"
                            android:gravity="center"
                            android:background="@drawable/subscribe_privilege_card_bg"
                            android:padding="@dimen/subscribe_privilege_card_padding">

                        <ImageView
                                android:layout_width="@dimen/subscribe_privilege_icon_size"
                                android:layout_height="@dimen/subscribe_privilege_icon_size"
                                android:src="@drawable/vip_ic_ad"
                                android:layout_marginBottom="@dimen/subscribe_privilege_icon_margin_bottom"/>

                        <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="AD-free"
                                android:textColor="#fcffffff"
                                android:textSize="@dimen/subscribe_privilege_card_text_size"
                                android:gravity="center"/>
                    </LinearLayout>
                </LinearLayout>
            </LinearLayout>

            <!-- Tips Section -->
            <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="40dp"
                    android:text="Tips"
                    android:textColor="#fcffffff"
                    android:textSize="@dimen/subscribe_tips_title_text_size"
                    android:fontFamily="sans-serif-medium"/>

            <!-- VIP Privileges List with Background -->
            <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="20dp"
                    android:layout_marginEnd="16dp"
                    android:orientation="vertical"
                    android:background="@drawable/subscribe_tips_background"
                    android:padding="12dp">

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="VIP Privileges"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_title_text_size"
                        android:fontFamily="sans-serif"/>

                <!-- Privilege Items -->
                <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="12dp"
                        android:orientation="vertical">

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Ad-free streaming – Watch without interruptions"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_item_text_size"
                        android:layout_marginBottom="8dp"/>

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Early access – New episodes 1 week before free users"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_item_text_size"
                        android:layout_marginBottom="8dp"/>

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Exclusive content – Members-only short films &amp; behind-the-scenes"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_item_text_size"
                        android:layout_marginBottom="8dp"/>

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• HD/4K streaming – Enhanced video quality"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_item_text_size"
                        android:layout_marginBottom="8dp"/>

                <TextView
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="• Download offline – Save videos without WiFi"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_privileges_item_text_size"/>

                </LinearLayout>

            </LinearLayout>

            <!-- Subscribe Now Button -->
            <TextView
                    android:id="@+id/btn_subscribe"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/subscribe_button_height"
                    android:layout_marginStart="@dimen/subscribe_button_margin_horizontal"
                    android:layout_marginTop="@dimen/subscribe_button_margin_top"
                    android:layout_marginEnd="@dimen/subscribe_button_margin_horizontal"
                    android:text="Subscribe now"
                    android:textColor="#fcffffff"
                    android:textSize="@dimen/subscribe_button_text_size"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:background="@drawable/subscribe_button_bg"
                    android:clickable="true"
                    android:focusable="true"/>

        </LinearLayout>
    </ScrollView>

</FrameLayout>
