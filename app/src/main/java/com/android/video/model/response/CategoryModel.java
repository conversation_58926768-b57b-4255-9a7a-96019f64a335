package com.android.video.model.response;

import com.android.video.constants.HomeApiConstantsUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 分类标签数据模型
 * <p>
 * 用于解析分类标签列表API返回的数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_CATEGORY_LIST}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>categoryId: 分类唯一标识符</li>
 *   <li>categoryName: 分类显示名称</li>
 *   <li>weight: 分类权重，用于排序</li>
 *   <li>language: 支持的语言类型（1=英语, 2=俄语，多种语言用逗号分隔）</li>
 *   <li>isDelete: 删除状态（1=正常, 0=已删除）</li>
 *   <li>createTime: 创建时间</li>
 *   <li>updateTime: 最后更新时间</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * List&lt;CategoryModel&gt; categories = gson.fromJson(jsonArray, 
 *     new TypeToken&lt;List&lt;CategoryModel&gt;&gt;(){}.getType());
 * 
 * // 获取分类信息
 * for (CategoryModel category : categories) {
 *     String id = category.getCategoryId();
 *     String name = category.getCategoryName();
 *     int weight = category.getWeight();
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_CATEGORY_LIST
 */
public class CategoryModel {

    /**
     * 分类ID
     * <p>
     * 分类的唯一标识符，用于API请求和数据关联。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_ID)
    @Expose
    private String categoryId;

    /**
     * 分类名称
     * <p>
     * 分类的显示名称，用于UI展示。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_NAME)
    @Expose
    private String categoryName;

    /**
     * 分类权重
     * <p>
     * 用于分类排序的权重值，数值越大优先级越高。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_WEIGHT)
    @Expose
    private int weight;

    /**
     * 支持的语言类型
     * <p>
     * 语言类型字符串，多种语言用逗号分隔。
     * 1=英语, 2=俄语
     * 例如："1" 表示仅支持英语，"1,2" 表示支持英语和俄语
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_LANGUAGE)
    @Expose
    private String language;

    /**
     * 删除状态
     * <p>
     * 标识分类是否已被删除。
     * 1=正常状态, 0=已删除
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_IS_DELETE)
    @Expose
    private int isDelete;

    /**
     * 创建时间
     * <p>
     * 分类创建的时间戳，格式：yyyy-MM-dd HH:mm:ss
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CREATE_TIME)
    @Expose
    private String createTime;

    /**
     * 更新时间
     * <p>
     * 分类最后更新的时间戳，格式：yyyy-MM-dd HH:mm:ss
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_UPDATE_TIME)
    @Expose
    private String updateTime;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public CategoryModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param categoryId 分类ID
     * @param categoryName 分类名称
     * @param weight 权重
     * @param language 语言类型
     * @param isDelete 删除状态
     * @param createTime 创建时间
     * @param updateTime 更新时间
     */
    public CategoryModel(String categoryId, String categoryName, int weight, 
                        String language, int isDelete, String createTime, String updateTime) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.weight = weight;
        this.language = language;
        this.isDelete = isDelete;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    // ========== Getter和Setter方法 ==========

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public int getWeight() {
        return weight;
    }

    public void setWeight(int weight) {
        this.weight = weight;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    // ========== 工具方法 ==========

    /**
     * 检查分类是否为正常状态
     * <p>
     * 判断分类是否未被删除。
     * </p>
     * 
     * @return 如果分类正常则返回true，否则返回false
     */
    public boolean isActive() {
        return isDelete == 1;
    }

    /**
     * 检查分类是否支持指定语言
     * <p>
     * 判断分类是否支持指定的语言类型。
     * </p>
     * 
     * @param languageType 语言类型（1=英语, 2=俄语）
     * @return 如果支持指定语言则返回true，否则返回false
     */
    public boolean supportsLanguage(int languageType) {
        if (language == null || language.trim().isEmpty()) {
            return false;
        }
        
        String[] supportedLanguages = language.split(",");
        String targetLanguage = String.valueOf(languageType);
        
        for (String lang : supportedLanguages) {
            if (targetLanguage.equals(lang.trim())) {
                return true;
            }
        }
        
        return false;
    }

    /**
     * 获取支持的语言类型列表
     * <p>
     * 解析language字段，返回支持的语言类型数组。
     * </p>
     * 
     * @return 支持的语言类型数组
     */
    public int[] getSupportedLanguages() {
        if (language == null || language.trim().isEmpty()) {
            return new int[0];
        }
        
        String[] languageStrings = language.split(",");
        int[] languages = new int[languageStrings.length];
        
        for (int i = 0; i < languageStrings.length; i++) {
            try {
                languages[i] = Integer.parseInt(languageStrings[i].trim());
            } catch (NumberFormatException e) {
                languages[i] = 0; // 无效语言类型
            }
        }
        
        return languages;
    }

    @Override
    public String toString() {
        return "CategoryModel{" +
                "categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", weight=" + weight +
                ", language='" + language + '\'' +
                ", isDelete=" + isDelete +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CategoryModel that = (CategoryModel) o;

        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }

    @Override
    public int hashCode() {
        return categoryId != null ? categoryId.hashCode() : 0;
    }
}
