<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    tools:context=".ui.fragment.MyListFragment">

    <!-- 顶部阴影背景 - 最底层 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <!-- 主要内容区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <!-- 顶部标题栏 - 居中容器 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="54.5dp"
            android:paddingBottom="16dp">

        <RelativeLayout
            android:layout_width="@dimen/my_list_grid_container_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

        <!-- My List标题 -->
        <TextView
            android:id="@+id/tv_my_list_title"
            android:layout_width="66dp"
            android:layout_height="24dp"
            android:layout_alignParentStart="true"
            android:text="My List"
            android:textColor="#ffffffff"
            android:textSize="20sp"
            android:gravity="center_vertical"
            android:fontFamily="sans-serif" />

        <!-- 下载图标 -->
        <ImageView
            android:id="@+id/iv_download"
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_alignParentEnd="true"
            android:layout_alignTop="@id/tv_my_list_title"
            android:layout_alignBottom="@id/tv_my_list_title"
            android:src="@drawable/list_ic_download"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

        </RelativeLayout>

    </FrameLayout>

    <!-- 标签页容器 - 居中容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingBottom="16dp">

        <LinearLayout
            android:layout_width="@dimen/my_list_grid_container_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:orientation="horizontal"
            android:paddingStart="16dp"
            android:paddingEnd="16dp">

        <!-- Following标签 -->
        <LinearLayout
            android:id="@+id/layout_tab_following"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/tv_tab_following"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Following"
                android:textColor="#ffffffff"
                android:textSize="16sp"
                android:gravity="center"
                android:paddingBottom="8dp"
                android:fontFamily="sans-serif" />

            <!-- 选中指示器 -->
            <View
                android:id="@+id/indicator_following"
                android:layout_width="53dp"
                android:layout_height="3dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/my_list_tab_indicator"
                android:visibility="visible" />

        </LinearLayout>

        <!-- History标签 -->
        <LinearLayout
            android:id="@+id/layout_tab_history"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/tv_tab_history"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="History"
                android:textColor="#80ffffff"
                android:textSize="16sp"
                android:gravity="center"
                android:paddingBottom="8dp"
                android:fontFamily="sans-serif" />

            <!-- 选中指示器 -->
            <View
                android:id="@+id/indicator_history"
                android:layout_width="53dp"
                android:layout_height="3dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/my_list_tab_indicator"
                android:visibility="invisible" />

        </LinearLayout>

        <!-- Interest标签 -->
        <LinearLayout
            android:id="@+id/layout_tab_interest"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackground">

            <TextView
                android:id="@+id/tv_tab_interest"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="Interest"
                android:textColor="#80ffffff"
                android:textSize="16sp"
                android:gravity="center"
                android:paddingBottom="8dp"
                android:fontFamily="sans-serif" />

            <!-- 选中指示器 -->
            <View
                android:id="@+id/indicator_interest"
                android:layout_width="53dp"
                android:layout_height="3dp"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/my_list_tab_indicator"
                android:visibility="invisible" />

        </LinearLayout>

        </LinearLayout>

    </FrameLayout>

    <!-- 分割线 - 居中容器 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <View
            android:layout_width="@dimen/my_list_grid_container_width"
            android:layout_height="0.5dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/my_list_divider" />

    </FrameLayout>

    <!-- 内容区域 - 全宽自适应 -->
    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 下拉刷新容器 -->
        <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
            android:id="@+id/swipe_refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_videos"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:paddingTop="24dp"
                android:paddingBottom="16dp"
                android:clipToPadding="false"
                android:scrollbars="none" />

        </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>



    </FrameLayout>

    </LinearLayout>

</FrameLayout>
