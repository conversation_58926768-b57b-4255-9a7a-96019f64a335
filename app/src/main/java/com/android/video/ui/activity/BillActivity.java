package com.android.video.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.adapter.VipRecordAdapter;
import com.android.video.model.VipRecord;
import com.android.video.adapter.PointsPurchaseAdapter;
import com.android.video.adapter.VideoRecordAdapter;
import com.android.video.model.PointsPurchase;
import com.android.video.model.VideoRecord;
import com.android.video.constants.BillApiConstantsUtils;
import com.android.video.model.response.BillOrderItem;
import com.android.video.model.response.BillOrderListResponse;
import com.android.video.network.BillApiService;
import com.android.video.utils.BillDataConverter;

import java.util.ArrayList;
import java.util.List;

public class BillActivity extends BaseFullScreenActivity {

    private static final String TAG = "BillActivity";
    private static final int PAGE_SIZE = 10;

    private TextView tabVipRecord;
    private TextView tabPointsPurchase;
    private TextView tabVideoRecord;
    private View selectedTabBackground;
    private SwipeRefreshLayout swipeRefreshLoading;
    private LinearLayout emptyStateContainer;
    private LinearLayout vipRecordContainer;
    private LinearLayout pointsPurchaseContainer;
    private LinearLayout videoRecordContainer;

    // SwipeRefreshLayout相关
    private SwipeRefreshLayout swipeRefreshVipRecord;
    private SwipeRefreshLayout swipeRefreshPointsPurchase;
    private SwipeRefreshLayout swipeRefreshVideoRecord;

    // VIP Record相关
    private RecyclerView rvVipRecords;
    private VipRecordAdapter vipRecordAdapter;
    private List<VipRecord> vipRecordList;

    // Points Purchase相关
    private RecyclerView rvPointsPurchase;
    private PointsPurchaseAdapter pointsPurchaseAdapter;
    private List<PointsPurchase> pointsPurchaseList;

    // Video Record相关
    private RecyclerView rvVideoRecord;
    private VideoRecordAdapter videoRecordAdapter;
    private List<VideoRecord> videoRecordList;

    // 分页状态管理
    private int currentTab = 0; // 0: VIP Record, 1: Points purchase, 2: Video Record
    private int[] currentPages = {1, 1, 1}; // 每个标签页的当前页码
    private boolean[] hasMoreData = {true, true, true}; // 每个标签页是否还有更多数据
    private boolean[] isLoading = {false, false, false}; // 每个标签页是否正在加载

    // 网络服务
    private BillApiService billApiService;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_bill);

        initViews();
        setupClickListeners();
        initNetworkService();
        selectTab(0); // 默认选择第一个标签
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (billApiService != null) {
            billApiService.release();
        }
    }

    private void initViews() {
        // 返回按钮
        ImageView btnBack = findViewById(R.id.btn_back);
        btnBack.setOnClickListener(v -> finish());

        // 标签页
        tabVipRecord = findViewById(R.id.tab_vip_record);
        tabPointsPurchase = findViewById(R.id.tab_points_purchase);
        tabVideoRecord = findViewById(R.id.tab_video_record);
        selectedTabBackground = findViewById(R.id.selected_tab_background);

        // 内容容器
        swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);
        emptyStateContainer = findViewById(R.id.empty_state_container);
        vipRecordContainer = findViewById(R.id.vip_record_container);
        pointsPurchaseContainer = findViewById(R.id.points_purchase_container);
        videoRecordContainer = findViewById(R.id.video_record_container);

        // SwipeRefreshLayout
        swipeRefreshVipRecord = findViewById(R.id.swipe_refresh_vip_record);
        swipeRefreshPointsPurchase = findViewById(R.id.swipe_refresh_points_purchase);
        swipeRefreshVideoRecord = findViewById(R.id.swipe_refresh_video_record);

        // VIP Record RecyclerView
        rvVipRecords = findViewById(R.id.rv_vip_records);
        setupVipRecordRecyclerView();

        // Points Purchase RecyclerView
        rvPointsPurchase = findViewById(R.id.rv_points_purchase);
        setupPointsPurchaseRecyclerView();

        // Video Record RecyclerView
        rvVideoRecord = findViewById(R.id.rv_video_record);
        setupVideoRecordRecyclerView();

        // 设置下拉刷新
        setupSwipeRefresh();
    }

    private void setupClickListeners() {
        tabVipRecord.setOnClickListener(v -> selectTab(0));
        tabPointsPurchase.setOnClickListener(v -> selectTab(1));
        tabVideoRecord.setOnClickListener(v -> selectTab(2));
    }

    /**
     * 初始化网络服务
     */
    private void initNetworkService() {
        billApiService = new BillApiService();
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        // 设置刷新颜色
        int[] colors = {
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        };

        // VIP Record下拉刷新
        swipeRefreshVipRecord.setColorSchemeResources(colors);
        swipeRefreshVipRecord.setOnRefreshListener(() -> refreshData(0));

        // Points Purchase下拉刷新
        swipeRefreshPointsPurchase.setColorSchemeResources(colors);
        swipeRefreshPointsPurchase.setOnRefreshListener(() -> refreshData(1));

        // Video Record下拉刷新
        swipeRefreshVideoRecord.setColorSchemeResources(colors);
        swipeRefreshVideoRecord.setOnRefreshListener(() -> refreshData(2));
    }

    private void selectTab(int tabIndex) {
        currentTab = tabIndex;

        // 更新选中状态背景位置
        updateSelectedTabBackground();

        // 更新文字颜色
        updateTabTextColors();

        // 显示对应内容
        showTabContent();
    }

    private void updateSelectedTabBackground() {
        // 获取标签页容器的总宽度和选中背景的宽度
        int containerWidth = getResources().getDimensionPixelSize(R.dimen.bill_tabs_background_width);
        int selectedTabWidth = getResources().getDimensionPixelSize(R.dimen.bill_selected_tab_width);
        int containerMargin = getResources().getDimensionPixelSize(R.dimen.bill_selected_tab_margin_start);

        // 计算每个标签页的实际可用宽度
        // 容器总宽度减去左右边距，然后除以3个标签页
        int availableWidth = containerWidth - (2 * containerMargin);
        int singleTabWidth = availableWidth / 3;

        // 计算选中背景的起始位置：
        // 1. 容器左边距
        // 2. 加上当前标签页的偏移量
        // 3. 加上在当前标签页中的居中偏移量
        int tabStartPosition = containerMargin + (currentTab * singleTabWidth);
        int centerOffset = (singleTabWidth - selectedTabWidth) / 2;
        int marginStart = tabStartPosition + centerOffset;

        // 更新选中背景的位置
        FrameLayout.LayoutParams params = (FrameLayout.LayoutParams) selectedTabBackground.getLayoutParams();
        params.setMarginStart(marginStart);
        selectedTabBackground.setLayoutParams(params);
    }

    private void updateTabTextColors() {
        // 重置所有标签文字颜色为白色
        tabVipRecord.setTextColor(getResources().getColor(android.R.color.white, null));
        tabPointsPurchase.setTextColor(getResources().getColor(android.R.color.white, null));
        tabVideoRecord.setTextColor(getResources().getColor(android.R.color.white, null));

        // 设置选中标签文字颜色为黑色
        switch (currentTab) {
            case 0:
                tabVipRecord.setTextColor(getResources().getColor(android.R.color.black, null));
                break;
            case 1:
                tabPointsPurchase.setTextColor(getResources().getColor(android.R.color.black, null));
                break;
            case 2:
                tabVideoRecord.setTextColor(getResources().getColor(android.R.color.black, null));
                break;
        }
    }

    private void showTabContent() {
        // 隐藏所有内容容器
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
        emptyStateContainer.setVisibility(View.GONE);
        vipRecordContainer.setVisibility(View.GONE);
        pointsPurchaseContainer.setVisibility(View.GONE);
        videoRecordContainer.setVisibility(View.GONE);

        // 检查当前标签页是否已有数据
        boolean hasData = hasTabData(currentTab);

        if (hasData) {
            // 如果已有数据，直接显示对应容器
            switch (currentTab) {
                case 0: // VIP Record
                    vipRecordContainer.setVisibility(View.VISIBLE);
                    break;
                case 1: // Points purchase
                    pointsPurchaseContainer.setVisibility(View.VISIBLE);
                    break;
                case 2: // Video Record
                    videoRecordContainer.setVisibility(View.VISIBLE);
                    break;
            }
        } else {
            // 如果没有数据，显示loading状态并加载数据
            showLoadingState();
            loadData(currentTab, false);
        }
    }

    /**
     * 检查标签页是否已有数据
     */
    private boolean hasTabData(int tabIndex) {
        switch (tabIndex) {
            case 0:
                return vipRecordList != null && !vipRecordList.isEmpty();
            case 1:
                return pointsPurchaseList != null && !pointsPurchaseList.isEmpty();
            case 2:
                return videoRecordList != null && !videoRecordList.isEmpty();
            default:
                return false;
        }
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        swipeRefreshLoading.setVisibility(View.VISIBLE);
        swipeRefreshLoading.setRefreshing(true);
        emptyStateContainer.setVisibility(View.GONE);
        vipRecordContainer.setVisibility(View.GONE);
        pointsPurchaseContainer.setVisibility(View.GONE);
        videoRecordContainer.setVisibility(View.GONE);
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoadingState() {
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
    }

    /**
     * 刷新数据
     * @param tabIndex 标签页索引
     */
    private void refreshData(int tabIndex) {
        Log.d(TAG, "Refreshing data for tab: " + tabIndex);

        // 重置分页状态
        currentPages[tabIndex] = 1;
        hasMoreData[tabIndex] = true;

        // 清空现有数据
        clearTabData(tabIndex);

        // 加载新数据
        loadData(tabIndex, true);
    }

    /**
     * 加载数据
     * @param tabIndex 标签页索引
     * @param isRefresh 是否为刷新操作
     */
    private void loadData(int tabIndex, boolean isRefresh) {
        if (isLoading[tabIndex]) {
            Log.d(TAG, "Already loading data for tab: " + tabIndex);
            return;
        }

        if (!hasMoreData[tabIndex] && !isRefresh) {
            Log.d(TAG, "No more data for tab: " + tabIndex);
            return;
        }

        isLoading[tabIndex] = true;

        // 获取对应的orderType
        int orderType = getOrderTypeByTabIndex(tabIndex);
        int page = currentPages[tabIndex];

        Log.d(TAG, "Loading data - Tab: " + tabIndex + ", OrderType: " + orderType + ", Page: " + page);

        billApiService.getOrderList(page, PAGE_SIZE, orderType, new BillApiService.ApiCallback<BillOrderListResponse>() {
            @Override
            public void onSuccess(BillOrderListResponse response) {
                isLoading[tabIndex] = false;

                // 隐藏加载状态
                hideLoadingState();

                // 停止刷新动画
                stopRefreshAnimation(tabIndex);

                if (response.getData() != null && response.getData().getRecords() != null) {
                    List<BillOrderItem> records = response.getData().getRecords();
                    Log.d(TAG, "Received " + records.size() + " records for tab: " + tabIndex);

                    // 更新分页状态
                    hasMoreData[tabIndex] = response.getData().getCurrent() < response.getData().getPages();

                    if (isRefresh) {
                        // 刷新时替换数据
                        updateTabData(tabIndex, records, true);
                    } else {
                        // 加载更多时追加数据
                        updateTabData(tabIndex, records, false);
                    }

                    // 更新页码
                    currentPages[tabIndex]++;

                    // 显示内容或空状态
                    if (records.isEmpty() && isRefresh) {
                        showEmptyState();
                    } else {
                        hideEmptyState();
                    }
                } else {
                    Log.w(TAG, "No data received for tab: " + tabIndex);
                    if (isRefresh) {
                        showEmptyState();
                    }
                }
            }

            @Override
            public void onError(String error) {
                isLoading[tabIndex] = false;

                // 隐藏加载状态
                hideLoadingState();

                // 停止刷新动画
                stopRefreshAnimation(tabIndex);

                Log.e(TAG, "Error loading data for tab " + tabIndex + ": " + error);
                Toast.makeText(BillActivity.this, "加载失败: " + error, Toast.LENGTH_SHORT).show();

                if (isRefresh) {
                    showEmptyState();
                }
            }
        });
    }

    /**
     * 根据标签页索引获取对应的订单类型
     */
    private int getOrderTypeByTabIndex(int tabIndex) {
        switch (tabIndex) {
            case 0: // VIP Record
                return BillApiConstantsUtils.ORDER_TYPE_VIP_PURCHASE;
            case 1: // Points purchase
                return BillApiConstantsUtils.ORDER_TYPE_POINTS_PURCHASE;
            case 2: // Video Record
                return BillApiConstantsUtils.ORDER_TYPE_VIDEO_UNLOCK;
            default:
                return BillApiConstantsUtils.ORDER_TYPE_VIP_PURCHASE;
        }
    }

    /**
     * 停止刷新动画
     */
    private void stopRefreshAnimation(int tabIndex) {
        switch (tabIndex) {
            case 0:
                if (swipeRefreshVipRecord.isRefreshing()) {
                    swipeRefreshVipRecord.setRefreshing(false);
                }
                break;
            case 1:
                if (swipeRefreshPointsPurchase.isRefreshing()) {
                    swipeRefreshPointsPurchase.setRefreshing(false);
                }
                break;
            case 2:
                if (swipeRefreshVideoRecord.isRefreshing()) {
                    swipeRefreshVideoRecord.setRefreshing(false);
                }
                break;
        }
    }

    /**
     * 清空标签页数据
     */
    private void clearTabData(int tabIndex) {
        switch (tabIndex) {
            case 0:
                if (vipRecordList != null) {
                    vipRecordList.clear();
                    if (vipRecordAdapter != null) {
                        vipRecordAdapter.notifyDataSetChanged();
                    }
                }
                break;
            case 1:
                if (pointsPurchaseList != null) {
                    pointsPurchaseList.clear();
                    if (pointsPurchaseAdapter != null) {
                        pointsPurchaseAdapter.notifyDataSetChanged();
                    }
                }
                break;
            case 2:
                if (videoRecordList != null) {
                    videoRecordList.clear();
                    if (videoRecordAdapter != null) {
                        videoRecordAdapter.notifyDataSetChanged();
                    }
                }
                break;
        }
    }

    /**
     * 更新标签页数据
     */
    private void updateTabData(int tabIndex, List<BillOrderItem> records, boolean isRefresh) {
        switch (tabIndex) {
            case 0: // VIP Record
                List<VipRecord> vipRecords = BillDataConverter.convertToVipRecordList(records);
                if (isRefresh) {
                    vipRecordList.clear();
                }
                vipRecordList.addAll(vipRecords);
                if (vipRecordAdapter != null) {
                    vipRecordAdapter.notifyDataSetChanged();
                }
                break;
            case 1: // Points Purchase
                List<PointsPurchase> pointsPurchases = BillDataConverter.convertToPointsPurchaseList(records);
                if (isRefresh) {
                    pointsPurchaseList.clear();
                }
                pointsPurchaseList.addAll(pointsPurchases);
                if (pointsPurchaseAdapter != null) {
                    pointsPurchaseAdapter.notifyDataSetChanged();
                }
                break;
            case 2: // Video Record
                List<VideoRecord> videoRecords = BillDataConverter.convertToVideoRecordList(records);
                if (isRefresh) {
                    videoRecordList.clear();
                }
                videoRecordList.addAll(videoRecords);
                if (videoRecordAdapter != null) {
                    videoRecordAdapter.notifyDataSetChanged();
                }
                break;
        }
    }

    /**
     * 显示空状态
     */
    private void showEmptyState() {
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
        emptyStateContainer.setVisibility(View.VISIBLE);
        vipRecordContainer.setVisibility(View.GONE);
        pointsPurchaseContainer.setVisibility(View.GONE);
        videoRecordContainer.setVisibility(View.GONE);
    }

    /**
     * 隐藏空状态
     */
    private void hideEmptyState() {
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
        emptyStateContainer.setVisibility(View.GONE);

        // 显示当前标签页的内容
        switch (currentTab) {
            case 0:
                vipRecordContainer.setVisibility(View.VISIBLE);
                break;
            case 1:
                pointsPurchaseContainer.setVisibility(View.VISIBLE);
                break;
            case 2:
                videoRecordContainer.setVisibility(View.VISIBLE);
                break;
        }
    }

    // 设置VIP记录RecyclerView
    private void setupVipRecordRecyclerView() {
        vipRecordList = new ArrayList<>();
        vipRecordAdapter = new VipRecordAdapter(vipRecordList);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvVipRecords.setLayoutManager(layoutManager);
        rvVipRecords.setAdapter(vipRecordAdapter);

        // 添加滚动监听实现上拉加载更多
        rvVipRecords.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                    if ((visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                        // 接近底部时加载更多数据
                        loadData(0, false);
                    }
                }
            }
        });
    }

    // 设置Points Purchase RecyclerView
    private void setupPointsPurchaseRecyclerView() {
        pointsPurchaseList = new ArrayList<>();
        pointsPurchaseAdapter = new PointsPurchaseAdapter(pointsPurchaseList);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvPointsPurchase.setLayoutManager(layoutManager);
        rvPointsPurchase.setAdapter(pointsPurchaseAdapter);

        // 添加滚动监听实现上拉加载更多
        rvPointsPurchase.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                    if ((visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                        // 接近底部时加载更多数据
                        loadData(1, false);
                    }
                }
            }
        });
    }

    // 设置Video Record RecyclerView
    private void setupVideoRecordRecyclerView() {
        videoRecordList = new ArrayList<>();
        videoRecordAdapter = new VideoRecordAdapter(videoRecordList);
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvVideoRecord.setLayoutManager(layoutManager);
        rvVideoRecord.setAdapter(videoRecordAdapter);

        // 添加滚动监听实现上拉加载更多
        rvVideoRecord.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                    if ((visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                        // 接近底部时加载更多数据
                        loadData(2, false);
                    }
                }
            }
        });
    }
}
