package com.android.video.network;

import android.content.Context;
import android.util.Log;

import com.android.video.cache.DataCacheManager;
import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;

/**
 * API缓存拦截器
 * 自动缓存GET请求的响应数据，避免重复网络请求
 * <AUTHOR> Team
 */
public class ApiCacheInterceptor implements Interceptor {
    
    private static final String TAG = "ApiCacheInterceptor";
    
    private DataCacheManager cacheManager;
    private Gson gson;
    
    // 不同API的缓存时间配置（分钟）
    private static final long BANNER_CACHE_TIME = TimeUnit.HOURS.toMillis(2);      // Banner 2小时
    private static final long FEATURED_CACHE_TIME = TimeUnit.HOURS.toMillis(1);    // Featured 1小时
    private static final long CATEGORY_CACHE_TIME = TimeUnit.HOURS.toMillis(6);    // 分类 6小时
    private static final long VIDEO_LIST_CACHE_TIME = TimeUnit.MINUTES.toMillis(30); // 视频列表 30分钟
    private static final long USER_INFO_CACHE_TIME = TimeUnit.HOURS.toMillis(24);  // 用户信息 24小时
    private static final long DEFAULT_CACHE_TIME = TimeUnit.HOURS.toMillis(1);     // 默认 1小时
    
    public ApiCacheInterceptor(Context context) {
        this.cacheManager = DataCacheManager.getInstance(context);
        this.gson = new Gson();
    }
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request originalRequest = chain.request();
        
        // 只缓存GET请求
        if (!"GET".equals(originalRequest.method())) {
            return chain.proceed(originalRequest);
        }
        
        String url = originalRequest.url().toString();
        String cacheKey = generateCacheKey(url);
        
        // 检查缓存
        String cachedResponse = cacheManager.getCachedData(cacheKey, String.class);
        if (cachedResponse != null) {
            Log.d(TAG, "Returning cached response for: " + url);
            return createResponseFromCache(originalRequest, cachedResponse);
        }
        
        // 执行网络请求
        Response response = chain.proceed(originalRequest);
        
        // 缓存成功的响应
        if (response.isSuccessful() && response.body() != null) {
            String responseBody = response.body().string();

            // 验证响应是否为有效JSON
            if (isValidJsonResponse(responseBody)) {
                long cacheTime = getCacheTimeForUrl(url);
                // 只有缓存时间大于0才进行缓存
                if (cacheTime > 0) {
                    cacheManager.cacheData(cacheKey, responseBody, cacheTime);
                    Log.d(TAG, "Cached response for: " + url + " (cache time: " + cacheTime + "ms)");
                } else {
                    Log.d(TAG, "Skipping cache for: " + url + " (cache time: 0)");
                }
            }

            // 重新创建响应，因为body已经被读取
            return response.newBuilder()
                    .body(ResponseBody.create(response.body().contentType(), responseBody))
                    .build();
        }
        
        return response;
    }
    
    /**
     * 生成缓存键
     */
    private String generateCacheKey(String url) {
        // 移除查询参数中的时间戳等动态参数
        String cleanUrl = url.replaceAll("[?&]timestamp=\\d+", "")
                             .replaceAll("[?&]_t=\\d+", "")
                             .replaceAll("[?&]random=\\w+", "");
        
        return "api_cache_" + cleanUrl.hashCode();
    }
    
    /**
     * 根据URL确定缓存时间
     */
    private long getCacheTimeForUrl(String url) {
        // 分类页面的轮播图不缓存（location=2表示分类详情页）
        if ((url.contains("/banner") || url.contains("/carousel")) && url.contains("location=2")) {
            return 0; // 不缓存分类页面轮播图
        } else if (url.contains("/banner") || url.contains("/carousel")) {
            return BANNER_CACHE_TIME;
        } else if (url.contains("/featured") || url.contains("/recommend")) {
            return FEATURED_CACHE_TIME;
        } else if (url.contains("/category") || url.contains("/tag")) {
            return CATEGORY_CACHE_TIME;
        } else if (url.contains("/video") || url.contains("/movie") || url.contains("/series")) {
            return VIDEO_LIST_CACHE_TIME;
        } else if (url.contains("/user") || url.contains("/profile")) {
            return USER_INFO_CACHE_TIME;
        } else {
            return DEFAULT_CACHE_TIME;
        }
    }
    
    /**
     * 验证响应是否为有效JSON
     */
    private boolean isValidJsonResponse(String responseBody) {
        try {
            JsonParser.parseString(responseBody);
            return true;
        } catch (Exception e) {
            Log.w(TAG, "Invalid JSON response, not caching");
            return false;
        }
    }
    
    /**
     * 从缓存创建响应
     */
    private Response createResponseFromCache(Request request, String cachedBody) {
        return new Response.Builder()
                .request(request)
                .protocol(okhttp3.Protocol.HTTP_1_1)
                .code(200)
                .message("OK")
                .header("Content-Type", "application/json")
                .header("Cache-Control", "max-age=3600")
                .header("X-Cache", "HIT")
                .body(ResponseBody.create(
                        okhttp3.MediaType.parse("application/json"),
                        cachedBody
                ))
                .build();
    }
    
    /**
     * 清除特定URL的缓存
     */
    public void clearCacheForUrl(String url) {
        String cacheKey = generateCacheKey(url);
        cacheManager.clearCachedData(cacheKey);
        Log.d(TAG, "Cache cleared for URL: " + url);
    }
    
    /**
     * 清除所有API缓存
     */
    public void clearAllApiCache() {
        cacheManager.clearAllCache();
        Log.d(TAG, "All API cache cleared");
    }
    
    /**
     * 预加载API数据
     */
    public void preloadApiData(String url, okhttp3.OkHttpClient httpClient) {
        String cacheKey = generateCacheKey(url);
        
        // 检查是否已有缓存
        if (cacheManager.isCacheValid(cacheKey)) {
            Log.d(TAG, "Cache already exists for: " + url);
            return;
        }
        
        // 异步预加载
        new Thread(() -> {
            try {
                Request request = new Request.Builder()
                        .url(url)
                        .build();
                
                Response response = httpClient.newCall(request).execute();
                if (response.isSuccessful() && response.body() != null) {
                    String responseBody = response.body().string();
                    if (isValidJsonResponse(responseBody)) {
                        long cacheTime = getCacheTimeForUrl(url);
                        cacheManager.cacheData(cacheKey, responseBody, cacheTime);
                        Log.d(TAG, "Preloaded and cached: " + url);
                    }
                }
                response.close();
            } catch (Exception e) {
                Log.e(TAG, "Failed to preload: " + url, e);
            }
        }).start();
    }
    
    /**
     * 获取缓存统计信息
     */
    public DataCacheManager.CacheStats getCacheStats() {
        return cacheManager.getCacheStats();
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanupExpiredCache() {
        cacheManager.cleanupExpiredCache();
    }
    
    /**
     * 检查特定URL是否有有效缓存
     */
    public boolean hasCacheForUrl(String url) {
        String cacheKey = generateCacheKey(url);
        return cacheManager.isCacheValid(cacheKey);
    }
    
    /**
     * 强制刷新特定URL的缓存
     */
    public void forceRefreshCache(String url, okhttp3.OkHttpClient httpClient) {
        // 先清除现有缓存
        clearCacheForUrl(url);
        
        // 重新加载
        preloadApiData(url, httpClient);
    }
    
    /**
     * 批量预加载API数据
     */
    public void batchPreloadApiData(String[] urls, okhttp3.OkHttpClient httpClient) {
        for (String url : urls) {
            preloadApiData(url, httpClient);
            
            // 添加小延迟避免同时发起太多请求
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }
    
    /**
     * 设置缓存策略
     */
    public static class CacheStrategy {
        public static final String CACHE_CONTROL_NO_CACHE = "no-cache";
        public static final String CACHE_CONTROL_FORCE_CACHE = "only-if-cached";
        public static final String CACHE_CONTROL_FORCE_NETWORK = "no-store";
        
        /**
         * 添加缓存控制头到请求
         */
        public static Request.Builder addCacheControl(Request.Builder builder, String cacheControl) {
            return builder.header("Cache-Control", cacheControl);
        }
        
        /**
         * 创建强制使用缓存的请求
         */
        public static Request.Builder forceCacheRequest(Request.Builder builder) {
            return addCacheControl(builder, CACHE_CONTROL_FORCE_CACHE);
        }
        
        /**
         * 创建强制网络请求
         */
        public static Request.Builder forceNetworkRequest(Request.Builder builder) {
            return addCacheControl(builder, CACHE_CONTROL_FORCE_NETWORK);
        }
        
        /**
         * 创建不使用缓存的请求
         */
        public static Request.Builder noCacheRequest(Request.Builder builder) {
            return addCacheControl(builder, CACHE_CONTROL_NO_CACHE);
        }
    }
}
