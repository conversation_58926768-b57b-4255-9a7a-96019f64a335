package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.TextView;
import com.android.video.manager.LocalizationManager;
import java.util.HashMap;
import java.util.Map;

/**
 * 本地化工具类
 * 提供便捷的多语言文本获取和界面更新方法
 * 支持英语、俄语、Kaza语三种语言
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class LocalizationUtils {
    
    private static final String TAG = "LocalizationUtils";
    
    // 缓存LocalizationManager实例，避免重复获取
    private static final Map<Context, LocalizationManager> managerCache = new HashMap<>();
    
    /**
     * 获取本地化文本
     * @param context 上下文
     * @param key 翻译键
     * @return 本地化文本，如果不存在则返回键本身
     */
    public static String getString(Context context, String key) {
        if (context == null || key == null) {
            Log.w(TAG, "Context or key is null");
            return key != null ? key : "";
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            String result = manager.getString(key);
            
            if (result.equals(key)) {
                Log.d(TAG, "Translation not found for key: " + key);
            }
            
            return result;
        } catch (Exception e) {
            Log.e(TAG, "Error getting localized string for key: " + key, e);
            return key;
        }
    }
    
    /**
     * 获取格式化的本地化文本
     * 支持俄语和Kaza语的参数替换
     * @param context 上下文
     * @param key 翻译键
     * @param args 格式化参数
     * @return 格式化后的本地化文本
     */
    public static String getString(Context context, String key, Object... args) {
        if (context == null || key == null) {
            Log.w(TAG, "Context or key is null");
            return key != null ? key : "";
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            String result = manager.getString(key, args);
            
            if (result.equals(key)) {
                Log.d(TAG, "Translation not found for key: " + key);
            }
            
            return result;
        } catch (Exception e) {
            Log.e(TAG, "Error getting formatted localized string for key: " + key, e);
            return key;
        }
    }
    
    /**
     * 检查是否有指定键的翻译
     * @param context 上下文
     * @param key 翻译键
     * @return 是否存在翻译
     */
    public static boolean hasTranslation(Context context, String key) {
        if (context == null || key == null) {
            return false;
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            return manager.hasTranslation(key);
        } catch (Exception e) {
            Log.e(TAG, "Error checking translation for key: " + key, e);
            return false;
        }
    }
    
    /**
     * 获取当前语言类型
     * @param context 上下文
     * @return 当前语言类型 (1=英语, 2=俄语, 3=Kaza语)
     */
    public static int getCurrentLanguageType(Context context) {
        if (context == null) {
            Log.w(TAG, "Context is null, returning default language type");
            return 1; // 默认英语
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            return manager.getCurrentLanguageType();
        } catch (Exception e) {
            Log.e(TAG, "Error getting current language type", e);
            return 1; // 默认英语
        }
    }
    
    /**
     * 获取当前语言的描述
     * @param context 上下文
     * @return 语言描述字符串
     */
    public static String getCurrentLanguageDescription(Context context) {
        if (context == null) {
            return "English";
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            return manager.getCurrentLanguageDescription();
        } catch (Exception e) {
            Log.e(TAG, "Error getting current language description", e);
            return "English";
        }
    }
    
    /**
     * 切换语言
     * @param context 上下文
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     */
    public static void setLanguage(Context context, int languageType) {
        if (context == null) {
            Log.w(TAG, "Context is null, cannot set language");
            return;
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            manager.setLanguage(languageType);
            Log.d(TAG, "Language changed to type: " + languageType);
        } catch (Exception e) {
            Log.e(TAG, "Error setting language type: " + languageType, e);
        }
    }
    
    /**
     * 自动更新View中的文本
     * 遍历View树，自动更新所有TextView和Button的文本
     * @param rootView 根视图
     */
    public static void updateViewTexts(View rootView) {
        if (rootView == null) {
            Log.w(TAG, "Root view is null, cannot update texts");
            return;
        }
        
        Context context = rootView.getContext();
        if (context == null) {
            Log.w(TAG, "Context is null, cannot update texts");
            return;
        }
        
        try {
            updateViewTextsRecursive(rootView, context);
            Log.d(TAG, "View texts updated successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error updating view texts", e);
        }
    }
    
    /**
     * 递归更新View文本
     * @param view 当前视图
     * @param context 上下文
     */
    private static void updateViewTextsRecursive(View view, Context context) {
        if (view == null) {
            return;
        }
        
        // 更新TextView和Button的文本
        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            CharSequence currentText = textView.getText();
            
            if (currentText != null && currentText.length() > 0) {
                String textKey = currentText.toString();
                
                // 检查是否有对应的翻译
                if (hasTranslation(context, textKey)) {
                    String localizedText = getString(context, textKey);
                    textView.setText(localizedText);
                    Log.d(TAG, "Updated text: " + textKey + " -> " + localizedText);
                }
            }
        }
        
        // 如果是ViewGroup，递归处理子视图
        if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            int childCount = viewGroup.getChildCount();
            
            for (int i = 0; i < childCount; i++) {
                View childView = viewGroup.getChildAt(i);
                updateViewTextsRecursive(childView, context);
            }
        }
    }
    
    /**
     * 更新指定TextView的文本
     * @param textView TextView实例
     * @param key 翻译键
     */
    public static void updateTextView(TextView textView, String key) {
        if (textView == null || key == null) {
            Log.w(TAG, "TextView or key is null");
            return;
        }
        
        Context context = textView.getContext();
        if (context == null) {
            Log.w(TAG, "Context is null");
            return;
        }
        
        try {
            String localizedText = getString(context, key);
            textView.setText(localizedText);
            Log.d(TAG, "Updated TextView with key: " + key + " -> " + localizedText);
        } catch (Exception e) {
            Log.e(TAG, "Error updating TextView with key: " + key, e);
        }
    }
    
    /**
     * 更新指定Button的文本
     * @param button Button实例
     * @param key 翻译键
     */
    public static void updateButton(Button button, String key) {
        if (button == null || key == null) {
            Log.w(TAG, "Button or key is null");
            return;
        }
        
        Context context = button.getContext();
        if (context == null) {
            Log.w(TAG, "Context is null");
            return;
        }
        
        try {
            String localizedText = getString(context, key);
            button.setText(localizedText);
            Log.d(TAG, "Updated Button with key: " + key + " -> " + localizedText);
        } catch (Exception e) {
            Log.e(TAG, "Error updating Button with key: " + key, e);
        }
    }
    
    /**
     * 处理俄语和Kaza语的文本方向
     * @param textView TextView实例
     */
    public static void handleTextDirection(TextView textView) {
        if (textView == null) {
            return;
        }
        
        Context context = textView.getContext();
        if (context == null) {
            return;
        }
        
        try {
            int languageType = getCurrentLanguageType(context);
            
            // 俄语和Kaza语都是从左到右的文字，与英语相同
            // 如果将来需要支持从右到左的语言（如阿拉伯语），可以在这里添加处理
            switch (languageType) {
                case 1: // 英语
                case 2: // 俄语
                case 3: // Kaza语
                default:
                    // 所有支持的语言都是从左到右
                    if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.JELLY_BEAN_MR1) {
                        textView.setTextDirection(View.TEXT_DIRECTION_LTR);
                    }
                    break;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error handling text direction", e);
        }
    }
    
    /**
     * 获取LocalizationManager实例
     * 使用缓存避免重复创建
     * @param context 上下文
     * @return LocalizationManager实例
     */
    private static LocalizationManager getLocalizationManager(Context context) {
        Context appContext = context.getApplicationContext();
        
        LocalizationManager manager = managerCache.get(appContext);
        if (manager == null) {
            manager = LocalizationManager.getInstance(appContext);
            managerCache.put(appContext, manager);
        }
        
        return manager;
    }
    
    /**
     * 清除缓存
     * 通常在应用退出时调用
     */
    public static void clearCache() {
        managerCache.clear();
        Log.d(TAG, "LocalizationUtils cache cleared");
    }
    
    /**
     * 获取语言类型的描述文本
     * @param languageType 语言类型
     * @return 语言描述
     */
    public static String getLanguageTypeDescription(int languageType) {
        switch (languageType) {
            case 1:
                return "English";
            case 2:
                return "Русский";
            case 3:
                return "Қазақ";
            default:
                return "English";
        }
    }
    
    /**
     * 初始化本地化系统
     * 在Application或主Activity中调用
     * @param context 上下文
     */
    public static void initialize(Context context) {
        if (context == null) {
            Log.w(TAG, "Context is null, cannot initialize");
            return;
        }
        
        try {
            LocalizationManager manager = getLocalizationManager(context);
            manager.loadTranslations();
            Log.d(TAG, "LocalizationUtils initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing LocalizationUtils", e);
        }
    }
}
