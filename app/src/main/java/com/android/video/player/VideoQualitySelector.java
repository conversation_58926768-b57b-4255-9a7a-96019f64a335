package com.android.video.player;

import java.util.List;

/**
 * 视频清晰度选择接口
 * 处理视频清晰度切换功能
 * <AUTHOR>
 */
public interface VideoQualitySelector {
    
    /**
     * 视频清晰度枚举
     */
    enum VideoQuality {
        QUALITY_AUTO("Auto", 0),
        QUALITY_480P("480P", 480),
        QUALITY_720P("720P", 720),
        QUALITY_1080P("1080P", 1080),
        QUALITY_ULTRA("超清", 1440);
        
        private final String displayName;
        private final int height;
        
        VideoQuality(String displayName, int height) {
            this.displayName = displayName;
            this.height = height;
        }
        
        public String getDisplayName() {
            return displayName;
        }
        
        public int getHeight() {
            return height;
        }
    }
    
    /**
     * 获取可用的清晰度列表
     * @return 清晰度列表
     */
    List<VideoQuality> getAvailableQualities();
    
    /**
     * 获取当前清晰度
     * @return 当前清晰度
     */
    VideoQuality getCurrentQuality();
    
    /**
     * 设置清晰度
     * @param quality 目标清晰度
     */
    void setQuality(VideoQuality quality);
    
    /**
     * 清晰度变化监听器
     */
    interface OnQualityChangeListener {
        /**
         * 清晰度变化回调
         * @param oldQuality 旧清晰度
         * @param newQuality 新清晰度
         */
        void onQualityChanged(VideoQuality oldQuality, VideoQuality newQuality);
        
        /**
         * 清晰度切换开始
         * @param targetQuality 目标清晰度
         */
        void onQualityChangeStarted(VideoQuality targetQuality);
        
        /**
         * 清晰度切换完成
         * @param quality 当前清晰度
         */
        void onQualityChangeCompleted(VideoQuality quality);
        
        /**
         * 清晰度切换失败
         * @param quality 目标清晰度
         * @param error 错误信息
         */
        void onQualityChangeFailed(VideoQuality quality, String error);
    }
    
    /**
     * 设置清晰度变化监听器
     * @param listener 监听器
     */
    void setOnQualityChangeListener(OnQualityChangeListener listener);
}
