package com.android.video.constants;

/**
 * VIP模块API常量工具类
 * <p>
 * 提供VIP相关的API常量定义和调用方法。
 * 继承自BaseApiConstantsUtils，遵循统一的API规范。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class VipApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== API路径常量 ==========

    /**
     * 获取VIP配置列表API路径
     */
    public static final String API_GET_VIP_CONFIG_LIST = "/app/vipCenter/vipConfigList";

    /**
     * VIP兑换码兑换API路径
     */
    public static final String API_REDEEM_CODE_EXCHANGE = "/app/vipCenter/redeemCodeExchange";

    /**
     * VIP购买API路径
     * <p>
     * 用于购买VIP套餐，支持Google Pay支付。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * </p>
     */
    public static final String API_VIP_BUY = "/app/vipCenter/vipBuy";

    // ========== 请求参数常量 ==========

    /**
     * VIP配置ID参数名
     */
    public static final String PARAM_VIP_ID = "vipId";

    /**
     * VIP名称参数名
     */
    public static final String PARAM_VIP_NAME = "vipName";

    /**
     * 时长天数参数名
     */
    public static final String PARAM_DURATION_DAYS = "durationDays";

    /**
     * 价格参数名
     */
    public static final String PARAM_PRICE = "price";

    /**
     * 实际价格参数名
     */
    public static final String PARAM_ACTUAL_PRICE = "actualPrice";

    /**
     * 赠送积分参数名
     */
    public static final String PARAM_GIFT_POINTS = "giftPoints";

    /**
     * 会员权益参数名
     */
    public static final String PARAM_MEMBER_BENEFITS = "memberBenefits";

    /**
     * 额外积分参数名
     */
    public static final String PARAM_EXTRA_POINTS = "extraPoints";

    /**
     * 状态参数名
     */
    public static final String PARAM_STATUS = "status";

    /**
     * 折扣参数名
     */
    public static final String PARAM_DISCOUNT = "discount";

    /**
     * 兑换码参数名
     */
    public static final String PARAM_CODE = "code";

    /**
     * 支付金额参数名
     */
    public static final String PARAM_PAY_AMOUNT = "payAmount";

    /**
     * 系统类型参数名（1=Android, 2=iOS）
     */
    public static final String PARAM_SYSTEM_TYPE = "systemType";

    /**
     * 支付类型参数名（google, apple等）
     */
    public static final String PARAM_PAY_TYPE = "payType";

    // ========== 响应字段常量 ==========

    /**
     * VIP配置ID字段名
     */
    public static final String FIELD_VIP_ID = "vipId";

    /**
     * VIP名称字段名
     */
    public static final String FIELD_VIP_NAME = "vipName";

    /**
     * 时长文本字段名
     */
    public static final String FIELD_DURATION_TEXT = "durationText";

    /**
     * 时长天数字段名
     */
    public static final String FIELD_DURATION_DAYS = "durationDays";

    /**
     * 价格字段名
     */
    public static final String FIELD_PRICE = "price";

    /**
     * 实际价格字段名
     */
    public static final String FIELD_ACTUAL_PRICE = "actualPrice";

    /**
     * 赠送积分字段名
     */
    public static final String FIELD_GIFT_POINTS = "giftPoints";

    /**
     * 会员权益字段名
     */
    public static final String FIELD_MEMBER_BENEFITS = "memberBenefits";

    /**
     * 额外积分字段名
     */
    public static final String FIELD_EXTRA_POINTS = "extraPoints";

    /**
     * 状态字段名
     */
    public static final String FIELD_STATUS = "status";

    /**
     * 创建时间字段名
     */
    public static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 更新时间字段名
     */
    public static final String FIELD_UPDATE_TIME = "updateTime";

    /**
     * 折扣字段名
     */
    public static final String FIELD_DISCOUNT = "discount";

    /**
     * 订单号字段名
     */
    public static final String FIELD_ORDER_NO = "orderNo";

    /**
     * Google支付参数字段名
     */
    public static final String FIELD_GOOGLE_PAY_PARAMS = "googlePayParams";

    /**
     * Apple支付参数字段名
     */
    public static final String FIELD_APPLE_PAY_PARAMS = "applePayParams";

    /**
     * 支付状态字段名
     */
    public static final String FIELD_PAY_STATUS = "payStatus";

    // ========== 业务常量 ==========

    /**
     * VIP配置启用状态
     */
    public static final int STATUS_ENABLED = 1;

    /**
     * VIP配置禁用状态
     */
    public static final int STATUS_DISABLED = 0;

    /**
     * 最小折扣值
     */
    public static final int MIN_DISCOUNT = 1;

    /**
     * 最大折扣值
     */
    public static final int MAX_DISCOUNT = 100;

    /**
     * 默认VIP名称最大长度
     */
    public static final int MAX_VIP_NAME_LENGTH = 50;

    /**
     * 默认时长文本最大长度
     */
    public static final int MAX_DURATION_TEXT_LENGTH = 20;

    // ========== 会员权益常量 ==========

    /**
     * 免费观看权益
     */
    public static final String BENEFIT_FREE_VIDEO = "free_video";

    /**
     * 高清视频权益
     */
    public static final String BENEFIT_HD_VIDEO = "hd_video";

    /**
     * 无广告权益
     */
    public static final String BENEFIT_NO_ADS = "no_ads";

    /**
     * 每日积分权益
     */
    public static final String BENEFIT_DAILY_POINTS = "daily_points";

    /**
     * 权益分隔符
     */
    public static final String BENEFIT_SEPARATOR = ",";

    // ========== 系统类型常量 ==========

    /**
     * Android系统类型
     */
    public static final int SYSTEM_TYPE_ANDROID = 1;

    /**
     * iOS系统类型
     */
    public static final int SYSTEM_TYPE_IOS = 2;

    // ========== 支付类型常量 ==========

    /**
     * Google支付类型
     */
    public static final String PAY_TYPE_GOOGLE = "google";

    /**
     * Apple支付类型
     */
    public static final String PAY_TYPE_APPLE = "apple";

    // ========== 工具方法 ==========

    /**
     * 验证VIP配置状态是否有效
     *
     * @param status 状态值
     * @return 如果状态有效则返回true，否则返回false
     */
    public static boolean isValidStatus(int status) {
        return status == STATUS_ENABLED || status == STATUS_DISABLED;
    }

    /**
     * 验证折扣值是否有效
     *
     * @param discount 折扣值
     * @return 如果折扣值有效则返回true，否则返回false
     */
    public static boolean isValidDiscount(Integer discount) {
        return discount == null || (discount >= MIN_DISCOUNT && discount <= MAX_DISCOUNT);
    }

    /**
     * 验证VIP名称是否有效
     *
     * @param vipName VIP名称
     * @return 如果VIP名称有效则返回true，否则返回false
     */
    public static boolean isValidVipName(String vipName) {
        return vipName != null && !vipName.trim().isEmpty() && vipName.length() <= MAX_VIP_NAME_LENGTH;
    }

    /**
     * 验证时长文本是否有效
     *
     * @param durationText 时长文本
     * @return 如果时长文本有效则返回true，否则返回false
     */
    public static boolean isValidDurationText(String durationText) {
        return durationText != null && !durationText.trim().isEmpty() && durationText.length() <= MAX_DURATION_TEXT_LENGTH;
    }

    /**
     * 解析会员权益字符串为数组
     *
     * @param memberBenefits 会员权益字符串
     * @return 会员权益数组
     */
    public static String[] parseMemberBenefits(String memberBenefits) {
        if (memberBenefits == null || memberBenefits.trim().isEmpty()) {
            return new String[0];
        }
        return memberBenefits.split(BENEFIT_SEPARATOR);
    }

    /**
     * 将会员权益数组转换为字符串
     *
     * @param benefits 会员权益数组
     * @return 会员权益字符串
     */
    public static String joinMemberBenefits(String[] benefits) {
        if (benefits == null || benefits.length == 0) {
            return "";
        }
        return String.join(BENEFIT_SEPARATOR, benefits);
    }

    /**
     * 检查是否包含指定权益
     *
     * @param memberBenefits 会员权益字符串
     * @param benefit        要检查的权益
     * @return 如果包含指定权益则返回true，否则返回false
     */
    public static boolean hasBenefit(String memberBenefits, String benefit) {
        if (memberBenefits == null || benefit == null) {
            return false;
        }
        String[] benefits = parseMemberBenefits(memberBenefits);
        for (String b : benefits) {
            if (benefit.equals(b.trim())) {
                return true;
            }
        }
        return false;
    }
}
