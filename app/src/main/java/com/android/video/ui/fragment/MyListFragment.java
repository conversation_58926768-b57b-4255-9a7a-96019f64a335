package com.android.video.ui.fragment;

import android.content.Intent;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import java.util.Objects;
import com.android.video.decoration.GridSpacingItemDecoration;
import com.android.video.model.MyListVideo;
import com.android.video.model.VideoModel;
import com.android.video.model.response.MyLoveListResponseModel;
import com.android.video.model.response.MyHistoryListResponseModel;
import com.android.video.model.response.MySubscribeListResponseModel;
import com.android.video.network.MyLoveListApiService;
import com.android.video.network.MyHistoryListApiService;
import com.android.video.network.MySubscribeListApiService;
import com.android.video.ui.activity.DownloadActivity;
import com.android.video.ui.activity.VideoDetailActivity;
import com.android.video.ui.activity.VideoPlayerActivity;
import com.android.video.ui.adapter.MyListVideoAdapter;
import com.android.video.manager.FragmentCacheManager;
import com.android.video.cache.ImprovedDataCacheManager;
import com.android.video.cache.SmartCacheManager;
import com.android.video.base.BaseMultiLanguageFragment;
import java.util.ArrayList;
import java.util.List;

/**
 * 我的列表Fragment - 显示用户收藏和观看历史
 * <AUTHOR>
 */
public class MyListFragment extends BaseMultiLanguageFragment implements MyListVideoAdapter.OnVideoClickListener {

    private static final String TAG = "MyListFragment";

    // Fragment缓存管理器
    private FragmentCacheManager fragmentCacheManager;
    private String fragmentKey = "MyListFragment";

    // UI组件
    private ImageView ivDownload;
    private LinearLayout layoutTabFollowing, layoutTabHistory, layoutTabInterest;
    private TextView tvTabFollowing, tvTabHistory, tvTabInterest;
    private View indicatorFollowing, indicatorHistory, indicatorInterest;
    private RecyclerView rvVideos;
    private SwipeRefreshLayout swipeRefreshLayout;

    // 适配器和数据
    private MyListVideoAdapter videoAdapter;
    private List<MyListVideo> followingVideos;
    private List<MyListVideo> historyVideos;
    private List<MyListVideo> interestVideos;
    private String currentTab = "following"; // 当前选中的标签

    // API服务
    private MyLoveListApiService myLoveApiService;
    private MyHistoryListApiService myHistoryApiService;
    private MySubscribeListApiService mySubscribeApiService;

    // 分页相关
    private static final int PAGE_SIZE = 10;
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    public MyListFragment() {
        // Required empty public constructor
    }

    public static MyListFragment newInstance() {
        return new MyListFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "MyListFragment onCreate called");

        // 初始化Fragment缓存管理器
        fragmentCacheManager = FragmentCacheManager.getInstance();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_my_list, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 检查登录状态
        if (!com.android.video.utils.LoginRequiredUtils.isLoggedIn(getContext())) {
            // 用户未登录，显示登录提示并跳转
            com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext(),
                "Please login to view your list");
            return;
        }

        initViews(view);
        initData();
        setupRecyclerView();
        setupSwipeRefresh();
        setupScrollListener();
        setupClickListeners();
    }

    private void initViews(View view) {
        ivDownload = view.findViewById(R.id.iv_download);

        // 标签页
        layoutTabFollowing = view.findViewById(R.id.layout_tab_following);
        layoutTabHistory = view.findViewById(R.id.layout_tab_history);
        layoutTabInterest = view.findViewById(R.id.layout_tab_interest);

        tvTabFollowing = view.findViewById(R.id.tv_tab_following);
        tvTabHistory = view.findViewById(R.id.tv_tab_history);
        tvTabInterest = view.findViewById(R.id.tv_tab_interest);

        indicatorFollowing = view.findViewById(R.id.indicator_following);
        indicatorHistory = view.findViewById(R.id.indicator_history);
        indicatorInterest = view.findViewById(R.id.indicator_interest);

        rvVideos = view.findViewById(R.id.rv_videos);
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);
    }

    private void initData() {
        // 初始化独立的数据集合
        if (followingVideos == null) {
            followingVideos = new ArrayList<>();
        }
        if (historyVideos == null) {
            historyVideos = new ArrayList<>();
        }
        if (interestVideos == null) {
            interestVideos = new ArrayList<>();
        }
        myLoveApiService = MyLoveListApiService.getInstance();
        myHistoryApiService = MyHistoryListApiService.getInstance();
        mySubscribeApiService = MySubscribeListApiService.getInstance();

        // 使用FragmentCacheManager检查是否需要加载数据
        String currentFragmentKey = fragmentKey + "_" + currentTab;
        if (fragmentCacheManager.shouldReloadData(currentFragmentKey)) {
            Log.d(TAG, "Fragment缓存管理器建议加载 " + currentTab + " 数据");
            loadFollowingData(true);
            fragmentCacheManager.updateDataLoaded(currentFragmentKey, true);
        } else {
            Log.d(TAG, "Fragment缓存管理器建议使用缓存数据");
        }
    }

    private void setupRecyclerView() {
        // 设置GridLayoutManager，2列布局
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 2);
        rvVideos.setLayoutManager(gridLayoutManager);

        // 初始化适配器
        videoAdapter = new MyListVideoAdapter(getCurrentTabVideos(), this);
        rvVideos.setAdapter(videoAdapter);
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            // 设置刷新颜色
            swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );

            // 设置下拉刷新监听
            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "下拉刷新触发，当前标签页: " + currentTab);
                refreshCurrentTab();
            });
        }
    }

    /**
     * 设置滚动监听实现上拉加载更多
     */
    private void setupScrollListener() {
        if (rvVideos != null) {
            rvVideos.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);

                    if (dy > 0) { // 向下滚动
                        GridLayoutManager layoutManager = (GridLayoutManager) recyclerView.getLayoutManager();
                        if (layoutManager != null) {
                            int visibleItemCount = layoutManager.getChildCount();
                            int totalItemCount = layoutManager.getItemCount();
                            int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                            // 接近底部时加载更多数据
                            if ((visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                                loadMoreData();
                            }
                        }
                    }
                }
            });
        }
    }

    private void setupClickListeners() {
        // 下载按钮点击
        ivDownload.setOnClickListener(v -> {
            Intent intent = new Intent(getContext(), DownloadActivity.class);
            startActivity(intent);
        });

        // Following标签点击
        layoutTabFollowing.setOnClickListener(v -> selectTab("following"));

        // History标签点击
        layoutTabHistory.setOnClickListener(v -> selectTab("history"));

        // Interest标签点击
        layoutTabInterest.setOnClickListener(v -> selectTab("interest"));
    }

    private void selectTab(String tab) {
        currentTab = tab;
        updateTabUI();

        // 根据标签页加载相应数据
        if ("following".equals(tab)) {
            // 重置分页状态并加载Following数据
            currentPage = 1;
            hasMoreData = true;
            loadFollowingData(true);
        } else if ("history".equals(tab)) {
            // 重置分页状态并加载History数据
            currentPage = 1;
            hasMoreData = true;
            loadHistoryData(true);
        } else if ("interest".equals(tab)) {
            // 重置分页状态并加载Interest数据
            currentPage = 1;
            hasMoreData = true;
            loadInterestData(true);
        } else {
            // 其他标签页使用现有逻辑
            updateVideoList();
        }
    }

    private void updateTabUI() {
        // 检查Fragment是否已附加到Context
        if (!isAdded() || getContext() == null) {
            Log.w(TAG, "Fragment not attached to context, skipping UI update");
            return;
        }

        // 重置所有标签状态
        tvTabFollowing.setTextColor(getResources().getColor(android.R.color.white, null));
        tvTabHistory.setTextColor(getResources().getColor(android.R.color.white, null));
        tvTabInterest.setTextColor(getResources().getColor(android.R.color.white, null));

        indicatorFollowing.setVisibility(View.INVISIBLE);
        indicatorHistory.setVisibility(View.INVISIBLE);
        indicatorInterest.setVisibility(View.INVISIBLE);

        // 设置选中标签状态
        switch (currentTab) {
            case "following":
                tvTabFollowing.setTextColor(getResources().getColor(android.R.color.white, null));
                indicatorFollowing.setVisibility(View.VISIBLE);
                break;
            case "history":
                tvTabHistory.setTextColor(getResources().getColor(android.R.color.white, null));
                indicatorHistory.setVisibility(View.VISIBLE);
                break;
            case "interest":
                tvTabInterest.setTextColor(getResources().getColor(android.R.color.white, null));
                indicatorInterest.setVisibility(View.VISIBLE);
                break;
        }
    }

    private void updateVideoList() {
        // 检查Fragment是否已附加到Context
        if (!isAdded() || getContext() == null) {
            Log.w(TAG, "Fragment not attached to context, skipping video list update");
            return;
        }

        // 设置适配器的当前标签
        videoAdapter.setCurrentTab(currentTab);

        // 根据标签设置布局管理器
        if ("history".equals(currentTab) || "interest".equals(currentTab)) {
            // History和Interest使用线性布局
            LinearLayoutManager linearLayoutManager = new LinearLayoutManager(getContext());
            rvVideos.setLayoutManager(linearLayoutManager);
            // 清除之前的装饰器
            while (rvVideos.getItemDecorationCount() > 0) {
                rvVideos.removeItemDecorationAt(0);
            }
        } else {
            // Following使用网格布局
            GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 2);
            rvVideos.setLayoutManager(gridLayoutManager);

            // 清除之前的装饰器
            while (rvVideos.getItemDecorationCount() > 0) {
                rvVideos.removeItemDecorationAt(0);
            }

            // 使用GridSpacingItemDecoration，包含边缘间距（全宽布局）
            int spacingInPixels = getResources().getDimensionPixelSize(R.dimen.my_list_grid_spacing);
            rvVideos.addItemDecoration(new GridSpacingItemDecoration(2, spacingInPixels, true));
        }

        List<MyListVideo> currentVideos = getCurrentTabVideos();
        videoAdapter.updateVideos(currentVideos);
    }

    /**
     * 获取当前标签页的视频数据
     */
    private List<MyListVideo> getCurrentTabVideos() {
        switch (currentTab) {
            case "following":
                return followingVideos;
            case "history":
                return historyVideos;
            case "interest":
                return interestVideos;
            default:
                return new ArrayList<>();
        }
    }

    @Override
    public void onVideoClick(MyListVideo video) {
        if (video == null) {
            Log.w(TAG, "onVideoClick: video is null");
            return;
        }

        // Following模块点击视频海报跳转到视频详情页
        if ("following".equals(video.getCategory())) {
            navigateToVideoDetail(video);
        } else {
            // 其他模块暂时显示提示信息
            Toast.makeText(getContext(), "Video clicked: " + video.getTitle(), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onPlayClick(MyListVideo video) {
        if (video == null) {
            Log.w(TAG, "onPlayClick: video is null");
            return;
        }

        // History模块点击play按钮跳转到视频播放页面
        if ("history".equals(video.getCategory())) {
            navigateToVideoPlayer(video);
        } else {
            // 其他模块暂时显示提示信息
            Toast.makeText(getContext(), "Play clicked: " + video.getTitle(), Toast.LENGTH_SHORT).show();
        }
    }

    @Override
    public void onLikeClick(MyListVideo video) {
        video.setLiked(!video.isLiked());
        videoAdapter.notifyDataSetChanged();
        Toast.makeText(getContext(),
            video.isLiked() ? "Added to favorites" : "Removed from favorites",
            Toast.LENGTH_SHORT).show();
    }

    /**
     * 跳转到视频详情页面
     * @param myListVideo MyList视频数据
     */
    private void navigateToVideoDetail(MyListVideo myListVideo) {
        if (myListVideo == null || getContext() == null) {
            Log.w(TAG, "Cannot navigate to video detail: myListVideo or context is null");
            return;
        }

        try {
            // 将MyListVideo转换为VideoModel
            VideoModel videoModel = convertToVideoModel(myListVideo);

            // 启动视频详情页面
            Intent intent = new Intent(getContext(), VideoDetailActivity.class);
            intent.putExtra("video_model", videoModel);
            startActivity(intent);

            Log.d(TAG, "Successfully navigated to video detail: " + myListVideo.getTitle());
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to video detail", e);
            Toast.makeText(getContext(), "跳转失败，请重试", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 跳转到视频播放页面
     * @param myListVideo MyList视频数据
     */
    private void navigateToVideoPlayer(MyListVideo myListVideo) {
        if (myListVideo == null || getContext() == null) {
            Log.w(TAG, "Cannot navigate to video player: myListVideo or context is null");
            return;
        }

        try {
            // 将MyListVideo转换为VideoModel
            VideoModel videoModel = convertToVideoModel(myListVideo);

            // 启动视频播放页面
            VideoPlayerActivity.start(getContext(), videoModel);

            Log.d(TAG, "Successfully navigated to video player: " + myListVideo.getTitle());
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to video player", e);
            Toast.makeText(getContext(), "跳转失败，请重试", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 将MyListVideo转换为VideoModel
     * @param myListVideo MyList视频数据
     * @return VideoModel对象
     */
    private VideoModel convertToVideoModel(MyListVideo myListVideo) {
        VideoModel videoModel = new VideoModel();

        // 基本信息
        videoModel.setId(myListVideo.getId());
        videoModel.setTitle(myListVideo.getTitle());
        videoModel.setPosterUrl(myListVideo.getPosterUrl());
        videoModel.setLiked(myListVideo.isLiked());

        // 设置filmLanguageInfoId用于API调用
        videoModel.setFilmLanguageInfoId(myListVideo.getFilmLanguageInfoId());

        // 剧集信息
        videoModel.setCurrentEpisode(myListVideo.getCurrentEpisode());
        videoModel.setTotalEpisodes(myListVideo.getTotalEpisodes());

        // 根据类别设置相应的信息
        if ("following".equals(myListVideo.getCategory())) {
            videoModel.setCategory("Drama");
            videoModel.setDescription(myListVideo.getDescription().isEmpty() ?
                "A captivating story that will keep you engaged." : myListVideo.getDescription());
        } else if ("history".equals(myListVideo.getCategory())) {
            videoModel.setCategory("Action");
            videoModel.setDescription(myListVideo.getDescription().isEmpty() ?
                "Continue watching from where you left off." : myListVideo.getDescription());
        } else {
            videoModel.setCategory("General");
            videoModel.setDescription(myListVideo.getDescription().isEmpty() ?
                "An interesting video content." : myListVideo.getDescription());
        }

        // 设置默认值
        videoModel.setRating(4.5f);
        videoModel.setDuration("120 min");
        videoModel.setViewCount(1000000);
        videoModel.setSubscribed(myListVideo.isSubscribed());

        return videoModel;
    }

    /**
     * 加载Following标签页数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadFollowingData(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;

        // 显示loading状态
        if (isRefresh) {
            showLoading();
        }

        Log.d(TAG, "开始加载Following数据，页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        myLoveApiService.getMyLoveList(currentPage, PAGE_SIZE,
            new MyLoveListApiService.MyLoveListCallback() {
                @Override
                public void onSuccess(MyLoveListResponseModel response) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画

                    Log.d(TAG, "Following数据加载成功: " + response.toString());

                    if (response.isSuccess()) {
                        List<MyListVideo> newVideos = new ArrayList<>();
                        if (response.getData() != null) {
                            newVideos = response.getData().toMyListVideoList();
                        }

                        if (isRefresh) {
                            // 刷新时清空现有Following数据
                            followingVideos.clear();
                        }

                        // 添加新数据到Following专用集合（包括空数据）
                        followingVideos.addAll(newVideos);

                        // 更新分页状态
                        hasMoreData = response.getData() != null && response.getData().hasMorePages();

                        // 如果当前显示的是Following标签页，更新UI
                        if ("following".equals(currentTab) && isAdded() && getContext() != null) {
                            updateVideoList();
                            // 保存数据到缓存并更新Fragment缓存状态（包括空数据）
                            saveDataToCache();
                            String currentFragmentKey = fragmentKey + "_" + currentTab;
                            fragmentCacheManager.updateDataLoaded(currentFragmentKey, true);
                            fragmentCacheManager.updateCacheState(currentFragmentKey, true);
                        }

                        Log.d(TAG, "Following数据更新完成，当前总数: " + followingVideos.size() +
                                   ", 新增: " + newVideos.size() + ", 是否还有更多: " + hasMoreData);
                    } else {
                        showError("获取数据失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画
                    Log.e(TAG, "Following数据加载失败: " + errorMessage);
                    showError("加载失败: " + errorMessage);
                }
            });
    }



    /**
     * 显示loading状态
     */
    private void showLoading() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }
    }

    /**
     * 隐藏loading状态
     */
    private void hideLoading() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    /**
     * 加载History标签页数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadHistoryData(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;

        // 显示loading状态
        if (isRefresh) {
            showLoading();
        }

        Log.d(TAG, "开始加载History数据，页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        myHistoryApiService.getMyPlayHistoryList(currentPage, PAGE_SIZE,
            new MyHistoryListApiService.MyHistoryListCallback() {
                @Override
                public void onSuccess(MyHistoryListResponseModel response) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画

                    Log.d(TAG, "History数据加载成功: " + response.toString());

                    if (response.isSuccess()) {
                        List<MyListVideo> newVideos = new ArrayList<>();
                        if (response.getData() != null) {
                            newVideos = response.getData().toMyListVideoList();
                        }

                        if (isRefresh) {
                            // 刷新时清空现有History数据
                            historyVideos.clear();
                        }

                        // 添加新数据到History专用集合（包括空数据）
                        historyVideos.addAll(newVideos);

                        // 更新分页状态
                        hasMoreData = response.getData() != null && response.getData().hasMorePages();

                        // 如果当前显示的是History标签页，更新UI
                        if ("history".equals(currentTab) && isAdded() && getContext() != null) {
                            updateVideoList();
                            // 保存数据到缓存并更新Fragment缓存状态（包括空数据）
                            saveDataToCache();
                            String currentFragmentKey = fragmentKey + "_" + currentTab;
                            fragmentCacheManager.updateDataLoaded(currentFragmentKey, true);
                            fragmentCacheManager.updateCacheState(currentFragmentKey, true);
                        }

                        Log.d(TAG, "History数据更新完成，当前总数: " + newVideos.size() +
                                   ", 是否还有更多: " + hasMoreData);
                    } else {
                        showError("获取数据失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画
                    Log.e(TAG, "History数据加载失败: " + errorMessage);
                    showError("加载失败: " + errorMessage);
                }
            });
    }

    /**
     * 加载Interest标签页数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadInterestData(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;

        // 显示loading状态
        if (isRefresh) {
            showLoading();
        }

        Log.d(TAG, "开始加载Interest数据，页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        mySubscribeApiService.getMySubscribeList(currentPage, PAGE_SIZE,
            new MySubscribeListApiService.MySubscribeListCallback() {
                @Override
                public void onSuccess(MySubscribeListResponseModel response) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画

                    Log.d(TAG, "Interest数据加载成功: " + response.toString());

                    if (response.isSuccess()) {
                        List<MyListVideo> newVideos = new ArrayList<>();
                        if (response.getData() != null) {
                            newVideos = response.getData().toMyListVideoList();
                        }

                        if (isRefresh) {
                            // 刷新时清空现有Interest数据
                            interestVideos.clear();
                        }

                        // 添加新数据到Interest专用集合（包括空数据）
                        interestVideos.addAll(newVideos);

                        // 更新分页状态
                        hasMoreData = response.getData() != null && response.getData().hasMorePages();

                        // 如果当前显示的是Interest标签页，更新UI
                        if ("interest".equals(currentTab) && isAdded() && getContext() != null) {
                            updateVideoList();
                            // 保存数据到缓存并更新Fragment缓存状态（包括空数据）
                            saveDataToCache();
                            String currentFragmentKey = fragmentKey + "_" + currentTab;
                            fragmentCacheManager.updateDataLoaded(currentFragmentKey, true);
                            fragmentCacheManager.updateCacheState(currentFragmentKey, true);
                        }

                        Log.d(TAG, "Interest数据更新完成，当前总数: " + newVideos.size() +
                                   ", 是否还有更多: " + hasMoreData);
                    } else {
                        showError("获取数据失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    isLoading = false;
                    hideLoading(); // 隐藏loading状态
                    stopRefreshAnimation(); // 停止刷新动画
                    Log.e(TAG, "Interest数据加载失败: " + errorMessage);
                    showError("加载失败: " + errorMessage);
                }
            });
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 切换到观看历史标签页
     */
    public void switchToHistoryTab() {
        // 检查Fragment是否已附加到Context
        if (!isAdded() || getContext() == null) {
            Log.w(TAG, "Fragment not attached to context, delaying history tab switch");
            // 延迟执行，等待Fragment附加
            if (getView() != null) {
                getView().post(() -> {
                    if (isAdded() && getContext() != null) {
                        selectTab("history");
                    }
                });
            }
            return;
        }

        // 切换到History标签
        selectTab("history");
    }

    /**
     * 刷新当前标签页数据
     */
    private void refreshCurrentTab() {
        Log.d(TAG, "刷新当前标签页: " + currentTab);

        // 重置Fragment缓存状态，强制重新加载
        String currentFragmentKey = fragmentKey + "_" + currentTab;
        fragmentCacheManager.resetFragmentState(currentFragmentKey);

        // 清除当前标签页的缓存数据
        clearCurrentTabCache();

        // 重置分页状态
        currentPage = 1;
        hasMoreData = true;

        // 根据当前标签页刷新数据
        if ("following".equals(currentTab)) {
            loadFollowingData(true);
        } else if ("history".equals(currentTab)) {
            loadHistoryData(true);
        } else if ("interest".equals(currentTab)) {
            loadInterestData(true);
        }
    }

    /**
     * 清除当前标签页的缓存数据（使用改进的缓存管理器）
     */
    private void clearCurrentTabCache() {
        if (getContext() == null) {
            return;
        }

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());

            // 清除当前标签页的累积数据缓存
            String cacheKey = generateAccumulatedCacheKey(currentTab, currentPage);
            cacheManager.clearCachedData(cacheKey);

            // 同时清除Fragment缓存管理器中的状态
            String currentFragmentKey = fragmentKey + "_" + currentTab;
            fragmentCacheManager.clearFragmentState(currentFragmentKey);

            Log.d(TAG, "清除 " + currentTab + " 标签页累积缓存数据完成");
        } catch (Exception e) {
            Log.e(TAG, "清除 " + currentTab + " 标签页累积缓存数据失败", e);
        }
    }

    /**
     * 清除所有MyList缓存数据（使用改进的缓存管理器）
     */
    private void clearCacheData() {
        if (getContext() == null) {
            return;
        }

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());

            // 清理所有标签页的累积数据缓存
            String[] tabs = {"following", "history", "interest"};
            for (String tab : tabs) {
                String cacheKey = generateAccumulatedCacheKey(tab, 1);
                cacheManager.clearCachedData(cacheKey);

                String fragmentKey = this.fragmentKey + "_" + tab;
                fragmentCacheManager.clearFragmentState(fragmentKey);
            }

            Log.d(TAG, "清除MyList所有累积缓存数据完成");
        } catch (Exception e) {
            Log.e(TAG, "清除MyList缓存数据失败", e);
        }
    }

    /**
     * 加载更多数据
     */
    private void loadMoreData() {
        if (isLoading || !hasMoreData) {
            Log.d(TAG, "跳过加载更多 - isLoading: " + isLoading + ", hasMoreData: " + hasMoreData);
            return;
        }

        Log.d(TAG, "加载更多数据，当前标签页: " + currentTab + ", 下一页: " + (currentPage + 1));

        // 增加页码
        currentPage++;

        // 根据当前标签页加载更多数据
        if ("following".equals(currentTab)) {
            loadFollowingData(false);
        } else if ("history".equals(currentTab)) {
            loadHistoryData(false);
        } else if ("interest".equals(currentTab)) {
            loadInterestData(false);
        }
    }

    /**
     * 停止刷新动画
     */
    private void stopRefreshAnimation() {
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "MyListFragment onResume called");

        // 检查登录状态
        if (!com.android.video.utils.LoginRequiredUtils.isLoggedIn(getContext())) {
            Log.d(TAG, "用户未登录，跳过数据加载");
            return;
        }

        // 使用FragmentCacheManager优化的数据加载策略
        String currentFragmentKey = fragmentKey + "_" + currentTab;
        if (fragmentCacheManager.shouldReloadData(currentFragmentKey)) {
            Log.d(TAG, "Fragment缓存管理器建议重新加载 " + currentTab + " 数据");
            refreshCurrentTabWithCache();
        } else {
            Log.d(TAG, "Fragment缓存管理器建议使用 " + currentTab + " 缓存数据");
            // 确保UI显示缓存的数据
            if (!restoreDataFromCache()) {
                // 缓存恢复失败，重新加载
                Log.d(TAG, "缓存恢复失败，重新加载 " + currentTab + " 数据");
                refreshCurrentTabWithCache();
            } else if (videoAdapter != null) {
                videoAdapter.updateVideoList(getCurrentTabVideos());
            }
        }
    }

    /**
     * 带缓存支持的刷新当前标签页
     */
    private void refreshCurrentTabWithCache() {
        // 先尝试从缓存恢复数据
        if (restoreDataFromCache()) {
            Log.d(TAG, "成功从缓存恢复 " + currentTab + " 数据，同时在后台调用接口进行数据比对");
            String currentFragmentKey = fragmentKey + "_" + currentTab;
            fragmentCacheManager.updateCacheState(currentFragmentKey, true);
            fragmentCacheManager.updateDataLoaded(currentFragmentKey, true);

            // 在后台调用接口进行数据比对
            loadMyListDataInBackground();
            return;
        }

        // 缓存中没有数据，进行网络请求
        Log.d(TAG, "缓存中无 " + currentTab + " 数据，开始网络请求");
        refreshCurrentTab();
    }

    /**
     * 在后台加载MyList数据并与缓存比对
     */
    private void loadMyListDataInBackground() {
        Log.d(TAG, "开始后台MyList数据加载和比对");

        // 在后台线程执行，避免影响UI
        new Thread(() -> {
            try {
                // 获取当前缓存的数据作为比对基准
                List<MyListVideo> cachedData = getCachedMyListData();

                // 后台调用接口获取最新数据
                loadMyListDataForComparison(cachedData);

            } catch (Exception e) {
                Log.e(TAG, "后台MyList数据比对过程中发生错误: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 生成累积数据缓存键
     *
     * @param tabType 标签页类型
     * @param currentPage 当前页码
     * @return 累积数据缓存键
     */
    private String generateAccumulatedCacheKey(String tabType, int currentPage) {
        return String.format("mylist_%s_accumulated_data", tabType.toLowerCase());
    }

    /**
     * MyList缓存数据结构
     */
    private static class MyListCacheData {
        public List<MyListVideo> videos;
        public int currentPage;
        public boolean hasMoreData;
        public long timestamp;

        public MyListCacheData() {
            this.videos = new ArrayList<>();
            this.currentPage = 1;
            this.hasMoreData = true;
            this.timestamp = System.currentTimeMillis();
        }
    }

    /**
     * 从缓存恢复数据（使用改进的缓存管理器）
     */
    private boolean restoreDataFromCache() {
        if (getContext() == null) {
            return false;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());

            // 使用累积数据缓存键
            String cacheKey = generateAccumulatedCacheKey(currentTab, currentPage);
            Log.d(TAG, "开始从智能缓存恢复 " + currentTab + " 累积数据，缓存键: " + cacheKey);

            MyListCacheData cachedData = cacheManager.getCachedDataSmart(cacheKey,
                new com.google.gson.reflect.TypeToken<MyListCacheData>(){});

            if (cachedData != null && cachedData.videos != null && !cachedData.videos.isEmpty()) {
                // 恢复当前标签页的数据和分页状态
                switch (currentTab) {
                    case "following":
                        followingVideos = new ArrayList<>(cachedData.videos);
                        break;
                    case "history":
                        historyVideos = new ArrayList<>(cachedData.videos);
                        break;
                    case "interest":
                        interestVideos = new ArrayList<>(cachedData.videos);
                        break;
                }

                currentPage = cachedData.currentPage;
                hasMoreData = cachedData.hasMoreData;

                if (videoAdapter != null) {
                    videoAdapter.updateVideoList(getCurrentTabVideos());
                }
                Log.d(TAG, "成功恢复 " + currentTab + " 缓存数据: " + cachedData.videos.size() + " 项，页数: " + currentPage + "，还有更多: " + hasMoreData);
                return true;
            } else {
                Log.d(TAG, "改进缓存中没有 " + currentTab + " 数据");
            }

        } catch (Exception e) {
            Log.e(TAG, "从改进缓存恢复 " + currentTab + " 累积数据失败", e);
        }

        return false;
    }

    /**
     * 保存数据到缓存（使用改进的缓存管理器）
     * 支持缓存空数据，确保后端返回空数据时能清除旧缓存
     */
    private void saveDataToCache() {
        List<MyListVideo> currentVideos = getCurrentTabVideos();
        if (getContext() == null) {
            Log.d(TAG, "无法保存 " + currentTab + " 数据到缓存：上下文无效");
            return;
        }

        // 允许缓存空数据，确保后端返回空数据时能清除旧缓存
        if (currentVideos == null) {
            currentVideos = new ArrayList<>();
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());

            // 使用累积数据缓存键
            String cacheKey = generateAccumulatedCacheKey(currentTab, currentPage);
            Log.d(TAG, "开始保存 " + currentTab + " 数据到智能缓存，缓存键: " + cacheKey + "，数据量: " + currentVideos.size() + "，当前页: " + currentPage);

            // 创建缓存数据结构，包含当前标签页的数据和分页信息
            MyListCacheData cacheData = new MyListCacheData();
            cacheData.videos = new ArrayList<>(currentVideos);
            cacheData.currentPage = currentPage;
            cacheData.hasMoreData = hasMoreData;
            cacheData.timestamp = System.currentTimeMillis();

            boolean saved = cacheManager.cacheDataSmart(cacheKey, cacheData);

            if (saved) {
                // 更新Fragment缓存管理器状态
                String currentFragmentKey = fragmentKey + "_" + currentTab;
                fragmentCacheManager.updateCacheState(currentFragmentKey, true);
                Log.d(TAG, "成功保存 " + currentTab + " 数据到改进缓存: " + cacheData.videos.size() + " 项，页数: " + currentPage);
            } else {
                Log.e(TAG, "保存 " + currentTab + " 数据到改进缓存失败");
            }

        } catch (Exception e) {
            Log.e(TAG, "保存 " + currentTab + " 数据到改进缓存时发生异常", e);
        }
    }

    /**
     * 获取缓存的MyList数据
     */
    private List<MyListVideo> getCachedMyListData() {
        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            String cacheKey = generateAccumulatedCacheKey(currentTab, currentPage);

            MyListCacheData cachedData = cacheManager.getCachedDataSmart(cacheKey,
                new com.google.gson.reflect.TypeToken<MyListCacheData>(){});

            return cachedData != null ? cachedData.videos : new ArrayList<>();
        } catch (Exception e) {
            Log.e(TAG, "获取缓存MyList数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 后台加载MyList数据进行比对
     */
    private void loadMyListDataForComparison(List<MyListVideo> cachedData) {
        Log.d(TAG, "开始后台MyList数据比对，缓存数据量: " + (cachedData != null ? cachedData.size() : 0));

        // 根据当前标签页调用相应的接口
        switch (currentTab) {
            case "following":
                myLoveApiService.getMyLoveList(1, 20, new MyLoveListApiService.MyLoveListCallback() {
                    @Override
                    public void onSuccess(MyLoveListResponseModel response) {
                        if (response != null && response.hasData()) {
                            List<MyListVideo> newVideos = response.getData().toMyListVideoList();
                            compareAndUpdateMyListData(cachedData, newVideos);
                        }
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e(TAG, "后台Following数据获取失败: " + errorMessage);
                    }
                });
                break;

            case "history":
                myHistoryApiService.getMyPlayHistoryList(1, 20, new MyHistoryListApiService.MyHistoryListCallback() {
                    @Override
                    public void onSuccess(MyHistoryListResponseModel response) {
                        if (response != null && response.hasData()) {
                            List<MyListVideo> newVideos = response.getData().toMyListVideoList();
                            compareAndUpdateMyListData(cachedData, newVideos);
                        }
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e(TAG, "后台History数据获取失败: " + errorMessage);
                    }
                });
                break;

            case "interest":
                mySubscribeApiService.getMySubscribeList(1, 20, new MySubscribeListApiService.MySubscribeListCallback() {
                    @Override
                    public void onSuccess(MySubscribeListResponseModel response) {
                        if (response != null && response.hasData()) {
                            List<MyListVideo> newVideos = response.getData().toMyListVideoList();
                            compareAndUpdateMyListData(cachedData, newVideos);
                        }
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e(TAG, "后台Subscribe数据获取失败: " + errorMessage);
                    }
                });
                break;

            default:
                Log.w(TAG, "未知的标签页类型: " + currentTab);
                break;
        }
    }

    /**
     * 比对并更新MyList数据
     */
    private void compareAndUpdateMyListData(List<MyListVideo> cachedData, List<MyListVideo> newData) {
        if (!isMyListDataEqual(cachedData, newData)) {
            Log.d(TAG, currentTab + "数据有更新，刷新UI");
            updateMyListDataInUI(newData);
        } else {
            Log.d(TAG, currentTab + "数据无变化");
        }
    }

    /**
     * 比对两个MyListVideo列表是否相等
     */
    private boolean isMyListDataEqual(List<MyListVideo> cachedData, List<MyListVideo> newData) {
        if (cachedData == null && newData == null) return true;
        if (cachedData == null || newData == null) return false;
        if (cachedData.size() != newData.size()) return false;

        for (int i = 0; i < cachedData.size(); i++) {
            MyListVideo cached = cachedData.get(i);
            MyListVideo newVideo = newData.get(i);

            if (!isMyListVideoEqual(cached, newVideo)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 比对两个MyListVideo是否相等
     */
    private boolean isMyListVideoEqual(MyListVideo video1, MyListVideo video2) {
        if (video1 == null && video2 == null) return true;
        if (video1 == null || video2 == null) return false;

        return Objects.equals(video1.getId(), video2.getId()) &&
               Objects.equals(video1.getTitle(), video2.getTitle()) &&
               Objects.equals(video1.getPosterUrl(), video2.getPosterUrl());
    }

    /**
     * 在UI线程更新MyList数据
     */
    private void updateMyListDataInUI(List<MyListVideo> newData) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                try {
                    // 更新当前标签页的视频列表
                    switch (currentTab) {
                        case "following":
                            followingVideos.clear();
                            followingVideos.addAll(newData);
                            break;
                        case "history":
                            historyVideos.clear();
                            historyVideos.addAll(newData);
                            break;
                        case "interest":
                            interestVideos.clear();
                            interestVideos.addAll(newData);
                            break;
                    }

                    // 通知适配器数据变化
                    if (videoAdapter != null) {
                        videoAdapter.notifyDataSetChanged();
                    }

                    // 更新缓存
                    saveDataToCache();

                    Log.d(TAG, currentTab + "数据已更新到UI");
                } catch (Exception e) {
                    Log.e(TAG, "更新MyList UI时发生错误: " + e.getMessage());
                }
            });
        }
    }
}


