package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 瀑布流推荐分辨率信息模型
 * <p>
 * 用于映射瀑布流推荐API返回的分辨率信息数据结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>type: 分辨率类型（auto/480/720/1080）</li>
 *   <li>needVip: 是否需要VIP（0=否, 1=是）</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallQualityModel {
    
    /**
     * 分辨率类型
     * 可选值：auto, 480, 720, 1080
     */
    @SerializedName("type")
    private String type;
    
    /**
     * 是否需要VIP
     * 0=否, 1=是
     */
    @SerializedName("needVip")
    private int needVip;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallQualityModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param type 分辨率类型
     * @param needVip 是否需要VIP
     */
    public WaterfallQualityModel(String type, int needVip) {
        this.type = type;
        this.needVip = needVip;
    }

    // ========== Getter和Setter方法 ==========

    /**
     * 获取分辨率类型
     * @return 分辨率类型
     */
    public String getType() {
        return type;
    }

    /**
     * 设置分辨率类型
     * @param type 分辨率类型
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取是否需要VIP
     * @return 是否需要VIP（0=否, 1=是）
     */
    public int getNeedVip() {
        return needVip;
    }

    /**
     * 设置是否需要VIP
     * @param needVip 是否需要VIP（0=否, 1=是）
     */
    public void setNeedVip(int needVip) {
        this.needVip = needVip;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否需要VIP
     * @return 如果需要VIP则返回true
     */
    public boolean isVipRequired() {
        return needVip == 1;
    }

    /**
     * 检查是否为自动分辨率
     * @return 如果是自动分辨率则返回true
     */
    public boolean isAutoQuality() {
        return "auto".equalsIgnoreCase(type);
    }

    /**
     * 获取分辨率显示名称
     * @return 分辨率显示名称
     */
    public String getDisplayName() {
        if (isAutoQuality()) {
            return "自动";
        }
        return type + "P";
    }

    @Override
    public String toString() {
        return "WaterfallQualityModel{" +
                "type='" + type + '\'' +
                ", needVip=" + needVip +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        WaterfallQualityModel that = (WaterfallQualityModel) o;

        if (needVip != that.needVip) return false;
        return type != null ? type.equals(that.type) : that.type == null;
    }

    @Override
    public int hashCode() {
        int result = type != null ? type.hashCode() : 0;
        result = 31 * result + needVip;
        return result;
    }
}
