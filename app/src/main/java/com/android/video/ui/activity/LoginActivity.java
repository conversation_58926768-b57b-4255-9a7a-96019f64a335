package com.android.video.ui.activity;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.util.Log;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.OvershootInterpolator;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import com.android.video.network.AuthApiUtils;
import com.android.video.network.ApiClientUtils;
import com.android.video.model.response.LoginResponseModel;

import androidx.activity.OnBackPressedCallback;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.content.ContextCompat;
import com.android.video.R;
import com.android.video.utils.UserSessionUtils;
import com.android.video.base.BaseMultiLanguageActivity;

/**
 * 登录选择页面 - 提供多种登录方式选择
 * 继承BaseFullScreenActivity以获得统一的全屏状态栏处理
 * <AUTHOR>
 */
public class LoginActivity extends BaseMultiLanguageActivity {

    private static final String TAG = "LoginActivity";

    private ConstraintLayout btnPhoneLogin;
    private ConstraintLayout btnFacebookLogin;
    private ConstraintLayout btnTiktokLogin;
    private TextView tvUserAgreement;
    private ImageView ivLogo;
    private ImageView ivBackButton;
    private ImageView ivBackground;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 初始化网络客户端（应用启动时必须初始化）
        initializeNetworkComponents();

        // 检查用户是否已经登录
        if (UserSessionUtils.isUserLoggedIn(this)) {
            // 用户已登录，直接跳转到主页
            Log.d(TAG, "🎯 用户已登录，跳转到MainActivity");
            navigateToMainActivity();
            return;
        } else {
            Log.d(TAG, "🎯 用户未登录，显示登录页面");
        }

        setContentView(R.layout.activity_login);

        mainHandler = new Handler(Looper.getMainLooper());

        // 处理TikTok OAuth回调
        handleTikTokCallback();

        initViews();
        setupClickListeners();
        setupAnimations();
        setupUserAgreementText();
        setupBackPressedCallback();
    }

    /**
     * 初始化网络组件
     */
    private void initializeNetworkComponents() {
        try {
            // 初始化ApiClientUtils
            ApiClientUtils.initialize(this);
            Log.d(TAG, "✅ Network components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to initialize network components", e);
        }
    }

    /**
     * 处理TikTok OAuth回调
     */
    private void handleTikTokCallback() {
        Intent intent = getIntent();
        if (intent != null && intent.getData() != null) {
            Uri data = intent.getData();
            String scheme = data.getScheme();
            String host = data.getHost();

            // 检查是否是TikTok回调
            if ("videoplayer".equals(scheme) && "tiktok.callback".equals(host)) {
                Log.d(TAG, "Received TikTok OAuth callback: " + data.toString());

                String code = data.getQueryParameter("code");
                String error = data.getQueryParameter("error");

                if (error != null) {
                    Log.e(TAG, "TikTok OAuth error: " + error);
                    Toast.makeText(this, "TikTok登录失败: " + error, Toast.LENGTH_LONG).show();
                } else if (code != null) {
                    Log.d(TAG, "TikTok OAuth code received: " + code);
                    // 这里可以调用TikTok登录处理逻辑
                    handleTikTokAuthCode(code);
                } else {
                    Log.e(TAG, "TikTok OAuth callback missing code and error");
                    Toast.makeText(this, "TikTok登录回调数据异常", Toast.LENGTH_SHORT).show();
                }
            }
        }
    }

    /**
     * 处理TikTok授权码
     * @param authCode 授权码
     */
    private void handleTikTokAuthCode(String authCode) {
        // 调用AuthApiUtils进行后端登录
        AuthApiUtils.tiktokLogin(this, authCode, new AuthApiUtils.ApiCallback<LoginResponseModel>() {
            @Override
            public void onSuccess(LoginResponseModel loginResponse) {
                Log.d(TAG, "TikTok login successful");

                // 保存登录状态
                UserSessionUtils.saveLoginState(LoginActivity.this, true);
                UserSessionUtils.saveUserId(LoginActivity.this, loginResponse.getUid());
                UserSessionUtils.saveVipStatus(LoginActivity.this, loginResponse.getUserVip());

                // 更新TokenManager中的token和deviceId
                com.android.video.manager.TokenManager tokenManager =
                    com.android.video.manager.TokenManager.getInstance(LoginActivity.this);
                tokenManager.updateToken(loginResponse);

                Log.d(TAG, "Token已更新到TokenManager");

                Toast.makeText(LoginActivity.this, "TikTok登录成功", Toast.LENGTH_SHORT).show();
                navigateToMainActivity();
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "TikTok login failed: " + errorMessage);
                Toast.makeText(LoginActivity.this, "TikTok登录失败: " + errorMessage, Toast.LENGTH_LONG).show();
            }
        });
    }

    private void initViews() {
        btnPhoneLogin = findViewById(R.id.btn_phone_login);
        btnFacebookLogin = findViewById(R.id.btn_facebook_login);
        btnTiktokLogin = findViewById(R.id.btn_tiktok_login);
        tvUserAgreement = findViewById(R.id.tv_user_agreement);
        ivLogo = findViewById(R.id.iv_logo);
        ivBackButton = findViewById(R.id.iv_back_button);
        ivBackground = findViewById(R.id.iv_background);
    }

    private void setupClickListeners() {
        btnPhoneLogin.setOnClickListener(v -> {
            animateButton(v);
            handlePhoneLogin();
        });

        btnFacebookLogin.setOnClickListener(v -> {
            animateButton(v);
            handleFacebookLogin();
        });

        btnTiktokLogin.setOnClickListener(v -> {
            animateButton(v);
            handleTiktokLogin();
        });

        ivBackButton.setOnClickListener(v -> handleBackButton());
    }

    private void setupAnimations() {
        // Logo entrance animation
        animateLogoEntrance();

        // Add subtle entrance animations for buttons
        animateButtonEntrance(btnPhoneLogin, 300);
        animateButtonEntrance(btnFacebookLogin, 400);
        animateButtonEntrance(btnTiktokLogin, 500);

        // Animate text elements
        animateTextEntrance(findViewById(R.id.tv_privacy_notice), 600);
        animateTextEntrance(tvUserAgreement, 700);
    }

    private void animateLogoEntrance() {
        ivLogo.setAlpha(0f);
        ivLogo.setScaleX(0.3f);
        ivLogo.setScaleY(0.3f);

        AnimatorSet logoAnimator = new AnimatorSet();
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(ivLogo, "alpha", 0f, 1f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(ivLogo, "scaleX", 0.3f, 1.1f, 1f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(ivLogo, "scaleY", 0.3f, 1.1f, 1f);

        logoAnimator.playTogether(alphaAnimator, scaleXAnimator, scaleYAnimator);
        logoAnimator.setDuration(800);
        logoAnimator.setInterpolator(new OvershootInterpolator(1.2f));
        logoAnimator.start();
    }

    private void animateTextEntrance(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(30f);

        view.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(delay)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void animateButtonEntrance(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(50f);
        
        view.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(600)
                .setStartDelay(delay)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void animateButton(View view) {
        // 使用统一的按钮点击动画（0.9倍缩放，0.2秒）
        com.android.video.utils.UIAnimationUtils.animateButtonClick(view);
    }

    private void handlePhoneLogin() {
        // Navigate to phone login screen
        Intent intent = new Intent(LoginActivity.this, PhoneLoginActivity.class);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.slide_in_left, android.R.anim.slide_out_right);
        startActivity(intent, options.toBundle());
    }

    private void handleFacebookLogin() {
        Log.d(TAG, "VKontakte login button clicked");

        // 显示加载状态
        showLoadingState("Connecting to VKontakte...");

        // 初始化并开始VKontakte登录
        com.android.video.auth.VKontakteLoginUtils.startLogin(this, new com.android.video.auth.VKontakteLoginUtils.VKontakteLoginCallback() {
            @Override
            public void onSuccess(com.android.video.model.response.LoginResponseModel loginResponse) {
                Log.d(TAG, "VKontakte login successful");
                hideLoadingState();

                // 保存登录状态
                saveLoginState(loginResponse);

                // 显示成功消息
                Toast.makeText(LoginActivity.this, "VKontakte login successful!", Toast.LENGTH_SHORT).show();

                // 导航到主界面
                navigateToMainActivity();
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "VKontakte login failed: " + errorMessage);
                hideLoadingState();
                Toast.makeText(LoginActivity.this, "VKontakte login failed: " + errorMessage, Toast.LENGTH_LONG).show();
            }

            @Override
            public void onCancel() {
                Log.d(TAG, "VKontakte login cancelled");
                hideLoadingState();
                Toast.makeText(LoginActivity.this, "VKontakte login cancelled", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void handleTiktokLogin() {
        Log.d(TAG, "TikTok login button clicked");

        // 检查TikTok应用是否已安装（使用改进的检测方法）
        boolean isTikTokInstalled = com.android.video.auth.TiktokLoginUtils.isTikTokAppInstalled(this);
        String installedPackage = com.android.video.auth.TiktokLoginUtils.getInstalledTikTokPackageName(this);

        Log.d(TAG, "TikTok installation check - Installed: " + isTikTokInstalled + ", Package: " + installedPackage);

        if (!isTikTokInstalled) {
            // 运行调试方法来查找TikTok相关应用
            Log.d(TAG, "TikTok not detected, running debug scan...");
            com.android.video.auth.TiktokLoginUtils.debugListTikTokRelatedApps(this);

            String message = "TikTok app is not installed. Please install TikTok first.\n\nChecked packages:\n- com.zhiliaoapp.musically\n- com.ss.android.ugc.trill\n\nCheck logs for detailed scan results.";
            Toast.makeText(this, message, Toast.LENGTH_LONG).show();
            return;
        }

        Log.d(TAG, "TikTok app detected with package: " + installedPackage);

        // 显示加载状态
        showLoadingState("Connecting to TikTok...");

        // 初始化并开始TikTok登录
        com.android.video.auth.TiktokLoginUtils.startLogin(this, new com.android.video.auth.TiktokLoginUtils.TiktokLoginCallback() {
            @Override
            public void onSuccess(com.android.video.model.response.LoginResponseModel loginResponse) {
                Log.d(TAG, "TikTok login successful");
                hideLoadingState();

                // 保存登录状态
                saveLoginState(loginResponse);

                // 显示成功消息
                Toast.makeText(LoginActivity.this, "TikTok login successful!", Toast.LENGTH_SHORT).show();

                // 导航到主界面
                navigateToMainActivity();
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "TikTok login failed: " + errorMessage);
                hideLoadingState();
                Toast.makeText(LoginActivity.this, "TikTok login failed: " + errorMessage, Toast.LENGTH_LONG).show();
            }

            @Override
            public void onCancel() {
                Log.d(TAG, "TikTok login cancelled");
                hideLoadingState();
                Toast.makeText(LoginActivity.this, "TikTok login cancelled", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void handleBackButton() {
        // Handle back button click - navigate to main activity
        navigateToMainActivity();
    }

    /**
     * 显示加载状态
     * @param message 加载消息
     */
    private void showLoadingState(String message) {
        // 这里可以显示进度条或加载对话框
        // 暂时使用Toast显示加载状态
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoadingState() {
        // 隐藏进度条或加载对话框
        // 目前无需特殊处理
    }

    /**
     * 保存登录状态
     * @param loginResponse 登录响应数据
     */
    private void saveLoginState(com.android.video.model.response.LoginResponseModel loginResponse) {
        if (loginResponse != null) {
            // 使用UserSessionUtils保存登录状态
            com.android.video.utils.UserSessionUtils.saveLoginState(this, true);

            // 保存用户信息
            if (loginResponse.getUid() != null) {
                com.android.video.utils.UserSessionUtils.saveUserId(this, loginResponse.getUid());
            }
            if (loginResponse.getPhoneNumber() != null) {
                com.android.video.utils.UserSessionUtils.savePhoneNumber(this, loginResponse.getPhoneNumber());
            }

            // 保存VIP状态
            com.android.video.utils.UserSessionUtils.saveVipStatus(this, loginResponse.getUserVip());

            // 更新TokenManager中的token和deviceId
            // 直接使用response.LoginResponseModel，因为TokenManager已经支持这个类型
            if (loginResponse.getUserToken() != null) {
                com.android.video.manager.TokenManager tokenManager =
                    com.android.video.manager.TokenManager.getInstance(this);
                tokenManager.updateToken(loginResponse);

                Log.d(TAG, "Token已更新到TokenManager");
            }

            Log.d(TAG, "Login state saved successfully");
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 处理Facebook登录结果
        com.android.video.auth.FacebookLoginUtils.onActivityResult(requestCode, resultCode, data);

        // 处理TikTok登录结果
        com.android.video.auth.TiktokLoginUtils.onActivityResult(requestCode, resultCode, data);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理第三方登录回调，防止内存泄漏
        com.android.video.auth.TiktokLoginUtils.clearCallback();
        com.android.video.auth.VKontakteLoginUtils.clearCallback();
    }

    private void navigateToMainActivity() {
        Log.d(TAG, "🎯 开始跳转到MainActivity");
        Intent intent = new Intent(LoginActivity.this, MainActivity.class);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.fade_in, android.R.anim.fade_out);
        startActivity(intent, options.toBundle());
        finish(); // Close login activity
    }

    private void setupUserAgreementText() {
        String fullText = "By logging in, you agree to the user agreement and privacy policy.";
        SpannableString spannableString = new SpannableString(fullText);

        // Get colors
        int grayColor = ContextCompat.getColor(this, R.color.login_text_gray);
        int whiteColor = ContextCompat.getColor(this, R.color.login_text_white);

        // Set different colors for different parts
        // "By logging in, you agree to the " - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 0, 32, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "user agreement" - white
        spannableString.setSpan(new ForegroundColorSpan(whiteColor), 32, 46, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // " and " - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 46, 51, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "privacy policy" - white
        spannableString.setSpan(new ForegroundColorSpan(whiteColor), 51, 65, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "." - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 65, 66, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        tvUserAgreement.setText(spannableString);
    }

    private void setupBackPressedCallback() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // Navigate back to previous screen instead of exiting app
                finish();
            }
        };
        getOnBackPressedDispatcher().addCallback(this, callback);
    }
}
