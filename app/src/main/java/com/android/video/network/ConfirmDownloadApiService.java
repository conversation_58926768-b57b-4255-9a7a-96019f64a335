package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.DownloadApiConstantsUtils;
import com.android.video.model.request.ConfirmDownloadRequestModel;
import com.android.video.model.response.ConfirmDownloadResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 确认下载API服务类
 * <p>
 * 提供确认下载完成的网络请求服务。
 * 使用OkHttp进行网络请求，Gson进行JSON解析，Handler进行主线程回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class ConfirmDownloadApiService {

    private static final String TAG = "ConfirmDownloadApiService";

    /**
     * JSON媒体类型
     */
    private static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    /**
     * OkHttp客户端实例
     */
    private final OkHttpClient client;

    /**
     * Gson实例，用于JSON解析
     */
    private final Gson gson;

    /**
     * 主线程Handler，用于回调
     */
    private final Handler mainHandler;

    /**
     * 线程池，用于执行网络请求
     */
    private final ExecutorService executor;

    /**
     * 单例实例
     */
    private static volatile ConfirmDownloadApiService instance;

    /**
     * 确认下载回调接口
     */
    public interface ConfirmDownloadCallback {
        /**
         * 确认下载成功
         *
         * @param response 响应数据模型
         */
        void onSuccess(ConfirmDownloadResponseModel response);

        /**
         * 确认下载失败
         *
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造函数
     */
    private ConfirmDownloadApiService() {
        this.client = new OkHttpClient();
        this.gson = new Gson();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }

    /**
     * 获取单例实例
     *
     * @return ConfirmDownloadApiService实例
     */
    public static ConfirmDownloadApiService getInstance() {
        if (instance == null) {
            synchronized (ConfirmDownloadApiService.class) {
                if (instance == null) {
                    instance = new ConfirmDownloadApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 确认下载完成
     * <p>
     * 向服务器发送确认下载完成的请求，包含下载记录ID、文件校验和以及本地存储路径。
     * </p>
     *
     * @param requestModel 确认下载请求数据模型
     * @param callback 回调接口
     */
    public void confirmDownload(ConfirmDownloadRequestModel requestModel, ConfirmDownloadCallback callback) {
        // 参数验证
        if (requestModel == null) {
            if (callback != null) {
                callback.onError("请求参数不能为空");
            }
            return;
        }

        if (!requestModel.isValid()) {
            String error = requestModel.getValidationError();
            Log.e(TAG, "请求参数验证失败: " + error);
            if (callback != null) {
                callback.onError(error);
            }
            return;
        }

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + DownloadApiConstantsUtils.API_CONFIRM_DOWNLOAD;

        // 构建请求体JSON
        String requestBodyJson = gson.toJson(requestModel);

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建POST请求
        RequestBody requestBody = RequestBody.create(requestBodyJson, JSON_MEDIA_TYPE);

        Request.Builder requestBuilder = new Request.Builder()
                .url(apiUrl)
                .post(requestBody);

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 确认下载API请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "请求头: " + headers);
        Log.d(TAG, "请求体: " + requestBodyJson);
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "确认下载API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body() != null ? response.body().string() : "";
                        Log.d(TAG, "=== 确认下载API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseConfirmDownloadResponse(responseBody, callback);
                        } else {
                            // 处理HTTP错误状态码
                            String errorMessage = buildHttpErrorMessage(response.code());
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(errorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "确认下载响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析确认下载响应数据
     *
     * @param responseBody 响应体字符串
     * @param callback 回调接口
     */
    private void parseConfirmDownloadResponse(String responseBody, ConfirmDownloadCallback callback) {
        try {
            if (responseBody == null || responseBody.trim().isEmpty()) {
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("服务器响应为空");
                    }
                });
                return;
            }

            // 解析JSON响应
            ConfirmDownloadResponseModel responseModel = gson.fromJson(responseBody, ConfirmDownloadResponseModel.class);

            if (responseModel == null) {
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据解析失败");
                    }
                });
                return;
            }

            // 在主线程中回调结果
            mainHandler.post(() -> {
                if (callback != null) {
                    if (responseModel.isSuccess()) {
                        callback.onSuccess(responseModel);
                    } else {
                        callback.onError(responseModel.getErrorMessage());
                    }
                }
            });

        } catch (JsonSyntaxException e) {
            Log.e(TAG, "JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("响应数据格式错误");
                }
            });
        } catch (Exception e) {
            Log.e(TAG, "响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("响应解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 构建HTTP错误信息
     *
     * @param httpCode HTTP状态码
     * @return 错误信息字符串
     */
    private String buildHttpErrorMessage(int httpCode) {
        switch (httpCode) {
            case 400:
                return "请求参数错误 (400)";
            case 401:
                return "认证失败，请重新登录 (401)";
            case 403:
                return "访问被拒绝 (403)";
            case 404:
                return "接口不存在 (404)";
            case 500:
                return "服务器内部错误 (500)";
            case 502:
                return "网关错误 (502)";
            case 503:
                return "服务不可用 (503)";
            default:
                return "服务器错误 (" + httpCode + ")";
        }
    }
}
