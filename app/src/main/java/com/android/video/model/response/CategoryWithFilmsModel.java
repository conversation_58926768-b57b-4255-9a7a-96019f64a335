package com.android.video.model.response;

import com.android.video.constants.HomeApiConstantsUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 包含短剧列表的分类数据模型
 * <p>
 * 用于解析分类短剧列表API返回的分类数据结构，包含该分类下的短剧列表。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_CATEGORIES}
 * </p>
 *
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>categoryId: 分类唯一标识符</li>
 *   <li>categoryName: 分类显示名称</li>
 *   <li>films: 该分类下的短剧列表</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * List&lt;CategoryWithFilmsModel&gt; categories = gson.fromJson(jsonArray,
 *     new TypeToken&lt;List&lt;CategoryWithFilmsModel&gt;&gt;(){}.getType());
 *
 * // 获取分类和短剧信息
 * for (CategoryWithFilmsModel category : categories) {
 *     String categoryName = category.getCategoryName();
 *     List&lt;CategoryFilmModel&gt; films = category.getFilms();
 *     if (category.hasFilms()) {
 *         // 处理短剧列表
 *     }
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_CATEGORIES
 * @see CategoryFilmModel
 */
public class CategoryWithFilmsModel {

    /**
     * 分类ID
     * <p>
     * 分类的唯一标识符，用于API请求和数据关联。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_ID)
    @Expose
    private String categoryId;

    /**
     * 分类名称
     * <p>
     * 分类的显示名称，用于UI展示。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_NAME)
    @Expose
    private String categoryName;

    /**
     * 短剧列表
     * <p>
     * 该分类下的短剧列表，可能为null或空列表。
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILMS)
    @Expose
    private List<CategoryFilmModel> films;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public CategoryWithFilmsModel() {
    }

    /**
     * 基础构造函数
     *
     * @param categoryId 分类ID
     * @param categoryName 分类名称
     */
    public CategoryWithFilmsModel(String categoryId, String categoryName) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
    }

    /**
     * 完整构造函数
     *
     * @param categoryId 分类ID
     * @param categoryName 分类名称
     * @param films 短剧列表
     */
    public CategoryWithFilmsModel(String categoryId, String categoryName, List<CategoryFilmModel> films) {
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.films = films;
    }

    // ========== Getter和Setter方法 ==========

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public List<CategoryFilmModel> getFilms() {
        return films;
    }

    public void setFilms(List<CategoryFilmModel> films) {
        this.films = films;
    }

    // ========== 工具方法 ==========

    /**
     * 检查分类是否有短剧
     * <p>
     * 判断该分类下是否有短剧内容。
     * </p>
     *
     * @return 如果有短剧则返回true，否则返回false
     */
    public boolean hasFilms() {
        return films != null && !films.isEmpty();
    }

    /**
     * 获取短剧数量
     * <p>
     * 返回该分类下的短剧数量。
     * </p>
     *
     * @return 短剧数量
     */
    public int getFilmCount() {
        return films != null ? films.size() : 0;
    }

    /**
     * 检查分类信息是否有效
     * <p>
     * 判断分类的基本信息是否完整。
     * </p>
     *
     * @return 如果分类信息有效则返回true，否则返回false
     */
    public boolean isValid() {
        return categoryId != null && !categoryId.trim().isEmpty() &&
               categoryName != null && !categoryName.trim().isEmpty();
    }

    /**
     * 获取有效的短剧列表
     * <p>
     * 过滤掉无效的短剧，返回有效的短剧列表。
     * </p>
     *
     * @return 有效的短剧列表
     */
    public List<CategoryFilmModel> getValidFilms() {
        if (!hasFilms()) {
            return null;
        }

        return films.stream()
                   .filter(CategoryFilmModel::isValid)
                   .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据语言类型过滤短剧
     * <p>
     * 返回指定语言类型的短剧列表。
     * </p>
     *
     * @param languageType 语言类型（1=英语, 2=俄语）
     * @return 指定语言的短剧列表
     */
    public List<CategoryFilmModel> getFilmsByLanguage(int languageType) {
        if (!hasFilms()) {
            return null;
        }

        return films.stream()
                   .filter(film -> film.getLanguageType() == languageType)
                   .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 检查分类是否为空分类
     * <p>
     * 判断分类是否没有任何短剧内容。
     * </p>
     *
     * @return 如果是空分类则返回true，否则返回false
     */
    public boolean isEmpty() {
        return !hasFilms();
    }

    /**
     * 获取分类状态描述
     * <p>
     * 返回分类的状态描述信息。
     * </p>
     *
     * @return 分类状态描述
     */
    public String getStatusDescription() {
        if (!isValid()) {
            return "无效分类";
        }

        if (isEmpty()) {
            return "空分类";
        }

        return "包含 " + getFilmCount() + " 部短剧";
    }

    @Override
    public String toString() {
        return "CategoryWithFilmsModel{" +
                "categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", films=" + (films != null ? films.size() + " films" : "null") +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CategoryWithFilmsModel that = (CategoryWithFilmsModel) o;

        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }

    @Override
    public int hashCode() {
        return categoryId != null ? categoryId.hashCode() : 0;
    }
}
