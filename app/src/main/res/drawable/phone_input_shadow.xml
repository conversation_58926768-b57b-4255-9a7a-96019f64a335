<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Shadow layer with gradient for blur effect -->
    <item android:left="@dimen/common_shadow_offset_x" android:top="@dimen/common_shadow_offset_y">
        <shape android:shape="rectangle">
            <gradient
                android:type="radial"
                android:gradientRadius="@dimen/common_shadow_radius"
                android:centerColor="#3ddf00ff"
                android:endColor="#00df00ff" />
            <corners android:radius="@dimen/common_corner_radius_large" />
        </shape>
    </item>

    <!-- Main background layer -->
    <item android:right="@dimen/common_shadow_offset_x" android:bottom="@dimen/common_shadow_offset_y">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="@dimen/common_corner_radius_large" />
        </shape>
    </item>
    
</layer-list>
