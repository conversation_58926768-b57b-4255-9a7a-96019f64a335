<resources>
    <dimen name="fab_margin">16dp</dimen>

    <!-- Elevation Values - Base values for 360dp reference -->
    <dimen name="elevation_none">0dp</dimen>
    <dimen name="elevation_low">2dp</dimen>
    <dimen name="elevation_medium">4dp</dimen>
    <dimen name="elevation_high">8dp</dimen>

    <!-- Video Player Fixed Values - Base values for 360dp reference -->
    <dimen name="video_player_divider_height">1dp</dimen>
    <dimen name="video_player_progress_height">4dp</dimen>
    <dimen name="video_player_thumb_offset">0dp</dimen>
    <dimen name="video_player_control_margin_end">2dp</dimen>
    <dimen name="video_player_popup_margin">16dp</dimen>
    <dimen name="video_player_selection_bottom_margin">200dp</dimen>
    <dimen name="video_player_speed_selection_offset">70dp</dimen>

    <!-- Login Screen Dimensions - Base values for 375dp reference -->
    <!-- Login Screen Dimensions Base values for 375dp reference -->

    <!-- Back button dimensions -->
    <dimen name="login_back_button_size">36dp</dimen>
    <dimen name="login_back_button_margin_top">44dp</dimen>
    <dimen name="login_back_button_margin_start">16dp</dimen>

    <!-- Logo dimensions -->
    <dimen name="login_logo_width">268dp</dimen>
    <dimen name="login_logo_height">139dp</dimen>
    <dimen name="login_logo_margin_top">125dp</dimen>

    <!-- Login button dimensions -->
    <dimen name="login_button_width">298dp</dimen>
    <dimen name="login_button_height">48dp</dimen>
    <dimen name="login_button_corner_radius">24dp</dimen>
    <dimen name="login_button_margin_horizontal">23dp</dimen>
    <dimen name="login_button_margin_bottom">16dp</dimen>
    <dimen name="login_button_elevation">3dp</dimen>

    <!-- Icon dimensions -->
    <dimen name="login_icon_size">24dp</dimen>
    <dimen name="login_icon_margin_end">12dp</dimen>

    <!-- Text dimensions -->
    <dimen name="login_text_size">16sp</dimen>
    <dimen name="login_text_line_height">28dp</dimen>
    <dimen name="login_privacy_text_size">13sp</dimen>
    <dimen name="login_privacy_line_height">18dp</dimen>
    <dimen name="login_agreement_text_size">13sp</dimen>
    <dimen name="login_agreement_line_height">18dp</dimen>

    <!-- Update Dialog Dimensions - Base values for 360dp reference -->
    <dimen name="update_dialog_width">332dp</dimen>
    <dimen name="update_dialog_height">317dp</dimen>
    <dimen name="update_dialog_margin_top">230dp</dimen>
    <dimen name="update_title_margin_top">23dp</dimen>
    <dimen name="update_title_text_size">23sp</dimen>
    <dimen name="update_version_margin_top">7dp</dimen>
    <dimen name="update_version_text_size">14sp</dimen>
    <dimen name="update_line_width">30dp</dimen>
    <dimen name="update_line_height">0.5dp</dimen>
    <dimen name="update_line_margin">6dp</dimen>
    <dimen name="update_description_margin_top">23dp</dimen>
    <dimen name="update_description_margin_horizontal">24dp</dimen>
    <dimen name="update_description_text_size">16sp</dimen>
    <dimen name="update_description_line_spacing">5dp</dimen>
    <dimen name="update_rocket_size">36dp</dimen>
    <dimen name="update_button_margin_top">38dp</dimen>
    <dimen name="update_button_width">242dp</dimen>
    <dimen name="update_button_height">42dp</dimen>
    <dimen name="update_button_corner_radius">21dp</dimen>
    <dimen name="update_button_text_size">16sp</dimen>

    <!-- Privacy text area dimensions -->
    <dimen name="login_privacy_text_width">343dp</dimen>
    <dimen name="login_privacy_text_height">87dp</dimen>
    <dimen name="login_privacy_text_margin_horizontal">16dp</dimen>
    <dimen name="login_privacy_text_margin_top">48dp</dimen>

    <!-- Agreement text area dimensions -->
    <dimen name="login_agreement_text_width">343dp</dimen>
    <dimen name="login_agreement_text_height">36dp</dimen>
    <dimen name="login_agreement_text_margin_horizontal">16dp</dimen>
    <dimen name="login_agreement_text_margin_top">5dp</dimen>
    <dimen name="login_agreement_text_margin_bottom">16dp</dimen>

    <!-- Content padding -->
    <dimen name="login_content_padding">16dp</dimen>

    <!-- Phone Login Dimensions - Base values for 375dp reference -->
    <dimen name="phone_login_title_margin_top">0dp</dimen>
    <dimen name="phone_input_width">343dp</dimen>
    <dimen name="phone_input_height">54dp</dimen>
    <dimen name="phone_input_margin_top">60dp</dimen>
    <dimen name="send_code_button_width">343dp</dimen>
    <dimen name="send_code_button_height">42dp</dimen>
    <dimen name="send_code_button_margin_top">55dp</dimen>

    <!-- Bottom Navigation Dimensions -->
    <dimen name="bottom_nav_height">64dp</dimen>
    <dimen name="bottom_nav_icon_size">24dp</dimen>
    <dimen name="bottom_nav_text_size">11sp</dimen>
    <dimen name="bottom_nav_icon_text_margin">10.5dp</dimen>
    <dimen name="bottom_nav_item_padding_vertical">8dp</dimen>
    <dimen name="bottom_nav_item_padding_horizontal">12dp</dimen>

    <!-- Bottom Navigation Selected Background Dimensions -->
    <dimen name="bottom_nav_selected_bg_width">48dp</dimen>
    <dimen name="bottom_nav_selected_bg_height">32dp</dimen>
    <dimen name="bottom_nav_selected_bg_radius">12dp</dimen>

    <!-- Bottom Navigation Layout Positioning -->
    <dimen name="bottom_nav_selected_bg_margin_top">3dp</dimen>
    <dimen name="bottom_nav_text_margin_from_rect">1.5dp</dimen>
    <dimen name="bottom_nav_text_margin_bottom">4dp</dimen>


    <dimen name="profile_edit_button_corner_radius">19dp</dimen>
    <dimen name="profile_edit_text_line_height">19dp</dimen>

    <!-- Auto Renewal Dimensions -->
    <dimen name="profile_auto_renewal_text_line_height">17dp</dimen>
    <dimen name="profile_renewal_period_text_line_height">14dp</dimen>
    <dimen name="profile_renewal_icon_size">20dp</dimen>

    <!-- VIP Card Content Positioning -->
    <dimen name="profile_vip_title_width">304dp</dimen>
    <dimen name="profile_vip_title_height">24dp</dimen>
    <dimen name="profile_vip_title_margin_start">16dp</dimen>
    <dimen name="profile_vip_title_margin_top">35dp</dimen>

    <dimen name="profile_vip_star_margin_start">16dp</dimen>
    <dimen name="profile_vip_star_margin_top">80dp</dimen>

    <dimen name="profile_vip_text_width">192dp</dimen>
    <dimen name="profile_vip_text_height">17dp</dimen>
    <dimen name="profile_vip_text_margin_start">38dp</dimen>
    <dimen name="profile_vip_text_margin_top">80dp</dimen>

    <dimen name="profile_vip_button_width">86dp</dimen>
    <dimen name="profile_vip_button_height">38dp</dimen>
    <dimen name="profile_vip_button_margin_start">242dp</dimen>
    <dimen name="profile_vip_button_margin_top">69dp</dimen>



    <dimen name="profile_points_refill_button_corner_radius">19dp</dimen>
    <dimen name="profile_points_refill_text_line_height">19dp</dimen>

    <!-- Menu Items Dimensions - Base values for 375dp reference -->
    <dimen name="profile_menu_corner_radius">12dp</dimen>

    <!-- Subscribe Page Dimensions - Base values for 375dp reference -->
    <!-- Precise positioning based on design specs -->
    <dimen name="subscribe_title_width">154dp</dimen>
    <dimen name="subscribe_title_height">38dp</dimen>
    <dimen name="subscribe_title_margin_start">111dp</dimen>
    <dimen name="subscribe_title_margin_top">85dp</dimen>
    <dimen name="subscribe_title_text_size">32sp</dimen>

    <dimen name="subscribe_redeem_icon_width">22dp</dimen>
    <dimen name="subscribe_redeem_icon_height">23dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_start">247dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_top">52dp</dimen>

    <dimen name="subscribe_redeem_text_width">88dp</dimen>
    <dimen name="subscribe_redeem_text_height">17dp</dimen>
    <dimen name="subscribe_redeem_text_margin_start">273dp</dimen>
    <dimen name="subscribe_redeem_text_margin_top">55dp</dimen>
    <dimen name="subscribe_redeem_text_size">14sp</dimen>

    <dimen name="subscribe_watch_more_width">191dp</dimen>
    <dimen name="subscribe_watch_more_height">21dp</dimen>
    <dimen name="subscribe_watch_more_margin_start">92dp</dimen>
    <dimen name="subscribe_watch_more_margin_top">100dp</dimen>
    <dimen name="subscribe_watch_more_text_size">18sp</dimen>

    <dimen name="subscribe_decoration_size">16dp</dimen>
    <dimen name="subscribe_decoration_left_margin_start">64dp</dimen>
    <dimen name="subscribe_decoration_right_margin_start">295dp</dimen>
    <dimen name="subscribe_decoration_margin_top">143dp</dimen>

    <!-- Subscription Cards - Legacy (will be replaced by new responsive dimensions) -->

    <!-- Card Text Sizes -->
    <dimen name="subscribe_plan_title_text_size">18sp</dimen>
    <dimen name="subscribe_plan_subtitle_text_size">14sp</dimen>
    <dimen name="subscribe_plan_description_text_size">13sp</dimen>
    <dimen name="subscribe_price_text_size">24sp</dimen>
    <dimen name="subscribe_discount_text_size">13sp</dimen>

    <!-- Discount Badge -->
    <dimen name="subscribe_discount_badge_width">88dp</dimen>
    <dimen name="subscribe_discount_badge_height">20dp</dimen>
    <dimen name="subscribe_discount_badge_corner_radius">11dp</dimen>

    <!-- VIP Privileges - Legacy (replaced by new responsive dimensions) -->

    <!-- Subscribe Button - Legacy (replaced by new responsive dimensions) -->

    <!-- Tag Dimensions - Base values for 375dp reference -->
    <dimen name="tag_margin">6dp</dimen>
    <dimen name="tag_padding_horizontal">16dp</dimen>
    <dimen name="tag_padding_vertical">8dp</dimen>
    <dimen name="tag_text_size">14sp</dimen>
    <dimen name="tag_corner_radius">14dp</dimen>
    <dimen name="tag_border_width">1dp</dimen>

    <!-- Favorite Selection Page Dimensions - Base values for 375dp reference -->
    <dimen name="favorite_selection_top_shadow_height">44dp</dimen>
    <dimen name="favorite_selection_title_margin_top">90dp</dimen>
    <dimen name="favorite_selection_title_text_size">21sp</dimen>
    <dimen name="favorite_selection_description_margin_top">12dp</dimen>
    <dimen name="favorite_selection_description_text_size">21sp</dimen>
    <dimen name="favorite_selection_viewpager_margin_top">40dp</dimen>
    <dimen name="favorite_selection_viewpager_height">300dp</dimen>
    <dimen name="favorite_selection_indicator_margin_top">20dp</dimen>
    <dimen name="favorite_selection_indicator_dot_size">8dp</dimen>
    <dimen name="favorite_selection_indicator_dot_margin">4dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_top">40dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_bottom">40dp</dimen>
    <dimen name="favorite_selection_complete_button_height">48dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_horizontal">32dp</dimen>

    <!-- ConstraintLayout 2.0 Advanced Features Support - Base values for 375dp reference -->

    <!-- Layout Guidelines Percentages (as float items for programmatic use) -->
    <item name="layout_guideline_vertical_center" format="float" type="dimen">0.5</item>
    <item name="layout_guideline_content_start" format="float" type="dimen">0.1</item>
    <item name="layout_guideline_content_end" format="float" type="dimen">0.9</item>
    <item name="layout_guideline_header_bottom" format="float" type="dimen">0.25</item>
    <item name="layout_guideline_form_bottom" format="float" type="dimen">0.75</item>
    <item name="layout_guideline_footer_top" format="float" type="dimen">0.85</item>
    <item name="layout_guideline_bottom_safe" format="float" type="dimen">0.95</item>

    <!-- Unified Layout Margins for ConstraintLayout -->
    <dimen name="layout_margin_tiny">4dp</dimen>
    <dimen name="layout_margin_small">8dp</dimen>
    <dimen name="layout_margin_medium">16dp</dimen>
    <dimen name="layout_margin_large">24dp</dimen>
    <dimen name="layout_margin_xlarge">32dp</dimen>

    <!-- Chain Spacing for ConstraintLayout Chains -->
    <dimen name="chain_spacing_tiny">4dp</dimen>
    <dimen name="chain_spacing_small">8dp</dimen>
    <dimen name="chain_spacing_medium">16dp</dimen>
    <dimen name="chain_spacing_large">24dp</dimen>

    <!-- Vertical Bias Values for Responsive Positioning -->
    <item name="layout_bias_top" format="float" type="dimen">0.2</item>
    <item name="layout_bias_center_top" format="float" type="dimen">0.4</item>
    <item name="layout_bias_center" format="float" type="dimen">0.5</item>
    <item name="layout_bias_center_bottom" format="float" type="dimen">0.6</item>
    <item name="layout_bias_bottom" format="float" type="dimen">0.8</item>

    <!-- Responsive Content Widths (percentage-based) -->
    <item name="content_width_narrow" format="float" type="dimen">0.7</item>
    <item name="content_width_normal" format="float" type="dimen">0.8</item>
    <item name="content_width_wide" format="float" type="dimen">0.9</item>
    <item name="content_width_full" format="float" type="dimen">1.0</item>

    <!-- Flow and Grid Layout Support -->
    <dimen name="flow_horizontal_gap">12dp</dimen>
    <dimen name="flow_vertical_gap">8dp</dimen>
    <dimen name="grid_column_gap">16dp</dimen>
    <dimen name="grid_row_gap">12dp</dimen>

    <!-- Subscribe Activity Specific -->
    <dimen name="subscribe_scroll_margin_top">180dp</dimen>
    <dimen name="subscribe_content_padding_bottom">100dp</dimen>

    <!-- Favorite Selection Page Indicator Spacing -->
    <dimen name="favorite_selection_indicator_button_spacing">20dp</dimen>

    <!-- Profile Page Dimensions - Base values for 375dp reference -->
    <!-- Status bar and header -->
    <dimen name="profile_status_bar_height">20dp</dimen>
    <dimen name="profile_status_bar_margin_start">21dp</dimen>
    <dimen name="profile_status_bar_margin_top">4dp</dimen>
    <dimen name="profile_status_time_width">54dp</dimen>
    <dimen name="profile_status_time_height">18dp</dimen>
    <dimen name="profile_status_icon_margin_top">8dp</dimen>
    <dimen name="profile_status_battery_margin_top">10dp</dimen>

    <!-- User info section -->
    <dimen name="profile_avatar_size">68dp</dimen>
    <dimen name="profile_avatar_margin_start">16dp</dimen>
    <dimen name="profile_avatar_margin_top">45dp</dimen>
    <dimen name="profile_username_width">55dp</dimen>
    <dimen name="profile_username_height">24dp</dimen>
    <dimen name="profile_username_margin_start">96dp</dimen>
    <dimen name="profile_username_margin_top">55dp</dimen>
    <dimen name="profile_uid_width">93dp</dimen>
    <dimen name="profile_uid_height">17dp</dimen>
    <dimen name="profile_uid_margin_start">96dp</dimen>
    <dimen name="profile_uid_margin_top">82dp</dimen>
    <dimen name="profile_copy_icon_size">14dp</dimen>
    <dimen name="profile_copy_margin_start">193dp</dimen>
    <dimen name="profile_copy_margin_top">87dp</dimen>
    <dimen name="profile_login_button_width">73dp</dimen>
    <dimen name="profile_login_button_height">38dp</dimen>
    <dimen name="profile_login_button_margin_top">55dp</dimen>
    <dimen name="profile_login_button_margin_end">16dp</dimen>

    <!-- VIP card -->
    <dimen name="profile_vip_card_height">167dp</dimen>
    <dimen name="profile_vip_card_margin_horizontal">16dp</dimen>
    <dimen name="profile_vip_card_margin_top">130dp</dimen>
    <dimen name="profile_vip_card_content_margin">32dp</dimen>
    <dimen name="profile_vip_card_type_margin_top">36dp</dimen>
    <dimen name="profile_vip_card_corner_radius">16dp</dimen>
    <dimen name="profile_vip_card_stroke_width">1dp</dimen>
    <dimen name="profile_vip_star_size">16dp</dimen>
    <dimen name="profile_vip_renewal_icon_size">20dp</dimen>

    <!-- VIP Card Edit Button -->
    <dimen name="profile_edit_button_width">60dp</dimen>
    <dimen name="profile_edit_button_height">32dp</dimen>
    <dimen name="profile_edit_text_size">14sp</dimen>
    <dimen name="profile_auto_renewal_text_size">14sp</dimen>
    <dimen name="profile_renewal_period_text_size">12sp</dimen>

    <!-- VIP Card Internal Elements -->
    <dimen name="profile_vip_card_type_width">304dp</dimen>
    <dimen name="profile_vip_card_type_height">24dp</dimen>
    <dimen name="profile_unlimited_access_icon_size">24dp</dimen>
    <dimen name="profile_vip_star_margin_bottom">52dp</dimen>
    <dimen name="profile_vip_star_second_margin_bottom">16dp</dimen>
    <dimen name="profile_vip_go_button_width">80dp</dimen>
    <dimen name="profile_vip_go_button_height">32dp</dimen>
    <dimen name="profile_vip_renewal_text_width">123dp</dimen>
    <dimen name="profile_vip_renewal_text_height">17dp</dimen>
    <dimen name="profile_vip_renewal_period_width">98dp</dimen>
    <dimen name="profile_vip_renewal_period_height">14dp</dimen>

    <!-- Points section -->
    <dimen name="profile_points_card_height">95dp</dimen>
    <dimen name="profile_points_card_margin_top">310dp</dimen>
    <dimen name="profile_points_card_padding">16dp</dimen>
    <dimen name="profile_points_icon_size">24dp</dimen>
    <dimen name="profile_points_text_size">16sp</dimen>
    <dimen name="profile_points_value_size">20sp</dimen>
    <dimen name="profile_points_jinbi_size">20dp</dimen>
    <dimen name="profile_points_text_line_height">24dp</dimen>
    <dimen name="profile_points_refill_button_width">80dp</dimen>
    <dimen name="profile_points_refill_button_height">32dp</dimen>
    <dimen name="profile_points_refill_text_size">14sp</dimen>

    <!-- Menu items -->
    <dimen name="profile_menu_item_height">56dp</dimen>
    <dimen name="profile_menu_item_padding">16dp</dimen>
    <dimen name="profile_menu_icon_size">24dp</dimen>
    <dimen name="profile_menu_text_size">16sp</dimen>
    <dimen name="profile_menu_text_margin_start">16dp</dimen>
    <dimen name="profile_menu_arrow_size">16dp</dimen>
    <dimen name="profile_menu_margin_horizontal">16dp</dimen>
    <dimen name="profile_menu_margin_vertical">8dp</dimen>
    <dimen name="profile_menu_divider_height">1dp</dimen>
    <dimen name="profile_menu_divider_margin_start">16dp</dimen>

    <!-- Guidelines for responsive layout -->
    <dimen name="profile_content_margin_start">5dp</dimen>
    <dimen name="profile_content_margin_end">5dp</dimen>
    <dimen name="profile_section_spacing">16dp</dimen>

    <!-- Additional text sizes for consistency -->
    <dimen name="login_title_text_size">24sp</dimen>
    <dimen name="login_input_text_size">16sp</dimen>
    <dimen name="login_button_text_size">16sp</dimen>
    <dimen name="verification_subtitle_text_size">14sp</dimen>
    <dimen name="verification_otp_text_size">24sp</dimen>
    <dimen name="verification_otp_input_height">37.5dp</dimen>
    <dimen name="verification_otp_line_width">35dp</dimen>
    <dimen name="verification_otp_line_height">0.5dp</dimen>
    <dimen name="verification_otp_line_margin_top">12dp</dimen>

    <!-- Profile Status Bar Icon Sizes -->
    <dimen name="profile_status_mobile_signal_width">17dp</dimen>
    <dimen name="profile_status_mobile_signal_height">11dp</dimen>
    <dimen name="profile_status_wifi_width">15dp</dimen>
    <dimen name="profile_status_wifi_height">11dp</dimen>
    <dimen name="profile_status_battery_width">18dp</dimen>
    <dimen name="profile_status_battery_height">7dp</dimen>

    <!-- Profile Text Sizes -->
    <dimen name="profile_status_time_text_size">15sp</dimen>
    <dimen name="profile_username_text_size">20sp</dimen>
    <dimen name="profile_uid_text_size">14sp</dimen>
    <dimen name="profile_login_button_text_size">16sp</dimen>
    <dimen name="profile_vip_text_size">14sp</dimen>
    <dimen name="profile_vip_go_button_text_size">14sp</dimen>
    <dimen name="profile_expiration_date_text_size">10sp</dimen>

    <!-- Profile Small Margins -->
    <dimen name="profile_points_icon_margin_top">8dp</dimen>
    <dimen name="profile_points_value_margin_start">8dp</dimen>
    <dimen name="profile_expiration_date_margin_end">16dp</dimen>
    <dimen name="profile_expiration_date_margin_bottom">4dp</dimen>

    <!-- Profile Status Bar Icon Margins -->
    <dimen name="profile_status_mobile_signal_margin_end">50dp</dimen>
    <dimen name="profile_status_wifi_margin_end">30dp</dimen>

    <!-- Profile Menu Bottom Margin -->
    <dimen name="profile_menu_bottom_margin">100dp</dimen>
    <dimen name="profile_vip_card_type_use_width">62dp</dimen>
    <dimen name="profile_vip_card_type_use_height">44dp</dimen>
    <dimen name="profile_vip_card_type_use_margin_top">47dp</dimen>
    <dimen name="subscribe_privilege_margin">19dp</dimen>

    <!-- Subscribe Button -->
    <dimen name="subscribe_button_height">65dp</dimen>
    <dimen name="subscribe_button_corner_radius">32dp</dimen>
    <dimen name="subscribe_button_text_size">20sp</dimen>
    <dimen name="subscribe_button_margin">19dp</dimen>
    <!-- Subscribe Card Dimensions -->
    <dimen name="subscribe_card_margin_top">24dp</dimen>
    <dimen name="subscribe_card_title_text_size">18sp</dimen>
    <dimen name="subscribe_card_subtitle_text_size">14sp</dimen>
    <dimen name="subscribe_card_price_text_size">24sp</dimen>
    <dimen name="subscribe_card_period_text_size">14sp</dimen>
    <dimen name="subscribe_card_discount_text_size">12sp</dimen>
    <dimen name="subscribe_card_description_text_size">12sp</dimen>

    <!-- Subscribe Privileges Dimensions -->
    <dimen name="subscribe_privileges_margin_start">32dp</dimen>
    <dimen name="subscribe_privileges_margin_top">32dp</dimen>
    <dimen name="subscribe_privileges_icon_size">20dp</dimen>
    <dimen name="subscribe_privileges_text_size">16sp</dimen>
    <dimen name="subscribe_privileges_grid_margin_horizontal">16dp</dimen>
    <dimen name="subscribe_privileges_grid_margin_top">16dp</dimen>

    <!-- Subscribe Button Margins -->
    <dimen name="subscribe_button_margin_horizontal">16dp</dimen>
    <dimen name="subscribe_button_margin_top">32dp</dimen>

    <!-- Subscribe Page Unique Dimensions - Base values for 375dp reference -->
    <!-- Header Area -->
    <dimen name="subscribe_header_back_button_size">36dp</dimen>
    <dimen name="subscribe_header_back_button_margin_start">16dp</dimen>
    <dimen name="subscribe_header_back_button_margin_top">44dp</dimen>
    <dimen name="subscribe_header_redeem_code_margin_end">16dp</dimen>
    <dimen name="subscribe_header_redeem_code_margin_top">8dp</dimen>
    <dimen name="subscribe_header_redeem_icon_size">22dp</dimen>
    <dimen name="subscribe_header_redeem_text_size">14sp</dimen>
    <dimen name="subscribe_header_redeem_underline_width">75dp</dimen>
    <dimen name="subscribe_header_redeem_underline_height">0.5dp</dimen>
    <dimen name="subscribe_header_redeem_underline_spacing">2dp</dimen>

    <!-- Title Area -->
    <dimen name="subscribe_main_title_text_size">32sp</dimen>
    <dimen name="subscribe_main_title_margin_top">40dp</dimen>
    <dimen name="subscribe_subtitle_text_size">18sp</dimen>
    <dimen name="subscribe_subtitle_margin_top">30dp</dimen>
    <dimen name="subscribe_decoration_icon_size">16dp</dimen>
    <dimen name="subscribe_decoration_margin_horizontal">16dp</dimen>

    <!-- Tips and Privileges Section -->
    <dimen name="subscribe_tips_title_text_size">20sp</dimen>
    <dimen name="subscribe_privileges_title_text_size">16sp</dimen>
    <dimen name="subscribe_privileges_item_text_size">14sp</dimen>
    <dimen name="subscribe_privilege_card_text_size">12sp</dimen>
    <dimen name="subscribe_privilege_card_height">100dp</dimen>
    <dimen name="subscribe_privilege_card_padding">12dp</dimen>
    <dimen name="subscribe_privilege_icon_size">32dp</dimen>
    <dimen name="subscribe_privilege_icon_margin_bottom">0dp</dimen>
    <dimen name="subscribe_privilege_card_margin">3dp</dimen>
    <dimen name="subscribe_privilege_row_margin_bottom">16dp</dimen>

    <!-- 4 Privileges Title Section -->
    <dimen name="subscribe_privileges_title_margin_start">32dp</dimen>
    <dimen name="subscribe_privileges_title_margin_top">32dp</dimen>
    <dimen name="subscribe_privileges_crown_icon_size">24dp</dimen>
    <dimen name="subscribe_privileges_crown_padding">8dp</dimen>
    <dimen name="subscribe_privileges_section_title_text_size">18sp</dimen>
    <dimen name="subscribe_privileges_title_text_margin_start">8dp</dimen>

    <!-- Subscription Cards - Base dimensions for 375dp reference -->
    <dimen name="subscribe_cards_container_margin_horizontal">12dp</dimen>
    <dimen name="subscribe_cards_container_margin_top">24dp</dimen>
    <dimen name="subscribe_card_width">337dp</dimen>
    <dimen name="subscribe_card_height">128dp</dimen>
    <dimen name="subscribe_card_corner_radius">17dp</dimen>
    <dimen name="subscribe_card_margin_horizontal">12dp</dimen>
    <dimen name="subscribe_card_margin_vertical">8dp</dimen>
    <dimen name="subscribe_card_padding">15dp</dimen>
    <dimen name="subscribe_card_stroke_width">1dp</dimen>

    <!-- Card Price Text Sizes -->
    <dimen name="subscribe_card_price_main_text_size">24sp</dimen>
    <dimen name="subscribe_card_price_unit_text_size">16sp</dimen>

    <!-- VIP Card Border Frame - Base dimensions for 375dp reference -->
    <dimen name="vip_card_border_width">345dp</dimen>
    <dimen name="vip_card_border_height">136dp</dimen>
    <dimen name="vip_card_border_corner_radius">18dp</dimen>
    <dimen name="vip_card_border_stroke_width">1dp</dimen>
    <dimen name="vip_card_border_overflow">4dp</dimen>

    <!-- Points Page - Base dimensions for 375dp reference -->
    <!-- Header -->
    <dimen name="points_header_height">88dp</dimen>
    <dimen name="points_header_padding_horizontal">16dp</dimen>
    <dimen name="points_header_padding_top">16dp</dimen>
    <dimen name="points_back_button_size">36dp</dimen> <!-- 与订阅页面一致 -->
    <dimen name="points_title_text_size">18sp</dimen>
    <dimen name="points_title_margin_top">16dp</dimen> <!-- My points在返回箭头下方的间距 -->

    <!-- Tips部分 -->
    <dimen name="points_tips_margin_horizontal">16dp</dimen>
    <dimen name="points_tips_margin_bottom">20dp</dimen>
    <dimen name="points_tips_title_text_size">16sp</dimen>
    <dimen name="points_tips_title_margin_bottom">12dp</dimen>
    <dimen name="points_tips_subtitle_text_size">14sp</dimen>
    <dimen name="points_tips_subtitle_margin_bottom">8dp</dimen>
    <dimen name="points_tips_content_text_size">12sp</dimen>
    <dimen name="points_tips_content_line_spacing">4dp</dimen>

    <!-- 分割线 -->
    <dimen name="points_divider_margin_horizontal">16dp</dimen>

    <!-- 积分记录金额样式 -->
    <dimen name="points_record_amount_width">30dp</dimen>
    <dimen name="points_record_amount_height">21dp</dimen>
    <dimen name="points_record_amount_text_size">16sp</dimen>

    <!-- Expenses记录特定样式 -->
    <dimen name="expenses_record_title_width">179dp</dimen>
    <dimen name="expenses_record_title_height">19dp</dimen>
    <dimen name="expenses_record_title_text_size">16sp</dimen>
    <dimen name="expenses_record_subtitle_width">111dp</dimen>
    <dimen name="expenses_record_subtitle_height">17dp</dimen>
    <dimen name="expenses_record_subtitle_text_size">14sp</dimen>
    <dimen name="expenses_record_time_width">135dp</dimen>
    <dimen name="expenses_record_time_height">17dp</dimen>
    <dimen name="expenses_record_time_text_size">14sp</dimen>
    <dimen name="expenses_record_margin_start">16dp</dimen>

    <!-- VIP Privileges样式 -->
    <dimen name="vip_privileges_width">343dp</dimen>
    <dimen name="vip_privileges_height">216dp</dimen>
    <dimen name="vip_privileges_text_size">14sp</dimen>

    <!-- 积分记录列表 -->
    <dimen name="points_list_margin_horizontal">16dp</dimen>
    <dimen name="points_list_margin_bottom">20dp</dimen>
    <dimen name="points_item_padding">16dp</dimen>
    <dimen name="points_item_margin_bottom">12dp</dimen>
    <dimen name="points_record_padding">16dp</dimen>
    <dimen name="points_record_subtitle_margin_top">3dp</dimen>
    <dimen name="points_record_time_margin_top">3dp</dimen>
    <dimen name="points_item_title_text_size">16sp</dimen>
    <dimen name="points_item_date_text_size">14sp</dimen>
    <dimen name="points_item_date_margin_top">4dp</dimen>
    <dimen name="points_item_amount_text_size">14sp</dimen>
    <dimen name="points_item_coin_size">16dp</dimen>
    <dimen name="points_item_coin_margin">4dp</dimen>

    <!-- Balance Display -->
    <dimen name="points_balance_margin_top">32dp</dimen>
    <dimen name="points_balance_margin_bottom">32dp</dimen>
    <dimen name="points_coin_icon_size">32dp</dimen>
    <dimen name="points_coin_margin_end">8dp</dimen>
    <dimen name="points_balance_text_size">48sp</dimen>

    <!-- Points Cards Grid -->
    <dimen name="points_grid_margin_horizontal">16dp</dimen>
    <dimen name="points_grid_margin_bottom">24dp</dimen>
    <dimen name="points_card_width">166dp</dimen>
    <dimen name="points_card_height">78dp</dimen>
    <dimen name="points_card_margin">6dp</dimen>
    <dimen name="points_card_padding">12dp</dimen>
    <dimen name="points_card_corner_radius">8dp</dimen>
    <dimen name="points_card_stroke_width">1dp</dimen>
    <dimen name="points_card_selected_stroke_width">2dp</dimen>
    <dimen name="points_card_coin_size">16dp</dimen>
    <dimen name="points_card_coin_margin">4dp</dimen>
    <dimen name="points_card_main_text_size">16sp</dimen>
    <dimen name="points_card_bonus_text_size">16sp</dimen>
    <dimen name="points_card_bonus_margin">4dp</dimen>
    <dimen name="points_card_price_text_size">12sp</dimen>
    <dimen name="points_card_price_margin_top">4dp</dimen>

    <!-- Tabs -->
    <dimen name="points_tabs_margin_horizontal">16dp</dimen>
    <dimen name="points_tabs_margin_bottom">16dp</dimen>
    <dimen name="points_tab_text_size">16sp</dimen>
    <dimen name="points_tab_margin_end">24dp</dimen>
    <dimen name="points_tab_indicator_width">50dp</dimen>
    <dimen name="points_tab_indicator_height">3dp</dimen>
    <dimen name="points_tab_indicator_corner_radius">1dp</dimen>
    <dimen name="points_tab_obtain_indicator_margin">0dp</dimen>
    <dimen name="points_tab_expenses_indicator_margin">74dp</dimen>




    <dimen name="points_tips_item_margin_bottom">8dp</dimen>
    <dimen name="points_tips_bottom_margin">24dp</dimen>

    <!-- Refill Button -->
    <dimen name="points_refill_button_padding">16dp</dimen>
    <dimen name="points_refill_button_height">48dp</dimen>
    <dimen name="points_refill_button_text_size">16sp</dimen>
    <dimen name="points_refill_button_corner_radius">24dp</dimen>

    <!-- Bill Page - Base dimensions for 375dp reference -->
    <!-- Header -->
    <dimen name="bill_header_padding_horizontal">16dp</dimen>
    <dimen name="bill_header_padding_top">58dp</dimen>
    <dimen name="bill_back_button_size">36dp</dimen> <!-- 与订阅页面一致 -->
    <dimen name="bill_back_button_margin_top">0dp</dimen>
    <dimen name="bill_back_button_margin_start">16dp</dimen>
    <dimen name="bill_title_width">27dp</dimen>
    <dimen name="bill_title_height">21dp</dimen>
    <dimen name="bill_title_margin_top">-3dp</dimen>
    <dimen name="bill_title_text_size">18sp</dimen>

    <!-- Tabs -->
    <dimen name="bill_tabs_margin_horizontal">16dp</dimen> <!-- 距离左右16dp -->
    <dimen name="bill_tabs_margin_top">40dp</dimen>
    <dimen name="bill_tabs_background_width">343dp</dimen> <!-- 屏幕宽度375-16*2=343dp -->
    <dimen name="bill_tabs_background_height">45dp</dimen>
    <dimen name="bill_tabs_background_corner_radius">20dp</dimen>
    <dimen name="bill_selected_tab_width">109dp</dimen> <!-- (343-6)/3=112.33，调整为109dp确保居中 -->
    <dimen name="bill_selected_tab_height">39dp</dimen>
    <dimen name="bill_selected_tab_margin_start">3dp</dimen> <!-- 选中背景距离容器边缘3dp -->
    <dimen name="bill_selected_tab_margin_top">3dp</dimen>
    <dimen name="bill_selected_tab_corner_radius">17dp</dimen>
    <dimen name="bill_tab_text_size">16sp</dimen>

    <!-- Content -->
    <dimen name="bill_content_margin_top">24dp</dimen>

    <!-- Empty State -->
    <dimen name="bill_empty_image_width">286dp</dimen>
    <dimen name="bill_empty_image_height">258dp</dimen>
    <dimen name="bill_empty_image_margin_horizontal">44.5dp</dimen> <!-- 左右各44.5dp -->
    <dimen name="bill_empty_image_margin_bottom">216dp</dimen> <!-- 距离底部216dp -->
    <dimen name="bill_empty_text_margin_top">16dp</dimen>
    <dimen name="bill_empty_text_size">14sp</dimen>

    <!-- Banner Area -->
    <dimen name="home_banner_height">500dp</dimen>
    <dimen name="home_logo_width">114dp</dimen>
    <dimen name="home_logo_height">24dp</dimen>
    <dimen name="home_logo_margin_top">54dp</dimen>
    <dimen name="home_logo_margin_start">16dp</dimen>

    <!-- Search Icon -->
    <dimen name="home_search_icon_size">22dp</dimen>
    <dimen name="home_search_icon_margin_top">54dp</dimen>
    <dimen name="home_search_icon_margin_end">16dp</dimen>

    <!-- Search Page Dimensions Base values for 375dp reference -->

    <!-- Top shadow background -->
    <dimen name="search_top_shadow_height">160dp</dimen>

    <!-- Back button -->
    <dimen name="search_back_button_size">18dp</dimen>
    <dimen name="search_back_button_margin_start">16dp</dimen>
    <dimen name="search_back_button_margin_top">54dp</dimen>

    <!-- Search input -->
    <dimen name="search_input_width">307dp</dimen>
    <dimen name="search_input_height">38dp</dimen>
    <dimen name="search_input_margin_start">52dp</dimen>
    <dimen name="search_input_margin_top">47dp</dimen>
    <dimen name="search_input_corner_radius">12dp</dimen>
    <dimen name="search_input_padding_start">12dp</dimen>
    <dimen name="search_input_padding_vertical">8dp</dimen>

    <!-- Search icon in input -->
    <dimen name="search_input_icon_size">16dp</dimen>
    <dimen name="search_input_icon_margin_start">12dp</dimen>

    <!-- Search text -->
    <dimen name="search_text_size">16sp</dimen>
    <dimen name="search_text_margin_start">98dp</dimen>
    <dimen name="search_text_margin_top">56dp</dimen>

    <!-- Recent searches section -->
    <dimen name="search_recent_title_margin_start">16dp</dimen>
    <dimen name="search_recent_title_margin_top">104dp</dimen>
    <dimen name="search_recent_title_size">18sp</dimen>

    <!-- Clear recent searches icon -->
    <dimen name="search_clear_icon_size">18dp</dimen>
    <dimen name="search_clear_icon_margin_end">16dp</dimen>
    <dimen name="search_clear_icon_margin_top">105dp</dimen>

    <!-- Recent search tags -->
    <dimen name="search_recent_tag_height">32dp</dimen>
    <dimen name="search_recent_tag_padding_horizontal">12dp</dimen>
    <dimen name="search_recent_tag_padding_vertical">6dp</dimen>
    <dimen name="search_recent_tag_margin">4dp</dimen>
    <dimen name="search_recent_tag_text_size">14sp</dimen>
    <dimen name="search_recent_tag_corner_radius">16dp</dimen>

    <!-- Trending searches section -->
    <dimen name="search_trending_title_size">18sp</dimen>
    <dimen name="search_trending_title_margin_start">16dp</dimen>
    <dimen name="search_trending_title_margin_top">12dp</dimen>

    <!-- Trending search cards -->
    <dimen name="search_trending_card_width">343dp</dimen>
    <dimen name="search_trending_card_height">96dp</dimen>
    <dimen name="search_trending_card_margin_horizontal">16dp</dimen>
    <dimen name="search_trending_card_margin_vertical">6dp</dimen>
    <dimen name="search_trending_card_corner_radius">12dp</dimen>
    <dimen name="search_trending_card_padding">14dp</dimen>

    <!-- Rank background -->
    <dimen name="search_rank_bg_size">28dp</dimen>
    <dimen name="search_rank_bg_corner_radius">6dp</dimen>
    <dimen name="search_rank_text_size">16sp</dimen>

    <!-- Poster in trending card -->
    <dimen name="search_poster_size">68dp</dimen>
    <dimen name="search_poster_corner_radius">8dp</dimen>
    <dimen name="search_poster_margin_start">10dp</dimen>

    <!-- Content in trending card -->
    <dimen name="search_content_margin_start">10dp</dimen>
    <dimen name="search_title_text_size">14sp</dimen>
    <dimen name="search_count_text_size">12sp</dimen>
    <dimen name="search_count_icon_size">16dp</dimen>
    <dimen name="search_count_icon_margin_end">4dp</dimen>
    <dimen name="search_count_margin_top">23dp</dimen>

    <!-- Search page bottom spacing -->
    <dimen name="search_bottom_margin">16dp</dimen>
    <dimen name="search_bottom_padding">16dp</dimen>

    <!-- Carousel -->
    <dimen name="home_carousel_unselected_width">50dp</dimen>
    <dimen name="home_carousel_unselected_height">66.5dp</dimen>
    <dimen name="home_carousel_selected_width">62dp</dimen>
    <dimen name="home_carousel_selected_height">82.5dp</dimen>
    <dimen name="home_carousel_margin_bottom">17dp</dimen>
    <dimen name="home_carousel_margin_start">16dp</dimen>
    <dimen name="home_carousel_item_spacing">8dp</dimen>

    <!-- Play Icon -->
    <dimen name="home_play_icon_size">38dp</dimen>
    <dimen name="home_play_icon_margin_bottom">14dp</dimen>
    <dimen name="home_play_icon_margin_end">16dp</dimen>

    <!-- Gradient -->
    <dimen name="home_gradient_height">49dp</dimen>
    <dimen name="home_top_gradient_height">88dp</dimen>

    <!-- Categories Section -->
    <dimen name="home_categories_margin_top">2dp</dimen>
    <dimen name="home_categories_margin_start">16dp</dimen>
    <dimen name="home_categories_text_size">18sp</dimen>
    <dimen name="home_see_all_text_size">16sp</dimen>
    <dimen name="home_see_all_margin_end">16dp</dimen>
    <dimen name="home_next_icon_size">8dp</dimen>
    <dimen name="home_next_icon_margin">10dp</dimen>

    <!-- Tags -->
    <dimen name="home_tag_height">36dp</dimen>
    <dimen name="home_tag_corner_radius">14dp</dimen>
    <dimen name="home_tag_text_size">14sp</dimen>
    <dimen name="home_tag_padding_horizontal">16dp</dimen>
    <dimen name="home_tag_padding_vertical">10dp</dimen>
    <dimen name="home_tag_margin_top">14dp</dimen>

    <!-- Video Grid -->
    <dimen name="home_video_item_width">169dp</dimen>
    <dimen name="home_video_item_height">227dp</dimen>
    <dimen name="home_video_item_spacing_horizontal">16dp</dimen>
    <dimen name="home_video_item_spacing_vertical">12dp</dimen>
    <dimen name="home_video_title_margin_bottom">13dp</dimen>
    <dimen name="home_video_title_margin_start">12dp</dimen>
    <dimen name="home_video_title_text_size">14sp</dimen>
    <dimen name="home_video_description_text_size">14sp</dimen>
    <dimen name="home_video_description_margin_top">8dp</dimen>

    <!-- Standard Video Item (for Continue Watching, Coming Soon, Trending Now, Today's Hot) -->
    <dimen name="home_standard_video_item_width">132dp</dimen>
    <dimen name="home_standard_video_item_height">176dp</dimen>
    <dimen name="home_standard_video_item_corner_radius">12dp</dimen>
    <dimen name="home_standard_video_item_border_width">0.5dp</dimen>

    <!-- Large Video Item (for Best For You) -->
    <dimen name="home_large_video_item_width">164dp</dimen>
    <dimen name="home_large_video_item_height">218dp</dimen>
    <dimen name="home_large_video_item_corner_radius">12dp</dimen>
    <dimen name="home_large_video_item_border_width">0.55dp</dimen>

    <!-- Continue Watching Section -->
    <dimen name="home_continue_watching_margin_top">40dp</dimen>
    <dimen name="home_continue_watching_item_width">132dp</dimen>
    <dimen name="home_continue_watching_item_height">176dp</dimen>
    <dimen name="home_continue_watching_item_corner_radius">12dp</dimen>
    <dimen name="home_continue_watching_item_border_width">0.44dp</dimen>
    <dimen name="home_continue_watching_title_margin_top">8dp</dimen>
    <dimen name="home_continue_watching_progress_margin_top">5.5dp</dimen>
    <dimen name="home_continue_watching_progress_text_size">14sp</dimen>
    <dimen name="home_continue_watching_list_margin_top">18dp</dimen>

    <!-- Redemption Code Dialog - Base dimensions for 375dp reference (24dp margin each side) -->
    <dimen name="redeem_dialog_width">327dp</dimen>
    <dimen name="redeem_dialog_height">266dp</dimen>
    <dimen name="redeem_dialog_corner_radius">16dp</dimen>
    <dimen name="redeem_dialog_title_text_size">18sp</dimen>
    <dimen name="redeem_dialog_title_line_height">21dp</dimen>
    <dimen name="redeem_dialog_description_text_size">14sp</dimen>
    <dimen name="redeem_dialog_input_width">295dp</dimen>
    <dimen name="redeem_dialog_input_height">54dp</dimen>
    <dimen name="redeem_dialog_input_corner_radius">12dp</dimen>
    <dimen name="redeem_dialog_input_stroke_width">1dp</dimen>
    <dimen name="redeem_dialog_button_width">295dp</dimen>
    <dimen name="redeem_dialog_button_height">42dp</dimen>
    <dimen name="redeem_dialog_button_corner_radius">21dp</dimen>
    <dimen name="redeem_dialog_button_text_size">16sp</dimen>
    <dimen name="redeem_dialog_button_line_height">23dp</dimen>

    <!-- Additional Redemption Dialog dimensions - New responsive dimensions -->
    <dimen name="redeem_dialog_padding">16dp</dimen>
    <dimen name="redeem_dialog_title_margin_top">6dp</dimen>
    <dimen name="redeem_dialog_description_margin_top">10dp</dimen>
    <dimen name="redeem_dialog_line_spacing">2dp</dimen>
    <dimen name="redeem_dialog_element_margin_top">16dp</dimen>
    <dimen name="redeem_dialog_input_padding_horizontal">14dp</dimen>

    <!-- Payment Method Dialog - Base dimensions for 375dp reference -->
    <dimen name="payment_dialog_width">320dp</dimen>
    <dimen name="payment_dialog_padding">24dp</dimen>
    <dimen name="payment_dialog_title_text_size">18sp</dimen>
    <dimen name="payment_dialog_option_height">48dp</dimen>
    <dimen name="payment_dialog_option_padding_horizontal">16dp</dimen>
    <dimen name="payment_dialog_option_text_size">16sp</dimen>
    <dimen name="payment_dialog_icon_size">24dp</dimen>
    <dimen name="payment_dialog_icon_margin_end">12dp</dimen>
    <dimen name="payment_dialog_options_margin_top">20dp</dimen>
    <dimen name="payment_dialog_divider_height">0.5dp</dimen>
    <dimen name="payment_dialog_divider_margin_horizontal">16dp</dimen>

    <!-- New Home Modules Dimensions - Base values for 375dp reference -->

    <!-- Notify/Subscribe Button -->
    <dimen name="home_notify_button_width">130dp</dimen>
    <dimen name="home_notify_button_height">27dp</dimen>
    <dimen name="home_notify_button_corner_radius">8dp</dimen>
    <dimen name="home_notify_button_text_size">12sp</dimen>
    <dimen name="home_notify_button_margin_top">8dp</dimen>
    <dimen name="home_notify_button_margin_end">8dp</dimen>

    <!-- Match Percentage Label -->
    <dimen name="home_match_percentage_width">35dp</dimen>
    <dimen name="home_match_percentage_height">19dp</dimen>
    <dimen name="home_match_percentage_corner_radius">6dp</dimen>
    <dimen name="home_match_percentage_text_size">10sp</dimen>
    <dimen name="home_match_percentage_margin_start">8dp</dimen>
    <dimen name="home_match_percentage_margin_bottom">8dp</dimen>

    <!-- Ranking Label -->
    <dimen name="home_ranking_width">38dp</dimen>
    <dimen name="home_ranking_height">38dp</dimen>
    <dimen name="home_ranking_corner_radius">6dp</dimen>
    <dimen name="home_ranking_text_size">14sp</dimen>
    <dimen name="home_ranking_margin_start">8dp</dimen>
    <dimen name="home_ranking_margin_top">8dp</dimen>

    <!-- Best For You Item -->
    <dimen name="home_best_for_you_item_width">164dp</dimen>
    <dimen name="home_best_for_you_item_height">218.5dp</dimen>
    <dimen name="home_best_for_you_item_corner_radius">12dp</dimen>
    <dimen name="home_best_for_you_item_border_width">0.55dp</dimen>
    <dimen name="home_best_for_you_match_margin_top">13dp</dimen>
    <dimen name="home_best_for_you_match_margin_end">15dp</dimen>
    <dimen name="home_best_for_you_title_margin_top">10dp</dimen>
    <dimen name="home_best_for_you_reason_margin_top">5dp</dimen>

    <!-- Popular Series Item -->
    <dimen name="home_popular_series_item_width">112dp</dimen>
    <dimen name="home_popular_series_item_height">149.5dp</dimen>
    <dimen name="home_popular_series_item_corner_radius">12dp</dimen>
    <dimen name="home_popular_series_item_border_width">0.38dp</dimen>
    <dimen name="home_popular_series_heat_margin_bottom">10dp</dimen>
    <dimen name="home_popular_series_heat_margin_end">12dp</dimen>

    <!-- Coming Soon Detail Page Item (增加8dp宽度，12dp高度) -->
    <dimen name="coming_soon_detail_item_width">120dp</dimen>
    <dimen name="coming_soon_detail_item_height">161.5dp</dimen>

    <!-- Play Count Text -->
    <dimen name="home_play_count_text_size">12sp</dimen>
    <dimen name="home_play_count_margin_bottom">8dp</dimen>
    <dimen name="home_play_count_margin_end">8dp</dimen>

    <!-- Most Popular Page Specific -->
    <dimen name="most_popular_video_title_text_size">16sp</dimen>
    <dimen name="most_popular_title_text_size">16sp</dimen>
    <dimen name="most_popular_poster_width">112dp</dimen>
    <dimen name="most_popular_poster_height">149.5dp</dimen>
    <dimen name="most_popular_poster_margin_start">16dp</dimen>
    <dimen name="most_popular_info_margin_start">14dp</dimen>
    <dimen name="most_popular_info_margin_end">16dp</dimen>
    <dimen name="most_popular_episode_text_size">14sp</dimen>
    <dimen name="most_popular_synopsis_text_size">14sp</dimen>

    <!-- Module Section Spacing -->
    <dimen name="home_module_section_margin_top">32dp</dimen>
    <dimen name="home_module_title_text_size">18sp</dimen>
    <dimen name="home_module_title_margin_top">24dp</dimen>
    <dimen name="home_module_see_all_text_size">16sp</dimen>
    <dimen name="home_module_list_margin_top">16dp</dimen>

    <!-- VIP Background Image - Base dimensions for 375dp reference -->
    <dimen name="vip_bg_width">375dp</dimen>
    <dimen name="vip_bg_height">198dp</dimen>
    <dimen name="vip_bg_margin_top">30dp</dimen>

    <!-- Discount Badge - Base dimensions for 375dp reference -->
    <dimen name="discount_badge_width">88dp</dimen>
    <dimen name="discount_badge_height">20dp</dimen>
    <dimen name="discount_badge_text_size">13sp</dimen>
    <dimen name="discount_badge_padding_horizontal">8dp</dimen>

    <!-- VIP Record Card - Base dimensions for 375dp reference -->
    <dimen name="vip_record_card_width">343dp</dimen>
    <dimen name="vip_record_card_height">79dp</dimen>
    <dimen name="vip_record_card_margin_bottom">12dp</dimen>
    <dimen name="vip_record_card_padding">16dp</dimen>
    <dimen name="vip_record_first_card_margin_top">22dp</dimen>

    <!-- VIP Record Text Sizes -->
    <dimen name="vip_record_title_text_size">16sp</dimen>
    <dimen name="vip_record_price_text_size">16sp</dimen>
    <dimen name="vip_record_date_text_size">14sp</dimen>
    <dimen name="vip_record_payment_text_size">14sp</dimen>

    <!-- VIP Record Payment Icon -->
    <dimen name="vip_record_payment_icon_size">16dp</dimen>
    <dimen name="vip_record_payment_icon_margin_end">4dp</dimen>

    <!-- Points Purchase Card - Base dimensions for 375dp reference -->
    <dimen name="points_purchase_card_height">99dp</dimen>
    <dimen name="points_purchase_card_margin_bottom">9dp</dimen>
    <dimen name="points_purchase_card_padding">16dp</dimen>
    <dimen name="points_purchase_first_card_margin_top">18dp</dimen>

    <!-- Points Purchase Text Sizes -->
    <dimen name="points_purchase_title_text_size">16sp</dimen>
    <dimen name="points_purchase_price_text_size">16sp</dimen>
    <dimen name="points_purchase_date_text_size">14sp</dimen>
    <dimen name="points_purchase_points_text_size">14sp</dimen>
    <dimen name="points_purchase_bonus_text_size">14sp</dimen>

    <!-- Points Purchase Icon -->
    <dimen name="points_purchase_icon_size">16dp</dimen>
    <dimen name="points_purchase_icon_margin_end">4dp</dimen>
    <dimen name="points_purchase_bonus_margin_start">4dp</dimen>

    <!-- Video Record Card - Base dimensions for 375dp reference -->
    <dimen name="video_record_card_height">125dp</dimen>
    <dimen name="video_record_card_margin_bottom">12dp</dimen>
    <dimen name="video_record_card_padding">16dp</dimen>
    <dimen name="video_record_first_card_margin_top">18dp</dimen>

    <!-- Video Record Poster -->
    <dimen name="video_record_poster_width">74dp</dimen>
    <dimen name="video_record_poster_height">99dp</dimen>
    <dimen name="video_record_content_margin_start">16dp</dimen>

    <!-- Video Record Text Sizes -->
    <dimen name="video_record_title_text_size">16sp</dimen>
    <dimen name="video_record_cost_text_size">16sp</dimen>
    <dimen name="video_record_episode_text_size">14sp</dimen>
    <dimen name="video_record_description_text_size">14sp</dimen>
    <dimen name="video_record_date_text_size">14sp</dimen>

    <!-- Video Record Episode -->
    <dimen name="video_record_episode_width">27dp</dimen>
    <dimen name="video_record_episode_height">17dp</dimen>

    <!-- Video Record Cost Icon -->
    <dimen name="video_record_cost_icon_size">16dp</dimen>
    <dimen name="video_record_cost_icon_margin_start">4dp</dimen>

    <!-- Message Page - Base dimensions for 375dp reference -->
    <dimen name="message_first_card_margin_top">24dp</dimen>
    <dimen name="message_card_margin_bottom">16dp</dimen>
    <dimen name="message_card_padding">16dp</dimen>

    <!-- Message Card Sizes -->
    <dimen name="message_card_with_video_height">240dp</dimen>
    <dimen name="message_card_without_video_height">120dp</dimen>

    <!-- Message Notification Icon -->
    <dimen name="message_notification_icon_size">38dp</dimen>
    <dimen name="message_notification_icon_margin_end">12dp</dimen>
    <dimen name="message_unread_dot_size">7dp</dimen>

    <!-- Message Text Sizes -->
    <dimen name="message_title_text_size">16sp</dimen>
    <dimen name="message_date_text_size">14sp</dimen>
    <dimen name="message_content_text_size">14sp</dimen>
    <dimen name="message_video_title_text_size">16sp</dimen>
    <dimen name="message_episode_text_size">14sp</dimen>

    <!-- Message Go Button -->
    <dimen name="message_go_button_width">62dp</dimen>
    <dimen name="message_go_button_height">28dp</dimen>
    <dimen name="message_go_button_text_size">14sp</dimen>

    <!-- Message Video Poster -->
    <dimen name="message_video_poster_width">74dp</dimen>
    <dimen name="message_video_poster_height">99dp</dimen>
    <dimen name="message_video_content_margin_start">16dp</dimen>

    <!-- Continue Playing Button (Base dimensions for sw360dp) -->
    <dimen name="continue_playing_button_width">343dp</dimen>
    <dimen name="continue_playing_button_height">42dp</dimen>
    <dimen name="continue_playing_button_corner_radius">21dp</dimen>
    <dimen name="continue_playing_button_margin_bottom">20dp</dimen>
    <dimen name="continue_playing_button_text_size">16sp</dimen>

    <!-- Video Player Dimensions -->
    <!-- Top Control Bar -->
    <dimen name="video_player_back_button_size">24dp</dimen>
    <dimen name="video_player_control_button_size">32dp</dimen>
    <dimen name="video_player_control_button_corner_radius">10dp</dimen>
    <dimen name="video_player_control_button_margin_end">16dp</dimen>
    <dimen name="video_player_control_button_spacing">8dp</dimen>
    <dimen name="video_player_control_icon_size">22dp</dimen>

    <!-- Video Area -->
    <dimen name="video_player_area_height">670dp</dimen>

    <!-- Play Controls -->
    <dimen name="video_player_seek_button_size">32dp</dimen>
    <dimen name="video_player_seek_button_margin_horizontal">80dp</dimen>
    <dimen name="video_player_play_button_size">55dp</dimen>

    <!-- Side Controls -->
    <dimen name="video_player_side_button_size">22dp</dimen>
    <dimen name="video_player_side_button_margin_end">18dp</dimen>
    <dimen name="video_player_side_button_spacing">2dp</dimen>
    <dimen name="video_player_side_text_size">12sp</dimen>
    <dimen name="video_player_side_button_vertical_spacing">28dp</dimen>
    <dimen name="video_player_like_button_margin_bottom">64dp</dimen>

    <!-- Progress Bar -->
    <dimen name="video_player_progress_corner_radius">1.5dp</dimen>
    <dimen name="video_player_progress_margin_horizontal">16dp</dimen>

    <!-- Video Info -->
    <dimen name="video_player_info_margin_top">10dp</dimen>
    <dimen name="video_player_title_text_size">23sp</dimen>
    <dimen name="video_player_title_icon_size">18dp</dimen>
    <dimen name="video_player_title_icon_margin_start">8dp</dimen>
    <dimen name="video_player_description_text_size">13sp</dimen>
    <dimen name="video_player_description_margin_top">7dp</dimen>

    <!-- Bottom Controls -->
    <dimen name="video_player_bottom_controls_margin_top">10dp</dimen>
    <dimen name="video_player_episode_button_width">92dp</dimen>
    <dimen name="video_player_episode_button_height">36dp</dimen>
    <dimen name="video_player_episode_button_corner_radius">8dp</dimen>
    <dimen name="video_player_episode_text_size">14sp</dimen>
    <dimen name="video_player_episode_icon_size">18dp</dimen>
    <dimen name="video_player_episode_padding_start">10dp</dimen>
    <dimen name="video_player_episode_padding_end">14dp</dimen>

    <dimen name="video_player_control_small_button_size">57dp</dimen>
    <dimen name="video_player_control_small_icon_size">26dp</dimen>
    <dimen name="video_player_control_button_spacing_small">6dp</dimen>

    <dimen name="video_player_fullscreen_button_size">36dp</dimen>
    <dimen name="video_player_fullscreen_button_width">36dp</dimen>
    <dimen name="video_player_fullscreen_button_height">36dp</dimen>
    <dimen name="video_player_fullscreen_icon_size">26dp</dimen>
    <dimen name="video_player_danmaku_button_width">57dp</dimen>
    <dimen name="video_player_danmaku_button_height">36dp</dimen>
    <dimen name="video_player_danmaku_icon_size">26dp</dimen>

    <!-- Speed/Quality Selection Popup -->
    <dimen name="video_player_popup_width">53dp</dimen>
    <dimen name="video_player_popup_height">194dp</dimen>
    <dimen name="video_player_popup_item_height">32dp</dimen>
    <dimen name="video_player_popup_corner_radius">8dp</dimen>
    <dimen name="video_player_popup_text_size">14sp</dimen>

    <!-- Quality Selection Popup -->
    <dimen name="video_player_quality_popup_width">73dp</dimen>
    <dimen name="video_player_quality_popup_height">135dp</dimen>
    <dimen name="video_player_quality_popup_divider_width">65dp</dimen>
    <dimen name="video_player_quality_popup_divider_height">1dp</dimen>
    <dimen name="video_player_quality_vip_icon_width">13dp</dimen>
    <dimen name="video_player_quality_vip_icon_height">12dp</dimen>

    <!-- Speed Selection Popup -->
    <dimen name="video_player_speed_popup_divider_width">48dp</dimen>
    <dimen name="video_player_speed_popup_divider_height">1dp</dimen>
    <dimen name="video_player_speed_popup_offset">12dp</dimen>

    <!-- Subtitle Panel Dimensions -->
    <dimen name="subtitle_panel_height">200dp</dimen>
    <dimen name="subtitle_panel_corner_radius_top">24dp</dimen>
    <dimen name="subtitle_panel_close_button_size">18dp</dimen>
    <dimen name="subtitle_panel_close_button_margin_top">24dp</dimen>
    <dimen name="subtitle_panel_close_button_margin_end">16dp</dimen>
    <dimen name="subtitle_panel_title_margin_top">24dp</dimen>
    <dimen name="subtitle_panel_title_text_size">18sp</dimen>
    <dimen name="subtitle_panel_subtitle_margin_top">16dp</dimen>
    <dimen name="subtitle_panel_subtitle_text_size">16sp</dimen>
    <dimen name="subtitle_panel_switch_margin_top">7dp</dimen>
    <dimen name="subtitle_panel_switch_size">36dp</dimen>
    <dimen name="subtitle_panel_language_margin_top">28dp</dimen>
    <dimen name="subtitle_panel_language_item_margin_top">16dp</dimen>
    <dimen name="subtitle_panel_language_width">310dp</dimen>
    <dimen name="subtitle_panel_language_height">42dp</dimen>
    <dimen name="subtitle_panel_language_corner_radius">14dp</dimen>
    <dimen name="subtitle_panel_language_text_size">16sp</dimen>
    <dimen name="subtitle_panel_language_icon_size">18dp</dimen>
    <dimen name="subtitle_panel_language_icon_margin_start">12dp</dimen>

    <!-- Episode Selection Popup Dimensions -->
    <dimen name="episode_selection_popup_height">420dp</dimen>
    <dimen name="episode_selection_popup_corner_radius">24dp</dimen>
    <dimen name="episode_selection_close_button_size">18dp</dimen>
    <dimen name="episode_selection_close_button_margin_top">18dp</dimen>
    <dimen name="episode_selection_close_button_margin_end">16dp</dimen>
    <dimen name="episode_selection_title_margin_top">18dp</dimen>
    <dimen name="episode_selection_title_text_size">18sp</dimen>
    <dimen name="episode_selection_range_margin_top">18dp</dimen>
    <dimen name="episode_selection_divider_width">343dp</dimen>
    <dimen name="episode_selection_divider_height">0.5dp</dimen>
    <dimen name="episode_selection_episodes_margin_top">18dp</dimen>
    <dimen name="episode_selection_episodes_padding_horizontal">16dp</dimen>

    <!-- Video Detail Episode Tag Dimensions -->
    <dimen name="video_detail_episode_tag_width">48dp</dimen>
    <dimen name="video_detail_episode_tag_height">48dp</dimen>
    <dimen name="video_detail_episode_tag_spacing">8dp</dimen>
    <dimen name="video_detail_episode_text_size">14sp</dimen>
    <dimen name="video_detail_episode_icon_size">18dp</dimen>
    <dimen name="video_detail_episode_icon_margin_start">4dp</dimen>

    <!-- Message Detail Page - Base dimensions for 375dp reference -->
    <dimen name="message_detail_card_width">343dp</dimen>
    <dimen name="message_detail_card_min_height">400dp</dimen>
    <dimen name="message_detail_card_margin_top">16dp</dimen>
    <dimen name="message_detail_divider_width">319dp</dimen>
    <dimen name="message_detail_divider_height">0.5dp</dimen>
    <dimen name="message_detail_divider_margin_vertical">10dp</dimen>

    <!-- Video Poster Popup Dimensions - Base values for 360dp reference -->
    <dimen name="poster_popup_margin_top">140dp</dimen>
    <dimen name="poster_popup_width">298dp</dimen>
    <dimen name="poster_popup_height">466dp</dimen>
    <dimen name="poster_popup_corner_radius">18dp</dimen>
    <dimen name="poster_popup_close_button_size">30dp</dimen>
    <dimen name="poster_popup_close_button_margin_top">16dp</dimen>

    <!-- VIP Unlock Popup Dimensions - Base values for 360dp reference -->
    <dimen name="vip_unlock_popup_margin_top">240dp</dimen>
    <dimen name="vip_unlock_message_width">270dp</dimen>
    <dimen name="vip_unlock_message_text_size">20sp</dimen>
    <dimen name="vip_unlock_message_line_spacing">4dp</dimen>
    <dimen name="vip_unlock_button_width">294dp</dimen>
    <dimen name="vip_unlock_button_height">62dp</dimen>
    <dimen name="vip_unlock_button_corner_radius">18dp</dimen>
    <dimen name="vip_unlock_button_text_size">18sp</dimen>
    <dimen name="vip_unlock_button_margin_top">29dp</dimen>
    <dimen name="vip_unlock_button_spacing">16dp</dimen>
    <dimen name="vip_unlock_coin_icon_size">22dp</dimen>
    <dimen name="vip_unlock_coin_icon_margin_start">10dp</dimen>
    <dimen name="vip_unlock_coin_text_margin_start">4dp</dimen>
    <dimen name="vip_unlock_coin_text_size">20sp</dimen>
    <dimen name="vip_unlock_close_button_size">30dp</dimen>
    <dimen name="vip_unlock_close_button_margin_top">44dp</dimen>

    <!-- Watch AD Popup Dimensions - Base values for 360dp reference -->
    <dimen name="watch_ad_popup_width">294dp</dimen>
    <dimen name="watch_ad_popup_height">62dp</dimen>
    <dimen name="watch_ad_popup_corner_radius">18dp</dimen>
    <dimen name="watch_ad_popup_text_size">14sp</dimen>
    <dimen name="watch_ad_popup_coin_text_size">20sp</dimen>
    <dimen name="watch_ad_popup_coin_icon_size">22dp</dimen>
    <dimen name="watch_ad_popup_coin_margin_start">4dp</dimen>
    <dimen name="watch_ad_popup_text_margin_top">3dp</dimen>
    <dimen name="watch_ad_popup_padding_vertical">8dp</dimen>

    <!-- Additional Video Player Dimensions -->
    <dimen name="video_player_bottom_margin">16dp</dimen>
    <dimen name="video_player_side_margin">16dp</dimen>
    <dimen name="video_player_info_margin_bottom">16dp</dimen>
    <dimen name="video_player_info_margin_end">16dp</dimen>
    <dimen name="video_player_info_margin_start">16dp</dimen>
    <dimen name="video_player_control_row_margin_top">16dp</dimen>
    <dimen name="video_player_episode_button_padding">12dp</dimen>
    <dimen name="video_player_control_icon_margin">8dp</dimen>
    <dimen name="video_player_small_button_padding">8dp</dimen>
    <dimen name="video_player_progress_margin_top">10dp</dimen>
    <dimen name="video_player_loading_size">48dp</dimen>
    <dimen name="video_player_buffering_size">32dp</dimen>
    <dimen name="video_player_debug_text_size">12sp</dimen>

    <!-- Error State Dimensions -->
    <dimen name="video_player_error_icon_size">48dp</dimen>
    <dimen name="video_player_error_text_size">16sp</dimen>
    <dimen name="video_player_error_text_margin_top">16dp</dimen>
    <dimen name="video_player_error_button_padding_vertical">12dp</dimen>
    <dimen name="video_player_error_button_padding_horizontal">24dp</dimen>
    <dimen name="video_player_error_button_margin_top">16dp</dimen>

    <!-- Buffering State Dimensions -->
    <dimen name="video_player_buffering_text_size">14sp</dimen>
    <dimen name="video_player_buffering_text_margin_top">12dp</dimen>

    <!-- Debug Info Dimensions -->
    <dimen name="video_player_debug_info_padding">8dp</dimen>
    <dimen name="video_player_debug_info_margin">8dp</dimen>

    <!-- Interest Tab Card Dimensions - Base values for 360dp reference (缩放因子: 1.0) -->
    <!-- Card Layout -->
    <dimen name="interest_card_margin_horizontal">12dp</dimen>
    <dimen name="interest_card_margin_bottom">18dp</dimen>

    <!-- Poster - 放大尺寸 -->
    <dimen name="interest_poster_width">120dp</dimen>
    <dimen name="interest_poster_height">160dp</dimen>

    <!-- Content Area -->
    <dimen name="interest_content_margin_start">14dp</dimen>

    <!-- Title -->
    <dimen name="interest_title_width">200dp</dimen>
    <dimen name="interest_title_height">22dp</dimen>
    <dimen name="interest_title_text_size">16sp</dimen>

    <!-- Status Tag - 调整位置，确保不超过海报底部 -->
    <dimen name="interest_status_tag_width">88dp</dimen>
    <dimen name="interest_status_tag_height">22dp</dimen>
    <dimen name="interest_status_tag_margin_top">6dp</dimen>
    <dimen name="interest_status_text_width">76dp</dimen>
    <dimen name="interest_status_text_height">14dp</dimen>
    <dimen name="interest_status_text_size">12sp</dimen>

    <!-- Description - 优化文字大小 -->
    <dimen name="interest_description_width">200dp</dimen>
    <dimen name="interest_description_height">36dp</dimen>
    <dimen name="interest_description_margin_top">8dp</dimen>
    <dimen name="interest_description_text_size">12sp</dimen>

    <!-- Time Info -->
    <dimen name="interest_time_container_margin_top">12dp</dimen>
    <dimen name="interest_time_icon_size">14dp</dimen>
    <dimen name="interest_time_text_width">80dp</dimen>
    <dimen name="interest_time_text_height">18dp</dimen>
    <dimen name="interest_time_text_margin_start">4dp</dimen>
    <dimen name="interest_time_text_size">12sp</dimen>

    <!-- Cancel Button -->
    <dimen name="interest_cancel_button_width">180dp</dimen>
    <dimen name="interest_cancel_button_height">28dp</dimen>
    <dimen name="interest_cancel_button_margin_top">12dp</dimen>
    <dimen name="interest_cancel_icon_size">14dp</dimen>
    <dimen name="interest_cancel_text_width">80dp</dimen>
    <dimen name="interest_cancel_text_height">14dp</dimen>
    <dimen name="interest_cancel_text_margin_start">4dp</dimen>
    <dimen name="interest_cancel_text_size">12sp</dimen>

    <!-- New Video Player Style Dimensions -->
    <!-- Top Control Buttons -->
    <dimen name="video_player_new_back_button_size">24dp</dimen>
    <dimen name="video_player_new_download_button_size">32dp</dimen>
    <dimen name="video_player_new_download_button_corner_radius">10dp</dimen>
    <dimen name="video_player_new_download_button_margin_end">16dp</dimen>
    <dimen name="video_player_new_small_screen_button_margin_end">13dp</dimen>
    <dimen name="video_player_new_control_icon_size">22dp</dimen>

    <!-- Video Area -->
    <dimen name="video_player_new_area_height">670dp</dimen>

    <!-- Center Play Controls -->
    <dimen name="video_player_new_seek_backward_size">32dp</dimen>
    <dimen name="video_player_new_seek_backward_margin_start">80dp</dimen>
    <dimen name="video_player_new_play_pause_size">55dp</dimen>
    <dimen name="video_player_new_seek_forward_size">32dp</dimen>
    <dimen name="video_player_new_seek_forward_margin_end">80dp</dimen>

    <!-- Side Control Buttons -->
    <dimen name="video_player_new_like_button_size">22dp</dimen>
    <dimen name="video_player_new_like_button_margin_end">18dp</dimen>
    <dimen name="video_player_new_like_button_margin_bottom">64dp</dimen>
    <dimen name="video_player_new_side_text_size">13sp</dimen>
    <dimen name="video_player_new_side_button_spacing">2dp</dimen>
    <dimen name="video_player_new_side_vertical_spacing">28dp</dimen>

    <!-- Progress Bar -->
    <dimen name="video_player_new_progress_height">3dp</dimen>
    <dimen name="video_player_new_progress_corner_radius">1.5dp</dimen>
    <dimen name="video_player_new_progress_margin_horizontal">16dp</dimen>
    <dimen name="video_player_new_progress_margin_top">10dp</dimen>
    <!-- Video Player Fixed Values Converted to Responsive - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="video_player_side_button_container_width">60dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_seek_button_padding">8dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_seek_button_margin">50dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_episode_selection_width">112dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin">16dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin_small">8dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin_medium">10dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_padding">15dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_height">14dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_margin">5dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_margin_negative">-5dp</dimen> <!-- 基准值 -->

    <!-- Video Detail Fixed Values Converted to Responsive - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="video_detail_poster_border_margin">1dp</dimen> <!-- 基准值 -->
    <dimen name="video_detail_view_count_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="video_detail_like_text_margin_start">8dp</dimen> <!-- 基准值 -->
    <!-- Video Info -->
    <dimen name="video_player_new_title_text_size">23sp</dimen>
    <dimen name="video_player_new_title_margin_top">7dp</dimen>
    <dimen name="video_player_new_detail_icon_size">18dp</dimen>
    <dimen name="video_player_new_detail_icon_margin_start">8dp</dimen>
    <dimen name="video_player_new_description_text_size">13sp</dimen>
    <dimen name="video_player_new_description_margin_top">10dp</dimen>

    <!-- Bottom Control Row -->
    <dimen name="video_player_new_bottom_controls_margin_top">10dp</dimen>
    <dimen name="video_player_new_episode_button_width">112dp</dimen>
    <dimen name="video_player_new_episode_button_height">36dp</dimen>
    <dimen name="video_player_new_episode_button_corner_radius">8dp</dimen>
    <dimen name="video_player_new_episode_text_size">14sp</dimen>
    <dimen name="video_player_new_episode_icon_size">18dp</dimen>
    <dimen name="video_player_new_episode_padding_start">10dp</dimen>
    <dimen name="video_player_new_episode_padding_end">14dp</dimen>

    <dimen name="video_player_new_danmaku_button_width">57dp</dimen>
    <dimen name="video_player_new_danmaku_button_height">36dp</dimen>
    <dimen name="video_player_new_danmaku_button_corner_radius">8dp</dimen>
    <dimen name="video_player_new_danmaku_icon_size">26dp</dimen>
    <dimen name="video_player_new_control_button_spacing">6dp</dimen>

    <dimen name="video_player_new_speed_button_width">53dp</dimen>
    <dimen name="video_player_new_speed_button_height">36dp</dimen>
    <dimen name="video_player_new_speed_popup_height">184dp</dimen>
    <dimen name="video_player_new_speed_popup_item_height">32dp</dimen>

    <dimen name="video_player_new_fullscreen_button_size">36dp</dimen>
    <dimen name="video_player_new_fullscreen_button_corner_radius">8dp</dimen>
    <dimen name="video_player_new_fullscreen_icon_size">26dp</dimen>

    <!-- Setting Page - Base dimensions for 375dp reference -->
    <dimen name="setting_first_item_margin_start">16dp</dimen>
    <dimen name="setting_first_item_margin_top">32dp</dimen>
    <dimen name="setting_double_item_width">343dp</dimen>
    <dimen name="setting_double_item_height">114dp</dimen>
    <dimen name="setting_single_item_width">343dp</dimen>
    <dimen name="setting_single_item_height">51dp</dimen>
    <dimen name="setting_item_margin_bottom">16dp</dimen>
    <dimen name="setting_item_padding">16dp</dimen>
    <dimen name="setting_text_size">16sp</dimen>
    <dimen name="setting_text_line_height">19dp</dimen>
    <dimen name="setting_arrow_size">16dp</dimen> <!-- 16dp基准值 -->
    <dimen name="setting_terms_text_width">160dp</dimen> <!-- Terms of Service文本宽度 -->
    <dimen name="setting_cache_text_width">120dp</dimen> <!-- Clear Cache文本宽度 -->
    <dimen name="setting_cache_size_width">55dp</dimen> <!-- 缓存大小文本宽度 -->
    <dimen name="setting_bottom_buttons_margin_top">32dp</dimen>
    <dimen name="setting_bottom_buttons_margin_bottom">32dp</dimen>
    <dimen name="setting_bottom_button_margin_horizontal">16dp</dimen>
    <dimen name="setting_bottom_button_margin_between">16dp</dimen>
    <dimen name="setting_bottom_button_corner_radius">21dp</dimen> <!-- 21dp基准值 -->

    <!-- Edit Profile Page - Base dimensions for 375dp reference -->
    <dimen name="edit_profile_card_width">343dp</dimen>
    <dimen name="edit_profile_card_height">200dp</dimen> <!-- 进一步减少高度 -->
    <dimen name="edit_profile_card_margin_top">16dp</dimen>
    <dimen name="edit_profile_avatar_size">48dp</dimen>
    <dimen name="edit_profile_arrow_size">16dp</dimen>
    <dimen name="edit_profile_divider_width">311dp</dimen>
    <dimen name="edit_profile_divider_height">0.5dp</dimen>
    <dimen name="edit_profile_input_width">311dp</dimen>
    <dimen name="edit_profile_input_height">48dp</dimen>
    <dimen name="edit_profile_ip_location_width">343dp</dimen>
    <dimen name="edit_profile_ip_location_height">50dp</dimen>
    <dimen name="edit_profile_save_button_width">343dp</dimen>
    <dimen name="edit_profile_save_button_height">42dp</dimen>
    <dimen name="edit_profile_text_size">16sp</dimen>
    <dimen name="edit_profile_text_line_height">19dp</dimen>
    <dimen name="edit_profile_china_text_width">60dp</dimen> <!-- 增加宽度以显示完整的China -->
    <dimen name="edit_profile_save_text_width">37dp</dimen>
    <dimen name="edit_profile_item_margin_bottom">16dp</dimen>
    <dimen name="edit_profile_item_padding">16dp</dimen>
    <dimen name="edit_profile_save_button_margin_bottom">38dp</dimen> <!-- Save按钮底部间距 -->
    <dimen name="edit_profile_popup_width">120dp</dimen> <!-- 下拉菜单宽度 -->
    <dimen name="edit_profile_popup_item_height">32dp</dimen> <!-- 下拉菜单项高度 -->
    <dimen name="edit_profile_popup_padding">8dp</dimen> <!-- 下拉菜单内边距 -->
    <dimen name="edit_profile_popup_text_size">14sp</dimen> <!-- 下拉菜单文字大小 -->
    <dimen name="edit_profile_popup_corner_radius">8dp</dimen> <!-- 下拉菜单圆角 -->

    <!-- Log Off Page - Base dimensions for 375dp reference -->
    <dimen name="log_off_warning_icon_size">60dp</dimen> <!-- 警告图标大小 -->
    <dimen name="log_off_warning_icon_margin_top">24dp</dimen> <!-- 警告图标距离标题下方 -->
    <dimen name="log_off_description_width">328dp</dimen> <!-- 描述文本宽度 -->
    <dimen name="log_off_description_height">60dp</dimen> <!-- 描述文本高度 -->
    <dimen name="log_off_description_margin_top">24dp</dimen> <!-- 描述文本上边距 -->
    <dimen name="log_off_card_width">343dp</dimen> <!-- 警告卡片宽度 -->
    <dimen name="log_off_card_height">320dp</dimen> <!-- 警告卡片高度（增加） -->
    <dimen name="log_off_card_margin_top">24dp</dimen> <!-- 警告卡片上边距 -->
    <dimen name="log_off_card_padding">16dp</dimen> <!-- 警告卡片内边距 -->
    <dimen name="log_off_card_item_line_height">26dp</dimen> <!-- 卡片内容行高 -->
    <dimen name="log_off_checkbox_width">343dp</dimen> <!-- 确认复选框宽度 -->
    <dimen name="log_off_checkbox_height">60dp</dimen> <!-- 确认复选框高度 -->
    <dimen name="log_off_checkbox_margin_top">24dp</dimen> <!-- 确认复选框距离卡片（靠近按钮） -->
    <dimen name="log_off_checkbox_icon_size">20dp</dimen> <!-- 复选框图标大小（基准值） -->
    <dimen name="log_off_checkbox_icon_margin_end">8dp</dimen> <!-- 复选框图标右边距（基准值） -->
    <dimen name="log_off_checkbox_text_margin_start">28dp</dimen> <!-- 确认文本左边距（20dp图标+8dp间距）基准值 -->
    <dimen name="log_off_checkbox_text_size">13sp</dimen> <!-- 确认文本字体大小 -->
    <dimen name="log_off_checkbox_text_line_spacing">7dp</dimen> <!-- 确认文本行间距（基准值） -->
    <dimen name="log_off_delete_button_width">343dp</dimen> <!-- 删除按钮宽度 -->
    <dimen name="log_off_delete_button_height">42dp</dimen> <!-- 删除按钮高度 -->
    <dimen name="log_off_delete_button_margin_top">24dp</dimen> <!-- 删除按钮上边距 -->
    <dimen name="log_off_delete_button_margin_bottom">38dp</dimen> <!-- 删除按钮下边距 -->
    <dimen name="log_off_delete_text_width">115dp</dimen> <!-- 删除按钮文本宽度 -->
    <dimen name="log_off_delete_text_height">19dp</dimen> <!-- 删除按钮文本高度 -->
    <dimen name="log_off_item_margin_horizontal">16dp</dimen> <!-- 左右边距 -->

    <!-- Feedback Page - Base dimensions for 375dp reference -->
    <dimen name="feedback_required_star_width">7dp</dimen> <!-- 必填星号宽度（基准值） -->
    <dimen name="feedback_required_star_height">19dp</dimen> <!-- 必填星号高度（基准值） -->
    <dimen name="feedback_label_width">108dp</dimen> <!-- 标签宽度（基准值） -->
    <dimen name="feedback_label_height">19dp</dimen> <!-- 标签高度（基准值） -->
    <dimen name="feedback_type_selected_width">113dp</dimen> <!-- 选中类型宽度（基准值） -->
    <dimen name="feedback_type_selected_height">40dp</dimen> <!-- 选中类型高度（基准值） -->
    <dimen name="feedback_type_unselected_width">218dp</dimen> <!-- 未选中类型宽度（基准值） -->
    <dimen name="feedback_type_unselected_height">40dp</dimen> <!-- 未选中类型高度（基准值） -->
    <dimen name="feedback_type_corner_radius">14dp</dimen> <!-- 类型按钮圆角（基准值） -->
    <dimen name="feedback_input_width">343dp</dimen> <!-- 输入框宽度（基准值） -->
    <dimen name="feedback_input_height">186dp</dimen> <!-- 输入框高度（基准值） -->
    <dimen name="feedback_input_corner_radius">16dp</dimen> <!-- 输入框圆角（基准值） -->
    <dimen name="feedback_input_padding">15dp</dimen> <!-- 输入框内边距（基准值） -->
    <dimen name="feedback_image_size">107dp</dimen> <!-- 图片选择框大小（基准值） -->
    <dimen name="feedback_image_corner_radius">16dp</dimen> <!-- 图片框圆角（基准值） -->
    <dimen name="feedback_image_icon_size">28dp</dimen> <!-- 图片图标大小（基准值） -->
    <dimen name="feedback_button_width">343dp</dimen> <!-- 提交按钮宽度（基准值） -->
    <dimen name="feedback_button_height">42dp</dimen> <!-- 提交按钮高度（基准值） -->
    <dimen name="feedback_button_corner_radius">21dp</dimen> <!-- 提交按钮圆角（基准值） -->
    <dimen name="feedback_item_margin_horizontal">16dp</dimen> <!-- 水平边距（基准值） -->
    <dimen name="feedback_item_margin_vertical">24dp</dimen> <!-- 垂直边距（基准值） -->
    <dimen name="feedback_type_margin_end">8dp</dimen> <!-- 类型按钮右边距（基准值） -->

    <!-- My List History Layout Dimensions (基准值 375dp，优化间距设计) -->
    <dimen name="my_list_history_item_height">180dp</dimen> <!-- 保持大尺寸 -->
    <dimen name="my_list_history_item_margin_start">8dp</dimen> <!-- 进一步减少左侧距离 -->
    <dimen name="my_list_history_item_margin_end">8dp</dimen> <!-- 进一步减少右侧距离 -->
    <dimen name="my_list_history_item_margin_bottom">4dp</dimen> <!-- 大幅减少记录间隔 -->
    <dimen name="my_list_history_poster_width">120dp</dimen> <!-- 保持大海报尺寸 -->
    <dimen name="my_list_history_poster_height">160dp</dimen> <!-- 保持大海报尺寸 -->
    <dimen name="my_list_history_poster_corner_radius">15dp</dimen>
    <dimen name="my_list_history_poster_margin_end">16dp</dimen> <!-- 适中的海报右边距 -->
    <dimen name="my_list_history_title_width">180dp</dimen> <!-- 保持大标题宽度 -->
    <dimen name="my_list_history_title_height">28dp</dimen> <!-- 保持大标题高度 -->
    <dimen name="my_list_history_title_text_size">22sp</dimen> <!-- 保持大字体 -->
    <dimen name="my_list_history_episode_text_size">20sp</dimen> <!-- 保持大字体 -->
    <dimen name="my_list_history_episode_margin_top">16dp</dimen>
    <dimen name="my_list_history_play_button_width">100dp</dimen> <!-- 保持大按钮 -->
    <dimen name="my_list_history_play_button_height">50dp</dimen> <!-- 保持大按钮 -->
    <dimen name="my_list_history_play_button_corner_radius">25dp</dimen>
    <dimen name="my_list_history_play_button_margin_end">12dp</dimen> <!-- 减少按钮右边距 -->
    <dimen name="my_list_history_play_icon_size">24dp</dimen> <!-- 保持大图标 -->
    <dimen name="my_list_history_play_text_size">18sp</dimen> <!-- 保持大字体 -->

    <!-- My List Grid Layout Dimensions (基准值 375dp) -->
    <dimen name="my_list_grid_poster_width">164dp</dimen>
    <dimen name="my_list_grid_poster_height">219dp</dimen>
    <dimen name="my_list_grid_poster_corner_radius">16dp</dimen>
    <dimen name="my_list_grid_margin_end">15dp</dimen>
    <dimen name="my_list_grid_margin_bottom">16dp</dimen>
    <dimen name="my_list_grid_like_icon_size">32dp</dimen>
    <dimen name="my_list_grid_like_margin_start">5dp</dimen>
    <dimen name="my_list_grid_like_margin_top">5dp</dimen>
    <dimen name="my_list_grid_title_width">164dp</dimen>
    <dimen name="my_list_grid_title_height">17dp</dimen>
    <dimen name="my_list_grid_title_margin_top">8dp</dimen>
    <dimen name="my_list_grid_title_text_size">14sp</dimen>

    <!-- My List Grid Spacing (基准值 375dp) -->
    <dimen name="my_list_grid_spacing">15dp</dimen>
    <dimen name="my_list_grid_edge_spacing">15dp</dimen>

    <!-- My List Grid Container Dimensions (响应式居中，基准375dp) -->
    <dimen name="my_list_grid_container_width">343dp</dimen> <!-- 375 - 16*2 = 343dp -->
    <dimen name="my_list_grid_item_width">164dp</dimen> <!-- (343 - 15) / 2 = 164dp -->

    <!-- Download Page Dimensions - Base values for 375dp reference (812dp height) -->
    <!-- Header -->
    <dimen name="download_header_height">88dp</dimen>
    <dimen name="download_header_padding_start">16dp</dimen>
    <dimen name="download_header_padding_end">16dp</dimen>
    <dimen name="download_header_padding_top">44dp</dimen>
    <dimen name="download_back_button_size">36dp</dimen>
    <dimen name="download_header_title_text_size">18sp</dimen>

    <!-- List -->
    <dimen name="download_list_margin_top">14dp</dimen>
    <dimen name="download_list_padding_horizontal">16dp</dimen>
    <dimen name="download_list_padding_bottom">20dp</dimen>

    <!-- Download Item -->
    <dimen name="download_item_height">140dp</dimen>
    <dimen name="download_item_margin_bottom">20dp</dimen>

    <!-- Delete Icon -->
    <dimen name="download_delete_icon_size">22dp</dimen>
    <dimen name="download_delete_margin_end">16dp</dimen>

    <!-- Poster -->
    <dimen name="download_poster_width">92dp</dimen>
    <dimen name="download_poster_height">123dp</dimen>
    <dimen name="download_poster_margin_top">17dp</dimen>

    <!-- Content Area -->
    <dimen name="download_content_margin_start">15dp</dimen>
    <dimen name="download_content_margin_top">17dp</dimen>
    <dimen name="download_content_margin_end">15dp</dimen>

    <!-- Title -->
    <dimen name="download_title_width">180dp</dimen>
    <dimen name="download_title_height">24dp</dimen>
    <dimen name="download_title_text_size">20sp</dimen>

    <!-- Status -->
    <dimen name="download_status_width">120dp</dimen>
    <dimen name="download_status_height">17dp</dimen>
    <dimen name="download_status_margin_top">5dp</dimen>
    <dimen name="download_status_text_size">15sp</dimen>

    <!-- Progress -->
    <dimen name="download_progress_margin_top">10dp</dimen>
    <dimen name="download_count_width">22dp</dimen>
    <dimen name="download_count_height">24dp</dimen>
    <dimen name="download_count_text_size">20sp</dimen>
    <dimen name="download_arrow_icon_size">20dp</dimen>
    <dimen name="download_arrow_margin_start">5dp</dimen>

    <!-- Play Button -->
    <dimen name="download_play_button_width">95dp</dimen>
    <dimen name="download_play_button_height">40dp</dimen>
    <dimen name="download_play_button_margin_end">16dp</dimen>
    <dimen name="download_play_icon_size">20dp</dimen>
    <dimen name="download_play_icon_margin_end">5dp</dimen>
    <dimen name="download_play_text_width">31dp</dimen>
    <dimen name="download_play_text_height">19dp</dimen>
    <dimen name="download_play_text_size">16sp</dimen>

    <!-- Empty State -->
    <dimen name="download_empty_image_size">120dp</dimen>
    <dimen name="download_empty_text_margin_top">16dp</dimen>
    <dimen name="download_empty_text_size">14sp</dimen>

    <!-- Loading State -->
    <dimen name="download_loading_progress_size">48dp</dimen>
    <dimen name="download_loading_text_margin_top">16dp</dimen>
    <dimen name="download_loading_text_size">14sp</dimen>

    <!-- Download Page Title -->
    <dimen name="download_page_title_text_size">22sp</dimen>

    <!-- Video Download Detail Page - Base values for 375dp reference -->
    <!-- Top poster area -->
    <dimen name="video_detail_poster_height">500dp</dimen>

    <!-- Navigation buttons -->
    <dimen name="video_detail_back_button_size">16dp</dimen>
    <dimen name="video_detail_share_button_size">22dp</dimen>
    <dimen name="video_detail_nav_button_margin_top">55dp</dimen>
    <dimen name="video_detail_nav_button_margin_horizontal">16dp</dimen>

    <!-- Small poster -->
    <dimen name="video_detail_small_poster_width">84dp</dimen>
    <dimen name="video_detail_small_poster_height">112dp</dimen>
    <dimen name="video_detail_small_poster_corner_radius">11dp</dimen>
    <dimen name="video_detail_small_poster_margin_start">16dp</dimen>
    <dimen name="video_detail_small_poster_margin_top">20dp</dimen>

    <!-- Video info -->
    <dimen name="video_detail_title_margin_start">12dp</dimen>
    <dimen name="video_detail_title_margin_top">6dp</dimen>
    <dimen name="video_detail_title_text_size">20sp</dimen>

    <dimen name="video_detail_view_icon_size">18dp</dimen>
    <dimen name="video_detail_view_text_margin_start">12dp</dimen>
    <dimen name="video_detail_view_text_margin_top">15dp</dimen>
    <dimen name="video_detail_view_text_size">13sp</dimen>

    <!-- Like button -->
    <dimen name="video_detail_like_button_width">144dp</dimen>
    <dimen name="video_detail_like_button_height">32dp</dimen>
    <dimen name="video_detail_like_button_corner_radius">16dp</dimen>
    <dimen name="video_detail_like_button_margin_top">18dp</dimen>
    <dimen name="video_detail_like_icon_size">20dp</dimen>
    <dimen name="video_detail_like_text_size">14sp</dimen>

    <!-- Synopsis section -->
    <dimen name="video_detail_synopsis_title_text_size">18sp</dimen>
    <dimen name="video_detail_synopsis_margin_top">20dp</dimen>
    <dimen name="video_detail_synopsis_title_margin_bottom">14dp</dimen>
    <dimen name="video_detail_synopsis_element_spacing">14dp</dimen>

    <!-- Tags -->
    <dimen name="video_detail_tag_width">80dp</dimen>
    <dimen name="video_detail_tag_height">30dp</dimen>
    <dimen name="video_detail_tag_corner_radius">10dp</dimen>
    <dimen name="video_detail_tag_spacing">10dp</dimen>

    <!-- Synopsis content -->
    <dimen name="video_detail_synopsis_content_margin_top">14dp</dimen>
    <dimen name="video_detail_synopsis_content_text_size">14sp</dimen>
    <dimen name="video_detail_synopsis_content_line_height">21dp</dimen>

    <!-- Actor section -->
    <dimen name="video_detail_actor_margin_top">20dp</dimen>
    <dimen name="video_detail_actor_poster_width">74dp</dimen>
    <dimen name="video_detail_actor_poster_height">91.5dp</dimen>
    <dimen name="video_detail_actor_poster_corner_radius">9dp</dimen>
    <dimen name="video_detail_actor_spacing">12dp</dimen>
    <dimen name="video_detail_actor_name_margin_top">4dp</dimen>

    <!-- Director section -->
    <dimen name="video_detail_director_margin_top">20dp</dimen>
    <dimen name="video_detail_director_poster_width">74dp</dimen>
    <dimen name="video_detail_director_poster_height">91.5dp</dimen>
    <dimen name="video_detail_director_poster_corner_radius">9dp</dimen>
    <dimen name="video_detail_director_spacing">12dp</dimen>
    <dimen name="video_detail_director_name_margin_top">4dp</dimen>

    <!-- Episodes section -->
    <dimen name="video_detail_episodes_margin_top">23dp</dimen>
    <dimen name="video_detail_episodes_title_text_size">18sp</dimen>
    <dimen name="video_detail_episodes_range_margin_top">13dp</dimen>
    <dimen name="video_detail_episodes_range_text_size">14sp</dimen>
    <dimen name="video_detail_episodes_range_spacing">25dp</dimen>
    <dimen name="video_detail_episodes_range_underline_margin_top">7dp</dimen>


    <!-- Video Info Card -->
    <dimen name="download_detail_card_height">140dp</dimen>
    <dimen name="download_detail_card_margin_horizontal">16dp</dimen>
    <dimen name="download_detail_card_margin_top">24dp</dimen>

    <!-- Poster -->
    <dimen name="download_detail_poster_width">92dp</dimen>
    <dimen name="download_detail_poster_height">123dp</dimen>

    <!-- Content Area -->
    <dimen name="download_detail_content_margin_start">15dp</dimen>
    <dimen name="download_detail_content_margin_end">15dp</dimen>

    <!-- Title -->
    <dimen name="download_detail_title_text_size">20sp</dimen>

    <!-- Status -->
    <dimen name="download_detail_status_margin_top">5dp</dimen>
    <dimen name="download_detail_status_text_size">15sp</dimen>

    <!-- Progress -->
    <dimen name="download_detail_progress_margin_top">10dp</dimen>
    <dimen name="download_detail_count_text_size">20sp</dimen>

    <!-- File Size -->
    <dimen name="download_detail_file_size_margin_top">8dp</dimen>
    <dimen name="download_detail_file_size_text_size">15sp</dimen>

    <!-- Play Button -->
    <dimen name="download_detail_play_button_width">80dp</dimen>
    <dimen name="download_detail_play_button_height">40dp</dimen>
    <dimen name="download_detail_play_icon_size">20dp</dimen>
    <dimen name="download_detail_play_icon_margin_end">5dp</dimen>
    <dimen name="download_detail_play_text_size">16sp</dimen>

    <!-- Episodes List -->
    <dimen name="download_detail_episodes_margin_top">32dp</dimen>
    <dimen name="download_detail_episodes_padding_horizontal">16dp</dimen>
    <dimen name="download_detail_episodes_padding_bottom">20dp</dimen>

    <!-- Episode Item -->
    <dimen name="episode_item_height">56dp</dimen>
    <dimen name="episode_item_margin_bottom">16dp</dimen>

    <!-- Episode Number -->
    <dimen name="episode_number_width">60dp</dimen>
    <dimen name="episode_number_height">24dp</dimen>
    <dimen name="episode_number_margin_start">16dp</dimen>
    <dimen name="episode_number_text_size">18sp</dimen>

    <!-- Episode File Size -->
    <dimen name="episode_file_size_width">60dp</dimen>
    <dimen name="episode_file_size_height">24dp</dimen>
    <dimen name="episode_file_size_margin_start">24dp</dimen>
    <dimen name="episode_file_size_margin_top">4dp</dimen>
    <dimen name="episode_file_size_text_size">15sp</dimen>

    <!-- Episode Buttons -->
    <dimen name="episode_play_button_size">32dp</dimen>
    <dimen name="episode_play_button_margin_end">16dp</dimen>
    <dimen name="episode_delete_button_size">32dp</dimen>
    <dimen name="episode_delete_button_margin_end">16dp</dimen>

    <!-- Episode Divider -->
    <dimen name="episode_divider_height">0.5dp</dimen>
    <dimen name="episode_divider_margin_top">8dp</dimen>

    <!-- Bottom Sheet -->
    <dimen name="bottom_sheet_padding_top">16dp</dimen>
    <dimen name="bottom_sheet_padding_horizontal">16dp</dimen>
    <dimen name="bottom_sheet_handle_width">40dp</dimen>
    <dimen name="bottom_sheet_handle_height">4dp</dimen>
    <dimen name="bottom_sheet_handle_margin_bottom">16dp</dimen>
    <dimen name="bottom_sheet_close_button_size">22dp</dimen>
    <dimen name="bottom_sheet_divider_height">6dp</dimen>
    <dimen name="bottom_sheet_divider_margin_top">24dp</dimen>
    <dimen name="bottom_sheet_divider_margin_bottom">16dp</dimen>
    <dimen name="bottom_sheet_episodes_max_height">400dp</dimen>

    <!-- Recharge Popup Dimensions -->
    <dimen name="recharge_popup_height">684dp</dimen> <!-- 缩短16dp -->
    <dimen name="recharge_popup_corner_radius_top">24dp</dimen>
    <dimen name="recharge_popup_corner_radius_bottom">0dp</dimen>

    <!-- Recharge Background Image -->
    <dimen name="recharge_bg_image_height">202dp</dimen>

    <!-- My Points Section -->
    <dimen name="recharge_my_points_margin_top">13dp</dimen> <!-- 缩短20dp -->
    <dimen name="recharge_my_points_margin_start">16dp</dimen>
    <dimen name="recharge_my_points_text_size">16sp</dimen>
    <dimen name="recharge_header_jinbi_icon_size">30dp</dimen> <!-- 标题区域的jinbi图片大小 -->
    <dimen name="recharge_jinbi_icon_margin_start">10dp</dimen>
    <dimen name="recharge_jinbi_icon_margin_top">0dp</dimen> <!-- 缩短20dp -->
    <dimen name="recharge_header_coin_count_text_size">28sp</dimen> <!-- 标题区域的金币数文本大小 -->
    <dimen name="recharge_coin_count_margin_top">0dp</dimen> <!-- 缩短20dp -->

    <!-- Recharge Amount Cards -->
    <dimen name="recharge_amount_cards_margin_top">22dp</dimen> <!-- 缩短10dp -->
    <dimen name="recharge_amount_card_width">156dp</dimen> <!-- 缩短10dp -->
    <dimen name="recharge_amount_card_height">90dp</dimen> <!-- 增加12dp -->
    <dimen name="recharge_amount_card_corner_radius">8dp</dimen>
    <dimen name="recharge_amount_card_margin_start">16dp</dimen>
    <dimen name="recharge_amount_card_margin_between">12dp</dimen>
    <dimen name="recharge_amount_card_border_width">2dp</dimen>
    <dimen name="recharge_amount_jinbi_margin_top">16dp</dimen>
    <dimen name="recharge_amount_jinbi_margin_start">16dp</dimen>
    <dimen name="recharge_amount_jinbi_icon_size">18dp</dimen> <!-- 充值矩形中的jinbi图片大小 -->
    <dimen name="recharge_amount_coin_text_size">20sp</dimen>
    <dimen name="recharge_amount_coin_margin_start">3dp</dimen>
    <dimen name="recharge_amount_bonus_text_size">16sp</dimen>
    <dimen name="recharge_amount_bonus_margin_start">5dp</dimen>
    <dimen name="recharge_amount_price_text_size">16sp</dimen>
    <dimen name="recharge_amount_price_margin_top">12dp</dimen>

    <!-- Crown Section -->
    <dimen name="recharge_crown_margin_top">20dp</dimen>
    <dimen name="recharge_crown_width">86dp</dimen>
    <dimen name="recharge_crown_height">55dp</dimen>
    <dimen name="recharge_crown_text_margin_top">-5dp</dimen> <!-- 文本与皇冠图片的距离 -->
    <dimen name="recharge_crown_text_size">20sp</dimen>
    <dimen name="recharge_yellow_router_margin_top">-8dp</dimen> <!-- yellow_router与文本的距离 -->
    <dimen name="recharge_yellow_router_width">44dp</dimen>
    <dimen name="recharge_yellow_router_height">5dp</dimen>

    <!-- Close Button -->
    <dimen name="recharge_close_button_size">18dp</dimen> <!-- 关闭按钮大小 -->

    <!-- VIP Cards -->
    <dimen name="recharge_vip_cards_margin_top">18dp</dimen>
    <dimen name="recharge_vip_card_width">323dp</dimen> <!-- 缩短20dp -->
    <dimen name="recharge_vip_card_height">107dp</dimen>
    <dimen name="recharge_vip_card_corner_radius">18dp</dimen>
    <dimen name="recharge_vip_card_border_width">2dp</dimen>
    <dimen name="recharge_vip_card_margin_between">12dp</dimen>
    <dimen name="recharge_vip_title_margin_top">16dp</dimen>
    <dimen name="recharge_vip_title_margin_start">14dp</dimen>
    <dimen name="recharge_vip_title_text_size">18sp</dimen>
    <dimen name="recharge_vip_subtitle_margin_top">6dp</dimen>
    <dimen name="recharge_vip_subtitle_text_size">14sp</dimen>
    <dimen name="recharge_vip_bonus_margin_top">16dp</dimen>
    <dimen name="recharge_vip_bonus_text_size">14sp</dimen>
    <dimen name="recharge_vip_price_container_width">112dp</dimen>
    <dimen name="recharge_vip_price_container_height">38dp</dimen>
    <dimen name="recharge_vip_price_container_corner_radius">14dp</dimen>
    <dimen name="recharge_vip_price_container_margin_top">19dp</dimen>
    <dimen name="recharge_vip_price_container_margin_end">12dp</dimen>
    <dimen name="recharge_vip_price_text_size">20sp</dimen>
    <dimen name="recharge_vip_period_text_size">14sp</dimen>
    <dimen name="recharge_vip_period_margin_start">4dp</dimen>

    <!-- Agreement Text -->
    <dimen name="recharge_agreement_margin_top">15dp</dimen> <!-- 缩短5dp -->
    <dimen name="recharge_agreement_text_size">11sp</dimen>

    <!-- Search Result Screen Dimensions - Base values for 411dp reference -->

    <!-- Header Section -->
    <dimen name="search_result_header_height">88dp</dimen>
    <dimen name="search_result_header_padding_horizontal">16dp</dimen>
    <dimen name="search_result_header_padding_top">44dp</dimen>

    <!-- Back Button -->
    <dimen name="search_result_back_button_size">24dp</dimen>

    <!-- Search Box -->
    <dimen name="search_result_search_box_height">36dp</dimen>
    <dimen name="search_result_search_box_margin_start">16dp</dimen>
    <dimen name="search_result_search_box_margin_end">16dp</dimen>
    <dimen name="search_result_search_box_padding_horizontal">12dp</dimen>

    <!-- Search Icon -->
    <dimen name="search_result_search_icon_size">16dp</dimen>
    <dimen name="search_result_search_icon_margin_end">8dp</dimen>

    <!-- Search Text -->
    <dimen name="search_result_search_text_size">14sp</dimen>

    <!-- Clear Button -->
    <dimen name="search_result_clear_button_size">24dp</dimen>

    <!-- Content Section -->
    <dimen name="search_result_content_padding_horizontal">16dp</dimen>

    <!-- Result Count -->
    <dimen name="search_result_count_padding_vertical">12dp</dimen>
    <dimen name="search_result_count_text_size">12sp</dimen>

    <!-- Video Item -->
    <dimen name="search_result_item_height">79dp</dimen>
    <dimen name="search_result_item_margin_bottom">18dp</dimen>
    <dimen name="search_result_item_padding_horizontal">0dp</dimen>
    <dimen name="search_result_item_padding_vertical">0dp</dimen>

    <!-- Poster -->
    <dimen name="search_result_poster_width">118dp</dimen>
    <dimen name="search_result_poster_height">79dp</dimen>
    <dimen name="search_result_poster_margin_end">12dp</dimen>

    <!-- Title -->
    <dimen name="search_result_title_height">20dp</dimen>
    <dimen name="search_result_title_margin_end">8dp</dimen>
    <dimen name="search_result_title_text_size">16sp</dimen>

    <!-- Episode Info -->
    <dimen name="search_result_episode_margin_top">8dp</dimen>
    <dimen name="search_result_episode_text_size">12sp</dimen>

    <!-- Play Button -->
    <dimen name="search_result_play_button_size">32dp</dimen>
    <dimen name="search_result_play_button_margin_end">16dp</dimen>

    <!-- Like Button -->
    <dimen name="search_result_like_button_size">24dp</dimen>

    <!-- Empty State -->
    <dimen name="empty_state_padding_horizontal">32dp</dimen>
    <dimen name="empty_state_container_width">286dp</dimen>
    <dimen name="empty_state_container_height">286dp</dimen>
    <dimen name="empty_state_image_width">286dp</dimen>
    <dimen name="empty_state_image_height">286dp</dimen>
    <dimen name="empty_state_text_margin_top">16dp</dimen>
    <dimen name="empty_state_text_size">14sp</dimen>

    <!-- Information Page Dimensions - Base values for 375dp reference -->

    <!-- Content margins -->
    <dimen name="information_content_margin_horizontal">16dp</dimen>
    <dimen name="information_content_margin_top">24dp</dimen>
    <dimen name="information_content_margin_bottom">24dp</dimen>

    <!-- Text dimensions -->
    <dimen name="information_content_text_size">16sp</dimen>
    <dimen name="information_content_line_spacing">4dp</dimen>

    <!-- Loading state -->
    <dimen name="information_loading_margin_top">100dp</dimen>

    <!-- Video Player Controls - New responsive dimensions (Base values for 360dp reference) -->
    <dimen name="video_player_view_height">667dp</dimen>
    <dimen name="video_player_view_margin_top">60dp</dimen>
    <dimen name="video_player_text_size_small">12sp</dimen>
    <dimen name="video_player_text_size_medium">14sp</dimen>
    <dimen name="video_player_text_size_large">16sp</dimen>

    <!-- Common Style Properties - New responsive dimensions (Base values for 360dp reference) -->
    <dimen name="common_stroke_width_thin">0.5dp</dimen>
    <dimen name="common_stroke_width_normal">1dp</dimen>
    <dimen name="common_corner_radius_small">4dp</dimen>
    <dimen name="common_corner_radius_medium">8dp</dimen>
    <dimen name="common_corner_radius_large">16dp</dimen>
    <dimen name="common_shadow_offset_x">2dp</dimen>
    <dimen name="common_shadow_offset_y">1.5dp</dimen>
    <dimen name="common_shadow_radius">6dp</dimen>
    <dimen name="common_dash_width">4dp</dimen>
    <dimen name="common_dash_gap">2dp</dimen>

</resources>
