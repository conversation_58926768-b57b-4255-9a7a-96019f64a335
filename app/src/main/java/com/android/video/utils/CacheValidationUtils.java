package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import com.android.video.cache.DataCacheManager;
import com.android.video.cache.ImprovedDataCacheManager;
import com.android.video.manager.FragmentCacheManager;
import java.util.Map;

/**
 * 缓存验证工具类
 * 用于验证和测试缓存功能的正确性
 * <AUTHOR> Team
 */
public class CacheValidationUtils {
    
    private static final String TAG = "CacheValidationUtils";
    
    /**
     * 验证结果类
     */
    public static class CacheValidationResult {
        public boolean isValid;
        public String message;
        public int cacheHitCount;
        public int cacheMissCount;
        public long totalValidationTime;
        
        public CacheValidationResult(boolean isValid, String message) {
            this.isValid = isValid;
            this.message = message;
        }
        
        @Override
        public String toString() {
            return "CacheValidationResult{" +
                    "isValid=" + isValid +
                    ", message='" + message + '\'' +
                    ", cacheHitCount=" + cacheHitCount +
                    ", cacheMissCount=" + cacheMissCount +
                    ", totalValidationTime=" + totalValidationTime + "ms" +
                    '}';
        }
    }
    
    /**
     * 验证Fragment缓存功能
     */
    public static CacheValidationResult validateFragmentCache(Context context) {
        Log.d(TAG, "开始验证Fragment缓存功能");
        long startTime = System.currentTimeMillis();
        
        try {
            FragmentCacheManager cacheManager = FragmentCacheManager.getInstance();
            
            // 测试Fragment状态管理
            String testFragmentKey = "test_fragment";
            
            // 1. 测试初始状态
            if (!cacheManager.shouldReloadData(testFragmentKey)) {
                return new CacheValidationResult(false, "新Fragment应该需要加载数据");
            }
            
            // 2. 测试数据加载后的状态
            cacheManager.updateDataLoaded(testFragmentKey, true);
            cacheManager.updateCacheState(testFragmentKey, true);
            
            if (cacheManager.shouldReloadData(testFragmentKey)) {
                return new CacheValidationResult(false, "已缓存的Fragment不应该需要重新加载数据");
            }
            
            // 3. 测试缓存重置
            cacheManager.resetFragmentState(testFragmentKey);
            
            if (!cacheManager.shouldReloadData(testFragmentKey)) {
                return new CacheValidationResult(false, "重置后的Fragment应该需要重新加载数据");
            }
            
            // 4. 清理测试数据
            cacheManager.clearFragmentState(testFragmentKey);
            
            long endTime = System.currentTimeMillis();
            CacheValidationResult result = new CacheValidationResult(true, "Fragment缓存功能验证通过");
            result.totalValidationTime = endTime - startTime;
            
            Log.d(TAG, "Fragment缓存功能验证完成: " + result.toString());
            return result;
            
        } catch (Exception e) {
            Log.e(TAG, "Fragment缓存功能验证失败", e);
            return new CacheValidationResult(false, "验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 验证数据缓存功能（包括改进的缓存管理器）
     */
    public static CacheValidationResult validateDataCache(Context context) {
        Log.d(TAG, "开始验证数据缓存功能");
        long startTime = System.currentTimeMillis();

        try {
            // 测试原始缓存管理器
            DataCacheManager oldCacheManager = DataCacheManager.getInstance(context);

            String testKey1 = "test_cache_key_old";
            String testData1 = "test_cache_data_old";

            // 1. 测试原始缓存存储
            oldCacheManager.cacheData(testKey1, testData1);

            // 2. 测试原始缓存读取
            String cachedData1 = oldCacheManager.getCachedData(testKey1, String.class);
            if (!testData1.equals(cachedData1)) {
                return new CacheValidationResult(false, "原始缓存数据读取不匹配");
            }

            // 测试改进的缓存管理器
            ImprovedDataCacheManager newCacheManager = ImprovedDataCacheManager.getInstance(context);

            String testKey2 = "test_cache_key_new";
            String testData2 = "test_cache_data_new";

            // 3. 测试改进缓存存储
            boolean stored = newCacheManager.cacheDataAtomic(testKey2, testData2);
            if (!stored) {
                return new CacheValidationResult(false, "改进缓存数据存储失败");
            }

            // 4. 测试改进缓存读取
            String cachedData2 = newCacheManager.getCachedDataAtomic(testKey2, String.class);
            if (!testData2.equals(cachedData2)) {
                return new CacheValidationResult(false, "改进缓存数据读取不匹配");
            }

            // 5. 测试改进缓存有效性检查
            if (!newCacheManager.isCacheValid(testKey2)) {
                return new CacheValidationResult(false, "改进缓存应该是有效的");
            }

            // 6. 测试缓存清除
            oldCacheManager.clearCachedData(testKey1);
            newCacheManager.clearCachedData(testKey2);

            String clearedData1 = oldCacheManager.getCachedData(testKey1, String.class);
            String clearedData2 = newCacheManager.getCachedDataAtomic(testKey2, String.class);

            if (clearedData1 != null || clearedData2 != null) {
                return new CacheValidationResult(false, "缓存清除后应该返回null");
            }

            long endTime = System.currentTimeMillis();
            CacheValidationResult result = new CacheValidationResult(true, "数据缓存功能验证通过（包括改进版本）");
            result.totalValidationTime = endTime - startTime;

            Log.d(TAG, "数据缓存功能验证完成: " + result.toString());
            return result;

        } catch (Exception e) {
            Log.e(TAG, "数据缓存功能验证失败", e);
            return new CacheValidationResult(false, "验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 验证缓存性能（包括改进的缓存管理器）
     */
    public static CacheValidationResult validateCachePerformance(Context context) {
        Log.d(TAG, "开始验证缓存性能");
        long startTime = System.currentTimeMillis();

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);

            int testCount = 50; // 减少测试数量，因为改进版本有更多验证步骤
            String testKeyPrefix = "perf_test_improved_";
            String testData = "performance_test_data_improved_" + System.currentTimeMillis();

            // 测试改进缓存写入性能
            long writeStartTime = System.currentTimeMillis();
            int writeSuccessCount = 0;
            for (int i = 0; i < testCount; i++) {
                boolean success = cacheManager.cacheDataAtomic(testKeyPrefix + i, testData + i);
                if (success) {
                    writeSuccessCount++;
                }
            }
            long writeEndTime = System.currentTimeMillis();
            long writeTime = writeEndTime - writeStartTime;

            // 测试改进缓存读取性能
            long readStartTime = System.currentTimeMillis();
            int readSuccessCount = 0;
            for (int i = 0; i < testCount; i++) {
                String cachedData = cacheManager.getCachedDataAtomic(testKeyPrefix + i, String.class);
                if (cachedData != null && cachedData.equals(testData + i)) {
                    readSuccessCount++;
                }
            }
            long readEndTime = System.currentTimeMillis();
            long readTime = readEndTime - readStartTime;

            // 清理测试数据
            for (int i = 0; i < testCount; i++) {
                cacheManager.clearCachedData(testKeyPrefix + i);
            }

            long endTime = System.currentTimeMillis();
            CacheValidationResult result = new CacheValidationResult(true,
                String.format("改进缓存性能验证通过 - 写入%d项耗时%dms(成功%d), 读取%d项耗时%dms(成功%d)",
                    testCount, writeTime, writeSuccessCount, testCount, readTime, readSuccessCount));
            result.totalValidationTime = endTime - startTime;
            result.cacheHitCount = readSuccessCount;
            result.cacheMissCount = testCount - readSuccessCount;

            Log.d(TAG, "改进缓存性能验证完成: " + result.toString());
            return result;

        } catch (Exception e) {
            Log.e(TAG, "改进缓存性能验证失败", e);
            return new CacheValidationResult(false, "验证过程中发生异常: " + e.getMessage());
        }
    }
    
    /**
     * 运行完整的缓存验证
     */
    public static void runFullCacheValidation(Context context) {
        Log.i(TAG, "=== 开始完整缓存验证 ===");
        
        // 验证Fragment缓存
        CacheValidationResult fragmentResult = validateFragmentCache(context);
        Log.i(TAG, "Fragment缓存验证结果: " + fragmentResult.toString());
        
        // 验证数据缓存
        CacheValidationResult dataResult = validateDataCache(context);
        Log.i(TAG, "数据缓存验证结果: " + dataResult.toString());
        
        // 验证缓存性能
        CacheValidationResult perfResult = validateCachePerformance(context);
        Log.i(TAG, "缓存性能验证结果: " + perfResult.toString());
        
        // 打印Fragment缓存状态
        FragmentCacheManager.getInstance().debugPrintAllStates();
        
        Log.i(TAG, "=== 完整缓存验证结束 ===");
    }
    
    /**
     * 获取缓存统计信息（包括改进的缓存管理器）
     */
    public static String getCacheStatistics(Context context) {
        try {
            DataCacheManager oldCacheManager = DataCacheManager.getInstance(context);
            DataCacheManager.CacheStats oldStats = oldCacheManager.getCacheStats();

            ImprovedDataCacheManager newCacheManager = ImprovedDataCacheManager.getInstance(context);
            ImprovedDataCacheManager.CacheStats newStats = newCacheManager.getCacheStats();

            FragmentCacheManager fragmentManager = FragmentCacheManager.getInstance();
            Map<String, FragmentCacheManager.FragmentCacheState> fragmentStates =
                fragmentManager.getAllFragmentStates();

            return String.format("缓存统计 - 原始缓存(内存: %d项, 磁盘: %d项), 改进缓存(磁盘: %d项, 大小: %d字符), Fragment状态: %d个",
                oldStats.memoryCacheSize, oldStats.diskCacheSize,
                newStats.diskCacheSize, newStats.totalDataSize, fragmentStates.size());

        } catch (Exception e) {
            Log.e(TAG, "获取缓存统计信息失败", e);
            return "缓存统计信息获取失败: " + e.getMessage();
        }
    }
}
