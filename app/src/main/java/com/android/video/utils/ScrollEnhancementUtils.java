package com.android.video.utils;

import android.content.Context;
import android.view.View;
import android.widget.ProgressBar;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import android.os.Handler;
import android.os.Looper;

/**
 * 滑动增强工具类
 * <p>
 * 实现滑动惯性和快速定位功能
 * </p>
 */
public class ScrollEnhancementUtils {
    
    // 滑动惯性参数
    public static final float INERTIA_DECAY_FACTOR = 0.9f;
    public static final int FAST_SCROLL_THRESHOLD = 3; // 快速滑动3屏的阈值
    public static final int PROGRESS_SHOW_DURATION = 2000; // 进度条显示时长
    
    private static Handler handler = new Handler(Looper.getMainLooper());
    
    /**
     * 为RecyclerView添加滑动增强功能
     * <p>
     * 需求：滑动惯性衰减系数 0.9，快速滑动 3 屏显示进度条
     * </p>
     */
    public static void enhanceRecyclerViewScrolling(RecyclerView recyclerView, ProgressBar progressBar) {
        if (recyclerView == null) return;
        
        recyclerView.addOnScrollListener(new RecyclerView.OnScrollListener() {
            private int totalScrollDistance = 0;
            private long scrollStartTime = 0;
            private boolean isScrolling = false;
            private Runnable hideProgressRunnable;
            
            @Override
            public void onScrollStateChanged(RecyclerView recyclerView, int newState) {
                super.onScrollStateChanged(recyclerView, newState);
                
                switch (newState) {
                    case RecyclerView.SCROLL_STATE_DRAGGING:
                        // 开始滑动
                        isScrolling = true;
                        scrollStartTime = System.currentTimeMillis();
                        totalScrollDistance = 0;
                        
                        // 取消之前的隐藏任务
                        if (hideProgressRunnable != null) {
                            handler.removeCallbacks(hideProgressRunnable);
                        }
                        break;
                        
                    case RecyclerView.SCROLL_STATE_SETTLING:
                        // 惯性滑动中
                        break;
                        
                    case RecyclerView.SCROLL_STATE_IDLE:
                        // 滑动结束
                        if (isScrolling) {
                            handleScrollEnd(recyclerView, progressBar);
                            isScrolling = false;
                        }
                        break;
                }
            }
            
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);
                
                if (isScrolling) {
                    totalScrollDistance += Math.abs(dy);
                    
                    // 检查是否快速滑动
                    checkFastScroll(recyclerView, progressBar);
                }
            }
            
            private void checkFastScroll(RecyclerView recyclerView, ProgressBar progressBar) {
                if (progressBar == null) return;
                
                // 计算屏幕高度
                int screenHeight = recyclerView.getHeight();
                int fastScrollThreshold = screenHeight * FAST_SCROLL_THRESHOLD;
                
                // 如果滑动距离超过3屏，显示进度条
                if (totalScrollDistance > fastScrollThreshold) {
                    showScrollProgress(recyclerView, progressBar);
                }
            }
            
            private void showScrollProgress(RecyclerView recyclerView, ProgressBar progressBar) {
                if (progressBar == null) return;
                
                // 显示进度条
                if (progressBar.getVisibility() != View.VISIBLE) {
                    progressBar.setVisibility(View.VISIBLE);
                    UIAnimationUtils.animateLoadingState(progressBar, true);
                }
                
                // 计算滚动进度
                LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                if (layoutManager != null) {
                    int firstVisiblePosition = layoutManager.findFirstVisibleItemPosition();
                    int totalItemCount = layoutManager.getItemCount();
                    
                    if (totalItemCount > 0) {
                        int progress = (int) ((float) firstVisiblePosition / totalItemCount * 100);
                        progressBar.setProgress(Math.min(progress, 100));
                    }
                }
            }
            
            private void handleScrollEnd(RecyclerView recyclerView, ProgressBar progressBar) {
                // 应用惯性衰减
                applyInertiaDecay(recyclerView);
                
                // 延迟隐藏进度条
                if (progressBar != null && progressBar.getVisibility() == View.VISIBLE) {
                    hideProgressRunnable = () -> {
                        UIAnimationUtils.animateLoadingState(progressBar, false);
                    };
                    handler.postDelayed(hideProgressRunnable, PROGRESS_SHOW_DURATION);
                }
            }
            
            private void applyInertiaDecay(RecyclerView recyclerView) {
                // 获取当前滑动速度并应用衰减
                // 这里可以通过自定义的方式实现更精细的惯性控制
                // 由于Android RecyclerView已有内置惯性，这里主要是概念性实现
                
                android.util.Log.d("ScrollEnhancement", 
                    "应用惯性衰减，衰减系数: " + INERTIA_DECAY_FACTOR);
            }
        });
    }
    
    /**
     * 创建"Swipe up to see more"加载提示
     */
    public static View createSwipeUpHint(Context context) {
        // 创建包含APP图标和文字的布局
        android.widget.LinearLayout layout = new android.widget.LinearLayout(context);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setGravity(android.view.Gravity.CENTER);
        layout.setPadding(32, 32, 32, 32);
        
        // APP图标
        android.widget.ImageView iconView = new android.widget.ImageView(context);
        iconView.setImageResource(android.R.drawable.ic_menu_upload); // 使用系统图标，实际应用中替换为APP图标
        android.widget.LinearLayout.LayoutParams iconParams = 
            new android.widget.LinearLayout.LayoutParams(120, 120);
        iconParams.bottomMargin = 16;
        iconView.setLayoutParams(iconParams);
        
        // 提示文字
        android.widget.TextView textView = new android.widget.TextView(context);
        textView.setText("Swipe up to see more");
        textView.setTextSize(14);
        textView.setTextColor(0xFF666666);
        textView.setGravity(android.view.Gravity.CENTER);
        
        layout.addView(iconView);
        layout.addView(textView);
        
        return layout;
    }
    
    /**
     * 快速定位到指定位置
     */
    public static void smoothScrollToPosition(RecyclerView recyclerView, int position) {
        if (recyclerView == null) return;
        
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        if (layoutManager != null) {
            // 使用平滑滚动
            recyclerView.smoothScrollToPosition(position);
            
            android.util.Log.d("ScrollEnhancement", 
                "快速定位到位置: " + position);
        }
    }
    
    /**
     * 检查是否需要显示加载更多
     */
    public static boolean shouldShowLoadMore(RecyclerView recyclerView, int threshold) {
        if (recyclerView == null) return false;
        
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        if (layoutManager == null) return false;
        
        int lastVisiblePosition = layoutManager.findLastVisibleItemPosition();
        int totalItemCount = layoutManager.getItemCount();
        
        // 当滚动到倒数第threshold个item时，触发加载更多
        return lastVisiblePosition >= totalItemCount - threshold;
    }
    
    /**
     * 获取当前滚动进度百分比
     */
    public static int getScrollProgress(RecyclerView recyclerView) {
        if (recyclerView == null) return 0;
        
        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
        if (layoutManager == null) return 0;
        
        int firstVisiblePosition = layoutManager.findFirstVisibleItemPosition();
        int totalItemCount = layoutManager.getItemCount();
        
        if (totalItemCount == 0) return 0;
        
        return (int) ((float) firstVisiblePosition / totalItemCount * 100);
    }
}
