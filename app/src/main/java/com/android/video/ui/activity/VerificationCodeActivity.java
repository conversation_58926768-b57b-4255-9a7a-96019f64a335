package com.android.video.ui.activity;

import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.CountDownTimer;
import android.os.Handler;
import android.os.Looper;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.InputMethodManager;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.content.ContextCompat;
import androidx.constraintlayout.widget.ConstraintLayout;

import com.android.video.R;
import com.android.video.model.UserModel;
import com.android.video.utils.UserSessionUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.LoginResponseModel;
import com.android.video.utils.DeviceIdUtils;

/**
 * 验证码输入页面 - 处理短信验证码输入和验证
 * 继承BaseFullScreenActivity以获得统一的全屏状态栏处理
 * <AUTHOR>
 */
public class VerificationCodeActivity extends BaseFullScreenActivity {

    private static final String EXTRA_PHONE_NUMBER = "phone_number";
    private static final int OTP_LENGTH = 6;
    private static final int COUNTDOWN_SECONDS = 60;

    private ConstraintLayout clRootLayout;
    private ImageView ivLogo;
    private ImageView ivBackButton;
    private ImageView ivBackground;
    private TextView tvTitle;
    private TextView tvSubtitle;
    private TextView tvUserAgreement;
    private LinearLayout llOtpContainer;
    private Button btnSubmit;
    private Button btnSendAgain;

    private EditText[] otpEditTexts;
    private View[] otpLines;
    private Handler mainHandler;
    private CountDownTimer countDownTimer;
    private String phoneNumber;
    private boolean isCountdownActive = false;

    public static Intent createIntent(Context context, String phoneNumber) {
        Intent intent = new Intent(context, VerificationCodeActivity.class);
        intent.putExtra(EXTRA_PHONE_NUMBER, phoneNumber);
        return intent;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_verification_code);

        // Get phone number from intent
        phoneNumber = getIntent().getStringExtra(EXTRA_PHONE_NUMBER);

        mainHandler = new Handler(Looper.getMainLooper());

        initViews();
        setupOtpInputs();
        setupClickListeners();

        // 初始化按钮状态 - 在动画之前设置
        initButtonStates();

        setupAnimations();
        startCountdown();
    }

    private void initViews() {
        clRootLayout = findViewById(R.id.cl_root_layout);
        ivLogo = findViewById(R.id.iv_logo);
        ivBackButton = findViewById(R.id.iv_back_button);
        ivBackground = findViewById(R.id.iv_background);
        tvTitle = findViewById(R.id.tv_title);
        tvSubtitle = findViewById(R.id.tv_subtitle);
        tvUserAgreement = findViewById(R.id.tv_user_agreement);
        llOtpContainer = findViewById(R.id.ll_otp_container);
        btnSubmit = findViewById(R.id.btn_submit);
        btnSendAgain = findViewById(R.id.btn_send_again);

        // Initialize OTP EditTexts and lines
        otpEditTexts = new EditText[OTP_LENGTH];
        otpLines = new View[OTP_LENGTH];

        otpEditTexts[0] = findViewById(R.id.et_otp_1);
        otpEditTexts[1] = findViewById(R.id.et_otp_2);
        otpEditTexts[2] = findViewById(R.id.et_otp_3);
        otpEditTexts[3] = findViewById(R.id.et_otp_4);
        otpEditTexts[4] = findViewById(R.id.et_otp_5);
        otpEditTexts[5] = findViewById(R.id.et_otp_6);

        otpLines[0] = findViewById(R.id.line_1);
        otpLines[1] = findViewById(R.id.line_2);
        otpLines[2] = findViewById(R.id.line_3);
        otpLines[3] = findViewById(R.id.line_4);
        otpLines[4] = findViewById(R.id.line_5);
        otpLines[5] = findViewById(R.id.line_6);
    }

    private void setupOtpInputs() {
        for (int i = 0; i < OTP_LENGTH; i++) {
            final int index = i;
            EditText editText = otpEditTexts[i];

            editText.addTextChangedListener(new TextWatcher() {
                @Override
                public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

                @Override
                public void onTextChanged(CharSequence s, int start, int before, int count) {
                    if (s.length() == 1) {
                        // Highlight current line
                        highlightLine(index, true);
                        
                        // Move to next EditText
                        if (index < OTP_LENGTH - 1) {
                            otpEditTexts[index + 1].requestFocus();
                        } else {
                            // All fields filled, hide keyboard
                            hideKeyboard();
                        }
                    } else if (s.length() == 0) {
                        // Unhighlight current line
                        highlightLine(index, false);
                    }
                }

                @Override
                public void afterTextChanged(Editable s) {}
            });

            editText.setOnKeyListener((v, keyCode, event) -> {
                if (keyCode == KeyEvent.KEYCODE_DEL && event.getAction() == KeyEvent.ACTION_DOWN) {
                    if (editText.getText().toString().isEmpty() && index > 0) {
                        // Move to previous EditText and clear it
                        otpEditTexts[index - 1].requestFocus();
                        otpEditTexts[index - 1].setText("");
                        highlightLine(index - 1, false);
                    }
                }
                return false;
            });

            editText.setOnFocusChangeListener((v, hasFocus) -> {
                if (hasFocus && editText.getText().toString().isEmpty()) {
                    highlightLine(index, false);
                }
            });
        }

        // Focus on first EditText
        otpEditTexts[0].requestFocus();
    }

    private void highlightLine(int index, boolean highlight) {
        if (index >= 0 && index < OTP_LENGTH) {
            View line = otpLines[index];
            if (highlight) {
                line.setBackgroundColor(ContextCompat.getColor(this, R.color.otp_line_focused));
                line.getLayoutParams().height = (int) (1 * getResources().getDisplayMetrics().density);
            } else {
                line.setBackgroundColor(ContextCompat.getColor(this, R.color.otp_line_normal));
                line.getLayoutParams().height = (int) (0.5f * getResources().getDisplayMetrics().density);
            }
            line.requestLayout();
        }
    }

    private void setupClickListeners() {
        btnSubmit.setOnClickListener(v -> {
            animateButton(v);
            handleSubmit();
        });

        btnSendAgain.setOnClickListener(v -> {
            // 总是显示按钮点击动画
            animateButton(v);

            if (!isCountdownActive) {
                handleSendAgain();
            } else {
                Toast.makeText(this, "Please wait patiently for one minute", Toast.LENGTH_SHORT).show();
            }
        });

        ivBackButton.setOnClickListener(v -> handleBackButton());

        // Root layout click to clear focus
        clRootLayout.setOnClickListener(v -> {
            clearAllFocus();
            hideKeyboard();
        });
    }

    private void initButtonStates() {
        // 初始化Send Again按钮状态
        isCountdownActive = true;

        // 重新设计Send Again按钮初始化逻辑
        initSendAgainButton();
    }

    private void initSendAgainButton() {
        // 设置按钮初始状态 - 完全透明且不可见，避免任何闪烁
        btnSendAgain.setVisibility(View.INVISIBLE);
        btnSendAgain.setAlpha(0f);
        btnSendAgain.setTranslationY(50f);

        // 设置初始文本和颜色
        btnSendAgain.setText("Send Again");
        btnSendAgain.setTextColor(ContextCompat.getColor(this, R.color.login_text_gray));
    }

    private void setupAnimations() {
        // Logo entrance animation
        animateLogoEntrance();

        // Add subtle entrance animations for components
        animateTextEntrance(tvTitle, 300);
        animateTextEntrance(tvSubtitle, 400);
        animateButtonEntrance(llOtpContainer, 500);
        animateButtonEntrance(btnSubmit, 600);
        animateButtonEntrance(btnSendAgain, 700);
        animateTextEntrance(tvUserAgreement, 800);
    }

    private void animateLogoEntrance() {
        ivLogo.setAlpha(0f);
        ivLogo.setScaleX(0.8f);
        ivLogo.setScaleY(0.8f);

        ivLogo.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(800)
                .setStartDelay(100)
                .start();
    }

    private void animateTextEntrance(View view, long delay) {
        // 不重复设置alpha，因为XML中已经设置了alpha="0"
        // view.setAlpha(0f); // 移除这行，避免覆盖XML设置
        view.setTranslationY(30f);

        view.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(600)
                .setStartDelay(delay)
                .start();
    }

    private void animateButtonEntrance(View view, long delay) {
        // 特殊处理Send Again按钮
        if (view == btnSendAgain) {
            animateSendAgainButtonEntrance(delay);
        } else {
            // 其他按钮的正常处理
            view.setAlpha(0f);
            view.setTranslationY(50f);

            view.animate()
                    .alpha(1f)
                    .translationY(0f)
                    .setDuration(700)
                    .setStartDelay(delay)
                    .start();
        }
    }

    private void animateSendAgainButtonEntrance(long delay) {
        // 延迟后开始动画，确保不会有闪烁
        mainHandler.postDelayed(() -> {
            // 设置为可见并开始动画
            btnSendAgain.setVisibility(View.VISIBLE);

            // 确定最终的alpha值 - 根据倒计时状态
            float finalAlpha = isCountdownActive ? 0.6f : 1f;

            btnSendAgain.animate()
                    .alpha(finalAlpha)
                    .translationY(0f)
                    .setDuration(700)
                    .start();
        }, delay);
    }

    private void animateButton(View button) {
        // 使用统一的按钮点击动画（0.9倍缩放，0.2秒）
        com.android.video.utils.UIAnimationUtils.animateButtonClick(button);
    }

    private void startCountdown() {
        isCountdownActive = true;
        updateSendAgainButtonState();

        countDownTimer = new CountDownTimer(COUNTDOWN_SECONDS * 1000, 1000) {
            @Override
            public void onTick(long millisUntilFinished) {
                int secondsLeft = (int) (millisUntilFinished / 1000);
                updateSendAgainButtonText("Send Again (" + secondsLeft + ")");
            }

            @Override
            public void onFinish() {
                isCountdownActive = false;
                updateSendAgainButtonText("Send Again");
                updateSendAgainButtonState();
            }
        };
        countDownTimer.start();
    }

    private void updateSendAgainButtonText(String text) {
        if (btnSendAgain != null) {
            btnSendAgain.setText(text);
        }
    }

    private void updateSendAgainButtonState() {
        if (btnSendAgain == null) {
            return;
        }

        if (isCountdownActive) {
            // 倒计时期间 - 半透明灰色状态
            btnSendAgain.animate()
                    .alpha(0.6f)
                    .setDuration(200)
                    .start();
            btnSendAgain.setTextColor(ContextCompat.getColor(this, R.color.login_text_gray));
        } else {
            // 正常状态 - 完全不透明黑色文字
            btnSendAgain.animate()
                    .alpha(1.0f)
                    .setDuration(200)
                    .start();
            btnSendAgain.setTextColor(ContextCompat.getColor(this, R.color.login_button_text_black));
        }
    }

    private void handleSubmit() {
        String otp = getEnteredOtp();
        if (otp.length() == OTP_LENGTH) {
            // 显示验证中状态
            btnSubmit.setEnabled(false);
            btnSubmit.setText("Verifying...");
            Toast.makeText(this, "Verifying OTP: " + otp, Toast.LENGTH_SHORT).show();

            // 所有环境都使用后端API - 已弃用静态认证
            /*
            // 环境判断：dev环境使用静态认证，prod/test环境调用API - 已弃用
            if (EnvironmentConfigUtils.isDevelopment()) {
                // dev环境：使用现有静态认证
                performStaticAuthentication();
            } else {
                // prod/test环境：调用登录API
                performApiAuthentication(otp);
            }
            */

            // 所有环境都调用登录API
            performApiAuthentication(otp);
        } else {
            Toast.makeText(this, "Please enter complete verification code", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * @deprecated 此方法已弃用，所有环境都使用后端API认证
     * 执行静态认证（dev环境）
     */
    @Deprecated
    private void performStaticAuthentication() {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            restoreSubmitButton();
            Toast.makeText(this, "Phone number not found", Toast.LENGTH_SHORT).show();
            return;
        }

        // 使用UserSessionUtils执行登录
        UserModel user = UserSessionUtils.performLogin(this, phoneNumber);

        if (user != null && user.isLoggedIn()) {
            // 显示登录成功信息
            String welcomeMessage = user.isVip() ?
                "Welcome back, " + user.getUsername() + " (VIP)!" :
                "Welcome, " + user.getUsername() + "!";
            Toast.makeText(this, welcomeMessage, Toast.LENGTH_LONG).show();

            // 检查用户是否有收藏标签
            if (UserSessionUtils.hasFavoriteTags(this)) {
                // 用户已有收藏标签，直接跳转到主页面
                mainHandler.postDelayed(this::navigateToMainActivity, 1500);
            } else {
                // 用户没有收藏标签，跳转到收藏选择页面
                mainHandler.postDelayed(this::navigateToFavoriteSelectionActivity, 1500);
            }
        } else {
            restoreSubmitButton();
            Toast.makeText(this, "Login failed, please try again", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 执行API认证（prod/test环境）
     * @param verificationCode 验证码
     */
    private void performApiAuthentication(String verificationCode) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            restoreSubmitButton();
            Toast.makeText(this, "Phone number not found", Toast.LENGTH_SHORT).show();
            return;
        }

        // 提取纯手机号（去除国家代码和格式化）
        String cleanPhoneNumber = extractCleanPhoneNumber(phoneNumber);

        // 调用登录API
        AuthApiUtils.phoneLogin(this, cleanPhoneNumber, verificationCode,
                new AuthApiUtils.ApiCallback<LoginResponseModel>() {
            @Override
            public void onSuccess(LoginResponseModel loginResponse) {
                // 保存登录状态
                saveApiLoginState(loginResponse);

                // 显示登录成功信息
                String welcomeMessage = loginResponse.isVip() ?
                    "Welcome back, " + loginResponse.getDisplayName() + " (VIP)!" :
                    "Welcome, " + loginResponse.getDisplayName() + "!";
                Toast.makeText(VerificationCodeActivity.this, welcomeMessage, Toast.LENGTH_LONG).show();

                // 检查用户是否有收藏标签
                if (UserSessionUtils.hasFavoriteTags(VerificationCodeActivity.this)) {
                    // 用户已有收藏标签，直接跳转到主页面
                    mainHandler.postDelayed(() -> navigateToMainActivity(), 1500);
                } else {
                    // 用户没有收藏标签，跳转到收藏选择页面
                    mainHandler.postDelayed(() -> navigateToFavoriteSelectionActivity(), 1500);
                }
            }

            @Override
            public void onError(String errorMessage) {
                restoreSubmitButton();
                Toast.makeText(VerificationCodeActivity.this,
                              "Login failed: " + errorMessage,
                              Toast.LENGTH_LONG).show();

                // 清空验证码输入
                clearOtpInputs();
            }
        });
    }

    /**
     * 恢复提交按钮状态
     */
    private void restoreSubmitButton() {
        btnSubmit.setEnabled(true);
        btnSubmit.setText("Submit");
    }

    /**
     * 提取纯手机号（去除国家代码和格式化）
     * @param fullPhoneNumber 完整格式化的手机号
     * @return 纯手机号
     */
    private String extractCleanPhoneNumber(String fullPhoneNumber) {
        if (fullPhoneNumber == null) {
            return "";
        }

        // 移除国家代码和格式化字符
        String cleaned = fullPhoneNumber.replaceAll("[^0-9]", "");

        // 如果以国家代码开头，移除它们
        if (cleaned.startsWith("1") && cleaned.length() == 11) {
            // 美国号码：移除开头的1
            return cleaned.substring(1);
        } else if (cleaned.startsWith("7") && cleaned.length() == 11) {
            // 俄罗斯号码：移除开头的7
            return cleaned.substring(1);
        } else if (cleaned.startsWith("86") && cleaned.length() == 13) {
            // 中国号码：移除开头的86
            return cleaned.substring(2);
        }

        return cleaned;
    }

    /**
     * 保存API登录状态
     * @param loginResponse 登录响应
     */
    private void saveApiLoginState(LoginResponseModel loginResponse) {
        // 保存登录状态
        UserSessionUtils.saveLoginState(this, true);

        // 保存用户信息
        if (loginResponse.getUid() != null) {
            UserSessionUtils.saveUserId(this, loginResponse.getUid());
        }
        if (loginResponse.getPhoneNumber() != null) {
            UserSessionUtils.savePhoneNumber(this, loginResponse.getPhoneNumber());
        }

        // 保存VIP状态
        UserSessionUtils.saveVipStatus(this, loginResponse.getUserVip());

        // 更新TokenManager中的token和deviceId
        com.android.video.manager.TokenManager tokenManager =
            com.android.video.manager.TokenManager.getInstance(this);
        tokenManager.updateToken(loginResponse);

        android.util.Log.d("VerificationCodeActivity", "Token已更新到TokenManager");
    }

    /**
     * 清空验证码输入
     */
    private void clearOtpInputs() {
        for (EditText editText : otpEditTexts) {
            editText.setText("");
        }
        if (otpEditTexts.length > 0) {
            otpEditTexts[0].requestFocus();
        }
    }

    /**
     * 导航到主页面
     */
    private void navigateToMainActivity() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    /**
     * 导航到收藏选择页面
     */
    private void navigateToFavoriteSelectionActivity() {
        Intent intent = new Intent(this, FavoriteSelectionActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);
        finish();
    }

    private void handleSendAgain() {
        if (!isCountdownActive) {
            // TODO: Implement resend OTP logic
            Toast.makeText(this, "Verification code sent again", Toast.LENGTH_SHORT).show();
            startCountdown();
        }
    }

    private void handleBackButton() {
        finish();
    }

    private String getEnteredOtp() {
        StringBuilder otp = new StringBuilder();
        for (EditText editText : otpEditTexts) {
            otp.append(editText.getText().toString());
        }
        return otp.toString();
    }

    private void clearAllFocus() {
        for (EditText editText : otpEditTexts) {
            editText.clearFocus();
        }
    }

    private void hideKeyboard() {
        InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
        if (imm != null && getCurrentFocus() != null) {
            imm.hideSoftInputFromWindow(getCurrentFocus().getWindowToken(), 0);
        }
    }



    @Override
    protected void onDestroy() {
        super.onDestroy();
        if (countDownTimer != null) {
            countDownTimer.cancel();
        }
    }
}
