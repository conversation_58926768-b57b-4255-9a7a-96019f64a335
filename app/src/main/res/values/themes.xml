<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.VideoPlayer" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Customize your light theme here. -->
        <!-- <item name="colorPrimary">@color/my_light_primary</item> -->
    </style>

    <style name="Theme.VideoPlayer" parent="Base.Theme.VideoPlayer"/>

    <!-- Login Activity Theme - Full Screen -->
    <style name="Theme.VideoPlayer.NoActionBar" parent="Theme.VideoPlayer">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:navigationBarColor">@android:color/transparent</item>
        <item name="android:windowBackground">@drawable/login_bg</item>
    </style>

    <!-- Main Activity Theme - No ActionBar but keep system bars -->
    <style name="Theme.VideoPlayer.MainActivity" parent="Theme.VideoPlayer">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowBackground">@color/black</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/bottom_nav_background</item>
    </style>

    <!-- Bottom Navigation Text Style - Selected (Active) -->
    <style name="BottomNavigationTextStyle" parent="TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/bottom_nav_text_size</item>
        <item name="android:layout_marginTop">@dimen/bottom_nav_icon_text_margin</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:fontFamily">sans-serif-medium</item> <!-- Roboto Medium -->
        <item name="android:textColor">@color/bottom_nav_selected</item>
    </style>

    <!-- Bottom Navigation Text Style - Unselected (Inactive) -->
    <style name="BottomNavigationTextStyleInactive" parent="TextAppearance.AppCompat.Caption">
        <item name="android:textSize">@dimen/bottom_nav_text_size</item>
        <item name="android:layout_marginTop">@dimen/bottom_nav_icon_text_margin</item>
        <item name="android:paddingTop">2dp</item>
        <item name="android:fontFamily">sans-serif</item> <!-- Roboto Regular -->
        <item name="android:textColor">@color/bottom_nav_unselected</item>
    </style>

    <!-- Custom Bottom Navigation Style -->
    <style name="CustomBottomNavigationView" parent="Widget.MaterialComponents.BottomNavigationView">
        <item name="itemBackground">@drawable/bottom_nav_centered_background</item>
        <item name="android:background">@color/bottom_nav_background</item>
    </style>

    <!-- Custom Dialog Style -->
    <style name="CustomDialog" parent="Theme.AppCompat.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowTitleStyle">@null</item>
        <item name="android:windowAnimationStyle">@android:style/Animation.Dialog</item>
        <item name="android:windowSoftInputMode">stateUnspecified|adjustPan</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:backgroundDimAmount">0.5</item>
    </style>

    <!-- Video Detail Synopsis Text Style -->
    <style name="VideoDetailSynopsisTextStyle">
        <item name="android:fontFamily">sans-serif</item> <!-- Roboto Regular -->
        <item name="android:textStyle">normal</item> <!-- Regular weight -->
        <item name="android:textSize">@dimen/video_detail_synopsis_content_text_size</item> <!-- 14sp -->
        <item name="android:textColor">#CCFFFFFF</item> <!-- 80% transparent white -->
    </style>

    <!-- Continue Playing Button Style -->
    <style name="ContinuePlayingButtonStyle">
        <item name="android:background">@drawable/continue_playing_button_bg</item>
        <item name="android:fontFamily">sans-serif-medium</item> <!-- Roboto Medium -->
        <item name="android:textStyle">normal</item>
        <item name="android:textSize">@dimen/continue_playing_button_text_size</item>
        <item name="android:textColor">@color/continue_playing_button_text</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:elevation">8dp</item>
        <item name="android:stateListAnimator">@null</item>
    </style>

</resources>
