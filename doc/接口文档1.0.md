### 获取APP版本信息

> 创建人: 曹晗

> 更新人: 曹晗

> 创建时间: 2025-07-29 23:43:20

> 更新时间: 2025-07-29 23:58:52

```text
暂无描述
```

**接口状态**

> 开发中

**接口URL**

> /app/customer/appVersion?clientType=1

| 环境  | URL |
| --- | --- |
| 测试环境 | https://short-play-api.gymooit.cn/v1 |

**Mock URL**

> /app/customer/appVersion?apipost_id=4c9de391d496000

**请求方式**

> GET

**Content-Type**

> none

**请求Query参数**

| 参数名 | 示例值 | 参数类型 | 是否必填 | 参数描述 |
| --- | --- | ---- | ---- | ---- |
| clientType | 1 | integer | 否 | 客户端类型  1=安卓 2=IOS |

**认证方式**

> 继承父级

**响应示例**

* 成功(200)

```javascript
{
    "code": "200",
    "message": "success",
    "data": {
        "versionId": "a51ff4564c286d2be002ca107a76d3e6",
        "clientType": 1,
        "versionCode": "v1.0.3",
        "versionStatus": 1,
        "releaseTime": "2025-07-27 00:23:57",
        "isDelete": 1,
        "versionInfo": "First release version",
        "packageUrl": "https://aasdasdasdasd.apk",
        "createTime": "2025-07-26 13:49:55",
        "updateTime": "2025-07-27 00:23:57"
    }
}
```

| 参数名 | 示例值 | 参数类型 | 参数描述 |
| --- | --- | ---- | ---- |
| code | 200 | string | - |
| message | success | string | - |
| data | - | object | 返回数据 |
| data.versionId | a51ff4564c286d2be002ca107a76d3e6 | string | 版本ID |
| data.clientType | 1 | number | 客户端类型  1=安卓 2=IOS |
| data.versionCode | v1.0.3 | string | 版本 |
| data.versionStatus | 1 | number | 发布状态 0=待发布 1=已发布 |
| data.releaseTime | 2025-07-27 00:23:57 | string | 发布时间 |
| data.versionInfo | First release version | string | 版本描述 |
| data.packageUrl | https://aasdasdasdasd.apk | string | 安装包下载路径 |
| data.createTime | 2025-07-26 13:49:55 | string | 创建时间 |
| data.updateTime | 2025-07-27 00:23:57 | string | 修改时间 |

* 失败(404)

```javascript
暂无数据
```

**Query**

