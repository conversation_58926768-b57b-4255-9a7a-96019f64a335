<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.fragment.HomeFragment">

<androidx.core.widget.NestedScrollView
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Banner区域 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_banner_area"
            android:layout_width="match_parent"
            android:layout_height="@dimen/home_banner_height">

            <!-- 背景海报 -->
            <ImageView
                android:id="@+id/iv_banner_poster"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleType="centerCrop"
                android:src="@drawable/movie_poster"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <!-- 顶部渐变遮罩 -->
            <View
                android:id="@+id/view_top_gradient"
                android:layout_width="0dp"
                android:layout_height="@dimen/home_top_gradient_height"
                android:background="@drawable/home_top_gradient"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- Logo -->
            <ImageView
                android:id="@+id/iv_home_logo"
                android:layout_width="@dimen/home_logo_width"
                android:layout_height="@dimen/home_logo_height"
                android:layout_marginTop="@dimen/home_logo_margin_top"
                android:layout_marginStart="@dimen/home_logo_margin_start"
                android:scaleType="centerInside"
                android:src="@drawable/home_logo"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <!-- 搜索图标 -->
            <ImageView
                android:id="@+id/iv_search_icon"
                android:layout_width="@dimen/home_search_icon_size"
                android:layout_height="@dimen/home_search_icon_size"
                android:layout_marginTop="@dimen/home_search_icon_margin_top"
                android:layout_marginEnd="@dimen/home_search_icon_margin_end"
                android:scaleType="centerInside"
                android:src="@drawable/home_ic_search"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <!-- 轮播图容器 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_carousel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/home_carousel_margin_start"
                android:layout_marginBottom="@dimen/home_carousel_margin_bottom"
                android:orientation="horizontal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintBottom_toTopOf="@id/view_banner_gradient" />

            <!-- 播放按钮 -->
            <ImageView
                android:id="@+id/iv_play_button"
                android:layout_width="@dimen/home_play_icon_size"
                android:layout_height="@dimen/home_play_icon_size"
                android:layout_marginEnd="@dimen/home_play_icon_margin_end"
                android:layout_marginBottom="@dimen/home_play_icon_margin_bottom"
                android:scaleType="centerInside"
                android:src="@drawable/home_ic_play"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toTopOf="@id/view_banner_gradient" />

            <!-- 底部渐变 -->
            <View
                android:id="@+id/view_banner_gradient"
                android:layout_width="0dp"
                android:layout_height="@dimen/home_gradient_height"
                android:background="@drawable/home_banner_gradient"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- Categories区域 -->
        <LinearLayout
            android:id="@+id/ll_categories_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_categories_margin_top">

            <!-- Categories标题行 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_categories_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Categories"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_categories_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/iv_categories_next"
                    android:layout_width="@dimen/home_next_icon_size"
                    android:layout_height="@dimen/home_next_icon_size"
                    android:layout_marginEnd="@dimen/home_see_all_margin_end"
                    android:alpha="0.64"
                    android:scaleType="centerInside"
                    android:src="@drawable/home_ic_next"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/tv_categories_see_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/home_next_icon_margin"
                    android:text="See All"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_see_all_text_size"
                    android:fontFamily="sans-serif-light"
                    android:alpha="0.64"
                    android:clickable="true"
                    android:focusable="true"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_categories_next"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- 标签选择器 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_category_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_tag_margin_top"
                android:layout_marginStart="@dimen/home_categories_margin_start"
                android:orientation="horizontal"
                android:clipToPadding="false"
                android:paddingEnd="@dimen/home_categories_margin_start" />

            <!-- 视频网格 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_video_grid"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_video_item_spacing_vertical"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false" />

        </LinearLayout>

        <!-- Continue Watching区域 -->
        <LinearLayout
            android:id="@+id/ll_continue_watching_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_continue_watching_margin_top">

            <!-- Continue Watching标题行 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_continue_watching_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Continue Watching"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_categories_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/iv_continue_watching_next"
                    android:layout_width="@dimen/home_next_icon_size"
                    android:layout_height="@dimen/home_next_icon_size"
                    android:layout_marginEnd="@dimen/home_see_all_margin_end"
                    android:alpha="0.64"
                    android:scaleType="centerInside"
                    android:src="@drawable/home_ic_next"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/tv_continue_watching_see_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/home_next_icon_margin"
                    android:text="See All"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_see_all_text_size"
                    android:fontFamily="sans-serif-light"
                    android:alpha="0.64"
                    android:clickable="true"
                    android:focusable="true"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_continue_watching_next"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Continue Watching视频列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_continue_watching"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_continue_watching_list_margin_top"
                android:layout_marginStart="@dimen/home_categories_margin_start"
                android:orientation="horizontal"
                android:clipToPadding="false"
                android:paddingEnd="@dimen/home_categories_margin_start"
                android:paddingBottom="@dimen/layout_margin_large" />

        </LinearLayout>

        <!-- Coming Soon区域 -->
        <LinearLayout
            android:id="@+id/ll_coming_soon_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_module_section_margin_top">

            <!-- Coming Soon标题行 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_coming_soon_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Coming Soon"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_title_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/iv_coming_soon_next"
                    android:layout_width="@dimen/home_next_icon_size"
                    android:layout_height="@dimen/home_next_icon_size"
                    android:layout_marginEnd="@dimen/home_see_all_margin_end"
                    android:alpha="0.64"
                    android:scaleType="centerInside"
                    android:src="@drawable/home_ic_next"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/tv_coming_soon_see_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/home_next_icon_margin"
                    android:text="See All"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_see_all_text_size"
                    android:fontFamily="sans-serif-light"
                    android:alpha="0.64"
                    android:clickable="true"
                    android:focusable="true"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_coming_soon_next"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Coming Soon视频列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_coming_soon"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_module_list_margin_top"
                android:layout_marginStart="@dimen/home_video_item_spacing_horizontal"
                android:orientation="horizontal"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingEnd="@dimen/home_video_item_spacing_horizontal" />

        </LinearLayout>

        <!-- Best For You区域 -->
        <LinearLayout
            android:id="@+id/ll_best_for_you_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_module_section_margin_top">

            <!-- Best For You标题行 (无See All) -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_best_for_you_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Best For You"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_title_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Best For You视频列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_best_for_you"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_module_list_margin_top"
                android:layout_marginStart="@dimen/home_video_item_spacing_horizontal"
                android:orientation="horizontal"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingEnd="@dimen/home_video_item_spacing_horizontal" />

        </LinearLayout>

        <!-- Today's Hot区域 -->
        <LinearLayout
            android:id="@+id/ll_todays_hot_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_module_section_margin_top">

            <!-- Today's Hot标题行 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_todays_hot_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Today's Hot"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_title_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/iv_todays_hot_next"
                    android:layout_width="@dimen/home_next_icon_size"
                    android:layout_height="@dimen/home_next_icon_size"
                    android:layout_marginEnd="@dimen/home_see_all_margin_end"
                    android:alpha="0.64"
                    android:scaleType="centerInside"
                    android:src="@drawable/home_ic_next"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/tv_todays_hot_see_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/home_next_icon_margin"
                    android:text="See All"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_see_all_text_size"
                    android:fontFamily="sans-serif-light"
                    android:alpha="0.64"
                    android:clickable="true"
                    android:focusable="true"
                    android:visibility="gone"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_todays_hot_next"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Today's Hot视频列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_todays_hot"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_module_list_margin_top"
                android:layout_marginStart="@dimen/home_video_item_spacing_horizontal"
                android:orientation="horizontal"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingEnd="@dimen/home_video_item_spacing_horizontal" />

        </LinearLayout>

        <!-- 动态推荐位容器 -->
        <LinearLayout
            android:id="@+id/ll_dynamic_featured_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical" />

        <!-- Popular Series区域 -->
        <LinearLayout
            android:id="@+id/ll_popular_series_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="@dimen/home_module_section_margin_top">

            <!-- Popular Series标题行 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <TextView
                    android:id="@+id/tv_popular_series_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/home_categories_margin_start"
                    android:text="Most Popular"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_title_text_size"
                    android:fontFamily="sans-serif-medium"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <ImageView
                    android:id="@+id/iv_popular_series_next"
                    android:layout_width="@dimen/home_next_icon_size"
                    android:layout_height="@dimen/home_next_icon_size"
                    android:layout_marginEnd="@dimen/home_see_all_margin_end"
                    android:alpha="0.64"
                    android:scaleType="centerInside"
                    android:src="@drawable/home_ic_next"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent" />

                <TextView
                    android:id="@+id/tv_popular_series_see_all"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginEnd="@dimen/home_next_icon_margin"
                    android:text="See All"
                    android:textColor="@color/white"
                    android:textSize="@dimen/home_module_see_all_text_size"
                    android:fontFamily="sans-serif-light"
                    android:alpha="0.64"
                    android:clickable="true"
                    android:focusable="true"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintEnd_toStartOf="@id/iv_popular_series_next"
                    app:layout_constraintBottom_toBottomOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <!-- Popular Series视频列表 (垂直布局) -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_popular_series"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/home_module_list_margin_top"
                android:orientation="vertical"
                android:nestedScrollingEnabled="false"
                android:clipToPadding="false"
                android:paddingBottom="@dimen/layout_margin_large" />

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
