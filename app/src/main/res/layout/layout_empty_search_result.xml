<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center"
    android:paddingHorizontal="@dimen/empty_state_padding_horizontal">

    <!-- 空状态图片 -->
    <ImageView
        android:layout_width="@dimen/empty_state_image_width"
        android:layout_height="@dimen/empty_state_image_height"
        android:layout_gravity="center"
        android:src="@drawable/empty"
        android:scaleType="centerInside" />

    <!-- 空状态文字 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/empty_state_text_margin_top"
        android:text="No related content"
        android:textColor="#ff888888"
        android:textSize="@dimen/empty_state_text_size"
        android:gravity="center" />

</LinearLayout>
