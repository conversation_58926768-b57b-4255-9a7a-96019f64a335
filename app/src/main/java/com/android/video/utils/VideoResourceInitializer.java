package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import com.android.video.model.TestVideoModel;
import java.util.List;

/**
 * 视频资源初始化工具类
 * 负责在应用启动时初始化视频资源管理器
 * <AUTHOR> Team
 */
public class VideoResourceInitializer {

    private static final String TAG = "VideoResourceInitializer";
    private static boolean isInitialized = false;

    /**
     * 初始化视频资源
     * @param context 应用上下文
     * @return 是否初始化成功
     */
    public static boolean initialize(Context context) {
        if (isInitialized) {
            Log.d(TAG, "Video resources already initialized");
            return true;
        }

        try {
            Log.d(TAG, "Starting video resource initialization...");

            // 初始化VideoResourceManager
            VideoResourceManager manager = VideoResourceManager.getInstance();
            manager.initialize(context);

            // 验证初始化结果
            if (manager.isInitialized()) {
                List<TestVideoModel> videos = manager.getAllTestVideos();
                Log.d(TAG, "Video resource initialization completed successfully");
                Log.d(TAG, "Total videos loaded: " + videos.size());
                Log.d(TAG, "Valid videos: " + manager.getValidVideoCount());
                
                // 打印视频列表
                printVideoList(videos);
                
                // 检查资源完整性
                boolean integrityCheck = manager.checkResourceIntegrity();
                Log.d(TAG, "Resource integrity check: " + (integrityCheck ? "PASSED" : "FAILED"));
                
                isInitialized = true;
                return true;
            } else {
                Log.e(TAG, "VideoResourceManager initialization failed");
                return false;
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize video resources", e);
            return false;
        }
    }

    /**
     * 检查是否已初始化
     * @return 是否已初始化
     */
    public static boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 强制重新初始化
     * @param context 应用上下文
     * @return 是否重新初始化成功
     */
    public static boolean forceReinitialize(Context context) {
        Log.d(TAG, "Force reinitializing video resources...");
        isInitialized = false;
        
        // 清理现有资源
        VideoResourceManager manager = VideoResourceManager.getInstance();
        manager.clearAllVideos();
        manager.clearThumbnailCache();
        
        // 重新初始化
        return initialize(context);
    }

    /**
     * 打印视频列表
     * @param videos 视频列表
     */
    private static void printVideoList(List<TestVideoModel> videos) {
        Log.d(TAG, "=== Video List ===");
        for (int i = 0; i < videos.size(); i++) {
            TestVideoModel video = videos.get(i);
            Log.d(TAG, String.format("[%d] %s - %s (%s) - Valid: %s", 
                i + 1, 
                video.getTitle(), 
                video.getResolution(),
                video.getDurationText(),
                video.isValid() ? "YES" : "NO"
            ));
        }
        Log.d(TAG, "=== End Video List ===");
    }

    /**
     * 获取初始化状态报告
     * @return 状态报告字符串
     */
    public static String getInitializationReport() {
        if (!isInitialized) {
            return "Video resources not initialized";
        }

        VideoResourceManager manager = VideoResourceManager.getInstance();
        StringBuilder report = new StringBuilder();
        
        report.append("=== Video Resource Initialization Report ===\n");
        report.append("Status: INITIALIZED\n");
        report.append("Manager Status: ").append(manager.isInitialized() ? "OK" : "ERROR").append("\n");
        report.append("Statistics: ").append(manager.getStatistics()).append("\n");
        
        List<String> errors = manager.getErrorMessages();
        if (!errors.isEmpty()) {
            report.append("Errors:\n");
            for (String error : errors) {
                report.append("  - ").append(error).append("\n");
            }
        } else {
            report.append("No errors found\n");
        }
        
        report.append("=== End Report ===");
        
        return report.toString();
    }

    /**
     * 验证视频资源完整性
     * @return 验证结果
     */
    public static ValidationResult validateResources() {
        if (!isInitialized) {
            return new ValidationResult(false, "Video resources not initialized");
        }

        VideoResourceManager manager = VideoResourceManager.getInstance();
        List<TestVideoModel> videos = manager.getAllTestVideos();
        
        int totalVideos = videos.size();
        int validVideos = 0;
        int invalidVideos = 0;
        StringBuilder issues = new StringBuilder();

        for (TestVideoModel video : videos) {
            if (video.isValid()) {
                validVideos++;
            } else {
                invalidVideos++;
                issues.append("Invalid video: ").append(video.getTitle()).append("\n");
            }
        }

        boolean isValid = invalidVideos == 0 && totalVideos > 0;
        String message = String.format("Total: %d, Valid: %d, Invalid: %d", 
                                     totalVideos, validVideos, invalidVideos);
        
        if (issues.length() > 0) {
            message += "\nIssues:\n" + issues.toString();
        }

        return new ValidationResult(isValid, message);
    }

    /**
     * 验证结果类
     */
    public static class ValidationResult {
        private final boolean isValid;
        private final String message;

        public ValidationResult(boolean isValid, String message) {
            this.isValid = isValid;
            this.message = message;
        }

        public boolean isValid() {
            return isValid;
        }

        public String getMessage() {
            return message;
        }

        @Override
        public String toString() {
            return "ValidationResult{" +
                    "isValid=" + isValid +
                    ", message='" + message + '\'' +
                    '}';
        }
    }

    /**
     * 清理资源
     */
    public static void cleanup() {
        if (isInitialized) {
            VideoResourceManager manager = VideoResourceManager.getInstance();
            manager.clearThumbnailCache();
            Log.d(TAG, "Video resources cleaned up");
        }
    }

    /**
     * 获取快速状态检查
     * @return 状态字符串
     */
    public static String getQuickStatus() {
        if (!isInitialized) {
            return "NOT_INITIALIZED";
        }

        VideoResourceManager manager = VideoResourceManager.getInstance();
        if (!manager.isInitialized()) {
            return "MANAGER_ERROR";
        }

        int validCount = manager.getValidVideoCount();
        int totalCount = manager.getVideoCount();
        
        if (totalCount == 0) {
            return "NO_VIDEOS";
        } else if (validCount == totalCount) {
            return "ALL_VALID";
        } else if (validCount > 0) {
            return "PARTIAL_VALID";
        } else {
            return "ALL_INVALID";
        }
    }

    /**
     * 执行完整的诊断检查
     * @param context 应用上下文
     * @return 诊断报告
     */
    public static String runDiagnostics(Context context) {
        StringBuilder diagnostics = new StringBuilder();
        
        diagnostics.append("=== Video Resource Diagnostics ===\n");
        diagnostics.append("Timestamp: ").append(System.currentTimeMillis()).append("\n");
        diagnostics.append("Quick Status: ").append(getQuickStatus()).append("\n");
        
        if (!isInitialized) {
            diagnostics.append("Attempting initialization...\n");
            boolean initResult = initialize(context);
            diagnostics.append("Initialization Result: ").append(initResult ? "SUCCESS" : "FAILED").append("\n");
        }
        
        if (isInitialized) {
            ValidationResult validation = validateResources();
            diagnostics.append("Validation: ").append(validation.isValid() ? "PASSED" : "FAILED").append("\n");
            diagnostics.append("Details: ").append(validation.getMessage()).append("\n");
            
            VideoResourceManager manager = VideoResourceManager.getInstance();
            diagnostics.append("Manager Statistics: ").append(manager.getStatistics()).append("\n");
            
            List<String> errors = manager.getErrorMessages();
            if (!errors.isEmpty()) {
                diagnostics.append("Errors Found:\n");
                for (String error : errors) {
                    diagnostics.append("  - ").append(error).append("\n");
                }
            }
        }
        
        diagnostics.append("=== End Diagnostics ===");
        
        return diagnostics.toString();
    }
}
