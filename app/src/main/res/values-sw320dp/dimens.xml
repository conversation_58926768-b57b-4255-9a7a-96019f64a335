<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Small phone specific dimensions (320dp width, scale factor: 0.9) -->

    <!-- Elevation Values - Small screen (320dp) -->
    <dimen name="elevation_none">0dp</dimen>
    <dimen name="elevation_low">1.5dp</dimen>
    <dimen name="elevation_medium">3dp</dimen>
    <dimen name="elevation_high">6dp</dimen>

    <!-- Video Player Fixed Values - Small screen (320dp) -->
    <dimen name="video_player_divider_height">0.8dp</dimen>
    <dimen name="video_player_progress_height">3dp</dimen>
    <dimen name="video_player_thumb_offset">0dp</dimen>
    <dimen name="video_player_control_margin_end">1.5dp</dimen>
    <dimen name="video_player_popup_margin">12dp</dimen>
    <dimen name="video_player_selection_bottom_margin">160dp</dimen>
    <dimen name="video_player_speed_selection_offset">56dp</dimen>

    <!-- Back button dimensions -->
    <dimen name="login_back_button_size">32dp</dimen>
    <dimen name="login_back_button_margin_top">43dp</dimen>
    <dimen name="login_back_button_margin_start">14dp</dimen>

    <!-- Logo dimensions -->
    <dimen name="login_logo_width">231dp</dimen>
    <dimen name="login_logo_height">120dp</dimen>
    <dimen name="login_logo_margin_top">108dp</dimen>

    <!-- Login button dimensions -->
    <dimen name="login_button_width">257dp</dimen>
    <dimen name="login_button_height">41dp</dimen>
    <dimen name="login_button_corner_radius">21dp</dimen>
    <dimen name="login_button_margin_horizontal">20dp</dimen>
    <dimen name="login_button_margin_bottom">14dp</dimen>
    <dimen name="login_button_elevation">3dp</dimen>

    <!-- Icon dimensions -->
    <dimen name="login_icon_size">21dp</dimen>
    <dimen name="login_icon_margin_end">11dp</dimen>

    <!-- Text dimensions -->
    <dimen name="login_text_size">14sp</dimen>
    <dimen name="login_text_line_height">24dp</dimen>
    <dimen name="login_privacy_text_size">11sp</dimen>
    <dimen name="login_privacy_line_height">15dp</dimen>
    <dimen name="login_agreement_text_size">11sp</dimen>
    <dimen name="login_agreement_line_height">15dp</dimen>

    <!-- Privacy text area dimensions -->
    <dimen name="login_privacy_text_width">296dp</dimen>
    <dimen name="login_privacy_text_height">76dp</dimen>
    <dimen name="login_privacy_text_margin_horizontal">14dp</dimen>
    <dimen name="login_privacy_text_margin_top">117dp</dimen>

    <!-- Agreement text area dimensions -->
    <dimen name="login_agreement_text_width">296dp</dimen>
    <dimen name="login_agreement_text_height">32dp</dimen>
    <dimen name="login_agreement_text_margin_horizontal">14dp</dimen>
    <dimen name="login_agreement_text_margin_top">5dp</dimen>
    <dimen name="login_agreement_text_margin_bottom">14dp</dimen>

    <!-- Content padding -->
    <dimen name="login_content_padding">14dp</dimen>

    <!-- Phone Login Dimensions - Small phone (scale factor: 0.9) -->
    <dimen name="phone_login_title_margin_top">0dp</dimen>
    <dimen name="phone_input_width">296dp</dimen>
    <dimen name="phone_input_height">47dp</dimen>
    <dimen name="phone_input_margin_top">52dp</dimen>
    <dimen name="send_code_button_width">296dp</dimen>
    <dimen name="send_code_button_height">36dp</dimen>
    <dimen name="send_code_button_margin_top">48dp</dimen>

    <!-- Bottom Navigation Dimensions - Small phone -->
    <dimen name="bottom_nav_height">58dp</dimen>
    <dimen name="bottom_nav_icon_size">21dp</dimen>
    <dimen name="bottom_nav_text_size">10sp</dimen>
    <dimen name="bottom_nav_icon_text_margin">9dp</dimen>
    <dimen name="bottom_nav_item_padding_vertical">7dp</dimen>
    <dimen name="bottom_nav_item_padding_horizontal">11dp</dimen>

    <!-- Bottom Navigation Selected Background Dimensions -->
    <dimen name="bottom_nav_selected_bg_width">41dp</dimen>
    <dimen name="bottom_nav_selected_bg_height">28dp</dimen>
    <dimen name="bottom_nav_selected_bg_radius">11dp</dimen>

    <!-- Bottom Navigation Layout Positioning -->
    <dimen name="bottom_nav_selected_bg_margin_top">3dp</dimen>
    <dimen name="bottom_nav_text_margin_from_rect">1dp</dimen>
    <dimen name="bottom_nav_text_margin_bottom">4dp</dimen>



    <!-- VIP Card Content Positioning -->
    <dimen name="profile_vip_title_width">258dp</dimen>
    <dimen name="profile_vip_title_height">20dp</dimen>
    <dimen name="profile_vip_title_margin_start">14dp</dimen>
    <dimen name="profile_vip_title_margin_top">30dp</dimen>

    <dimen name="profile_vip_star_margin_start">14dp</dimen>
    <dimen name="profile_vip_star_margin_top">68dp</dimen>

    <dimen name="profile_vip_text_width">163dp</dimen>
    <dimen name="profile_vip_text_height">14dp</dimen>
    <dimen name="profile_vip_text_margin_top">68dp</dimen>

    <dimen name="profile_vip_button_width">73dp</dimen>
    <dimen name="profile_vip_button_height">32dp</dimen>
    <dimen name="profile_vip_button_margin_start">206dp</dimen>
    <dimen name="profile_vip_button_margin_top">59dp</dimen>



    <!-- Refill Button Dimensions -->
    <dimen name="profile_points_refill_button_corner_radius">16dp</dimen>
    <dimen name="profile_points_refill_text_line_height">16dp</dimen>

    <!-- Menu Items Dimensions - Small phone (scale factor: 0.9) -->
    <dimen name="profile_menu_corner_radius">11dp</dimen>

    <!-- Subscribe Page Dimensions - Small phone (320dp width, scale factor: 0.9) -->
    <!-- Precise positioning based on design specs -->
    <dimen name="subscribe_title_width">133dp</dimen>
    <dimen name="subscribe_title_height">32dp</dimen>
    <dimen name="subscribe_title_margin_start">96dp</dimen>
    <dimen name="subscribe_title_margin_top">74dp</dimen>
    <dimen name="subscribe_title_text_size">28sp</dimen>

    <dimen name="subscribe_redeem_icon_width">19dp</dimen>
    <dimen name="subscribe_redeem_icon_height">20dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_start">213dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_top">45dp</dimen>

    <dimen name="subscribe_redeem_text_width">77dp</dimen>
    <dimen name="subscribe_redeem_text_height">14dp</dimen>
    <dimen name="subscribe_redeem_text_margin_start">236dp</dimen>
    <dimen name="subscribe_redeem_text_margin_top">48dp</dimen>
    <dimen name="subscribe_redeem_text_size">12sp</dimen>

    <dimen name="subscribe_watch_more_width">165dp</dimen>
    <dimen name="subscribe_watch_more_height">18dp</dimen>
    <dimen name="subscribe_watch_more_margin_start">80dp</dimen>
    <dimen name="subscribe_watch_more_margin_top">119dp</dimen>
    <dimen name="subscribe_watch_more_text_size">15sp</dimen>

    <dimen name="subscribe_decoration_size">14dp</dimen>
    <dimen name="subscribe_decoration_left_margin_start">55dp</dimen>
    <dimen name="subscribe_decoration_right_margin_start">255dp</dimen>
    <dimen name="subscribe_decoration_margin_top">123dp</dimen>

    <!-- Subscription Cards - Legacy (replaced by new responsive dimensions) -->

    <!-- Card Text Sizes -->
    <dimen name="subscribe_plan_title_text_size">15sp</dimen>
    <dimen name="subscribe_plan_subtitle_text_size">12sp</dimen>
    <dimen name="subscribe_plan_description_text_size">11sp</dimen>
    <dimen name="subscribe_price_text_size">21sp</dimen>
    <dimen name="subscribe_discount_text_size">11sp</dimen>

    <!-- Discount Badge -->
    <dimen name="subscribe_discount_badge_width">77dp</dimen>
    <dimen name="subscribe_discount_badge_height">17dp</dimen>
    <dimen name="subscribe_discount_badge_corner_radius">10dp</dimen>

    <!-- VIP Privileges -->
    <dimen name="subscribe_privilege_icon_size">41dp</dimen>
    <dimen name="subscribe_privilege_text_size">12sp</dimen>
    <dimen name="subscribe_privilege_margin">14dp</dimen>

    <!-- Subscribe Button - Legacy (replaced by new responsive dimensions) -->

    <!-- Tag Dimensions - Small phone (scale factor: 0.9) -->
    <dimen name="tag_margin">5dp</dimen>
    <dimen name="tag_padding_horizontal">14dp</dimen>
    <dimen name="tag_padding_vertical">7dp</dimen>
    <dimen name="tag_text_size">12sp</dimen>
    <dimen name="tag_corner_radius">12dp</dimen>
    <dimen name="tag_border_width">1dp</dimen>

    <!-- Favorite Selection Page Dimensions - Small phone (scale factor: 0.9) -->
    <dimen name="favorite_selection_top_shadow_height">38dp</dimen>
    <dimen name="favorite_selection_title_margin_top">81dp</dimen>
    <dimen name="favorite_selection_title_text_size">19sp</dimen>
    <dimen name="favorite_selection_description_margin_top">11dp</dimen>
    <dimen name="favorite_selection_description_text_size">19sp</dimen>
    <dimen name="favorite_selection_viewpager_margin_top">34dp</dimen>
    <dimen name="favorite_selection_viewpager_height">259dp</dimen>
    <dimen name="favorite_selection_indicator_margin_top">17dp</dimen>
    <dimen name="favorite_selection_indicator_dot_size">7dp</dimen>
    <dimen name="favorite_selection_indicator_dot_margin">4dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_top">34dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_bottom">34dp</dimen>
    <dimen name="favorite_selection_complete_button_height">41dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_horizontal">28dp</dimen>

    <!-- ConstraintLayout 2.0 Advanced Features Support - Small phone (scale factor: 0.9) -->



    <!-- Unified Layout Margins for ConstraintLayout - Small phone -->
    <dimen name="layout_margin_tiny">4dp</dimen>
    <dimen name="layout_margin_small">7dp</dimen>
    <dimen name="layout_margin_medium">14dp</dimen>
    <dimen name="layout_margin_large">21dp</dimen>
    <dimen name="layout_margin_xlarge">28dp</dimen>

    <!-- Chain Spacing for ConstraintLayout Chains - Small phone -->
    <dimen name="chain_spacing_tiny">4dp</dimen>
    <dimen name="chain_spacing_small">7dp</dimen>
    <dimen name="chain_spacing_medium">14dp</dimen>
    <dimen name="chain_spacing_large">21dp</dimen>



    <!-- Flow and Grid Layout Support - Small phone -->
    <dimen name="flow_horizontal_gap">11dp</dimen>
    <dimen name="flow_vertical_gap">7dp</dimen>
    <dimen name="grid_column_gap">14dp</dimen>
    <dimen name="grid_row_gap">11dp</dimen>

    <!-- Subscribe Activity Specific -->
    <dimen name="subscribe_scroll_margin_top">156dp</dimen>
    <dimen name="subscribe_content_padding_bottom">86dp</dimen>

    <!-- Favorite Selection Page Indicator Spacing -->
    <dimen name="favorite_selection_indicator_button_spacing">17dp</dimen>

    <!-- Profile Page Dimensions - Small phone (scale factor: 0.9) -->
    <!-- Status bar and header -->
    <dimen name="profile_status_bar_height">17dp</dimen>
    <dimen name="profile_status_bar_margin_start">18dp</dimen>
    <dimen name="profile_status_bar_margin_top">4dp</dimen>
    <dimen name="profile_status_time_width">47dp</dimen>
    <dimen name="profile_status_time_height">15dp</dimen>
    <dimen name="profile_status_icon_margin_top">7dp</dimen>
    <dimen name="profile_status_battery_margin_top">9dp</dimen>

    <!-- User info section -->
    <dimen name="profile_avatar_size">59dp</dimen>
    <dimen name="profile_avatar_margin_start">14dp</dimen>
    <dimen name="profile_avatar_margin_top">39dp</dimen>
    <dimen name="profile_username_width">48dp</dimen>
    <dimen name="profile_username_height">21dp</dimen>
    <dimen name="profile_username_margin_start">83dp</dimen>
    <dimen name="profile_username_margin_top">48dp</dimen>
    <dimen name="profile_uid_width">80dp</dimen>
    <dimen name="profile_uid_height">14dp</dimen>
    <dimen name="profile_uid_margin_start">83dp</dimen>
    <dimen name="profile_uid_margin_top">71dp</dimen>
    <dimen name="profile_copy_icon_size">12dp</dimen>
    <dimen name="profile_copy_margin_start">167dp</dimen>
    <dimen name="profile_copy_margin_top">76dp</dimen>
    <dimen name="profile_login_button_width">63dp</dimen>
    <dimen name="profile_login_button_height">32dp</dimen>
    <dimen name="profile_login_button_margin_top">48dp</dimen>
    <dimen name="profile_login_button_margin_end">14dp</dimen>

    <!-- VIP card -->
    <dimen name="profile_vip_card_height">144dp</dimen>
    <dimen name="profile_vip_card_margin_horizontal">14dp</dimen>
    <dimen name="profile_vip_card_margin_top">113dp</dimen>
    <dimen name="profile_vip_card_content_margin">14dp</dimen>
    <dimen name="profile_vip_card_type_margin_top">32dp</dimen>
    <dimen name="profile_vip_card_corner_radius">14dp</dimen>
    <dimen name="profile_vip_card_stroke_width">1dp</dimen>
    <dimen name="profile_vip_card_type_width">274dp</dimen>
    <dimen name="profile_vip_card_type_height">22dp</dimen>
    <dimen name="profile_vip_card_type_use_width">56dp</dimen>
    <dimen name="profile_vip_card_type_use_height">40dp</dimen>
    <dimen name="profile_vip_card_type_use_margin_top">42dp</dimen>
    <dimen name="profile_unlimited_access_icon_size">22dp</dimen>
    <dimen name="profile_vip_star_size">14dp</dimen>
    <dimen name="profile_vip_renewal_icon_size">17dp</dimen>

    <!-- VIP Card Edit Button -->
    <dimen name="profile_edit_button_width">52dp</dimen>
    <dimen name="profile_edit_button_height">28dp</dimen>
    <dimen name="profile_edit_text_size">12sp</dimen>
    <dimen name="profile_auto_renewal_text_size">12sp</dimen>
    <dimen name="profile_renewal_period_text_size">11sp</dimen>

    <!-- Points section -->
    <dimen name="profile_points_card_height">86dp</dimen>
    <dimen name="profile_points_card_margin_top">268dp</dimen>
    <dimen name="profile_points_card_padding">14dp</dimen>
    <dimen name="profile_points_icon_size">21dp</dimen>
    <dimen name="profile_points_text_size">14sp</dimen>
    <dimen name="profile_points_value_size">17sp</dimen>

    <!-- VIP Card Internal Elements -->
    <dimen name="profile_vip_star_margin_bottom">46dp</dimen>
    <dimen name="profile_vip_star_second_margin_bottom">14dp</dimen>
    <dimen name="profile_vip_go_button_width">69dp</dimen>
    <dimen name="profile_vip_go_button_height">28dp</dimen>
    <dimen name="profile_vip_renewal_text_width">106dp</dimen>
    <dimen name="profile_vip_renewal_text_height">14dp</dimen>
    <dimen name="profile_vip_renewal_period_width">85dp</dimen>
    <dimen name="profile_vip_renewal_period_height">12dp</dimen>

    <!-- Points section additional -->
    <dimen name="profile_points_jinbi_size">17dp</dimen>
    <dimen name="profile_points_text_line_height">21dp</dimen>
    <dimen name="profile_points_refill_button_width">69dp</dimen>
    <dimen name="profile_points_refill_button_height">28dp</dimen>
    <dimen name="profile_points_refill_text_size">12sp</dimen>

    <!-- Menu items -->
    <dimen name="profile_menu_item_height">49dp</dimen>
    <dimen name="profile_menu_item_padding">14dp</dimen>
    <dimen name="profile_menu_icon_size">21dp</dimen>
    <dimen name="profile_menu_text_size">14sp</dimen>
    <dimen name="profile_menu_text_margin_start">14dp</dimen>
    <dimen name="profile_menu_arrow_size">14dp</dimen>
    <dimen name="profile_menu_margin_horizontal">14dp</dimen>
    <dimen name="profile_menu_margin_vertical">7dp</dimen>
    <dimen name="profile_menu_divider_height">1dp</dimen>
    <dimen name="profile_menu_divider_margin_start">14dp</dimen>

    <!-- Guidelines for responsive layout -->
    <dimen name="profile_content_margin_start">5dp</dimen>
    <dimen name="profile_content_margin_end">5dp</dimen>
    <dimen name="profile_section_spacing">14dp</dimen>

    <!-- Additional text sizes for consistency -->
    <dimen name="login_title_text_size">21sp</dimen>
    <dimen name="login_input_text_size">14sp</dimen>
    <dimen name="login_button_text_size">14sp</dimen>
    <dimen name="verification_subtitle_text_size">12sp</dimen>
    <dimen name="verification_otp_text_size">21sp</dimen>
    <dimen name="verification_otp_input_height">32dp</dimen>
    <dimen name="verification_otp_line_width">31dp</dimen>
    <dimen name="verification_otp_line_height">0.5dp</dimen>
    <dimen name="verification_otp_line_margin_top">11dp</dimen>

    <!-- Profile Status Bar Icon Sizes - Small phone (scale factor: 0.9) -->
    <dimen name="profile_status_mobile_signal_width">15dp</dimen>
    <dimen name="profile_status_mobile_signal_height">10dp</dimen>
    <dimen name="profile_status_wifi_width">14dp</dimen>
    <dimen name="profile_status_wifi_height">10dp</dimen>
    <dimen name="profile_status_battery_width">16dp</dimen>
    <dimen name="profile_status_battery_height">6dp</dimen>

    <!-- Profile Text Sizes - Small phone (scale factor: 0.9) -->
    <dimen name="profile_status_time_text_size">14sp</dimen>
    <dimen name="profile_username_text_size">18sp</dimen>
    <dimen name="profile_uid_text_size">13sp</dimen>
    <dimen name="profile_login_button_text_size">14sp</dimen>
    <dimen name="profile_vip_text_size">13sp</dimen>
    <dimen name="profile_vip_go_button_text_size">13sp</dimen>
    <dimen name="profile_expiration_date_text_size">9sp</dimen>

    <!-- Profile Small Margins - Small phone -->
    <dimen name="profile_vip_text_margin_start">3dp</dimen>
    <dimen name="profile_points_icon_margin_top">7dp</dimen>
    <dimen name="profile_points_value_margin_start">7dp</dimen>
    <dimen name="profile_expiration_date_margin_end">14dp</dimen>
    <dimen name="profile_expiration_date_margin_bottom">3dp</dimen>

    <!-- Profile Status Bar Icon Margins - Small phone -->
    <dimen name="profile_status_mobile_signal_margin_end">43dp</dimen>
    <dimen name="profile_status_wifi_margin_end">26dp</dimen>

    <!-- Profile Menu Bottom Margin - Small phone -->
    <dimen name="profile_menu_bottom_margin">86dp</dimen>

    <!-- Subscribe Page Dimensions - Small phone (320dp, scale factor: 0.85) -->
    <!-- Header Area -->
    <dimen name="subscribe_header_back_button_size">31dp</dimen>
    <dimen name="subscribe_header_back_button_margin_start">14dp</dimen>
    <dimen name="subscribe_header_back_button_margin_top">37dp</dimen>
    <dimen name="subscribe_header_redeem_code_margin_end">14dp</dimen>
    <dimen name="subscribe_header_redeem_code_margin_top">7dp</dimen>
    <dimen name="subscribe_header_redeem_icon_size">19dp</dimen>
    <dimen name="subscribe_header_redeem_text_size">12sp</dimen>

    <!-- Title Area -->
    <dimen name="subscribe_main_title_text_size">27sp</dimen>
    <dimen name="subscribe_main_title_margin_top">34dp</dimen>
    <dimen name="subscribe_subtitle_text_size">15sp</dimen>
    <dimen name="subscribe_subtitle_margin_top">26dp</dimen>
    <dimen name="subscribe_decoration_icon_size">14dp</dimen>
    <dimen name="subscribe_decoration_margin_horizontal">14dp</dimen>

    <!-- Subscription Cards -->
    <dimen name="subscribe_cards_container_margin_horizontal">10dp</dimen>
    <dimen name="subscribe_cards_container_margin_top">20dp</dimen>
    <dimen name="subscribe_card_height">113dp</dimen>
    <dimen name="subscribe_card_margin_vertical">7dp</dimen>
    <dimen name="subscribe_card_padding">14dp</dimen>
    <dimen name="subscribe_card_corner_radius">15dp</dimen>
    <!-- Banner Area -->
    <dimen name="home_banner_height">450dp</dimen>
    <dimen name="home_logo_width">103dp</dimen>
    <dimen name="home_logo_height">22dp</dimen>
    <dimen name="home_logo_margin_top">49dp</dimen>
    <dimen name="home_logo_margin_start">14dp</dimen>

    <!-- Search Icon -->
    <dimen name="home_search_icon_size">20dp</dimen>
    <dimen name="home_search_icon_margin_top">49dp</dimen>
    <dimen name="home_search_icon_margin_end">14dp</dimen>

    <!-- Carousel -->
    <dimen name="home_carousel_unselected_width">45dp</dimen>
    <dimen name="home_carousel_unselected_height">60dp</dimen>
    <dimen name="home_carousel_selected_width">56dp</dimen>
    <dimen name="home_carousel_selected_height">74dp</dimen>
    <dimen name="home_carousel_margin_bottom">15dp</dimen>
    <dimen name="home_carousel_margin_start">14dp</dimen>
    <dimen name="home_carousel_item_spacing">7dp</dimen>

    <!-- Play Icon -->
    <dimen name="home_play_icon_size">34dp</dimen>
    <dimen name="home_play_icon_margin_bottom">11dp</dimen>
    <dimen name="home_play_icon_margin_end">14dp</dimen>

    <!-- Gradient -->
    <dimen name="home_gradient_height">44dp</dimen>

    <!-- Categories Section -->
    <dimen name="home_categories_margin_top">2dp</dimen>
    <dimen name="home_categories_margin_start">14dp</dimen>
    <dimen name="home_categories_text_size">16sp</dimen>
    <dimen name="home_see_all_text_size">14sp</dimen>
    <dimen name="home_see_all_margin_end">14dp</dimen>
    <dimen name="home_next_icon_size">7dp</dimen>
    <dimen name="home_next_icon_margin">9dp</dimen>

    <!-- Tags -->
    <dimen name="home_tag_height">32dp</dimen>
    <dimen name="home_tag_corner_radius">13dp</dimen>
    <dimen name="home_tag_text_size">13sp</dimen>
    <dimen name="home_tag_padding_horizontal">14dp</dimen>
    <dimen name="home_tag_padding_vertical">9dp</dimen>
    <dimen name="home_tag_margin_top">13dp</dimen>

    <!-- Video Grid -->
    <dimen name="home_video_item_width">153dp</dimen>
    <dimen name="home_video_item_height">204dp</dimen>
    <dimen name="home_video_item_spacing_horizontal">14dp</dimen>
    <dimen name="home_video_item_spacing_vertical">11dp</dimen>
    <dimen name="home_video_title_margin_bottom">12dp</dimen>
    <dimen name="home_video_title_margin_start">11dp</dimen>
    <dimen name="home_video_title_text_size">13sp</dimen>

    <!-- Most Popular Page Specific -->
    <dimen name="most_popular_video_title_text_size">14sp</dimen>

    <!-- Continue Watching Section -->
    <dimen name="home_continue_watching_margin_top">36dp</dimen>
    <dimen name="home_continue_watching_item_width">119dp</dimen>
    <dimen name="home_continue_watching_item_height">158dp</dimen>
    <dimen name="home_continue_watching_item_corner_radius">11dp</dimen>
    <dimen name="home_continue_watching_item_border_width">0.4dp</dimen>
    <dimen name="home_continue_watching_title_margin_top">7dp</dimen>
    <dimen name="home_continue_watching_progress_margin_top">5dp</dimen>
    <dimen name="home_continue_watching_progress_text_size">13sp</dimen>
    <dimen name="home_continue_watching_list_margin_top">16dp</dimen>

    <!-- Redemption Code Dialog - Small phone (320dp, 24dp margin each side) -->
    <dimen name="redeem_dialog_width">272dp</dimen>
    <dimen name="redeem_dialog_height">220dp</dimen>
    <dimen name="redeem_dialog_corner_radius">12dp</dimen>
    <dimen name="redeem_dialog_title_text_size">16sp</dimen>
    <dimen name="redeem_dialog_title_line_height">18dp</dimen>
    <dimen name="redeem_dialog_description_text_size">13sp</dimen>
    <dimen name="redeem_dialog_input_width">240dp</dimen>
    <dimen name="redeem_dialog_input_height">44dp</dimen>
    <dimen name="redeem_dialog_input_corner_radius">10dp</dimen>
    <dimen name="redeem_dialog_input_stroke_width">1dp</dimen>
    <dimen name="redeem_dialog_button_width">240dp</dimen>
    <dimen name="redeem_dialog_button_height">38dp</dimen>
    <dimen name="redeem_dialog_button_corner_radius">19dp</dimen>
    <dimen name="redeem_dialog_button_text_size">15sp</dimen>
    <dimen name="redeem_dialog_button_line_height">20dp</dimen>

    <!-- Additional Redemption Dialog dimensions - Small phone (320dp, scale factor: 0.9) -->
    <dimen name="redeem_dialog_padding">14dp</dimen> <!-- 16 × 0.9 = 14.4, rounded to 14dp -->
    <dimen name="redeem_dialog_title_margin_top">5dp</dimen> <!-- 6 × 0.9 = 5.4, rounded to 5dp -->
    <dimen name="redeem_dialog_description_margin_top">9dp</dimen> <!-- 10 × 0.9 = 9dp -->
    <dimen name="redeem_dialog_line_spacing">2dp</dimen> <!-- 2 × 0.9 = 1.8, rounded to 2dp -->
    <dimen name="redeem_dialog_element_margin_top">14dp</dimen> <!-- 16 × 0.9 = 14.4, rounded to 14dp -->
    <dimen name="redeem_dialog_input_padding_horizontal">13dp</dimen> <!-- 14 × 0.9 = 12.6, rounded to 13dp -->

    <!-- New Home Modules Dimensions - Small phone (320dp width, scale factor: 0.9) -->

    <!-- Notify/Subscribe Button -->
    <dimen name="home_notify_button_width">117dp</dimen>
    <dimen name="home_notify_button_height">24dp</dimen>
    <dimen name="home_notify_button_corner_radius">7dp</dimen>
    <dimen name="home_notify_button_text_size">11sp</dimen>
    <dimen name="home_notify_button_margin_top">7dp</dimen>
    <dimen name="home_notify_button_margin_end">7dp</dimen>

    <!-- Match Percentage Label -->
    <dimen name="home_match_percentage_width">32dp</dimen>
    <dimen name="home_match_percentage_height">17dp</dimen>
    <dimen name="home_match_percentage_corner_radius">5dp</dimen>
    <dimen name="home_match_percentage_text_size">9sp</dimen>
    <dimen name="home_match_percentage_margin_start">7dp</dimen>
    <dimen name="home_match_percentage_margin_bottom">7dp</dimen>

    <!-- Ranking Label -->
    <dimen name="home_ranking_width">34dp</dimen>
    <dimen name="home_ranking_height">34dp</dimen>
    <dimen name="home_ranking_corner_radius">5dp</dimen>
    <dimen name="home_ranking_text_size">13sp</dimen>
    <dimen name="home_ranking_margin_start">7dp</dimen>
    <dimen name="home_ranking_margin_top">7dp</dimen>

    <!-- Best For You Item -->
    <dimen name="home_best_for_you_item_width">148dp</dimen>
    <dimen name="home_best_for_you_item_height">197dp</dimen>
    <dimen name="home_best_for_you_item_corner_radius">11dp</dimen>
    <dimen name="home_best_for_you_item_border_width">0.5dp</dimen>
    <dimen name="home_best_for_you_match_margin_top">12dp</dimen>
    <dimen name="home_best_for_you_match_margin_end">14dp</dimen>
    <dimen name="home_best_for_you_title_margin_top">9dp</dimen>
    <dimen name="home_best_for_you_reason_margin_top">5dp</dimen>

    <!-- Popular Series Item -->
    <dimen name="home_popular_series_item_width">101dp</dimen>
    <dimen name="home_popular_series_item_height">135dp</dimen>
    <dimen name="home_popular_series_item_corner_radius">11dp</dimen>
    <dimen name="home_popular_series_item_border_width">0.34dp</dimen>
    <dimen name="home_popular_series_heat_margin_bottom">9dp</dimen>
    <dimen name="home_popular_series_heat_margin_end">11dp</dimen>

    <!-- Coming Soon Detail Page Item (增加8dp宽度，12dp高度) -->
    <dimen name="coming_soon_detail_item_width">109dp</dimen>
    <dimen name="coming_soon_detail_item_height">147dp</dimen>

    <!-- Play Count Text -->
    <dimen name="home_play_count_text_size">11sp</dimen>
    <dimen name="home_play_count_margin_bottom">7dp</dimen>
    <dimen name="home_play_count_margin_end">7dp</dimen>

    <!-- Module Section Spacing -->
    <dimen name="home_module_section_margin_top">29dp</dimen>
    <dimen name="home_module_title_text_size">16sp</dimen>
    <dimen name="home_module_see_all_text_size">14sp</dimen>
    <dimen name="home_module_list_margin_top">14dp</dimen>

    <!-- VIP Background Image - Small phone (320dp, scale factor: 0.85) -->
    <dimen name="vip_bg_width">320dp</dimen>
    <dimen name="vip_bg_height">168dp</dimen>
    <dimen name="vip_bg_margin_top">26dp</dimen>

    <!-- Discount Badge - Small phone (320dp, scale factor: 0.85) -->
    <dimen name="discount_badge_width">75dp</dimen>
    <dimen name="discount_badge_height">17dp</dimen>
    <dimen name="discount_badge_text_size">11sp</dimen>
    <dimen name="discount_badge_padding_horizontal">7dp</dimen>

    <!-- VIP Card Border Frame - Small phone (scale factor: 0.85) -->
    <dimen name="vip_card_border_width">293dp</dimen>
    <dimen name="vip_card_border_height">119dp</dimen>
    <dimen name="vip_card_border_corner_radius">15dp</dimen>
    <dimen name="vip_card_border_stroke_width">1dp</dimen>
    <dimen name="vip_card_border_overflow">3dp</dimen>

    <!-- Subscription Card - Small phone (scale factor: 0.85) -->
    <dimen name="subscribe_card_width">287dp</dimen>
    <dimen name="subscribe_card_price_main_text_size">20sp</dimen>
    <dimen name="subscribe_card_price_unit_text_size">14sp</dimen>

    <!-- Points Page - Small phone (scale factor: 0.85) -->
    <dimen name="points_header_height">75dp</dimen>
    <dimen name="points_header_padding_horizontal">14dp</dimen>
    <dimen name="points_header_padding_top">58dp</dimen>
    <dimen name="points_back_button_size">32dp</dimen> <!-- 36 × 0.9 -->
    <dimen name="points_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="points_title_margin_top">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- Tips部分 -->
    <dimen name="points_tips_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_tips_margin_bottom">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="points_tips_title_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_tips_title_margin_bottom">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="points_tips_subtitle_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="points_tips_subtitle_margin_bottom">7dp</dimen> <!-- 8 × 0.9 -->
    <dimen name="points_tips_content_text_size">11sp</dimen> <!-- 12 × 0.9 -->
    <dimen name="points_tips_content_line_spacing">4dp</dimen> <!-- 4 × 0.9 -->

    <!-- 分割线 -->
    <dimen name="points_divider_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- 积分记录金额样式 -->
    <dimen name="points_record_amount_width">27dp</dimen> <!-- 30 × 0.9 -->
    <dimen name="points_record_amount_height">19dp</dimen> <!-- 21 × 0.9 -->
    <dimen name="points_record_amount_text_size">14sp</dimen> <!-- 16 × 0.9 -->

    <!-- Expenses记录特定样式 -->
    <dimen name="expenses_record_title_width">161dp</dimen> <!-- 179 × 0.9 -->
    <dimen name="expenses_record_title_height">17dp</dimen> <!-- 19 × 0.9 -->
    <dimen name="expenses_record_title_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="expenses_record_subtitle_width">100dp</dimen> <!-- 111 × 0.9 -->
    <dimen name="expenses_record_subtitle_height">15dp</dimen> <!-- 17 × 0.9 -->
    <dimen name="expenses_record_subtitle_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="expenses_record_time_width">122dp</dimen> <!-- 135 × 0.9 -->
    <dimen name="expenses_record_time_height">15dp</dimen> <!-- 17 × 0.9 -->
    <dimen name="expenses_record_time_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="expenses_record_margin_start">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- VIP Privileges样式 -->
    <dimen name="vip_privileges_width">309dp</dimen> <!-- 343 × 0.9 -->
    <dimen name="vip_privileges_height">194dp</dimen> <!-- 216 × 0.9 -->
    <dimen name="vip_privileges_text_size">13sp</dimen> <!-- 14 × 0.9 -->

    <!-- 积分记录列表 -->
    <dimen name="points_list_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_list_margin_bottom">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="points_item_padding">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_item_margin_bottom">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="points_record_padding">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_record_subtitle_margin_top">3dp</dimen> <!-- 3 × 0.9 -->
    <dimen name="points_record_time_margin_top">3dp</dimen> <!-- 3 × 0.9 -->
    <dimen name="points_item_title_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_item_date_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="points_item_date_margin_top">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="points_item_amount_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="points_item_coin_size">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="points_item_coin_margin">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="points_balance_margin_top">27dp</dimen>
    <dimen name="points_balance_margin_bottom">27dp</dimen>
    <dimen name="points_coin_icon_size">27dp</dimen>
    <dimen name="points_coin_margin_end">7dp</dimen>
    <dimen name="points_balance_text_size">41sp</dimen>
    <dimen name="points_grid_margin_horizontal">14dp</dimen>
    <dimen name="points_grid_margin_bottom">20dp</dimen>
    <dimen name="points_card_width">141dp</dimen>
    <dimen name="points_card_height">68dp</dimen>
    <dimen name="points_card_margin">5dp</dimen>
    <dimen name="points_card_padding">10dp</dimen>
    <dimen name="points_card_corner_radius">7dp</dimen>
    <dimen name="points_card_selected_stroke_width">1.7dp</dimen>
    <dimen name="points_card_coin_size">14dp</dimen>
    <dimen name="points_card_coin_margin">3dp</dimen>
    <dimen name="points_card_main_text_size">14sp</dimen>
    <dimen name="points_card_bonus_text_size">14sp</dimen>
    <dimen name="points_card_bonus_margin">3dp</dimen>
    <dimen name="points_card_price_text_size">10sp</dimen>
    <dimen name="points_card_price_margin_top">3dp</dimen>
    <dimen name="points_tab_indicator_width">45dp</dimen>
    <dimen name="points_tab_indicator_height">3dp</dimen>
    <dimen name="points_tab_indicator_corner_radius">1dp</dimen>
    <dimen name="points_tab_obtain_indicator_margin">0dp</dimen>
    <dimen name="points_tab_expenses_indicator_margin">67dp</dimen>
    <dimen name="points_divider_stroke_width">0.4dp</dimen>
    <dimen name="points_tabs_margin_horizontal">14dp</dimen>
    <dimen name="points_tabs_margin_bottom">14dp</dimen>
    <dimen name="points_tab_text_size">14sp</dimen>
    <dimen name="points_tab_margin_end">20dp</dimen>


    <dimen name="points_tips_item_margin_bottom">7dp</dimen>
    <dimen name="points_tips_bottom_margin">20dp</dimen>
    <dimen name="points_refill_button_padding">14dp</dimen>
    <dimen name="points_refill_button_height">41dp</dimen>
    <dimen name="points_refill_button_text_size">14sp</dimen>
    <dimen name="points_refill_button_corner_radius">20dp</dimen>

    <!-- Bill Page - sw320dp -->
    <!-- Header -->
    <dimen name="bill_header_padding_horizontal">16dp</dimen> <!-- 统一基准值 -->

    <!-- VIP Record Card - 统一基准值 -->
    <dimen name="vip_record_first_card_margin_top">22dp</dimen> <!-- 统一基准值 -->

    <!-- Points Purchase Card - 统一基准值 -->
    <dimen name="points_purchase_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Video Record Card - 统一基准值 -->
    <dimen name="video_record_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Message Page - 统一基准值 -->
    <dimen name="message_first_card_margin_top">24dp</dimen> <!-- 统一基准值 -->

    <!-- Message Detail Page - 适配320dp屏幕 (320/375 ≈ 0.85) -->
    <dimen name="message_detail_card_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="message_detail_card_min_height">556dp</dimen> <!-- 654 × 0.85 -->
    <dimen name="message_detail_card_margin_top">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="message_detail_divider_width">271dp</dimen> <!-- 319 × 0.85 -->
    <dimen name="message_detail_divider_height">0.5dp</dimen> <!-- 保持不变 -->
    <dimen name="message_detail_divider_margin_vertical">9dp</dimen> <!-- 10 × 0.85 -->

    <!-- Setting Page - 适配320dp屏幕 (320/375 ≈ 0.85) -->
    <dimen name="setting_first_item_margin_start">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_first_item_margin_top">27dp</dimen> <!-- 32 × 0.85 -->
    <dimen name="setting_double_item_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="setting_double_item_height">80dp</dimen> <!-- 94 × 0.85 -->
    <dimen name="setting_single_item_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="setting_single_item_height">43dp</dimen> <!-- 51 × 0.85 -->
    <dimen name="setting_item_margin_bottom">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_item_padding">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_text_size">15sp</dimen> <!-- 16 × 0.94 -->
    <dimen name="setting_text_line_height">16dp</dimen> <!-- 19 × 0.85 -->
    <dimen name="setting_arrow_size">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_terms_text_width">103dp</dimen> <!-- 121 × 0.85 -->
    <dimen name="setting_cache_text_width">73dp</dimen> <!-- 86 × 0.85 -->
    <dimen name="setting_cache_size_width">47dp</dimen> <!-- 55 × 0.85 -->
    <dimen name="setting_bottom_buttons_margin_top">27dp</dimen> <!-- 32 × 0.85 -->
    <dimen name="setting_bottom_buttons_margin_bottom">27dp</dimen> <!-- 32 × 0.85 -->
    <dimen name="setting_bottom_button_margin_horizontal">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_bottom_button_margin_between">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="setting_bottom_button_corner_radius">18dp</dimen> <!-- 21 × 0.85 -->

    <!-- Tabs - 响应式适配 -->
    <dimen name="bill_tabs_margin_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_tabs_background_width">288dp</dimen> <!-- 320-32=288dp -->
    <dimen name="bill_selected_tab_width">94dp</dimen> <!-- (288-6)/3=94dp -->

    <!-- Video Detail Page Dimensions (320dp width, scale factor: 0.9) -->

    <!-- Top poster area -->
    <dimen name="video_detail_poster_height">450dp</dimen> <!-- 500 × 0.9 -->

    <!-- Navigation buttons -->
    <dimen name="video_detail_back_button_size">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_detail_share_button_size">20dp</dimen> <!-- 22 × 0.9 -->
    <dimen name="video_detail_nav_button_margin_top">50dp</dimen> <!-- 55 × 0.9 -->
    <dimen name="video_detail_nav_button_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- Small poster -->
    <dimen name="video_detail_small_poster_width">76dp</dimen> <!-- 84 × 0.9 -->
    <dimen name="video_detail_small_poster_height">101dp</dimen> <!-- 112 × 0.9 -->
    <dimen name="video_detail_small_poster_corner_radius">10dp</dimen> <!-- 11 × 0.9 -->
    <dimen name="video_detail_small_poster_margin_start">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_detail_small_poster_margin_top">18dp</dimen> <!-- 20 × 0.9 -->

    <!-- Video info -->
    <dimen name="video_detail_title_margin_start">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_detail_title_margin_top">5dp</dimen> <!-- 6 × 0.9 -->
    <dimen name="video_detail_title_text_size">18sp</dimen> <!-- 20 × 0.9 -->

    <dimen name="video_detail_view_icon_size">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_view_text_margin_start">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_detail_view_text_margin_top">14dp</dimen> <!-- 15 × 0.9 -->
    <dimen name="video_detail_view_text_size">12sp</dimen> <!-- 13 × 0.9 -->

    <!-- Like button -->
    <dimen name="video_detail_like_button_width">130dp</dimen> <!-- 144 × 0.9 -->
    <dimen name="video_detail_like_button_height">29dp</dimen> <!-- 32 × 0.9 -->
    <dimen name="video_detail_like_button_corner_radius">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_detail_like_button_margin_top">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_like_icon_size">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="video_detail_like_text_size">13sp</dimen> <!-- 14 × 0.9 -->

    <!-- Synopsis section -->
    <dimen name="video_detail_synopsis_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_synopsis_margin_top">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="video_detail_synopsis_title_margin_bottom">11dp</dimen> <!-- 12 × 0.9 -->

    <!-- Tags -->
    <dimen name="video_detail_tag_width">72dp</dimen> <!-- 80 × 0.9 -->
    <dimen name="video_detail_tag_height">27dp</dimen> <!-- 30 × 0.9 -->
    <dimen name="video_detail_tag_corner_radius">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="video_detail_tag_spacing">9dp</dimen> <!-- 10 × 0.9 -->

    <!-- Synopsis content -->
    <dimen name="video_detail_synopsis_content_margin_top">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="video_detail_synopsis_content_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="video_detail_synopsis_content_line_height">19dp</dimen> <!-- 21 × 0.9 -->

    <!-- Actor section -->
    <dimen name="video_detail_actor_margin_top">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="video_detail_actor_poster_width">67dp</dimen> <!-- 74 × 0.9 -->
    <dimen name="video_detail_actor_poster_height">82dp</dimen> <!-- 91.5 × 0.9 -->
    <dimen name="video_detail_actor_poster_corner_radius">8dp</dimen> <!-- 9 × 0.9 -->
    <dimen name="video_detail_actor_spacing">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_detail_actor_name_margin_top">4dp</dimen> <!-- 4 × 0.9 -->

    <!-- Director section -->
    <dimen name="video_detail_director_margin_top">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="video_detail_director_poster_width">67dp</dimen> <!-- 74 × 0.9 -->
    <dimen name="video_detail_director_poster_height">82dp</dimen> <!-- 91.5 × 0.9 -->
    <dimen name="video_detail_director_poster_corner_radius">8dp</dimen> <!-- 9 × 0.9 -->
    <dimen name="video_detail_director_spacing">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_detail_director_name_margin_top">4dp</dimen> <!-- 4 × 0.9 -->

    <!-- Episodes section -->
    <dimen name="video_detail_episodes_margin_top">21dp</dimen> <!-- 23 × 0.9 -->
    <dimen name="video_detail_episodes_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_episodes_range_margin_top">12dp</dimen> <!-- 13 × 0.9 -->
    <dimen name="video_detail_episodes_range_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="video_detail_episodes_range_spacing">23dp</dimen> <!-- 25 × 0.9 -->
    <dimen name="video_detail_episodes_range_underline_margin_top">6dp</dimen> <!-- 7 × 0.9 -->

    <dimen name="video_detail_episode_tag_width">55dp</dimen> <!-- 61 × 0.9 -->
    <dimen name="video_detail_episode_tag_height">32dp</dimen> <!-- 36 × 0.9 -->
    <dimen name="video_detail_episode_tag_corner_radius">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_detail_episode_tag_spacing">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="video_detail_episode_tag_margin_top">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_detail_episode_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_detail_episode_icon_size">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_episode_icon_margin_start">2dp</dimen> <!-- 2 × 0.9 -->

    <!-- More Like This section -->
    <dimen name="video_detail_more_like_this_margin_top">30dp</dimen> <!-- 33 × 0.9 -->
    <dimen name="video_detail_more_like_this_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="video_detail_more_like_this_grid_margin_top">11dp</dimen> <!-- 12 × 0.9 -->

    <!-- Continue Playing Button (320dp width, scale factor: 0.9) -->
    <dimen name="continue_playing_button_width">309dp</dimen> <!-- 343 × 0.9 -->
    <dimen name="continue_playing_button_height">38dp</dimen> <!-- 42 × 0.9 -->
    <dimen name="continue_playing_button_corner_radius">19dp</dimen> <!-- 21 × 0.9 -->
    <dimen name="continue_playing_button_margin_bottom">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="continue_playing_button_text_size">14sp</dimen> <!-- 16 × 0.9 -->

    <!-- Edit Profile Page - 适配320dp屏幕 (320/375 ≈ 0.85) -->
    <dimen name="edit_profile_card_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="edit_profile_card_height">204dp</dimen> <!-- 240 × 0.85 -->
    <dimen name="edit_profile_card_margin_top">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="edit_profile_avatar_size">41dp</dimen> <!-- 48 × 0.85 -->
    <dimen name="edit_profile_arrow_size">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="edit_profile_divider_width">264dp</dimen> <!-- 311 × 0.85 -->
    <dimen name="edit_profile_input_width">264dp</dimen> <!-- 311 × 0.85 -->
    <dimen name="edit_profile_input_height">41dp</dimen> <!-- 48 × 0.85 -->
    <dimen name="edit_profile_ip_location_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="edit_profile_ip_location_height">43dp</dimen> <!-- 50 × 0.85 -->
    <dimen name="edit_profile_save_button_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="edit_profile_save_button_height">36dp</dimen> <!-- 42 × 0.85 -->
    <dimen name="edit_profile_text_size">15sp</dimen> <!-- 16 × 0.94 -->
    <dimen name="edit_profile_text_line_height">16dp</dimen> <!-- 19 × 0.85 -->
    <dimen name="edit_profile_china_text_width">51dp</dimen> <!-- 60 × 0.85 -->
    <dimen name="edit_profile_save_text_width">31dp</dimen> <!-- 37 × 0.85 -->
    <dimen name="edit_profile_item_margin_bottom">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="edit_profile_item_padding">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="edit_profile_save_button_margin_bottom">32dp</dimen> <!-- 38 × 0.85 -->
    <dimen name="edit_profile_popup_width">102dp</dimen> <!-- 120 × 0.85 -->
    <dimen name="edit_profile_popup_item_height">27dp</dimen> <!-- 32 × 0.85 -->
    <dimen name="edit_profile_popup_padding">7dp</dimen> <!-- 8 × 0.85 -->
    <dimen name="edit_profile_popup_text_size">13sp</dimen> <!-- 14 × 0.93 -->
    <dimen name="edit_profile_popup_corner_radius">7dp</dimen> <!-- 8 × 0.85 -->

    <!-- Log Off Page - 适配320dp屏幕 (320/375 ≈ 0.85) -->
    <dimen name="log_off_warning_icon_size">51dp</dimen> <!-- 60 × 0.85 -->
    <dimen name="log_off_warning_icon_margin_top">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="log_off_description_width">279dp</dimen> <!-- 328 × 0.85 -->
    <dimen name="log_off_description_height">51dp</dimen> <!-- 60 × 0.85 -->
    <dimen name="log_off_description_margin_top">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="log_off_card_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="log_off_card_height">272dp</dimen> <!-- 320 × 0.85 -->
    <dimen name="log_off_card_margin_top">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="log_off_card_padding">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="log_off_card_item_line_height">22dp</dimen> <!-- 26 × 0.85 -->
    <dimen name="log_off_checkbox_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="log_off_checkbox_height">51dp</dimen> <!-- 60 × 0.85 -->
    <dimen name="log_off_checkbox_margin_top">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="log_off_checkbox_icon_size">17dp</dimen> <!-- 20 × 0.85 -->
    <dimen name="log_off_checkbox_icon_margin_end">7dp</dimen> <!-- 8 × 0.85 -->
    <dimen name="log_off_checkbox_text_margin_start">24dp</dimen> <!-- 28 × 0.85 -->
    <dimen name="log_off_checkbox_text_size">12sp</dimen> <!-- 13 × 0.92 -->
    <dimen name="log_off_checkbox_text_line_spacing">6dp</dimen> <!-- 7 × 0.85 -->
    <dimen name="log_off_delete_button_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="log_off_delete_button_height">36dp</dimen> <!-- 42 × 0.85 -->
    <dimen name="log_off_delete_button_margin_top">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="log_off_delete_button_margin_bottom">32dp</dimen> <!-- 38 × 0.85 -->
    <dimen name="log_off_delete_text_width">98dp</dimen> <!-- 115 × 0.85 -->
    <dimen name="log_off_delete_text_height">16dp</dimen> <!-- 19 × 0.85 -->
    <dimen name="log_off_item_margin_horizontal">14dp</dimen> <!-- 16 × 0.85 -->

    <!-- Feedback Page - 适配320dp屏幕 (320/375 ≈ 0.85) -->
    <dimen name="feedback_required_star_width">6dp</dimen> <!-- 7 × 0.85 -->
    <dimen name="feedback_required_star_height">16dp</dimen> <!-- 19 × 0.85 -->
    <dimen name="feedback_label_width">92dp</dimen> <!-- 108 × 0.85 -->
    <dimen name="feedback_label_height">16dp</dimen> <!-- 19 × 0.85 -->
    <dimen name="feedback_type_selected_width">96dp</dimen> <!-- 113 × 0.85 -->
    <dimen name="feedback_type_selected_height">34dp</dimen> <!-- 40 × 0.85 -->
    <dimen name="feedback_type_unselected_width">185dp</dimen> <!-- 218 × 0.85 -->
    <dimen name="feedback_type_unselected_height">34dp</dimen> <!-- 40 × 0.85 -->
    <dimen name="feedback_type_corner_radius">12dp</dimen> <!-- 14 × 0.85 -->
    <dimen name="feedback_input_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="feedback_input_height">158dp</dimen> <!-- 186 × 0.85 -->
    <dimen name="feedback_input_corner_radius">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="feedback_input_padding">13dp</dimen> <!-- 15 × 0.85 -->
    <dimen name="feedback_image_size">91dp</dimen> <!-- 107 × 0.85 -->
    <dimen name="feedback_image_corner_radius">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="feedback_image_icon_size">24dp</dimen> <!-- 28 × 0.85 -->
    <dimen name="feedback_button_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="feedback_button_height">36dp</dimen> <!-- 42 × 0.85 -->
    <dimen name="feedback_button_corner_radius">18dp</dimen> <!-- 21 × 0.85 -->
    <dimen name="feedback_item_margin_horizontal">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="feedback_item_margin_vertical">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="feedback_type_margin_end">7dp</dimen> <!-- 8 × 0.85 -->

    <!-- Interest Tab Card Dimensions - sw320dp (缩放因子: 0.9，基准360dp) -->
    <!-- Card Layout -->
    <dimen name="interest_card_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 = 14dp -->
    <dimen name="interest_card_margin_bottom">16dp</dimen> <!-- 18 × 0.9 = 16dp -->

    <!-- Poster - 放大尺寸 -->
    <dimen name="interest_poster_width">108dp</dimen> <!-- 120 × 0.9 = 108dp -->
    <dimen name="interest_poster_height">144dp</dimen> <!-- 160 × 0.9 = 144dp -->

    <!-- Content Area -->
    <dimen name="interest_content_margin_start">13dp</dimen> <!-- 14 × 0.9 = 13dp -->

    <!-- Title -->
    <dimen name="interest_title_width">180dp</dimen> <!-- 200 × 0.9 = 180dp -->
    <dimen name="interest_title_height">20dp</dimen> <!-- 22 × 0.9 = 20dp -->
    <dimen name="interest_title_text_size">14sp</dimen> <!-- 16 × 0.9 = 14sp -->

    <!-- Status Tag - 调整位置和大小 -->
    <dimen name="interest_status_tag_width">79dp</dimen> <!-- 88 × 0.9 = 79dp -->
    <dimen name="interest_status_tag_height">20dp</dimen> <!-- 22 × 0.9 = 20dp -->
    <dimen name="interest_status_tag_margin_top">5dp</dimen> <!-- 6 × 0.9 = 5dp -->
    <dimen name="interest_status_text_width">68dp</dimen> <!-- 76 × 0.9 = 68dp -->
    <dimen name="interest_status_text_height">13dp</dimen> <!-- 14 × 0.9 = 13dp -->
    <dimen name="interest_status_text_size">11sp</dimen> <!-- 12 × 0.9 = 11sp -->

    <!-- Description - 优化文字大小 -->
    <dimen name="interest_description_width">180dp</dimen> <!-- 200 × 0.9 = 180dp -->
    <dimen name="interest_description_height">32dp</dimen> <!-- 36 × 0.9 = 32dp -->
    <dimen name="interest_description_margin_top">7dp</dimen> <!-- 8 × 0.9 = 7dp -->
    <dimen name="interest_description_text_size">11sp</dimen> <!-- 12 × 0.9 = 11sp -->

    <!-- Time Info -->
    <dimen name="interest_time_container_margin_top">11dp</dimen> <!-- 12 × 0.9 = 11dp -->
    <dimen name="interest_time_icon_size">13dp</dimen> <!-- 14 × 0.9 = 13dp -->
    <dimen name="interest_time_text_width">72dp</dimen> <!-- 80 × 0.9 = 72dp -->
    <dimen name="interest_time_text_height">16dp</dimen> <!-- 18 × 0.9 = 16dp -->
    <dimen name="interest_time_text_margin_start">4dp</dimen> <!-- 4 × 0.9 = 4dp -->
    <dimen name="interest_time_text_size">11sp</dimen> <!-- 12 × 0.9 = 11sp -->

    <!-- Cancel Button -->
    <dimen name="interest_cancel_button_width">180dp</dimen> <!-- 200 × 0.9 = 180dp -->
    <dimen name="interest_cancel_button_height">25dp</dimen> <!-- 28 × 0.9 = 25dp -->
    <dimen name="interest_cancel_button_margin_top">11dp</dimen> <!-- 12 × 0.9 = 11dp -->
    <dimen name="interest_cancel_icon_size">13dp</dimen> <!-- 14 × 0.9 = 13dp -->
    <dimen name="interest_cancel_text_width">72dp</dimen> <!-- 80 × 0.9 = 72dp -->
    <dimen name="interest_cancel_text_height">13dp</dimen> <!-- 14 × 0.9 = 13dp -->
    <dimen name="interest_cancel_text_margin_start">4dp</dimen> <!-- 4 × 0.9 = 4dp -->
    <dimen name="interest_cancel_text_size">11sp</dimen> <!-- 12 × 0.9 = 11sp -->

    <!-- Video Player Dimensions - sw320dp (缩放因子: 0.9) -->
    <dimen name="video_player_progress_margin_top">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="video_player_bottom_controls_margin_top">9dp</dimen> <!-- 10 × 0.9 -->

    <!-- Subtitle Panel Dimensions - sw320dp -->
    <dimen name="subtitle_panel_height">286dp</dimen> <!-- 318 × 0.9 -->
    <dimen name="subtitle_panel_corner_radius_top">22dp</dimen> <!-- 24 × 0.9 -->
    <dimen name="subtitle_panel_close_button_size">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="subtitle_panel_close_button_margin_top">22dp</dimen> <!-- 24 × 0.9 -->
    <dimen name="subtitle_panel_close_button_margin_end">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="subtitle_panel_title_margin_top">22dp</dimen> <!-- 24 × 0.9 -->
    <dimen name="subtitle_panel_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="subtitle_panel_subtitle_margin_top">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="subtitle_panel_subtitle_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="subtitle_panel_switch_margin_top">6dp</dimen> <!-- 7 × 0.9 -->
    <dimen name="subtitle_panel_switch_size">32dp</dimen> <!-- 36 × 0.9 -->
    <dimen name="subtitle_panel_language_margin_top">25dp</dimen> <!-- 28 × 0.9 -->
    <dimen name="subtitle_panel_language_item_margin_top">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="subtitle_panel_language_width">279dp</dimen> <!-- 310 × 0.9 -->
    <dimen name="subtitle_panel_language_height">38dp</dimen> <!-- 42 × 0.9 -->
    <dimen name="subtitle_panel_language_corner_radius">13dp</dimen> <!-- 14 × 0.9 -->
    <dimen name="subtitle_panel_language_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="subtitle_panel_language_icon_size">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="subtitle_panel_language_icon_margin_start">11dp</dimen> <!-- 12 × 0.9 -->

    <!-- Video Poster Popup Dimensions - Small phone (320dp, scale factor 0.9) -->
    <dimen name="poster_popup_margin_top">126dp</dimen> <!-- 140 × 0.9 -->
    <dimen name="poster_popup_width">268dp</dimen> <!-- 298 × 0.9 -->
    <dimen name="poster_popup_height">419dp</dimen> <!-- 466 × 0.9 -->
    <dimen name="poster_popup_corner_radius">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="poster_popup_close_button_size">27dp</dimen> <!-- 30 × 0.9 -->
    <dimen name="poster_popup_close_button_margin_top">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- VIP Unlock Popup Dimensions - Small phone (320dp, scale factor 0.9) -->
    <dimen name="vip_unlock_popup_margin_top">216dp</dimen> <!-- 240 × 0.9 -->
    <dimen name="vip_unlock_message_width">243dp</dimen> <!-- 270 × 0.9 -->
    <dimen name="vip_unlock_message_text_size">18sp</dimen> <!-- 20 × 0.9 -->
    <dimen name="vip_unlock_message_line_spacing">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="vip_unlock_button_width">265dp</dimen> <!-- 294 × 0.9 -->
    <dimen name="vip_unlock_button_height">56dp</dimen> <!-- 62 × 0.9 -->
    <dimen name="vip_unlock_button_corner_radius">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="vip_unlock_button_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="vip_unlock_button_margin_top">26dp</dimen> <!-- 29 × 0.9 -->
    <dimen name="vip_unlock_button_spacing">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="vip_unlock_coin_icon_size">20dp</dimen> <!-- 22 × 0.9 -->
    <dimen name="vip_unlock_coin_icon_margin_start">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="vip_unlock_coin_text_margin_start">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="vip_unlock_coin_text_size">18sp</dimen> <!-- 20 × 0.9 -->
    <dimen name="vip_unlock_close_button_size">27dp</dimen> <!-- 30 × 0.9 -->
    <dimen name="vip_unlock_close_button_margin_top">40dp</dimen> <!-- 44 × 0.9 -->

    <!-- Watch AD Popup Dimensions - Small phone (320dp, scale factor 0.9) -->
    <dimen name="watch_ad_popup_width">265dp</dimen> <!-- 294 × 0.9 -->
    <dimen name="watch_ad_popup_height">56dp</dimen> <!-- 62 × 0.9 -->
    <dimen name="watch_ad_popup_corner_radius">16dp</dimen> <!-- 18 × 0.9 -->
    <dimen name="watch_ad_popup_text_size">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="watch_ad_popup_coin_text_size">18sp</dimen> <!-- 20 × 0.9 -->
    <dimen name="watch_ad_popup_coin_icon_size">20dp</dimen> <!-- 22 × 0.9 -->
    <dimen name="watch_ad_popup_coin_margin_start">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="watch_ad_popup_text_margin_top">3dp</dimen> <!-- 3 × 0.9 -->
    <dimen name="watch_ad_popup_padding_vertical">7dp</dimen> <!-- 8 × 0.9 -->

    <!-- Video Player Fixed Values Converted to Responsive - Small phone (320dp, scale factor 0.9) -->
    <dimen name="video_player_side_button_container_width">54dp</dimen> <!-- 60 × 0.9 -->
    <dimen name="video_player_seek_button_size">43dp</dimen> <!-- 48 × 0.9 -->
    <dimen name="video_player_seek_button_padding">7dp</dimen> <!-- 8 × 0.9 -->
    <dimen name="video_player_seek_button_margin">45dp</dimen> <!-- 50 × 0.9 -->
    <dimen name="video_player_episode_selection_width">101dp</dimen> <!-- 112 × 0.9 -->
    <dimen name="video_player_common_margin">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="video_player_common_margin_small">7dp</dimen> <!-- 8 × 0.9 -->
    <dimen name="video_player_common_margin_medium">9dp</dimen> <!-- 10 × 0.9 -->
    <dimen name="video_player_common_padding">14dp</dimen> <!-- 15 × 0.9 -->
    <dimen name="video_player_progress_touch_area_height">13dp</dimen> <!-- 14 × 0.9 -->
    <dimen name="video_player_progress_touch_area_margin">5dp</dimen> <!-- 5 × 0.9 -->
    <dimen name="video_player_progress_touch_area_margin_negative">-5dp</dimen> <!-- 5 × 0.9 -->

    <!-- Video Detail Fixed Values Converted to Responsive - Small phone (320dp, scale factor 0.9) -->
    <dimen name="video_detail_poster_border_margin">1dp</dimen> <!-- 1 × 0.9 -->
    <dimen name="video_detail_view_count_margin_start">4dp</dimen> <!-- 4 × 0.9 -->
    <dimen name="video_detail_like_text_margin_start">7dp</dimen> <!-- 8 × 0.9 -->

    <!-- Search Page Dimensions Scaled for 320dp (0.85x) -->

    <!-- Back button -->
    <dimen name="search_back_button_size">20dp</dimen> <!-- 24 × 0.85 -->
    <dimen name="search_back_button_margin_start">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_back_button_margin_top">46dp</dimen> <!-- 54 × 0.85 -->

    <!-- Search input -->
    <dimen name="search_input_width">261dp</dimen> <!-- 307 × 0.85 -->
    <dimen name="search_input_height">32dp</dimen> <!-- 38 × 0.85 -->
    <dimen name="search_input_margin_start">44dp</dimen> <!-- 52 × 0.85 -->
    <dimen name="search_input_margin_top">40dp</dimen> <!-- 47 × 0.85 -->
    <dimen name="search_input_corner_radius">10dp</dimen> <!-- 12 × 0.85 -->
    <dimen name="search_input_padding_start">10dp</dimen> <!-- 12 × 0.85 -->
    <dimen name="search_input_padding_vertical">7dp</dimen> <!-- 8 × 0.85 -->

    <!-- Search icon in input -->
    <dimen name="search_input_icon_size">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_input_icon_margin_start">10dp</dimen> <!-- 12 × 0.85 -->

    <!-- Search text -->
    <dimen name="search_text_size">14sp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_text_margin_start">83dp</dimen> <!-- 98 × 0.85 -->
    <dimen name="search_text_margin_top">48dp</dimen> <!-- 56 × 0.85 -->

    <!-- Recent searches section -->
    <dimen name="search_recent_title_margin_start">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_recent_title_margin_top">88dp</dimen> <!-- 104 × 0.85 -->
    <dimen name="search_recent_title_size">15sp</dimen> <!-- 18 × 0.85 -->

    <!-- Clear recent searches icon -->
    <dimen name="search_clear_icon_size">15dp</dimen> <!-- 18 × 0.85 -->
    <dimen name="search_clear_icon_margin_end">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_clear_icon_margin_top">89dp</dimen> <!-- 105 × 0.85 -->

    <!-- Recent search tags -->
    <dimen name="search_recent_tag_height">27dp</dimen> <!-- 32 × 0.85 -->
    <dimen name="search_recent_tag_padding_horizontal">10dp</dimen> <!-- 12 × 0.85 -->
    <dimen name="search_recent_tag_padding_vertical">5dp</dimen> <!-- 6 × 0.85 -->
    <dimen name="search_recent_tag_margin">3dp</dimen> <!-- 4 × 0.85 -->
    <dimen name="search_recent_tag_text_size">12sp</dimen> <!-- 14 × 0.85 -->
    <dimen name="search_recent_tag_corner_radius">14dp</dimen> <!-- 16 × 0.85 -->

    <!-- Trending searches section -->
    <dimen name="search_trending_title_size">15sp</dimen> <!-- 18 × 0.85 -->
    <dimen name="search_trending_title_margin_start">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_trending_title_margin_top">10dp</dimen> <!-- 12 × 0.85 -->

    <!-- Trending search cards -->
    <dimen name="search_trending_card_width">292dp</dimen> <!-- 343 × 0.85 -->
    <dimen name="search_trending_card_height">82dp</dimen> <!-- 96 × 0.85 -->
    <dimen name="search_trending_card_margin_horizontal">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_trending_card_margin_vertical">5dp</dimen> <!-- 6 × 0.85 -->
    <dimen name="search_trending_card_corner_radius">10dp</dimen> <!-- 12 × 0.85 -->
    <dimen name="search_trending_card_padding">12dp</dimen> <!-- 14 × 0.85 -->

    <!-- Rank background -->
    <dimen name="search_rank_bg_size">24dp</dimen> <!-- 28 × 0.85 -->
    <dimen name="search_rank_bg_corner_radius">5dp</dimen> <!-- 6 × 0.85 -->
    <dimen name="search_rank_text_size">14sp</dimen> <!-- 16 × 0.85 -->

    <!-- Poster in trending card -->
    <dimen name="search_poster_size">58dp</dimen> <!-- 68 × 0.85 -->
    <dimen name="search_poster_corner_radius">7dp</dimen> <!-- 8 × 0.85 -->
    <dimen name="search_poster_margin_start">9dp</dimen> <!-- 10 × 0.85 -->

    <!-- Content in trending card -->
    <dimen name="search_content_margin_start">9dp</dimen> <!-- 10 × 0.85 -->
    <dimen name="search_title_text_size">12sp</dimen> <!-- 14 × 0.85 -->
    <dimen name="search_count_text_size">10sp</dimen> <!-- 12 × 0.85 -->
    <dimen name="search_count_icon_size">14dp</dimen> <!-- 16 × 0.85 -->
    <dimen name="search_count_icon_margin_end">3dp</dimen> <!-- 4 × 0.85 -->
    <dimen name="search_count_margin_top">20dp</dimen> <!-- 23 × 0.85 -->

    <!-- Payment Method Dialog - Small screen (320dp, scale factor 0.9) -->
    <dimen name="payment_dialog_width">288dp</dimen> <!-- 320 × 0.9 -->
    <dimen name="payment_dialog_padding">22dp</dimen> <!-- 24 × 0.9 -->
    <dimen name="payment_dialog_title_text_size">16sp</dimen> <!-- 18 × 0.9 -->
    <dimen name="payment_dialog_option_height">43dp</dimen> <!-- 48 × 0.9 -->
    <dimen name="payment_dialog_option_padding_horizontal">14dp</dimen> <!-- 16 × 0.9 -->
    <dimen name="payment_dialog_option_text_size">14sp</dimen> <!-- 16 × 0.9 -->
    <dimen name="payment_dialog_icon_size">22dp</dimen> <!-- 24 × 0.9 -->
    <dimen name="payment_dialog_icon_margin_end">11dp</dimen> <!-- 12 × 0.9 -->
    <dimen name="payment_dialog_options_margin_top">18dp</dimen> <!-- 20 × 0.9 -->
    <dimen name="payment_dialog_divider_height">0.5dp</dimen> <!-- 0.5 × 0.9 -->
    <dimen name="payment_dialog_divider_margin_horizontal">14dp</dimen> <!-- 16 × 0.9 -->

    <!-- Video Player Controls - Small phone (320dp, scale factor: 0.9) -->
    <dimen name="video_player_view_height">600dp</dimen> <!-- 667 × 0.9 -->
    <dimen name="video_player_view_margin_top">54dp</dimen> <!-- 60 × 0.9 -->
    <dimen name="video_player_text_size_small">11sp</dimen> <!-- 12 × 0.9 -->
    <dimen name="video_player_text_size_medium">13sp</dimen> <!-- 14 × 0.9 -->
    <dimen name="video_player_text_size_large">14sp</dimen> <!-- 16 × 0.9 -->

    <!-- Common Style Properties - Small phone (320dp, scale factor: 0.9) -->
    <dimen name="common_stroke_width_thin">1dp</dimen> <!-- 0.5 × 0.9 = 0.45, min 1dp for visibility -->
    <dimen name="common_stroke_width_normal">1dp</dimen> <!-- 1 × 0.9 = 0.9, rounded to 1dp -->
    <dimen name="common_corner_radius_small">4dp</dimen> <!-- 4 × 0.9 = 3.6, rounded to 4dp -->
    <dimen name="common_corner_radius_medium">7dp</dimen> <!-- 8 × 0.9 = 7.2, rounded to 7dp -->
    <dimen name="common_corner_radius_large">14dp</dimen> <!-- 16 × 0.9 = 14.4, rounded to 14dp -->
    <dimen name="common_shadow_offset_x">2dp</dimen> <!-- 2 × 0.9 = 1.8, rounded to 2dp -->
    <dimen name="common_shadow_offset_y">1dp</dimen> <!-- 1.5 × 0.9 = 1.35, rounded to 1dp -->
    <dimen name="common_shadow_radius">5dp</dimen> <!-- 6 × 0.9 = 5.4, rounded to 5dp -->
    <dimen name="common_dash_width">4dp</dimen> <!-- 4 × 0.9 = 3.6, rounded to 4dp -->
    <dimen name="common_dash_gap">2dp</dimen> <!-- 2 × 0.9 = 1.8, rounded to 2dp -->

    <!-- Update Dialog Dimensions - Scaled for 320dp (factor: 0.9) -->
    <dimen name="update_dialog_width">299dp</dimen>
    <dimen name="update_dialog_height">285dp</dimen>
    <dimen name="update_dialog_margin_top">207dp</dimen>
    <dimen name="update_title_margin_top">21dp</dimen>
    <dimen name="update_version_margin_top">6dp</dimen>
    <dimen name="update_line_width">27dp</dimen>
    <dimen name="update_line_margin">5dp</dimen>
    <dimen name="update_description_margin_top">21dp</dimen>
    <dimen name="update_description_margin_horizontal">22dp</dimen>
    <dimen name="update_description_line_spacing">5dp</dimen>
    <dimen name="update_rocket_size">32dp</dimen>
    <dimen name="update_button_margin_top">34dp</dimen>
    <dimen name="update_button_width">218dp</dimen>
    <dimen name="update_button_height">38dp</dimen>
    <dimen name="update_button_corner_radius">19dp</dimen>

</resources>
