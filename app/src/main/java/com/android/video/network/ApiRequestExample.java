package com.android.video.network;

import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.AuthApiConstantsUtils;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.constants.ProfileApiConstantsUtils;
import com.android.video.utils.ApiHeaderUtils;
import java.util.Map;
import java.util.HashMap;

/**
 * API请求示例类 - 展示如何使用全局请求头和token
 * <p>
 * 此示例类演示了如何在实际的网络请求中使用
 * BaseApiConstantsUtils 中定义的全局请求头和访问令牌。
 * </p>
 *
 * <p>
 * 示例包含：
 * <ul>
 *   <li>如何设置默认请求头</li>
 *   <li>如何构建API请求URL</li>
 *   <li>如何在登录后更新访问令牌</li>
 *   <li>如何验证请求头的有效性</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 * @see ApiHeaderUtils
 */
public class ApiRequestExample {

    // 注释：现在使用全局配置的API URL，不再硬编码
    // private static final String BASE_API_URL = "https://short-play-api.gymooit.cn/v1";

    /**
     * 示例：发送验证码请求
     * <p>
     * 演示如何使用全局请求头发送API请求。
     * </p>
     *
     * @param phoneNumber 手机号码
     */
    public static void sendSmsCodeExample(String phoneNumber) {
        // 1. 构建API URL - 使用全局配置
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            AuthApiConstantsUtils.API_SEND_SMS_CODE
        );

        // 2. 获取默认请求头（包含X-Access-Token）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 3. 准备请求参数
        Map<String, String> params = new HashMap<>();
        params.put(AuthApiConstantsUtils.PARAM_PHONE_NUMBER, phoneNumber);
        params.put(BaseApiConstantsUtils.PARAM_DEVICE_ID, "device_id_example");

        // 4. 打印请求信息（实际项目中这里会发送HTTP请求）
        System.out.println("=== 发送验证码请求示例 ===");
        System.out.println("请求URL: " + apiUrl);
        System.out.println("请求头:");
        for (Map.Entry<String, String> header : headers.entrySet()) {
            System.out.println("  " + header.getKey() + ": " + header.getValue());
        }
        System.out.println("请求参数:");
        for (Map.Entry<String, String> param : params.entrySet()) {
            System.out.println("  " + param.getKey() + ": " + param.getValue());
        }
        System.out.println();
    }

    /**
     * 示例：手机号登录请求
     * <p>
     * 演示登录请求的处理流程。
     * </p>
     *
     * @param phoneNumber 手机号码
     * @param verificationCode 验证码
     */
    public static void phoneLoginExample(String phoneNumber, String verificationCode) {
        // 1. 构建API URL - 使用全局配置
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            AuthApiConstantsUtils.API_PHONE_LOGIN
        );

        // 2. 获取默认请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 3. 准备请求参数
        Map<String, String> params = new HashMap<>();
        params.put(AuthApiConstantsUtils.PARAM_PHONE_NUMBER, phoneNumber);
        params.put(AuthApiConstantsUtils.PARAM_VERIFICATION_CODE, verificationCode);
        params.put(BaseApiConstantsUtils.PARAM_DEVICE_ID, "device_id_example");

        // 4. 打印请求信息
        System.out.println("=== 手机号登录请求示例 ===");
        System.out.println("请求URL: " + apiUrl);
        System.out.println("请求头:");
        for (Map.Entry<String, String> header : headers.entrySet()) {
            System.out.println("  " + header.getKey() + ": " + header.getValue());
        }
        System.out.println("请求参数:");
        for (Map.Entry<String, String> param : params.entrySet()) {
            System.out.println("  " + param.getKey() + ": " + param.getValue());
        }

        // 5. 模拟登录成功后更新访问令牌
        String newToken = "new_token_from_login_response_12345";
        ApiHeaderUtils.setAccessToken(newToken);
        System.out.println("登录成功，访问令牌已更新为: " + newToken);
        System.out.println();
    }

    /**
     * 示例：验证请求头有效性
     * <p>
     * 演示如何验证当前请求头的有效性。
     * </p>
     */
    public static void validateHeadersExample() {
        System.out.println("=== 请求头验证示例 ===");

        // 检查当前访问令牌是否有效
        boolean isValid = ApiHeaderUtils.isCurrentTokenValid();
        System.out.println("当前访问令牌是否有效: " + isValid);

        // 获取当前访问令牌
        String currentToken = ApiHeaderUtils.getCurrentAccessToken();
        System.out.println("当前访问令牌: " + currentToken);

        // 获取访问令牌请求头名称
        String headerName = ApiHeaderUtils.getAccessTokenHeaderName();
        System.out.println("访问令牌请求头名称: " + headerName);

        System.out.println();
    }

    /**
     * 示例：获取用户信息请求
     * <p>
     * 演示如何获取当前登录用户的详细信息。
     * </p>
     */
    public static void getUserInfoExample() {
        // 1. 构建API URL - 使用全局配置
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_GET_USER_INFO
        );

        // 2. 获取默认请求头（包含X-Access-Token）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 3. 打印请求信息
        System.out.println("=== 获取用户信息请求示例 ===");
        System.out.println("请求URL: " + apiUrl);
        System.out.println("请求头:");
        for (Map.Entry<String, String> header : headers.entrySet()) {
            System.out.println("  " + header.getKey() + ": " + header.getValue());
        }

        // 4. 模拟响应数据解析
        System.out.println("响应字段说明:");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_CUSTOMER_ID + ": 用户ID");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_CUSTOMER_NAME + ": 用户昵称");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_POINTS + ": 当前积分数");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_IS_VIP + ": VIP状态 (0=否, 1=是)");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_VIP_DAYS + ": VIP剩余天数");
        System.out.println("  " + ProfileApiConstantsUtils.FIELD_USER_TOKEN + ": 用户访问令牌");

        // 5. 演示工具方法使用
        System.out.println("工具方法示例:");
        System.out.println("  手机号注册描述: " + ProfileApiConstantsUtils.getRegistrationTypeDescription(1));
        System.out.println("  英语描述: " + ProfileApiConstantsUtils.getLanguageTypeDescription(1));
        System.out.println("  VIP用户判断: " + ProfileApiConstantsUtils.isVipUser(1));
        System.out.println();
    }

    /**
     * 示例：用户退出登录
     * <p>
     * 演示用户退出登录时如何重置访问令牌。
     * </p>
     */
    public static void logoutExample() {
        System.out.println("=== 用户退出登录示例 ===");

        System.out.println("退出前访问令牌: " + ApiHeaderUtils.getCurrentAccessToken());

        // 重置访问令牌为默认值
        ApiHeaderUtils.resetAccessToken();

        System.out.println("退出后访问令牌: " + ApiHeaderUtils.getCurrentAccessToken());
        System.out.println("访问令牌已重置为默认值");
        System.out.println();
    }

    /**
     * 示例：获取分类标签列表请求
     * <p>
     * 演示如何获取所有分类标签的列表信息。
     * </p>
     */
    public static void getCategoryListExample() {
        // 1. 构建API URL - 使用全局配置
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            HomeApiConstantsUtils.API_GET_CATEGORY_LIST
        );

        // 2. 获取默认请求头（包含X-Access-Token）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 3. 打印请求信息
        System.out.println("=== 获取分类标签列表请求示例 ===");
        System.out.println("请求URL: " + apiUrl);
        System.out.println("请求方法: GET");
        System.out.println("请求头:");
        for (Map.Entry<String, String> header : headers.entrySet()) {
            System.out.println("  " + header.getKey() + ": " + header.getValue());
        }
        System.out.println("API描述: " + HomeApiConstantsUtils.getHomeApiDescription(HomeApiConstantsUtils.API_GET_CATEGORY_LIST));
        System.out.println("是否为首页API: " + HomeApiConstantsUtils.isHomeApi(HomeApiConstantsUtils.API_GET_CATEGORY_LIST));
        System.out.println("是否为分类标签API: " + HomeApiConstantsUtils.isCategoryListApi(HomeApiConstantsUtils.API_GET_CATEGORY_LIST));
        System.out.println();
    }

    /**
     * 示例：获取分类短剧列表请求
     * <p>
     * 演示如何获取分类短剧列表，支持分页查询。
     * </p>
     */
    public static void getCategoriesWithFilmsExample() {
        // 1. 构建API URL - 使用全局配置
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            HomeApiConstantsUtils.API_GET_CATEGORIES
        );

        // 2. 获取默认请求头（包含X-Access-Token）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 3. 准备请求参数
        String categoryId = ""; // 空字符串表示查询所有分类
        String page = "1";
        String size = "10";

        // 4. 构建完整URL（包含查询参数）
        String fullUrl = apiUrl + "?" +
                        HomeApiConstantsUtils.PARAM_CATEGORY_ID + "=" + categoryId + "&" +
                        HomeApiConstantsUtils.PARAM_PAGE + "=" + page + "&" +
                        HomeApiConstantsUtils.PARAM_SIZE + "=" + size;

        // 5. 打印请求信息
        System.out.println("=== 获取分类短剧列表请求示例 ===");
        System.out.println("请求URL: " + fullUrl);
        System.out.println("请求方法: GET");
        System.out.println("请求头:");
        for (Map.Entry<String, String> header : headers.entrySet()) {
            System.out.println("  " + header.getKey() + ": " + header.getValue());
        }
        System.out.println("请求参数:");
        System.out.println("  " + HomeApiConstantsUtils.PARAM_CATEGORY_ID + ": " + (categoryId.isEmpty() ? "ALL" : categoryId));
        System.out.println("  " + HomeApiConstantsUtils.PARAM_PAGE + ": " + page);
        System.out.println("  " + HomeApiConstantsUtils.PARAM_SIZE + ": " + size);
        System.out.println("API描述: " + HomeApiConstantsUtils.getHomeApiDescription(HomeApiConstantsUtils.API_GET_CATEGORIES));
        System.out.println("是否为首页API: " + HomeApiConstantsUtils.isHomeApi(HomeApiConstantsUtils.API_GET_CATEGORIES));
        System.out.println();
    }

    /**
     * 主方法 - 运行所有示例
     */
    public static void main(String[] args) {
        System.out.println("========== API请求头和Token使用示例 ==========\n");

        // 1. 发送验证码示例
        sendSmsCodeExample("13300000001");

        // 2. 手机号登录示例
        phoneLoginExample("13300000001", "123456");

        // 3. 获取用户信息示例
        getUserInfoExample();

        // 4. 获取分类标签列表示例
        getCategoryListExample();

        // 5. 获取分类短剧列表示例
        getCategoriesWithFilmsExample();

        // 6. 验证请求头示例
        validateHeadersExample();

        // 7. 用户退出登录示例
        logoutExample();

        System.out.println("========== 示例运行完成 ==========");
    }
}
