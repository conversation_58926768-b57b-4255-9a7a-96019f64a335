package com.android.video.model;

/**
 * 用户数据模型类
 * <AUTHOR>
 */
public class UserModel {

    private String username;
    private String uid;
    private String phoneNumber;
    /**
     * 0: 普通用户, 1: VIP用户
     */
    private int userVip;
    private String email;
    private String avatar;
    private int points;
    private boolean isLoggedIn;

    // VIP相关信息
    private Integer vipDays;
    private String vipExpireDate;

    // 新接口字段
    private String userToken;
    private int loginType;
    private String deviceId;

    /**
     * 默认构造函数
     */
    public UserModel() {
        this.username = "Guest";
        this.uid = "";
        this.phoneNumber = "";
        this.userVip = 0;
        this.email = "";
        this.avatar = "";
        this.points = 0;
        this.isLoggedIn = false;
        this.vipDays = null;
        this.vipExpireDate = null;
        this.userToken = "";
        this.loginType = 0;
        this.deviceId = "";
    }

    /**
     * 完整构造函数
     */
    public UserModel(String username, String uid, String phoneNumber, int userVip,
                    String email, String avatar, int points, boolean isLoggedIn) {
        this.username = username;
        this.uid = uid;
        this.phoneNumber = phoneNumber;
        this.userVip = userVip;
        this.email = email;
        this.avatar = avatar;
        this.points = points;
        this.isLoggedIn = isLoggedIn;
        this.vipDays = null;
        this.vipExpireDate = null;
    }

    /**
     * 包含VIP信息的完整构造函数
     */
    public UserModel(String username, String uid, String phoneNumber, int userVip,
                    String email, String avatar, int points, boolean isLoggedIn,
                    Integer vipDays, String vipExpireDate) {
        this.username = username;
        this.uid = uid;
        this.phoneNumber = phoneNumber;
        this.userVip = userVip;
        this.email = email;
        this.avatar = avatar;
        this.points = points;
        this.isLoggedIn = isLoggedIn;
        this.vipDays = vipDays;
        this.vipExpireDate = vipExpireDate;
        this.userToken = "";
        this.loginType = 0;
        this.deviceId = "";
    }

    /**
     * 从新接口响应创建UserModel的构造函数
     */
    public UserModel(String customerId, String customerName, String userToken,
                    int loginType, String deviceId) {
        this.uid = customerId;
        this.username = customerName != null ? customerName : "User " + customerId;
        this.phoneNumber = ""; // 新接口不返回手机号
        this.userVip = 0; // 新接口不返回VIP状态，默认为普通用户
        this.email = customerId != null ? "user" + customerId + "@videoplayer.com" : "";
        this.avatar = "";
        this.points = 100; // 默认积分
        this.isLoggedIn = true;
        this.vipDays = null;
        this.vipExpireDate = null;
        this.userToken = userToken;
        this.loginType = loginType;
        this.deviceId = deviceId;
    }

    /**
     * Getter和Setter方法
     * @return
     */
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public int getUserVip() {
        return userVip;
    }

    public void setUserVip(int userVip) {
        this.userVip = userVip;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public boolean isLoggedIn() {
        return isLoggedIn;
    }

    public void setLoggedIn(boolean loggedIn) {
        isLoggedIn = loggedIn;
    }

    /**
     * 便利方法
     * @return
     */
    public boolean isVip() {
        return userVip == 1;
    }

    public String getDisplayName() {
        return isLoggedIn ? username : "Guest";
    }

    public String getDisplayUid() {
        return isLoggedIn && !uid.isEmpty() ? "UID:" + uid : "UID:--";
    }

    public Integer getVipDays() {
        return vipDays;
    }

    public void setVipDays(Integer vipDays) {
        this.vipDays = vipDays;
    }

    public String getVipExpireDate() {
        return vipExpireDate;
    }

    public void setVipExpireDate(String vipExpireDate) {
        this.vipExpireDate = vipExpireDate;
    }

    /**
     * 获取格式化的VIP到期时间显示
     * @return 格式化的VIP到期时间字符串
     */
    public String getFormattedVipExpireDate() {
        // 添加调试日志
        android.util.Log.d("UserModel", "getFormattedVipExpireDate - isVip: " + isVip() +
            ", vipDays: " + vipDays + ", vipExpireDate: " + vipExpireDate);

        if (!isVip()) {
            return "Not VIP";
        }

        // 优先显示VIP到期日期
        if (vipExpireDate != null && !vipExpireDate.isEmpty()) {
            try {
                // 假设API返回的日期格式是 "yyyy-MM-dd HH:mm:ss"
                // 这里可以根据实际API格式进行调整
                if (vipExpireDate.length() >= 10) {
                    String dateOnly = vipExpireDate.substring(0, 10); // 取日期部分
                    android.util.Log.d("UserModel", "显示VIP到期日期: " + dateOnly);
                    return "Expires: " + dateOnly;
                } else {
                    android.util.Log.d("UserModel", "显示VIP到期日期（完整）: " + vipExpireDate);
                    return "Expires: " + vipExpireDate;
                }
            } catch (Exception e) {
                android.util.Log.e("UserModel", "解析VIP到期日期失败: " + vipExpireDate, e);
                return "Expires: " + vipExpireDate;
            }
        }

        // 如果没有到期日期但有剩余天数，显示剩余天数
        if (vipDays != null && vipDays > 0) {
            android.util.Log.d("UserModel", "显示VIP剩余天数: " + vipDays);
            return vipDays + " days remaining";
        }

        // 如果都没有，显示当前时间
        return getCurrentTimeFormatted();
    }

    /**
     * 获取当前时间的格式化字符串
     * @return 格式化的当前时间字符串
     */
    private String getCurrentTimeFormatted() {
        try {
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd", java.util.Locale.getDefault());
            return "Current: " + sdf.format(new java.util.Date());
        } catch (Exception e) {
            return "Current time";
        }
    }

    /**
     * 检查VIP是否即将到期（剩余天数少于7天）
     * @return 如果VIP即将到期则返回true，否则返回false
     */
    public boolean isVipExpiringSoon() {
        return isVip() && vipDays != null && vipDays <= 7 && vipDays > 0;
    }

    // ========== 新接口字段的Getter和Setter方法 ==========

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    /**
     * 获取登录类型描述
     * @return 登录类型描述
     */
    public String getLoginTypeDescription() {
        switch (loginType) {
            case 1:
                return "Phone Login";
            case 2:
                return "VKontakte Login";
            case 3:
                return "TikTok Login";
            default:
                return "Unknown Login Type";
        }
    }

    /**
     * 检查用户token是否有效
     * @return 是否有效
     */
    public boolean isTokenValid() {
        return userToken != null && !userToken.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "UserModel{" +
                "username='" + username + '\'' +
                ", uid='" + uid + '\'' +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", userVip=" + userVip +
                ", email='" + email + '\'' +
                ", avatar='" + avatar + '\'' +
                ", points=" + points +
                ", isLoggedIn=" + isLoggedIn +
                '}';
    }
}
