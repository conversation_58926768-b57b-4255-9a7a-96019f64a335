package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 我的播放历史列表项数据模型
 * <AUTHOR> Team
 */
public class MyHistoryItemModel {

    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;

    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;

    /**
     * 语言类型 (1=英语, 2=俄语)
     */
    @SerializedName("languageType")
    private int languageType;

    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;

    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;

    /**
     * 简介
     */
    @SerializedName("details")
    private String details;

    /**
     * 总章节数EP
     */
    @SerializedName("totalChaptersNum")
    private int totalChaptersNum;

    /**
     * 上次观看章节数EP
     */
    @SerializedName("lastWatchChapterNum")
    private int lastWatchChapterNum;

    public MyHistoryItemModel() {
    }

    public MyHistoryItemModel(String filmTitle, String cover, int languageType, String filmId,
                             String filmLanguageInfoId, String details, int totalChaptersNum,
                             int lastWatchChapterNum) {
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.details = details;
        this.totalChaptersNum = totalChaptersNum;
        this.lastWatchChapterNum = lastWatchChapterNum;
    }

    // Getters and Setters
    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public int getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(int totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    public int getLastWatchChapterNum() {
        return lastWatchChapterNum;
    }

    public void setLastWatchChapterNum(int lastWatchChapterNum) {
        this.lastWatchChapterNum = lastWatchChapterNum;
    }

    /**
     * 获取语言类型描述
     * @return 语言类型描述
     */
    public String getLanguageTypeDescription() {
        switch (languageType) {
            case 1:
                return "English";
            case 2:
                return "Russian";
            default:
                return "Unknown";
        }
    }

    /**
     * 获取观看进度百分比
     * @return 观看进度百分比 (0-100)
     */
    public int getWatchProgress() {
        if (totalChaptersNum <= 0) {
            return 0;
        }
        return (int) ((lastWatchChapterNum * 100.0) / totalChaptersNum);
    }

    /**
     * 检查是否已完成观看
     * @return 是否已完成观看
     */
    public boolean isCompleted() {
        return lastWatchChapterNum >= totalChaptersNum && totalChaptersNum > 0;
    }

    /**
     * 转换为MyListVideo模型
     * @return MyListVideo对象
     */
    public MyListVideo toMyListVideo() {
        MyListVideo video = new MyListVideo();
        video.setId(filmId);
        video.setTitle(filmTitle != null ? filmTitle : "");
        video.setPosterUrl(cover != null ? cover : "");
        video.setLiked(false); // History列表默认不显示喜欢状态
        video.setCategory("history"); // History标签页类别
        video.setDescription(details != null ? details : "");
        video.setReleaseDate(""); // API未提供发布日期
        video.setCurrentEpisode(lastWatchChapterNum);
        video.setTotalEpisodes(totalChaptersNum);
        video.setDownloadedEpisodes(0); // History列表不涉及下载
        video.setSubscribed(false); // History列表不涉及订阅
        video.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");
        return video;
    }

    /**
     * 转换为VideoModel模型（用于Continue Watching）
     * @return VideoModel对象
     */
    public VideoModel toVideoModel() {
        VideoModel video = new VideoModel();
        video.setId(filmId);
        video.setTitle(filmTitle != null ? filmTitle : "");
        video.setPosterUrl(cover != null ? cover : "");
        video.setCategory("continue_watching"); // Continue Watching类别
        video.setDescription(details != null ? details : "");
        video.setCurrentEpisode(lastWatchChapterNum);
        video.setTotalEpisodes(totalChaptersNum);
        video.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");

        // 设置其他默认值
        video.setRating(0.0f);
        video.setDuration("");
        video.setSubscribed(false);
        video.setLiked(false);

        return video;
    }

    @Override
    public String toString() {
        return "MyHistoryItemModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType + " (" + getLanguageTypeDescription() + ")" +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", details='" + details + '\'' +
                ", totalChaptersNum=" + totalChaptersNum +
                ", lastWatchChapterNum=" + lastWatchChapterNum +
                ", watchProgress=" + getWatchProgress() + "%" +
                ", isCompleted=" + isCompleted() +
                '}';
    }
}
