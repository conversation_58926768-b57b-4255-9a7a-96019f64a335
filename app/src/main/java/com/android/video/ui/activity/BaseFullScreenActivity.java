package com.android.video.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import com.android.video.manager.ContentProtectionManager;
import com.android.video.utils.StatusBarUtils;

/**
 * 全屏Activity基类 - 统一处理状态栏渲染和WindowInsets
 * 解决状态栏黑色区域、内容重叠等问题，确保在不同Android版本和设备上都能正确显示
 * 
 * <AUTHOR> Team
 */
public abstract class BaseFullScreenActivity extends AppCompatActivity {

    private static final String TAG = "BaseFullScreenActivity";

    /**
     * 设置为true启用调试
     */
    private static final boolean DEBUG_STATUS_BAR = false;

    // 内容保护相关
    private ContentProtectionManager protectionManager;
    private boolean contentProtectionEnabled = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        // 设置全屏显示，必须在setContentView之前调用
        setupFullScreen();
    }

    /**
     * 设置全屏显示，允许内容渲染到状态栏区域
     * 确保内容可以渲染到状态栏区域以实现沉浸式体验
     */
    private void setupFullScreen() {
        // 允许内容渲染到状态栏区域，实现沉浸式全屏体验
        WindowCompat.setDecorFitsSystemWindows(getWindow(), false);

        // 隐藏ActionBar但保持状态栏显示
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // 可选启用内容保护
        if (shouldEnableContentProtection()) {
            enableContentProtection();
        }
    }

    @Override
    public void setContentView(int layoutResID) {
        super.setContentView(layoutResID);
        initializeWindowInsets();
    }

    @Override
    public void setContentView(View view) {
        super.setContentView(view);
        initializeWindowInsets();
    }

    @Override
    public void setContentView(View view, ViewGroup.LayoutParams params) {
        super.setContentView(view, params);
        initializeWindowInsets();
    }

    /**
     * 初始化WindowInsets处理
     */
    private void initializeWindowInsets() {
        // ActionBar已在setupFullScreen中隐藏

        // 设置WindowInsets处理
        setupWindowInsets();

        // 调试状态栏配置（仅在DEBUG模式下）
        if (DEBUG_STATUS_BAR) {
            debugStatusBarConfiguration();
        }
    }

    /**
     * 设置WindowInsets处理，确保正确处理状态栏和导航栏
     */
    private void setupWindowInsets() {
        View rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
                // 调用原有的WindowInsets处理方法
                onApplyWindowInsets(insets);

                // 获取导航栏信息并调用新的处理方法
                int navigationBarHeight = getNavigationBarHeight();
                boolean hasNavigationBar = hasNavigationBar();
                boolean isGestureNavigation = isGestureNavigation();

                // 调用导航栏专用的处理方法
                onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

                // 消费导航栏insets，避免系统UI与应用UI冲突
                androidx.core.graphics.Insets navigationBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.navigationBars());
                androidx.core.graphics.Insets systemBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.systemBars());

                // 只消费底部的导航栏insets，保留状态栏insets
                androidx.core.graphics.Insets consumedInsets = androidx.core.graphics.Insets.of(0, 0, 0, navigationBars.bottom);
                return insets.inset(consumedInsets);
            });
        }
    }





    /**
     * 获取状态栏高度，供子类使用
     * @return 状态栏高度（像素）
     */
    protected int getStatusBarHeight() {
        View rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                return insets.getInsets(WindowInsetsCompat.Type.systemBars()).top;
            }
        }

        // 备用方法：通过系统资源获取
        int resourceId = getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return getResources().getDimensionPixelSize(resourceId);
        }

        return 0;
    }

    /**
     * 获取底部导航栏高度，供子类使用
     * @return 导航栏高度（像素），如果不存在导航栏则返回0
     */
    protected int getNavigationBarHeight() {
        View rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                return insets.getInsets(WindowInsetsCompat.Type.navigationBars()).bottom;
            }
        }

        // 备用方法：通过系统资源获取（仅在有物理导航栏的设备上有效）
        int resourceId = getResources().getIdentifier("navigation_bar_height", "dimen", "android");
        if (resourceId > 0) {
            return getResources().getDimensionPixelSize(resourceId);
        }

        return 0;
    }

    /**
     * 检测是否存在底部导航栏
     * @return true表示存在导航栏，false表示不存在
     */
    protected boolean hasNavigationBar() {
        return getNavigationBarHeight() > 0;
    }

    /**
     * 检测是否为手势导航模式
     * 通过比较navigationBars和tappableElement的高度来判断
     * @return true表示手势导航，false表示按钮导航或无导航栏
     */
    protected boolean isGestureNavigation() {
        View rootView = findViewById(android.R.id.content);
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                Insets navBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars());
                Insets tappable = insets.getInsets(WindowInsetsCompat.Type.tappableElement());

                // 如果导航栏高度大于可点击区域高度，通常表示手势导航
                // 手势导航的导航栏区域更大，但可点击区域较小
                return navBars.bottom > 0 && navBars.bottom > tappable.bottom;
            }
        }
        return false;
    }

    /**
     * 子类可以重写此方法来处理特定的WindowInsets需求
     * @param insets WindowInsets对象
     */
    protected void onApplyWindowInsets(WindowInsetsCompat insets) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 子类可以重写此方法来处理导航栏相关的布局调整
     * @param insets WindowInsets对象
     * @param navigationBarHeight 导航栏高度
     * @param hasNavigationBar 是否存在导航栏
     * @param isGestureNavigation 是否为手势导航
     */
    protected void onApplyNavigationBarInsets(WindowInsetsCompat insets, int navigationBarHeight,
                                            boolean hasNavigationBar, boolean isGestureNavigation) {
        // 默认实现为空，子类可以重写来处理导航栏适配
        // 例如：调整底部内容的padding或margin，避免被导航栏遮挡
    }

    /**
     * 调试状态栏配置（仅在DEBUG模式下使用）
     */
    private void debugStatusBarConfiguration() {
        // 延迟执行，确保布局已完成
        findViewById(android.R.id.content).post(() -> {
            StatusBarUtils.debugStatusBarConfig(this);
            StatusBarUtils.StatusBarValidationResult result =
                StatusBarUtils.validateStatusBarRendering(this);
            android.util.Log.d("BaseFullScreenActivity", result.toString());
        });
    }

    // ==================== 内容保护功能 ====================

    /**
     * 子类重写此方法决定是否启用内容保护
     * 默认返回false，子类可以根据需要重写返回true
     *
     * @return true表示启用内容保护，false表示不启用
     */
    protected boolean shouldEnableContentProtection() {
        return false; // 默认不启用，子类重写决定是否启用
    }

    /**
     * 启用内容保护功能
     * 包括防录屏、防截图等功能
     */
    protected void enableContentProtection() {
        if (contentProtectionEnabled) {
            Log.w(TAG, "Content protection already enabled");
            return;
        }

        try {
            protectionManager = ContentProtectionManager.getInstance();
            protectionManager.enableProtection(this);
            contentProtectionEnabled = true;

            Log.d(TAG, "Content protection enabled for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable content protection", e);
        }
    }

    /**
     * 禁用内容保护功能
     */
    protected void disableContentProtection() {
        if (!contentProtectionEnabled) {
            return;
        }

        try {
            if (protectionManager != null) {
                protectionManager.disableProtection();
                protectionManager = null;
            }
            contentProtectionEnabled = false;

            Log.d(TAG, "Content protection disabled for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to disable content protection", e);
        }
    }

    /**
     * 检查内容保护是否已启用
     *
     * @return true表示已启用，false表示未启用
     */
    protected boolean isContentProtectionEnabled() {
        return contentProtectionEnabled;
    }

    /**
     * 获取内容保护管理器实例
     * 仅在内容保护已启用时返回有效实例
     *
     * @return ContentProtectionManager实例，如果未启用则返回null
     */
    protected ContentProtectionManager getContentProtectionManager() {
        return contentProtectionEnabled ? protectionManager : null;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理内容保护资源
        disableContentProtection();
    }
}
