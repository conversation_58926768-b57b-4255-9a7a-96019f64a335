package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.HomeApiConstantsUtils;

/**
 * 即将来袭推荐位数据模型
 * <p>
 * 用于映射即将来袭推荐位API返回的短剧数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_WORTH_WAITING}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmTitle: 短剧标题</li>
 *   <li>cover: 封面图片URL</li>
 *   <li>languageType: 语言类型（1=英语, 2=俄语）</li>
 *   <li>filmId: 短剧唯一标识符</li>
 *   <li>filmLanguageInfoId: 短剧语言信息ID</li>
 *   <li>releaseTime: 发布时间</li>
 *   <li>details: 短剧简介</li>
 *   <li>isSubscribe: 是否订阅（0=未订阅, 1=已订阅）</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * List&lt;WorthWaitingModel&gt; worthWaitingList = gson.fromJson(jsonArray,
 *     new TypeToken&lt;List&lt;WorthWaitingModel&gt;&gt;(){}.getType());
 *
 * // 获取即将来袭数据
 * for (WorthWaitingModel item : worthWaitingList) {
 *     String title = item.getFilmTitle();
 *     String cover = item.getCover();
 *     boolean isSubscribed = item.isSubscribed();
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_WORTH_WAITING
 */
public class WorthWaitingModel {
    
    /**
     * 短剧标题
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_TITLE)
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_COVER)
    private String cover;
    
    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 短剧ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_ID)
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_LANGUAGE_INFO_ID)
    private String filmLanguageInfoId;
    
    /**
     * 发布时间
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_RELEASE_TIME)
    private String releaseTime;
    
    /**
     * 短剧简介
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_DETAILS)
    private String details;
    
    /**
     * 是否订阅
     * 0=未订阅, 1=已订阅
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_IS_SUBSCRIBE)
    private Integer isSubscribe;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WorthWaitingModel() {
    }

    /**
     * 完整构造函数
     */
    public WorthWaitingModel(String filmTitle, String cover, Integer languageType, 
                            String filmId, String filmLanguageInfoId, String releaseTime, 
                            String details, Integer isSubscribe) {
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.releaseTime = releaseTime;
        this.details = details;
        this.isSubscribe = isSubscribe;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Integer getIsSubscribe() {
        return isSubscribe;
    }

    public void setIsSubscribe(Integer isSubscribe) {
        this.isSubscribe = isSubscribe;
    }

    // ========== 工具方法 ==========

    /**
     * 获取语言类型的文本描述
     * @return 语言类型描述
     */
    public String getLanguageTypeText() {
        if (languageType == null) {
            return "未知语言";
        }
        return HomeApiConstantsUtils.getLanguageTypeDescription(languageType);
    }

    /**
     * 检查是否已订阅
     * @return 如果已订阅则返回true
     */
    public boolean isSubscribed() {
        return isSubscribe != null && isSubscribe == 1;
    }

    /**
     * 检查是否有有效的封面图片
     * @return 如果有有效封面则返回true
     */
    public boolean hasCover() {
        return cover != null && !cover.trim().isEmpty() && !cover.equals("null");
    }

    /**
     * 检查是否有有效的简介
     * @return 如果有有效简介则返回true
     */
    public boolean hasDetails() {
        return details != null && !details.trim().isEmpty() && !details.equals("null");
    }

    /**
     * 转换为VideoModel对象
     * @return VideoModel对象
     */
    public VideoModel toVideoModel() {
        VideoModel videoModel = new VideoModel();
        videoModel.setId(filmId);
        videoModel.setTitle(filmTitle);
        videoModel.setPosterUrl(cover);
        videoModel.setDescription(details);
        videoModel.setReleaseDate(releaseTime);
        videoModel.setSubscribed(isSubscribed());
        
        // 添加语言标签
        if (languageType != null) {
            videoModel.addTag(getLanguageTypeText());
        }
        
        // 存储原始数据用于跳转
        videoModel.addTag("film_id:" + filmId);
        if (filmLanguageInfoId != null) {
            videoModel.addTag("film_language_info_id:" + filmLanguageInfoId);
        }
        
        return videoModel;
    }

    @Override
    public String toString() {
        return "WorthWaitingModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", releaseTime='" + releaseTime + '\'' +
                ", details='" + details + '\'' +
                ", isSubscribe=" + isSubscribe +
                '}';
    }
}
