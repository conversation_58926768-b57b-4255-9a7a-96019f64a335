<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报图片 -->
    <ImageView
        android:id="@+id/iv_poster"
        android:layout_width="@dimen/most_popular_poster_width"
        android:layout_height="@dimen/most_popular_poster_height"
        android:layout_marginStart="@dimen/most_popular_poster_margin_start"
        android:scaleType="centerCrop"
        android:src="@drawable/movie_poster"
        android:background="@drawable/most_popular_poster_bg"
        android:clipToOutline="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 视频信息区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_video_info"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/most_popular_info_margin_start"
        android:layout_marginEnd="@dimen/most_popular_info_margin_end"
        app:layout_constraintTop_toTopOf="@id/iv_poster"
        app:layout_constraintStart_toEndOf="@id/iv_poster"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 视频标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="Escorting the heiress"
            android:textColor="@color/white"
            android:textSize="@dimen/most_popular_title_text_size"
            android:fontFamily="sans-serif-medium"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 集数信息 -->
        <TextView
            android:id="@+id/tv_episode"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="EP.72"
            android:textColor="#E6FFFFFF"
            android:textSize="@dimen/most_popular_episode_text_size"
            android:fontFamily="sans-serif"
            app:layout_constraintTop_toBottomOf="@id/tv_title"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- 视频概要 -->
        <TextView
            android:id="@+id/tv_synopsis"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:text="A captivating historical drama set in a grand palace, where love and power collide. The story..."
            android:textColor="#B3FFFFFF"
            android:textSize="@dimen/most_popular_synopsis_text_size"
            android:fontFamily="sans-serif"
            android:maxLines="3"
            android:ellipsize="end"
            android:lineSpacingExtra="2dp"
            app:layout_constraintTop_toBottomOf="@id/tv_episode"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
