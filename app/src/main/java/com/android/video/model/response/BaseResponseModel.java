package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.BaseApiConstantsUtils;

/**
 * 基础API响应模型 - 提供通用的API响应数据结构
 * <p>
 * 这个基础响应模型包含了所有API响应的通用字段：响应码和响应消息。
 * 适用于那些只需要返回状态信息而不需要具体数据的API接口。
 * </p>
 * 
 * <p>
 * 使用场景：
 * <ul>
 *   <li>添加/删除操作的响应</li>
 *   <li>状态更新操作的响应</li>
 *   <li>简单的成功/失败确认响应</li>
 * </ul>
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success"
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class BaseResponseModel {

    /**
     * 响应码
     */
    @SerializedName(BaseApiConstantsUtils.FIELD_CODE)
    private String code;

    /**
     * 响应消息
     */
    @SerializedName(BaseApiConstantsUtils.FIELD_MESSAGE)
    private String message;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public BaseResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     */
    public BaseResponseModel(String code, String message) {
        this.code = code;
        this.message = message;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    // ========== 便利方法 ==========

    /**
     * 检查响应是否成功
     * 
     * @return 如果响应码表示成功则返回true，否则返回false
     */
    public boolean isSuccess() {
        return BaseApiConstantsUtils.isSuccessCode(code);
    }

    /**
     * 检查响应是否失败
     * 
     * @return 如果响应码表示失败则返回true，否则返回false
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 获取错误消息（如果有）
     * 
     * @return 如果是错误响应则返回错误消息，否则返回null
     */
    public String getErrorMessage() {
        if (isError()) {
            return message != null && !message.isEmpty() ? message : "Unknown error";
        }
        return null;
    }

    /**
     * 检查响应码是否为指定值
     * 
     * @param expectedCode 期望的响应码
     * @return 如果响应码匹配则返回true，否则返回false
     */
    public boolean isCodeEquals(String expectedCode) {
        return expectedCode != null && expectedCode.equals(code);
    }

    /**
     * 检查是否为参数错误
     * 
     * @return 如果是参数错误则返回true，否则返回false
     */
    public boolean isParamError() {
        return isCodeEquals(BaseApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR);
    }

    /**
     * 检查是否为未授权错误
     * 
     * @return 如果是未授权错误则返回true，否则返回false
     */
    public boolean isUnauthorized() {
        return isCodeEquals(BaseApiConstantsUtils.RESPONSE_CODE_UNAUTHORIZED);
    }

    /**
     * 检查是否为服务器错误
     * 
     * @return 如果是服务器错误则返回true，否则返回false
     */
    public boolean isServerError() {
        return isCodeEquals(BaseApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建成功响应
     * 
     * @return 成功响应对象
     */
    public static BaseResponseModel success() {
        return new BaseResponseModel(BaseApiConstantsUtils.RESPONSE_CODE_SUCCESS, "success");
    }

    /**
     * 创建成功响应（自定义消息）
     * 
     * @param message 成功消息
     * @return 成功响应对象
     */
    public static BaseResponseModel success(String message) {
        return new BaseResponseModel(BaseApiConstantsUtils.RESPONSE_CODE_SUCCESS, message);
    }

    /**
     * 创建错误响应
     * 
     * @param code 错误码
     * @param message 错误消息
     * @return 错误响应对象
     */
    public static BaseResponseModel error(String code, String message) {
        return new BaseResponseModel(code, message);
    }

    /**
     * 创建参数错误响应
     * 
     * @param message 错误消息
     * @return 参数错误响应对象
     */
    public static BaseResponseModel paramError(String message) {
        return error(BaseApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR, message);
    }

    /**
     * 创建服务器错误响应
     * 
     * @param message 错误消息
     * @return 服务器错误响应对象
     */
    public static BaseResponseModel serverError(String message) {
        return error(BaseApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR, message);
    }

    // ========== Object方法重写 ==========

    @Override
    public String toString() {
        return "BaseResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", isSuccess=" + isSuccess() +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        BaseResponseModel that = (BaseResponseModel) obj;
        
        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        return message != null ? message.equals(that.message) : that.message == null;
    }

    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        return result;
    }
}
