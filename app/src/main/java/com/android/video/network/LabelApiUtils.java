package com.android.video.network;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.config.GlobalConfigUtils;
import com.android.video.constants.LabelApiConstantsUtils;
import com.android.video.model.TagModel;
import com.android.video.model.request.SubmitLabelsRequestModel;
import com.android.video.model.response.ApiResponseModel;
import com.android.video.model.response.LabelsResponseModel;
import com.android.video.utils.ApiExceptionUtils;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;

/**
 * 标签相关API工具类 - 处理标签获取和提交接口
 * <AUTHOR> Team
 */
public class LabelApiUtils {

    private static final String TAG = "LabelApiUtils";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final Gson gson = new Gson();
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    // 缓存相关常量
    private static final String CACHE_PREF_NAME = "LabelCache";
    private static final String CACHE_KEY_LABELS = "cached_labels";
    private static final String CACHE_KEY_TIMESTAMP = "cache_timestamp";

    /**
     * 标签API回调接口
     */
    public interface LabelCallback {
        void onSuccess(List<TagModel> tags);
        void onError(String errorMessage);
    }

    /**
     * 提交标签回调接口
     */
    public interface SubmitCallback {
        void onSuccess();
        void onError(String errorMessage);
    }

    /**
     * 获取所有可选标签
     * @param context 上下文
     * @param callback 回调接口
     */
    public static void getLabels(Context context, LabelCallback callback) {
        Log.d(TAG, "getLabels called");

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 环境判断
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境返回静态标签数据
            Log.d(TAG, "Dev environment: returning static labels");
            List<TagModel> staticLabels = createStaticLabels();
            mainHandler.post(() -> callback.onSuccess(staticLabels));
            return;
        }

        // 检查缓存
        List<TagModel> cachedLabels = getCachedLabels(context);
        if (cachedLabels != null && !cachedLabels.isEmpty()) {
            Log.d(TAG, "Returning cached labels");
            mainHandler.post(() -> callback.onSuccess(cachedLabels));
            return;
        }

        // 发送API请求
        String url = LabelApiConstantsUtils.buildApiUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                LabelApiConstantsUtils.API_GET_LABELS
        );

        Request request = new Request.Builder()
                .url(url)
                .get()
                .build();

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "getLabels network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);

                // 网络错误时尝试返回缓存数据
                List<TagModel> fallbackLabels = getCachedLabels(context, true);
                if (fallbackLabels != null && !fallbackLabels.isEmpty()) {
                    Log.d(TAG, "Network error, returning cached labels as fallback");
                    mainHandler.post(() -> callback.onSuccess(fallbackLabels));
                } else {
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "getLabels response: " + response.code());

                    if (!response.isSuccessful()) {
                        ApiExceptionUtils.ApiException apiException =
                                ApiExceptionUtils.handleHttpException(response.code(), responseBody);

                        // HTTP错误时尝试返回缓存数据
                        List<TagModel> fallbackLabels = getCachedLabels(context, true);
                        if (fallbackLabels != null && !fallbackLabels.isEmpty()) {
                            Log.d(TAG, "HTTP error, returning cached labels as fallback");
                            mainHandler.post(() -> callback.onSuccess(fallbackLabels));
                        } else {
                            mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                        }
                        return;
                    }

                    // 解析响应
                    Type responseType = new TypeToken<ApiResponseModel<LabelsResponseModel>>(){}.getType();
                    ApiResponseModel<LabelsResponseModel> apiResponse = gson.fromJson(responseBody, responseType);

                    if (apiResponse.isSuccess()) {
                        if (apiResponse.hasData()) {
                            LabelsResponseModel labelsResponse = apiResponse.getData();
                            List<TagModel> tagModels = convertToTagModels(labelsResponse);

                            // 缓存标签数据（包括空数据）
                            cacheLabels(context, tagModels);

                            mainHandler.post(() -> callback.onSuccess(tagModels));
                        } else {
                            // 接口成功但数据为空，缓存空数据并返回空列表
                            Log.d(TAG, "接口成功返回但数据为空，清除缓存并返回空列表");
                            cacheLabels(context, new ArrayList<>());
                            mainHandler.post(() -> callback.onSuccess(new ArrayList<>()));
                        }
                    } else {
                        ApiExceptionUtils.ApiException apiException =
                                ApiExceptionUtils.handleBusinessException(apiResponse.getCode(), apiResponse.getMessage());
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                    }

                } catch (Exception e) {
                    Log.e(TAG, "getLabels parse error: " + e.getMessage());
                    ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }
        });
    }

    /**
     * 提交用户选择的标签
     * @param context 上下文
     * @param selectedTags 选中的标签列表
     * @param callback 回调接口
     */
    public static void submitLabels(Context context, List<TagModel> selectedTags, SubmitCallback callback) {
        Log.d(TAG, "submitLabels called with " + (selectedTags != null ? selectedTags.size() : 0) + " tags");

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 环境判断
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境返回模拟成功
            Log.d(TAG, "Dev environment: returning mock submit success");
            mainHandler.post(callback::onSuccess);
            return;
        }

        // 参数验证
        if (selectedTags == null || selectedTags.isEmpty()) {
            mainHandler.post(() -> callback.onError("No tags selected"));
            return;
        }

        // 转换为API请求格式
        List<String> labelIds = convertToLabelIds(selectedTags);
        SubmitLabelsRequestModel requestModel = new SubmitLabelsRequestModel(labelIds);

        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 发送API请求
        String url = LabelApiConstantsUtils.buildApiUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                LabelApiConstantsUtils.API_SUBMIT_LABELS
        );

        String jsonBody = gson.toJson(requestModel);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "submitLabels network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "submitLabels response: " + response.code());

                    if (!response.isSuccessful()) {
                        ApiExceptionUtils.ApiException apiException =
                                ApiExceptionUtils.handleHttpException(response.code(), responseBody);
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                        return;
                    }

                    // 解析响应
                    ApiResponseModel<String> apiResponse = gson.fromJson(responseBody, ApiResponseModel.class);
                    if (apiResponse.isSuccess()) {
                        mainHandler.post(callback::onSuccess);
                    } else {
                        ApiExceptionUtils.ApiException apiException =
                                ApiExceptionUtils.handleBusinessException(apiResponse.getCode(), apiResponse.getMessage());
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                    }

                } catch (Exception e) {
                    Log.e(TAG, "submitLabels parse error: " + e.getMessage());
                    ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }
        });
    }

    /**
     * 将LabelsResponseModel转换为TagModel列表
     * @param labelsResponse API响应数据
     * @return TagModel列表
     */
    private static List<TagModel> convertToTagModels(LabelsResponseModel labelsResponse) {
        List<TagModel> tagModels = new ArrayList<>();

        if (labelsResponse != null && labelsResponse.hasLabels()) {
            for (LabelsResponseModel.LabelItem labelItem : labelsResponse.getLabels()) {
                if (labelItem.isValid()) {
                    TagModel tagModel = new TagModel(labelItem.getLabelName(), labelItem.getLabelId());
                    tagModels.add(tagModel);
                }
            }
        }

        Log.d(TAG, "Converted " + tagModels.size() + " labels to TagModels");
        return tagModels;
    }

    /**
     * 将TagModel列表转换为标签ID列表
     * @param tagModels TagModel列表
     * @return 标签ID列表
     */
    private static List<String> convertToLabelIds(List<TagModel> tagModels) {
        List<String> labelIds = new ArrayList<>();

        for (TagModel tagModel : tagModels) {
            if (tagModel.isSelected() && !tagModel.isEmpty()) {
                // 使用type字段作为labelId（在convertToTagModels中设置）
                String labelId = tagModel.getType();
                if (labelId != null && !labelId.trim().isEmpty()) {
                    labelIds.add(labelId);
                }
            }
        }

        Log.d(TAG, "Converted " + labelIds.size() + " selected tags to label IDs");
        return labelIds;
    }

    /**
     * 创建静态标签数据（dev环境使用）
     * @return 静态标签列表
     */
    private static List<TagModel> createStaticLabels() {
        List<TagModel> staticLabels = new ArrayList<>();

        // 模拟API返回的标签数据
        staticLabels.add(new TagModel("Romance", "1"));
        staticLabels.add(new TagModel("Action", "2"));
        staticLabels.add(new TagModel("Comedy", "3"));
        staticLabels.add(new TagModel("Drama", "4"));
        staticLabels.add(new TagModel("Thriller", "5"));
        staticLabels.add(new TagModel("Horror", "6"));
        staticLabels.add(new TagModel("Sci-Fi", "7"));
        staticLabels.add(new TagModel("Fantasy", "8"));

        Log.d(TAG, "Created " + staticLabels.size() + " static labels");
        return staticLabels;
    }

    /**
     * 缓存标签数据（使用SmartCacheManager支持空数据缓存）
     * @param context 上下文
     * @param labels 标签列表
     */
    private static void cacheLabels(Context context, List<TagModel> labels) {
        try {
            // 使用SmartCacheManager进行智能缓存（支持空数据）
            com.android.video.cache.SmartCacheManager cacheManager =
                com.android.video.cache.SmartCacheManager.getInstance(context);
            boolean success = cacheManager.cacheDataSmart("labels_data", labels);

            if (success) {
                Log.d(TAG, "Labels cached successfully using SmartCacheManager: " +
                      (labels != null ? labels.size() : 0) + " items");
            } else {
                Log.e(TAG, "Failed to cache labels using SmartCacheManager");
            }

            // 保留原有的SharedPreferences缓存作为备用
            SharedPreferences prefs = context.getSharedPreferences(CACHE_PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();

            String labelsJson = gson.toJson(labels);
            editor.putString(CACHE_KEY_LABELS, labelsJson);
            editor.putLong(CACHE_KEY_TIMESTAMP, System.currentTimeMillis());
            editor.apply();

        } catch (Exception e) {
            Log.e(TAG, "Failed to cache labels: " + e.getMessage());
        }
    }

    /**
     * 获取缓存的标签数据
     * @param context 上下文
     * @return 缓存的标签列表，如果无缓存或过期则返回null
     */
    private static List<TagModel> getCachedLabels(Context context) {
        return getCachedLabels(context, false);
    }

    /**
     * 获取缓存的标签数据
     * @param context 上下文
     * @param ignoreCacheExpiry 是否忽略缓存过期时间
     * @return 缓存的标签列表，如果无缓存或过期则返回null
     */
    private static List<TagModel> getCachedLabels(Context context, boolean ignoreCacheExpiry) {
        try {
            // 优先使用SmartCacheManager获取缓存
            com.android.video.cache.SmartCacheManager cacheManager =
                com.android.video.cache.SmartCacheManager.getInstance(context);
            List<TagModel> smartCachedLabels = cacheManager.getCachedDataSmart("labels_data",
                new com.google.gson.reflect.TypeToken<List<TagModel>>(){});

            if (smartCachedLabels != null) {
                Log.d(TAG, "Retrieved " + smartCachedLabels.size() + " labels from SmartCacheManager");
                return smartCachedLabels;
            }

            // 如果SmartCacheManager没有数据，回退到SharedPreferences
            SharedPreferences prefs = context.getSharedPreferences(CACHE_PREF_NAME, Context.MODE_PRIVATE);

            String labelsJson = prefs.getString(CACHE_KEY_LABELS, null);
            long cacheTimestamp = prefs.getLong(CACHE_KEY_TIMESTAMP, 0);

            if (labelsJson == null || labelsJson.isEmpty()) {
                return null;
            }

            // 检查缓存是否过期
            if (!ignoreCacheExpiry) {
                long currentTime = System.currentTimeMillis();
                if (currentTime - cacheTimestamp > GlobalConfigUtils.LABELS_CACHE_DURATION) {
                    Log.d(TAG, "SharedPreferences cache expired, ignoring cached labels");
                    return null;
                }
            }

            Type listType = new TypeToken<List<TagModel>>(){}.getType();
            List<TagModel> cachedLabels = gson.fromJson(labelsJson, listType);

            Log.d(TAG, "Retrieved " + (cachedLabels != null ? cachedLabels.size() : 0) + " cached labels from SharedPreferences");
            return cachedLabels;

        } catch (Exception e) {
            Log.e(TAG, "Failed to get cached labels: " + e.getMessage());
            return null;
        }
    }

    /**
     * 清除标签缓存
     * @param context 上下文
     */
    public static void clearCache(Context context) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(CACHE_PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.clear();
            editor.apply();
            Log.d(TAG, "Label cache cleared");
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear cache: " + e.getMessage());
        }
    }
}
