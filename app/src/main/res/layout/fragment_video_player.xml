<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">

    <!-- ExoPlayer PlayerView -->
    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/black"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:use_controller="false"
        app:resize_mode="zoom"
        app:surface_type="texture_view" />

    <!-- Danmaku View -->
    <master.flame.danmaku.ui.widget.DanmakuView
        android:id="@+id/danmakuView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Loading Progress -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_video_loading"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Gesture Detection Overlay - 放在按钮之前，这样按钮会在覆盖层之上 -->
    <View
        android:id="@+id/gestureOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent"
        android:clickable="false"
        android:focusable="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Seek Backward Button -->
    <ImageView
        android:id="@+id/btnSeekBackward"
        android:layout_width="@dimen/video_player_seek_button_size"
        android:layout_height="@dimen/video_player_seek_button_size"
        android:padding="@dimen/video_player_seek_button_padding"
        android:src="@drawable/play_ic_5s_left"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        android:contentDescription="@string/video_player_seek_backward"
        android:layout_marginEnd="@dimen/video_player_seek_button_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/btnPlayPause"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Play/Pause Button Overlay -->
    <ImageView
        android:id="@+id/btnPlayPause"
        android:layout_width="@dimen/video_player_play_button_size"
        android:layout_height="@dimen/video_player_play_button_size"
        android:layout_centerInParent="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:src="@drawable/play_ic_play"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        android:contentDescription="@string/video_player_play_pause"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Seek Forward Button -->
    <ImageView
        android:id="@+id/btnSeekForward"
        android:layout_width="@dimen/video_player_seek_button_size"
        android:layout_height="@dimen/video_player_seek_button_size"
        android:padding="@dimen/video_player_seek_button_padding"
        android:src="@drawable/play_ic_5s_right"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        android:contentDescription="@string/video_player_seek_forward"
        android:layout_marginStart="@dimen/video_player_seek_button_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@+id/btnPlayPause"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Error Message -->
    <LinearLayout
        android:id="@+id/layoutError"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="@dimen/video_player_error_icon_size"
            android:layout_height="@dimen/video_player_error_icon_size"
            android:src="@drawable/ic_error_outline"
            android:tint="@color/video_player_error_color"
            android:contentDescription="错误图标" />

        <TextView
            android:id="@+id/tvErrorMessage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/video_player_error_text_margin_top"
            android:text="@string/error_video_playback_failed"
            android:textColor="@color/video_player_error_color"
            android:textSize="@dimen/video_player_error_text_size"
            android:gravity="center" />

        <Button
            android:id="@+id/btnRetry"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/video_player_error_button_margin_top"
            android:text="重试"
            android:textColor="@color/video_player_primary_text"
            android:background="@drawable/video_player_control_button_bg"
            android:paddingHorizontal="@dimen/video_player_error_button_padding_horizontal"
            android:paddingVertical="@dimen/video_player_error_button_padding_vertical" />

    </LinearLayout>



    <!-- Video Info Overlay (for debugging) -->
    <LinearLayout
        android:id="@+id/layoutVideoInfo"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/debug_label_bg"
        android:padding="@dimen/video_player_debug_info_padding"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:layout_margin="@dimen/video_player_debug_info_margin">

        <TextView
            android:id="@+id/tvVideoTitle"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/video_player_primary_text"
            android:textSize="@dimen/video_player_debug_text_size"
            android:text="Video Title"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvVideoResolution"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/video_player_secondary_text"
            android:textSize="@dimen/video_player_debug_text_size"
            android:text="1920x1080"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvVideoDuration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/video_player_secondary_text"
            android:textSize="@dimen/video_player_debug_text_size"
            android:text="00:00 / 00:00"
            tools:ignore="HardcodedText" />

        <TextView
            android:id="@+id/tvVideoPosition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/video_player_secondary_text"
            android:textSize="@dimen/video_player_debug_text_size"
            android:text="Position: 0"
            tools:ignore="HardcodedText" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
