package com.android.video.utils;

import android.content.Context;
import android.net.Uri;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * Uri处理工具类
 * 
 * 提供Uri与File之间的转换功能，特别是处理content://类型的Uri
 */
public class UriUtils {
    
    private static final String TAG = "UriUtils";
    
    /**
     * 将Uri转换为File对象
     * 
     * @param context 上下文
     * @param uri 要转换的Uri
     * @param prefix 临时文件名前缀
     * @return 转换后的File对象，失败时返回null
     */
    public static File uriToFile(Context context, Uri uri, String prefix) {
        if (context == null || uri == null) {
            Log.e(TAG, "Context或Uri为空");
            return null;
        }
        
        try {
            String scheme = uri.getScheme();
            Log.d(TAG, "处理Uri: " + uri.toString() + ", scheme: " + scheme);
            
            if ("file".equals(scheme)) {
                // file://类型的Uri，直接转换为File
                String path = uri.getPath();
                if (path != null) {
                    File file = new File(path);
                    if (file.exists()) {
                        Log.d(TAG, "file://类型Uri转换成功: " + path);
                        return file;
                    } else {
                        Log.e(TAG, "file://类型Uri对应的文件不存在: " + path);
                        return null;
                    }
                }
            } else if ("content".equals(scheme)) {
                // content://类型的Uri，需要复制到临时文件
                return copyContentUriToFile(context, uri, prefix);
            } else {
                Log.e(TAG, "不支持的Uri scheme: " + scheme);
                return null;
            }
        } catch (Exception e) {
            Log.e(TAG, "Uri转换为File失败", e);
            return null;
        }
        
        return null;
    }
    
    /**
     * 将content://类型的Uri复制到临时文件
     * 
     * @param context 上下文
     * @param uri content://类型的Uri
     * @param prefix 临时文件名前缀
     * @return 复制后的临时文件
     */
    private static File copyContentUriToFile(Context context, Uri uri, String prefix) {
        InputStream inputStream = null;
        FileOutputStream outputStream = null;
        
        try {
            // 打开输入流
            inputStream = context.getContentResolver().openInputStream(uri);
            if (inputStream == null) {
                Log.e(TAG, "无法打开Uri的输入流: " + uri.toString());
                return null;
            }
            
            // 创建临时文件
            String fileName = prefix + "_" + System.currentTimeMillis() + ".jpg";
            File tempFile = new File(context.getExternalFilesDir(null), fileName);
            
            // 确保父目录存在
            File parentDir = tempFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                if (!parentDir.mkdirs()) {
                    Log.e(TAG, "无法创建父目录: " + parentDir.getAbsolutePath());
                    return null;
                }
            }
            
            // 复制文件内容
            outputStream = new FileOutputStream(tempFile);
            byte[] buffer = new byte[4096];
            int bytesRead;
            long totalBytes = 0;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }
            
            outputStream.flush();
            
            Log.d(TAG, "content://类型Uri复制成功: " + tempFile.getAbsolutePath() + 
                  ", 大小: " + totalBytes + " bytes");
            
            return tempFile;
            
        } catch (IOException e) {
            Log.e(TAG, "复制content://类型Uri失败", e);
            return null;
        } finally {
            // 关闭流
            try {
                if (inputStream != null) {
                    inputStream.close();
                }
                if (outputStream != null) {
                    outputStream.close();
                }
            } catch (IOException e) {
                Log.e(TAG, "关闭流时发生错误", e);
            }
        }
    }
    
    /**
     * 将Uri转换为File对象（使用默认前缀）
     * 
     * @param context 上下文
     * @param uri 要转换的Uri
     * @return 转换后的File对象，失败时返回null
     */
    public static File uriToFile(Context context, Uri uri) {
        return uriToFile(context, uri, "temp_file");
    }
    
    /**
     * 将Uri字符串转换为File对象
     * 
     * @param context 上下文
     * @param uriString Uri字符串
     * @param prefix 临时文件名前缀
     * @return 转换后的File对象，失败时返回null
     */
    public static File uriStringToFile(Context context, String uriString, String prefix) {
        if (uriString == null || uriString.trim().isEmpty()) {
            Log.e(TAG, "Uri字符串为空");
            return null;
        }
        
        try {
            Uri uri = Uri.parse(uriString);
            return uriToFile(context, uri, prefix);
        } catch (Exception e) {
            Log.e(TAG, "解析Uri字符串失败: " + uriString, e);
            return null;
        }
    }
    
    /**
     * 将Uri字符串转换为File对象（使用默认前缀）
     * 
     * @param context 上下文
     * @param uriString Uri字符串
     * @return 转换后的File对象，失败时返回null
     */
    public static File uriStringToFile(Context context, String uriString) {
        return uriStringToFile(context, uriString, "temp_file");
    }
    
    /**
     * 检查Uri是否有效
     * 
     * @param context 上下文
     * @param uri 要检查的Uri
     * @return 如果Uri有效且可访问则返回true
     */
    public static boolean isValidUri(Context context, Uri uri) {
        if (context == null || uri == null) {
            return false;
        }
        
        try {
            String scheme = uri.getScheme();
            if ("file".equals(scheme)) {
                String path = uri.getPath();
                return path != null && new File(path).exists();
            } else if ("content".equals(scheme)) {
                InputStream inputStream = context.getContentResolver().openInputStream(uri);
                if (inputStream != null) {
                    inputStream.close();
                    return true;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "检查Uri有效性失败", e);
        }
        
        return false;
    }
    
    /**
     * 检查Uri字符串是否有效
     * 
     * @param context 上下文
     * @param uriString Uri字符串
     * @return 如果Uri字符串有效且可访问则返回true
     */
    public static boolean isValidUriString(Context context, String uriString) {
        if (uriString == null || uriString.trim().isEmpty()) {
            return false;
        }
        
        try {
            Uri uri = Uri.parse(uriString);
            return isValidUri(context, uri);
        } catch (Exception e) {
            Log.e(TAG, "检查Uri字符串有效性失败: " + uriString, e);
            return false;
        }
    }
    
    /**
     * 清理临时文件
     * 
     * @param file 要清理的文件
     */
    public static void cleanupTempFile(File file) {
        if (file != null && file.exists() && file.getName().contains("temp_")) {
            try {
                if (file.delete()) {
                    Log.d(TAG, "临时文件清理成功: " + file.getAbsolutePath());
                } else {
                    Log.w(TAG, "临时文件清理失败: " + file.getAbsolutePath());
                }
            } catch (Exception e) {
                Log.e(TAG, "清理临时文件时发生错误", e);
            }
        }
    }
}
