<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/download_item_height"
    android:layout_marginBottom="@dimen/download_item_margin_bottom"
    android:background="@android:color/transparent">

    <!-- Delete button (top right) -->
    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/download_delete_icon_size"
        android:layout_height="@dimen/download_delete_icon_size"
        android:layout_alignParentTop="true"
        android:layout_alignParentEnd="true"
        android:layout_marginEnd="@dimen/download_delete_margin_end"
        android:src="@drawable/dw_ic_delete"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true" />

    <!-- Video poster -->
    <ImageView
        android:id="@+id/iv_poster"
        android:layout_width="@dimen/download_poster_width"
        android:layout_height="@dimen/download_poster_height"
        android:layout_alignParentStart="true"
        android:layout_marginTop="@dimen/download_poster_margin_top"
        android:scaleType="centerCrop"
        android:src="@drawable/movie_poster"
        android:background="@drawable/download_poster_bg"
        android:clipToOutline="true" />

    <!-- Content area (right of poster) -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/iv_poster"
        android:layout_toStartOf="@id/btn_play"
        android:layout_marginStart="@dimen/download_content_margin_start"
        android:layout_marginTop="@dimen/download_content_margin_top"
        android:layout_marginEnd="@dimen/download_content_margin_end"
        android:orientation="vertical">

        <!-- Video title -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="@dimen/download_title_width"
            android:layout_height="@dimen/download_title_height"
            android:text="30 years frozen"
            android:textColor="#ffffffff"
            android:textSize="@dimen/download_title_text_size"
            android:maxLines="1"
            android:ellipsize="end"
            android:fontFamily="sans-serif" />

        <!-- Downloaded status -->
        <TextView
            android:id="@+id/tv_downloaded_status"
            android:layout_width="@dimen/download_status_width"
            android:layout_height="@dimen/download_status_height"
            android:layout_marginTop="@dimen/download_status_margin_top"
            android:text="Downloaded/Total episodes"
            android:textColor="#59ffffff"
            android:textSize="@dimen/download_status_text_size"
            android:fontFamily="sans-serif" />

        <!-- Progress container -->
        <LinearLayout
            android:id="@+id/ll_episode_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/download_progress_margin_top"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:clickable="true"
            android:focusable="true"
            android:background="?android:attr/selectableItemBackgroundBorderless">

            <!-- Downloaded count (red) -->
            <TextView
                android:id="@+id/tv_downloaded_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="EP.24"
                android:textColor="#fff12626"
                android:textSize="@dimen/download_count_text_size"
                android:fontFamily="sans-serif" />

            <!-- Separator -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="/"
                android:textColor="#a3ffffff"
                android:textSize="@dimen/download_count_text_size"
                android:fontFamily="sans-serif" />

            <!-- Total count -->
            <TextView
                android:id="@+id/tv_total_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="EP.72"
                android:textColor="#a3ffffff"
                android:textSize="@dimen/download_count_text_size"
                android:fontFamily="sans-serif" />

            <!-- Arrow icon -->
            <ImageView
                android:layout_width="@dimen/download_arrow_icon_size"
                android:layout_height="@dimen/download_arrow_icon_size"
                android:layout_marginStart="@dimen/download_arrow_margin_start"
                android:src="@drawable/dw_ic_click" />

        </LinearLayout>

    </LinearLayout>

    <!-- Play button -->
    <LinearLayout
        android:id="@+id/btn_play"
        android:layout_width="@dimen/download_play_button_width"
        android:layout_height="@dimen/download_play_button_height"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/download_play_button_margin_end"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/download_play_button_bg"
        android:clickable="true"
        android:focusable="true">

        <!-- Play icon -->
        <ImageView
            android:layout_width="@dimen/download_play_icon_size"
            android:layout_height="@dimen/download_play_icon_size"
            android:layout_marginEnd="@dimen/download_play_icon_margin_end"
            android:src="@drawable/list_ic_play" />

        <!-- Play text -->
        <TextView
            android:layout_width="@dimen/download_play_text_width"
            android:layout_height="@dimen/download_play_text_height"
            android:text="Play"
            android:textColor="#ffffffff"
            android:textSize="@dimen/download_play_text_size"
            android:gravity="center"
            android:fontFamily="sans-serif" />

    </LinearLayout>

</RelativeLayout>
