package com.android.video;

import android.app.Application;
import android.util.Log;
import com.android.video.network.ApiClientUtils;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.InitDeviceResponseModel;
import com.android.video.utils.UserSessionUtils;

/**
 * 视频播放器应用程序类 - 应用启动时的全局初始化
 * <AUTHOR> Team
 */
public class VideoPlayerApplication extends Application {

    private static final String TAG = "VideoPlayerApplication";

    @Override
    public void onCreate() {
        super.onCreate();
        
        Log.d(TAG, "🚀 VideoPlayerApplication onCreate - 应用启动");
        
        // 初始化网络组件
        initializeNetworkComponents();
        
        // 初始化TokenManager（会自动调用初始化设备接口）
        initializeTokenManager();
    }

    /**
     * 初始化网络组件
     */
    private void initializeNetworkComponents() {
        try {
            // 初始化ApiClientUtils
            ApiClientUtils.initialize(this);

            // 初始化ApiHeaderUtils
            com.android.video.utils.ApiHeaderUtils.initialize(this);

            Log.d(TAG, "✅ Network components initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "❌ Failed to initialize network components", e);
        }
    }

    /**
     * 初始化TokenManager
     */
    private void initializeTokenManager() {
        Log.d(TAG, "🔧 开始初始化TokenManager...");

        com.android.video.manager.TokenManager tokenManager =
            com.android.video.manager.TokenManager.getInstance(this);

        tokenManager.initialize(new com.android.video.manager.TokenManager.TokenInitCallback() {
            @Override
            public void onInitialized(boolean success, String message) {
                if (success) {
                    Log.d(TAG, "✅ TokenManager初始化成功: " + message);
                    // 验证token是否正确设置到ApiHeaderUtils
                    String currentToken = com.android.video.utils.ApiHeaderUtils.getCurrentAccessToken();
                    if (currentToken != null && !currentToken.trim().isEmpty()) {
                        Log.d(TAG, "✅ Token已正确设置到ApiHeaderUtils");
                    } else {
                        Log.w(TAG, "⚠️ Token未正确设置到ApiHeaderUtils");
                    }
                } else {
                    Log.e(TAG, "❌ TokenManager初始化失败: " + message);
                }
            }
        });
    }
}
