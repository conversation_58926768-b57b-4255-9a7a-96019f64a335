<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="16dp"
    android:clickable="true"
    android:focusable="true">

    <!-- 中间视频海报容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_container"
        android:layout_width="@dimen/coming_soon_detail_item_width"
        android:layout_height="@dimen/coming_soon_detail_item_height"
        android:layout_marginStart="16dp"
        android:background="@drawable/home_popular_series_border_new"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 海报 -->
        <ImageView
            android:id="@+id/iv_video_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/home_popular_series_item_border_width"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/home_popular_series_inner_border"
            android:clipToOutline="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 右侧信息容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_info_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        app:layout_constraintTop_toTopOf="@id/cl_poster_container"
        app:layout_constraintStart_toEndOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 视频标题 -->
        <TextView
            android:id="@+id/tv_video_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="哪吒之魔童降世2"
            android:textColor="#FFFFFF"
            android:textSize="16sp"
            android:fontFamily="sans-serif-medium"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- Coming Soon标签 -->
        <TextView
            android:id="@+id/tv_coming_soon_tag"
            android:layout_width="82dp"
            android:layout_height="20dp"
            android:layout_marginTop="6dp"
            android:text="coming soon"
            android:textColor="#B936CF"
            android:textSize="12sp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:background="@drawable/coming_soon_tag_background"
            app:layout_constraintTop_toBottomOf="@id/tv_video_title"
            app:layout_constraintStart_toStartOf="parent" />

        <!-- 视频描述 -->
        <TextView
            android:id="@+id/tv_video_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:text="哪吒的全新冒险即将开始，这一次他将面临更大的挑战，守护自己珍视的一切。"
            android:textColor="#FFFFFF"
            android:textSize="@dimen/home_video_description_text_size"
            android:fontFamily="sans-serif-light"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/tv_coming_soon_tag"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 上映时间容器 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_release_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="14dp"
            app:layout_constraintTop_toBottomOf="@id/tv_video_description"
            app:layout_constraintStart_toStartOf="parent">

            <!-- 时间图标 -->
            <ImageView
                android:id="@+id/iv_time_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/home_ic_time"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_release_time"
                app:layout_constraintHorizontal_chainStyle="packed" />

            <!-- 上映时间文本 -->
            <TextView
                android:id="@+id/tv_release_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="2dp"
                android:text="2025-06-18"
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:fontFamily="sans-serif"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_time_icon"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <!-- 订阅提醒按钮 -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_notify_button"
            android:layout_width="0dp"
            android:layout_height="@dimen/home_notify_button_height"
            android:layout_marginTop="12dp"
            android:background="@drawable/coming_soon_detail_notify_button_selector"
            android:clickable="true"
            android:focusable="true"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            app:layout_constraintTop_toBottomOf="@id/cl_release_time"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

            <!-- 通知图标 -->
            <ImageView
                android:id="@+id/iv_notify_icon"
                android:layout_width="14dp"
                android:layout_height="14dp"
                android:src="@drawable/home_ic_notify_0"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_notify_text"
                app:layout_constraintHorizontal_chainStyle="packed" />

            <!-- 通知文本 -->
            <TextView
                android:id="@+id/tv_notify_text"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="Notify me"
                android:textColor="@color/white"
                android:textSize="@dimen/home_notify_button_text_size"
                android:fontFamily="sans-serif"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_notify_icon"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
