<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- 选中状态 - 显示渐变背景 -->
    <item android:state_selected="true">
        <layer-list>
            <!-- 渐变背景 -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:type="linear"
                        android:startColor="#ff6c5ce7"
                        android:endColor="#ffb936cf"
                        android:angle="135" />
                    <corners android:radius="@dimen/subscribe_card_corner_radius" />
                </shape>
            </item>

            <!-- 边框 -->
            <item>
                <shape android:shape="rectangle">
                    <stroke
                        android:width="@dimen/subscribe_card_stroke_width"
                        android:color="#7ab936cf" />
                    <corners android:radius="@dimen/subscribe_card_corner_radius" />
                </shape>
            </item>
        </layer-list>
    </item>

    <!-- 默认状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1affffff" />
            <corners android:radius="@dimen/subscribe_card_corner_radius" />
        </shape>
    </item>
</selector>
