package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import java.util.List;
import java.util.Random;

/**
 * Categories页面视频列表适配器
 * <AUTHOR>
 */
public class CategoriesVideoAdapter extends RecyclerView.Adapter<CategoriesVideoAdapter.VideoViewHolder> {

    private List<VideoModel> videoList;
    private OnVideoItemClickListener onItemClickListener;
    private Random random = new Random();

    public interface OnVideoItemClickListener {
        void onVideoClick(VideoModel video, int position);
    }

    public CategoriesVideoAdapter(List<VideoModel> videoList) {
        this.videoList = videoList;
    }

    public void setOnItemClickListener(OnVideoItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public VideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_categories_video, parent, false);
        return new VideoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull VideoViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return videoList != null ? videoList.size() : 0;
    }

    public class VideoViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private TextView tvTitle;
        private TextView tvEpisode;
        private TextView tvSynopsis;

        public VideoViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvEpisode = itemView.findViewById(R.id.tv_episode);
            tvSynopsis = itemView.findViewById(R.id.tv_synopsis);
        }

        public void bind(VideoModel video, int position) {
            if (video == null) return;

            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(ivPoster);
            } else {
                // 如果没有URL，使用默认图片
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置视频标题
            tvTitle.setText(video.getTitle());

            // 设置集数信息
            if (video.getTotalEpisodes() > 0) {
                tvEpisode.setText("EP." + video.getTotalEpisodes());
            } else if (video.getTotalEpisodes() == 0) {
                // 如果API返回的是0，显示"暂无集数"或隐藏
                tvEpisode.setText("EP.0");
            } else {
                // 如果没有设置集数信息（默认值-1），使用随机数作为后备
                int episodeNumber = random.nextInt(100) + 1; // 1-100集
                tvEpisode.setText("EP." + episodeNumber);
            }

            // 设置视频概要
            tvSynopsis.setText(video.getSynopsis());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    onItemClickListener.onVideoClick(video, position);
                }
            });
        }
    }

    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList;
        notifyDataSetChanged();
    }

    /**
     * 添加视频列表（用于分页加载更多）
     *
     * @param newVideoList 新的视频列表
     */
    public void addVideoList(List<VideoModel> newVideoList) {
        if (newVideoList != null && !newVideoList.isEmpty()) {
            int startPosition = this.videoList.size();
            this.videoList.addAll(newVideoList);
            notifyItemRangeInserted(startPosition, newVideoList.size());
        }
    }

    /**
     * 获取当前视频列表大小
     *
     * @return 视频列表大小
     */
    public int getVideoListSize() {
        return videoList != null ? videoList.size() : 0;
    }

    /**
     * 清空视频列表
     */
    public void clearVideoList() {
        if (videoList != null) {
            videoList.clear();
            notifyDataSetChanged();
        }
    }
}
