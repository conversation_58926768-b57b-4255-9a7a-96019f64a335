package com.android.video.network;

import android.util.Log;

import java.io.IOException;

import okhttp3.Interceptor;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 缓存调试拦截器
 * 用于调试和跟踪缓存行为
 * <AUTHOR> Team
 */
public class CacheDebugInterceptor implements Interceptor {
    
    private static final String TAG = "CacheDebug";
    
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();
        
        // 记录请求信息
        Log.d(TAG, "=== REQUEST ===");
        Log.d(TAG, "URL: " + request.url());
        Log.d(TAG, "Method: " + request.method());
        Log.d(TAG, "Cache-Control: " + request.header("Cache-Control"));
        Log.d(TAG, "Request X-Access-Token: " + request.header("X-Access-Token"));
        
        long startTime = System.currentTimeMillis();
        Response response = chain.proceed(request);
        long endTime = System.currentTimeMillis();
        
        // 记录响应信息
        Log.d(TAG, "=== RESPONSE ===");
        Log.d(TAG, "URL: " + response.request().url());
        Log.d(TAG, "Code: " + response.code());
        Log.d(TAG, "Time: " + (endTime - startTime) + "ms");
        Log.d(TAG, "Cache-Control: " + response.header("Cache-Control"));
        Log.d(TAG, "From Cache: " + (response.cacheResponse() != null));
        Log.d(TAG, "From Network: " + (response.networkResponse() != null));
        Log.d(TAG, "Response X-Access-Token: " + response.header("X-Access-Token"));
        if (response.cacheResponse() != null) {
            Log.d(TAG, "✓ CACHE HIT - Response served from cache");
        } else if (response.networkResponse() != null) {
            Log.d(TAG, "✗ CACHE MISS - Response from network");
        }
        
        Log.d(TAG, "================");
        
        return response;
    }
}
