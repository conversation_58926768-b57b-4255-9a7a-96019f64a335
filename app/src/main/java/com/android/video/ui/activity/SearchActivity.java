package com.android.video.ui.activity;

import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.android.video.model.SearchResultModel;
import com.android.video.network.HomeApiService;
import com.android.video.ui.adapter.SearchResultAdapter;
import com.android.video.ui.adapter.RecentSearchAdapter;
import com.android.video.ui.activity.VideoDetailActivity;
import com.android.video.config.EnvironmentConfigUtils;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.android.flexbox.JustifyContent;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import com.android.video.base.BaseMultiLanguageActivity;

/**
 * 搜索页面Activity
 * 实现搜索功能，包含最近搜索和热门搜索
 */
public class SearchActivity extends BaseMultiLanguageActivity {

    private static final String TAG = "SearchActivity";
    private static final String PREFS_NAME = "search_prefs";
    private static final String KEY_RECENT_SEARCHES = "recent_searches";
    private static final int MAX_RECENT_SEARCHES = 10;

    // UI组件
    private ImageView ivBack;
    private EditText etSearch;
    private ImageView ivClear;
    private ImageView ivSearchIcon;
    private ImageView ivClearRecentSearches;
    private RecyclerView rvRecentSearches;
    private RecyclerView rvTrendingSearches;

    // 适配器
    private SearchResultAdapter trendingSearchAdapter;
    private RecentSearchAdapter recentSearchAdapter;

    // 数据
    private List<SearchResultModel> trendingSearches;
    private List<String> recentSearches;

    // SharedPreferences
    private SharedPreferences sharedPreferences;
    private Gson gson;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search);

        initSharedPreferences();
        initViews();
        initData();
        setupClickListeners();
        setupSearchFunctionality();
    }

    /**
     * 初始化SharedPreferences
     */
    private void initSharedPreferences() {
        sharedPreferences = getSharedPreferences(PREFS_NAME, MODE_PRIVATE);
        gson = new Gson();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        etSearch = findViewById(R.id.et_search);
        ivClear = findViewById(R.id.iv_clear);
        ivSearchIcon = findViewById(R.id.iv_search_icon);
        ivClearRecentSearches = findViewById(R.id.iv_clear_recent_searches);
        rvRecentSearches = findViewById(R.id.rv_recent_searches);
        rvTrendingSearches = findViewById(R.id.rv_trending_searches);

        // 确保搜索框可以获得焦点
        etSearch.setFocusable(true);
        etSearch.setFocusableInTouchMode(true);
        etSearch.setClickable(true);

        // 初始化清除按钮可见性
        updateClearButtonVisibility();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        // 初始化搜索历史列表（避免null指针异常）
        if (recentSearches == null) {
            recentSearches = new ArrayList<>();
        }

        // 初始化热门搜索数据（空列表，将通过API加载）
        trendingSearches = new ArrayList<>();

        setupRecyclerViews();

        // 异步加载最近搜索数据
        loadRecentSearches();

        // 异步加载热门搜索数据
        loadTrendingSearches();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerViews() {
        // 设置最近搜索RecyclerView（使用FlexboxLayoutManager实现自适应换行）
        FlexboxLayoutManager flexboxLayoutManager = new FlexboxLayoutManager(this);
        flexboxLayoutManager.setFlexDirection(FlexDirection.ROW);
        flexboxLayoutManager.setFlexWrap(FlexWrap.WRAP);
        flexboxLayoutManager.setJustifyContent(JustifyContent.FLEX_START);
        rvRecentSearches.setLayoutManager(flexboxLayoutManager);

        recentSearchAdapter = new RecentSearchAdapter(recentSearches, this::onRecentSearchClick);
        rvRecentSearches.setAdapter(recentSearchAdapter);

        // 设置热门搜索RecyclerView
        rvTrendingSearches.setLayoutManager(new LinearLayoutManager(this));
        trendingSearchAdapter = new SearchResultAdapter(trendingSearches, this::onTrendingSearchClick);
        rvTrendingSearches.setAdapter(trendingSearchAdapter);

        updateRecentSearchesVisibility();
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // 清除搜索框内容
        ivClear.setOnClickListener(v -> {
            etSearch.setText("");
            etSearch.requestFocus();
        });

        // 清除最近搜索
        ivClearRecentSearches.setOnClickListener(v -> {
            clearSearchHistory();
        });
    }

    /**
     * 设置搜索功能
     */
    private void setupSearchFunctionality() {
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // 更新清除按钮可见性
                updateClearButtonVisibility();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // 设置搜索按钮监听
        etSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    performSearch(etSearch.getText().toString().trim());
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 最近搜索点击事件
     */
    private void onRecentSearchClick(String searchTerm) {
        // 将点击的搜索词添加到搜索框
        etSearch.setText(searchTerm);
        etSearch.setSelection(searchTerm.length());

        // 执行搜索
        performSearch(searchTerm);
    }

    /**
     * 热门搜索点击事件
     */
    private void onTrendingSearchClick(SearchResultModel searchResult) {
        // 检查是否有filmLanguageInfoId，如果有则直接跳转到视频详情页
        if (searchResult.getFilmLanguageInfoId() != null && !searchResult.getFilmLanguageInfoId().isEmpty()) {
            // 直接跳转到视频详情页面
            Intent intent = new Intent(this, VideoDetailActivity.class);
            intent.putExtra("filmLanguageInfoId", searchResult.getFilmLanguageInfoId());
            intent.putExtra("filmTitle", searchResult.getTitle());
            startActivity(intent);

            Log.d(TAG, "热门搜索直接跳转到详情页: " + searchResult.getTitle() +
                  ", filmLanguageInfoId: " + searchResult.getFilmLanguageInfoId());
        } else {
            // 如果没有filmLanguageInfoId，则执行搜索（回退方案）
            etSearch.setText(searchResult.getTitle());
            etSearch.setSelection(searchResult.getTitle().length());
            performSearch(searchResult.getTitle());

            Log.d(TAG, "热门搜索回退到搜索模式: " + searchResult.getTitle());
        }
    }

    /**
     * 执行搜索
     */
    private void performSearch(String searchTerm) {
        if (searchTerm.isEmpty()) {
            return;
        }

        // 添加到最近搜索记录
        addToRecentSearches(searchTerm);

        // 跳转到搜索结果页面
        Intent intent = new Intent(this, SearchResultActivity.class);
        intent.putExtra(SearchResultActivity.EXTRA_SEARCH_TERM, searchTerm);
        startActivity(intent);
    }

    /**
     * 添加到最近搜索记录
     */
    private void addToRecentSearches(String searchTerm) {
        // 如果已存在，先移除
        recentSearches.remove(searchTerm);

        // 添加到开头
        recentSearches.add(0, searchTerm);

        // 限制最大数量
        if (recentSearches.size() > MAX_RECENT_SEARCHES) {
            recentSearches = recentSearches.subList(0, MAX_RECENT_SEARCHES);
        }

        // 保存到SharedPreferences
        saveRecentSearches();

        // 更新UI
        if (recentSearchAdapter != null) {
            recentSearchAdapter.notifyDataSetChanged();
        }
        updateRecentSearchesVisibility();
    }

    /**
     * 加载最近搜索记录
     */
    private void loadRecentSearches() {
        Log.d(TAG, "开始加载搜索历史，调用API...");

        // 调用真实API获取搜索历史
        HomeApiService.getInstance().getSearchHistory(new HomeApiService.SearchHistoryCallback() {
            @Override
            public void onSuccess(List<String> searchHistory) {
                runOnUiThread(() -> {
                    Log.d(TAG, "搜索历史API获取成功: " + searchHistory.size() + " 条记录");

                    // 优先使用后端数据
                    if (searchHistory != null && !searchHistory.isEmpty()) {
                        // 后端有数据，使用后端数据
                        recentSearches.clear();
                        recentSearches.addAll(searchHistory);
                        Log.d(TAG, "使用后端搜索历史数据: " + searchHistory.size() + " 条");
                    } else {
                        // 后端没有数据，尝试使用本地数据
                        Log.d(TAG, "后端搜索历史为空，尝试加载本地数据");
                        loadRecentSearchesFromLocal();
                    }

                    // 更新UI
                    if (recentSearchAdapter != null) {
                        recentSearchAdapter.notifyDataSetChanged();
                    }
                    updateRecentSearchesVisibility();
                });
            }

            @Override
            public void onError(String errorMessage) {
                runOnUiThread(() -> {
                    Log.e(TAG, "搜索历史API获取失败: " + errorMessage);

                    // API失败时，尝试从本地存储加载
                    Log.d(TAG, "API失败，加载本地搜索历史数据");
                    loadRecentSearchesFromLocal();

                    // 更新UI
                    if (recentSearchAdapter != null) {
                        recentSearchAdapter.notifyDataSetChanged();
                    }
                    updateRecentSearchesVisibility();
                });
            }
        });
    }

    /**
     * 从本地存储加载最近搜索记录（作为后端数据为空或API失败时的备用方案）
     */
    private void loadRecentSearchesFromLocal() {
        Log.d(TAG, "开始从本地加载搜索历史");

        String json = sharedPreferences.getString(KEY_RECENT_SEARCHES, null);
        if (json != null && !json.trim().isEmpty()) {
            try {
                Type type = new TypeToken<List<String>>(){}.getType();
                List<String> localSearches = gson.fromJson(json, type);
                if (localSearches != null && !localSearches.isEmpty()) {
                    recentSearches.clear();
                    recentSearches.addAll(localSearches);
                    Log.d(TAG, "本地搜索历史加载成功: " + localSearches.size() + " 条记录");
                } else {
                    Log.d(TAG, "本地搜索历史为空");
                }
            } catch (Exception e) {
                Log.e(TAG, "解析本地搜索历史失败: " + e.getMessage());
            }
        } else {
            Log.d(TAG, "本地没有保存的搜索历史");
        }

        Log.d(TAG, "最终搜索历史数量: " + recentSearches.size());
    }

    /**
     * 保存最近搜索记录
     */
    private void saveRecentSearches() {
        String json = gson.toJson(recentSearches);
        sharedPreferences.edit().putString(KEY_RECENT_SEARCHES, json).apply();
    }

    /**
     * 更新最近搜索的可见性
     */
    private void updateRecentSearchesVisibility() {
        if (recentSearches.isEmpty()) {
            rvRecentSearches.setVisibility(View.GONE);
            ivClearRecentSearches.setVisibility(View.GONE);
        } else {
            rvRecentSearches.setVisibility(View.VISIBLE);
            ivClearRecentSearches.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 清除搜索历史
     */
    private void clearSearchHistory() {
        Log.d(TAG, "开始清除搜索历史");

        // 调用API清除后端搜索历史
        HomeApiService.getInstance().clearSearchHistory(new HomeApiService.ClearSearchHistoryCallback() {
            @Override
            public void onSuccess(String message) {
                runOnUiThread(() -> {
                    Log.d(TAG, "后端搜索历史清除成功: " + message);

                    // 清除本地搜索历史
                    recentSearches.clear();
                    saveRecentSearches();

                    // 更新UI
                    if (recentSearchAdapter != null) {
                        recentSearchAdapter.notifyDataSetChanged();
                    }
                    updateRecentSearchesVisibility();

                    Toast.makeText(SearchActivity.this, "Search history cleared", Toast.LENGTH_SHORT).show();
                });
            }

            @Override
            public void onError(String errorMessage) {
                runOnUiThread(() -> {
                    Log.e(TAG, "后端搜索历史清除失败: " + errorMessage);

                    // API失败时，仍然清除本地搜索历史
                    recentSearches.clear();
                    saveRecentSearches();

                    // 更新UI
                    if (recentSearchAdapter != null) {
                        recentSearchAdapter.notifyDataSetChanged();
                    }
                    updateRecentSearchesVisibility();

                    Toast.makeText(SearchActivity.this, "Search history cleared locally", Toast.LENGTH_SHORT).show();
                });
            }
        });
    }

    /**
     * 更新清除按钮可见性
     */
    private void updateClearButtonVisibility() {
        if (etSearch.getText().toString().trim().isEmpty()) {
            ivClear.setVisibility(View.GONE);
        } else {
            ivClear.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 加载热门搜索数据
     */
    private void loadTrendingSearches() {
        Log.d(TAG, "开始加载热门搜索数据");

        // 调用真实API获取热门搜索
        HomeApiService.getInstance().getTrendingSearches(new HomeApiService.TrendingSearchesCallback() {
            @Override
            public void onSuccess(List<SearchResultModel> apiTrendingSearches) {
                runOnUiThread(() -> {
                    Log.d(TAG, "热门搜索API获取成功: " + apiTrendingSearches.size() + " 条记录");

                    // 使用API数据
                    if (apiTrendingSearches != null && !apiTrendingSearches.isEmpty()) {
                        trendingSearches.clear();
                        trendingSearches.addAll(apiTrendingSearches);
                        Log.d(TAG, "使用API热门搜索数据: " + apiTrendingSearches.size() + " 条");
                    } else {
                        // API返回空数据，使用fallback数据
                        Log.d(TAG, "API返回空数据，使用fallback数据");
                        loadTrendingSearchesFallback();
                    }

                    // 更新UI
                    if (trendingSearchAdapter != null) {
                        trendingSearchAdapter.notifyDataSetChanged();
                    }
                });
            }

            @Override
            public void onError(String errorMessage) {
                runOnUiThread(() -> {
                    Log.e(TAG, "热门搜索API获取失败: " + errorMessage);

                    // API失败时，使用fallback数据
                    Log.d(TAG, "API失败，使用fallback数据");
                    loadTrendingSearchesFallback();

                    // 更新UI
                    if (trendingSearchAdapter != null) {
                        trendingSearchAdapter.notifyDataSetChanged();
                    }

                    // 在debug模式下显示错误信息
                    if (EnvironmentConfigUtils.isDebugMode()) {
                        Toast.makeText(SearchActivity.this, "热门搜索加载失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }

    /**
     * 加载热门搜索fallback数据
     */
    private void loadTrendingSearchesFallback() {
        trendingSearches.clear();
        trendingSearches.add(new SearchResultModel(1, "Eternal Love", "138.7k", R.drawable.movie_poster));
        trendingSearches.add(new SearchResultModel(2, "Love in the fast lane", "108.7k", R.drawable.movie_poster));
        trendingSearches.add(new SearchResultModel(3, "A zee5 original", "98.7k", R.drawable.movie_poster));
        trendingSearches.add(new SearchResultModel(4, "A zee5 original", "98.7k", R.drawable.movie_poster));

        Log.d(TAG, "已加载热门搜索fallback数据: " + trendingSearches.size() + " 条");
    }
}
