package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import com.android.video.cache.ImprovedDataCacheManager;
import com.android.video.manager.FragmentCacheManager;
import com.google.gson.reflect.TypeToken;
import java.util.Arrays;
import java.util.List;
import java.util.ArrayList;

/**
 * 缓存数据一致性检查工具
 * 用于检测和修复缓存数据不一致问题
 * <AUTHOR> Team
 */
public class CacheConsistencyChecker {
    
    private static final String TAG = "CacheConsistencyChecker";
    
    /**
     * 检查结果类
     */
    public static class ConsistencyCheckResult {
        public boolean isConsistent;
        public String message;
        public List<String> issues;
        public List<String> fixedIssues;
        public int totalChecks;

        public ConsistencyCheckResult() {
            this.issues = new ArrayList<>();
            this.fixedIssues = new ArrayList<>();
            this.totalChecks = 0;
        }
        
        @Override
        public String toString() {
            return "ConsistencyCheckResult{" +
                    "isConsistent=" + isConsistent +
                    ", totalChecks=" + totalChecks +
                    ", issues=" + issues.size() +
                    ", fixedIssues=" + fixedIssues.size() +
                    ", message='" + message + '\'' +
                    '}';
        }
    }
    
    /**
     * 检查首页缓存数据一致性
     */
    public static ConsistencyCheckResult checkHomeCacheConsistency(Context context) {
        Log.d(TAG, "开始检查首页缓存数据一致性");
        
        ConsistencyCheckResult result = new ConsistencyCheckResult();
        
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);
            FragmentCacheManager fragmentManager = FragmentCacheManager.getInstance();
            
            String fragmentKey = "HomeFragment";
            
            // 检查轮播图数据
            checkCacheEntry(cacheManager, "home_carousel_data", "轮播图", result);
            
            // 检查分类标签数据
            checkCacheEntry(cacheManager, "home_category_tags", "分类标签", result);
            
            // 检查视频网格数据
            checkCacheEntry(cacheManager, "home_video_grid", "视频网格", result);
            
            // 检查继续观看数据
            checkCacheEntry(cacheManager, "home_continue_watching", "继续观看", result);
            
            // 检查Fragment状态一致性
            FragmentCacheManager.FragmentCacheState state = fragmentManager.getFragmentState(fragmentKey);
            if (state.isDataCached && !hasValidHomeCache(cacheManager)) {
                result.issues.add("Fragment状态显示已缓存，但实际缓存数据无效");
                // 修复：重置Fragment状态
                fragmentManager.resetFragmentState(fragmentKey);
                result.fixedIssues.add("重置了不一致的Fragment状态");
            }
            
            result.isConsistent = result.issues.isEmpty();
            result.message = result.isConsistent ? "首页缓存数据一致性检查通过" : 
                           "发现 " + result.issues.size() + " 个一致性问题，已修复 " + result.fixedIssues.size() + " 个";
            
        } catch (Exception e) {
            Log.e(TAG, "检查首页缓存一致性时发生异常", e);
            result.isConsistent = false;
            result.message = "检查过程中发生异常: " + e.getMessage();
        }
        
        Log.d(TAG, "首页缓存一致性检查完成: " + result.toString());
        return result;
    }
    
    /**
     * 检查MyList缓存数据一致性
     */
    public static ConsistencyCheckResult checkMyListCacheConsistency(Context context) {
        Log.d(TAG, "开始检查MyList缓存数据一致性");
        
        ConsistencyCheckResult result = new ConsistencyCheckResult();
        
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);
            FragmentCacheManager fragmentManager = FragmentCacheManager.getInstance();
            
            String[] tabs = {"following", "history", "interest"};
            
            for (String tab : tabs) {
                String cacheKey = "mylist_" + tab + "_data";
                String fragmentKey = "MyListFragment_" + tab;
                
                // 检查缓存条目
                checkCacheEntry(cacheManager, cacheKey, "MyList " + tab, result);
                
                // 检查Fragment状态一致性
                FragmentCacheManager.FragmentCacheState state = fragmentManager.getFragmentState(fragmentKey);
                boolean hasCacheData = cacheManager.isCacheValid(cacheKey);
                
                if (state.isDataCached && !hasCacheData) {
                    result.issues.add("MyList " + tab + " Fragment状态显示已缓存，但实际缓存数据无效");
                    // 修复：重置Fragment状态
                    fragmentManager.resetFragmentState(fragmentKey);
                    result.fixedIssues.add("重置了 MyList " + tab + " 不一致的Fragment状态");
                }
            }
            
            result.isConsistent = result.issues.isEmpty();
            result.message = result.isConsistent ? "MyList缓存数据一致性检查通过" : 
                           "发现 " + result.issues.size() + " 个一致性问题，已修复 " + result.fixedIssues.size() + " 个";
            
        } catch (Exception e) {
            Log.e(TAG, "检查MyList缓存一致性时发生异常", e);
            result.isConsistent = false;
            result.message = "检查过程中发生异常: " + e.getMessage();
        }
        
        Log.d(TAG, "MyList缓存一致性检查完成: " + result.toString());
        return result;
    }
    
    /**
     * 检查单个缓存条目
     */
    private static void checkCacheEntry(ImprovedDataCacheManager cacheManager, String cacheKey,
                                       String description, ConsistencyCheckResult result) {
        result.totalChecks++;
        try {
            if (!cacheManager.isCacheValid(cacheKey)) {
                // 缓存无效，清除可能损坏的数据
                cacheManager.clearCachedData(cacheKey);
                result.issues.add(description + " 缓存数据无效");
                result.fixedIssues.add("清除了 " + description + " 的无效缓存");
            } else {
                Log.d(TAG, description + " 缓存数据有效");
            }
        } catch (Exception e) {
            Log.e(TAG, "检查 " + description + " 缓存时发生异常", e);
            result.issues.add(description + " 缓存检查异常: " + e.getMessage());
            // 尝试清除可能损坏的缓存
            try {
                cacheManager.clearCachedData(cacheKey);
                result.fixedIssues.add("清除了 " + description + " 的异常缓存");
            } catch (Exception clearException) {
                Log.e(TAG, "清除 " + description + " 缓存时也发生异常", clearException);
            }
        }
    }
    
    /**
     * 检查首页是否有有效缓存
     */
    private static boolean hasValidHomeCache(ImprovedDataCacheManager cacheManager) {
        return cacheManager.isCacheValid("home_carousel_data") ||
               cacheManager.isCacheValid("home_category_tags") ||
               cacheManager.isCacheValid("home_video_grid") ||
               cacheManager.isCacheValid("home_continue_watching");
    }
    
    /**
     * 运行完整的缓存一致性检查
     */
    public static void runFullConsistencyCheck(Context context) {
        Log.i(TAG, "=== 开始完整缓存一致性检查 ===");
        
        // 检查首页缓存一致性
        ConsistencyCheckResult homeResult = checkHomeCacheConsistency(context);
        Log.i(TAG, "首页缓存一致性检查结果: " + homeResult.toString());
        
        // 检查MyList缓存一致性
        ConsistencyCheckResult myListResult = checkMyListCacheConsistency(context);
        Log.i(TAG, "MyList缓存一致性检查结果: " + myListResult.toString());
        
        // 总结
        boolean overallConsistent = homeResult.isConsistent && myListResult.isConsistent;
        int totalIssues = homeResult.issues.size() + myListResult.issues.size();
        int totalFixed = homeResult.fixedIssues.size() + myListResult.fixedIssues.size();
        
        Log.i(TAG, "=== 缓存一致性检查总结 ===");
        Log.i(TAG, "整体一致性: " + (overallConsistent ? "通过" : "存在问题"));
        Log.i(TAG, "发现问题: " + totalIssues + " 个");
        Log.i(TAG, "修复问题: " + totalFixed + " 个");
        Log.i(TAG, "=== 缓存一致性检查结束 ===");
    }

    /**
     * 检查Featured推荐位缓存一致性
     */
    public static ConsistencyCheckResult checkFeaturedCacheConsistency(Context context) {
        Log.d(TAG, "开始检查Featured推荐位缓存一致性");

        ConsistencyCheckResult result = new ConsistencyCheckResult();

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);

            // 检查原始Featured响应
            checkCacheEntry(cacheManager, "home_featured_raw_response", "Featured原始响应", result);

            // 检查各推荐位细分缓存
            checkCacheEntry(cacheManager, "home_featured_categories_films", "Categories推荐位", result);
            checkCacheEntry(cacheManager, "home_featured_coming_soon_films", "Coming Soon推荐位", result);
            checkCacheEntry(cacheManager, "home_featured_popular_series_films", "Popular Series推荐位", result);

            result.isConsistent = result.issues.isEmpty();
            result.message = result.isConsistent ? "Featured推荐位缓存一致性检查通过" :
                           "发现 " + result.issues.size() + " 个一致性问题，已修复 " + result.fixedIssues.size() + " 个";

        } catch (Exception e) {
            Log.e(TAG, "检查Featured推荐位缓存一致性时发生异常", e);
            result.isConsistent = false;
            result.message = "检查过程中发生异常: " + e.getMessage();
        }

        Log.d(TAG, "Featured推荐位缓存一致性检查完成: " + result.toString());
        return result;
    }

    /**
     * 检查MyList分页缓存一致性
     */
    public static ConsistencyCheckResult checkMyListPaginationCache(Context context) {
        Log.d(TAG, "开始检查MyList累积缓存一致性");

        ConsistencyCheckResult result = new ConsistencyCheckResult();

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);

            // 检查各标签页的累积数据缓存
            String[] tabs = {"following", "history", "interest"};
            for (String tab : tabs) {
                String cacheKey = String.format("mylist_%s_accumulated_data", tab);
                checkCacheEntry(cacheManager, cacheKey, tab + "累积数据", result);
            }

            result.isConsistent = result.issues.isEmpty();
            result.message = result.isConsistent ? "MyList累积缓存一致性检查通过" :
                           "发现 " + result.issues.size() + " 个一致性问题，已修复 " + result.fixedIssues.size() + " 个";

        } catch (Exception e) {
            Log.e(TAG, "检查MyList累积缓存一致性时发生异常", e);
            result.isConsistent = false;
            result.message = "检查过程中发生异常: " + e.getMessage();
        }

        Log.d(TAG, "MyList累积缓存一致性检查完成: " + result.toString());
        return result;
    }

    /**
     * 强制清理所有可能不一致的缓存
     */
    public static void forceCleanInconsistentCache(Context context) {
        Log.i(TAG, "开始强制清理不一致的缓存");

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(context);
            FragmentCacheManager fragmentManager = FragmentCacheManager.getInstance();

            // 清理所有首页相关缓存（使用命名空间清理）
            cacheManager.clearCacheNamespace("home");

            // 清理所有MyList累积缓存
            String[] tabs = {"following", "history", "interest"};
            for (String tab : tabs) {
                String cacheKey = String.format("mylist_%s_accumulated_data", tab);
                cacheManager.clearCachedData(cacheKey);
            }

            // 重置所有Fragment状态
            fragmentManager.clearAllStates();

            Log.i(TAG, "强制清理所有不一致的缓存完成");

        } catch (Exception e) {
            Log.e(TAG, "强制清理缓存时发生异常", e);
        }
    }
}
