package com.android.video.model.response;

/**
 * 账单订单项数据模型
 */
public class BillOrderItem {
    private String orderId;
    private String orderNo;
    private String userId;
    private String userName;
    private String phone;
    private int orderType;
    private String bizId;
    private String bizName;
    private String language;
    private String unlockChapter;
    private String cover;
    private int spendPoints;
    private double price;
    private double payAmount;
    private String timeOfPayment;
    private int payStatus;
    private String payType;
    private int isDelete;
    private String createTime;
    private String updateTime;
    private int points;
    private int giftPoints;
    private String payReq;
    private String payRsp;
    private String payCallbackJson;
    private String payCallbackTime;
    private String payId;
    private FilmOrderVO filmOrderVO;

    public BillOrderItem() {
    }

    // Getters and Setters
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getOrderType() {
        return orderType;
    }

    public void setOrderType(int orderType) {
        this.orderType = orderType;
    }

    public String getBizId() {
        return bizId;
    }

    public void setBizId(String bizId) {
        this.bizId = bizId;
    }

    public String getBizName() {
        return bizName;
    }

    public void setBizName(String bizName) {
        this.bizName = bizName;
    }

    public String getLanguage() {
        return language;
    }

    public void setLanguage(String language) {
        this.language = language;
    }

    public String getUnlockChapter() {
        return unlockChapter;
    }

    public void setUnlockChapter(String unlockChapter) {
        this.unlockChapter = unlockChapter;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getSpendPoints() {
        return spendPoints;
    }

    public void setSpendPoints(int spendPoints) {
        this.spendPoints = spendPoints;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public double getPayAmount() {
        return payAmount;
    }

    public void setPayAmount(double payAmount) {
        this.payAmount = payAmount;
    }

    public String getTimeOfPayment() {
        return timeOfPayment;
    }

    public void setTimeOfPayment(String timeOfPayment) {
        this.timeOfPayment = timeOfPayment;
    }

    public int getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(int payStatus) {
        this.payStatus = payStatus;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public int getGiftPoints() {
        return giftPoints;
    }

    public void setGiftPoints(int giftPoints) {
        this.giftPoints = giftPoints;
    }

    public String getPayReq() {
        return payReq;
    }

    public void setPayReq(String payReq) {
        this.payReq = payReq;
    }

    public String getPayRsp() {
        return payRsp;
    }

    public void setPayRsp(String payRsp) {
        this.payRsp = payRsp;
    }

    public String getPayCallbackJson() {
        return payCallbackJson;
    }

    public void setPayCallbackJson(String payCallbackJson) {
        this.payCallbackJson = payCallbackJson;
    }

    public String getPayCallbackTime() {
        return payCallbackTime;
    }

    public void setPayCallbackTime(String payCallbackTime) {
        this.payCallbackTime = payCallbackTime;
    }

    public String getPayId() {
        return payId;
    }

    public void setPayId(String payId) {
        this.payId = payId;
    }

    public FilmOrderVO getFilmOrderVO() {
        return filmOrderVO;
    }

    public void setFilmOrderVO(FilmOrderVO filmOrderVO) {
        this.filmOrderVO = filmOrderVO;
    }
}
