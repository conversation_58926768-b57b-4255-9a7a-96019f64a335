<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 主背景 -->
    <item>
        <shape android:shape="rectangle">
            <!-- 半透明黑色背景 -->
            <solid android:color="#5C000000" />
            <!-- 圆角 -->
            <corners android:radius="4dp" />
        </shape>
    </item>
    
    <!-- 内阴影效果 -->
    <item
        android:left="0.5dp"
        android:top="0.5dp"
        android:right="0.5dp"
        android:bottom="0.5dp">
        <shape android:shape="rectangle">
            <!-- 透明背景 -->
            <solid android:color="#00000000" />
            <!-- 内阴影边框 -->
            <stroke
                android:width="0.5dp"
                android:color="#80FFFFFF" />
            <!-- 圆角 -->
            <corners android:radius="3.5dp" />
        </shape>
    </item>
    
</layer-list>
