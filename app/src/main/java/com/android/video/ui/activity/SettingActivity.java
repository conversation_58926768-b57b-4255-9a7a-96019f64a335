package com.android.video.ui.activity;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;

import com.android.video.R;
import com.android.video.ui.activity.InformationActivity;
import com.android.video.ui.activity.LoginActivity;
import com.android.video.ui.activity.LogOffActivity;
import com.android.video.ui.activity.BaseFullScreenActivity;
import com.android.video.ui.dialog.UpdateDialog;
import com.android.video.utils.CacheManager;
import com.android.video.cache.SmartCacheManager;
import com.android.video.utils.UserSessionUtils;
import com.android.video.utils.LocalizationUtils;
import com.android.video.utils.GlobalTextUpdater;
import com.android.video.base.BaseMultiLanguageActivity;

public class SettingActivity extends BaseMultiLanguageActivity {

    private ImageView ivBack;
    private RelativeLayout itemTermsOfService;
    private RelativeLayout itemPrivacyPolicy;
    private RelativeLayout itemClearCache;
    private RelativeLayout itemAboutUs;
    private RelativeLayout itemLanguage;
    private RelativeLayout itemFeedback;
    private RelativeLayout itemVersion;
    private TextView btnLogOut;
    private TextView btnDeleteAccount;
    private TextView tvCacheSize;
    private TextView tvCurrentLanguage;
    private TextView tvVersion;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_setting);

        initViews();
        setupClickListeners();
        loadData();
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        itemTermsOfService = findViewById(R.id.item_terms_of_service);
        itemPrivacyPolicy = findViewById(R.id.item_privacy_policy);
        itemClearCache = findViewById(R.id.item_clear_cache);
        itemAboutUs = findViewById(R.id.item_about_us);
        itemLanguage = findViewById(R.id.item_language);
        itemFeedback = findViewById(R.id.item_feedback);
        itemVersion = findViewById(R.id.item_version);
        btnLogOut = findViewById(R.id.btn_log_out);
        btnDeleteAccount = findViewById(R.id.btn_delete_account);
        tvCacheSize = findViewById(R.id.tv_cache_size);
        tvCurrentLanguage = findViewById(R.id.tv_current_language);
        tvVersion = findViewById(R.id.tv_version);
    }

    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // Terms of Service
        itemTermsOfService.setOnClickListener(v -> {
            Toast.makeText(this, "Terms of Service", Toast.LENGTH_SHORT).show();
        });

        // Privacy Policy
        itemPrivacyPolicy.setOnClickListener(v -> {
            Toast.makeText(this, "Privacy Policy", Toast.LENGTH_SHORT).show();
        });

        // Clear Cache
        itemClearCache.setOnClickListener(v -> {
            clearCache();
        });

        // About Us
        itemAboutUs.setOnClickListener(v -> {
            Intent intent = InformationActivity.createAboutUsIntent(this);
            startActivity(intent);
        });

        // Language
        itemLanguage.setOnClickListener(v -> {
            showLanguageSelectionDialog();
        });

        // Feedback
        itemFeedback.setOnClickListener(v -> {
            Intent intent = new Intent(this, FeedbackActivity.class);
            startActivity(intent);
        });

        // Version - 显示更新弹窗
        itemVersion.setOnClickListener(v -> {
            showUpdateDialog();
        });

        // Log Out
        btnLogOut.setOnClickListener(v -> {
            handleLogOut();
        });

        // Delete Account
        btnDeleteAccount.setOnClickListener(v -> {
            handleDeleteAccount();
        });
    }

    private void loadData() {
        // 设置版本号
        tvVersion.setText("V0.0.1");

        // 计算并显示缓存大小
        calculateCacheSize();

        // 更新当前语言显示
        updateLanguageDisplay();
    }

    private void calculateCacheSize() {
        // Show calculating status
        tvCacheSize.setText("Calculating...");

        // Use enhanced cache size calculation
        new Thread(() -> {
            try {
                SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(this);
                SmartCacheManager.CacheSpaceInfo spaceInfo = smartCacheManager.getCacheSpaceInfo();

                // Format the display text with usage information
                String displayText = String.format("%s / %s (%.1f%%)",
                    spaceInfo.getCurrentSizeFormatted(),
                    spaceInfo.getLimitSizeFormatted(),
                    spaceInfo.getUsagePercentage());

                // Update UI on main thread
                new Handler(Looper.getMainLooper()).post(() -> {
                    tvCacheSize.setText(displayText);

                    // Show warning if near limit
                    if (spaceInfo.isNearLimit()) {
                        Toast.makeText(SettingActivity.this,
                            "Cache usage is high. Consider clearing cache.",
                            Toast.LENGTH_LONG).show();
                    }
                });

            } catch (Exception e) {
                // Fallback to original method
                CacheManager.calculateCacheSize(SettingActivity.this, new CacheManager.CacheSizeCallback() {
                    @Override
                    public void onCacheSizeCalculated(String formattedSize, long sizeInBytes) {
                        new Handler(Looper.getMainLooper()).post(() -> {
                            tvCacheSize.setText(formattedSize);
                        });
                    }

                    @Override
                    public void onError(String error) {
                        new Handler(Looper.getMainLooper()).post(() -> {
                            tvCacheSize.setText("Calculation failed");
                            Toast.makeText(SettingActivity.this, error, Toast.LENGTH_SHORT).show();
                        });
                    }
                });
            }
        }).start();
    }

    private void clearCache() {
        // Get current cache info for the dialog
        SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(this);
        SmartCacheManager.CacheSpaceInfo spaceInfo = smartCacheManager.getCacheSpaceInfo();

        String message = String.format(
            "Are you sure you want to clear all cache data?\n\n" +
            "Current cache size: %s\n" +
            "Cache entries: %d\n" +
            "Usage: %.1f%% of limit\n\n" +
            "This will delete temporary files and cached data, but will not affect your personal settings.",
            spaceInfo.getCurrentSizeFormatted(),
            spaceInfo.getEntryCount(),
            spaceInfo.getUsagePercentage()
        );

        // Show enhanced confirmation dialog
        new AlertDialog.Builder(this)
            .setTitle("Clear Cache")
            .setMessage(message)
            .setPositiveButton("Clear All", (dialog, which) -> {
                performClearCache();
            })
            .setNeutralButton("Smart Clean", (dialog, which) -> {
                performSmartCleanup();
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void performClearCache() {
        // Show clearing status
        tvCacheSize.setText("Clearing...");

        // Use CacheManager to clear cache asynchronously
        CacheManager.clearCache(this, false, new CacheManager.ClearCacheCallback() {
            @Override
            public void onCacheCleared(boolean success, String message, String newCacheSize) {
                // Update UI on main thread
                new Handler(Looper.getMainLooper()).post(() -> {
                    if (success) {
                        tvCacheSize.setText(newCacheSize);
                        Toast.makeText(SettingActivity.this, "Cache cleared successfully", Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(SettingActivity.this, "Cache clearing partially failed: " + message, Toast.LENGTH_LONG).show();
                        // Recalculate cache size
                        calculateCacheSize();
                    }
                });
            }

            @Override
            public void onError(String error) {
                // Show error on main thread
                new Handler(Looper.getMainLooper()).post(() -> {
                    Toast.makeText(SettingActivity.this, error, Toast.LENGTH_LONG).show();
                    // Recalculate cache size
                    calculateCacheSize();
                });
            }
        });
    }

    /**
     * 执行智能清理（只清理过期和超限的缓存）
     */
    private void performSmartCleanup() {
        // Show cleaning status
        tvCacheSize.setText("Smart cleaning...");

        new Thread(() -> {
            try {
                SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(this);

                // Get cache info before cleanup
                SmartCacheManager.CacheSpaceInfo beforeInfo = smartCacheManager.getCacheSpaceInfo();

                // Perform smart cleanup
                smartCacheManager.performManualCleanup();

                // Get cache info after cleanup
                SmartCacheManager.CacheSpaceInfo afterInfo = smartCacheManager.getCacheSpaceInfo();

                // Calculate cleaned size
                long cleanedSize = beforeInfo.getCurrentSize() - afterInfo.getCurrentSize();

                // Update UI on main thread
                new Handler(Looper.getMainLooper()).post(() -> {
                    String displayText = String.format("%s / %s (%.1f%%)",
                        afterInfo.getCurrentSizeFormatted(),
                        afterInfo.getLimitSizeFormatted(),
                        afterInfo.getUsagePercentage());

                    tvCacheSize.setText(displayText);

                    String message = String.format("Smart cleanup completed!\n\nCleaned: %s\nRemaining: %s",
                        formatFileSize(cleanedSize), afterInfo.getCurrentSizeFormatted());

                    Toast.makeText(SettingActivity.this, message, Toast.LENGTH_LONG).show();
                });

            } catch (Exception e) {
                new Handler(Looper.getMainLooper()).post(() -> {
                    Toast.makeText(SettingActivity.this,
                        "Smart cleanup failed: " + e.getMessage(),
                        Toast.LENGTH_LONG).show();
                    calculateCacheSize(); // Recalculate
                });
            }
        }).start();
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size <= 0) return "0.0B";

        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f%s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }

    private void handleLogOut() {
        // 执行登出操作
        UserSessionUtils.performLogout(this);

        // 清除TokenManager中的token缓存（重要：确保完全清除token）
        com.android.video.manager.TokenManager tokenManager =
            com.android.video.manager.TokenManager.getInstance(this);
        tokenManager.clearToken();

        android.util.Log.d("SettingActivity", "用户退出登录，已清除所有token缓存");

        // 跳转到登录页面
        Intent intent = new Intent(this, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        startActivity(intent);

        Toast.makeText(this, "Logged out successfully", Toast.LENGTH_SHORT).show();
        finish();
    }

    private void handleDeleteAccount() {
        // 跳转到删除账户确认页面
        Intent intent = new Intent(this, LogOffActivity.class);
        startActivity(intent);
    }



    /**
     * 更新语言显示
     */
    private void updateLanguageDisplay() {
        try {
            int currentLanguageType = LocalizationUtils.getCurrentLanguageType(this);
            String currentLanguage = LocalizationUtils.getLanguageTypeDescription(currentLanguageType);

            if (tvCurrentLanguage != null) {
                tvCurrentLanguage.setText(currentLanguage);
            }

            android.util.Log.d("SettingActivity", "Language display updated: " + currentLanguage);
        } catch (Exception e) {
            android.util.Log.e("SettingActivity", "Error updating language display", e);
            // 回退到原有方法
            String currentLanguage = UserSessionUtils.getLanguageDescription(this);
            if (tvCurrentLanguage != null) {
                tvCurrentLanguage.setText(currentLanguage);
            }
        }
    }

    /**
     * 显示语言选择对话框
     */
    private void showLanguageSelectionDialog() {
        com.android.video.ui.dialog.LanguageSelectionDialog dialog =
            new com.android.video.ui.dialog.LanguageSelectionDialog(this);

        dialog.setOnLanguageSelectedListener(languageType -> {
            changeLanguage(languageType);
        });

        dialog.show();
    }

    /**
     * 切换语言
     */
    private void changeLanguage(int languageType) {
        // 显示加载提示
        Toast.makeText(this, "Changing language...", Toast.LENGTH_SHORT).show();

        // 调用API切换语言
        com.android.video.network.ProfileApiService.getInstance().changeLanguage(
            languageType,
            new com.android.video.network.ProfileApiService.LanguageChangeCallback() {
                @Override
                public void onSuccess() {
                    // 保存到本地
                    UserSessionUtils.saveLanguageType(SettingActivity.this, languageType);

                    // 更新本地化管理器的语言设置
                    if (localizationManager != null) {
                        localizationManager.setLanguage(languageType);
                    }

                    // 更新UI显示
                    updateLanguageDisplay();

                    // 显示成功提示（使用本地化文本）
                    String successMessage = LocalizationUtils.getString(SettingActivity.this, "language_changed_successfully");
                    if (successMessage.equals("language_changed_successfully")) {
                        // 如果没有找到翻译，使用默认文本
                        successMessage = "Language changed successfully";
                    }
                    Toast.makeText(SettingActivity.this, successMessage, Toast.LENGTH_SHORT).show();
                }

                @Override
                public void onError(String errorMessage) {
                    // 显示错误提示
                    Toast.makeText(SettingActivity.this, "Failed to change language: " + errorMessage, Toast.LENGTH_LONG).show();
                }
            }
        );
    }

    @Override
    protected void updateAllTexts() {
        super.updateAllTexts();
        // 更新语言显示
        updateLanguageDisplay();
    }

    /**
     * 显示更新弹窗
     */
    private void showUpdateDialog() {
        // 获取当前版本号
        String currentVersion = getCurrentVersion();

        // 模拟检查更新（在实际应用中，这里应该调用服务器API）
        String latestVersion = "1.2.0"; // 模拟的最新版本号

        // 显示更新弹窗
        UpdateDialog.showUpdateDialog(this, latestVersion, new UpdateDialog.OnUpdateDialogListener() {
            @Override
            public void onUpdateClicked() {
                // 用户点击了更新按钮
                handleUpdateClick();
            }

            @Override
            public void onCancelClicked() {
                // 用户点击了取消按钮（强制更新模式下不会调用）
                Toast.makeText(SettingActivity.this, "Update cancelled", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDialogDismissed() {
                // 弹窗被关闭
                Toast.makeText(SettingActivity.this, "Update dialog dismissed", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 获取当前应用版本号
     * @return 版本号字符串
     */
    private String getCurrentVersion() {
        try {
            return getPackageManager()
                .getPackageInfo(getPackageName(), 0)
                .versionName;
        } catch (Exception e) {
            e.printStackTrace();
            return "1.0.0";
        }
    }

    /**
     * 处理更新按钮点击事件
     */
    private void handleUpdateClick() {
        // 这里可以实现具体的更新逻辑
        // 例如：跳转到应用商店、下载APK等

        Toast.makeText(this, "Redirecting to update...", Toast.LENGTH_SHORT).show();

        // 示例：跳转到Google Play Store
        try {
            String packageName = getPackageName();
            Intent intent = new Intent(Intent.ACTION_VIEW,
                android.net.Uri.parse("market://details?id=" + packageName));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            startActivity(intent);
        } catch (Exception e) {
            // 如果无法打开应用商店，可以打开网页版或显示其他提示
            Toast.makeText(this, "Unable to open app store", Toast.LENGTH_SHORT).show();
            e.printStackTrace();
        }
    }
}
