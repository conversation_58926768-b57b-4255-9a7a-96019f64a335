package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 瀑布流推荐短剧信息模型
 * <p>
 * 用于映射瀑布流推荐API返回的短剧信息数据结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmTitle: 短剧标题</li>
 *   <li>categoryId: 分类ID（多个用逗号分隔，ALL表示所有）</li>
 *   <li>categoryName: 分类名称（多个用逗号分隔）</li>
 *   <li>cover: 短剧封面图片URL</li>
 *   <li>languageType: 支持语言（1=英语, 2=俄语）</li>
 *   <li>filmId: 短剧ID</li>
 *   <li>filmLanguageInfoId: 短剧语言信息ID</li>
 *   <li>searchNum: 搜索数量</li>
 *   <li>details: 短剧简介</li>
 *   <li>isLove: 加入喜欢（0=取消, 1=加入）</li>
 *   <li>playNum: 播放数</li>
 *   <li>totalChaptersNum: 总章节数</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallFilmInfoModel {
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 分类ID
     * 多个用逗号分隔，ALL表示所有
     */
    @SerializedName("categoryId")
    private String categoryId;
    
    /**
     * 分类名称
     * 多个用逗号分隔
     */
    @SerializedName("categoryName")
    private String categoryName;
    
    /**
     * 短剧封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 支持语言
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private int languageType;
    
    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 搜索数量
     */
    @SerializedName("searchNum")
    private Integer searchNum;
    
    /**
     * 短剧简介
     */
    @SerializedName("details")
    private String details;
    
    /**
     * 加入喜欢
     * 0=取消, 1=加入
     */
    @SerializedName("isLove")
    private Integer isLove;
    
    /**
     * 播放数
     */
    @SerializedName("playNum")
    private int playNum;
    
    /**
     * 总章节数
     */
    @SerializedName("totalChaptersNum")
    private Integer totalChaptersNum;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallFilmInfoModel() {
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Integer getIsLove() {
        return isLove;
    }

    public void setIsLove(Integer isLove) {
        this.isLove = isLove;
    }

    public int getPlayNum() {
        return playNum;
    }

    public void setPlayNum(int playNum) {
        this.playNum = playNum;
    }

    public Integer getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(Integer totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否为英语内容
     * @return 如果是英语内容则返回true
     */
    public boolean isEnglish() {
        return languageType == 1;
    }

    /**
     * 检查是否为俄语内容
     * @return 如果是俄语内容则返回true
     */
    public boolean isRussian() {
        return languageType == 2;
    }

    /**
     * 检查是否已加入喜欢
     * @return 如果已加入喜欢则返回true
     */
    public boolean isLoved() {
        return isLove != null && isLove == 1;
    }

    /**
     * 获取总章节数
     * @return 总章节数，如果为null则返回1
     */
    public int getTotalChapters() {
        return totalChaptersNum != null ? totalChaptersNum : 1;
    }

    /**
     * 获取语言类型显示名称
     * @return 语言类型显示名称
     */
    public String getLanguageDisplayName() {
        switch (languageType) {
            case 1:
                return "英语";
            case 2:
                return "俄语";
            default:
                return "未知";
        }
    }

    @Override
    public String toString() {
        return "WaterfallFilmInfoModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", searchNum=" + searchNum +
                ", details='" + details + '\'' +
                ", isLove=" + isLove +
                ", playNum=" + playNum +
                ", totalChaptersNum=" + totalChaptersNum +
                '}';
    }
}
