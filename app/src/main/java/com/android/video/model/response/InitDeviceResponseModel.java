package com.android.video.model.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 初始化设备信息响应模型
 * <p>
 * 用于APP启动时调用初始化接口的响应数据模型。
 * </p>
 * 
 * <AUTHOR> Team
 */
public class InitDeviceResponseModel {

    /**
     * 用户ID（初始化时可能为null）
     */
    @SerializedName("customerId")
    @Expose
    private String customerId;

    /**
     * 用户昵称（初始化时可能为null）
     */
    @SerializedName("customerName")
    @Expose
    private String customerName;

    /**
     * 用户token
     */
    @SerializedName("userToken")
    @Expose
    private String userToken;

    /**
     * 登录类型（初始化时可能为null）
     */
    @SerializedName("loginType")
    @Expose
    private Integer loginType;

    /**
     * 设备唯一标识编码
     */
    @SerializedName("deviceId")
    @Expose
    private String deviceId;

    /**
     * 默认构造函数
     */
    public InitDeviceResponseModel() {
    }

    // ========== Getter和Setter方法 ==========

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public Integer getLoginType() {
        return loginType;
    }

    public void setLoginType(Integer loginType) {
        this.loginType = loginType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    // ========== 便利方法 ==========

    /**
     * 检查用户是否已登录
     * @return 是否已登录
     */
    public boolean isUserLoggedIn() {
        return customerId != null && !customerId.trim().isEmpty() &&
               loginType != null;
    }

    /**
     * 检查token是否有效
     * @return 是否有效
     */
    public boolean isTokenValid() {
        return userToken != null && !userToken.trim().isEmpty();
    }

    /**
     * 获取用户显示名称
     * @return 显示名称
     */
    public String getDisplayName() {
        if (customerName != null && !customerName.trim().isEmpty()) {
            return customerName;
        }
        if (customerId != null && !customerId.trim().isEmpty()) {
            return "User " + customerId;
        }
        return "Guest";
    }

    /**
     * 获取登录类型描述
     * @return 登录类型描述
     */
    public String getLoginTypeDescription() {
        if (loginType == null) {
            return "Not Logged In";
        }
        switch (loginType) {
            case 1:
                return "Phone Login";
            case 2:
                return "VKontakte Login";
            case 3:
                return "TikTok Login";
            default:
                return "Unknown Login Type";
        }
    }

    @Override
    public String toString() {
        return "InitDeviceResponseModel{" +
                "customerId='" + customerId + '\'' +
                ", customerName='" + customerName + '\'' +
                ", userToken='" + (userToken != null ? "***" : "null") + '\'' +
                ", loginType=" + loginType + " (" + getLoginTypeDescription() + ")" +
                ", deviceId='" + deviceId + '\'' +
                '}';
    }

    /**
     * 2025-08-13 12:59:24.675 29826-32020 AuthApiUtils            com.android.video                    D  initDevice response body: {"code":"200","message":"success","data":{"customerId":null,"customerName":null,"userToken":"91eb06cfeb164b35a1fb04c3b91aacda","loginType":null,"deviceId":"e9edfedeeb34064c0065b7c8141956d7"}}
     * @param obj
     * @return
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        InitDeviceResponseModel that = (InitDeviceResponseModel) obj;
        
        if (customerId != null ? !customerId.equals(that.customerId) : that.customerId != null) return false;
        if (customerName != null ? !customerName.equals(that.customerName) : that.customerName != null) return false;
        if (userToken != null ? !userToken.equals(that.userToken) : that.userToken != null) return false;
        if (loginType != null ? !loginType.equals(that.loginType) : that.loginType != null) return false;
        return deviceId != null ? deviceId.equals(that.deviceId) : that.deviceId == null;
    }

    @Override
    public int hashCode() {
        int result = customerId != null ? customerId.hashCode() : 0;
        result = 31 * result + (customerName != null ? customerName.hashCode() : 0);
        result = 31 * result + (userToken != null ? userToken.hashCode() : 0);
        result = 31 * result + (loginType != null ? loginType.hashCode() : 0);
        result = 31 * result + (deviceId != null ? deviceId.hashCode() : 0);
        return result;
    }
}
