package com.android.video.model.response;

import java.io.Serializable;

/**
 * 积分解锁章节响应数据模型
 * <p>
 * 用于封装服务器对积分解锁章节请求的响应数据。
 * 包含响应码、响应消息以及响应数据等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class UnlockChapterResponseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     * <p>
     * 服务器返回的状态码，用于标识请求处理结果。
     * 通常"200"表示成功，其他值表示不同类型的错误。
     * </p>
     */
    private String code;

    /**
     * 响应消息
     * <p>
     * 服务器返回的描述性消息，用于说明请求处理结果。
     * 成功时通常为"success"，失败时包含具体的错误描述。
     * </p>
     */
    private String message;

    /**
     * 响应数据
     * <p>
     * 服务器返回的具体数据内容。
     * 对于解锁章节接口，成功时通常返回"Unlock success."等确认信息。
     * </p>
     */
    private String data;

    /**
     * 默认构造函数
     */
    public UnlockChapterResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 响应数据
     */
    public UnlockChapterResponseModel(String code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    // ========== 业务方法 ==========

    /**
     * 检查响应是否表示成功
     *
     * @return 如果响应表示成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 检查是否为积分不足错误
     * <p>
     * 根据响应码或消息判断是否为积分不足的错误。
     * </p>
     *
     * @return 如果是积分不足错误返回true，否则返回false
     */
    public boolean isInsufficientPoints() {
        // 可以根据实际的错误码来判断
        return "400".equals(code) && message != null && 
               (message.contains("积分不足") || message.contains("insufficient points"));
    }

    /**
     * 检查是否为章节已解锁错误
     * <p>
     * 根据响应码或消息判断章节是否已经解锁。
     * </p>
     *
     * @return 如果章节已解锁返回true，否则返回false
     */
    public boolean isAlreadyUnlocked() {
        return "400".equals(code) && message != null && 
               (message.contains("已解锁") || message.contains("already unlocked"));
    }

    /**
     * 检查是否为章节不存在错误
     * <p>
     * 根据响应码判断章节是否不存在。
     * </p>
     *
     * @return 如果章节不存在返回true，否则返回false
     */
    public boolean isChapterNotFound() {
        return "404".equals(code);
    }

    /**
     * 获取错误信息
     * <p>
     * 当响应不成功时，返回错误信息用于显示给用户。
     * 优先返回message字段，如果为空则返回默认错误信息。
     * </p>
     *
     * @return 错误信息字符串
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }
        
        // 根据错误码返回默认错误信息
        switch (code) {
            case "400":
                return "请求参数错误或积分不足";
            case "401":
                return "用户未登录，请先登录";
            case "403":
                return "没有权限执行此操作";
            case "404":
                return "章节不存在";
            case "500":
                return "服务器内部错误";
            default:
                return "解锁章节失败，请稍后重试";
        }
    }

    /**
     * 获取成功信息
     * <p>
     * 当响应成功时，返回成功信息用于显示给用户。
     * 优先返回data字段，如果为空则返回message字段。
     * </p>
     *
     * @return 成功信息字符串
     */
    public String getSuccessMessage() {
        if (!isSuccess()) {
            return null;
        }
        
        if (data != null && !data.trim().isEmpty()) {
            return data;
        }
        
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }
        
        return "章节解锁成功";
    }

    /**
     * 获取用户友好的提示信息
     * <p>
     * 根据响应结果返回适合显示给用户的提示信息。
     * </p>
     *
     * @return 用户友好的提示信息
     */
    public String getUserFriendlyMessage() {
        if (isSuccess()) {
            return getSuccessMessage();
        } else if (isInsufficientPoints()) {
            return "积分不足，无法解锁此章节";
        } else if (isAlreadyUnlocked()) {
            return "此章节已经解锁";
        } else if (isChapterNotFound()) {
            return "章节不存在或已下架";
        } else {
            return getErrorMessage();
        }
    }

    // ========== 工具方法 ==========

    /**
     * 转换为字符串表示
     *
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "UnlockChapterResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                '}';
    }

    /**
     * 比较两个对象是否相等
     *
     * @param obj 要比较的对象
     * @return 如果相等返回true，否则返回false
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        UnlockChapterResponseModel that = (UnlockChapterResponseModel) obj;

        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return data != null ? data.equals(that.data) : that.data == null;
    }

    /**
     * 获取对象的哈希码
     *
     * @return 哈希码值
     */
    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (data != null ? data.hashCode() : 0);
        return result;
    }

    /**
     * 创建成功响应的静态方法
     *
     * @param data 成功数据
     * @return 成功响应模型
     */
    public static UnlockChapterResponseModel success(String data) {
        return new UnlockChapterResponseModel("200", "success", data);
    }

    /**
     * 创建错误响应的静态方法
     *
     * @param code 错误码
     * @param message 错误消息
     * @return 错误响应模型
     */
    public static UnlockChapterResponseModel error(String code, String message) {
        return new UnlockChapterResponseModel(code, message, null);
    }
}
