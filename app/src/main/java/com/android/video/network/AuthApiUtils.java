package com.android.video.network;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.AuthApiConstantsUtils;
import com.android.video.model.request.InitDeviceRequestModel;
import com.android.video.model.request.LoginRequestModel;
import com.android.video.model.request.SendSmsRequestModel;
import com.android.video.model.request.FacebookLoginRequestModel;
import com.android.video.model.request.TiktokLoginRequestModel;
import com.android.video.model.response.ApiResponseModel;
import com.android.video.model.response.InitDeviceResponseModel;
import com.android.video.model.response.LoginResponseModel;
import com.android.video.utils.ApiExceptionUtils;
import com.android.video.utils.ApiHeaderUtils;
import com.android.video.utils.DeviceIdUtils;
import com.android.video.utils.UserAuthUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.google.gson.reflect.TypeToken;
import java.util.Map;
import com.android.video.model.UserModel;
import com.google.gson.Gson;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import java.io.IOException;

/**
 * 认证相关API工具类 - 处理登录、验证码等认证接口
 * <AUTHOR> Team
 */
public class AuthApiUtils {

    private static final String TAG = "AuthApiUtils";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static final Gson gson = new Gson();
    private static final Handler mainHandler = new Handler(Looper.getMainLooper());

    /**
     * API回调接口
     */
    public interface ApiCallback<T> {
        void onSuccess(T data);
        void onError(String errorMessage);
    }

    /**
     * 发送手机验证码
     * @param context 上下文
     * @param phoneNumber 手机号
     * @param callback 回调接口
     */
    public static void sendSmsCode(Context context, String phoneNumber, ApiCallback<String> callback) {
        Log.d(TAG, "sendSmsCode called with phoneNumber: " + phoneNumber);

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 参数验证
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            mainHandler.post(() -> callback.onError("Phone number is required"));
            return;
        }

        // 环境判断
        // if (EnvironmentConfigUtils.isDevelopment()) {
        //     // dev环境返回模拟成功
        //     Log.d(TAG, "Dev environment: returning mock success");
        //     mainHandler.post(() -> callback.onSuccess("SMS code sent successfully (mock)"));
        //     return;
        // }

        // 创建请求模型
        SendSmsRequestModel requestModel = new SendSmsRequestModel(phoneNumber);
        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 发送API请求
        String url = AuthApiConstantsUtils.buildApiUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                AuthApiConstantsUtils.API_SEND_SMS_CODE
        );

        String jsonBody = gson.toJson(requestModel);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        Request request = requestBuilder.build();

        Log.d(TAG, "sendSmsCode: requesting URL: " + url);
        Log.d(TAG, "sendSmsCode: Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "sendSmsCode: request body: " + jsonBody);

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "sendSmsCode network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "sendSmsCode response code: " + response.code());
                    Log.d(TAG, "sendSmsCode response headers: " + response.headers().toString());
                    Log.d(TAG, "sendSmsCode response body: " + responseBody);

                    if (!response.isSuccessful()) {
                        ApiExceptionUtils.ApiException apiException = 
                                ApiExceptionUtils.handleHttpException(response.code(), responseBody);
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                        return;
                    }

                    // 解析响应
                    ApiResponseModel<String> apiResponse = gson.fromJson(responseBody,
                            new TypeToken<ApiResponseModel<String>>(){}.getType());
                    if (apiResponse != null && BaseApiConstantsUtils.isSuccessCode(apiResponse.getCode())) {
                        String message = apiResponse.getData();
                        Log.d(TAG, "sendSmsCode success - message: " + message);
                        mainHandler.post(() -> callback.onSuccess(message != null ? message : "SMS code sent successfully"));
                    } else {
                        String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Unknown error";
                        Log.e(TAG, "sendSmsCode business error: " + errorMessage);
                        mainHandler.post(() -> callback.onError(errorMessage));
                    }

                } catch (Exception e) {
                    Log.e(TAG, "sendSmsCode parse error: " + e.getMessage());
                    ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }
        });
    }

    /**
     * 手机号登录
     * @param context 上下文
     * @param phoneNumber 手机号
     * @param verificationCode 验证码
     * @param callback 回调接口
     */
    public static void phoneLogin(Context context, String phoneNumber, String verificationCode, 
                                  ApiCallback<LoginResponseModel> callback) {
        Log.d(TAG, "phoneLogin called with phoneNumber: " + phoneNumber);

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 所有环境都使用后端API进行认证
        Log.d(TAG, "Using backend API for authentication in all environments");

        // 获取设备ID
        String deviceCode = DeviceIdUtils.getDeviceId(context);

        // 创建请求模型（使用新的统一登录接口）
        LoginRequestModel requestModel = new LoginRequestModel(phoneNumber, verificationCode, deviceCode, AuthApiConstantsUtils.OS_TYPE_ANDROID);
        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 发送API请求（使用新的统一登录接口）
        String url = AuthApiConstantsUtils.buildApiUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                AuthApiConstantsUtils.API_LOGIN
        );

        String jsonBody = gson.toJson(requestModel);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
                .url(url)
                .post(body);

        Request request = requestBuilder.build();

        Log.d(TAG, "phoneLogin: requesting URL: " + url);
        Log.d(TAG, "phoneLogin: Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "phoneLogin: request body: " + jsonBody);

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "phoneLogin network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "phoneLogin response code: " + response.code());
                    Log.d(TAG, "phoneLogin response headers: " + response.headers().toString());
                    Log.d(TAG, "phoneLogin response body: " + responseBody);

                    if (!response.isSuccessful()) {
                        ApiExceptionUtils.ApiException apiException = 
                                ApiExceptionUtils.handleHttpException(response.code(), responseBody);
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                        return;
                    }

                    // 解析响应
                    ApiResponseModel<LoginResponseModel> apiResponse = gson.fromJson(responseBody,
                            new TypeToken<ApiResponseModel<LoginResponseModel>>(){}.getType());
                    if (apiResponse != null && BaseApiConstantsUtils.isSuccessCode(apiResponse.getCode())) {
                        LoginResponseModel loginResponse = apiResponse.getData();
                        if (loginResponse != null) {
                            Log.d(TAG, "phoneLogin success - customerId: " + loginResponse.getCustomerId());
                            Log.d(TAG, "phoneLogin success - customerName: " + loginResponse.getCustomerName());
                            Log.d(TAG, "phoneLogin success - userToken: " + (loginResponse.getUserToken() != null ? "***" : "null"));
                            Log.d(TAG, "phoneLogin success - loginType: " + loginResponse.getLoginType());
                            Log.d(TAG, "phoneLogin success - deviceId: " + loginResponse.getDeviceId());
                            mainHandler.post(() -> callback.onSuccess(loginResponse));
                        } else {
                            Log.e(TAG, "phoneLogin error: Empty response data");
                            mainHandler.post(() -> callback.onError("Empty response data"));
                        }
                    } else {
                        String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Unknown error";
                        Log.e(TAG, "phoneLogin business error: " + errorMessage);
                        mainHandler.post(() -> callback.onError(errorMessage));
                    }

                } catch (Exception e) {
                    Log.e(TAG, "phoneLogin parse error: " + e.getMessage());
                    ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }
        });
    }

    /**
     * Facebook登录回调
     * @param context 上下文
     * @param authCode Facebook授权码
     * @param callback 回调接口
     */
    public static void facebookLogin(Context context, String authCode, ApiCallback<LoginResponseModel> callback) {
        Log.d(TAG, "facebookLogin called with authCode: " + (authCode != null ? "***" : "null"));

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 环境判断
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境返回模拟成功
            Log.d(TAG, "Dev environment: returning mock Facebook login success");
            LoginResponseModel mockResponse = createMockLoginResponse("facebook_user", 0);
            mainHandler.post(() -> callback.onSuccess(mockResponse));
            return;
        }

        // 获取设备ID
        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 创建请求模型
        FacebookLoginRequestModel requestModel = new FacebookLoginRequestModel(authCode, deviceId);
        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 发送API请求
        performThirdPartyLogin(AuthApiConstantsUtils.API_FACEBOOK_LOGIN, requestModel, callback);
    }

    /**
     * TikTok登录回调
     * @param context 上下文
     * @param authCode TikTok授权码
     * @param callback 回调接口
     */
    public static void tiktokLogin(Context context, String authCode, ApiCallback<LoginResponseModel> callback) {
        Log.d(TAG, "tiktokLogin called with authCode: " + (authCode != null ? "***" : "null"));

        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 环境判断
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境返回模拟成功
            Log.d(TAG, "Dev environment: returning mock TikTok login success");
            LoginResponseModel mockResponse = createMockLoginResponse("tiktok_user", 0);
            mainHandler.post(() -> callback.onSuccess(mockResponse));
            return;
        }

        // 获取设备ID
        String deviceId = DeviceIdUtils.getDeviceId(context);

        // 创建请求模型
        TiktokLoginRequestModel requestModel = new TiktokLoginRequestModel(authCode, deviceId);
        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 发送API请求
        performThirdPartyLogin(AuthApiConstantsUtils.API_TIKTOK_LOGIN, requestModel, callback);
    }

    /**
     * 执行第三方登录API请求
     * @param apiPath API路径
     * @param requestModel 请求模型
     * @param callback 回调接口
     */
    private static void performThirdPartyLogin(String apiPath, Object requestModel,
                                               ApiCallback<LoginResponseModel> callback) {
        String url = AuthApiConstantsUtils.buildApiUrl(EnvironmentConfigUtils.getApiBaseUrl(), apiPath);
        String jsonBody = gson.toJson(requestModel);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Third party login network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body() != null ? response.body().string() : "";
                    Log.d(TAG, "Third party login response: " + response.code());

                    if (!response.isSuccessful()) {
                        ApiExceptionUtils.ApiException apiException = 
                                ApiExceptionUtils.handleHttpException(response.code(), responseBody);
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                        return;
                    }

                    // 解析响应
                    ApiResponseModel<LoginResponseModel> apiResponse = gson.fromJson(responseBody, 
                            ApiResponseModel.class);
                    if (apiResponse.isSuccess() && apiResponse.hasData()) {
                        mainHandler.post(() -> callback.onSuccess(apiResponse.getData()));
                    } else {
                        ApiExceptionUtils.ApiException apiException = 
                                ApiExceptionUtils.handleBusinessException(apiResponse.getCode(), apiResponse.getMessage());
                        mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Third party login parse error: " + e.getMessage());
                    ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                    mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
                }
            }
        });
    }

    /**
     * 将UserModel转换为LoginResponseModel
     * @param userModel 用户模型
     * @return 登录响应模型
     */
    private static LoginResponseModel convertUserModelToLoginResponse(UserModel userModel) {
        LoginResponseModel response = new LoginResponseModel();

        // 使用新接口字段
        response.setCustomerId(userModel.getUid());
        response.setCustomerName(userModel.getUsername());
        response.setUserToken("dev_token_" + System.currentTimeMillis());
        response.setLoginType(AuthApiConstantsUtils.LOGIN_TYPE_PHONE);
        response.setDeviceId(userModel.getDeviceId() != null ? userModel.getDeviceId() : "dev_device_id");

        // 兼容性字段（用于向后兼容）
        response.setUid(userModel.getUid());
        response.setUserVip(userModel.getUserVip());
        response.setPhoneNumber(userModel.getPhoneNumber());
        response.setNickname(userModel.getUsername());
        response.setAccessToken("dev_token_" + System.currentTimeMillis());
        response.setExpiresAt(System.currentTimeMillis() + 24 * 60 * 60 * 1000L); // 24小时后过期

        return response;
    }

    /**
     * 创建模拟登录响应
     * @param userId 用户ID
     * @param vipStatus VIP状态
     * @return 模拟登录响应
     */
    private static LoginResponseModel createMockLoginResponse(String userId, int vipStatus) {
        LoginResponseModel response = new LoginResponseModel();
        response.setUid(userId);
        response.setUserVip(vipStatus);
        response.setNickname("Mock User");
        response.setAccessToken("mock_token_" + System.currentTimeMillis());
        response.setExpiresAt(System.currentTimeMillis() + 24 * 60 * 60 * 1000L);
        return response;
    }

    /**
     * 从LoginResponseModel创建UserModel
     * @param loginResponse 登录响应模型
     * @return 用户模型
     */
    public static UserModel createUserModelFromLoginResponse(LoginResponseModel loginResponse) {
        if (loginResponse == null) {
            return null;
        }

        // 使用新的构造函数创建UserModel
        UserModel userModel = new UserModel(
                loginResponse.getCustomerId(),
                loginResponse.getCustomerName(),
                loginResponse.getUserToken(),
                loginResponse.getLoginType(),
                loginResponse.getDeviceId()
        );

        return userModel;
    }

    /**
     * 从InitDeviceResponseModel创建UserModel（如果用户已登录）
     * @param initResponse 初始化响应模型
     * @return 用户模型，如果用户未登录则返回null
     */
    public static UserModel createUserModelFromInitResponse(InitDeviceResponseModel initResponse) {
        if (initResponse == null || !initResponse.isUserLoggedIn()) {
            return null;
        }

        // 使用新的构造函数创建UserModel
        UserModel userModel = new UserModel(
                initResponse.getCustomerId(),
                initResponse.getCustomerName(),
                initResponse.getUserToken(),
                initResponse.getLoginType(),
                initResponse.getDeviceId()
        );

        return userModel;
    }

    // ========== 新接口方法 ==========

    /**
     * 初始化设备信息
     * <p>
     * APP启动时调用的初始化接口，用于初始化设备信息。
     * </p>
     *
     * @param context 上下文
     * @param callback 回调接口
     */
    public static void initDevice(Context context, ApiCallback<InitDeviceResponseModel> callback) {
        if (context == null) {
            if (callback != null) {
                callback.onError("Context is null");
            }
            return;
        }

        if (callback == null) {
            Log.w(TAG, "initDevice: callback is null");
            return;
        }

        Handler mainHandler = new Handler(Looper.getMainLooper());

        // 获取设备信息
        String deviceCode = DeviceIdUtils.getDeviceId(context);
        int osType = AuthApiConstantsUtils.OS_TYPE_ANDROID;

        // 创建请求模型
        InitDeviceRequestModel requestModel = new InitDeviceRequestModel(deviceCode, osType);
        if (!requestModel.isValid()) {
            mainHandler.post(() -> callback.onError(requestModel.getValidationError()));
            return;
        }

        // 构建API URL
        String url = AuthApiConstantsUtils.buildApiUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                AuthApiConstantsUtils.API_INIT_DEVICE
        );

        String jsonBody = gson.toJson(requestModel);
        RequestBody body = RequestBody.create(jsonBody, JSON);

        // 构建请求（初始化接口不需要X-Access-Token）
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Content-Type", "application/json")
                .build();

        Log.d(TAG, "initDevice: requesting URL: " + url);
        Log.d(TAG, "initDevice: request body: " + jsonBody);
        Log.d(TAG, "initDevice: device code: " + deviceCode);
        Log.d(TAG, "initDevice: OS type: " + osType + " (Android)");

        ApiClientUtils.getHttpClient().newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "initDevice network error: " + e.getMessage());
                ApiExceptionUtils.ApiException apiException = ApiExceptionUtils.handleException(e);
                mainHandler.post(() -> callback.onError(apiException.getUserMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "initDevice response code: " + response.code());
                    Log.d(TAG, "initDevice response headers: " + response.headers().toString());
                    Log.d(TAG, "initDevice response body: " + responseBody);

                    if (response.isSuccessful()) {
                        // 解析响应
                        ApiResponseModel<InitDeviceResponseModel> apiResponse = gson.fromJson(
                                responseBody,
                                new TypeToken<ApiResponseModel<InitDeviceResponseModel>>(){}.getType()
                        );

                        if (apiResponse != null && BaseApiConstantsUtils.isSuccessCode(apiResponse.getCode())) {
                            InitDeviceResponseModel initResponse = apiResponse.getData();
                            if (initResponse != null) {
                                Log.d(TAG, "initDevice success - customerId: " + initResponse.getCustomerId());
                                Log.d(TAG, "initDevice success - customerName: " + initResponse.getCustomerName());
                                Log.d(TAG, "initDevice success - userToken: " + (initResponse.getUserToken() != null ? "***" : "null"));
                                Log.d(TAG, "initDevice success - loginType: " + initResponse.getLoginType());
                                Log.d(TAG, "initDevice success - deviceId: " + initResponse.getDeviceId());
                                Log.d(TAG, "initDevice success - isUserLoggedIn: " + initResponse.isUserLoggedIn());
                                mainHandler.post(() -> callback.onSuccess(initResponse));
                            } else {
                                Log.e(TAG, "initDevice error: Empty response data");
                                mainHandler.post(() -> callback.onError("Empty response data"));
                            }
                        } else {
                            String errorMessage = apiResponse != null ? apiResponse.getMessage() : "Unknown error";
                            mainHandler.post(() -> callback.onError(errorMessage));
                        }
                    } else {
                        String errorMessage = "Server error: " + response.code();
                        mainHandler.post(() -> callback.onError(errorMessage));
                    }
                } catch (Exception e) {
                    Log.e(TAG, "initDevice response parsing error", e);
                    mainHandler.post(() -> callback.onError("Response parsing failed: " + e.getMessage()));
                }
            }
        });
    }
}
