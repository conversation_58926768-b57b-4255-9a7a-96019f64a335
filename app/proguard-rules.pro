# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile

# ExoPlayer ProGuard rules
-keep class com.google.android.exoplayer2.** { *; }
-dontwarn com.google.android.exoplayer2.**

# Keep ExoPlayer's MediaSource classes
-keep class * extends com.google.android.exoplayer2.source.MediaSource
-keep class * extends com.google.android.exoplayer2.extractor.Extractor

# Keep ExoPlayer's Renderer classes
-keep class * extends com.google.android.exoplayer2.Renderer

# Keep ExoPlayer's TrackSelection classes
-keep class * extends com.google.android.exoplayer2.trackselection.TrackSelection