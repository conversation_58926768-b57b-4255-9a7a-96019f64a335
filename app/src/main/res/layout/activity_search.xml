<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000"
    tools:context=".ui.activity.SearchActivity">

    <!-- 顶部渐变阴影背景 -->
    <View
        android:layout_width="0dp"
        android:layout_height="@dimen/search_top_shadow_height"
        android:background="@drawable/search_top_shadow_background"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/search_back_button_size"
        android:layout_height="@dimen/search_back_button_size"
        android:layout_marginStart="@dimen/search_back_button_margin_start"
        android:layout_marginTop="@dimen/search_back_button_margin_top"
        android:src="@drawable/ic_back_chevron"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 搜索输入框容器 -->
    <FrameLayout
        android:id="@+id/fl_search_container"
        android:layout_width="@dimen/search_input_width"
        android:layout_height="@dimen/search_input_height"
        android:layout_marginStart="@dimen/search_input_margin_start"
        android:layout_marginTop="@dimen/search_input_margin_top"
        android:background="@drawable/search_input_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <!-- 搜索图标 -->
        <ImageView
            android:id="@+id/iv_search_icon"
            android:layout_width="@dimen/search_input_icon_size"
            android:layout_height="@dimen/search_input_icon_size"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="@dimen/search_input_icon_margin_start"
            android:src="@drawable/home_ic_search" />

        <!-- 搜索输入框 -->
        <EditText
            android:id="@+id/et_search"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/search_input_icon_size"
            android:layout_marginEnd="@dimen/search_input_padding_start"
            android:background="@android:color/transparent"
            android:hint="Search"
            android:textColorHint="#8fffffff"
            android:textColor="#ffffffff"
            android:textSize="@dimen/search_text_size"
            android:inputType="text"
            android:imeOptions="actionSearch"
            android:maxLines="1"
            android:focusable="true"
            android:focusableInTouchMode="true"
            android:clickable="true"
            android:paddingStart="@dimen/search_input_icon_size"
            android:paddingVertical="@dimen/search_input_padding_vertical" />

        <!-- 清除按钮 -->
        <ImageView
            android:id="@+id/iv_clear"
            android:layout_width="@dimen/search_input_icon_size"
            android:layout_height="@dimen/search_input_icon_size"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="@dimen/search_input_icon_margin_start"
            android:src="@drawable/home_ic_closesear"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true"
            android:visibility="gone"
            android:contentDescription="Clear" />

    </FrameLayout>

    <!-- Recent searches 标题 -->
    <TextView
        android:id="@+id/tv_recent_searches_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/search_recent_title_margin_start"
        android:layout_marginTop="@dimen/search_recent_title_margin_top"
        android:text="Recent searches"
        android:textColor="#ffffffff"
        android:textSize="@dimen/search_recent_title_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 清除最近搜索图标 -->
    <ImageView
        android:id="@+id/iv_clear_recent_searches"
        android:layout_width="@dimen/search_clear_icon_size"
        android:layout_height="@dimen/search_clear_icon_size"
        android:layout_marginTop="@dimen/search_clear_icon_margin_top"
        android:layout_marginEnd="@dimen/search_clear_icon_margin_end"
        android:src="@drawable/home_ic_delete"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Recent searches RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_recent_searches"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/search_recent_title_margin_start"
        android:layout_marginTop="@dimen/search_trending_title_margin_top"
        android:layout_marginEnd="@dimen/search_clear_icon_margin_end"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_recent_searches_title" />

    <!-- Trending searches 标题 -->
    <TextView
        android:id="@+id/tv_trending_searches_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/search_trending_title_margin_start"
        android:layout_marginTop="@dimen/search_trending_title_margin_top"
        android:text="Trending searches"
        android:textColor="#ffffffff"
        android:textSize="@dimen/search_trending_title_size"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/rv_recent_searches" />

    <!-- Trending searches RecyclerView -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_trending_searches"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/search_trending_title_margin_start"
        android:layout_marginTop="@dimen/search_trending_title_margin_top"
        android:layout_marginEnd="@dimen/search_trending_title_margin_start"
        android:layout_marginBottom="@dimen/search_bottom_margin"
        android:clipToPadding="false"
        android:paddingBottom="@dimen/search_bottom_padding"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_trending_searches_title" />

</androidx.constraintlayout.widget.ConstraintLayout>
