package com.android.video.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.EditText;
import android.widget.TextView;
import android.widget.Toast;

import com.android.video.R;

public class RedemptionCodeDialog extends Dialog {

    private EditText etRedeemCode;
    private TextView btnExchange;
    private OnRedeemCodeListener listener;

    public interface OnRedeemCodeListener {
        void onRedeemCode(String code);
    }

    public RedemptionCodeDialog(Context context) {
        super(context, R.style.CustomDialog);
    }

    public RedemptionCodeDialog(Context context, OnRedeemCodeListener listener) {
        super(context, R.style.CustomDialog);
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_redemption_code);

        // 设置窗口属性
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.WRAP_CONTENT;
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(params);
            window.setDimAmount(0.5f);
        }

        initViews();
        setupClickListeners();

        // 添加弹窗进入动画
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleIn(dialogView);
        }
    }

    private void initViews() {
        etRedeemCode = findViewById(R.id.et_redeem_code);
        btnExchange = findViewById(R.id.btn_exchange);
    }

    private void setupClickListeners() {
        btnExchange.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                // 添加按钮点击动画
                com.android.video.utils.UIAnimationUtils.animateButtonClick(v);
                handleExchange();
            }
        });
    }

    private void handleExchange() {
        String code = etRedeemCode.getText().toString().trim();

        if (TextUtils.isEmpty(code)) {
            Toast.makeText(getContext(), "Please enter redemption code", Toast.LENGTH_SHORT).show();
            return;
        }

        if (listener != null) {
            listener.onRedeemCode(code);
        }

        dismissWithAnimation();
    }

    public void setOnRedeemCodeListener(OnRedeemCodeListener listener) {
        this.listener = listener;
    }

    /**
     * 带动画的关闭弹窗
     */
    private void dismissWithAnimation() {
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleOut(dialogView, () -> {
                RedemptionCodeDialog.super.dismiss();
            });
        } else {
            super.dismiss();
        }
    }
}
