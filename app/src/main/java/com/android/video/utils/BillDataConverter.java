package com.android.video.utils;

import com.android.video.R;
import com.android.video.constants.BillApiConstantsUtils;
import com.android.video.model.PointsPurchase;
import com.android.video.model.VideoRecord;
import com.android.video.model.VipRecord;
import com.android.video.model.response.BillOrderItem;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Locale;

/**
 * 账单数据转换工具类
 * <p>
 * 负责将API返回的BillOrderItem数据转换为UI层使用的数据模型。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class BillDataConverter {

    private static final String TAG = "BillDataConverter";

    /**
     * 将BillOrderItem转换为VideoRecord
     * <p>
     * 用于orderType=1的剧集解锁订单数据转换。
     * </p>
     * 
     * @param orderItem API返回的订单项
     * @return VideoRecord对象
     */
    public static VideoRecord convertToVideoRecord(BillOrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }

        VideoRecord videoRecord = new VideoRecord();
        
        // 设置视频标题（使用bizName）
        videoRecord.setTitle(orderItem.getBizName() != null ? orderItem.getBizName() : "");
        
        // 设置集数（使用unlockChapter）
        videoRecord.setEpisode(orderItem.getUnlockChapter() != null ? orderItem.getUnlockChapter() : "");
        
        // 设置描述
        videoRecord.setDescription("Unlock 1 Episode");
        
        // 设置日期时间（格式化timeOfPayment）
        videoRecord.setDateTime(formatDateTime(orderItem.getTimeOfPayment()));
        
        // 设置消费积分（负数表示消费）
        videoRecord.setCost("-" + orderItem.getSpendPoints());

        // 设置海报URL（优先使用filmOrderVO中的cover字段）
        if (orderItem.getFilmOrderVO() != null &&
            orderItem.getFilmOrderVO().getCover() != null &&
            !orderItem.getFilmOrderVO().getCover().isEmpty()) {
            videoRecord.setPosterUrl(orderItem.getFilmOrderVO().getCover());
        } else if (orderItem.getCover() != null && !orderItem.getCover().isEmpty()) {
            videoRecord.setPosterUrl(orderItem.getCover());
        } else {
            // 如果没有cover字段，使用默认海报资源
            videoRecord.setPosterRes(R.drawable.movie_poster);
        }
        
        return videoRecord;
    }

    /**
     * 将BillOrderItem转换为VipRecord
     * <p>
     * 用于orderType=2的VIP购买订单数据转换。
     * </p>
     * 
     * @param orderItem API返回的订单项
     * @return VipRecord对象
     */
    public static VipRecord convertToVipRecord(BillOrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }

        VipRecord vipRecord = new VipRecord();
        
        // 设置购买类型（使用bizName）
        vipRecord.setPurchaseType(orderItem.getBizName() != null ? orderItem.getBizName() : "VIP Purchase");
        
        // 设置日期时间
        vipRecord.setDateTime(formatDateTime(orderItem.getTimeOfPayment()));
        
        // 设置价格（格式化为货币格式）
        vipRecord.setPrice(formatPrice(orderItem.getPayAmount()));
        
        // 设置支付方式
        vipRecord.setPaymentMethod(orderItem.getPayType() != null ? orderItem.getPayType() : "Unknown");
        
        // 设置支付方式图标（根据payType设置）
        vipRecord.setPaymentIconRes(getPaymentIcon(orderItem.getPayType()));
        
        return vipRecord;
    }

    /**
     * 将BillOrderItem转换为PointsPurchase
     * <p>
     * 用于orderType=3的积分包购买订单数据转换。
     * </p>
     * 
     * @param orderItem API返回的订单项
     * @return PointsPurchase对象
     */
    public static PointsPurchase convertToPointsPurchase(BillOrderItem orderItem) {
        if (orderItem == null) {
            return null;
        }

        PointsPurchase pointsPurchase = new PointsPurchase();
        
        // 设置积分套餐名称
        pointsPurchase.setPackageName(orderItem.getBizName() != null ? orderItem.getBizName() : "Points Package");
        
        // 设置日期时间
        pointsPurchase.setDateTime(formatDateTime(orderItem.getTimeOfPayment()));
        
        // 设置价格
        pointsPurchase.setPrice(formatPrice(orderItem.getPayAmount()));
        
        // 设置积分数量
        pointsPurchase.setPoints(orderItem.getPoints());
        
        // 设置赠送积分
        pointsPurchase.setBonusPoints(orderItem.getGiftPoints());
        
        return pointsPurchase;
    }

    /**
     * 批量转换为VideoRecord列表
     */
    public static List<VideoRecord> convertToVideoRecordList(List<BillOrderItem> orderItems) {
        List<VideoRecord> videoRecords = new ArrayList<>();
        if (orderItems != null) {
            for (BillOrderItem item : orderItems) {
                VideoRecord record = convertToVideoRecord(item);
                if (record != null) {
                    videoRecords.add(record);
                }
            }
        }
        return videoRecords;
    }

    /**
     * 批量转换为VipRecord列表
     */
    public static List<VipRecord> convertToVipRecordList(List<BillOrderItem> orderItems) {
        List<VipRecord> vipRecords = new ArrayList<>();
        if (orderItems != null) {
            for (BillOrderItem item : orderItems) {
                VipRecord record = convertToVipRecord(item);
                if (record != null) {
                    vipRecords.add(record);
                }
            }
        }
        return vipRecords;
    }

    /**
     * 批量转换为PointsPurchase列表
     */
    public static List<PointsPurchase> convertToPointsPurchaseList(List<BillOrderItem> orderItems) {
        List<PointsPurchase> pointsPurchases = new ArrayList<>();
        if (orderItems != null) {
            for (BillOrderItem item : orderItems) {
                PointsPurchase record = convertToPointsPurchase(item);
                if (record != null) {
                    pointsPurchases.add(record);
                }
            }
        }
        return pointsPurchases;
    }

    /**
     * 格式化日期时间
     * <p>
     * 将API返回的时间格式转换为UI显示格式。
     * </p>
     * 
     * @param dateTimeStr API返回的时间字符串
     * @return 格式化后的时间字符串
     */
    private static String formatDateTime(String dateTimeStr) {
        if (dateTimeStr == null || dateTimeStr.isEmpty()) {
            return "";
        }

        try {
            // API返回格式：2025-07-24 22:23:07
            SimpleDateFormat inputFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.getDefault());
            // UI显示格式：06/18/2025 10:03AM - 强制使用英文AM/PM
            SimpleDateFormat outputFormat = new SimpleDateFormat("MM/dd/yyyy hh:mma", Locale.ENGLISH);
            
            Date date = inputFormat.parse(dateTimeStr);
            if (date != null) {
                return outputFormat.format(date);
            }
        } catch (Exception e) {
            // 解析失败时返回原始字符串
            return dateTimeStr;
        }
        
        return dateTimeStr;
    }

    /**
     * 格式化价格
     * <p>
     * 将数值价格格式化为货币字符串。
     * </p>
     * 
     * @param price 价格数值
     * @return 格式化后的价格字符串
     */
    private static String formatPrice(double price) {
        if (price == 0) {
            return "$0";
        }
        return "-$" + String.format(Locale.getDefault(), "%.0f", price);
    }

    /**
     * 根据支付方式获取对应的图标资源ID
     * <p>
     * 根据API返回的payType字段返回对应的图标资源。
     * </p>
     *
     * @param payType 支付方式
     * @return 图标资源ID
     */
    private static int getPaymentIcon(String payType) {
        if (payType == null) {
            return 0;
        }

        switch (payType.toLowerCase()) {
            case "pay":
            case "ecom":
                return R.drawable.bill_ic_paymethon; // 使用现有的支付图标
            case "point-based payment":
                return 0; // 积分支付暂时不显示图标
            default:
                return 0;
        }
    }
}
