package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.util.Log;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.graphics.Insets;
import com.android.video.model.VideoModel;
import com.android.video.model.ActorModel;
import com.android.video.model.DirectorModel;
import com.android.video.model.EpisodeModel;
import java.util.List;

/**
 * 测试验证工具类
 * 用于验证修复后的功能是否正常工作
 * 
 * <AUTHOR> Team
 */
public class TestValidationUtils {
    
    private static final String TAG = "TestValidation";
    
    /**
     * 测试结果类
     */
    public static class TestResult {
        public final boolean success;
        public final String message;
        public final String details;
        
        public TestResult(boolean success, String message, String details) {
            this.success = success;
            this.message = message;
            this.details = details;
        }
        
        @Override
        public String toString() {
            return "TestResult{" +
                    "success=" + success +
                    ", message='" + message + '\'' +
                    ", details='" + details + '\'' +
                    '}';
        }
    }
    
    /**
     * 验证MainActivity的导航栏适配
     */
    public static TestResult validateMainActivityNavigationBar(Activity activity) {
        try {
            // 获取导航栏信息
            NavigationBarUtils.NavigationBarInfo navInfo = 
                NavigationBarUtils.getNavigationBarInfo(activity);
            
            // 验证导航栏适配
            NavigationBarUtils.NavigationBarValidationResult validationResult =
                NavigationBarUtils.validateNavigationBarAdaptation(activity);
            
            StringBuilder details = new StringBuilder();
            details.append("导航栏信息: ").append(navInfo.toString()).append("\n");
            details.append("适配验证: ").append(validationResult.toString()).append("\n");
            
            // 检查WindowInsets是否正确设置
            android.view.View rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
                if (insets != null) {
                    Insets navBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars());
                    Insets tappable = insets.getInsets(WindowInsetsCompat.Type.tappableElement());
                    
                    details.append("NavigationBars insets: ").append(navBars.toString()).append("\n");
                    details.append("TappableElement insets: ").append(tappable.toString()).append("\n");
                }
            }
            
            boolean success = validationResult.isValid || !navInfo.exists;
            String message = success ? "导航栏适配验证通过" : "导航栏适配存在问题";
            
            Log.d(TAG, message + " - " + details.toString());
            return new TestResult(success, message, details.toString());
            
        } catch (Exception e) {
            String errorMsg = "导航栏适配验证异常: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            return new TestResult(false, errorMsg, e.toString());
        }
    }
    
    /**
     * 验证VideoModel数据完整性
     */
    public static TestResult validateVideoModelData(VideoModel video) {
        if (video == null) {
            return new TestResult(false, "VideoModel为null", "");
        }
        
        StringBuilder details = new StringBuilder();
        boolean hasIssues = false;
        
        // 检查基本信息
        if (video.getTitle() == null || video.getTitle().isEmpty()) {
            details.append("标题为空; ");
            hasIssues = true;
        }
        
        if (video.getSynopsis() == null || video.getSynopsis().isEmpty()) {
            details.append("概要为空; ");
            hasIssues = true;
        }
        
        // 检查演员信息
        List<ActorModel> actors = video.getActors();
        if (actors == null || actors.isEmpty()) {
            details.append("演员列表为空; ");
            hasIssues = true;
        } else {
            details.append("演员数量: ").append(actors.size()).append("; ");
        }
        
        // 检查导演信息
        List<DirectorModel> directors = video.getDirectors();
        if (directors == null || directors.isEmpty()) {
            details.append("导演列表为空; ");
            hasIssues = true;
        } else {
            details.append("导演数量: ").append(directors.size()).append("; ");
        }
        
        // 检查标签信息
        List<String> tags = video.getTags();
        if (tags == null || tags.isEmpty()) {
            details.append("标签列表为空; ");
            hasIssues = true;
        } else {
            details.append("标签数量: ").append(tags.size()).append("; ");
        }
        
        // 检查剧集信息
        List<EpisodeModel> episodes = video.getEpisodes();
        if (video.getTotalEpisodes() > 1) {
            if (episodes == null || episodes.isEmpty()) {
                details.append("多集视频但剧集列表为空; ");
                hasIssues = true;
            } else {
                details.append("剧集数量: ").append(episodes.size()).append("; ");
                
                // 检查剧集区间文本
                String rangeText = video.getEpisodeRangeText();
                if (rangeText == null || rangeText.equals("1-1")) {
                    details.append("剧集区间显示异常: ").append(rangeText).append("; ");
                    hasIssues = true;
                }
            }
        }
        
        String message = hasIssues ? "VideoModel数据不完整" : "VideoModel数据完整";
        boolean success = !hasIssues;
        
        Log.d(TAG, message + " - " + details.toString());
        return new TestResult(success, message, details.toString());
    }
    
    /**
     * 验证设备配置变化适配
     */
    public static TestResult validateConfigurationChange(Activity activity, String orientation) {
        try {
            StringBuilder details = new StringBuilder();
            details.append("当前方向: ").append(orientation).append("\n");
            
            // 重新验证导航栏适配
            TestResult navResult = validateMainActivityNavigationBar(activity);
            details.append("导航栏适配: ").append(navResult.success ? "正常" : "异常").append("\n");
            details.append(navResult.details);
            
            String message = navResult.success ? "配置变化适配正常" : "配置变化适配异常";
            
            Log.d(TAG, message + " - " + details.toString());
            return new TestResult(navResult.success, message, details.toString());
            
        } catch (Exception e) {
            String errorMsg = "配置变化验证异常: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            return new TestResult(false, errorMsg, e.toString());
        }
    }
    
    /**
     * 生成完整的测试报告
     */
    public static String generateTestReport(List<TestResult> results) {
        StringBuilder report = new StringBuilder();
        report.append("=== 视频播放器修复验证报告 ===\n");
        report.append("测试时间: ").append(new java.util.Date().toString()).append("\n\n");
        
        int totalTests = results.size();
        int passedTests = 0;
        
        for (int i = 0; i < results.size(); i++) {
            TestResult result = results.get(i);
            if (result.success) passedTests++;
            
            report.append("测试 ").append(i + 1).append(": ").append(result.message).append("\n");
            report.append("结果: ").append(result.success ? "通过" : "失败").append("\n");
            if (!result.details.isEmpty()) {
                report.append("详情: ").append(result.details).append("\n");
            }
            report.append("\n");
        }
        
        report.append("=== 测试总结 ===\n");
        report.append("总测试数: ").append(totalTests).append("\n");
        report.append("通过测试: ").append(passedTests).append("\n");
        report.append("失败测试: ").append(totalTests - passedTests).append("\n");
        report.append("通过率: ").append(String.format("%.1f%%", (passedTests * 100.0 / totalTests))).append("\n");
        
        return report.toString();
    }
    
    /**
     * 调试输出系统信息
     */
    public static void debugSystemInfo(Context context) {
        Log.d(TAG, "=== 系统信息 ===");
        Log.d(TAG, "Android版本: " + android.os.Build.VERSION.RELEASE);
        Log.d(TAG, "API级别: " + android.os.Build.VERSION.SDK_INT);
        Log.d(TAG, "设备型号: " + android.os.Build.MODEL);
        Log.d(TAG, "设备制造商: " + android.os.Build.MANUFACTURER);
        
        // 屏幕信息
        android.util.DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        Log.d(TAG, "屏幕密度: " + metrics.density);
        Log.d(TAG, "屏幕尺寸: " + metrics.widthPixels + "x" + metrics.heightPixels);
        
        // 配置信息
        android.content.res.Configuration config = context.getResources().getConfiguration();
        String orientation = config.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE ? "横屏" : "竖屏";
        Log.d(TAG, "屏幕方向: " + orientation);
    }
}
