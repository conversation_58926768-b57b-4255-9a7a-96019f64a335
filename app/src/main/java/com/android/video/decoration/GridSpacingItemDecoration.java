package com.android.video.decoration;

import android.graphics.Rect;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 网格布局间距装饰器
 * 用于为GridLayoutManager添加统一的间距
 */
public class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {

    private final int spanCount; // 列数
    private final int edgeSpacing; // 边缘间距
    private final boolean includeEdge; // 是否包含边缘间距

    /**
     * 构造函数
     * @param spanCount 网格列数
     * @param edgeSpacing 边缘间距大小（dp）
     * @param includeEdge 是否包含边缘间距
     */
    public GridSpacingItemDecoration(int spanCount, int edgeSpacing, boolean includeEdge) {
        this.spanCount = spanCount;
        this.edgeSpacing = edgeSpacing;
        this.includeEdge = includeEdge;
    }

    @Override
    public void getItemOffsets(@NonNull Rect outRect, @NonNull View view,
                              @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
        int position = parent.getChildAdapterPosition(view); // item position
        int column = position % spanCount; // item column

        if (includeEdge) {
            // 使用设计规范：左右边距 = edgeSpacing，中间间距 = spacing
            // 左列：left = edgeSpacing, right = spacing/2
            // 右列：left = spacing/2, right = edgeSpacing

            if (column == 0) {
                // 左列
                outRect.left = edgeSpacing;
                outRect.right = edgeSpacing / 2; // 中间间距的一半
            } else {
                // 右列
                outRect.left = edgeSpacing / 2; // 中间间距的一半
                outRect.right = edgeSpacing;
            }

            if (position < spanCount) { // 第一行
                outRect.top = edgeSpacing;
            }
            outRect.bottom = edgeSpacing; // 所有item都有底部间距
        } else {
            // 不包含边缘间距的情况
            if (column == 0) {
                // 左列
                outRect.left = 0;
                outRect.right = edgeSpacing / 2;
            } else {
                // 右列
                outRect.left = edgeSpacing / 2;
                outRect.right = 0;
            }

            if (position >= spanCount) { // 非第一行
                outRect.top = edgeSpacing;
            }
        }
    }
}
