package com.android.video.constants;

/**
 * 积分中心模块API常量类 - 定义积分相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理积分中心相关的API常量，
 * 包括积分套餐查询、积分购买、积分记录等功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的积分中心功能：
 * <ul>
 *   <li>积分套餐管理</li>
 *   <li>积分购买和充值</li>
 *   <li>积分消费记录</li>
 *   <li>积分余额查询</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 积分中心模块特点：
 * <ul>
 *   <li>需要用户登录认证</li>
 *   <li>支持多种积分套餐</li>
 *   <li>提供完整的积分交易记录</li>
 *   <li>支持积分赠送功能</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取积分套餐API URL
 * String url = PointsApiConstantsUtils.buildApiUrl(baseUrl, PointsApiConstantsUtils.API_GET_POINTS_PACKAGE_LIST);
 * 
 * // 使用请求头
 * Map&lt;String, String&gt; headers = ApiHeaderUtils.getDefaultHeaders();
 * 
 * // 解析响应数据
 * String packageId = response.getString(PointsApiConstantsUtils.FIELD_PACKAGE_ID);
 * int points = response.getInt(PointsApiConstantsUtils.FIELD_POINTS);
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class PointsApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 积分中心相关API路径常量 ==========

    /**
     * 获取积分套餐列表API路径
     * <p>
     * 用于获取可购买的积分套餐列表。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>无需参数</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": [
     *     {
     *       "packageId": "07649e1a1072402f8366e2a2019dfe4f",
     *       "packageName": "NAME1",
     *       "points": 1,
     *       "giftPoints": 0,
     *       "price": 11,
     *       "isDelete": 1,
     *       "createTime": "2025-07-21 10:12:26",
     *       "updateTime": "2025-07-21 11:26:31"
     *     }
     *   ]
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_POINTS_PACKAGE_LIST = "/app/pointsCenter/pointsPackageList";

    /**
     * 获取积分流水列表API路径
     * <p>
     * 用于获取用户的积分收支流水记录，支持分页查询。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>page: 页码，从1开始 (int, 必填)</li>
     *   <li>size: 每页数量 (int, 必填)</li>
     *   <li>billType: 积分流水类型，1-收入 2-支出 (int, 必填)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "records": [
     *       {
     *         "id": "fc440bd4bcc11a096ab641554e31325e",
     *         "billType": 1,
     *         "points": 1,
     *         "bizDesc": "Buy Points",
     *         "createTime": "2025-07-21 16:11:31",
     *         "prePointsBalance": 10000,
     *         "post_points_balance": 10001
     *       }
     *     ],
     *     "total": 2,
     *     "current": 1,
     *     "pages": 1
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_POINTS_SERIAL_LIST = "/app/pointsCenter/pointsSerialList";

    /**
     * 积分套餐购买支付API路径
     * <p>
     * 用于购买积分套餐，调用OneVisionPay支付系统。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>packageId: 积分套餐ID (String, 必填)</li>
     *   <li>payAmount: 支付金额 (double, 必填)</li>
     *   <li>payType: 支付方式，ecom(支付卡) 或 mc(移动支付) (String, 必填)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "orderNo": "ORDER123456789",
     *     "paymentPageUrl": "https://checkout.onevisionpay.com/..."
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_POINTS_BUY = "/app/pointsCenter/pointsBuy";

    // ========== 积分套餐响应字段常量 ==========

    /**
     * 套餐ID字段名
     * <p>
     * 积分套餐的唯一标识符，采用UUID格式。
     * 字段类型：String
     * 示例值：07649e1a1072402f8366e2a2019dfe4f
     * </p>
     */
    public static final String FIELD_PACKAGE_ID = "packageId";

    /**
     * 套餐名称字段名
     * <p>
     * 积分套餐名称，全局唯一。
     * 字段类型：String
     * 示例值：500 points package
     * </p>
     */
    public static final String FIELD_PACKAGE_NAME = "packageName";

    /**
     * 基础积分字段名
     * <p>
     * 套餐包含的基础积分数量，需大于0。
     * 字段类型：int
     * 示例值：500
     * </p>
     */
    public static final String FIELD_POINTS = "points";

    /**
     * 赠送积分字段名
     * <p>
     * 套餐包含的赠送积分数量，可为0。
     * 字段类型：int
     * 示例值：50
     * </p>
     */
    public static final String FIELD_GIFT_POINTS = "giftPoints";

    /**
     * 套餐价格字段名
     * <p>
     * 套餐价格，支持小数。
     * 字段类型：double
     * 示例值：9.99
     * </p>
     */
    public static final String FIELD_PRICE = "price";

    // ========== 积分流水响应字段常量 ==========

    /**
     * 流水ID字段名
     * <p>
     * 积分流水的唯一标识符，采用32位字符串（通常为UUID格式）。
     * 字段类型：String
     * 示例值：fc440bd4bcc11a096ab641554e31325e
     * </p>
     */
    public static final String FIELD_SERIAL_ID = "id";

    /**
     * 用户ID字段名
     * <p>
     * 关联用户主表的用户ID，固定32位长度。
     * 字段类型：String
     * 示例值：fe045921c7a8c7b18aabdc76cd2edae8
     * </p>
     */
    public static final String FIELD_USER_ID = "userId";

    /**
     * 用户名字段名
     * <p>
     * 用户名，冗余存储便于查询，最大长度50。
     * 字段类型：String
     * 示例值：caoh
     * </p>
     */
    public static final String FIELD_USER_NAME = "userName";

    /**
     * 积分流水类型字段名
     * <p>
     * 积分流水类型：1-收入 2-支出。
     * 字段类型：int
     * 示例值：1
     * </p>
     */
    public static final String FIELD_BILL_TYPE = "billType";

    /**
     * 积分变动值字段名
     * <p>
     * 积分变动值，正数表示增加积分，负数表示减少积分。
     * 字段类型：int
     * 示例值：100
     * </p>
     */
    public static final String FIELD_POINTS_CHANGE = "points";

    /**
     * 业务类型字段名
     * <p>
     * 业务类型标识，用于区分积分变动的业务场景。
     * 字段类型：String
     * 示例值：BuyPoints
     * </p>
     */
    public static final String FIELD_BIZ_TYPE = "bizType";

    /**
     * 业务描述字段名
     * <p>
     * 业务详细描述，提供积分变动的具体说明，最大长度100。
     * 字段类型：String
     * 示例值：Buy Points
     * </p>
     */
    public static final String FIELD_BIZ_DESC = "bizDesc";

    /**
     * 流水前余额字段名
     * <p>
     * 积分流水前的账户余额。
     * 字段类型：int
     * 示例值：10000
     * </p>
     */
    public static final String FIELD_PRE_POINTS_BALANCE = "prePointsBalance";

    /**
     * 流水后余额字段名
     * <p>
     * 积分流水后的账户余额。
     * 字段类型：int
     * 示例值：10001
     * </p>
     */
    public static final String FIELD_POST_POINTS_BALANCE = "post_points_balance";

    // ========== 积分套餐状态常量 ==========

    /**
     * 套餐正常状态
     */
    public static final int PACKAGE_STATUS_NORMAL = 1;

    /**
     * 套餐已删除状态
     */
    public static final int PACKAGE_STATUS_DELETED = 0;

    // ========== 积分流水类型常量 ==========

    /**
     * 积分流水类型 - 收入
     */
    public static final int BILL_TYPE_INCOME = 1;

    /**
     * 积分流水类型 - 支出
     */
    public static final int BILL_TYPE_EXPENSE = 2;

    // ========== 积分流水请求参数常量 ==========

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 积分流水类型参数名
     */
    public static final String PARAM_BILL_TYPE = "billType";

    // ========== 积分套餐支付请求参数常量 ==========

    /**
     * 积分套餐ID参数名
     * <p>
     * 用于支付接口的套餐ID参数，标识要购买的积分套餐。
     * 字段类型：String
     * 示例值：07649e1a1072402f8366e2a2019dfe4f
     * </p>
     */
    public static final String PARAM_PACKAGE_ID = "packageId";

    /**
     * 支付金额参数名
     * <p>
     * 用于支付接口的金额参数，必须大于0。
     * 字段类型：double
     * 示例值：9.99
     * </p>
     */
    public static final String PARAM_PAY_AMOUNT = "payAmount";

    /**
     * 支付方式参数名
     * <p>
     * 用于支付接口的支付方式参数，支持ecom和mc两种方式。
     * 字段类型：String
     * 可选值：ecom(支付卡), mc(移动支付)
     * </p>
     */
    public static final String PARAM_PAY_TYPE = "payType";

    // ========== 积分套餐支付方式常量 ==========

    /**
     * 支付方式 - 支付卡
     * <p>
     * 使用支付卡进行支付，对应OneVisionPay的ecom支付方式。
     * </p>
     */
    public static final String PAY_TYPE_ECOM = "ecom";

    /**
     * 支付方式 - 移动支付
     * <p>
     * 使用移动支付进行支付，对应OneVisionPay的mc支付方式。
     * </p>
     */
    public static final String PAY_TYPE_MOBILE_COMMERCE = "mc";

    // ========== 积分套餐支付响应字段常量 ==========

    /**
     * 订单编号字段名
     * <p>
     * 支付接口返回的订单编号，用于跟踪支付状态。
     * 字段类型：String
     * 示例值：ORDER123456789
     * </p>
     */
    public static final String FIELD_ORDER_NO = "orderNo";

    /**
     * 支付页面URL字段名
     * <p>
     * 支付接口返回的OneVisionPay收银台链接。
     * 字段类型：String
     * 示例值：https://checkout.onevisionpay.com/...
     * </p>
     */
    public static final String FIELD_PAYMENT_PAGE_URL = "paymentPageUrl";

    // ========== 积分中心模块工具方法 ==========

    /**
     * 检查是否为积分中心相关的API路径
     * <p>
     * 判断给定的API路径是否属于积分中心模块。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是积分中心相关API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isPoints = isPointsApi("/app/pointsCenter/pointsPackageList");     // true
     * boolean isPoints = isPointsApi("/app/customer/info");                      // false
     * </pre>
     */
    public static boolean isPointsApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }

        return apiPath.equals(API_GET_POINTS_PACKAGE_LIST) ||
               apiPath.equals(API_GET_POINTS_SERIAL_LIST) ||
               apiPath.equals(API_POINTS_BUY);
    }

    /**
     * 检查积分套餐是否有效
     * <p>
     * 根据isDelete字段判断积分套餐是否有效。
     * </p>
     *
     * @param isDelete 删除状态
     * @return 如果套餐有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isPackageValid(1);     // true (正常状态)
     * boolean valid = isPackageValid(0);     // false (已删除)
     * </pre>
     */
    public static boolean isPackageValid(int isDelete) {
        return isDelete == PACKAGE_STATUS_NORMAL;
    }

    /**
     * 计算套餐总积分
     * <p>
     * 计算基础积分和赠送积分的总和。
     * </p>
     *
     * @param points 基础积分
     * @param giftPoints 赠送积分
     * @return 总积分数量
     *
     * @example
     * <pre>
     * int total = calculateTotalPoints(500, 50);  // 550
     * int total = calculateTotalPoints(100, 0);   // 100
     * </pre>
     */
    public static int calculateTotalPoints(int points, int giftPoints) {
        return points + giftPoints;
    }

    /**
     * 格式化价格显示
     * <p>
     * 将价格格式化为货币显示格式。
     * </p>
     *
     * @param price 价格
     * @return 格式化后的价格字符串
     *
     * @example
     * <pre>
     * String formatted = formatPrice(9.99);   // "$9.99"
     * String formatted = formatPrice(10.0);   // "$10.00"
     * </pre>
     */
    public static String formatPrice(double price) {
        return String.format("$%.2f", price);
    }

    /**
     * 获取套餐状态描述
     * <p>
     * 根据isDelete字段返回对应的中文描述。
     * </p>
     *
     * @param isDelete 删除状态
     * @return 状态描述
     *
     * @example
     * <pre>
     * String desc = getPackageStatusDescription(1);  // "正常"
     * String desc = getPackageStatusDescription(0);  // "已删除"
     * </pre>
     */
    public static String getPackageStatusDescription(int isDelete) {
        switch (isDelete) {
            case PACKAGE_STATUS_NORMAL:
                return "正常";
            case PACKAGE_STATUS_DELETED:
                return "已删除";
            default:
                return "未知状态";
        }
    }

    // ========== 积分套餐支付工具方法 ==========

    /**
     * 验证支付参数
     * <p>
     * 验证积分套餐支付所需的所有参数是否有效。
     * </p>
     *
     * @param packageId 套餐ID
     * @param payAmount 支付金额
     * @param payType 支付方式
     * @return 如果所有参数都有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = validatePaymentParams("07649e1a1072402f8366e2a2019dfe4f", 9.99, "ecom");  // true
     * boolean valid = validatePaymentParams("", 0, "invalid");                                   // false
     * </pre>
     */
    public static boolean validatePaymentParams(String packageId, double payAmount, String payType) {
        return packageId != null && !packageId.trim().isEmpty() &&
               isValidPayAmount(payAmount) &&
               isValidPayType(payType);
    }

    /**
     * 验证支付金额是否有效
     * <p>
     * 检查支付金额是否大于0。
     * </p>
     *
     * @param payAmount 支付金额
     * @return 如果金额有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidPayAmount(9.99);   // true
     * boolean valid = isValidPayAmount(0);      // false
     * boolean valid = isValidPayAmount(-1);     // false
     * </pre>
     */
    public static boolean isValidPayAmount(double payAmount) {
        return payAmount > 0;
    }

    /**
     * 验证支付方式是否有效
     * <p>
     * 检查支付方式是否为支持的类型。
     * </p>
     *
     * @param payType 支付方式
     * @return 如果支付方式有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidPayType("ecom");     // true
     * boolean valid = isValidPayType("mc");       // true
     * boolean valid = isValidPayType("invalid");  // false
     * </pre>
     */
    public static boolean isValidPayType(String payType) {
        return PAY_TYPE_ECOM.equals(payType) || PAY_TYPE_MOBILE_COMMERCE.equals(payType);
    }

    /**
     * 获取支付方式的中文描述
     * <p>
     * 根据支付方式代码返回对应的中文描述。
     * </p>
     *
     * @param payType 支付方式代码
     * @return 支付方式的中文描述
     *
     * @example
     * <pre>
     * String desc = getPayTypeDescription("ecom");     // "支付卡"
     * String desc = getPayTypeDescription("mc");       // "移动支付"
     * String desc = getPayTypeDescription("invalid");  // "未知支付方式"
     * </pre>
     */
    public static String getPayTypeDescription(String payType) {
        if (PAY_TYPE_ECOM.equals(payType)) {
            return "支付卡";
        } else if (PAY_TYPE_MOBILE_COMMERCE.equals(payType)) {
            return "移动支付";
        } else {
            return "未知支付方式";
        }
    }
}
