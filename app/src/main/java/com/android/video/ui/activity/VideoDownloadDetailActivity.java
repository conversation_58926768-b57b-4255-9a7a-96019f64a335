package com.android.video.ui.activity;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.model.Episode;
import com.android.video.model.DownloadVideo;
import com.android.video.model.response.DownloadChapterResponseModel;
import com.android.video.model.response.DeleteChapterResponseModel;
import com.android.video.network.DownloadChapterApiService;
import com.android.video.ui.adapter.EpisodeAdapter;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频下载详情页面Activity - 集成真实API数据
 * <AUTHOR> Team
 */
public class VideoDownloadDetailActivity extends BaseFullScreenActivity implements EpisodeAdapter.OnEpisodeClickListener {

    private static final String TAG = "VideoDownloadDetailActivity";
    private static final int PAGE_SIZE = 10;

    public static final String EXTRA_VIDEO_ID = "video_id";
    public static final String EXTRA_VIDEO_TITLE = "video_title";
    public static final String EXTRA_DOWNLOADED_COUNT = "downloaded_count";
    public static final String EXTRA_TOTAL_COUNT = "total_count";
    public static final String EXTRA_TOTAL_FILE_SIZE = "total_file_size";
    public static final String EXTRA_FILM_LANGUAGE_INFO_ID = "film_language_info_id";

    private ImageView ivBack;
    private TextView tvTitle;
    private ImageView ivPoster;
    private TextView tvVideoTitle;
    private TextView tvDownloadedStatus;
    private TextView tvDownloadedCount;
    private TextView tvTotalCount;
    private TextView tvTotalFileSize;
    private LinearLayout btnPlay;
    private LinearLayout llEpisodeProgress;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView rvEpisodes;

    private EpisodeAdapter adapter;
    private List<Episode> episodes;
    private DownloadChapterApiService apiService;

    private String videoId;
    private String videoTitle;
    private String filmLanguageInfoId;
    private int downloadedCount;
    private int totalCount;
    private String totalFileSize;

    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_download_detail);

        getIntentData();
        initViews();
        initData();
        setupRecyclerView();
        setupClickListeners();
        setupSwipeRefresh();

        // 加载真实数据
        if (filmLanguageInfoId != null && !filmLanguageInfoId.trim().isEmpty()) {
            loadChapterDetails(true);
        } else {
            showError("缺少必要参数：filmLanguageInfoId");
        }
    }

    private void getIntentData() {
        Intent intent = getIntent();
        videoId = intent.getStringExtra(EXTRA_VIDEO_ID);
        videoTitle = intent.getStringExtra(EXTRA_VIDEO_TITLE);
        filmLanguageInfoId = intent.getStringExtra(EXTRA_FILM_LANGUAGE_INFO_ID);
        downloadedCount = intent.getIntExtra(EXTRA_DOWNLOADED_COUNT, 0);
        totalCount = intent.getIntExtra(EXTRA_TOTAL_COUNT, 0);
        totalFileSize = intent.getStringExtra(EXTRA_TOTAL_FILE_SIZE);

        // 设置默认值
        if (videoTitle == null) videoTitle = "Unknown Video";
        if (totalFileSize == null) totalFileSize = "0 MB";

        Log.d(TAG, "Intent数据: videoId=" + videoId + ", videoTitle=" + videoTitle +
                   ", filmLanguageInfoId=" + filmLanguageInfoId);
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        ivPoster = findViewById(R.id.iv_poster);
        tvVideoTitle = findViewById(R.id.tv_video_title);
        tvDownloadedStatus = findViewById(R.id.tv_downloaded_status);
        tvDownloadedCount = findViewById(R.id.tv_downloaded_count);
        tvTotalCount = findViewById(R.id.tv_total_count);
        tvTotalFileSize = findViewById(R.id.tv_total_file_size);
        btnPlay = findViewById(R.id.btn_play);
        llEpisodeProgress = findViewById(R.id.ll_episode_progress);
        // swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout); // 布局中暂无SwipeRefreshLayout
        rvEpisodes = findViewById(R.id.rv_episodes);
    }

    private void initData() {
        episodes = new ArrayList<>();
        apiService = DownloadChapterApiService.getInstance();
    }

    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setColorSchemeColors(
                getResources().getColor(android.R.color.white, null)
            );
            swipeRefreshLayout.setProgressBackgroundColorSchemeColor(
                getResources().getColor(android.R.color.black, null)
            );

            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "下拉刷新触发");
                refreshData();
            });
        }
    }

    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvEpisodes.setLayoutManager(layoutManager);

        adapter = new EpisodeAdapter(episodes, this);
        rvEpisodes.setAdapter(adapter);
    }

    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());

        btnPlay.setOnClickListener(v -> {
            // 创建DownloadVideo对象并跳转到下载视频播放页面
            DownloadVideo downloadVideo = createDownloadVideoFromCurrentData();
            Intent intent = new Intent(this, DownloadVideoPlayerActivity.class);
            intent.putExtra(DownloadVideoPlayerActivity.EXTRA_DOWNLOAD_VIDEO, downloadVideo);
            startActivity(intent);
        });

        // 设置集数进度容器点击事件（滚动到集数列表）
        llEpisodeProgress.setOnClickListener(v -> {
            // 平滑滚动到集数列表顶部
            rvEpisodes.smoothScrollToPosition(0);
        });
    }

    private void updateUI() {
        // 设置视频标题
        if (videoTitle != null && !videoTitle.trim().isEmpty()) {
            tvVideoTitle.setText(videoTitle);
        }

        // 设置下载状态
        tvDownloadedStatus.setText("Downloaded/Total episodes");

        // 设置下载进度（EP.格式）
        tvDownloadedCount.setText("EP." + downloadedCount);
        tvTotalCount.setText("EP." + totalCount);

        // 设置总文件大小
        if (totalFileSize != null && !totalFileSize.trim().isEmpty()) {
            tvTotalFileSize.setText(totalFileSize);
        }

        // 不设置默认海报图片，等待API数据或使用空白
        // ivPoster.setImageResource(R.drawable.movie_poster);
    }

    @Override
    public void onPlayClick(Episode episode) {
        // 创建DownloadVideo对象并跳转到下载视频播放页面
        DownloadVideo downloadVideo = createDownloadVideoFromCurrentData();
        Intent intent = new Intent(this, DownloadVideoPlayerActivity.class);
        intent.putExtra(DownloadVideoPlayerActivity.EXTRA_DOWNLOAD_VIDEO, downloadVideo);
        startActivity(intent);
    }

    @Override
    public void onDeleteClick(Episode episode) {
        showDeleteConfirmDialog(episode);
    }

    @Override
    public void onEpisodeClick(Episode episode) {
        Toast.makeText(this, "Episode info: " + episode.getEpisodeNumber(), Toast.LENGTH_SHORT).show();
        // TODO: 显示剧集详情或直接播放
    }

    private void showDeleteConfirmDialog(Episode episode) {
        new AlertDialog.Builder(this)
            .setTitle("Delete Episode")
            .setMessage("Are you sure you want to delete \"" + episode.getEpisodeNumber() + "\"?")
            .setPositiveButton("Delete", (dialog, which) -> {
                deleteEpisode(episode);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void deleteEpisode(Episode episode) {
        // 检查是否有downloadRecordId
        if (episode.getDownloadRecordId() == null || episode.getDownloadRecordId().trim().isEmpty()) {
            showError("无法删除：缺少下载记录ID");
            return;
        }

        Log.d(TAG, "开始删除章节，downloadRecordId: " + episode.getDownloadRecordId());

        // 调用API删除章节
        apiService.deleteDownloadChapter(episode.getDownloadRecordId(),
            new DownloadChapterApiService.DeleteChapterCallback() {
                @Override
                public void onSuccess(DeleteChapterResponseModel response) {
                    Log.d(TAG, "删除章节成功: " + response.toString());

                    if (response.isSuccess() && response.isDeleteSuccess()) {
                        // API删除成功，更新本地数据
                        int position = episodes.indexOf(episode);
                        if (position != -1) {
                            // TODO: 删除本地文件
                            // File file = new File(episode.getFilePath());
                            // if (file.exists()) {
                            //     file.delete();
                            // }

                            adapter.removeEpisode(position);
                            downloadedCount--;

                            // 更新UI（EP.格式）
                            tvDownloadedCount.setText("EP." + downloadedCount);

                            Toast.makeText(VideoDownloadDetailActivity.this,
                                "\"" + episode.getEpisodeNumber() + "\" deleted", Toast.LENGTH_SHORT).show();

                            // 如果没有剧集了，返回上一页
                            if (episodes.isEmpty()) {
                                finish();
                            }
                        }
                    } else {
                        showError("删除失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "删除章节失败: " + errorMessage);
                    showError("删除失败: " + errorMessage);
                }
            });
    }

    /**
     * 刷新数据（下拉刷新）
     */
    private void refreshData() {
        currentPage = 1;
        hasMoreData = true;
        loadChapterDetails(true);
    }

    /**
     * 加载更多数据（上拉加载）
     */
    private void loadMoreData() {
        if (hasMoreData && !isLoading) {
            currentPage++;
            loadChapterDetails(false);
        }
    }

    /**
     * 加载章节详情数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadChapterDetails(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            showError("filmLanguageInfoId参数缺失");
            return;
        }

        isLoading = true;

        // 显示加载状态
        if (isRefresh && swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }

        Log.d(TAG, "开始加载章节详情，filmLanguageInfoId: " + filmLanguageInfoId +
                   ", 页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        apiService.getDownloadChapterDetails(filmLanguageInfoId, currentPage, PAGE_SIZE,
            new DownloadChapterApiService.DownloadChapterCallback() {
                @Override
                public void onSuccess(DownloadChapterResponseModel response) {
                    isLoading = false;
                    if (swipeRefreshLayout != null) {
                        swipeRefreshLayout.setRefreshing(false);
                    }

                    Log.d(TAG, "章节详情加载成功: " + response.toString());

                    if (response.isSuccess() && response.getData() != null) {
                        List<Episode> newEpisodes = response.getData().toEpisodeList();

                        if (isRefresh) {
                            // 刷新时清空现有数据
                            episodes.clear();
                        }

                        // 添加新数据
                        episodes.addAll(newEpisodes);

                        // 更新分页状态
                        hasMoreData = response.getData().hasMorePages();

                        // 更新统计信息
                        if (response.getData().getTotal() > 0) {
                            totalCount = response.getData().getTotal();
                        }
                        downloadedCount = response.getData().getDownloadedCount();

                        // 计算总文件大小
                        long totalBytes = response.getData().getTotalFileSize();
                        if (totalBytes > 0) {
                            totalFileSize = formatFileSize(totalBytes);
                        }

                        // 更新UI
                        adapter.notifyDataSetChanged();
                        updateUI();

                        Log.d(TAG, "数据更新完成，当前总数: " + episodes.size() +
                                   ", 是否还有更多: " + hasMoreData);
                    } else {
                        showError("获取数据失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    isLoading = false;
                    if (swipeRefreshLayout != null) {
                        swipeRefreshLayout.setRefreshing(false);
                    }

                    Log.e(TAG, "章节详情加载失败: " + errorMessage);
                    showError("加载失败: " + errorMessage);
                }
            });
    }

    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes <= 0) return "0 B";

        final String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(bytes) / Math.log10(1024));

        if (digitGroups >= units.length) {
            digitGroups = units.length - 1;
        }

        double size = bytes / Math.pow(1024, digitGroups);
        return String.format("%.1f %s", size, units[digitGroups]);
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 从当前数据创建DownloadVideo对象
     */
    private DownloadVideo createDownloadVideoFromCurrentData() {
        DownloadVideo downloadVideo = new DownloadVideo();
        downloadVideo.setId(videoId != null ? videoId : "unknown");
        downloadVideo.setTitle(videoTitle != null ? videoTitle : "Unknown Video");
        downloadVideo.setPosterUrl(""); // 设置海报URL为空
        downloadVideo.setDownloadedEpisodes(downloadedCount);
        downloadVideo.setTotalEpisodes(totalCount);
        downloadVideo.setDownloadDate(""); // 设置下载日期为空

        // 尝试解析文件大小
        if (totalFileSize != null && !totalFileSize.trim().isEmpty()) {
            try {
                // 简单的文件大小解析，假设格式为 "123.4 MB"
                String sizeStr = totalFileSize.replaceAll("[^0-9.]", "");
                if (!sizeStr.isEmpty()) {
                    double size = Double.parseDouble(sizeStr);
                    if (totalFileSize.contains("GB")) {
                        downloadVideo.setFileSize((long) (size * 1024 * 1024 * 1024));
                    } else if (totalFileSize.contains("MB")) {
                        downloadVideo.setFileSize((long) (size * 1024 * 1024));
                    } else if (totalFileSize.contains("KB")) {
                        downloadVideo.setFileSize((long) (size * 1024));
                    } else {
                        downloadVideo.setFileSize((long) size);
                    }
                }
            } catch (NumberFormatException e) {
                downloadVideo.setFileSize(0);
            }
        } else {
            downloadVideo.setFileSize(0);
        }

        downloadVideo.setFilePath(""); // 设置文件路径为空
        downloadVideo.setDownloading(false);
        downloadVideo.setDownloadProgress(100); // 假设已下载完成

        return downloadVideo;
    }
}
