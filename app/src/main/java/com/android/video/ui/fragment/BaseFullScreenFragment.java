package com.android.video.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.fragment.app.Fragment;
import com.android.video.manager.ContentProtectionManager;
import com.android.video.utils.StatusBarUtils;

/**
 * 全屏Fragment基类 - 统一处理状态栏渲染和WindowInsets
 * 解决状态栏黑色区域、内容重叠等问题，确保在不同Android版本和设备上都能正确显示
 * 与BaseFullScreenActivity保持一致的全屏渲染功能
 * 
 * <AUTHOR> Team
 */
public abstract class BaseFullScreenFragment extends Fragment {

    private static final String TAG = "BaseFullScreenFragment";

    /**
     * 设置为true启用调试
     */
    private static final boolean DEBUG_STATUS_BAR = false;

    // 内容保护相关
    private ContentProtectionManager protectionManager;
    private boolean contentProtectionEnabled = false;

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 设置全屏显示
        setupFullScreen();

        // 设置WindowInsets处理
        setupWindowInsets(view);

        // 可选启用内容保护
        if (shouldEnableContentProtection()) {
            enableContentProtection();
        }

        // 调试状态栏配置（仅在DEBUG模式下）
        if (DEBUG_STATUS_BAR) {
            debugStatusBarConfiguration();
        }
    }

    /**
     * 设置全屏显示和状态栏透明
     * 使用现代的WindowInsetsController API，兼容Android 6.0+
     */
    private void setupFullScreen() {
        if (getActivity() != null && getActivity().getWindow() != null) {
            // 告诉系统我们要处理WindowInsets
            WindowCompat.setDecorFitsSystemWindows(getActivity().getWindow(), false);

            // 设置状态栏和导航栏透明
            getActivity().getWindow().setStatusBarColor(android.graphics.Color.TRANSPARENT);
            getActivity().getWindow().setNavigationBarColor(android.graphics.Color.TRANSPARENT);

            // 获取WindowInsetsController并设置系统栏行为
            WindowInsetsControllerCompat windowInsetsController =
                WindowCompat.getInsetsController(getActivity().getWindow(), getActivity().getWindow().getDecorView());

            if (windowInsetsController != null) {
                // 设置系统栏行为为粘性沉浸式
                windowInsetsController.setSystemBarsBehavior(
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                );
            }
        }
    }

    /**
     * 设置WindowInsets处理，确保内容正确显示在状态栏下方
     * 优化后的实现：不推下整个内容，而是让布局自然延伸到状态栏区域
     * 同时处理底部导航栏的检测和适配
     */
    private void setupWindowInsets(View rootView) {
        if (rootView != null) {
            ViewCompat.setOnApplyWindowInsetsListener(rootView, (v, insets) -> {
                Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());

                // 不设置padding，让内容自然延伸到状态栏区域
                // 这样背景可以完全覆盖状态栏，避免黑色区域
                // 具体的内容定位由布局中的Guideline和约束来控制

                // 调用原有的WindowInsets处理方法
                onApplyWindowInsets(insets);

                // 获取导航栏信息并调用新的处理方法
                int navigationBarHeight = getNavigationBarHeight();
                boolean hasNavigationBar = hasNavigationBar();
                boolean isGestureNavigation = isGestureNavigation();

                // 调用导航栏专用的处理方法
                onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

                return insets; // 返回原始insets，不消费
            });
        }
    }

    @Override
    public void onResume() {
        super.onResume();
        // 重新确保全屏状态，处理系统栏可能重新出现的情况
        ensureFullScreenState();
    }

    /**
     * 确保全屏状态保持
     */
    private void ensureFullScreenState() {
        if (getActivity() != null && getActivity().getWindow() != null) {
            WindowInsetsControllerCompat windowInsetsController =
                WindowCompat.getInsetsController(getActivity().getWindow(), getActivity().getWindow().getDecorView());

            if (windowInsetsController != null) {
                // 重新设置系统栏行为
                windowInsetsController.setSystemBarsBehavior(
                    WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                );
            }
        }
    }

    /**
     * 获取状态栏高度，供子类使用
     * @return 状态栏高度（像素）
     */
    protected int getStatusBarHeight() {
        View rootView = getView();
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                return insets.getInsets(WindowInsetsCompat.Type.systemBars()).top;
            }
        }

        // 备用方法：通过系统资源获取
        if (getContext() != null) {
            int resourceId = getContext().getResources().getIdentifier("status_bar_height", "dimen", "android");
            if (resourceId > 0) {
                return getContext().getResources().getDimensionPixelSize(resourceId);
            }
        }

        return 0;
    }

    /**
     * 获取底部导航栏高度，供子类使用
     * @return 导航栏高度（像素），如果不存在导航栏则返回0
     */
    protected int getNavigationBarHeight() {
        View rootView = getView();
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                return insets.getInsets(WindowInsetsCompat.Type.navigationBars()).bottom;
            }
        }

        // 备用方法：通过系统资源获取（仅在有物理导航栏的设备上有效）
        if (getContext() != null) {
            int resourceId = getContext().getResources().getIdentifier("navigation_bar_height", "dimen", "android");
            if (resourceId > 0) {
                return getContext().getResources().getDimensionPixelSize(resourceId);
            }
        }

        return 0;
    }

    /**
     * 检测是否存在底部导航栏
     * @return true表示存在导航栏，false表示不存在
     */
    protected boolean hasNavigationBar() {
        return getNavigationBarHeight() > 0;
    }

    /**
     * 检测是否为手势导航模式
     * 通过比较navigationBars和tappableElement的高度来判断
     * @return true表示手势导航，false表示按钮导航或无导航栏
     */
    protected boolean isGestureNavigation() {
        View rootView = getView();
        if (rootView != null) {
            WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
            if (insets != null) {
                Insets navBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars());
                Insets tappable = insets.getInsets(WindowInsetsCompat.Type.tappableElement());

                // 如果导航栏高度大于可点击区域高度，通常表示手势导航
                // 手势导航的导航栏区域更大，但可点击区域较小
                return navBars.bottom > 0 && navBars.bottom > tappable.bottom;
            }
        }
        return false;
    }

    /**
     * 子类可以重写此方法来处理特定的WindowInsets需求
     * @param insets WindowInsets对象
     */
    protected void onApplyWindowInsets(WindowInsetsCompat insets) {
        // 默认实现为空，子类可以重写
    }

    /**
     * 子类可以重写此方法来处理导航栏相关的布局调整
     * @param insets WindowInsets对象
     * @param navigationBarHeight 导航栏高度
     * @param hasNavigationBar 是否存在导航栏
     * @param isGestureNavigation 是否为手势导航
     */
    protected void onApplyNavigationBarInsets(WindowInsetsCompat insets, int navigationBarHeight,
                                            boolean hasNavigationBar, boolean isGestureNavigation) {
        // 默认实现为空，子类可以重写来处理导航栏适配
        // 例如：调整底部内容的padding或margin，避免被导航栏遮挡
    }

    /**
     * 调试状态栏配置（仅在DEBUG模式下使用）
     */
    private void debugStatusBarConfiguration() {
        if (getView() != null && getActivity() != null) {
            // 延迟执行，确保布局已完成
            getView().post(() -> {
                StatusBarUtils.debugStatusBarConfig(getActivity());
                StatusBarUtils.StatusBarValidationResult result =
                    StatusBarUtils.validateStatusBarRendering(getActivity());
                android.util.Log.d("BaseFullScreenFragment", result.toString());
            });
        }
    }

    /**
     * 隐藏ActionBar（如果存在）
     */
    protected void hideActionBar() {
        if (getActivity() != null && 
            getActivity() instanceof androidx.appcompat.app.AppCompatActivity) {
            androidx.appcompat.app.AppCompatActivity activity = 
                (androidx.appcompat.app.AppCompatActivity) getActivity();
            if (activity.getSupportActionBar() != null) {
                activity.getSupportActionBar().hide();
            }
        }
    }

    /**
     * 检查Fragment是否需要全屏显示
     * 子类可以重写此方法来控制是否启用全屏功能
     * @return true表示需要全屏显示，false表示使用默认显示
     */
    protected boolean shouldEnableFullScreen() {
        return true;
    }

    // ==================== 内容保护功能 ====================

    /**
     * 子类重写此方法决定是否启用内容保护
     * 默认返回false，子类可以根据需要重写返回true
     *
     * @return true表示启用内容保护，false表示不启用
     */
    protected boolean shouldEnableContentProtection() {
        return false; // 默认不启用，子类重写决定是否启用
    }

    /**
     * 启用内容保护功能
     * 包括防录屏、防截图等功能
     */
    protected void enableContentProtection() {
        if (contentProtectionEnabled) {
            Log.w(TAG, "Content protection already enabled");
            return;
        }

        if (getActivity() == null) {
            Log.w(TAG, "Activity is null, cannot enable content protection");
            return;
        }

        try {
            protectionManager = ContentProtectionManager.getInstance();
            protectionManager.enableProtection(getActivity());
            contentProtectionEnabled = true;

            Log.d(TAG, "Content protection enabled for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to enable content protection", e);
        }
    }

    /**
     * 禁用内容保护功能
     */
    protected void disableContentProtection() {
        if (!contentProtectionEnabled) {
            return;
        }

        try {
            if (protectionManager != null) {
                protectionManager.disableProtection();
                protectionManager = null;
            }
            contentProtectionEnabled = false;

            Log.d(TAG, "Content protection disabled for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Failed to disable content protection", e);
        }
    }

    /**
     * 检查内容保护是否已启用
     *
     * @return true表示已启用，false表示未启用
     */
    protected boolean isContentProtectionEnabled() {
        return contentProtectionEnabled;
    }

    /**
     * 获取内容保护管理器实例
     * 仅在内容保护已启用时返回有效实例
     *
     * @return ContentProtectionManager实例，如果未启用则返回null
     */
    protected ContentProtectionManager getContentProtectionManager() {
        return contentProtectionEnabled ? protectionManager : null;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 清理内容保护资源
        disableContentProtection();
    }
}
