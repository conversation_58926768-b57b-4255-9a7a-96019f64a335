package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 我的喜爱列表项数据模型
 * <AUTHOR> Team
 */
public class MyLoveItemModel {
    
    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 总章节数
     */
    @SerializedName("totalChaptersNum")
    private int totalChaptersNum;
    
    /**
     * 已观看章节数
     */
    @SerializedName("watchedChaptersNum")
    private int watchedChaptersNum;
    
    /**
     * 是否已关注/喜欢
     */
    @SerializedName("isLoved")
    private boolean isLoved;
    
    /**
     * 添加到喜爱列表的时间
     */
    @SerializedName("addTime")
    private String addTime;

    public MyLoveItemModel() {
    }

    public MyLoveItemModel(String filmId, String filmTitle, String cover, String filmLanguageInfoId, 
                          int totalChaptersNum, int watchedChaptersNum, boolean isLoved, String addTime) {
        this.filmId = filmId;
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.totalChaptersNum = totalChaptersNum;
        this.watchedChaptersNum = watchedChaptersNum;
        this.isLoved = isLoved;
        this.addTime = addTime;
    }

    // Getters and Setters
    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public int getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(int totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    public int getWatchedChaptersNum() {
        return watchedChaptersNum;
    }

    public void setWatchedChaptersNum(int watchedChaptersNum) {
        this.watchedChaptersNum = watchedChaptersNum;
    }

    public boolean isLoved() {
        return isLoved;
    }

    public void setLoved(boolean loved) {
        isLoved = loved;
    }

    public String getAddTime() {
        return addTime;
    }

    public void setAddTime(String addTime) {
        this.addTime = addTime;
    }

    /**
     * 获取观看进度百分比
     * @return 观看进度百分比 (0-100)
     */
    public int getWatchProgress() {
        if (totalChaptersNum <= 0) {
            return 0;
        }
        return (int) ((watchedChaptersNum * 100.0) / totalChaptersNum);
    }

    /**
     * 检查是否已完成观看
     * @return 是否已完成观看
     */
    public boolean isCompleted() {
        return watchedChaptersNum >= totalChaptersNum && totalChaptersNum > 0;
    }

    /**
     * 转换为MyListVideo模型
     * @return MyListVideo对象
     */
    public MyListVideo toMyListVideo() {
        MyListVideo video = new MyListVideo();
        video.setId(filmId);
        video.setTitle(filmTitle != null ? filmTitle : "");
        video.setPosterUrl(cover != null ? cover : "");
        video.setLiked(isLoved);
        video.setCategory("following"); // Following标签页类别
        video.setDescription(""); // API未提供描述信息
        video.setReleaseDate(addTime != null ? addTime : "");
        video.setCurrentEpisode(watchedChaptersNum);
        video.setTotalEpisodes(totalChaptersNum);
        video.setDownloadedEpisodes(0); // Following列表不涉及下载
        video.setSubscribed(isLoved); // 使用isLoved作为订阅状态
        video.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");
        return video;
    }

    @Override
    public String toString() {
        return "MyLoveItemModel{" +
                "filmId='" + filmId + '\'' +
                ", filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", totalChaptersNum=" + totalChaptersNum +
                ", watchedChaptersNum=" + watchedChaptersNum +
                ", isLoved=" + isLoved +
                ", addTime='" + addTime + '\'' +
                ", watchProgress=" + getWatchProgress() + "%" +
                ", isCompleted=" + isCompleted() +
                '}';
    }
}
