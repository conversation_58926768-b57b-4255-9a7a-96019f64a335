package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.model.PointsPurchase;

import java.util.List;

/**
 * Points purchase记录适配器
 */
public class PointsPurchaseAdapter extends RecyclerView.Adapter<PointsPurchaseAdapter.ViewHolder> {

    private List<PointsPurchase> pointsPurchaseList;

    public PointsPurchaseAdapter(List<PointsPurchase> pointsPurchaseList) {
        this.pointsPurchaseList = pointsPurchaseList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_points_purchase, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        PointsPurchase pointsPurchase = pointsPurchaseList.get(position);
        holder.bind(pointsPurchase);
    }

    @Override
    public int getItemCount() {
        return pointsPurchaseList != null ? pointsPurchaseList.size() : 0;
    }

    /**
     * 更新数据
     */
    public void updateData(List<PointsPurchase> newData) {
        this.pointsPurchaseList = newData;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tvPackageName;
        private TextView tvPrice;
        private TextView tvDateTime;
        private TextView tvPoints;
        private TextView tvBonusPoints;
        private ImageView ivPointsIcon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvPackageName = itemView.findViewById(R.id.tv_package_name);
            tvPrice = itemView.findViewById(R.id.tv_price);
            tvDateTime = itemView.findViewById(R.id.tv_date_time);
            tvPoints = itemView.findViewById(R.id.tv_points);
            tvBonusPoints = itemView.findViewById(R.id.tv_bonus_points);
            ivPointsIcon = itemView.findViewById(R.id.iv_points_icon);
        }

        /**
         * 绑定数据
         */
        public void bind(PointsPurchase pointsPurchase) {
            if (pointsPurchase == null) {
                return;
            }

            tvPackageName.setText(pointsPurchase.getPackageName());
            tvPrice.setText(pointsPurchase.getPrice());
            tvDateTime.setText(pointsPurchase.getDateTime());
            tvPoints.setText(String.valueOf(pointsPurchase.getPoints()));

            // 格式化赠送积分显示
            if (pointsPurchase.getBonusPoints() > 0) {
                tvBonusPoints.setText("+" + pointsPurchase.getBonusPoints() + "gift");
                tvBonusPoints.setVisibility(View.VISIBLE);
            } else {
                tvBonusPoints.setVisibility(View.GONE);
            }
        }
    }
}
