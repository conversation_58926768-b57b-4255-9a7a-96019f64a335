package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.graphics.Canvas;
import android.graphics.Paint;
import android.text.Layout;
import android.text.SpannableString;
import android.text.style.LeadingMarginSpan;
import android.widget.ImageView;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;

import com.android.video.R;
import com.android.video.model.UserModel;
import com.android.video.utils.UserSessionUtils;
import com.android.video.ui.activity.LoginActivity;
import com.android.video.network.ProfileApiService;

public class LogOffActivity extends BaseFullScreenActivity {

    private ImageView ivBack;
    private ImageView ivCheckbox;
    private TextView tvConfirm;
    private FrameLayout layoutCheckbox;
    private TextView btnDeleteAccount;

    private boolean isConfirmed = false;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            setContentView(R.layout.activity_log_off);
            initViews();
            setupClickListeners();
        } catch (Exception e) {
            e.printStackTrace();
            Toast.makeText(this, "Error loading page: " + e.getMessage(), Toast.LENGTH_LONG).show();
            finish();
        }
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        ivCheckbox = findViewById(R.id.iv_checkbox);
        tvConfirm = findViewById(R.id.tv_confirm);
        layoutCheckbox = findViewById(R.id.layout_checkbox);
        btnDeleteAccount = findViewById(R.id.btn_delete_account);

        // 设置首行缩进效果
        setupHangingIndent();
    }

    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // 复选框点击
        layoutCheckbox.setOnClickListener(v -> {
            toggleCheckbox();
        });

        // 删除账户按钮
        btnDeleteAccount.setOnClickListener(v -> {
            if (isConfirmed) {
                showDeleteConfirmDialog();
            } else {
                Toast.makeText(this, "Please confirm that you have read and understood the information", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 切换复选框状态
     */
    private void toggleCheckbox() {
        isConfirmed = !isConfirmed;

        if (isConfirmed) {
            // 选中状态
            ivCheckbox.setImageResource(R.drawable.ic_xuanze);
            btnDeleteAccount.setEnabled(true);
            btnDeleteAccount.setAlpha(1.0f);
        } else {
            // 未选中状态
            ivCheckbox.setImageResource(R.drawable.ic_xuanze0);
            btnDeleteAccount.setEnabled(false);
            btnDeleteAccount.setAlpha(0.5f);
        }
    }

    /**
     * 显示删除确认对话框
     */
    private void showDeleteConfirmDialog() {
        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("确认删除账户")
                .setMessage("此操作不可撤销，确定要删除账户吗？")
                .setPositiveButton("确定删除", (dialog, which) -> {
                    deleteAccount();
                })
                .setNegativeButton("取消", null)
                .show();
    }

    /**
     * 执行账户删除
     */
    private void deleteAccount() {
        try {
            // 显示加载状态
            btnDeleteAccount.setEnabled(false);
            btnDeleteAccount.setText("正在注销...");

            // 调用注销API
            ProfileApiService.getInstance().unsubscribe(new ProfileApiService.UnsubscribeCallback() {
                @Override
                public void onSuccess() {
                    // API调用成功，清除本地数据
                    try {
                        // 清除用户会话数据
                        UserSessionUtils.clearUserSession(LogOffActivity.this);

                        // 清除TokenManager中的token缓存（重要：确保完全清除token）
                        com.android.video.manager.TokenManager tokenManager =
                            com.android.video.manager.TokenManager.getInstance(LogOffActivity.this);
                        tokenManager.clearToken();

                        android.util.Log.d("LogOffActivity", "账户注销，已清除所有token缓存");

                        // 显示删除成功消息
                        Toast.makeText(LogOffActivity.this, "账户已成功注销", Toast.LENGTH_LONG).show();

                        // 跳转到登录页面
                        Intent intent = new Intent(LogOffActivity.this, LoginActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                        finish();

                    } catch (Exception e) {
                        Toast.makeText(LogOffActivity.this, "注销成功，但清理本地数据时发生错误", Toast.LENGTH_SHORT).show();
                        // 即使本地清理失败，也应该跳转到登录页面，因为服务器端已经注销成功
                        Intent intent = new Intent(LogOffActivity.this, LoginActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
                        startActivity(intent);
                        finish();
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    // API调用失败，恢复按钮状态
                    btnDeleteAccount.setEnabled(true);
                    btnDeleteAccount.setText("Delete Account");

                    // 显示错误消息
                    Toast.makeText(LogOffActivity.this, "注销失败: " + errorMessage, Toast.LENGTH_LONG).show();
                }
            });

        } catch (Exception e) {
            // 恢复按钮状态
            btnDeleteAccount.setEnabled(true);
            btnDeleteAccount.setText("Delete Account");
            Toast.makeText(this, "注销请求发送失败，请稍后重试", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 设置首行缩进效果，让第二行文本与复选框左边对齐
     */
    private void setupHangingIndent() {
        String text = "I confirm that I have read and understood the above information, and voluntarily waive all data, rights, assets, and services in my account";
        SpannableString spannableString = new SpannableString(text);

        // 获取复选框图标大小和间距的像素值
        int iconSize = getResources().getDimensionPixelSize(R.dimen.log_off_checkbox_icon_size);
        int iconMargin = getResources().getDimensionPixelSize(R.dimen.log_off_checkbox_icon_margin_end);
        int totalIndent = iconSize + iconMargin;

        // 使用标准的LeadingMarginSpan：第一行缩进到复选框右边，第二行不缩进
        LeadingMarginSpan.Standard leadingMarginSpan = new LeadingMarginSpan.Standard(totalIndent, 0);
        spannableString.setSpan(leadingMarginSpan, 0, text.length(), 0);

        tvConfirm.setText(spannableString);
    }

    /**
     * 自定义悬挂缩进Span，实现第一行不缩进，第二行及后续行缩进的效果
     */
    private static class HangingIndentSpan implements LeadingMarginSpan {
        private final int mMargin;

        public HangingIndentSpan(int margin) {
            mMargin = margin;
        }

        @Override
        public int getLeadingMargin(boolean first) {
            // 第一行不缩进，第二行及后续行向左缩进
            return first ? 0 : -mMargin;
        }

        @Override
        public void drawLeadingMargin(Canvas c, Paint p, int x, int dir, int top, int baseline, int bottom, CharSequence text, int start, int end, boolean first, Layout layout) {
            // 不需要绘制任何内容
        }
    }
}
