package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.TagModel;
import java.util.ArrayList;
import java.util.List;

/**
 * Synopsis标签RecyclerView适配器
 * 专用于视频详情页的Synopsis标签显示，使用独立的样式定义
 * <AUTHOR>
 */
public class SynopsisTagAdapter extends RecyclerView.Adapter<SynopsisTagAdapter.SynopsisTagViewHolder> {

    private List<TagModel> tagList;
    
    /**
     * 构造函数
     */
    public SynopsisTagAdapter() {
        this.tagList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param tagList 标签列表
     */
    public SynopsisTagAdapter(List<TagModel> tagList) {
        this.tagList = tagList != null ? tagList : new ArrayList<>();
    }
    
    /**
     * 更新标签列表
     * @param newTagList 新的标签列表
     */
    public void updateTagList(List<TagModel> newTagList) {
        this.tagList = newTagList != null ? newTagList : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 获取标签列表
     * @return 标签列表
     */
    public List<TagModel> getTagList() {
        return new ArrayList<>(tagList);
    }
    
    @NonNull
    @Override
    public SynopsisTagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_synopsis_tag, parent, false);
        return new SynopsisTagViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull SynopsisTagViewHolder holder, int position) {
        TagModel tag = tagList.get(position);
        holder.bind(tag, position);
    }
    
    @Override
    public int getItemCount() {
        return tagList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public static class SynopsisTagViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvTagName;
        
        public SynopsisTagViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTagName = itemView.findViewById(R.id.tv_tag_name);
        }
        
        /**
         * 绑定数据
         * @param tag 标签数据
         * @param position 位置
         */
        public void bind(TagModel tag, int position) {
            if (tag == null) {
                return;
            }
            
            // 设置标签名称
            tvTagName.setText(tag.getDisplayName());
            
            // Synopsis标签是只读的，不需要点击事件
            // 样式已在item_synopsis_tag.xml中定义，包括：
            // - 背景：synopsis_tag_background_new
            // - 文本颜色：#CCFFFFFF (80%透明度白色)
            // - 字体大小：15dp
            // - 不可点击：clickable="false", focusable="false"
        }
    }
}
