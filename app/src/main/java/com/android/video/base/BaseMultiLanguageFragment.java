package com.android.video.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.android.video.manager.LocalizationManager;
import com.android.video.callback.LocalizationCallback;
import com.android.video.ui.fragment.BaseFullScreenFragment;
import com.android.video.utils.GlobalTextUpdater;

/**
 * 多语言基础Fragment
 * 所有需要多语言支持的Fragment都应该继承此类
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public abstract class BaseMultiLanguageFragment extends BaseFullScreenFragment implements LocalizationCallback {
    
    private static final String TAG = "BaseMultiLanguageFragment";
    protected LocalizationManager localizationManager;
    private BroadcastReceiver languageChangeReceiver;
    
    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initLocalization();
        registerLanguageChangeReceiver();
    }
    
    /**
     * 初始化多语言功能
     */
    private void initLocalization() {
        if (getContext() == null) return;
        
        try {
            localizationManager = LocalizationManager.getInstance(getContext());
            localizationManager.setLanguageChangeListener(this);
            localizationManager.loadTranslations();
            Log.d(TAG, "Localization initialized for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error initializing localization", e);
        }
    }
    
    @Override
    public void onResume() {
        super.onResume();
        // 每次页面恢复时更新文本，确保语言切换后的一致性
        updateAllTexts();
    }
    
    /**
     * 更新所有界面文本
     * 子类可以重写此方法来添加特定的文本更新逻辑
     */
    protected void updateAllTexts() {
        if (getContext() == null || !isAdded()) return;
        
        try {
            GlobalTextUpdater.updateViewTexts(getView(), getContext());
            Log.d(TAG, "Updated all texts for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error updating texts", e);
        }
    }
    
    // LocalizationCallback接口实现
    
    @Override
    public void onLanguageChanged(int newLanguageType) {
        if (getContext() != null && isAdded()) {
            Log.d(TAG, "Language changed to type: " + newLanguageType + " in " + getClass().getSimpleName());
            updateAllTexts();
        }
    }
    
    @Override
    public void onTranslationLoaded() {
        if (getContext() != null && isAdded()) {
            Log.d(TAG, "Translation data loaded in " + getClass().getSimpleName());
            updateAllTexts();
        }
    }
    
    @Override
    public void onError(String errorMessage) {
        if (getContext() != null && isAdded()) {
            Log.e(TAG, "Localization error in " + getClass().getSimpleName() + ": " + errorMessage);
        }
    }
    
    /**
     * 注册语言切换广播接收器
     */
    private void registerLanguageChangeReceiver() {
        if (getContext() == null) return;

        languageChangeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (LocalizationManager.ACTION_LANGUAGE_CHANGED.equals(intent.getAction())) {
                    int newLanguageType = intent.getIntExtra(LocalizationManager.EXTRA_LANGUAGE_TYPE, 1);
                    Log.d(TAG, "收到语言切换广播: " + newLanguageType + " in " + getClass().getSimpleName());
                    if (getContext() != null && isAdded()) {
                        updateAllTexts();
                    }
                }
            }
        };

        IntentFilter filter = new IntentFilter(LocalizationManager.ACTION_LANGUAGE_CHANGED);
        LocalBroadcastManager.getInstance(getContext()).registerReceiver(languageChangeReceiver, filter);
        Log.d(TAG, "已注册语言切换广播接收器");
    }

    @Override
    public void onDestroyView() {
        // 清理LocalizationManager监听器，避免内存泄漏
        if (localizationManager != null) {
            localizationManager.setLanguageChangeListener(null);
        }

        // 注销广播接收器
        if (languageChangeReceiver != null && getContext() != null) {
            LocalBroadcastManager.getInstance(getContext()).unregisterReceiver(languageChangeReceiver);
        }

        super.onDestroyView();
    }
}
