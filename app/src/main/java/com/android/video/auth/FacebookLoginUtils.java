package com.android.video.auth;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.LoginResponseModel;
import com.facebook.AccessToken;
import com.facebook.CallbackManager;
import com.facebook.FacebookCallback;
import com.facebook.FacebookException;
import com.facebook.FacebookSdk;
import com.facebook.login.LoginManager;
import com.facebook.login.LoginResult;
import java.util.Arrays;

/**
 * Facebook登录工具类 - 处理Facebook SDK集成和登录流程
 * <AUTHOR> Team
 */
public class FacebookLoginUtils {

    private static final String TAG = "FacebookLoginUtils";
    private static CallbackManager callbackManager;
    private static boolean isInitialized = false;

    /**
     * Facebook登录回调接口
     */
    public interface FacebookLoginCallback {
        void onSuccess(LoginResponseModel loginResponse);
        void onError(String errorMessage);
        void onCancel();
    }

    /**
     * 初始化Facebook SDK
     * @param activity 当前Activity
     */
    public static void initialize(Activity activity) {
        if (isInitialized) {
            Log.d(TAG, "Facebook SDK already initialized");
            return;
        }

        try {
            // 初始化Facebook SDK
            FacebookSdk.sdkInitialize(activity.getApplicationContext());

            // 创建回调管理器
            callbackManager = CallbackManager.Factory.create();

            isInitialized = true;
            Log.d(TAG, "Facebook SDK initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize Facebook SDK: " + e.getMessage());
        }
    }

    /**
     * 开始Facebook登录
     * @param activity 当前Activity
     * @param callback 登录回调
     */
    public static void startLogin(Activity activity, FacebookLoginCallback callback) {
        Log.d(TAG, "Starting Facebook login");

        if (!isInitialized) {
            initialize(activity);
        }

        if (callbackManager == null) {
            Log.e(TAG, "CallbackManager is null, cannot start login");
            if (callback != null) {
                callback.onError("Facebook SDK not properly initialized");
            }
            return;
        }

        // 检查是否已经登录
        AccessToken accessToken = AccessToken.getCurrentAccessToken();
        if (accessToken != null && !accessToken.isExpired()) {
            Log.d(TAG, "User already logged in to Facebook");
            handleExistingToken(activity, accessToken, callback);
            return;
        }

        // 注册登录回调
        LoginManager.getInstance().registerCallback(callbackManager, new FacebookCallback<LoginResult>() {
            @Override
            public void onSuccess(LoginResult loginResult) {
                Log.d(TAG, "Facebook login successful");
                handleLoginSuccess(activity, loginResult, callback);
            }

            @Override
            public void onCancel() {
                Log.d(TAG, "Facebook login cancelled by user");
                if (callback != null) {
                    callback.onCancel();
                }
            }

            @Override
            public void onError(FacebookException error) {
                Log.e(TAG, "Facebook login error: " + error.getMessage());
                if (callback != null) {
                    callback.onError("Facebook login failed: " + error.getMessage());
                }
            }
        });

        // 开始登录流程
        try {
            LoginManager.getInstance().logInWithReadPermissions(
                    activity,
                    Arrays.asList("public_profile", "email")
            );
        } catch (Exception e) {
            Log.e(TAG, "Failed to start Facebook login: " + e.getMessage());
            if (callback != null) {
                callback.onError("Failed to start Facebook login: " + e.getMessage());
            }
        }
    }

    /**
     * 处理Activity结果
     * @param requestCode 请求码
     * @param resultCode 结果码
     * @param data Intent数据
     */
    public static void onActivityResult(int requestCode, int resultCode, Intent data) {
        if (callbackManager != null) {
            callbackManager.onActivityResult(requestCode, resultCode, data);
        }
    }

    /**
     * 处理现有Token
     * @param activity 当前Activity
     * @param accessToken 访问Token
     * @param callback 回调接口
     */
    private static void handleExistingToken(Activity activity, AccessToken accessToken, FacebookLoginCallback callback) {
        String authCode = accessToken.getToken();
        callBackendApi(activity, authCode, callback);
    }

    /**
     * 处理登录成功
     * @param activity 当前Activity
     * @param loginResult 登录结果
     * @param callback 回调接口
     */
    private static void handleLoginSuccess(Activity activity, LoginResult loginResult, FacebookLoginCallback callback) {
        AccessToken accessToken = loginResult.getAccessToken();
        if (accessToken != null) {
            String authCode = accessToken.getToken();
            callBackendApi(activity, authCode, callback);
        } else {
            Log.e(TAG, "Access token is null");
            if (callback != null) {
                callback.onError("Failed to get Facebook access token");
            }
        }
    }

    /**
     * 调用后端API完成登录
     * @param activity 当前Activity
     * @param authCode 授权码
     * @param callback 回调接口
     */
    private static void callBackendApi(Activity activity, String authCode, FacebookLoginCallback callback) {
        Log.d(TAG, "Calling backend API with Facebook auth code");

        AuthApiUtils.facebookLogin(activity, authCode, new AuthApiUtils.ApiCallback<LoginResponseModel>() {
            @Override
            public void onSuccess(LoginResponseModel loginResponse) {
                Log.d(TAG, "Backend Facebook login successful");
                if (callback != null) {
                    callback.onSuccess(loginResponse);
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Backend Facebook login failed: " + errorMessage);
                if (callback != null) {
                    callback.onError(errorMessage);
                }
            }
        });
    }

    /**
     * 登出Facebook
     */
    public static void logout() {
        try {
            LoginManager.getInstance().logOut();
            Log.d(TAG, "Facebook logout successful");
        } catch (Exception e) {
            Log.e(TAG, "Facebook logout error: " + e.getMessage());
        }
    }

    /**
     * 检查是否已登录
     * @return 是否已登录
     */
    public static boolean isLoggedIn() {
        AccessToken accessToken = AccessToken.getCurrentAccessToken();
        return accessToken != null && !accessToken.isExpired();
    }

    /**
     * 获取当前访问Token
     * @return 访问Token，如果未登录则返回null
     */
    public static AccessToken getCurrentAccessToken() {
        return AccessToken.getCurrentAccessToken();
    }

    /**
     * 获取Facebook登录状态信息（用于调试）
     * @return 状态信息字符串
     */
    public static String getLoginStatusInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Facebook Login Status:\n");
        info.append("SDK Initialized: ").append(isInitialized).append("\n");
        info.append("Logged In: ").append(isLoggedIn()).append("\n");

        AccessToken accessToken = getCurrentAccessToken();
        if (accessToken != null) {
            info.append("Token Expired: ").append(accessToken.isExpired()).append("\n");
            info.append("User ID: ").append(accessToken.getUserId()).append("\n");
        } else {
            info.append("No Access Token");
        }

        return info.toString();
    }
}