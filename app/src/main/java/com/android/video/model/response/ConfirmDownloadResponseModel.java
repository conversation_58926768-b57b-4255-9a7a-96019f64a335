package com.android.video.model.response;

import java.io.Serializable;

/**
 * 确认下载响应数据模型
 * <p>
 * 用于封装服务器对确认下载请求的响应数据。
 * 包含响应码、响应消息以及响应数据等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class ConfirmDownloadResponseModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应码
     * <p>
     * 服务器返回的状态码，用于标识请求处理结果。
     * 通常"200"表示成功，其他值表示不同类型的错误。
     * </p>
     */
    private String code;

    /**
     * 响应消息
     * <p>
     * 服务器返回的描述性消息，用于说明请求处理结果。
     * 成功时通常为"success"，失败时包含具体的错误描述。
     * </p>
     */
    private String message;

    /**
     * 响应数据
     * <p>
     * 服务器返回的具体数据内容。
     * 对于确认下载接口，成功时通常返回"Download success."等确认信息。
     * </p>
     */
    private String data;

    /**
     * 默认构造函数
     */
    public ConfirmDownloadResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 响应数据
     */
    public ConfirmDownloadResponseModel(String code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    // ========== 业务方法 ==========

    /**
     * 检查响应是否表示成功
     *
     * @return 如果响应表示成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取错误信息
     * <p>
     * 当响应不成功时，返回错误信息用于显示给用户。
     * 优先返回message字段，如果为空则返回默认错误信息。
     * </p>
     *
     * @return 错误信息字符串
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }
        
        return "确认下载失败，请稍后重试";
    }

    /**
     * 获取成功信息
     * <p>
     * 当响应成功时，返回成功信息用于显示给用户。
     * 优先返回data字段，如果为空则返回message字段。
     * </p>
     *
     * @return 成功信息字符串
     */
    public String getSuccessMessage() {
        if (!isSuccess()) {
            return null;
        }
        
        if (data != null && !data.trim().isEmpty()) {
            return data;
        }
        
        if (message != null && !message.trim().isEmpty()) {
            return message;
        }
        
        return "下载确认成功";
    }

    // ========== 工具方法 ==========

    /**
     * 转换为字符串表示
     *
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "ConfirmDownloadResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                '}';
    }

    /**
     * 比较两个对象是否相等
     *
     * @param obj 要比较的对象
     * @return 如果相等返回true，否则返回false
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        ConfirmDownloadResponseModel that = (ConfirmDownloadResponseModel) obj;

        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return data != null ? data.equals(that.data) : that.data == null;
    }

    /**
     * 获取对象的哈希码
     *
     * @return 哈希码值
     */
    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (data != null ? data.hashCode() : 0);
        return result;
    }
}
