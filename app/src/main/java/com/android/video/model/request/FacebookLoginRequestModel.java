package com.android.video.model.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.android.video.constants.AuthApiConstantsUtils;

/**
 * Facebook登录请求模型
 * <AUTHOR> Team
 */
public class FacebookLoginRequestModel {

    /**
     * Facebook授权码
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_CODE)
    @Expose
    private String code;

    /**
     * 设备ID
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_DEVICE_ID)
    @Expose
    private String deviceId;

    /**
     * 默认构造函数
     */
    public FacebookLoginRequestModel() {
    }

    /**
     * 构造函数
     * @param code Facebook授权码
     * @param deviceId 设备ID
     */
    public FacebookLoginRequestModel(String code, String deviceId) {
        this.code = code;
        this.deviceId = deviceId;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    // ========== 便利方法 ==========

    /**
     * 验证请求参数是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return code != null && !code.trim().isEmpty() &&
               deviceId != null && !deviceId.trim().isEmpty();
    }

    /**
     * 获取验证失败的原因
     * @return 验证失败原因，如果验证通过则返回null
     */
    public String getValidationError() {
        if (code == null || code.trim().isEmpty()) {
            return "Facebook authorization code is required";
        }
        if (deviceId == null || deviceId.trim().isEmpty()) {
            return "Device ID is required";
        }
        return null;
    }

    @Override
    public String toString() {
        return "FacebookLoginRequestModel{" +
                "code='" + code + '\'' +
                ", deviceId='" + deviceId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        FacebookLoginRequestModel that = (FacebookLoginRequestModel) obj;
        
        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        return deviceId != null ? deviceId.equals(that.deviceId) : that.deviceId == null;
    }

    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (deviceId != null ? deviceId.hashCode() : 0);
        return result;
    }
}
