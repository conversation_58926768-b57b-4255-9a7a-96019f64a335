package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 下载列表API返回的单个下载项数据模型
 * <AUTHOR> Team
 */
public class DownloadItemModel {

    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;

    /**
     * 封面URL
     */
    @SerializedName("cover")
    private String cover;

    /**
     * 语言类型 1=英语 2=俄语
     */
    @SerializedName("languageType")
    private int languageType;

    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;

    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;

    /**
     * 简介
     */
    @SerializedName("details")
    private String details;

    /**
     * 总章节数
     */
    @SerializedName("totalChaptersNum")
    private int totalChaptersNum;

    /**
     * 已下载章节数
     */
    @SerializedName("downloadedChaptersNum")
    private int downloadedChaptersNum;

    public DownloadItemModel() {
    }

    public DownloadItemModel(String filmTitle, String cover, int languageType, String filmId,
                           String filmLanguageInfoId, String details, int totalChaptersNum,
                           int downloadedChaptersNum) {
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.details = details;
        this.totalChaptersNum = totalChaptersNum;
        this.downloadedChaptersNum = downloadedChaptersNum;
    }

    // Getters and Setters
    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public int getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(int totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    public int getDownloadedChaptersNum() {
        return downloadedChaptersNum;
    }

    public void setDownloadedChaptersNum(int downloadedChaptersNum) {
        this.downloadedChaptersNum = downloadedChaptersNum;
    }

    /**
     * 获取语言描述
     * @return 语言描述字符串
     */
    public String getLanguageDescription() {
        switch (languageType) {
            case 1:
                return "English";
            case 2:
                return "Russian";
            default:
                return "Unknown";
        }
    }

    /**
     * 转换为DownloadVideo模型
     * @return DownloadVideo对象
     */
    public DownloadVideo toDownloadVideo() {
        DownloadVideo downloadVideo = new DownloadVideo();
        downloadVideo.setId(filmId);
        downloadVideo.setTitle(filmTitle != null ? filmTitle : "");
        downloadVideo.setPosterUrl(cover != null ? cover : "");
        downloadVideo.setFilmLanguageInfoId(filmLanguageInfoId); // 设置短剧语言信息ID
        downloadVideo.setDownloadedEpisodes(downloadedChaptersNum);
        downloadVideo.setTotalEpisodes(totalChaptersNum);
        downloadVideo.setDownloadDate(""); // API未提供下载日期
        downloadVideo.setFileSize(0); // API未提供文件大小
        downloadVideo.setFilePath(""); // API未提供文件路径
        downloadVideo.setDownloading(false);
        downloadVideo.setDownloadProgress(100); // 假设已下载完成
        return downloadVideo;
    }

    @Override
    public String toString() {
        return "DownloadItemModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", details='" + details + '\'' +
                ", totalChaptersNum=" + totalChaptersNum +
                ", downloadedChaptersNum=" + downloadedChaptersNum +
                '}';
    }
}
