package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Html;
import android.view.View;
import android.widget.ImageView;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.constants.ProfileApiConstantsUtils;
import com.android.video.network.ProfileApiService;

/**
 * 协议信息展示页面
 * <p>
 * 用于显示各种协议信息，如关于我们、隐私政策、用户协议等。
 * 通过传入的informationId参数从服务器获取对应的协议内容。
 * </p>
 *
 * <p>
 * Intent参数：
 * <ul>
 *   <li>EXTRA_INFORMATION_ID: 信息ID (必填)</li>
 *   <li>EXTRA_TITLE: 页面标题 (可选，默认为"Information")</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * Intent intent = new Intent(context, InformationActivity.class);
 * intent.putExtra(InformationActivity.EXTRA_INFORMATION_ID, "02a51817828d4c7e9d764a9fa5c267d8");
 * intent.putExtra(InformationActivity.EXTRA_TITLE, "About Us");
 * startActivity(intent);
 * </pre>
 * </p>
 */
public class InformationActivity extends BaseFullScreenActivity {

    // Intent参数常量
    public static final String EXTRA_INFORMATION_ID = "information_id";
    public static final String EXTRA_TITLE = "title";

    // 预定义的信息ID常量
    public static final String INFORMATION_ID_ABOUT_US = "02a51817828d4c7e9d764a9fa5c267d8";
    public static final String INFORMATION_ID_PRIVACY_POLICY = "privacy_policy_id";
    public static final String INFORMATION_ID_TERMS_OF_SERVICE = "terms_of_service_id";

    private ImageView ivBack;
    private TextView tvTitle;
    private TextView tvContent;
    private SwipeRefreshLayout swipeRefreshLoading;

    private String informationId;
    private String pageTitle;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_information);

        // 获取Intent参数
        getIntentData();

        // 初始化视图
        initViews();

        // 设置点击事件
        setupClickListeners();

        // 加载数据
        loadInformation();
    }

    /**
     * 获取Intent传递的参数
     */
    private void getIntentData() {
        Intent intent = getIntent();
        informationId = intent.getStringExtra(EXTRA_INFORMATION_ID);
        pageTitle = intent.getStringExtra(EXTRA_TITLE);

        // 参数验证
        if (informationId == null || informationId.isEmpty()) {
            Toast.makeText(this, "Invalid information ID", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        // 设置默认标题
        if (pageTitle == null || pageTitle.isEmpty()) {
            pageTitle = "Information";
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        tvContent = findViewById(R.id.tv_content);
        swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);

        // 设置页面标题
        tvTitle.setText(pageTitle);
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());
    }

    /**
     * 加载协议信息
     */
    private void loadInformation() {
        // 显示加载状态
        showLoading(true);

        // 显示调试信息
        String debugInfo = "Loading information...\n\n" +
                          "Information ID: " + informationId + "\n" +
                          "API URL: https://short-play-api.gymooit.cn/v1/app/myMenter/getInformation?InformationId=" + informationId;
        tvContent.setText(debugInfo);

        // 调用API获取信息
        ProfileApiService.getInstance().getInformation(
            informationId,
            new ProfileApiService.InformationCallback() {
                @Override
                public void onSuccess(String informationName, String informationDetails) {
                    runOnUiThread(() -> {
                        // 隐藏加载状态
                        showLoading(false);

                        // 更新标题（如果服务器返回了名称）
                        if (informationName != null && !informationName.isEmpty()) {
                            tvTitle.setText(informationName);
                        }

                        // 显示内容
                        if (informationDetails != null && !informationDetails.isEmpty()) {
                            // 支持HTML格式的内容
                            tvContent.setText(Html.fromHtml(informationDetails, Html.FROM_HTML_MODE_COMPACT));
                        } else {
                            tvContent.setText("No content available");
                        }
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    runOnUiThread(() -> {
                        // 隐藏加载状态
                        showLoading(false);

                        // 显示错误信息
                        Toast.makeText(InformationActivity.this,
                            "Failed to load information: " + errorMessage,
                            Toast.LENGTH_LONG).show();

                        // 显示详细错误内容，包含调试信息
                        String debugInfo = "Failed to load content. Please try again later.\n\n" +
                                          "Debug Info:\n" +
                                          "Information ID: " + informationId + "\n" +
                                          "Error: " + errorMessage;
                        tvContent.setText(debugInfo);
                    });
                }
            }
        );
    }

    /**
     * 显示或隐藏加载状态
     *
     * @param show 是否显示加载状态
     */
    private void showLoading(boolean show) {
        if (show) {
            swipeRefreshLoading.setVisibility(View.VISIBLE);
            swipeRefreshLoading.setRefreshing(true);
            tvContent.setVisibility(View.GONE);
        } else {
            swipeRefreshLoading.setVisibility(View.GONE);
            swipeRefreshLoading.setRefreshing(false);
            tvContent.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 创建About Us页面的Intent
     *
     * @param context 上下文
     * @return Intent对象
     */
    public static Intent createAboutUsIntent(android.content.Context context) {
        Intent intent = new Intent(context, InformationActivity.class);
        intent.putExtra(EXTRA_INFORMATION_ID, INFORMATION_ID_ABOUT_US);
        intent.putExtra(EXTRA_TITLE, "About Us");
        return intent;
    }

    /**
     * 创建Privacy Policy页面的Intent
     *
     * @param context 上下文
     * @return Intent对象
     */
    public static Intent createPrivacyPolicyIntent(android.content.Context context) {
        Intent intent = new Intent(context, InformationActivity.class);
        intent.putExtra(EXTRA_INFORMATION_ID, INFORMATION_ID_PRIVACY_POLICY);
        intent.putExtra(EXTRA_TITLE, "Privacy Policy");
        return intent;
    }

    /**
     * 创建Terms of Service页面的Intent
     *
     * @param context 上下文
     * @return Intent对象
     */
    public static Intent createTermsOfServiceIntent(android.content.Context context) {
        Intent intent = new Intent(context, InformationActivity.class);
        intent.putExtra(EXTRA_INFORMATION_ID, INFORMATION_ID_TERMS_OF_SERVICE);
        intent.putExtra(EXTRA_TITLE, "Terms of Service");
        return intent;
    }
}
