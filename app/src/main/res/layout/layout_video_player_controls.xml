<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/video_player_background">

    <!-- ExoPlayer PlayerView -->
    <com.google.android.exoplayer2.ui.PlayerView
        android:id="@+id/playerView"
        android:layout_width="match_parent"
        android:layout_height="@dimen/video_player_view_height"
        android:layout_marginTop="@dimen/video_player_view_margin_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:use_controller="false"
        app:resize_mode="fit" />

    <!-- Center Play Controls -->
    <LinearLayout
        android:id="@+id/centerPlayControls"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:visibility="visible"
        app:layout_constraintTop_toTopOf="@+id/playerView"
        app:layout_constraintBottom_toBottomOf="@+id/playerView"
        app:layout_constraintStart_toStartOf="@+id/playerView"
        app:layout_constraintEnd_toEndOf="@+id/playerView">

        <!-- Seek Backward 5s Button -->
        <ImageView
            android:id="@+id/btnSeekBackward"
            android:layout_width="@dimen/video_player_seek_button_size"
            android:layout_height="@dimen/video_player_seek_button_size"
            android:layout_marginEnd="@dimen/video_player_seek_button_margin_horizontal"
            android:src="@drawable/play_ic_5s_left"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/video_player_seek_backward" />

        <!-- Play/Pause Button -->
        <ImageView
            android:id="@+id/btnPlayPause"
            android:layout_width="@dimen/video_player_play_button_size"
            android:layout_height="@dimen/video_player_play_button_size"
            android:src="@drawable/play_ic_play"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/video_player_play_pause" />

        <!-- Seek Forward 5s Button -->
        <ImageView
            android:id="@+id/btnSeekForward"
            android:layout_width="@dimen/video_player_seek_button_size"
            android:layout_height="@dimen/video_player_seek_button_size"
            android:layout_marginStart="@dimen/video_player_seek_button_margin_horizontal"
            android:src="@drawable/play_ic_5s_right"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="@string/video_player_seek_forward" />

    </LinearLayout>



    <!-- Buffering Text -->
    <TextView
        android:id="@+id/tvBuffering"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="缓冲中..."
        android:textColor="@color/video_player_text_white"
        android:textSize="@dimen/video_player_text_size_medium"
        android:visibility="gone"
        android:layout_marginTop="@dimen/video_player_common_margin_small"
        app:layout_constraintTop_toBottomOf="@+id/loadingIndicator"
        app:layout_constraintStart_toStartOf="@+id/loadingIndicator"
        app:layout_constraintEnd_toEndOf="@+id/loadingIndicator"
        tools:ignore="HardcodedText" />

    <!-- Current Time Display -->
    <TextView
        android:id="@+id/tvCurrentTime"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00"
        android:textColor="@color/video_player_text_white"
        android:textSize="@dimen/video_player_text_size_small"
        android:layout_margin="@dimen/video_player_common_margin"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/playerView"
        app:layout_constraintStart_toStartOf="@+id/playerView"
        tools:ignore="HardcodedText" />

    <!-- Total Duration Display -->
    <TextView
        android:id="@+id/tvTotalDuration"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="00:00"
        android:textColor="@color/video_player_text_white"
        android:textSize="@dimen/video_player_text_size_small"
        android:layout_margin="@dimen/video_player_common_margin"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="@+id/playerView"
        app:layout_constraintEnd_toEndOf="@+id/playerView"
        tools:ignore="HardcodedText" />

    <!-- Error Message -->
    <TextView
        android:id="@+id/tvErrorMessage"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="播放出错，请重试"
        android:textColor="@color/video_player_text_white"
        android:textSize="@dimen/video_player_text_size_large"
        android:visibility="gone"
        android:gravity="center"
        android:padding="@dimen/video_player_common_margin"
        app:layout_constraintTop_toTopOf="@+id/playerView"
        app:layout_constraintBottom_toBottomOf="@+id/playerView"
        app:layout_constraintStart_toStartOf="@+id/playerView"
        app:layout_constraintEnd_toEndOf="@+id/playerView"
        tools:ignore="HardcodedText" />

    <!-- Retry Button -->
    <Button
        android:id="@+id/btnRetry"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="重试"
        android:textColor="@color/video_player_text_white"
        android:background="@drawable/video_player_control_button_bg"
        android:visibility="gone"
        android:layout_marginTop="@dimen/video_player_common_margin"
        app:layout_constraintTop_toBottomOf="@+id/tvErrorMessage"
        app:layout_constraintStart_toStartOf="@+id/tvErrorMessage"
        app:layout_constraintEnd_toEndOf="@+id/tvErrorMessage"
        tools:ignore="HardcodedText" />

</androidx.constraintlayout.widget.ConstraintLayout>
