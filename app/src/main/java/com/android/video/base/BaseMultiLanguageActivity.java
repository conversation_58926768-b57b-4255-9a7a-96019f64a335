package com.android.video.base;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;
import androidx.annotation.Nullable;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import com.android.video.manager.LocalizationManager;
import com.android.video.callback.LocalizationCallback;
import com.android.video.ui.activity.BaseFullScreenActivity;
import com.android.video.utils.GlobalTextUpdater;

/**
 * 多语言基础Activity
 * 所有需要多语言支持的Activity都应该继承此类
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public abstract class BaseMultiLanguageActivity extends BaseFullScreenActivity implements LocalizationCallback {
    
    private static final String TAG = "BaseMultiLanguageActivity";
    protected LocalizationManager localizationManager;
    private BroadcastReceiver languageChangeReceiver;
    
    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        initLocalization();
        registerLanguageChangeReceiver();
    }
    
    /**
     * 初始化多语言功能
     */
    private void initLocalization() {
        try {
            localizationManager = LocalizationManager.getInstance(this);
            localizationManager.setLanguageChangeListener(this);
            localizationManager.loadTranslations();
            Log.d(TAG, "Localization initialized for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error initializing localization", e);
        }
    }
    
    @Override
    protected void onResume() {
        super.onResume();
        // 每次页面恢复时更新文本，确保语言切换后的一致性
        updateAllTexts();
    }
    
    /**
     * 更新所有界面文本
     * 子类可以重写此方法来添加特定的文本更新逻辑
     */
    protected void updateAllTexts() {
        try {
            GlobalTextUpdater.updateActivityTexts(this);
            Log.d(TAG, "Updated all texts for: " + getClass().getSimpleName());
        } catch (Exception e) {
            Log.e(TAG, "Error updating texts", e);
        }
    }
    
    // LocalizationCallback接口实现
    
    @Override
    public void onLanguageChanged(int newLanguageType) {
        Log.d(TAG, "Language changed to type: " + newLanguageType + " in " + getClass().getSimpleName());
        updateAllTexts();
    }
    
    @Override
    public void onTranslationLoaded() {
        Log.d(TAG, "Translation data loaded in " + getClass().getSimpleName());
        updateAllTexts();
    }
    
    @Override
    public void onError(String errorMessage) {
        Log.e(TAG, "Localization error in " + getClass().getSimpleName() + ": " + errorMessage);
    }
    
    /**
     * 注册语言切换广播接收器
     */
    private void registerLanguageChangeReceiver() {
        languageChangeReceiver = new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                if (LocalizationManager.ACTION_LANGUAGE_CHANGED.equals(intent.getAction())) {
                    int newLanguageType = intent.getIntExtra(LocalizationManager.EXTRA_LANGUAGE_TYPE, 1);
                    Log.d(TAG, "收到语言切换广播: " + newLanguageType + " in " + getClass().getSimpleName());
                    updateAllTexts();
                }
            }
        };

        IntentFilter filter = new IntentFilter(LocalizationManager.ACTION_LANGUAGE_CHANGED);
        LocalBroadcastManager.getInstance(this).registerReceiver(languageChangeReceiver, filter);
        Log.d(TAG, "已注册语言切换广播接收器");
    }

    @Override
    protected void onDestroy() {
        // 清理LocalizationManager监听器，避免内存泄漏
        if (localizationManager != null) {
            localizationManager.setLanguageChangeListener(null);
        }

        // 注销广播接收器
        if (languageChangeReceiver != null) {
            LocalBroadcastManager.getInstance(this).unregisterReceiver(languageChangeReceiver);
        }

        super.onDestroy();
    }
}
