<?xml version="1.0" encoding="utf-8"?>
<!-- VIP卡片布局模板 -->
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
             android:layout_width="match_parent"
             android:layout_height="wrap_content"
             android:layout_marginStart="@dimen/subscribe_card_margin_horizontal"
             android:layout_marginTop="@dimen/subscribe_card_margin_vertical"
             android:layout_marginEnd="@dimen/subscribe_card_margin_horizontal"
             android:layout_marginBottom="@dimen/vip_card_border_overflow"
             android:paddingBottom="@dimen/subscribe_card_margin_vertical">

    <!-- VIP卡片边框 -->
    <View
            android:id="@+id/vip_card_border"
            android:layout_width="match_parent"
            android:layout_height="@dimen/vip_card_border_height"
            android:background="@drawable/vip_card_border_frame"
            android:visibility="invisible" />

    <!-- VIP卡片内容 -->
    <LinearLayout
            android:id="@+id/vip_card_content"
            android:layout_width="match_parent"
            android:layout_height="@dimen/subscribe_card_height"
            android:layout_margin="@dimen/vip_card_border_overflow"
            android:orientation="vertical"
            android:background="@drawable/subscribe_weekly_card_bg"
            android:clickable="true"
            android:focusable="true"
            android:padding="@dimen/subscribe_card_padding">

        <!-- 标题和价格区域 -->
        <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

            <!-- 左侧标题区域 -->
            <LinearLayout
                    android:id="@+id/layout_vip_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:orientation="vertical">

                <!-- VIP名称 -->
                <TextView
                        android:id="@+id/tv_vip_name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="VIP Pass Pro"
                        android:textColor="#fcffffff"
                        android:textSize="@dimen/subscribe_card_title_text_size"
                        android:fontFamily="sans-serif-medium"/>

                <!-- VIP描述 -->
                <TextView
                        android:id="@+id/tv_vip_subtitle"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:text="Unlock all series"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/subscribe_card_subtitle_text_size"/>

            </LinearLayout>

            <!-- 右侧价格区域 -->
            <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:orientation="vertical"
                    android:gravity="end">

                <!-- 折扣和原价行 -->
                <LinearLayout
                        android:id="@+id/layout_discount"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/discount_badge_height"
                        android:background="@drawable/discount_yellow_bg"
                        android:orientation="horizontal"
                        android:gravity="center"
                        android:paddingStart="@dimen/discount_badge_padding_horizontal"
                        android:paddingEnd="@dimen/discount_badge_padding_horizontal"
                        android:visibility="gone"
                        android:minWidth="@dimen/discount_badge_width">

                    <!-- 折扣文字 -->
                    <TextView
                            android:id="@+id/tv_discount"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="(-20%)"
                            android:textColor="#ffb65c16"
                            android:textSize="@dimen/discount_badge_text_size"
                            android:maxLines="1"
                            android:ellipsize="end"/>

                    <!-- 原价 -->
                    <TextView
                            android:id="@+id/tv_original_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="4dp"
                            android:text="$49"
                            android:textColor="#a3b65c16"
                            android:textSize="@dimen/discount_badge_text_size"
                            android:maxLines="1"
                            android:ellipsize="end" />

                </LinearLayout>

                <!-- 现价行 -->
                <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                    <!-- 当前价格 -->
                    <TextView
                            android:id="@+id/tv_current_price"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="$39"
                            android:textColor="#fff12626"
                            android:textSize="@dimen/subscribe_card_price_main_text_size"
                            android:fontFamily="sans-serif-medium"/>

                    <!-- 价格单位 -->
                    <TextView
                            android:id="@+id/tv_price_unit"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="/7days"
                            android:textColor="#fff12626"
                            android:textSize="@dimen/subscribe_card_price_unit_text_size"
                            android:fontFamily="sans-serif-light"/>

                </LinearLayout>

            </LinearLayout>

        </RelativeLayout>

        <!-- VIP权益描述 -->
        <TextView
                android:id="@+id/tv_vip_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="12dp"
                android:text="Unlock VIP privileges and receive bonus points"
                android:textColor="#b3ffffff"
                android:textSize="@dimen/subscribe_card_description_text_size"/>

    </LinearLayout>

</FrameLayout>
