<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <layer-list>
            <!-- 渐变边框 -->
            <item>
                <shape android:shape="rectangle">
                    <gradient
                        android:angle="0"
                        android:startColor="#AF1E3C"
                        android:endColor="#2403AC"
                        android:type="linear" />
                    <corners android:radius="12dp" />
                </shape>
            </item>
            
            <!-- 白色背景 -->
            <item
                android:left="1dp"
                android:top="1dp"
                android:right="1dp"
                android:bottom="1dp">
                <shape android:shape="rectangle">
                    <solid android:color="#FFFFFF" />
                    <corners android:radius="11dp" />
                </shape>
            </item>
        </layer-list>
    </item>
    
    <!-- 未选中状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#00000000" />
            <stroke 
                android:width="1dp"
                android:color="#5CFFFFFF" />
            <corners android:radius="12dp" />
        </shape>
    </item>
    
</selector>
