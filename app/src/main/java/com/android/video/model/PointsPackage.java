package com.android.video.model;

/**
 * 积分套餐模型
 */
public class PointsPackage {
    private String packageId;
    private String packageName;
    private int points;
    private int giftPoints;
    private double price;
    private int isDelete;
    private String createTime;
    private String updateTime;

    /**
     * 构造方法 - 用于API响应数据
     */
    public PointsPackage(String packageId, String packageName, int points, int giftPoints, 
                        double price, int isDelete, String createTime, String updateTime) {
        this.packageId = packageId;
        this.packageName = packageName;
        this.points = points;
        this.giftPoints = giftPoints;
        this.price = price;
        this.isDelete = isDelete;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    /**
     * 默认构造方法
     */
    public PointsPackage() {
    }

    // Getter和Setter方法

    public String getPackageId() {
        return packageId;
    }

    public void setPackageId(String packageId) {
        this.packageId = packageId;
    }

    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public int getGiftPoints() {
        return giftPoints;
    }

    public void setGiftPoints(int giftPoints) {
        this.giftPoints = giftPoints;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    // 业务方法

    /**
     * 获取总积分（基础积分 + 赠送积分）
     */
    public int getTotalPoints() {
        return points + giftPoints;
    }

    /**
     * 检查套餐是否有效
     */
    public boolean isValid() {
        return isDelete == 1;
    }

    /**
     * 获取格式化的价格字符串
     */
    public String getFormattedPrice() {
        return String.format("$%.2f", price);
    }

    /**
     * 获取积分描述
     */
    public String getPointsDescription() {
        if (giftPoints > 0) {
            return points + " + " + giftPoints + " bonus";
        } else {
            return String.valueOf(points);
        }
    }

    /**
     * 检查是否有赠送积分
     */
    public boolean hasGiftPoints() {
        return giftPoints > 0;
    }

    @Override
    public String toString() {
        return "PointsPackage{" +
                "packageId='" + packageId + '\'' +
                ", packageName='" + packageName + '\'' +
                ", points=" + points +
                ", giftPoints=" + giftPoints +
                ", price=" + price +
                ", isDelete=" + isDelete +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PointsPackage that = (PointsPackage) o;

        return packageId != null ? packageId.equals(that.packageId) : that.packageId == null;
    }

    @Override
    public int hashCode() {
        return packageId != null ? packageId.hashCode() : 0;
    }
}
