package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.ApiConstantsUtils;

/**
 * 通用API响应模型 - 统一的API响应数据结构
 * <AUTHOR> Team
 * @param <T> 响应数据类型
 */
public class ApiResponseModel<T> {

    /**
     * 响应码
     */
    @SerializedName(ApiConstantsUtils.FIELD_CODE)
    private String code;

    /**
     * 响应消息
     */
    @SerializedName(ApiConstantsUtils.FIELD_MESSAGE)
    private String message;

    /**
     * 响应数据
     */
    @SerializedName(ApiConstantsUtils.FIELD_DATA)
    private T data;

    /**
     * 默认构造函数
     */
    public ApiResponseModel() {
    }

    /**
     * 完整构造函数
     * @param code 响应码
     * @param message 响应消息
     * @param data 响应数据
     */
    public ApiResponseModel(String code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应构造函数
     * @param data 响应数据
     */
    public ApiResponseModel(T data) {
        this.code = ApiConstantsUtils.RESPONSE_CODE_SUCCESS;
        this.message = "success";
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public T getData() {
        return data;
    }

    public void setData(T data) {
        this.data = data;
    }

    // ========== 便利方法 ==========

    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return ApiConstantsUtils.isSuccessCode(code);
    }

    /**
     * 检查响应是否失败
     * @return 是否失败
     */
    public boolean isError() {
        return !isSuccess();
    }

    /**
     * 获取错误消息（如果有）
     * @return 错误消息
     */
    public String getErrorMessage() {
        if (isError()) {
            return message != null && !message.isEmpty() ? message : "Unknown error";
        }
        return null;
    }

    /**
     * 检查是否有数据
     * @return 是否有数据
     */
    public boolean hasData() {
        return data != null;
    }

    /**
     * 安全获取数据（避免空指针）
     * @param defaultValue 默认值
     * @return 数据或默认值
     */
    public T getDataOrDefault(T defaultValue) {
        return data != null ? data : defaultValue;
    }

    /**
     * 检查响应码是否为指定值
     * @param expectedCode 期望的响应码
     * @return 是否匹配
     */
    public boolean isCodeEquals(String expectedCode) {
        return expectedCode != null && expectedCode.equals(code);
    }

    /**
     * 检查是否为参数错误
     * @return 是否为参数错误
     */
    public boolean isParamError() {
        return isCodeEquals(ApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR);
    }

    /**
     * 检查是否为未授权错误
     * @return 是否为未授权错误
     */
    public boolean isUnauthorized() {
        return isCodeEquals(ApiConstantsUtils.RESPONSE_CODE_UNAUTHORIZED);
    }

    /**
     * 检查是否为服务器错误
     * @return 是否为服务器错误
     */
    public boolean isServerError() {
        return isCodeEquals(ApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR);
    }

    // ========== 静态工厂方法 ==========

    /**
     * 创建成功响应
     * @param data 响应数据
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> ApiResponseModel<T> success(T data) {
        return new ApiResponseModel<>(ApiConstantsUtils.RESPONSE_CODE_SUCCESS, "success", data);
    }

    /**
     * 创建成功响应（无数据）
     * @param <T> 数据类型
     * @return 成功响应对象
     */
    public static <T> ApiResponseModel<T> success() {
        return success(null);
    }

    /**
     * 创建错误响应
     * @param code 错误码
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 错误响应对象
     */
    public static <T> ApiResponseModel<T> error(String code, String message) {
        return new ApiResponseModel<>(code, message, null);
    }

    /**
     * 创建参数错误响应
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 参数错误响应对象
     */
    public static <T> ApiResponseModel<T> paramError(String message) {
        return error(ApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR, message);
    }

    /**
     * 创建服务器错误响应
     * @param message 错误消息
     * @param <T> 数据类型
     * @return 服务器错误响应对象
     */
    public static <T> ApiResponseModel<T> serverError(String message) {
        return error(ApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR, message);
    }

    // ========== Object方法重写 ==========

    @Override
    public String toString() {
        return "ApiResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        ApiResponseModel<?> that = (ApiResponseModel<?>) obj;
        
        if (code != null ? !code.equals(that.code) : that.code != null) return false;
        if (message != null ? !message.equals(that.message) : that.message != null) return false;
        return data != null ? data.equals(that.data) : that.data == null;
    }

    @Override
    public int hashCode() {
        int result = code != null ? code.hashCode() : 0;
        result = 31 * result + (message != null ? message.hashCode() : 0);
        result = 31 * result + (data != null ? data.hashCode() : 0);
        return result;
    }
}
