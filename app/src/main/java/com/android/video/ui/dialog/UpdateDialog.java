package com.android.video.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.TextView;

import com.android.video.R;
import com.android.video.model.VersionInfoModel;

/**
 * 更新弹窗
 * 用于提示用户应用有新版本可更新
 */
public class UpdateDialog extends Dialog {

    private TextView tvVersion;
    private TextView tvUpdateTitle;
    private TextView tvDescriptionLine2;
    private TextView tvDescriptionLine3;
    private Button btnUpdate;
    private Button btnCancel;
    private OnUpdateDialogListener listener;
    private String versionName;
    private VersionInfoModel versionInfo;
    private boolean isForceUpdate = false;

    /**
     * 更新弹窗监听器接口
     */
    public interface OnUpdateDialogListener {
        /**
         * 点击更新按钮
         */
        void onUpdateClicked();

        /**
         * 点击取消按钮（仅在非强制更新时可用）
         */
        void onCancelClicked();

        /**
         * 弹窗被关闭
         */
        void onDialogDismissed();
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public UpdateDialog(Context context) {
        super(context, R.style.CustomDialog);
        this.versionName = "0.0.1"; // 默认版本号
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param versionName 版本号
     */
    public UpdateDialog(Context context, String versionName) {
        super(context, R.style.CustomDialog);
        this.versionName = versionName != null ? versionName : "0.0.1";
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param versionName 版本号
     * @param listener 监听器
     */
    public UpdateDialog(Context context, String versionName, OnUpdateDialogListener listener) {
        super(context, R.style.CustomDialog);
        this.versionName = versionName != null ? versionName : "0.0.1";
        this.listener = listener;
    }

    /**
     * 构造函数（支持版本信息模型）
     * @param context 上下文
     * @param versionInfo 版本信息模型
     * @param listener 监听器
     */
    public UpdateDialog(Context context, VersionInfoModel versionInfo, OnUpdateDialogListener listener) {
        super(context, R.style.CustomDialog);
        this.versionInfo = versionInfo;
        this.versionName = versionInfo != null ? versionInfo.getVersionCode() : "0.0.1";
        this.listener = listener;
        this.isForceUpdate = true; // 默认强制更新
    }

    /**
     * 构造函数（支持强制更新设置）
     * @param context 上下文
     * @param versionInfo 版本信息模型
     * @param listener 监听器
     * @param isForceUpdate 是否强制更新
     */
    public UpdateDialog(Context context, VersionInfoModel versionInfo, OnUpdateDialogListener listener, boolean isForceUpdate) {
        super(context, R.style.CustomDialog);
        this.versionInfo = versionInfo;
        this.versionName = versionInfo != null ? versionInfo.getVersionCode() : "0.0.1";
        this.listener = listener;
        this.isForceUpdate = isForceUpdate;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_update);

        // 设置窗口属性
        setupWindow();

        // 初始化视图
        initViews();

        // 设置监听器
        setupListeners();

        // 设置版本号
        updateVersionText();

        // 设置强制更新模式
        setupForceUpdateMode();
    }

    /**
     * 设置窗口属性
     */
    private void setupWindow() {
        Window window = getWindow();
        if (window != null) {
            // 设置窗口背景透明
            window.setBackgroundDrawableResource(android.R.color.transparent);
            
            // 设置窗口位置和大小
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.WRAP_CONTENT;
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            
            // 设置窗口位置（距离顶部230dp，通过gravity和margin实现）
            params.gravity = android.view.Gravity.CENTER_HORIZONTAL | android.view.Gravity.TOP;
            
            // 获取屏幕密度来转换dp到px
            float density = getContext().getResources().getDisplayMetrics().density;
            int marginTopPx = (int) (230 * density);
            params.y = marginTopPx;
            
            window.setAttributes(params);
            
            // 设置窗口可以接收触摸事件
            window.setFlags(WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
                    WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL);
        }
    }

    /**
     * 初始化视图
     */
    private void initViews() {
        tvVersion = findViewById(R.id.tv_version);
        tvUpdateTitle = findViewById(R.id.tv_update_title);
        tvDescriptionLine2 = findViewById(R.id.tv_description_line2);
        tvDescriptionLine3 = findViewById(R.id.tv_description_line3);
        btnUpdate = findViewById(R.id.btn_update);
        btnCancel = findViewById(R.id.btn_cancel);

        // 如果有版本信息，更新描述内容
        if (versionInfo != null) {
            updateVersionDescription();
        }
    }

    /**
     * 设置监听器
     */
    private void setupListeners() {
        // 更新按钮点击事件
        btnUpdate.setOnClickListener(new View.OnClickListener() {
            @Override
            public void onClick(View v) {
                if (listener != null) {
                    listener.onUpdateClicked();
                }
                dismiss();
            }
        });

        // 取消按钮点击事件
        if (btnCancel != null) {
            btnCancel.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    if (listener != null) {
                        listener.onCancelClicked();
                    }
                    dismiss();
                }
            });
        }

        // 点击弹窗背景区域关闭弹窗（仅在非强制更新时）
        View dialogContainer = findViewById(R.id.dialog_container);
        if (dialogContainer != null) {
            dialogContainer.setOnClickListener(new View.OnClickListener() {
                @Override
                public void onClick(View v) {
                    // 只有点击的是容器本身（不是子视图）且非强制更新才关闭弹窗
                    if (v.getId() == R.id.dialog_container && !isForceUpdate) {
                        dismiss();
                    }
                }
            });
        }

        // 设置弹窗关闭监听
        setOnDismissListener(dialog -> {
            if (listener != null) {
                listener.onDialogDismissed();
            }
        });
    }

    /**
     * 更新版本号文本
     */
    private void updateVersionText() {
        if (tvVersion != null) {
            String versionText = getContext().getString(R.string.update_dialog_version, versionName);
            tvVersion.setText(versionText);
        }
    }

    /**
     * 设置强制更新模式
     */
    private void setupForceUpdateMode() {
        if (isForceUpdate) {
            // 强制更新模式：隐藏取消按钮，禁用返回键
            if (btnCancel != null) {
                btnCancel.setVisibility(View.GONE);
            }
            setCancelable(false);
            setCanceledOnTouchOutside(false);
        } else {
            // 可选更新模式：显示取消按钮，允许取消
            if (btnCancel != null) {
                btnCancel.setVisibility(View.VISIBLE);
            }
            setCancelable(true);
            setCanceledOnTouchOutside(true);
        }
    }

    /**
     * 设置监听器
     * @param listener 监听器
     */
    public void setOnUpdateDialogListener(OnUpdateDialogListener listener) {
        this.listener = listener;
    }

    /**
     * 设置版本号
     * @param versionName 版本号
     */
    public void setVersionName(String versionName) {
        this.versionName = versionName != null ? versionName : "0.0.1";
        updateVersionText();
    }

    /**
     * 显示弹窗
     */
    @Override
    public void show() {
        try {
            super.show();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 更新版本描述内容
     */
    private void updateVersionDescription() {
        if (versionInfo == null) return;

        // 更新版本描述
        String versionDesc = versionInfo.getVersionInfo();
        if (versionDesc != null && !versionDesc.trim().isEmpty()) {
            // 如果有版本描述，显示在第二行
            if (tvDescriptionLine2 != null) {
                tvDescriptionLine2.setText(versionDesc);
            }

            // 第三行显示发布时间
            if (tvDescriptionLine3 != null && versionInfo.getReleaseTime() != null) {
                String releaseText = "发布时间: " + versionInfo.getReleaseTime();
                tvDescriptionLine3.setText(releaseText);
            }
        }
    }

    /**
     * 设置版本信息
     * @param versionInfo 版本信息模型
     */
    public void setVersionInfo(VersionInfoModel versionInfo) {
        this.versionInfo = versionInfo;
        if (versionInfo != null) {
            this.versionName = versionInfo.getVersionCode();
            updateVersionText();
            updateVersionDescription();
        }
    }

    /**
     * 静态方法：创建并显示更新弹窗
     * @param context 上下文
     * @param versionName 版本号
     * @param listener 监听器
     * @return UpdateDialog实例
     */
    public static UpdateDialog showUpdateDialog(Context context, String versionName, OnUpdateDialogListener listener) {
        UpdateDialog dialog = new UpdateDialog(context, versionName, listener);
        dialog.show();
        return dialog;
    }

    /**
     * 静态方法：创建并显示更新弹窗（支持版本信息模型）
     * @param context 上下文
     * @param versionInfo 版本信息模型
     * @param listener 监听器
     * @return UpdateDialog实例
     */
    public static UpdateDialog showUpdateDialog(Context context, VersionInfoModel versionInfo, OnUpdateDialogListener listener) {
        UpdateDialog dialog = new UpdateDialog(context, versionInfo, listener);
        dialog.show();
        return dialog;
    }

    /**
     * 静态方法：创建并显示更新弹窗（支持强制更新设置）
     * @param context 上下文
     * @param versionInfo 版本信息模型
     * @param listener 监听器
     * @param isForceUpdate 是否强制更新
     * @return UpdateDialog实例
     */
    public static UpdateDialog showUpdateDialog(Context context, VersionInfoModel versionInfo, OnUpdateDialogListener listener, boolean isForceUpdate) {
        UpdateDialog dialog = new UpdateDialog(context, versionInfo, listener, isForceUpdate);
        dialog.show();
        return dialog;
    }
}
