package com.android.video;

import android.content.Context;
import org.junit.Test;
import org.junit.Before;
import static org.junit.Assert.*;

import com.android.video.utils.TestVideoGenerator;
import com.android.video.model.TestVideoModel;
import com.android.video.utils.VideoResourceManager;

import java.util.List;

/**
 * 视频播放器功能综合测试
 * 验证所有修复的功能是否正常工作
 */
public class VideoPlayerFunctionalityTest {

    private TestVideoGenerator testVideoGenerator;
    private VideoResourceManager videoResourceManager;

    @Before
    public void setUp() {
        // 使用null context for testing (单元测试环境)
        testVideoGenerator = new TestVideoGenerator(null);
        videoResourceManager = VideoResourceManager.getInstance();
    }

    /**
     * 测试1：多视频资源加载功能
     */
    @Test
    public void testMultiVideoResourceLoading() {
        List<TestVideoModel> videos = testVideoGenerator.generateTestVideos();
        
        // 验证视频列表不为空
        assertNotNull("视频列表不应为空", videos);
        assertTrue("应该加载多个视频文件", videos.size() > 1);
        
        // 验证每个视频都有有效的资源ID
        for (TestVideoModel video : videos) {
            assertTrue("视频应该有有效的资源ID", video.getResourceId() > 0);
            assertNotNull("视频标题不应为空", video.getTitle());
            assertNotNull("视频ID不应为空", video.getId());
        }
        
        System.out.println("✅ 测试1通过：成功加载 " + videos.size() + " 个视频资源");
    }

    /**
     * 测试2：视频URL生成功能
     */
    @Test
    public void testVideoUrlGeneration() {
        List<TestVideoModel> videos = testVideoGenerator.generateTestVideos();
        
        for (TestVideoModel video : videos) {
            // 模拟VideoPlayerFragment的getVideoUrl逻辑
            String videoUrl = generateVideoUrl(video);
            
            assertNotNull("视频URL不应为空", videoUrl);
            assertTrue("视频URL应该是android.resource格式", 
                videoUrl.startsWith("android.resource://"));
            assertTrue("视频URL应该包含资源ID", 
                videoUrl.contains(String.valueOf(video.getResourceId())));
        }
        
        System.out.println("✅ 测试2通过：视频URL生成功能正常");
    }

    /**
     * 测试3：视频模型验证功能
     */
    @Test
    public void testVideoModelValidation() {
        List<TestVideoModel> videos = testVideoGenerator.generateTestVideos();
        
        for (TestVideoModel video : videos) {
            assertTrue("视频模型应该通过验证", video.validateVideo());
            assertTrue("视频应该标记为有效", video.isValid());
        }
        
        System.out.println("✅ 测试3通过：所有视频模型验证通过");
    }

    /**
     * 模拟VideoPlayerFragment的getVideoUrl方法
     */
    private String generateVideoUrl(TestVideoModel testVideoModel) {
        if (testVideoModel != null) {
            // 优先使用TestVideoModel中的视频URI
            if (testVideoModel.getVideoUri() != null) {
                return testVideoModel.getVideoUri().toString();
            }
            
            // 如果有资源ID，使用资源ID构建URI
            if (testVideoModel.getResourceId() > 0) {
                return "android.resource://com.android.video/" + testVideoModel.getResourceId();
            }
        }
        
        // 使用默认的测试视频作为备用
        return "android.resource://com.android.video/" + R.raw.movie;
    }
}
