package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 短剧信息数据模型
 * <p>
 * 用于映射API返回的短剧基本信息JSON数据结构。
 * 包含短剧的标题、分类、封面、语言类型、简介等基本信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class FilmInfo {
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 分类ID
     * 多个分类用逗号分隔
     */
    @SerializedName("categoryId")
    private String categoryId;
    
    /**
     * 分类名称
     * 多个分类用逗号分隔
     */
    @SerializedName("categoryName")
    private String categoryName;
    
    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 搜索次数
     */
    @SerializedName("searchNum")
    private Integer searchNum;
    
    /**
     * 短剧简介
     */
    @SerializedName("details")
    private String details;
    
    /**
     * 是否加入喜欢
     * 0=未加入, 1=已加入
     */
    @SerializedName("isLove")
    private Integer isLove;
    
    /**
     * 播放量
     */
    @SerializedName("playNum")
    private Long playNum;
    
    /**
     * 总章节数
     */
    @SerializedName("totalChaptersNum")
    private Integer totalChaptersNum;

    /**
     * 默认构造函数
     */
    public FilmInfo() {
    }

    /**
     * 完整构造函数
     */
    public FilmInfo(String filmTitle, String categoryId, String categoryName, String cover,
                   Integer languageType, String filmId, String filmLanguageInfoId,
                   Integer searchNum, String details, Integer isLove, Long playNum,
                   Integer totalChaptersNum) {
        this.filmTitle = filmTitle;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.searchNum = searchNum;
        this.details = details;
        this.isLove = isLove;
        this.playNum = playNum;
        this.totalChaptersNum = totalChaptersNum;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Integer getIsLove() {
        return isLove;
    }

    public void setIsLove(Integer isLove) {
        this.isLove = isLove;
    }

    public Long getPlayNum() {
        return playNum;
    }

    public void setPlayNum(Long playNum) {
        this.playNum = playNum;
    }

    public Integer getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(Integer totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否已收藏
     * 
     * @return 如果已收藏返回true，否则返回false
     */
    public boolean isLoved() {
        return isLove != null && isLove == 1;
    }

    /**
     * 获取语言类型文本
     * 
     * @return 语言类型文本
     */
    public String getLanguageTypeText() {
        if (languageType == null) return "未知";
        return languageType == 1 ? "英语" : "俄语";
    }

    /**
     * 获取格式化的播放量
     * 
     * @return 格式化的播放量字符串
     */
    public String getFormattedPlayNum() {
        if (playNum == null || playNum == 0) return "0";
        
        if (playNum >= 1000000) {
            return String.format("%.1fM", playNum / 1000000.0);
        } else if (playNum >= 1000) {
            return String.format("%.1fK", playNum / 1000.0);
        } else {
            return String.valueOf(playNum);
        }
    }

    @Override
    public String toString() {
        return "FilmInfo{" +
                "filmTitle='" + filmTitle + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", searchNum=" + searchNum +
                ", details='" + details + '\'' +
                ", isLove=" + isLove +
                ", playNum=" + playNum +
                ", totalChaptersNum=" + totalChaptersNum +
                '}';
    }
}
