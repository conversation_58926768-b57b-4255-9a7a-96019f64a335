package com.android.video.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.os.Binder;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.android.video.R;
import com.android.video.download.DownloadInfo;
import com.android.video.download.DownloadListener;
import com.android.video.download.DownloadManager;
import com.android.video.ui.activity.DownloadActivity;

import java.util.HashMap;
import java.util.Map;

/**
 * 下载服务
 * <p>
 * 后台下载服务，支持前台服务模式，提供下载通知功能。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadService extends Service {

    private static final String TAG = "DownloadService";
    private static final String CHANNEL_ID = "download_channel";
    private static final int NOTIFICATION_ID = 1001;

    // Intent Actions
    public static final String ACTION_START_DOWNLOAD = "com.android.video.action.START_DOWNLOAD";
    public static final String ACTION_PAUSE_DOWNLOAD = "com.android.video.action.PAUSE_DOWNLOAD";
    public static final String ACTION_CANCEL_DOWNLOAD = "com.android.video.action.CANCEL_DOWNLOAD";
    public static final String ACTION_PAUSE_ALL = "com.android.video.action.PAUSE_ALL";
    public static final String ACTION_RESUME_ALL = "com.android.video.action.RESUME_ALL";

    // Intent Extras
    public static final String EXTRA_DOWNLOAD_ID = "download_id";
    public static final String EXTRA_DOWNLOAD_URL = "download_url";
    public static final String EXTRA_FILE_NAME = "file_name";
    public static final String EXTRA_DOWNLOAD_RECORD_ID = "download_record_id";
    public static final String EXTRA_CHAPTER_ID = "chapter_id";
    public static final String EXTRA_FILM_LANGUAGE_INFO_ID = "film_language_info_id";
    public static final String EXTRA_TITLE = "title";

    private DownloadManager downloadManager;
    private NotificationManager notificationManager;
    private final Map<String, Integer> downloadNotifications = new HashMap<>();
    private boolean isForegroundService = false;

    /**
     * 服务绑定器
     */
    public class DownloadBinder extends Binder {
        public DownloadService getService() {
            return DownloadService.this;
        }
    }

    private final DownloadBinder binder = new DownloadBinder();

    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "DownloadService created");

        downloadManager = DownloadManager.getInstance(this);
        notificationManager = (NotificationManager) getSystemService(Context.NOTIFICATION_SERVICE);

        createNotificationChannel();
        setupDownloadListener();
    }

    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        if (intent != null) {
            String action = intent.getAction();
            Log.d(TAG, "onStartCommand: " + action);

            switch (action != null ? action : "") {
                case ACTION_START_DOWNLOAD:
                    handleStartDownload(intent);
                    break;
                case ACTION_PAUSE_DOWNLOAD:
                    handlePauseDownload(intent);
                    break;
                case ACTION_CANCEL_DOWNLOAD:
                    handleCancelDownload(intent);
                    break;
                case ACTION_PAUSE_ALL:
                    handlePauseAll();
                    break;
                case ACTION_RESUME_ALL:
                    handleResumeAll();
                    break;
            }
        }

        return START_STICKY; // 服务被杀死后自动重启
    }

    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return binder;
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, "DownloadService destroyed");
        
        if (isForegroundService) {
            stopForeground(true);
        }
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                    CHANNEL_ID,
                    "视频下载",
                    NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("视频文件下载进度通知");
            channel.setSound(null, null);
            notificationManager.createNotificationChannel(channel);
        }
    }

    /**
     * 设置下载监听器
     */
    private void setupDownloadListener() {
        downloadManager.addDownloadListener(new DownloadListener.SimpleDownloadListener() {
            @Override
            public void onDownloadStart(DownloadInfo downloadInfo) {
                showDownloadNotification(downloadInfo);
                startForegroundIfNeeded();
            }

            @Override
            public void onDownloadProgress(DownloadInfo downloadInfo, long downloadedSize, long totalSize, int progress) {
                updateDownloadNotification(downloadInfo);
            }

            @Override
            public void onDownloadComplete(DownloadInfo downloadInfo) {
                showDownloadCompleteNotification(downloadInfo);
                removeDownloadNotification(downloadInfo.getDownloadId());
                stopForegroundIfNeeded();
            }

            @Override
            public void onDownloadFailed(DownloadInfo downloadInfo, String errorMessage) {
                showDownloadFailedNotification(downloadInfo, errorMessage);
                removeDownloadNotification(downloadInfo.getDownloadId());
                stopForegroundIfNeeded();
            }

            @Override
            public void onDownloadCancelled(DownloadInfo downloadInfo) {
                removeDownloadNotification(downloadInfo.getDownloadId());
                stopForegroundIfNeeded();
            }

            @Override
            public void onDownloadPause(DownloadInfo downloadInfo) {
                updateDownloadNotification(downloadInfo);
                stopForegroundIfNeeded();
            }
        });
    }

    /**
     * 处理开始下载
     */
    private void handleStartDownload(Intent intent) {
        String downloadUrl = intent.getStringExtra(EXTRA_DOWNLOAD_URL);
        String fileName = intent.getStringExtra(EXTRA_FILE_NAME);
        String downloadRecordId = intent.getStringExtra(EXTRA_DOWNLOAD_RECORD_ID);
        String chapterId = intent.getStringExtra(EXTRA_CHAPTER_ID);
        String filmLanguageInfoId = intent.getStringExtra(EXTRA_FILM_LANGUAGE_INFO_ID);
        String title = intent.getStringExtra(EXTRA_TITLE);

        if (downloadUrl != null && fileName != null) {
            DownloadInfo downloadInfo = downloadManager.addDownload(
                    downloadUrl, fileName, downloadRecordId, chapterId, filmLanguageInfoId, title);
            if (downloadInfo != null) {
                downloadManager.startDownload(downloadInfo);
            }
        }
    }

    /**
     * 处理暂停下载
     */
    private void handlePauseDownload(Intent intent) {
        String downloadId = intent.getStringExtra(EXTRA_DOWNLOAD_ID);
        if (downloadId != null) {
            downloadManager.pauseDownload(downloadId);
        }
    }

    /**
     * 处理取消下载
     */
    private void handleCancelDownload(Intent intent) {
        String downloadId = intent.getStringExtra(EXTRA_DOWNLOAD_ID);
        if (downloadId != null) {
            downloadManager.cancelDownload(downloadId);
        }
    }

    /**
     * 处理暂停所有下载
     */
    private void handlePauseAll() {
        for (DownloadInfo info : downloadManager.getDownloadsByStatus(DownloadInfo.DownloadStatus.DOWNLOADING)) {
            downloadManager.pauseDownload(info.getDownloadId());
        }
    }

    /**
     * 处理恢复所有下载
     */
    private void handleResumeAll() {
        for (DownloadInfo info : downloadManager.getDownloadsByStatus(DownloadInfo.DownloadStatus.PAUSED)) {
            downloadManager.startDownload(info.getDownloadId());
        }
    }

    /**
     * 显示下载通知
     */
    private void showDownloadNotification(DownloadInfo downloadInfo) {
        if (!downloadManager.getConfig().isEnableNotification()) {
            return;
        }

        int notificationId = getNotificationId(downloadInfo.getDownloadId());
        
        Intent intent = new Intent(this, DownloadActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_download)
                .setContentTitle("下载中")
                .setContentText(downloadInfo.getTitle() != null ? downloadInfo.getTitle() : downloadInfo.getFileName())
                .setProgress(100, downloadInfo.getProgress(), false)
                .setOngoing(true)
                .setContentIntent(pendingIntent)
                .setAutoCancel(false);

        // 添加暂停按钮
        Intent pauseIntent = new Intent(this, DownloadService.class);
        pauseIntent.setAction(ACTION_PAUSE_DOWNLOAD);
        pauseIntent.putExtra(EXTRA_DOWNLOAD_ID, downloadInfo.getDownloadId());
        PendingIntent pausePendingIntent = PendingIntent.getService(
                this, 0, pauseIntent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        builder.addAction(R.drawable.ic_pause, "暂停", pausePendingIntent);

        // 添加取消按钮
        Intent cancelIntent = new Intent(this, DownloadService.class);
        cancelIntent.setAction(ACTION_CANCEL_DOWNLOAD);
        cancelIntent.putExtra(EXTRA_DOWNLOAD_ID, downloadInfo.getDownloadId());
        PendingIntent cancelPendingIntent = PendingIntent.getService(
                this, 0, cancelIntent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);
        builder.addAction(R.drawable.ic_cancel, "取消", cancelPendingIntent);

        Notification notification = builder.build();
        notificationManager.notify(notificationId, notification);
    }

    /**
     * 更新下载通知
     */
    private void updateDownloadNotification(DownloadInfo downloadInfo) {
        if (!downloadManager.getConfig().isEnableNotification()) {
            return;
        }

        int notificationId = getNotificationId(downloadInfo.getDownloadId());
        
        String contentText = downloadInfo.getTitle() != null ? downloadInfo.getTitle() : downloadInfo.getFileName();
        String progressText = downloadInfo.getProgress() + "% - " + 
                             downloadInfo.getFormattedDownloadedSize() + "/" + 
                             downloadInfo.getFormattedFileSize();

        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_download)
                .setContentTitle(downloadInfo.getStatus() == DownloadInfo.DownloadStatus.DOWNLOADING ? "下载中" : "已暂停")
                .setContentText(contentText)
                .setSubText(progressText)
                .setProgress(100, downloadInfo.getProgress(), false)
                .setOngoing(downloadInfo.getStatus() == DownloadInfo.DownloadStatus.DOWNLOADING);

        notificationManager.notify(notificationId, builder.build());
    }

    /**
     * 显示下载完成通知
     */
    private void showDownloadCompleteNotification(DownloadInfo downloadInfo) {
        if (!downloadManager.getConfig().isEnableNotification()) {
            return;
        }

        int notificationId = getNotificationId(downloadInfo.getDownloadId());
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_download_done)
                .setContentTitle("下载完成")
                .setContentText(downloadInfo.getTitle() != null ? downloadInfo.getTitle() : downloadInfo.getFileName())
                .setAutoCancel(true);

        notificationManager.notify(notificationId, builder.build());
    }

    /**
     * 显示下载失败通知
     */
    private void showDownloadFailedNotification(DownloadInfo downloadInfo, String errorMessage) {
        if (!downloadManager.getConfig().isEnableNotification()) {
            return;
        }

        int notificationId = getNotificationId(downloadInfo.getDownloadId());
        
        NotificationCompat.Builder builder = new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_error)
                .setContentTitle("下载失败")
                .setContentText(downloadInfo.getTitle() != null ? downloadInfo.getTitle() : downloadInfo.getFileName())
                .setSubText(errorMessage)
                .setAutoCancel(true);

        notificationManager.notify(notificationId, builder.build());
    }

    /**
     * 移除下载通知
     */
    private void removeDownloadNotification(String downloadId) {
        Integer notificationId = downloadNotifications.remove(downloadId);
        if (notificationId != null) {
            notificationManager.cancel(notificationId);
        }
    }

    /**
     * 获取通知ID
     */
    private int getNotificationId(String downloadId) {
        return downloadNotifications.computeIfAbsent(downloadId, 
                k -> NOTIFICATION_ID + downloadNotifications.size());
    }

    /**
     * 如果需要则启动前台服务
     */
    private void startForegroundIfNeeded() {
        if (!isForegroundService && hasActiveDownloads()) {
            Notification notification = createForegroundNotification();
            startForeground(NOTIFICATION_ID, notification);
            isForegroundService = true;
            Log.d(TAG, "Started foreground service");
        }
    }

    /**
     * 如果需要则停止前台服务
     */
    private void stopForegroundIfNeeded() {
        if (isForegroundService && !hasActiveDownloads()) {
            stopForeground(true);
            isForegroundService = false;
            Log.d(TAG, "Stopped foreground service");
        }
    }

    /**
     * 检查是否有活跃的下载
     */
    private boolean hasActiveDownloads() {
        return !downloadManager.getDownloadsByStatus(DownloadInfo.DownloadStatus.DOWNLOADING).isEmpty();
    }

    /**
     * 创建前台服务通知
     */
    private Notification createForegroundNotification() {
        Intent intent = new Intent(this, DownloadActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
                this, 0, intent, PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE);

        return new NotificationCompat.Builder(this, CHANNEL_ID)
                .setSmallIcon(R.drawable.ic_download)
                .setContentTitle("视频下载服务")
                .setContentText("正在后台下载视频文件")
                .setContentIntent(pendingIntent)
                .setOngoing(true)
                .build();
    }

    /**
     * 获取下载管理器
     */
    public DownloadManager getDownloadManager() {
        return downloadManager;
    }

    // ========== 静态方法 ==========

    /**
     * 启动下载服务并开始下载
     */
    public static void startDownload(Context context, String downloadUrl, String fileName,
                                   String downloadRecordId, String chapterId, 
                                   String filmLanguageInfoId, String title) {
        Intent intent = new Intent(context, DownloadService.class);
        intent.setAction(ACTION_START_DOWNLOAD);
        intent.putExtra(EXTRA_DOWNLOAD_URL, downloadUrl);
        intent.putExtra(EXTRA_FILE_NAME, fileName);
        intent.putExtra(EXTRA_DOWNLOAD_RECORD_ID, downloadRecordId);
        intent.putExtra(EXTRA_CHAPTER_ID, chapterId);
        intent.putExtra(EXTRA_FILM_LANGUAGE_INFO_ID, filmLanguageInfoId);
        intent.putExtra(EXTRA_TITLE, title);
        context.startService(intent);
    }

    /**
     * 暂停下载
     */
    public static void pauseDownload(Context context, String downloadId) {
        Intent intent = new Intent(context, DownloadService.class);
        intent.setAction(ACTION_PAUSE_DOWNLOAD);
        intent.putExtra(EXTRA_DOWNLOAD_ID, downloadId);
        context.startService(intent);
    }

    /**
     * 取消下载
     */
    public static void cancelDownload(Context context, String downloadId) {
        Intent intent = new Intent(context, DownloadService.class);
        intent.setAction(ACTION_CANCEL_DOWNLOAD);
        intent.putExtra(EXTRA_DOWNLOAD_ID, downloadId);
        context.startService(intent);
    }
}
