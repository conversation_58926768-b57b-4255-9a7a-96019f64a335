package com.android.video.network;

import android.content.Context;
import android.util.Log;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.config.GlobalConfigUtils;
import com.android.video.utils.DeviceIdUtils;
import com.android.video.network.NetworkUtils;
import okhttp3.Cache;
import okhttp3.ConnectionPool;
import okhttp3.Interceptor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.logging.HttpLoggingInterceptor;
import java.io.File;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * HTTP客户端工具类 - 提供统一的HTTP客户端配置和管理
 * <AUTHOR> Team
 */
public class ApiClientUtils {

    private static final String TAG = "ApiClientUtils";
    private static OkHttpClient httpClient;
    private static Context appContext;
    private static Cache httpCache;

    /**
     * 初始化HTTP客户端
     * @param context 应用上下文
     */
    public static void initialize(Context context) {
        appContext = context.getApplicationContext();
        initializeCache();
        httpClient = createHttpClient();
        Log.d(TAG, "ApiClientUtils initialized with OkHttp cache support");
    }

    /**
     * 初始化HTTP缓存
     */
    private static void initializeCache() {
        try {
            // 创建缓存目录
            File cacheDir = new File(appContext.getCacheDir(), "http_cache");
            if (!cacheDir.exists()) {
                cacheDir.mkdirs();
            }

            // 设置缓存大小为50MB
            long cacheSize = 50L * 1024L * 1024L; // 50 MiB
            httpCache = new Cache(cacheDir, cacheSize);

            Log.d(TAG, "HTTP cache initialized: " + cacheDir.getAbsolutePath() + ", size: " + cacheSize);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize HTTP cache", e);
            httpCache = null;
        }
    }

    /**
     * 获取HTTP客户端实例
     * @return OkHttpClient实例
     */
    public static OkHttpClient getHttpClient() {
        if (httpClient == null) {
            throw new IllegalStateException("ApiClientUtils not initialized. Call initialize() first.");
        }
        return httpClient;
    }

    /**
     * 创建HTTP客户端
     * @return 配置好的OkHttpClient
     */
    private static OkHttpClient createHttpClient() {
        OkHttpClient.Builder builder = new OkHttpClient.Builder();

        // 设置超时时间
        builder.connectTimeout(GlobalConfigUtils.getConnectTimeoutMillis(), TimeUnit.MILLISECONDS)
               .readTimeout(GlobalConfigUtils.getReadTimeoutMillis(), TimeUnit.MILLISECONDS)
               .writeTimeout(GlobalConfigUtils.getWriteTimeoutMillis(), TimeUnit.MILLISECONDS);

        // 配置连接池以避免Socket关闭问题
        ConnectionPool connectionPool = new ConnectionPool(10, 5, TimeUnit.MINUTES);
        builder.connectionPool(connectionPool);

        // 启用连接重用和失败重试
        builder.retryOnConnectionFailure(true);

        // 设置HTTP缓存
        if (httpCache != null) {
            builder.cache(httpCache);
            Log.d(TAG, "OkHttp cache enabled");
        }

        // 添加请求头拦截器
        builder.addInterceptor(new HeaderInterceptor());

        // 添加离线缓存拦截器（应用拦截器）
        builder.addInterceptor(new OfflineCacheInterceptor(appContext));

        // 添加强制缓存拦截器（网络拦截器）
        builder.addNetworkInterceptor(new ForceCacheInterceptor());

        // 添加日志拦截器（仅在DEBUG模式下）
        if (EnvironmentConfigUtils.isDebugMode()) {
            HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
            loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.BASIC); // 改为BASIC避免过多日志
            builder.addInterceptor(loggingInterceptor);

            // 添加缓存调试拦截器
            builder.addInterceptor(new CacheDebugInterceptor());

            Log.d(TAG, "HTTP logging and cache debug interceptors enabled");
        }

        // 添加重试拦截器
        builder.addInterceptor(new RetryInterceptor());

        return builder.build();
    }

    /**
     * 离线缓存拦截器 - 处理离线情况下的缓存
     */
    private static class OfflineCacheInterceptor implements Interceptor {
        private Context context;

        public OfflineCacheInterceptor(Context context) {
            this.context = context;
        }

        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();

            // 只处理GET请求
            if (!"GET".equals(request.method())) {
                return chain.proceed(request);
            }

            // 检查网络状态
            if (!NetworkUtils.isNetworkAvailable(context)) {
                // 离线时强制使用缓存
                request = request.newBuilder()
                        .header("Cache-Control", "public, only-if-cached, max-stale=" + 60 * 60 * 24 * 7) // 7天
                        .build();
                Log.d(TAG, "Offline mode: using cache for " + request.url());
            }

            return chain.proceed(request);
        }
    }

    /**
     * 强制缓存拦截器 - 强制为响应添加缓存头
     */
    private static class ForceCacheInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = chain.proceed(request);

            // 只对GET请求进行缓存
            if (!"GET".equals(request.method())) {
                return response;
            }

            String url = request.url().toString();
            String cacheControl = getCacheControlForUrl(url);

            Log.d(TAG, "Setting cache control for " + url + ": " + cacheControl);

            // 强制设置缓存控制头，覆盖服务器设置
            return response.newBuilder()
                    .removeHeader("Pragma")
                    .removeHeader("Cache-Control")
                    .header("Cache-Control", cacheControl)
                    .build();
        }

        private String getCacheControlForUrl(String url) {
            if (url.contains("/banner") || url.contains("/carousel")) {
                return "public, max-age=7200"; // Banner缓存2小时
            } else if (url.contains("/featured") || url.contains("/recommend")) {
                return "public, max-age=3600"; // Featured缓存1小时
            } else if (url.contains("/category") || url.contains("/tag")) {
                return "public, max-age=21600"; // 分类缓存6小时
            } else if (url.contains("/video") || url.contains("/movie") || url.contains("/series")) {
                return "public, max-age=1800"; // 视频列表缓存30分钟
            } else if (url.contains("/user") || url.contains("/profile")) {
                return "public, max-age=86400"; // 用户信息缓存24小时
            } else {
                return "public, max-age=3600"; // 默认缓存1小时
            }
        }
    }

    /**
     * 请求头拦截器
     */
    private static class HeaderInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request originalRequest = chain.request();
            
            Request.Builder requestBuilder = originalRequest.newBuilder()
                    .header("Content-Type", GlobalConfigUtils.CONTENT_TYPE_JSON)
                    .header("User-Agent", GlobalConfigUtils.getDefaultUserAgent());

            // 添加设备ID
            if (appContext != null) {
                String deviceId = DeviceIdUtils.getDeviceId(appContext);
                requestBuilder.header(GlobalConfigUtils.HEADER_DEVICE_ID, deviceId);
            }

            // 自动添加访问令牌
            String token = com.android.video.utils.ApiHeaderUtils.getCurrentAccessToken();
            if (token != null && !token.trim().isEmpty()) {
                requestBuilder.header(com.android.video.constants.BaseApiConstantsUtils.HEADER_ACCESS_TOKEN, token);
                android.util.Log.d("HeaderInterceptor", "Added token to request: " + maskToken(token));
            } else {
                android.util.Log.w("HeaderInterceptor", "No token available for request: " + originalRequest.url());
                // 对于需要token的接口，记录警告
                String url = originalRequest.url().toString();
                if (!url.contains("/init") && !url.contains("/public")) {
                    android.util.Log.w("HeaderInterceptor", "⚠️ API请求缺少token，可能导致认证失败: " + url);
                }
            }

            Request newRequest = requestBuilder.build();
            return chain.proceed(newRequest);
        }

        /**
         * 脱敏处理token用于日志输出
         */
        private String maskToken(String token) {
            if (token == null || token.length() <= 8) {
                return "***";
            }
            return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
        }
    }

    /**
     * 重试拦截器
     */
    private static class RetryInterceptor implements Interceptor {
        @Override
        public Response intercept(Chain chain) throws IOException {
            Request request = chain.request();
            Response response = null;
            IOException lastException = null;

            for (int i = 0; i <= GlobalConfigUtils.MAX_RETRY_COUNT; i++) {
                try {
                    response = chain.proceed(request);
                    
                    // 如果响应成功，直接返回
                    if (response.isSuccessful()) {
                        return response;
                    }
                    
                    // 如果是客户端错误（4xx），不重试
                    if (response.code() >= 400 && response.code() < 500) {
                        return response;
                    }
                    
                    // 服务器错误（5xx）或网络错误，进行重试
                    if (i < GlobalConfigUtils.MAX_RETRY_COUNT) {
                        Log.w(TAG, "Request failed, retrying... Attempt: " + (i + 1));
                        if (response != null) {
                            response.close();
                        }
                        
                        // 等待一段时间再重试
                        try {
                            Thread.sleep(1000 * (i + 1)); // 递增等待时间
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                    
                } catch (IOException e) {
                    lastException = e;
                    Log.w(TAG, "Network error, retrying... Attempt: " + (i + 1) + ", Error: " + e.getMessage());
                    
                    if (i < GlobalConfigUtils.MAX_RETRY_COUNT) {
                        try {
                            Thread.sleep(1000 * (i + 1));
                        } catch (InterruptedException ie) {
                            Thread.currentThread().interrupt();
                            break;
                        }
                    }
                }
            }

            // 如果所有重试都失败了
            if (response != null) {
                return response;
            } else if (lastException != null) {
                throw lastException;
            } else {
                throw new IOException("Request failed after " + GlobalConfigUtils.MAX_RETRY_COUNT + " retries");
            }
        }
    }

    /**
     * 获取访问令牌（已弃用，现在使用TokenManager）
     * @deprecated 使用TokenManager.getCurrentToken()替代
     * @return 访问令牌
     */
    @Deprecated
    private static String getAccessToken() {
        Log.w(TAG, "getAccessToken() 已弃用，现在通过HeaderInterceptor自动添加token");
        return com.android.video.utils.ApiHeaderUtils.getCurrentAccessToken();
    }

    /**
     * 设置访问令牌（已弃用，现在使用TokenManager）
     * @deprecated 使用TokenManager.updateToken()替代
     * @param token 访问令牌
     */
    @Deprecated
    public static void setAccessToken(String token) {
        Log.w(TAG, "setAccessToken() 已弃用，现在使用TokenManager管理token");
        com.android.video.utils.ApiHeaderUtils.setAccessToken(token);
    }

    /**
     * 清除访问令牌（已弃用，现在使用TokenManager）
     * @deprecated 使用TokenManager.clearToken()替代
     */
    @Deprecated
    public static void clearAccessToken() {
        Log.w(TAG, "clearAccessToken() 已弃用，现在使用TokenManager管理token");
        com.android.video.utils.ApiHeaderUtils.resetAccessToken();
    }

    /**
     * 获取客户端配置信息（用于调试）
     * @return 配置信息字符串
     */
    public static String getClientInfo() throws IOException {
        StringBuilder info = new StringBuilder();
        info.append("HTTP Client Configuration:\n");
        info.append("Connect Timeout: ").append(GlobalConfigUtils.CONNECT_TIMEOUT_SECONDS).append("s\n");
        info.append("Read Timeout: ").append(GlobalConfigUtils.READ_TIMEOUT_SECONDS).append("s\n");
        info.append("Write Timeout: ").append(GlobalConfigUtils.WRITE_TIMEOUT_SECONDS).append("s\n");
        info.append("Max Retry Count: ").append(GlobalConfigUtils.MAX_RETRY_COUNT).append("\n");
        info.append("Debug Mode: ").append(EnvironmentConfigUtils.isDebugMode() ? "Enabled" : "Disabled").append("\n");

        // 添加缓存信息
        if (httpCache != null) {
            info.append("Cache Status: Enabled\n");
            info.append("Cache Directory: ").append(httpCache.directory().getAbsolutePath()).append("\n");
            info.append("Cache Size: ").append(httpCache.size()).append(" bytes\n");
            info.append("Cache Max Size: ").append(httpCache.maxSize()).append(" bytes");
        } else {
            info.append("Cache Status: Disabled");
        }

        return info.toString();
    }

    // ========== 缓存管理方法 ==========

    /**
     * 获取HTTP缓存实例
     */
    public static Cache getHttpCache() {
        return httpCache;
    }

    /**
     * 清除所有缓存
     */
    public static void clearAllCache() {
        if (httpCache != null) {
            try {
                httpCache.evictAll();
                Log.d(TAG, "All cache cleared");
            } catch (IOException e) {
                Log.e(TAG, "Failed to clear cache", e);
            }
        }
    }

    /**
     * 获取缓存大小
     */
    public static long getCacheSize() {
        if (httpCache != null) {
            try {
                return httpCache.size();
            } catch (IOException e) {
                Log.e(TAG, "Failed to get cache size", e);
                return 0;
            }
        }
        return 0;
    }

    /**
     * 获取缓存目录
     */
    public static File getCacheDirectory() {
        return httpCache != null ? httpCache.directory() : null;
    }

    /**
     * 删除缓存
     */
    public static void deleteCache() {
        if (httpCache != null) {
            try {
                httpCache.delete();
                Log.d(TAG, "Cache deleted");
            } catch (IOException e) {
                Log.e(TAG, "Failed to delete cache", e);
            }
        }
    }

    /**
     * 强制网络请求（跳过缓存）
     */
    public static Request.Builder forceNetworkRequest(Request.Builder builder) {
        return builder.header("Cache-Control", "no-cache");
    }

    /**
     * 强制使用缓存（离线模式）
     */
    public static Request.Builder forceCacheRequest(Request.Builder builder) {
        return builder.header("Cache-Control", "only-if-cached, max-stale=" + Integer.MAX_VALUE);
    }
}
