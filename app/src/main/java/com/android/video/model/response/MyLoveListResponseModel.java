package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.model.MyLoveDataModel;

/**
 * 我的喜爱列表API响应模型
 * <AUTHOR> Team
 */
public class MyLoveListResponseModel {
    
    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 响应数据
     */
    @SerializedName("data")
    private MyLoveDataModel data;

    public MyLoveListResponseModel() {
    }

    public MyLoveListResponseModel(String code, String message, MyLoveDataModel data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MyLoveDataModel getData() {
        return data;
    }

    public void setData(MyLoveDataModel data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 检查是否有数据
     * @return 是否有数据
     */
    public boolean hasData() {
        return data != null && !data.isEmpty();
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null && !message.trim().isEmpty() ? message : "Unknown error";
    }

    @Override
    public String toString() {
        return "MyLoveListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
