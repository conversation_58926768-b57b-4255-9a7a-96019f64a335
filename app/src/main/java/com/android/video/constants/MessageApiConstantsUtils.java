package com.android.video.constants;

/**
 * 消息推送模块API常量类 - 定义消息相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理消息推送相关的API常量，
 * 包括消息列表查询、消息详情查询等功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的消息推送功能：
 * <ul>
 *   <li>消息列表分页查询</li>
 *   <li>消息详情查询</li>
 *   <li>支持不同消息类型筛选</li>
 *   <li>支持已读/未读状态</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 消息类型说明：
 * <ul>
 *   <li>messageType=1：系统消息</li>
 *   <li>messageType=2：订阅消息</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取消息列表API URL
 * String url = MessageApiConstantsUtils.buildMessageListUrl(baseUrl, 1, 10);
 * 
 * // 构建获取消息详情API URL
 * String detailUrl = MessageApiConstantsUtils.buildMessageDetailUrl(baseUrl, sendMessageId);
 * 
 * // 使用请求参数
 * Map&lt;String, String&gt; params = new HashMap&lt;&gt;();
 * params.put(MessageApiConstantsUtils.PARAM_PAGE, "1");
 * params.put(MessageApiConstantsUtils.PARAM_SIZE, "10");
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class MessageApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 消息推送相关API路径常量 ==========

    /**
     * 获取消息列表API路径
     * <p>
     * 用于获取用户的消息推送列表，支持分页查询。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>page: 页码，从1开始</li>
     *   <li>size: 每页数量，建议10</li>
     * </ul>
     * </p>
     */
    public static final String API_GET_MESSAGE_LIST = "/app/myMenter/getMessageList";

    /**
     * 获取消息详情API路径
     * <p>
     * 用于获取单个消息的详细信息。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>sendMessageId: 消息推送ID，必填</li>
     * </ul>
     * </p>
     */
    public static final String API_GET_MESSAGE_DETAIL = "/app/myMenter/getMessageDetail";

    // ========== 请求参数常量 ==========

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 消息推送ID参数名
     */
    public static final String PARAM_SEND_MESSAGE_ID = "sendMessageId";

    // ========== 消息类型常量 ==========

    /**
     * 消息类型：系统消息
     */
    public static final int MESSAGE_TYPE_SYSTEM = 1;

    /**
     * 消息类型：订阅消息
     */
    public static final int MESSAGE_TYPE_SUBSCRIPTION = 2;

    // ========== 已读状态常量 ==========

    /**
     * 未读状态
     */
    public static final int READ_STATUS_UNREAD = 0;

    /**
     * 已读状态
     */
    public static final int READ_STATUS_READ = 1;

    // ========== 语言类型常量 ==========

    /**
     * 语言类型：英语
     */
    public static final int LANGUAGE_TYPE_ENGLISH = 1;

    /**
     * 语言类型：俄语
     */
    public static final int LANGUAGE_TYPE_RUSSIAN = 2;

    // ========== 工具方法 ==========

    /**
     * 构建消息列表查询URL
     * <p>
     * 根据基础URL和分页参数构建完整的消息列表查询URL。
     * </p>
     * 
     * @param baseUrl API基础URL
     * @param page 页码，从1开始
     * @param size 每页数量
     * @return 完整的API URL
     * 
     * @example
     * <pre>
     * String url = buildMessageListUrl("https://api.example.com/v1", 1, 10);
     * // 返回: "https://api.example.com/v1/app/myMenter/getMessageList?page=1&size=10"
     * </pre>
     */
    public static String buildMessageListUrl(String baseUrl, int page, int size) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }
        
        if (page < 1) {
            throw new IllegalArgumentException("Page must be greater than 0");
        }
        
        if (size < 1 || size > 50) {
            throw new IllegalArgumentException("Size must be between 1 and 50");
        }
        
        return buildApiUrl(baseUrl, API_GET_MESSAGE_LIST) + 
               "?" + PARAM_PAGE + "=" + page + 
               "&" + PARAM_SIZE + "=" + size;
    }

    /**
     * 构建消息详情查询URL
     * <p>
     * 根据基础URL和消息推送ID构建完整的消息详情查询URL。
     * </p>
     * 
     * @param baseUrl API基础URL
     * @param sendMessageId 消息推送ID
     * @return 完整的API URL
     * 
     * @example
     * <pre>
     * String url = buildMessageDetailUrl("https://api.example.com/v1", "d88f21ef731f4583af988aea80a2476b");
     * // 返回: "https://api.example.com/v1/app/myMenter/getMessageDetail?sendMessageId=d88f21ef731f4583af988aea80a2476b"
     * </pre>
     */
    public static String buildMessageDetailUrl(String baseUrl, String sendMessageId) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }
        
        if (sendMessageId == null || sendMessageId.trim().isEmpty()) {
            throw new IllegalArgumentException("Send message ID cannot be null or empty");
        }
        
        return buildApiUrl(baseUrl, API_GET_MESSAGE_DETAIL) + 
               "?" + PARAM_SEND_MESSAGE_ID + "=" + sendMessageId;
    }

    /**
     * 检查是否为消息相关的API路径
     * <p>
     * 判断给定的API路径是否属于消息推送模块。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是消息相关API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isMessage = isMessageApi("/app/myMenter/getMessageList");   // true
     * boolean isMessage = isMessageApi("/app/myMenter/getMessageDetail"); // true
     * boolean isMessage = isMessageApi("/app/customer/info");             // false
     * </pre>
     */
    public static boolean isMessageApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }
        
        return apiPath.equals(API_GET_MESSAGE_LIST) ||
               apiPath.equals(API_GET_MESSAGE_DETAIL);
    }

    /**
     * 获取消息类型描述
     * <p>
     * 根据消息类型代码返回对应的中文描述信息。
     * </p>
     * 
     * @param messageType 消息类型代码
     * @return 消息类型的中文描述
     * 
     * @example
     * <pre>
     * String desc = getMessageTypeDescription(1);  // "系统消息"
     * String desc = getMessageTypeDescription(2);  // "订阅消息"
     * </pre>
     */
    public static String getMessageTypeDescription(int messageType) {
        switch (messageType) {
            case MESSAGE_TYPE_SYSTEM:
                return "系统消息";
            case MESSAGE_TYPE_SUBSCRIPTION:
                return "订阅消息";
            default:
                return "未知消息类型";
        }
    }

    /**
     * 获取已读状态描述
     * <p>
     * 根据已读状态代码返回对应的中文描述信息。
     * </p>
     * 
     * @param readStatus 已读状态代码
     * @return 已读状态的中文描述
     * 
     * @example
     * <pre>
     * String desc = getReadStatusDescription(0);  // "未读"
     * String desc = getReadStatusDescription(1);  // "已读"
     * </pre>
     */
    public static String getReadStatusDescription(int readStatus) {
        switch (readStatus) {
            case READ_STATUS_UNREAD:
                return "未读";
            case READ_STATUS_READ:
                return "已读";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取语言类型描述
     * <p>
     * 根据语言类型代码返回对应的中文描述信息。
     * </p>
     * 
     * @param languageType 语言类型代码
     * @return 语言类型的中文描述
     * 
     * @example
     * <pre>
     * String desc = getLanguageTypeDescription(1);  // "英语"
     * String desc = getLanguageTypeDescription(2);  // "俄语"
     * </pre>
     */
    public static String getLanguageTypeDescription(Integer languageType) {
        if (languageType == null) {
            return "未知语言";
        }
        
        switch (languageType) {
            case LANGUAGE_TYPE_ENGLISH:
                return "英语";
            case LANGUAGE_TYPE_RUSSIAN:
                return "俄语";
            default:
                return "未知语言";
        }
    }

    /**
     * 获取消息API的描述信息
     * <p>
     * 根据API路径返回对应的中文描述信息，
     * 便于调试和日志记录。
     * </p>
     * 
     * @param apiPath API路径
     * @return API的中文描述，未知API返回"未知消息接口"
     * 
     * @example
     * <pre>
     * String desc = getMessageApiDescription("/app/myMenter/getMessageList");   // "获取消息列表"
     * String desc = getMessageApiDescription("/app/myMenter/getMessageDetail"); // "获取消息详情"
     * String desc = getMessageApiDescription("/unknown/api");                   // "未知消息接口"
     * </pre>
     */
    public static String getMessageApiDescription(String apiPath) {
        if (apiPath == null) {
            return "UNDEFINED_ERROR";
        }
        
        switch (apiPath) {
            case API_GET_MESSAGE_LIST:
                return "获取消息列表";
            case API_GET_MESSAGE_DETAIL:
                return "获取消息详情";
            default:
                return "UNDEFINED_MESSAGE_ERROR";
        }
    }
}
