package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import java.util.List;

/**
 * 剧集区间选择适配器
 */
public class EpisodeRangeAdapter extends RecyclerView.Adapter<EpisodeRangeAdapter.RangeViewHolder> {

    private List<String> ranges;
    private int selectedPosition = 0;
    private OnRangeClickListener listener;

    public interface OnRangeClickListener {
        void onRangeClick(int position);
    }

    public EpisodeRangeAdapter(List<String> ranges, OnRangeClickListener listener) {
        this.ranges = ranges;
        this.listener = listener;
    }

    @NonNull
    @Override
    public RangeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_episode_range, parent, false);
        return new RangeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RangeViewHolder holder, int position) {
        String range = ranges.get(position);
        holder.textRange.setText(range);
        
        // 显示/隐藏下划线
        boolean isSelected = position == selectedPosition;
        holder.viewUnderline.setVisibility(isSelected ? View.VISIBLE : View.GONE);
        
        // 设置点击事件
        holder.itemView.setOnClickListener(v -> {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            notifyItemChanged(oldPosition);
            notifyItemChanged(selectedPosition);
            
            if (listener != null) {
                listener.onRangeClick(position);
            }
        });
    }

    @Override
    public int getItemCount() {
        return ranges != null ? ranges.size() : 0;
    }

    public void setSelectedPosition(int position) {
        int oldPosition = selectedPosition;
        selectedPosition = position;
        notifyItemChanged(oldPosition);
        notifyItemChanged(selectedPosition);
    }

    public void updateRanges(List<String> newRanges) {
        this.ranges = newRanges;
        selectedPosition = 0;
        notifyDataSetChanged();
    }

    static class RangeViewHolder extends RecyclerView.ViewHolder {
        TextView textRange;
        View viewUnderline;

        RangeViewHolder(@NonNull View itemView) {
            super(itemView);
            textRange = itemView.findViewById(R.id.text_range);
            viewUnderline = itemView.findViewById(R.id.view_range_underline);
        }
    }
}
