package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.adapter.MessageAdapter;
import com.android.video.model.MessageItemModel;
import com.android.video.model.MessageNotification;
import com.android.video.model.response.MessageListResponseModel;
import com.android.video.network.MessageApiService;
import com.android.video.network.NetworkUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
public class MessageActivity extends BaseFullScreenActivity {

    private static final String TAG = "MessageActivity";
    private static final int PAGE_SIZE = 10; // 每页数量

    private ImageView ivBack;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView rvMessages;
    private MessageAdapter messageAdapter;

    private MessageApiService messageApiService;
    private List<MessageItemModel> messageList;

    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        try {
            Log.d(TAG, "MessageActivity onCreate started");
            setContentView(R.layout.activity_message);

            initViews();
            setupRecyclerView();
            setupSwipeRefresh();
            setupScrollListener();

            // 初始化API服务
            messageApiService = new MessageApiService();
            Log.d(TAG, "MessageApiService initialized successfully");

            // 加载第一页数据
            loadMessageData(true);

            Log.d(TAG, "MessageActivity onCreate completed");
        } catch (Exception e) {
            Log.e(TAG, "Error in MessageActivity onCreate", e);
            Toast.makeText(this, "初始化失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
            finish();
        }
    }

    private void initViews() {
        try {
            Log.d(TAG, "Initializing views");

            ivBack = findViewById(R.id.iv_back);
            swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
            rvMessages = findViewById(R.id.rv_messages);

            if (ivBack == null || swipeRefreshLayout == null || rvMessages == null) {
                throw new RuntimeException("Failed to find required views");
            }

            // 返回按钮点击事件
            ivBack.setOnClickListener(v -> finish());

            Log.d(TAG, "Views initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error initializing views", e);
            throw e;
        }
    }

    private void setupRecyclerView() {
        messageList = new ArrayList<>();

        // 设置LinearLayoutManager
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvMessages.setLayoutManager(layoutManager);

        // 初始化适配器
        messageAdapter = new MessageAdapter(messageList, new MessageAdapter.OnMessageClickListener() {
            @Override
            public void onGoButtonClick(MessageItemModel message) {
                Log.d(TAG, "点击GO按钮，sendMessageId: " + message.getSendMessageId());
                Log.d(TAG, "消息标题: " + message.getMessageTitle());
                Log.d(TAG, "消息类型: " + message.getMessageType());
                Log.d(TAG, "是否有视频信息: " + message.hasVideoInfo());
                Log.d(TAG, "filmLanguageInfoId: " + message.getFilmLanguageInfoId());
                Log.d(TAG, "filmTitle: " + message.getFilmTitle());

                // 检查消息是否包含视频信息
                if (message.hasVideoInfo() && message.getFilmLanguageInfoId() != null && !message.getFilmLanguageInfoId().isEmpty()) {
                    // 有视频信息，跳转到视频详情页
                    Intent intent = new Intent(MessageActivity.this, VideoDetailActivity.class);
                    intent.putExtra("filmLanguageInfoId", message.getFilmLanguageInfoId());
                    intent.putExtra("filmTitle", message.getFilmTitle()); // 传递标题用于显示
                    startActivity(intent);
                    Log.d(TAG, "跳转到视频详情页，filmLanguageInfoId: " + message.getFilmLanguageInfoId() + ", 视频标题: " + message.getFilmTitle());
                } else {
                    // 无视频信息，跳转到消息详情页面
                    Intent intent = new Intent(MessageActivity.this, MessageDetailActivity.class);
                    intent.putExtra("sendMessageId", message.getSendMessageId());
                    intent.putExtra("messageTitle", message.getMessageTitle());
                    intent.putExtra("createTime", message.getCreateTime());
                    startActivity(intent);
                    Log.d(TAG, "跳转到消息详情页");
                }
            }
        });
        rvMessages.setAdapter(messageAdapter);
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            // 设置刷新颜色
            swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );

            // 设置下拉刷新监听
            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "下拉刷新触发");
                refreshData();
            });
        }
    }

    /**
     * 设置滚动监听实现上拉加载更多
     */
    private void setupScrollListener() {
        if (rvMessages != null) {
            rvMessages.addOnScrollListener(new RecyclerView.OnScrollListener() {
                @Override
                public void onScrolled(@NonNull RecyclerView recyclerView, int dx, int dy) {
                    super.onScrolled(recyclerView, dx, dy);

                    if (dy > 0) { // 向下滚动
                        LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                        if (layoutManager != null) {
                            int visibleItemCount = layoutManager.getChildCount();
                            int totalItemCount = layoutManager.getItemCount();
                            int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                            // 接近底部时加载更多数据
                            if ((visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                                loadMoreData();
                            }
                        }
                    }
                }
            });
        }
    }

    /**
     * 刷新数据（下拉刷新）
     */
    private void refreshData() {
        currentPage = 1;
        hasMoreData = true;
        loadMessageData(true);
    }

    /**
     * 加载更多数据（上拉加载）
     */
    private void loadMoreData() {
        if (hasMoreData && !isLoading) {
            currentPage++;
            loadMessageData(false);
        }
    }

    /**
     * 加载消息数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadMessageData(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        // 检查网络连接
        if (!NetworkUtils.isNetworkAvailable(this)) {
            Log.w(TAG, "网络不可用");
            showToast("网络连接不可用，请检查网络设置");
            return;
        }

        isLoading = true;

        // 显示loading状态
        if (isRefresh && swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }

        Log.d(TAG, "开始加载消息数据，页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        messageApiService.getMessageList(currentPage, PAGE_SIZE, new MessageApiService.ApiCallback<MessageListResponseModel>() {
            @Override
            public void onSuccess(MessageListResponseModel response) {
                // 确保在主线程中更新UI
                runOnUiThread(() -> {
                    isLoading = false;
                    stopRefreshAnimation();

                    Log.d(TAG, "消息数据加载成功: " + response.toString());

                    if (response.getData() != null && response.getData().getRecords() != null) {
                        List<MessageItemModel> newMessages = response.getData().getRecords();

                        if (isRefresh) {
                            // 刷新：清空现有数据，添加新数据
                            messageList.clear();
                            messageList.addAll(newMessages);
                            messageAdapter.notifyDataSetChanged();
                        } else {
                            // 加载更多：在现有数据后添加新数据
                            int oldSize = messageList.size();
                            messageList.addAll(newMessages);
                            messageAdapter.notifyItemRangeInserted(oldSize, newMessages.size());
                        }

                        // 检查是否还有更多数据
                        hasMoreData = newMessages.size() >= PAGE_SIZE &&
                                      currentPage < response.getData().getPages();

                        Log.d(TAG, "数据更新完成，当前总数: " + messageList.size() +
                                  ", 是否还有更多: " + hasMoreData);
                    } else {
                        Log.w(TAG, "响应数据为空");
                        if (isRefresh) {
                            showToast("暂无消息数据");
                        }
                    }
                });
            }

            @Override
            public void onError(String error) {
                // 确保在主线程中更新UI
                runOnUiThread(() -> {
                    isLoading = false;
                    stopRefreshAnimation();

                    Log.e(TAG, "消息数据加载失败: " + error);
                    showToast("加载失败: " + error);

                    // 如果是第一页加载失败，可以显示空状态或重试按钮
                    if (currentPage == 1 && messageList.isEmpty()) {
                        // 可以在这里显示空状态视图
                    }
                });
            }
        });
    }

    /**
     * 停止刷新动画
     */
    private void stopRefreshAnimation() {
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        // 检查是否在主线程中
        if (Thread.currentThread() == getMainLooper().getThread()) {
            Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
        } else {
            runOnUiThread(() -> Toast.makeText(this, message, Toast.LENGTH_SHORT).show());
        }
    }



}
