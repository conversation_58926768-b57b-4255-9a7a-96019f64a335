package com.android.video.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.View;
import android.view.Window;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.android.video.R;
import com.android.video.utils.UserSessionUtils;

public class LanguageSelectionDialog extends Dialog {

    private LinearLayout optionEnglish;
    private LinearLayout optionRussian;
    private LinearLayout optionKaza;
    private ImageView ivEnglishCheck;
    private ImageView ivRussianCheck;
    private ImageView ivKazaCheck;
    private TextView btnCancel;
    private OnLanguageSelectedListener listener;
    private int currentLanguageType;

    public interface OnLanguageSelectedListener {
        void onLanguageSelected(int languageType);
    }

    public LanguageSelectionDialog(Context context) {
        super(context, R.style.CustomDialog);
        this.currentLanguageType = UserSessionUtils.getLanguageType(context);
    }

    public LanguageSelectionDialog(Context context, OnLanguageSelectedListener listener) {
        super(context, R.style.CustomDialog);
        this.listener = listener;
        this.currentLanguageType = UserSessionUtils.getLanguageType(context);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_language_selection);

        // 设置窗口属性
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.WRAP_CONTENT;
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(params);
            window.setDimAmount(0.5f);
        }

        initViews();
        setupClickListeners();
        updateSelection();

        // 添加弹窗进入动画
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleIn(dialogView);
        }
    }

    private void initViews() {
        optionEnglish = findViewById(R.id.option_english);
        optionRussian = findViewById(R.id.option_russian);
        optionKaza = findViewById(R.id.option_kaza);
        ivEnglishCheck = findViewById(R.id.iv_english_check);
        ivRussianCheck = findViewById(R.id.iv_russian_check);
        ivKazaCheck = findViewById(R.id.iv_kaza_check);
        btnCancel = findViewById(R.id.btn_cancel);
    }

    private void setupClickListeners() {
        // English option
        optionEnglish.setOnClickListener(v -> {
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);
            if (listener != null) {
                listener.onLanguageSelected(1); // English
            }
            dismissWithAnimation();
        });

        // Russian option
        optionRussian.setOnClickListener(v -> {
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);
            if (listener != null) {
                listener.onLanguageSelected(2); // Russian
            }
            dismissWithAnimation();
        });

        // Kaza option
        optionKaza.setOnClickListener(v -> {
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);
            if (listener != null) {
                listener.onLanguageSelected(3); // Kaza
            }
            dismissWithAnimation();
        });

        // Cancel button
        btnCancel.setOnClickListener(v -> {
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);
            dismissWithAnimation();
        });
    }

    private void updateSelection() {
        // Hide all check icons first
        ivEnglishCheck.setVisibility(View.GONE);
        ivRussianCheck.setVisibility(View.GONE);
        ivKazaCheck.setVisibility(View.GONE);

        // Show check icon for current language
        if (currentLanguageType == 1) {
            ivEnglishCheck.setVisibility(View.VISIBLE);
        } else if (currentLanguageType == 2) {
            ivRussianCheck.setVisibility(View.VISIBLE);
        } else if (currentLanguageType == 3) {
            ivKazaCheck.setVisibility(View.VISIBLE);
        }
    }

    public void setOnLanguageSelectedListener(OnLanguageSelectedListener listener) {
        this.listener = listener;
    }

    /**
     * 带动画的关闭弹窗
     */
    private void dismissWithAnimation() {
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleOut(dialogView, () -> {
                LanguageSelectionDialog.super.dismiss();
            });
        } else {
            super.dismiss();
        }
    }
}
