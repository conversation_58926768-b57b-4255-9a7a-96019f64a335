<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="@dimen/home_video_item_spacing_vertical"
    android:clickable="true"
    android:focusable="true">



    <!-- 中间视频海报容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_container"
        android:layout_width="@dimen/home_popular_series_item_width"
        android:layout_height="@dimen/home_popular_series_item_height"
        android:layout_marginStart="16dp"
        android:background="@drawable/home_popular_series_border_new"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent">

        <!-- 海报 -->
        <ImageView
            android:id="@+id/iv_video_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/home_popular_series_item_border_width"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/home_popular_series_inner_border"
            android:clipToOutline="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />



    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 右侧信息区域 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_info_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/home_video_item_spacing_horizontal"
        android:layout_marginEnd="@dimen/home_video_item_spacing_horizontal"
        app:layout_constraintTop_toTopOf="@id/cl_poster_container"
        app:layout_constraintBottom_toBottomOf="@id/cl_poster_container"
        app:layout_constraintStart_toEndOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 视频标题 -->
        <TextView
            android:id="@+id/tv_video_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="喜洋洋与灰太狼"
            android:textColor="@color/white"
            android:textSize="@dimen/home_video_title_text_size"
            android:fontFamily="sans-serif-medium"
            android:maxLines="2"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 视频介绍 -->
        <TextView
            android:id="@+id/tv_video_description"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/home_video_description_margin_top"
            android:text="精彩的动画片，适合全家观看"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/home_video_description_text_size"
            android:fontFamily="sans-serif-light"
            android:maxLines="3"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/tv_video_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <!-- 播放热度标签 - 位于海报右侧、以矩形底部为基准上方10dp -->
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_play_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <!-- 播放图标 -->
            <ImageView
                android:id="@+id/iv_play_count_icon"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:src="@drawable/home_ic_view"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/tv_play_count"
                app:layout_constraintHorizontal_chainStyle="packed" />

            <!-- 播放次数文本 -->
            <TextView
                android:id="@+id/tv_play_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:text="1.2M"
                android:textColor="#CCFFFFFF"
                android:textSize="@dimen/home_play_count_text_size"
                android:fontFamily="sans-serif-light"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@id/iv_play_count_icon"
                app:layout_constraintEnd_toEndOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
