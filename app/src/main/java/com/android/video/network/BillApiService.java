package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.constants.BillApiConstantsUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.model.response.BillOrderListResponse;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 账单API服务类 - 处理账单相关的网络请求
 * <p>
 * 提供账单中心相关的API调用功能，包括订单列表查询等。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class BillApiService {

    private static final String TAG = "BillApiService";
    
    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    /**
     * API回调接口
     */
    public interface ApiCallback<T> {
        /**
         * 请求成功回调
         * @param data 响应数据
         */
        void onSuccess(T data);

        /**
         * 请求失败回调
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 构造方法
     */
    public BillApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
        this.gson = new GsonBuilder()
                .setLenient()
                .create();
    }

    /**
     * 获取订单列表
     * <p>
     * 根据订单类型和分页参数获取订单列表数据。
     * </p>
     * 
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param orderType 订单类型（1=剧集解锁，2=VIP购买，3=积分包购买）
     * @param callback 回调接口
     */
    public void getOrderList(int page, int size, int orderType, ApiCallback<BillOrderListResponse> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (page < 1) {
            callback.onError("页码必须大于0");
            return;
        }
        if (size < 1 || size > 50) {
            callback.onError("每页数量必须在1-50之间");
            return;
        }
        if (orderType < 1 || orderType > 3) {
            callback.onError("订单类型必须在1-3之间");
            return;
        }

        // 构建请求URL
        String apiUrl = BillApiConstantsUtils.buildOrderListUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            page,
            size,
            orderType
        );

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "Requesting order list: " + apiUrl);
        Log.d(TAG, "Order type: " + orderType + " (" + BillApiConstantsUtils.getOrderTypeDescription(orderType) + ")");

        // 执行异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "Request failed", e);
                mainHandler.post(() -> callback.onError("网络请求失败: " + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (!response.isSuccessful()) {
                        Log.e(TAG, "Request unsuccessful: " + response.code());
                        mainHandler.post(() -> callback.onError("请求失败，状态码: " + response.code()));
                        return;
                    }

                    String responseBody = response.body().string();
                    Log.d(TAG, "Response received: " + responseBody);

                    // 解析响应数据
                    BillOrderListResponse billResponse = gson.fromJson(responseBody, BillOrderListResponse.class);
                    
                    if (billResponse == null) {
                        Log.e(TAG, "Failed to parse response");
                        mainHandler.post(() -> callback.onError("响应数据解析失败"));
                        return;
                    }

                    // 检查响应码
                    if (!BillApiConstantsUtils.isSuccessCode(billResponse.getCode())) {
                        Log.e(TAG, "API error: " + billResponse.getMessage());
                        mainHandler.post(() -> callback.onError("API错误: " + billResponse.getMessage()));
                        return;
                    }

                    Log.d(TAG, "Request successful, records count: " + 
                        (billResponse.getData() != null && billResponse.getData().getRecords() != null ? 
                         billResponse.getData().getRecords().size() : 0));

                    // 回调成功结果
                    mainHandler.post(() -> callback.onSuccess(billResponse));

                } catch (Exception e) {
                    Log.e(TAG, "Error processing response", e);
                    mainHandler.post(() -> callback.onError("响应处理失败: " + e.getMessage()));
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * 取消所有请求
     */
    public void cancelAllRequests() {
        if (client != null) {
            client.dispatcher().cancelAll();
        }
        Log.d(TAG, "All requests cancelled");
    }

    /**
     * 释放资源
     */
    public void release() {
        cancelAllRequests();
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
        Log.d(TAG, "BillApiService released");
    }
}
