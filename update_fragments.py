#!/usr/bin/env python3
"""
批量更新Fragment类继承BaseMultiLanguageFragment
"""

import os
import re

# Fragment文件列表
FRAGMENT_FILES = [
    "app/src/main/java/com/android/video/ui/fragment/DiscoverFragment.java",
    "app/src/main/java/com/android/video/ui/fragment/HomeFragment.java",
    "app/src/main/java/com/android/video/ui/fragment/MyListFragment.java",
    "app/src/main/java/com/android/video/ui/fragment/VideoPlayerFragment.java",
]

def update_fragment_file(file_path):
    """更新单个Fragment文件"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经继承了BaseMultiLanguageFragment
        if 'BaseMultiLanguageFragment' in content:
            print(f"已更新: {file_path}")
            return True
        
        # 检查是否继承了BaseFullScreenFragment
        if 'extends BaseFullScreenFragment' not in content:
            print(f"不继承BaseFullScreenFragment，跳过: {file_path}")
            return False
        
        # 添加import
        import_pattern = r'(import com\.android\.video\.ui\.fragment\.BaseFullScreenFragment;)'
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r'\1\nimport com.android.video.base.BaseMultiLanguageFragment;',
                content
            )
        else:
            # 如果没有找到BaseFullScreenFragment的import，在其他import后添加
            import_section = re.search(r'(import [^;]+;)\n\n', content)
            if import_section:
                content = content.replace(
                    import_section.group(0),
                    import_section.group(0) + 'import com.android.video.base.BaseMultiLanguageFragment;\n\n'
                )
        
        # 替换继承
        content = re.sub(
            r'extends BaseFullScreenFragment',
            'extends BaseMultiLanguageFragment',
            content
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"更新成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量更新Fragment文件...")
    
    success_count = 0
    total_count = len(FRAGMENT_FILES)
    
    for file_path in FRAGMENT_FILES:
        if update_fragment_file(file_path):
            success_count += 1
    
    print(f"\n更新完成: {success_count}/{total_count} 个文件更新成功")

if __name__ == "__main__":
    main()
