package com.android.video.cache;

import android.content.Context;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.network.ApiClientUtils;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 缓存预加载管理器
 * 在应用启动时预加载常用数据，提升用户体验
 * <AUTHOR> Team
 */
public class CachePreloadManager {
    
    private static final String TAG = "CachePreloadManager";
    
    private static CachePreloadManager instance;
    private Context context;
    private ExecutorService executorService;
    private boolean isPreloading = false;
    
    // 预加载的API URL列表
    private static final String[] PRELOAD_URLS = {
        // Banner相关
        HomeApiConstantsUtils.API_GET_BANNERS,

        // Featured相关
        HomeApiConstantsUtils.API_GET_FEATURED,

        // 分类相关
        HomeApiConstantsUtils.API_GET_CATEGORY_LIST,

        // 热门内容
        HomeApiConstantsUtils.API_GET_DAILY_RANK,
        HomeApiConstantsUtils.API_GET_WORTH_WAITING,

        // 推荐内容
        HomeApiConstantsUtils.API_GET_GUESS_YOU_LIKE,
        HomeApiConstantsUtils.API_GET_MOST_POPULAR
    };
    
    private CachePreloadManager(Context context) {
        this.context = context.getApplicationContext();
        this.executorService = Executors.newFixedThreadPool(3); // 使用3个线程进行预加载
    }
    
    public static synchronized CachePreloadManager getInstance(Context context) {
        if (instance == null) {
            instance = new CachePreloadManager(context);
        }
        return instance;
    }
    
    /**
     * 开始预加载缓存
     */
    public void startPreloading() {
        if (isPreloading) {
            Log.d(TAG, "Preloading already in progress");
            return;
        }
        
        isPreloading = true;
        Log.d(TAG, "Starting cache preloading");
        
        executorService.execute(() -> {
            try {
                preloadEssentialData();
            } catch (Exception e) {
                Log.e(TAG, "Error during cache preloading", e);
            } finally {
                isPreloading = false;
            }
        });
    }
    
    /**
     * 预加载核心数据
     */
    private void preloadEssentialData() {
        Log.d(TAG, "Preloading essential data");
        
        // 检查网络状态
        if (!isNetworkAvailable()) {
            Log.w(TAG, "Network not available, skipping preload");
            return;
        }
        
        // 预加载API数据
        preloadApiData();
        
        // 预加载用户相关数据
        preloadUserData();
        
        Log.d(TAG, "Essential data preloading completed");
    }
    
    /**
     * 预加载API数据
     */
    private void preloadApiData() {
        Log.d(TAG, "Preloading API data");
        
        for (String url : PRELOAD_URLS) {
            try {
                String fullUrl = buildFullUrl(url);
                Log.d(TAG, "Preloading: " + fullUrl);

                // 使用OkHttp客户端进行预加载请求
                preloadUrl(fullUrl);

                // 添加延迟避免过于频繁的请求
                Thread.sleep(200);
            } catch (Exception e) {
                Log.e(TAG, "Failed to preload: " + url, e);
            }
        }
    }
    
    /**
     * 预加载用户相关数据
     */
    private void preloadUserData() {
        Log.d(TAG, "Preloading user data");
        
        // 这里可以预加载用户偏好设置、观看历史等
        // 由于这些数据可能需要用户登录，暂时跳过
        
        // 预加载标签数据
        try {
            // 标签数据通常比较稳定，可以预加载
            String tagsUrl = buildFullUrl("/api/tags");
            preloadUrl(tagsUrl);
        } catch (Exception e) {
            Log.e(TAG, "Failed to preload user data", e);
        }
    }
    
    /**
     * 构建完整URL
     */
    private String buildFullUrl(String path) {
        String baseUrl = EnvironmentConfigUtils.getApiBaseUrl();
        if (path.startsWith("http")) {
            return path; // 已经是完整URL
        }
        return baseUrl + (path.startsWith("/") ? path : "/" + path);
    }

    /**
     * 预加载单个URL
     */
    private void preloadUrl(String url) {
        try {
            okhttp3.OkHttpClient client = ApiClientUtils.getHttpClient();
            if (client == null) {
                Log.w(TAG, "HTTP client not available for preloading");
                return;
            }

            okhttp3.Request request = new okhttp3.Request.Builder()
                    .url(url)
                    .build();

            okhttp3.Response response = client.newCall(request).execute();
            if (response.isSuccessful()) {
                // 读取响应体以触发缓存
                String responseBody = response.body().string();
                Log.d(TAG, "Preloaded successfully: " + url + " (size: " + responseBody.length() + " chars)");
            } else {
                Log.w(TAG, "Preload failed: " + url + " (code: " + response.code() + ")");
            }
            response.close();
        } catch (Exception e) {
            Log.e(TAG, "Error preloading URL: " + url, e);
        }
    }
    
    /**
     * 检查网络是否可用
     */
    private boolean isNetworkAvailable() {
        // 这里可以使用NetworkUtils检查网络状态
        // 为了简化，暂时返回true
        return true;
    }
    
    /**
     * 预加载特定类型的数据
     */
    public void preloadSpecificData(String[] urls) {
        if (urls == null || urls.length == 0) {
            return;
        }
        
        executorService.execute(() -> {
            Log.d(TAG, "Preloading specific data: " + urls.length + " URLs");
            
            for (String url : urls) {
                try {
                    String fullUrl = buildFullUrl(url);
                    preloadUrl(fullUrl);
                    Thread.sleep(100); // 短暂延迟
                } catch (Exception e) {
                    Log.e(TAG, "Failed to preload specific URL: " + url, e);
                }
            }
            
            Log.d(TAG, "Specific data preloading completed");
        });
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanupExpiredCache() {
        executorService.execute(() -> {
            Log.d(TAG, "Cleaning up expired cache");
            // OkHttp的缓存会自动清理过期内容，这里只是记录日志
            long cacheSize = ApiClientUtils.getCacheSize();
            Log.d(TAG, "Current cache size after cleanup: " + cacheSize + " bytes");
            Log.d(TAG, "Cache cleanup completed");
        });
    }
    
    /**
     * 强制刷新核心数据
     */
    public void forceRefreshEssentialData() {
        executorService.execute(() -> {
            Log.d(TAG, "Force refreshing essential data");

            // 清除所有缓存
            ApiClientUtils.clearAllCache();

            // 重新预加载
            for (String url : PRELOAD_URLS) {
                try {
                    String fullUrl = buildFullUrl(url);
                    preloadUrl(fullUrl);
                    Thread.sleep(300); // 稍长的延迟
                } catch (Exception e) {
                    Log.e(TAG, "Failed to force refresh: " + url, e);
                }
            }

            Log.d(TAG, "Force refresh completed");
        });
    }
    
    /**
     * 获取预加载状态
     */
    public boolean isPreloading() {
        return isPreloading;
    }
    
    /**
     * 预加载回调接口
     */
    public interface PreloadCallback {
        void onPreloadStarted();
        void onPreloadProgress(int current, int total);
        void onPreloadCompleted();
        void onPreloadError(String error);
    }
    
    /**
     * 带回调的预加载
     */
    public void startPreloadingWithCallback(PreloadCallback callback) {
        if (isPreloading) {
            Log.d(TAG, "Preloading already in progress");
            return;
        }
        
        isPreloading = true;
        
        if (callback != null) {
            callback.onPreloadStarted();
        }
        
        executorService.execute(() -> {
            try {
                int total = PRELOAD_URLS.length;
                int current = 0;
                
                for (String url : PRELOAD_URLS) {
                    try {
                        String fullUrl = buildFullUrl(url);
                        preloadUrl(fullUrl);

                        current++;
                        if (callback != null) {
                            callback.onPreloadProgress(current, total);
                        }

                        Thread.sleep(200);
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to preload: " + url, e);
                        if (callback != null) {
                            callback.onPreloadError("Failed to preload: " + url);
                        }
                    }
                }
                
                if (callback != null) {
                    callback.onPreloadCompleted();
                }
                
            } catch (Exception e) {
                Log.e(TAG, "Error during preloading", e);
                if (callback != null) {
                    callback.onPreloadError(e.getMessage());
                }
            } finally {
                isPreloading = false;
            }
        });
    }
    
    /**
     * 关闭预加载管理器
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            Log.d(TAG, "CachePreloadManager shutdown");
        }
    }
}
