package com.android.video.player;

/**
 * 视频播放器控制接口
 * 定义视频播放的基本控制方法
 * <AUTHOR>
 */
public interface VideoPlayerController {
    
    /**
     * 播放视频
     */
    void play();
    
    /**
     * 暂停视频
     */
    void pause();
    
    /**
     * 停止播放
     */
    void stop();
    
    /**
     * 跳转到指定位置
     * @param positionMs 位置（毫秒）
     */
    void seekTo(long positionMs);
    
    /**
     * 设置播放速度
     * @param speed 播放速度（0.5x, 0.7x, 1.0x, 1.5x, 2.0x）
     */
    void setPlaybackSpeed(float speed);
    
    /**
     * 设置音量
     * @param volume 音量（0.0 - 1.0）
     */
    void setVolume(float volume);
    
    /**
     * 获取当前播放位置
     * @return 当前位置（毫秒）
     */
    long getCurrentPosition();
    
    /**
     * 获取视频总时长
     * @return 总时长（毫秒）
     */
    long getDuration();
    
    /**
     * 获取当前播放状态
     * @return true表示正在播放，false表示暂停或停止
     */
    boolean isPlaying();
    
    /**
     * 设置视频源
     * @param videoUrl 视频URL或资源路径
     */
    void setVideoSource(String videoUrl);
    
    /**
     * 释放播放器资源
     */
    void release();
    
    /**
     * 准备播放器
     */
    void prepare();
    
    /**
     * 重置播放器
     */
    void reset();
}
