package com.android.video.manager;

import android.content.Context;
import android.util.Log;

import com.android.video.player.VideoPlayerManager;
import com.android.video.ui.fragment.VideoPlayerFragment;

import java.lang.ref.WeakReference;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 全局视频播放器管理器
 * 确保同一时间只有一个视频在播放，彻底解决多重音频问题
 * <AUTHOR> Team
 */
public class GlobalVideoPlayerManager {
    
    private static final String TAG = "GlobalVideoPlayerManager";
    private static GlobalVideoPlayerManager instance;
    
    private WeakReference<VideoPlayerFragment> currentPlayingFragment;
    private List<WeakReference<VideoPlayerFragment>> allFragments;
    
    private GlobalVideoPlayerManager() {
        allFragments = new ArrayList<>();
    }
    
    public static synchronized GlobalVideoPlayerManager getInstance() {
        if (instance == null) {
            instance = new GlobalVideoPlayerManager();
        }
        return instance;
    }
    
    /**
     * 注册Fragment
     */
    public void registerFragment(VideoPlayerFragment fragment) {
        // 清理已经被回收的Fragment引用
        cleanupFragments();
        
        allFragments.add(new WeakReference<>(fragment));
        Log.d(TAG, "Fragment registered, total fragments: " + allFragments.size());
    }
    
    /**
     * 注销Fragment
     */
    public void unregisterFragment(VideoPlayerFragment fragment) {
        Iterator<WeakReference<VideoPlayerFragment>> iterator = allFragments.iterator();
        while (iterator.hasNext()) {
            WeakReference<VideoPlayerFragment> ref = iterator.next();
            VideoPlayerFragment f = ref.get();
            if (f == null || f == fragment) {
                iterator.remove();
            }
        }
        
        // 如果当前播放的Fragment被注销，清除引用
        if (currentPlayingFragment != null && currentPlayingFragment.get() == fragment) {
            currentPlayingFragment = null;
        }
        
        Log.d(TAG, "Fragment unregistered, total fragments: " + allFragments.size());
    }
    
    /**
     * 开始播放指定Fragment的视频
     * 会自动停止其他所有Fragment的播放
     */
    public void startPlaying(VideoPlayerFragment fragment) {
        Log.d(TAG, "Starting playback for fragment at position " + fragment.getFragmentPosition());

        // 检查是否已经是当前播放的Fragment
        if (currentPlayingFragment != null && currentPlayingFragment.get() == fragment) {
            Log.d(TAG, "Fragment is already the current playing fragment");
            return;
        }

        // 停止所有其他Fragment的播放
        stopAllOtherFragments(fragment);

        // 设置当前播放的Fragment
        currentPlayingFragment = new WeakReference<>(fragment);

        Log.d(TAG, "Fragment set as current playing");
    }
    
    /**
     * 停止指定Fragment的播放
     */
    public void stopPlaying(VideoPlayerFragment fragment) {
        if (currentPlayingFragment != null && currentPlayingFragment.get() == fragment) {
            currentPlayingFragment = null;
            Log.d(TAG, "Stopped current playing fragment");
        }
    }
    
    /**
     * 停止所有Fragment的播放
     */
    public void stopAllFragments() {
        Log.d(TAG, "Stopping all fragments - total registered: " + allFragments.size());

        cleanupFragments();

        int stoppedCount = 0;
        for (WeakReference<VideoPlayerFragment> ref : allFragments) {
            VideoPlayerFragment fragment = ref.get();
            if (fragment != null && fragment.getPlayerManager() != null) {
                try {
                    if (fragment.getPlayerManager().isPlaying()) {
                        fragment.getPlayerManager().pause();
                        stoppedCount++;
                        Log.d(TAG, "Stopped fragment at position " + fragment.getFragmentPosition());
                    }
                } catch (Exception e) {
                    Log.e(TAG, "Error stopping fragment at position " + fragment.getFragmentPosition(), e);
                }
            }
        }

        currentPlayingFragment = null;
        Log.d(TAG, "Stopped " + stoppedCount + " fragments, current playing fragment cleared");
    }
    
    /**
     * 停止除指定Fragment外的所有其他Fragment
     */
    private void stopAllOtherFragments(VideoPlayerFragment excludeFragment) {
        cleanupFragments();

        int stoppedCount = 0;
        for (WeakReference<VideoPlayerFragment> ref : allFragments) {
            VideoPlayerFragment fragment = ref.get();
            if (fragment != null && fragment != excludeFragment && fragment.getPlayerManager() != null) {
                if (fragment.getPlayerManager().isPlaying()) {
                    fragment.getPlayerManager().pause();
                    stoppedCount++;
                    Log.d(TAG, "Stopped other fragment at position " + fragment.getFragmentPosition());
                }
            }
        }
        Log.d(TAG, "Stopped " + stoppedCount + " other fragments");
    }
    
    /**
     * 清理已被回收的Fragment引用
     */
    private void cleanupFragments() {
        Iterator<WeakReference<VideoPlayerFragment>> iterator = allFragments.iterator();
        while (iterator.hasNext()) {
            WeakReference<VideoPlayerFragment> ref = iterator.next();
            if (ref.get() == null) {
                iterator.remove();
            }
        }
    }
    
    /**
     * 获取当前播放的Fragment
     */
    public VideoPlayerFragment getCurrentPlayingFragment() {
        if (currentPlayingFragment != null) {
            return currentPlayingFragment.get();
        }
        return null;
    }
    
    /**
     * 检查是否有Fragment正在播放
     */
    public boolean isAnyFragmentPlaying() {
        VideoPlayerFragment current = getCurrentPlayingFragment();
        return current != null && current.getPlayerManager() != null && current.getPlayerManager().isPlaying();
    }

    /**
     * 获取所有注册的Fragment列表（用于测试）
     */
    public java.util.List<VideoPlayerFragment> getAllFragments() {
        cleanupFragments();
        java.util.List<VideoPlayerFragment> fragments = new java.util.ArrayList<>();
        for (WeakReference<VideoPlayerFragment> ref : allFragments) {
            VideoPlayerFragment fragment = ref.get();
            if (fragment != null) {
                fragments.add(fragment);
            }
        }
        return fragments;
    }

    /**
     * 强制停止所有播放（用于页面切换时确保没有后台播放）
     */
    public void forceStopAllPlayback() {
        Log.d(TAG, "Force stopping all playback");

        cleanupFragments();

        // 强制停止所有Fragment的播放，包括暂停和停止
        for (WeakReference<VideoPlayerFragment> ref : allFragments) {
            VideoPlayerFragment fragment = ref.get();
            if (fragment != null && fragment.getPlayerManager() != null) {
                try {
                    VideoPlayerManager playerManager = fragment.getPlayerManager();
                    if (playerManager.isPlaying()) {
                        playerManager.pause();
                        Log.d(TAG, "Force paused fragment at position " + fragment.getFragmentPosition());
                    }
                    // 额外调用stop确保彻底停止
                    playerManager.stop();
                } catch (Exception e) {
                    Log.e(TAG, "Error force stopping fragment at position " + fragment.getFragmentPosition(), e);
                }
            }
        }

        currentPlayingFragment = null;
        Log.d(TAG, "Force stop completed");
    }
}
