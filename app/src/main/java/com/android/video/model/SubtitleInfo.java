package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 字幕信息数据模型
 * <p>
 * 用于映射API返回的字幕信息JSON数据结构。
 * 包含字幕的语言类型、文件名称和文件URL等信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class SubtitleInfo {
    
    /**
     * 支持语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 字幕文件名称
     */
    @SerializedName("subtitleName")
    private String subtitleName;
    
    /**
     * 字幕文件URL
     */
    @SerializedName("subtitleFileUrl")
    private String subtitleFileUrl;

    /**
     * 默认构造函数
     */
    public SubtitleInfo() {
    }

    /**
     * 完整构造函数
     */
    public SubtitleInfo(Integer languageType, String subtitleName, String subtitleFileUrl) {
        this.languageType = languageType;
        this.subtitleName = subtitleName;
        this.subtitleFileUrl = subtitleFileUrl;
    }

    // ========== Getter和Setter方法 ==========

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getSubtitleName() {
        return subtitleName;
    }

    public void setSubtitleName(String subtitleName) {
        this.subtitleName = subtitleName;
    }

    public String getSubtitleFileUrl() {
        return subtitleFileUrl;
    }

    public void setSubtitleFileUrl(String subtitleFileUrl) {
        this.subtitleFileUrl = subtitleFileUrl;
    }

    // ========== 便利方法 ==========

    /**
     * 获取语言类型描述
     * 
     * @return 语言类型描述
     */
    public String getLanguageTypeDescription() {
        if (languageType == null) {
            return "未知语言";
        }
        
        switch (languageType) {
            case 1:
                return "英语";
            case 2:
                return "俄语";
            default:
                return "未知语言";
        }
    }

    /**
     * 检查字幕文件URL是否有效
     * 
     * @return 如果URL有效返回true，否则返回false
     */
    public boolean hasValidUrl() {
        return subtitleFileUrl != null && !subtitleFileUrl.trim().isEmpty()
                && (subtitleFileUrl.startsWith("http://") || subtitleFileUrl.startsWith("https://"));
    }

    /**
     * 检查是否为英语字幕
     * 
     * @return 如果是英语字幕返回true，否则返回false
     */
    public boolean isEnglish() {
        return languageType != null && languageType == 1;
    }

    /**
     * 检查是否为俄语字幕
     * 
     * @return 如果是俄语字幕返回true，否则返回false
     */
    public boolean isRussian() {
        return languageType != null && languageType == 2;
    }

    /**
     * 获取字幕文件扩展名
     * 
     * @return 字幕文件扩展名，如果无法获取则返回空字符串
     */
    public String getFileExtension() {
        if (subtitleFileUrl == null || subtitleFileUrl.trim().isEmpty()) {
            return "";
        }
        
        String url = subtitleFileUrl.trim();
        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1).toLowerCase();
        }
        
        return "";
    }

    /**
     * 检查是否为支持的字幕格式
     * 
     * @return 如果是支持的格式返回true，否则返回false
     */
    public boolean isSupportedFormat() {
        String extension = getFileExtension();
        return extension.equals("srt") || extension.equals("ass") || 
               extension.equals("vtt") || extension.equals("sub");
    }

    @Override
    public String toString() {
        return "SubtitleInfo{" +
                "languageType=" + languageType +
                ", subtitleName='" + subtitleName + '\'' +
                ", subtitleFileUrl='" + subtitleFileUrl + '\'' +
                ", languageDescription='" + getLanguageTypeDescription() + '\'' +
                ", hasValidUrl=" + hasValidUrl() +
                ", fileExtension='" + getFileExtension() + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        SubtitleInfo that = (SubtitleInfo) o;

        if (languageType != null ? !languageType.equals(that.languageType) : that.languageType != null)
            return false;
        if (subtitleName != null ? !subtitleName.equals(that.subtitleName) : that.subtitleName != null)
            return false;
        return subtitleFileUrl != null ? subtitleFileUrl.equals(that.subtitleFileUrl) : that.subtitleFileUrl == null;
    }

    @Override
    public int hashCode() {
        int result = languageType != null ? languageType.hashCode() : 0;
        result = 31 * result + (subtitleName != null ? subtitleName.hashCode() : 0);
        result = 31 * result + (subtitleFileUrl != null ? subtitleFileUrl.hashCode() : 0);
        return result;
    }

    /**
     * 获取字幕的MIME类型
     *
     * @return MIME类型字符串
     */
    public String getMimeType() {
        String extension = getFileExtension();
        if ("srt".equalsIgnoreCase(extension)) {
            return "application/x-subrip";
        } else if ("vtt".equalsIgnoreCase(extension)) {
            return "text/vtt";
        } else if ("ass".equalsIgnoreCase(extension) || "ssa".equalsIgnoreCase(extension)) {
            return "text/x-ass";
        } else if ("sub".equalsIgnoreCase(extension)) {
            return "text/plain";
        }
        return "text/plain"; // 默认类型
    }

    /**
     * 获取语言代码
     *
     * @return 语言代码
     */
    public String getLanguageCode() {
        if (languageType == null) {
            return "und"; // undefined
        }

        switch (languageType) {
            case 1:
                return "en"; // 英语
            case 2:
                return "ru"; // 俄语
            default:
                return "und"; // undefined
        }
    }
}
