<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/home_video_item_spacing_horizontal"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_container"
        android:layout_width="@dimen/home_continue_watching_item_width"
        android:layout_height="@dimen/home_continue_watching_item_height"
        android:background="@drawable/home_continue_watching_border"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 海报 -->
        <ImageView
            android:id="@+id/iv_continue_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/home_continue_watching_item_border_width"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/home_continue_watching_border"
            android:clipToOutline="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 视频标题 -->
    <TextView
        android:id="@+id/tv_continue_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/home_continue_watching_title_margin_top"
        android:text="喜洋洋与灰太狼"
        android:textColor="@color/white"
        android:textSize="@dimen/home_video_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/cl_poster_container"
        app:layout_constraintStart_toStartOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="@id/cl_poster_container" />

    <!-- 观看进度 -->
    <TextView
        android:id="@+id/tv_continue_progress"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/home_continue_watching_progress_margin_top"
        android:text="EP.1/EP.72"
        android:textSize="@dimen/home_continue_watching_progress_text_size"
        android:fontFamily="sans-serif-light"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/tv_continue_title"
        app:layout_constraintStart_toStartOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="@id/cl_poster_container" />

</androidx.constraintlayout.widget.ConstraintLayout>
