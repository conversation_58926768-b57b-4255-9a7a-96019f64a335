package com.android.video.test;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.upstream.DefaultDataSource;

/**
 * ExoPlayer依赖测试类
 * 用于验证ExoPlayer依赖是否正确添加
 * <AUTHOR>
 */
public class ExoPlayerDependencyTest {
    
    /**
     * 测试ExoPlayer类是否可以正常导入和使用
     * 这个方法不会被实际调用，仅用于编译时验证
     */
    public void testExoPlayerImport() {
        // 测试ExoPlayer核心类
        ExoPlayer player = null;
        
        // 测试MediaItem类
        MediaItem mediaItem = null;
        
        // 测试MediaSource类
        MediaSource mediaSource = null;
        
        // 测试ProgressiveMediaSource类
        ProgressiveMediaSource progressiveMediaSource = null;
        
        // 测试DefaultDataSource类
        DefaultDataSource.Factory dataSourceFactory = null;
        
        // 如果能编译通过，说明ExoPlayer依赖添加成功
        System.out.println("ExoPlayer dependency test passed!");
    }
}
