package com.android.video.model;

/**
 * 标签数据模型类
 * <AUTHOR>
 */
public class TagModel {
    
    private String name;
    private String type;
    private boolean isSelected;

    /**
     * 默认构造函数
     */
    public TagModel() {
        this.name = "";
        this.type = "";
        this.isSelected = false;
    }

    /**
     * 完整构造函数
     */
    public TagModel(String name, String type, boolean isSelected) {
        this.name = name;
        this.type = type;
        this.isSelected = isSelected;
    }

    /**
     * 便利构造函数 - 创建未选中的标签
     */
    public TagModel(String name, String type) {
        this.name = name;
        this.type = type;
        this.isSelected = false;
    }

    /**
     * Getter和Setter方法
     * @return
     */
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public boolean isSelected() {
        return isSelected;
    }
    
    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    /**
     * 便利方法
     * @return
     */
    public void toggleSelection() {
        this.isSelected = !this.isSelected;
    }
    
    public String getDisplayName() {
        return name != null && !name.isEmpty() ? name : "Unknown Tag";
    }
    
    public boolean isEmpty() {
        return name == null || name.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "TagModel{" +
                "name='" + name + '\'' +
                ", type='" + type + '\'' +
                ", isSelected=" + isSelected +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TagModel tagModel = (TagModel) obj;
        return name != null ? name.equals(tagModel.name) : tagModel.name == null;
    }

    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }
}
