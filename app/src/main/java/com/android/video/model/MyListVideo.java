package com.android.video.model;

/**
 * My List视频项模型
 */
public class MyListVideo {
    private String id;
    private String title;
    private String posterUrl;
    private boolean isLiked;
    private String category; // "liked", "history", "subscribed", "downloaded"
    private String description; // 影片简介
    private String releaseDate; // 上映时间
    private int currentEpisode; // 当前观看章节
    private int totalEpisodes; // 总章节数
    private int downloadedEpisodes; // 已下载章节数
    private boolean isSubscribed; // 是否订阅
    private String filmLanguageInfoId; // 短剧语言信息ID，用于跳转详情页

    public MyListVideo() {
    }

    public MyListVideo(String id, String title, String posterUrl, boolean isLiked, String category) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.isLiked = isLiked;
        this.category = category;
        this.description = "";
        this.releaseDate = "";
        this.currentEpisode = 1;
        this.totalEpisodes = 1;
        this.downloadedEpisodes = 0;
        this.isSubscribed = false;
    }

    public MyListVideo(String id, String title, String posterUrl, String category,
                      String description, String releaseDate, int currentEpisode,
                      int totalEpisodes, int downloadedEpisodes, boolean isSubscribed) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.category = category;
        this.description = description;
        this.releaseDate = releaseDate;
        this.currentEpisode = currentEpisode;
        this.totalEpisodes = totalEpisodes;
        this.downloadedEpisodes = downloadedEpisodes;
        this.isSubscribed = isSubscribed;
        this.isLiked = false;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public boolean isLiked() {
        return isLiked;
    }

    public void setLiked(boolean liked) {
        isLiked = liked;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(String releaseDate) {
        this.releaseDate = releaseDate;
    }

    public int getCurrentEpisode() {
        return currentEpisode;
    }

    public void setCurrentEpisode(int currentEpisode) {
        this.currentEpisode = currentEpisode;
    }

    public int getTotalEpisodes() {
        return totalEpisodes;
    }

    public void setTotalEpisodes(int totalEpisodes) {
        this.totalEpisodes = totalEpisodes;
    }

    public int getDownloadedEpisodes() {
        return downloadedEpisodes;
    }

    public void setDownloadedEpisodes(int downloadedEpisodes) {
        this.downloadedEpisodes = downloadedEpisodes;
    }

    public boolean isSubscribed() {
        return isSubscribed;
    }

    public void setSubscribed(boolean subscribed) {
        isSubscribed = subscribed;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }
}
