package com.android.video.model;

import com.android.video.constants.ProfileApiConstantsUtils;

/**
 * 用户信息数据模型类
 * <p>
 * 用于封装从个人中心API获取的用户信息数据，
 * 对应 {@link ProfileApiConstantsUtils#API_GET_USER_INFO} 接口的响应数据。
 * </p>
 *
 * <p>
 * 包含的用户信息：
 * <ul>
 *   <li>基本信息：用户ID、昵称、手机号、头像等</li>
 *   <li>账户信息：用户类型、注册方式、状态等</li>
 *   <li>VIP信息：VIP状态、剩余天数、到期时间等</li>
 *   <li>其他信息：积分、语言偏好、设备信息等</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see ProfileApiConstantsUtils
 */
public class UserInfo {

    // ========== 基本信息字段 ==========

    /**
     * 用户ID
     */
    private String customerId;

    /**
     * 用户昵称
     */
    private String customerName;



    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 头像URL
     */
    private String headImg;

    // ========== 时间信息字段 ==========

    /**
     * 注册时间
     */
    private String registrationTime;

    /**
     * 最后登录时间
     */
    private String lastLoginTime;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 更新时间
     */
    private String updateTime;

    // ========== 账户信息字段 ==========

    /**
     * 注册方式 (1=Phone Number, 2=FaceBook, 3=Tiktok)
     */
    private int registrationType;

    /**
     * 删除状态
     */
    private int isDelete;

    /**
     * 用户状态
     */
    private int status;

    // ========== 第三方登录信息 ==========

    /**
     * Facebook ID
     */
    private String facebookId;

    /**
     * TikTok ID
     */
    private String tiktokId;

    // ========== 设备和令牌信息 ==========

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 用户访问令牌
     */
    private String userToken;

    // ========== VIP和积分信息 ==========

    /**
     * 当前积分数
     */
    private int points;

    /**
     * 消息读取状态
     * 0 = 没有未读消息
     * 1 = 有未读消息
     */
    private int messageReadStatus;

    /**
     * VIP状态 (0=否, 1=是, null=未设置)
     */
    private Integer isVip;

    /**
     * VIP剩余天数
     */
    private Integer vipDays;

    /**
     * VIP到期时间
     */
    private String vipExpireDate;

    // ========== 偏好设置 ==========

    /**
     * 语言类型 (1=英语, 2=俄语, null=未设置)
     */
    private Integer languageType;

    // ========== 构造方法 ==========

    /**
     * 默认构造方法
     */
    public UserInfo() {
    }

    /**
     * 完整构造方法
     */
    public UserInfo(String customerId, String customerName,
                   String phoneNumber, String headImg, String registrationTime,
                   String lastLoginTime, String createTime, String updateTime,
                   int registrationType, int isDelete, int status,
                   String facebookId, String tiktokId, String deviceId,
                   String userToken, int points, Integer isVip,
                   Integer vipDays, String vipExpireDate, Integer languageType) {
        this.customerId = customerId;
        this.customerName = customerName;
        this.phoneNumber = phoneNumber;
        this.headImg = headImg;
        this.registrationTime = registrationTime;
        this.lastLoginTime = lastLoginTime;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.registrationType = registrationType;
        this.isDelete = isDelete;
        this.status = status;
        this.facebookId = facebookId;
        this.tiktokId = tiktokId;
        this.deviceId = deviceId;
        this.userToken = userToken;
        this.points = points;
        this.isVip = isVip;
        this.vipDays = vipDays;
        this.vipExpireDate = vipExpireDate;
        this.languageType = languageType;
    }

    // ========== 业务方法 ==========

    /**
     * 检查用户是否为VIP
     *
     * @return 如果用户是VIP则返回true，否则返回false
     */
    public boolean isVipUser() {
        return isVip != null && isVip == ProfileApiConstantsUtils.VIP_STATUS_YES;
    }



    /**
     * 获取注册方式描述
     *
     * @return 注册方式的中文描述
     */
    public String getRegistrationTypeDescription() {
        return ProfileApiConstantsUtils.getRegistrationTypeDescription(registrationType);
    }

    /**
     * 获取语言类型描述
     *
     * @return 语言类型的中文描述
     */
    public String getLanguageTypeDescription() {
        return ProfileApiConstantsUtils.getLanguageTypeDescription(languageType);
    }

    /**
     * 检查用户是否有头像
     *
     * @return 如果用户有头像则返回true，否则返回false
     */
    public boolean hasHeadImg() {
        return headImg != null && !headImg.trim().isEmpty();
    }

    /**
     * 检查VIP是否即将到期（剩余天数少于7天）
     *
     * @return 如果VIP即将到期则返回true，否则返回false
     */
    public boolean isVipExpiringSoon() {
        return isVipUser() && vipDays != null && vipDays <= 7 && vipDays > 0;
    }

    // ========== Getter和Setter方法 ==========

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }



    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getHeadImg() {
        return headImg;
    }

    public void setHeadImg(String headImg) {
        this.headImg = headImg;
    }

    public String getRegistrationTime() {
        return registrationTime;
    }

    public void setRegistrationTime(String registrationTime) {
        this.registrationTime = registrationTime;
    }

    public String getLastLoginTime() {
        return lastLoginTime;
    }

    public void setLastLoginTime(String lastLoginTime) {
        this.lastLoginTime = lastLoginTime;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getRegistrationType() {
        return registrationType;
    }

    public void setRegistrationType(int registrationType) {
        this.registrationType = registrationType;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getFacebookId() {
        return facebookId;
    }

    public void setFacebookId(String facebookId) {
        this.facebookId = facebookId;
    }

    public String getTiktokId() {
        return tiktokId;
    }

    public void setTiktokId(String tiktokId) {
        this.tiktokId = tiktokId;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public Integer getIsVip() {
        return isVip;
    }

    public void setIsVip(Integer isVip) {
        this.isVip = isVip;
    }

    public Integer getVipDays() {
        return vipDays;
    }

    public void setVipDays(Integer vipDays) {
        this.vipDays = vipDays;
    }

    public String getVipExpireDate() {
        return vipExpireDate;
    }

    public void setVipExpireDate(String vipExpireDate) {
        this.vipExpireDate = vipExpireDate;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public int getMessageReadStatus() {
        return messageReadStatus;
    }

    public void setMessageReadStatus(int messageReadStatus) {
        this.messageReadStatus = messageReadStatus;
    }
}
