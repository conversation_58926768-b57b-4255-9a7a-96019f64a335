package com.android.video.network;

import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.MessageApiConstantsUtils;
import com.android.video.model.response.MessageDetailResponseModel;
import com.android.video.model.response.MessageListResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 消息推送API服务类 - 提供消息相关的网络请求功能
 * <p>
 * 此类封装了消息推送模块的所有API请求，包括：
 * <ul>
 *   <li>获取消息列表（分页）</li>
 *   <li>获取消息详情</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * MessageApiService messageApiService = new MessageApiService();
 * 
 * // 获取消息列表
 * messageApiService.getMessageList(1, 10, new ApiCallback&lt;MessageListResponseModel&gt;() {
 *     {@literal @}Override
 *     public void onSuccess(MessageListResponseModel response) {
 *         // 处理成功响应
 *     }
 *     
 *     {@literal @}Override
 *     public void onError(String error) {
 *         // 处理错误
 *     }
 * });
 * 
 * // 获取消息详情
 * messageApiService.getMessageDetail("sendMessageId", new ApiCallback&lt;MessageDetailResponseModel&gt;() {
 *     {@literal @}Override
 *     public void onSuccess(MessageDetailResponseModel response) {
 *         // 处理成功响应
 *     }
 *     
 *     {@literal @}Override
 *     public void onError(String error) {
 *         // 处理错误
 *     }
 * });
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see MessageApiConstantsUtils
 */
public class MessageApiService {

    private static final String TAG = "MessageApiService";
    
    private final OkHttpClient httpClient;
    private final Gson gson;

    /**
     * 构造函数
     */
    public MessageApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.httpClient = httpClient;
        this.gson = new Gson();
    }

    /**
     * API回调接口
     * 
     * @param <T> 响应数据类型
     */
    public interface ApiCallback<T> {
        /**
         * 请求成功回调
         * 
         * @param response 响应数据
         */
        void onSuccess(T response);

        /**
         * 请求失败回调
         * 
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 获取消息列表
     * <p>
     * 根据分页参数获取消息推送列表数据。
     * </p>
     * 
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param callback 回调接口
     */
    public void getMessageList(int page, int size, ApiCallback<MessageListResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (page < 1) {
            callback.onError("页码必须大于0");
            return;
        }
        if (size < 1 || size > 50) {
            callback.onError("每页数量必须在1-50之间");
            return;
        }

        // 构建请求URL
        String apiUrl = MessageApiConstantsUtils.buildMessageListUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            page,
            size
        );

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "发送消息列表请求: " + apiUrl);
        Log.d(TAG, "请求参数: page=" + page + ", size=" + size);

        // 发送异步请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "消息列表响应: " + responseBody);

                        // 解析JSON响应
                        MessageListResponseModel responseModel = gson.fromJson(responseBody, MessageListResponseModel.class);
                        
                        if (responseModel != null && responseModel.isSuccess()) {
                            callback.onSuccess(responseModel);
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * 获取消息详情
     * <p>
     * 根据消息推送ID获取消息的详细信息。
     * </p>
     * 
     * @param sendMessageId 消息推送ID
     * @param callback 回调接口
     */
    public void getMessageDetail(String sendMessageId, ApiCallback<MessageDetailResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (sendMessageId == null || sendMessageId.trim().isEmpty()) {
            callback.onError("消息推送ID不能为空");
            return;
        }

        // 构建请求URL
        String apiUrl = MessageApiConstantsUtils.buildMessageDetailUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            sendMessageId
        );

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "发送消息详情请求: " + apiUrl);
        Log.d(TAG, "请求参数: sendMessageId=" + sendMessageId);
        Log.d(TAG, "完整请求URL: " + request.url());

        // 发送异步请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "消息详情响应: " + responseBody);

                        // 解析JSON响应
                        MessageDetailResponseModel responseModel = gson.fromJson(responseBody, MessageDetailResponseModel.class);
                        
                        if (responseModel != null && responseModel.isSuccess()) {
                            callback.onSuccess(responseModel);
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    response.close();
                }
            }
        });
    }
}
