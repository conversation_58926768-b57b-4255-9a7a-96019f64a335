<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Normal phone specific dimensions (360dp width, scale factor: 1.0 - BASE) -->

    <!-- Elevation Values - Medium screen (360dp) -->
    <dimen name="elevation_none">0dp</dimen>
    <dimen name="elevation_low">2dp</dimen>
    <dimen name="elevation_medium">4dp</dimen>
    <dimen name="elevation_high">8dp</dimen>

    <!-- Video Player Fixed Values - Medium screen (360dp) -->
    <dimen name="video_player_divider_height">1dp</dimen>
    <dimen name="video_player_progress_height">4dp</dimen>
    <dimen name="video_player_thumb_offset">0dp</dimen>
    <dimen name="video_player_control_margin_end">2dp</dimen>
    <dimen name="video_player_popup_margin">16dp</dimen>
    <dimen name="video_player_selection_bottom_margin">200dp</dimen>
    <dimen name="video_player_speed_selection_offset">70dp</dimen>

    <!-- Back button dimensions -->
    <dimen name="login_back_button_size">35dp</dimen>
    <dimen name="login_back_button_margin_top">48dp</dimen>
    <dimen name="login_back_button_margin_start">16dp</dimen>

    <!-- Logo dimensions -->
    <dimen name="login_logo_width">257dp</dimen>
    <dimen name="login_logo_height">133dp</dimen>
    <dimen name="login_logo_margin_top">120dp</dimen>

    <!-- Login button dimensions -->
    <dimen name="login_button_width">286dp</dimen>
    <dimen name="login_button_height">46dp</dimen>
    <dimen name="login_button_corner_radius">23dp</dimen>
    <dimen name="login_button_margin_horizontal">22dp</dimen>
    <dimen name="login_button_margin_bottom">15dp</dimen>
    <dimen name="login_button_elevation">3dp</dimen>

    <!-- Icon dimensions -->
    <dimen name="login_icon_size">23dp</dimen>
    <dimen name="login_icon_margin_end">12dp</dimen>

    <!-- Text dimensions -->
    <dimen name="login_text_size">15sp</dimen>
    <dimen name="login_text_line_height">27dp</dimen>
    <dimen name="login_privacy_text_size">12sp</dimen>
    <dimen name="login_privacy_line_height">17dp</dimen>
    <dimen name="login_agreement_text_size">12sp</dimen>
    <dimen name="login_agreement_line_height">17dp</dimen>

    <!-- Privacy text area dimensions -->
    <dimen name="login_privacy_text_width">329dp</dimen>
    <dimen name="login_privacy_text_height">84dp</dimen>
    <dimen name="login_privacy_text_margin_horizontal">15dp</dimen>
    <dimen name="login_privacy_text_margin_top">130dp</dimen>

    <!-- Agreement text area dimensions -->
    <dimen name="login_agreement_text_width">329dp</dimen>
    <dimen name="login_agreement_text_height">35dp</dimen>
    <dimen name="login_agreement_text_margin_horizontal">15dp</dimen>
    <dimen name="login_agreement_text_margin_top">5dp</dimen>
    <dimen name="login_agreement_text_margin_bottom">15dp</dimen>

    <!-- Content padding -->
    <dimen name="login_content_padding">15dp</dimen>

    <!-- Phone Login Dimensions - Normal phone (scale factor: 0.96) -->
    <dimen name="phone_login_title_margin_top">0dp</dimen>
    <dimen name="phone_input_width">329dp</dimen>
    <dimen name="phone_input_height">52dp</dimen>
    <dimen name="phone_input_margin_top">58dp</dimen>
    <dimen name="send_code_button_width">329dp</dimen>
    <dimen name="send_code_button_height">40dp</dimen>
    <dimen name="send_code_button_margin_top">53dp</dimen>

    <!-- Bottom Navigation Dimensions - Normal phone -->
    <dimen name="bottom_nav_height">64dp</dimen>
    <dimen name="bottom_nav_icon_size">23dp</dimen>
    <dimen name="bottom_nav_text_size">11sp</dimen>
    <dimen name="bottom_nav_icon_text_margin">10dp</dimen>
    <dimen name="bottom_nav_item_padding_vertical">8dp</dimen>
    <dimen name="bottom_nav_item_padding_horizontal">12dp</dimen>

    <!-- Bottom Navigation Selected Background Dimensions -->
    <dimen name="bottom_nav_selected_bg_width">46dp</dimen>
    <dimen name="bottom_nav_selected_bg_height">31dp</dimen>
    <dimen name="bottom_nav_selected_bg_radius">12dp</dimen>

    <!-- Bottom Navigation Layout Positioning -->
    <dimen name="bottom_nav_selected_bg_margin_top">3dp</dimen>
    <dimen name="bottom_nav_text_margin_from_rect">1.5dp</dimen>
    <dimen name="bottom_nav_text_margin_bottom">4dp</dimen>







    <!-- Refill Button Dimensions -->
    <dimen name="profile_points_refill_button_corner_radius">18dp</dimen>
    <dimen name="profile_points_refill_text_line_height">18dp</dimen>

    <dimen name="profile_menu_corner_radius">12dp</dimen>

    <!-- Subscribe Page Dimensions - Normal phone (360dp width, scale factor: 0.96) -->
    <!-- Precise positioning based on design specs -->
    <dimen name="subscribe_title_width">148dp</dimen>
    <dimen name="subscribe_title_height">36dp</dimen>
    <dimen name="subscribe_title_margin_start">107dp</dimen>
    <dimen name="subscribe_title_margin_top">82dp</dimen>
    <dimen name="subscribe_title_text_size">31sp</dimen>

    <dimen name="subscribe_redeem_icon_width">21dp</dimen>
    <dimen name="subscribe_redeem_icon_height">22dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_start">237dp</dimen>
    <dimen name="subscribe_redeem_icon_margin_top">50dp</dimen>

    <dimen name="subscribe_redeem_text_width">85dp</dimen>
    <dimen name="subscribe_redeem_text_height">16dp</dimen>
    <dimen name="subscribe_redeem_text_margin_start">262dp</dimen>
    <dimen name="subscribe_redeem_text_margin_top">53dp</dimen>
    <dimen name="subscribe_redeem_text_size">13sp</dimen>

    <dimen name="subscribe_watch_more_width">183dp</dimen>
    <dimen name="subscribe_watch_more_height">20dp</dimen>
    <dimen name="subscribe_watch_more_margin_start">89dp</dimen>
    <dimen name="subscribe_watch_more_margin_top">132dp</dimen>
    <dimen name="subscribe_watch_more_text_size">17sp</dimen>

    <dimen name="subscribe_decoration_size">15dp</dimen>
    <dimen name="subscribe_decoration_left_margin_start">61dp</dimen>
    <dimen name="subscribe_decoration_right_margin_start">283dp</dimen>
    <dimen name="subscribe_decoration_margin_top">137dp</dimen>

    <!-- Subscription Cards -->
    <dimen name="subscribe_card_width">337dp</dimen>
    <dimen name="subscribe_card_height">128dp</dimen>
    <dimen name="subscribe_card_corner_radius">17dp</dimen>
    <dimen name="subscribe_card_margin_horizontal">12dp</dimen>
    <dimen name="subscribe_card_margin_vertical">8dp</dimen>
    <dimen name="subscribe_card_padding">15dp</dimen>
    <dimen name="subscribe_card_stroke_width">1dp</dimen>

    <!-- Card Text Sizes -->
    <dimen name="subscribe_plan_title_text_size">17sp</dimen>
    <dimen name="subscribe_plan_subtitle_text_size">13sp</dimen>
    <dimen name="subscribe_plan_description_text_size">12sp</dimen>
    <dimen name="subscribe_price_text_size">23sp</dimen>
    <dimen name="subscribe_discount_text_size">12sp</dimen>

    <!-- Discount Badge -->
    <dimen name="subscribe_discount_badge_width">85dp</dimen>
    <dimen name="subscribe_discount_badge_height">19dp</dimen>
    <dimen name="subscribe_discount_badge_corner_radius">11dp</dimen>

    <!-- VIP Privileges -->
    <dimen name="subscribe_privilege_icon_size">46dp</dimen>
    <dimen name="subscribe_privilege_text_size">13sp</dimen>
    <dimen name="subscribe_privilege_margin">15dp</dimen>

    <!-- Subscribe Button -->
    <dimen name="subscribe_button_height">54dp</dimen>
    <dimen name="subscribe_button_corner_radius">27dp</dimen>
    <dimen name="subscribe_button_text_size">17sp</dimen>
    <dimen name="subscribe_button_margin">15dp</dimen>

    <!-- Tag Dimensions - Normal phone (scale factor: 0.96) -->
    <dimen name="tag_margin">6dp</dimen>
    <dimen name="tag_padding_horizontal">15dp</dimen>
    <dimen name="tag_padding_vertical">8dp</dimen>
    <dimen name="tag_text_size">13sp</dimen>
    <dimen name="tag_corner_radius">13dp</dimen>
    <dimen name="tag_border_width">1dp</dimen>

    <!-- Favorite Selection Page Dimensions - Normal phone (scale factor: 1.0) -->
    <dimen name="favorite_selection_top_shadow_height">44dp</dimen>
    <dimen name="favorite_selection_title_margin_top">90dp</dimen>
    <dimen name="favorite_selection_title_text_size">21sp</dimen>
    <dimen name="favorite_selection_description_margin_top">12dp</dimen>
    <dimen name="favorite_selection_description_text_size">21sp</dimen>
    <dimen name="favorite_selection_viewpager_margin_top">38dp</dimen>
    <dimen name="favorite_selection_viewpager_height">288dp</dimen>
    <dimen name="favorite_selection_indicator_margin_top">19dp</dimen>
    <dimen name="favorite_selection_indicator_dot_size">8dp</dimen>
    <dimen name="favorite_selection_indicator_dot_margin">4dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_top">38dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_bottom">38dp</dimen>
    <dimen name="favorite_selection_complete_button_height">46dp</dimen>
    <dimen name="favorite_selection_complete_button_margin_horizontal">31dp</dimen>

    <!-- ConstraintLayout 2.0 Advanced Features Support - Normal phone (scale factor: 0.96) -->

    <!-- Unified Layout Margins for ConstraintLayout - Normal phone -->
    <dimen name="layout_margin_tiny">4dp</dimen>
    <dimen name="layout_margin_small">8dp</dimen>
    <dimen name="layout_margin_medium">15dp</dimen>
    <dimen name="layout_margin_large">23dp</dimen>
    <dimen name="layout_margin_xlarge">31dp</dimen>

    <!-- Chain Spacing for ConstraintLayout Chains - Normal phone -->
    <dimen name="chain_spacing_tiny">4dp</dimen>
    <dimen name="chain_spacing_small">8dp</dimen>
    <dimen name="chain_spacing_medium">15dp</dimen>
    <dimen name="chain_spacing_large">23dp</dimen>



    <!-- Flow and Grid Layout Support - Normal phone -->
    <dimen name="flow_horizontal_gap">12dp</dimen>
    <dimen name="flow_vertical_gap">8dp</dimen>
    <dimen name="grid_column_gap">15dp</dimen>
    <dimen name="grid_row_gap">12dp</dimen>

    <!-- Subscribe Activity Specific -->
    <dimen name="subscribe_scroll_margin_top">173dp</dimen>
    <dimen name="subscribe_content_padding_bottom">96dp</dimen>

    <!-- Favorite Selection Page Indicator Spacing -->
    <dimen name="favorite_selection_indicator_button_spacing">19dp</dimen>

    <!-- Profile Page Dimensions - Normal phone (scale factor: 0.96) -->
    <!-- Status bar and header -->
    <dimen name="profile_status_bar_height">19dp</dimen>
    <dimen name="profile_status_bar_margin_start">20dp</dimen>
    <dimen name="profile_status_bar_margin_top">4dp</dimen>
    <dimen name="profile_status_time_width">52dp</dimen>
    <dimen name="profile_status_time_height">17dp</dimen>
    <dimen name="profile_status_icon_margin_top">8dp</dimen>
    <dimen name="profile_status_battery_margin_top">10dp</dimen>

    <!-- User info section -->
    <dimen name="profile_avatar_size">65dp</dimen>
    <dimen name="profile_avatar_margin_start">15dp</dimen>
    <dimen name="profile_avatar_margin_top">43dp</dimen>
    <dimen name="profile_username_width">53dp</dimen>
    <dimen name="profile_username_height">23dp</dimen>
    <dimen name="profile_username_margin_start">92dp</dimen>
    <dimen name="profile_username_margin_top">53dp</dimen>
    <dimen name="profile_uid_width">89dp</dimen>
    <dimen name="profile_uid_height">16dp</dimen>
    <dimen name="profile_uid_margin_start">92dp</dimen>
    <dimen name="profile_uid_margin_top">79dp</dimen>
    <dimen name="profile_copy_icon_size">13dp</dimen>
    <dimen name="profile_copy_margin_start">185dp</dimen>
    <dimen name="profile_copy_margin_top">84dp</dimen>
    <dimen name="profile_login_button_width">70dp</dimen>
    <dimen name="profile_login_button_height">36dp</dimen>
    <dimen name="profile_login_button_margin_top">53dp</dimen>
    <dimen name="profile_login_button_margin_end">15dp</dimen>

    <!-- VIP card -->
    <dimen name="profile_vip_card_height">160dp</dimen>
    <dimen name="profile_vip_card_margin_horizontal">15dp</dimen>
    <dimen name="profile_vip_card_margin_top">125dp</dimen>
    <dimen name="profile_vip_card_content_margin">15dp</dimen>
    <dimen name="profile_vip_card_type_margin_top">35dp</dimen>
    <dimen name="profile_vip_card_corner_radius">15dp</dimen>
    <dimen name="profile_vip_card_stroke_width">1dp</dimen>
    <dimen name="profile_vip_card_type_width">292dp</dimen>
    <dimen name="profile_vip_card_type_height">23dp</dimen>
    <dimen name="profile_vip_card_type_use_width">62dp</dimen>
    <dimen name="profile_vip_card_type_use_height">44dp</dimen>
    <dimen name="profile_vip_card_type_use_margin_top">47dp</dimen>
    <dimen name="profile_unlimited_access_icon_size">23dp</dimen>
    <dimen name="profile_vip_star_size">15dp</dimen>
    <dimen name="profile_vip_star_margin_bottom">50dp</dimen>
    <dimen name="profile_vip_star_second_margin_bottom">15dp</dimen>
    <dimen name="profile_vip_go_button_width">77dp</dimen>
    <dimen name="profile_vip_go_button_height">31dp</dimen>
    <dimen name="profile_vip_renewal_text_width">118dp</dimen>
    <dimen name="profile_vip_renewal_text_height">16dp</dimen>
    <dimen name="profile_vip_renewal_period_width">94dp</dimen>
    <dimen name="profile_vip_renewal_period_height">13dp</dimen>
    <dimen name="profile_vip_renewal_icon_size">19dp</dimen>

    <!-- VIP Card Edit Button -->
    <dimen name="profile_edit_button_width">58dp</dimen>
    <dimen name="profile_edit_button_height">31dp</dimen>
    <dimen name="profile_edit_text_size">13sp</dimen>
    <dimen name="profile_auto_renewal_text_size">13sp</dimen>
    <dimen name="profile_renewal_period_text_size">12sp</dimen>

    <!-- Points section -->
    <dimen name="profile_points_card_height">95dp</dimen>
    <dimen name="profile_points_card_margin_top">298dp</dimen>
    <dimen name="profile_points_card_padding">15dp</dimen>
    <dimen name="profile_points_icon_size">23dp</dimen>
    <dimen name="profile_points_text_size">15sp</dimen>
    <dimen name="profile_points_value_size">19sp</dimen>



    <!-- Points section additional -->
    <dimen name="profile_points_jinbi_size">19dp</dimen>
    <dimen name="profile_points_text_line_height">23dp</dimen>
    <dimen name="profile_points_refill_button_width">77dp</dimen>
    <dimen name="profile_points_refill_button_height">31dp</dimen>
    <dimen name="profile_points_refill_text_size">13sp</dimen>

    <!-- Menu items -->
    <dimen name="profile_menu_item_height">54dp</dimen>
    <dimen name="profile_menu_item_padding">15dp</dimen>
    <dimen name="profile_menu_icon_size">23dp</dimen>
    <dimen name="profile_menu_text_size">15sp</dimen>
    <dimen name="profile_menu_text_margin_start">15dp</dimen>
    <dimen name="profile_menu_arrow_size">15dp</dimen>
    <dimen name="profile_menu_margin_horizontal">15dp</dimen>
    <dimen name="profile_menu_margin_vertical">8dp</dimen>
    <dimen name="profile_menu_divider_height">1dp</dimen>
    <dimen name="profile_menu_divider_margin_start">15dp</dimen>

    <!-- Guidelines for responsive layout -->
    <dimen name="profile_content_margin_start">5dp</dimen>
    <dimen name="profile_content_margin_end">5dp</dimen>
    <dimen name="profile_section_spacing">15dp</dimen>

    <!-- Additional text sizes for consistency -->
    <dimen name="login_title_text_size">23sp</dimen>
    <dimen name="login_input_text_size">15sp</dimen>
    <dimen name="login_button_text_size">15sp</dimen>
    <dimen name="verification_subtitle_text_size">13sp</dimen>
    <dimen name="verification_otp_text_size">23sp</dimen>
    <dimen name="verification_otp_input_height">36dp</dimen>
    <dimen name="verification_otp_line_width">34dp</dimen>
    <dimen name="verification_otp_line_height">0.5dp</dimen>
    <dimen name="verification_otp_line_margin_top">12dp</dimen>

    <!-- Profile Status Bar Icon Sizes - Normal phone (scale factor: 0.96) -->
    <dimen name="profile_status_mobile_signal_width">16dp</dimen>
    <dimen name="profile_status_mobile_signal_height">11dp</dimen>
    <dimen name="profile_status_wifi_width">14dp</dimen>
    <dimen name="profile_status_wifi_height">11dp</dimen>
    <dimen name="profile_status_battery_width">17dp</dimen>
    <dimen name="profile_status_battery_height">7dp</dimen>

    <!-- Profile Text Sizes - Normal phone -->
    <dimen name="profile_status_time_text_size">14sp</dimen>
    <dimen name="profile_username_text_size">19sp</dimen>
    <dimen name="profile_uid_text_size">13sp</dimen>
    <dimen name="profile_login_button_text_size">15sp</dimen>
    <dimen name="profile_vip_text_size">13sp</dimen>
    <dimen name="profile_vip_go_button_text_size">13sp</dimen>
    <dimen name="profile_expiration_date_text_size">10sp</dimen>

    <!-- Profile Small Margins - Normal phone -->
    <dimen name="profile_vip_text_margin_start">3dp</dimen>
    <dimen name="profile_points_icon_margin_top">8dp</dimen>
    <dimen name="profile_points_value_margin_start">8dp</dimen>
    <dimen name="profile_expiration_date_margin_end">15dp</dimen>
    <dimen name="profile_expiration_date_margin_bottom">4dp</dimen>

    <!-- Profile Status Bar Icon Margins - Normal phone -->
    <dimen name="profile_status_mobile_signal_margin_end">48dp</dimen>
    <dimen name="profile_status_wifi_margin_end">29dp</dimen>

    <!-- Profile Menu Bottom Margin - Normal phone -->
    <dimen name="profile_menu_bottom_margin">96dp</dimen>

    <!-- VIP Card Border Frame - Normal phone (scale factor: 0.96) -->
    <dimen name="vip_card_border_width">331dp</dimen>
    <dimen name="vip_card_border_height">136dp</dimen>
    <dimen name="vip_card_border_corner_radius">17dp</dimen>
    <dimen name="vip_card_border_stroke_width">1dp</dimen>
    <dimen name="vip_card_border_overflow">4dp</dimen>

    <!-- Subscription Card - Normal phone (scale factor: 0.96) -->
    <dimen name="subscribe_card_price_main_text_size">23sp</dimen>
    <dimen name="subscribe_card_price_unit_text_size">15sp</dimen>

    <!-- Points Page - Normal phone (scale factor: 0.96) -->
    <dimen name="points_header_height">85dp</dimen>
    <dimen name="points_header_padding_horizontal">15dp</dimen>
    <dimen name="points_header_padding_top">65dp</dimen>
    <dimen name="points_back_button_size">23dp</dimen>
    <dimen name="points_title_text_size">17sp</dimen>
    <dimen name="points_balance_margin_top">31dp</dimen>
    <dimen name="points_balance_margin_bottom">31dp</dimen>
    <dimen name="points_coin_icon_size">31dp</dimen>
    <dimen name="points_coin_margin_end">8dp</dimen>
    <dimen name="points_balance_text_size">46sp</dimen>
    <dimen name="points_grid_margin_horizontal">15dp</dimen>
    <dimen name="points_grid_margin_bottom">23dp</dimen>
    <dimen name="points_card_width">159dp</dimen>
    <dimen name="points_card_height">77dp</dimen>
    <dimen name="points_card_margin">6dp</dimen>
    <dimen name="points_card_padding">12dp</dimen>
    <dimen name="points_card_corner_radius">8dp</dimen>
    <dimen name="points_card_selected_stroke_width">1.9dp</dimen>
    <dimen name="points_card_coin_size">15dp</dimen>
    <dimen name="points_card_coin_margin">4dp</dimen>
    <dimen name="points_card_main_text_size">15sp</dimen>
    <dimen name="points_card_bonus_text_size">15sp</dimen>
    <dimen name="points_card_bonus_margin">4dp</dimen>
    <dimen name="points_card_price_text_size">12sp</dimen>
    <dimen name="points_card_price_margin_top">4dp</dimen>
    <dimen name="points_tab_indicator_width">75dp</dimen>
    <dimen name="points_tab_indicator_height">3dp</dimen>
    <dimen name="points_tab_indicator_corner_radius">1dp</dimen>
    <dimen name="points_tab_obtain_indicator_margin">0dp</dimen>
    <dimen name="points_tab_expenses_indicator_margin">75dp</dimen>
    <dimen name="points_tabs_margin_horizontal">15dp</dimen>
    <dimen name="points_tabs_margin_bottom">15dp</dimen>
    <dimen name="points_tab_text_size">15sp</dimen>
    <dimen name="points_tab_margin_end">23dp</dimen>
    <dimen name="points_list_margin_horizontal">15dp</dimen>
    <dimen name="points_item_padding_vertical">15dp</dimen>
    <dimen name="points_item_title_text_size">13sp</dimen>
    <dimen name="points_item_subtitle_text_size">12sp</dimen>
    <dimen name="points_item_subtitle_margin_top">4dp</dimen>
    <dimen name="points_item_time_text_size">10sp</dimen>
    <dimen name="points_item_time_margin_top">2dp</dimen>
    <dimen name="points_item_amount_text_size">13sp</dimen>
    <dimen name="points_item_coin_size">15dp</dimen>
    <dimen name="points_item_coin_margin">4dp</dimen>
    <dimen name="points_tips_margin_horizontal">15dp</dimen>
    <dimen name="points_tips_margin_top">23dp</dimen>
    <dimen name="points_tips_title_text_size">15sp</dimen>
    <dimen name="points_tips_title_margin_bottom">12dp</dimen>
    <dimen name="points_tips_subtitle_text_size">13sp</dimen>
    <dimen name="points_tips_content_text_size">12sp</dimen>
    <dimen name="points_tips_item_margin_bottom">8dp</dimen>
    <dimen name="points_tips_bottom_margin">23dp</dimen>
    <dimen name="points_refill_button_padding">15dp</dimen>
    <dimen name="points_refill_button_height">46dp</dimen>
    <dimen name="points_refill_button_text_size">15sp</dimen>
    <dimen name="points_refill_button_corner_radius">23dp</dimen>

    <!-- Bill Page - sw360dp -->
    <!-- Header -->
    <dimen name="bill_header_padding_horizontal">16dp</dimen> <!-- 统一基准值 -->

    <!-- VIP Record Card - 统一基准值 -->
    <dimen name="vip_record_first_card_margin_top">22dp</dimen> <!-- 统一基准值 -->

    <!-- Points Purchase Card - 统一基准值 -->
    <dimen name="points_purchase_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Video Record Card - 统一基准值 -->
    <dimen name="video_record_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Message Page - 统一基准值 -->
    <dimen name="message_first_card_margin_top">24dp</dimen> <!-- 统一基准值 -->

    <!-- Message Detail Page - 适配360dp屏幕 (360/375 ≈ 0.96) -->
    <dimen name="message_detail_card_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="message_detail_card_min_height">628dp</dimen> <!-- 654 × 0.96 -->
    <dimen name="message_detail_card_margin_top">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="message_detail_divider_width">306dp</dimen> <!-- 319 × 0.96 -->
    <dimen name="message_detail_divider_height">0.5dp</dimen> <!-- 保持不变 -->
    <dimen name="message_detail_divider_margin_vertical">15dp</dimen> <!-- 16 × 0.96 -->

    <!-- Setting Page - 适配360dp屏幕 (360/375 ≈ 0.96) -->
    <dimen name="setting_first_item_margin_start">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_first_item_margin_top">31dp</dimen> <!-- 32 × 0.96 -->
    <dimen name="setting_double_item_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="setting_double_item_height">90dp</dimen> <!-- 94 × 0.96 -->
    <dimen name="setting_single_item_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="setting_single_item_height">49dp</dimen> <!-- 51 × 0.96 -->
    <dimen name="setting_item_margin_bottom">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_item_padding">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_text_size">16sp</dimen> <!-- 16 × 1.0 -->
    <dimen name="setting_text_line_height">18dp</dimen> <!-- 19 × 0.96 -->
    <dimen name="setting_arrow_size">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_terms_text_width">116dp</dimen> <!-- 121 × 0.96 -->
    <dimen name="setting_cache_text_width">83dp</dimen> <!-- 86 × 0.96 -->
    <dimen name="setting_cache_size_width">53dp</dimen> <!-- 55 × 0.96 -->
    <dimen name="setting_bottom_buttons_margin_top">31dp</dimen> <!-- 32 × 0.96 -->
    <dimen name="setting_bottom_buttons_margin_bottom">31dp</dimen> <!-- 32 × 0.96 -->
    <dimen name="setting_bottom_button_margin_horizontal">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_bottom_button_margin_between">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="setting_bottom_button_corner_radius">20dp</dimen> <!-- 21 × 0.96 -->

    <!-- Tabs - 响应式适配 -->
    <dimen name="bill_tabs_margin_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_tabs_background_width">328dp</dimen> <!-- 360-32=328dp -->
    <dimen name="bill_selected_tab_width">107dp</dimen> <!-- (328-6)/3=107dp -->

    <!-- Video Detail Page Dimensions (360dp width, scale factor: 1.0 - BASE) -->

    <!-- Top poster area -->
    <dimen name="video_detail_poster_height">500dp</dimen>

    <!-- Navigation buttons -->
    <dimen name="video_detail_back_button_size">16dp</dimen>
    <dimen name="video_detail_share_button_size">22dp</dimen>
    <dimen name="video_detail_nav_button_margin_top">55dp</dimen>
    <dimen name="video_detail_nav_button_margin_horizontal">16dp</dimen>

    <!-- Small poster -->
    <dimen name="video_detail_small_poster_width">84dp</dimen>
    <dimen name="video_detail_small_poster_height">112dp</dimen>
    <dimen name="video_detail_small_poster_corner_radius">11dp</dimen>
    <dimen name="video_detail_small_poster_margin_start">16dp</dimen>
    <dimen name="video_detail_small_poster_margin_top">20dp</dimen>

    <!-- Video info -->
    <dimen name="video_detail_title_margin_start">12dp</dimen>
    <dimen name="video_detail_title_margin_top">6dp</dimen>
    <dimen name="video_detail_title_text_size">20sp</dimen>

    <dimen name="video_detail_view_icon_size">18dp</dimen>
    <dimen name="video_detail_view_text_margin_start">12dp</dimen>
    <dimen name="video_detail_view_text_margin_top">15dp</dimen>
    <dimen name="video_detail_view_text_size">13sp</dimen>

    <!-- Like button -->
    <dimen name="video_detail_like_button_width">144dp</dimen>
    <dimen name="video_detail_like_button_height">32dp</dimen>
    <dimen name="video_detail_like_button_corner_radius">16dp</dimen>
    <dimen name="video_detail_like_button_margin_top">18dp</dimen>
    <dimen name="video_detail_like_icon_size">20dp</dimen>
    <dimen name="video_detail_like_text_size">14sp</dimen>

    <!-- Synopsis section -->
    <dimen name="video_detail_synopsis_title_text_size">18sp</dimen>
    <dimen name="video_detail_synopsis_margin_top">20dp</dimen>
    <dimen name="video_detail_synopsis_title_margin_bottom">14dp</dimen>
    <dimen name="video_detail_synopsis_element_spacing">14dp</dimen>

    <!-- Tags -->
    <dimen name="video_detail_tag_width">80dp</dimen>
    <dimen name="video_detail_tag_height">30dp</dimen>
    <dimen name="video_detail_tag_corner_radius">10dp</dimen>
    <dimen name="video_detail_tag_spacing">10dp</dimen>

    <!-- Synopsis content -->
    <dimen name="video_detail_synopsis_content_margin_top">14dp</dimen>
    <dimen name="video_detail_synopsis_content_text_size">14sp</dimen>
    <dimen name="video_detail_synopsis_content_line_height">21dp</dimen>

    <!-- Actor section -->
    <dimen name="video_detail_actor_margin_top">20dp</dimen>
    <dimen name="video_detail_actor_poster_width">74dp</dimen>
    <dimen name="video_detail_actor_poster_height">91.5dp</dimen>
    <dimen name="video_detail_actor_poster_corner_radius">9dp</dimen>
    <dimen name="video_detail_actor_spacing">12dp</dimen>
    <dimen name="video_detail_actor_name_margin_top">4dp</dimen>

    <!-- Director section -->
    <dimen name="video_detail_director_margin_top">20dp</dimen>
    <dimen name="video_detail_director_poster_width">74dp</dimen>
    <dimen name="video_detail_director_poster_height">91.5dp</dimen>
    <dimen name="video_detail_director_poster_corner_radius">9dp</dimen>
    <dimen name="video_detail_director_spacing">12dp</dimen>
    <dimen name="video_detail_director_name_margin_top">4dp</dimen>

    <!-- Episodes section -->
    <dimen name="video_detail_episodes_margin_top">23dp</dimen>
    <dimen name="video_detail_episodes_title_text_size">18sp</dimen>
    <dimen name="video_detail_episodes_range_margin_top">13dp</dimen>
    <dimen name="video_detail_episodes_range_text_size">14sp</dimen>
    <dimen name="video_detail_episodes_range_spacing">25dp</dimen>
    <dimen name="video_detail_episodes_range_underline_margin_top">7dp</dimen>

    <dimen name="video_detail_episode_tag_width">61dp</dimen>
    <dimen name="video_detail_episode_tag_height">36dp</dimen>
    <dimen name="video_detail_episode_tag_corner_radius">12dp</dimen>
    <dimen name="video_detail_episode_tag_spacing">10dp</dimen>
    <dimen name="video_detail_episode_tag_margin_top">16dp</dimen>
    <dimen name="video_detail_episode_text_size">16sp</dimen>
    <dimen name="video_detail_episode_icon_size">18dp</dimen>
    <dimen name="video_detail_episode_icon_margin_start">2dp</dimen>

    <!-- More Like This section -->
    <dimen name="video_detail_more_like_this_margin_top">33dp</dimen>
    <dimen name="video_detail_more_like_this_title_text_size">18sp</dimen>
    <dimen name="video_detail_more_like_this_grid_margin_top">12dp</dimen>

    <!-- Continue Playing Button (360dp width, scale factor: 1.0 - BASE) -->
    <dimen name="continue_playing_button_width">343dp</dimen>
    <dimen name="continue_playing_button_height">42dp</dimen>
    <dimen name="continue_playing_button_corner_radius">21dp</dimen>
    <dimen name="continue_playing_button_margin_bottom">20dp</dimen>
    <dimen name="continue_playing_button_text_size">16sp</dimen>

    <!-- Edit Profile Page - 适配360dp屏幕 (360/375 ≈ 0.96) -->
    <dimen name="edit_profile_card_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="edit_profile_card_height">230dp</dimen> <!-- 240 × 0.96 -->
    <dimen name="edit_profile_card_margin_top">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="edit_profile_avatar_size">46dp</dimen> <!-- 48 × 0.96 -->
    <dimen name="edit_profile_arrow_size">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="edit_profile_divider_width">299dp</dimen> <!-- 311 × 0.96 -->
    <dimen name="edit_profile_input_width">299dp</dimen> <!-- 311 × 0.96 -->
    <dimen name="edit_profile_input_height">46dp</dimen> <!-- 48 × 0.96 -->
    <dimen name="edit_profile_ip_location_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="edit_profile_ip_location_height">48dp</dimen> <!-- 50 × 0.96 -->
    <dimen name="edit_profile_save_button_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="edit_profile_save_button_height">40dp</dimen> <!-- 42 × 0.96 -->
    <dimen name="edit_profile_text_size">16sp</dimen> <!-- 16 × 1.0 -->
    <dimen name="edit_profile_text_line_height">18dp</dimen> <!-- 19 × 0.96 -->
    <dimen name="edit_profile_china_text_width">58dp</dimen> <!-- 60 × 0.96 -->
    <dimen name="edit_profile_save_text_width">36dp</dimen> <!-- 37 × 0.96 -->
    <dimen name="edit_profile_item_margin_bottom">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="edit_profile_item_padding">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="edit_profile_save_button_margin_bottom">36dp</dimen> <!-- 38 × 0.96 -->
    <dimen name="edit_profile_popup_width">115dp</dimen> <!-- 120 × 0.96 -->
    <dimen name="edit_profile_popup_item_height">31dp</dimen> <!-- 32 × 0.96 -->
    <dimen name="edit_profile_popup_padding">8dp</dimen> <!-- 8 × 0.96 -->
    <dimen name="edit_profile_popup_text_size">14sp</dimen> <!-- 14 × 1.0 -->
    <dimen name="edit_profile_popup_corner_radius">8dp</dimen> <!-- 8 × 0.96 -->

    <!-- Log Off Page - 适配360dp屏幕 (360/375 ≈ 0.96) -->
    <dimen name="log_off_warning_icon_size">58dp</dimen> <!-- 60 × 0.96 -->
    <dimen name="log_off_warning_icon_margin_top">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="log_off_description_width">315dp</dimen> <!-- 328 × 0.96 -->
    <dimen name="log_off_description_height">58dp</dimen> <!-- 60 × 0.96 -->
    <dimen name="log_off_description_margin_top">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="log_off_card_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="log_off_card_height">307dp</dimen> <!-- 320 × 0.96 -->
    <dimen name="log_off_card_margin_top">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="log_off_card_padding">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="log_off_card_item_line_height">25dp</dimen> <!-- 26 × 0.96 -->
    <dimen name="log_off_checkbox_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="log_off_checkbox_height">58dp</dimen> <!-- 60 × 0.96 -->
    <dimen name="log_off_checkbox_margin_top">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="log_off_checkbox_icon_size">19dp</dimen> <!-- 20 × 0.96 -->
    <dimen name="log_off_checkbox_icon_margin_end">8dp</dimen> <!-- 8 × 0.96 -->
    <dimen name="log_off_checkbox_text_margin_start">27dp</dimen> <!-- 28 × 0.96 -->
    <dimen name="log_off_checkbox_text_size">13sp</dimen> <!-- 13 × 1.0 -->
    <dimen name="log_off_checkbox_text_line_spacing">7dp</dimen> <!-- 7 × 0.96 -->
    <dimen name="log_off_delete_button_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="log_off_delete_button_height">40dp</dimen> <!-- 42 × 0.96 -->
    <dimen name="log_off_delete_button_margin_top">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="log_off_delete_button_margin_bottom">36dp</dimen> <!-- 38 × 0.96 -->
    <dimen name="log_off_delete_text_width">110dp</dimen> <!-- 115 × 0.96 -->
    <dimen name="log_off_delete_text_height">18dp</dimen> <!-- 19 × 0.96 -->
    <dimen name="log_off_item_margin_horizontal">15dp</dimen> <!-- 16 × 0.96 -->

    <!-- Feedback Page - 适配360dp屏幕 (360/375 = 0.96) -->
    <dimen name="feedback_required_star_width">7dp</dimen> <!-- 7 × 0.96 -->
    <dimen name="feedback_required_star_height">18dp</dimen> <!-- 19 × 0.96 -->
    <dimen name="feedback_label_width">104dp</dimen> <!-- 108 × 0.96 -->
    <dimen name="feedback_label_height">18dp</dimen> <!-- 19 × 0.96 -->
    <dimen name="feedback_type_selected_width">108dp</dimen> <!-- 113 × 0.96 -->
    <dimen name="feedback_type_selected_height">38dp</dimen> <!-- 40 × 0.96 -->
    <dimen name="feedback_type_unselected_width">209dp</dimen> <!-- 218 × 0.96 -->
    <dimen name="feedback_type_unselected_height">38dp</dimen> <!-- 40 × 0.96 -->
    <dimen name="feedback_type_corner_radius">13dp</dimen> <!-- 14 × 0.96 -->
    <dimen name="feedback_input_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="feedback_input_height">179dp</dimen> <!-- 186 × 0.96 -->
    <dimen name="feedback_input_corner_radius">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="feedback_input_padding">14dp</dimen> <!-- 15 × 0.96 -->
    <dimen name="feedback_image_size">103dp</dimen> <!-- 107 × 0.96 -->
    <dimen name="feedback_image_corner_radius">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="feedback_image_icon_size">27dp</dimen> <!-- 28 × 0.96 -->
    <dimen name="feedback_button_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="feedback_button_height">40dp</dimen> <!-- 42 × 0.96 -->
    <dimen name="feedback_button_corner_radius">20dp</dimen> <!-- 21 × 0.96 -->
    <dimen name="feedback_item_margin_horizontal">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="feedback_item_margin_vertical">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="feedback_type_margin_end">8dp</dimen> <!-- 8 × 0.96 -->

    <!-- Interest Tab Card Dimensions - sw360dp (缩放因子: 1.0，基准360dp) -->
    <!-- Card Layout -->
    <dimen name="interest_card_margin_horizontal">16dp</dimen> <!-- 基准值 -->
    <dimen name="interest_card_margin_bottom">18dp</dimen> <!-- 基准值 -->

    <!-- Poster - 放大尺寸 -->
    <dimen name="interest_poster_width">120dp</dimen> <!-- 基准值 -->
    <dimen name="interest_poster_height">160dp</dimen> <!-- 基准值 -->

    <!-- Content Area -->
    <dimen name="interest_content_margin_start">14dp</dimen> <!-- 基准值 -->

    <!-- Title -->
    <dimen name="interest_title_width">200dp</dimen> <!-- 基准值 -->
    <dimen name="interest_title_height">22dp</dimen> <!-- 基准值 -->
    <dimen name="interest_title_text_size">16sp</dimen> <!-- 基准值 -->

    <!-- Status Tag - 调整位置和大小 -->
    <dimen name="interest_status_tag_width">88dp</dimen> <!-- 基准值 -->
    <dimen name="interest_status_tag_height">22dp</dimen> <!-- 基准值 -->
    <dimen name="interest_status_tag_margin_top">6dp</dimen> <!-- 基准值 -->
    <dimen name="interest_status_text_width">76dp</dimen> <!-- 基准值 -->
    <dimen name="interest_status_text_height">14dp</dimen> <!-- 基准值 -->
    <dimen name="interest_status_text_size">12sp</dimen> <!-- 基准值 -->

    <!-- Description - 优化文字大小 -->
    <dimen name="interest_description_width">200dp</dimen> <!-- 基准值 -->
    <dimen name="interest_description_height">36dp</dimen> <!-- 基准值 -->
    <dimen name="interest_description_margin_top">8dp</dimen> <!-- 基准值 -->
    <dimen name="interest_description_text_size">12sp</dimen> <!-- 基准值 -->

    <!-- Time Info -->
    <dimen name="interest_time_container_margin_top">12dp</dimen> <!-- 基准值 -->
    <dimen name="interest_time_icon_size">14dp</dimen> <!-- 基准值 -->
    <dimen name="interest_time_text_width">80dp</dimen> <!-- 基准值 -->
    <dimen name="interest_time_text_height">18dp</dimen> <!-- 基准值 -->
    <dimen name="interest_time_text_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="interest_time_text_size">12sp</dimen> <!-- 基准值 -->

    <!-- Cancel Button -->
    <dimen name="interest_cancel_button_width">180dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_button_height">28dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_button_margin_top">12dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_icon_size">14dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_text_width">80dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_text_height">14dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_text_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="interest_cancel_text_size">12sp</dimen> <!-- 基准值 -->

    <!-- Video Player Dimensions - sw360dp (基准值) -->
    <dimen name="video_player_progress_margin_top">10dp</dimen>
    <dimen name="video_player_bottom_controls_margin_top">10dp</dimen>

    <!-- Subtitle Panel Dimensions - sw360dp -->
    <dimen name="subtitle_panel_height">318dp</dimen>
    <dimen name="subtitle_panel_corner_radius_top">24dp</dimen>
    <dimen name="subtitle_panel_close_button_size">18dp</dimen>
    <dimen name="subtitle_panel_close_button_margin_top">24dp</dimen>
    <dimen name="subtitle_panel_close_button_margin_end">16dp</dimen>
    <dimen name="subtitle_panel_title_margin_top">24dp</dimen>
    <dimen name="subtitle_panel_title_text_size">18sp</dimen>
    <dimen name="subtitle_panel_subtitle_margin_top">16dp</dimen>
    <dimen name="subtitle_panel_subtitle_text_size">16sp</dimen>
    <dimen name="subtitle_panel_switch_margin_top">7dp</dimen>
    <dimen name="subtitle_panel_switch_size">36dp</dimen>
    <dimen name="subtitle_panel_language_margin_top">28dp</dimen>
    <dimen name="subtitle_panel_language_item_margin_top">16dp</dimen>
    <dimen name="subtitle_panel_language_width">310dp</dimen>
    <dimen name="subtitle_panel_language_height">42dp</dimen>
    <dimen name="subtitle_panel_language_corner_radius">14dp</dimen>
    <dimen name="subtitle_panel_language_text_size">16sp</dimen>
    <dimen name="subtitle_panel_language_icon_size">18dp</dimen>
    <dimen name="subtitle_panel_language_icon_margin_start">12dp</dimen>

    <!-- Video Poster Popup Dimensions - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="poster_popup_margin_top">140dp</dimen> <!-- 基准值 -->
    <dimen name="poster_popup_width">298dp</dimen> <!-- 基准值 -->
    <dimen name="poster_popup_height">466dp</dimen> <!-- 基准值 -->
    <dimen name="poster_popup_corner_radius">18dp</dimen> <!-- 基准值 -->
    <dimen name="poster_popup_close_button_size">30dp</dimen> <!-- 基准值 -->
    <dimen name="poster_popup_close_button_margin_top">16dp</dimen> <!-- 基准值 -->

    <!-- VIP Unlock Popup Dimensions - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="vip_unlock_popup_margin_top">240dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_message_width">270dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_message_text_size">20sp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_message_line_spacing">4dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_width">294dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_height">62dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_corner_radius">18dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_text_size">18sp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_margin_top">29dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_button_spacing">16dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_coin_icon_size">22dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_coin_icon_margin_start">10dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_coin_text_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_coin_text_size">20sp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_close_button_size">30dp</dimen> <!-- 基准值 -->
    <dimen name="vip_unlock_close_button_margin_top">44dp</dimen> <!-- 基准值 -->

    <!-- Watch AD Popup Dimensions - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="watch_ad_popup_width">294dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_height">62dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_corner_radius">18dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_text_size">14sp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_coin_text_size">20sp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_coin_icon_size">22dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_coin_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_text_margin_top">3dp</dimen> <!-- 基准值 -->
    <dimen name="watch_ad_popup_padding_vertical">8dp</dimen> <!-- 基准值 -->

    <!-- Video Player Fixed Values Converted to Responsive - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="video_player_side_button_container_width">60dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_seek_button_size">48dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_seek_button_padding">8dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_seek_button_margin">50dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_episode_selection_width">112dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin">16dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin_small">8dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_margin_medium">10dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_common_padding">15dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_height">14dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_margin">5dp</dimen> <!-- 基准值 -->
    <dimen name="video_player_progress_touch_area_margin_negative">-5dp</dimen> <!-- 基准值 -->

    <!-- Video Detail Fixed Values Converted to Responsive - Normal phone (360dp, scale factor 1.0 - BASE) -->
    <dimen name="video_detail_poster_border_margin">1dp</dimen> <!-- 基准值 -->
    <dimen name="video_detail_view_count_margin_start">4dp</dimen> <!-- 基准值 -->
    <dimen name="video_detail_like_text_margin_start">8dp</dimen> <!-- 基准值 -->

    <!-- Search Page Dimensions Scaled for 360dp (0.96x) -->

    <!-- Back button -->
    <dimen name="search_back_button_size">23dp</dimen> <!-- 24 × 0.96 -->
    <dimen name="search_back_button_margin_start">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_back_button_margin_top">52dp</dimen> <!-- 54 × 0.96 -->

    <!-- Search input -->
    <dimen name="search_input_width">295dp</dimen> <!-- 307 × 0.96 -->
    <dimen name="search_input_height">36dp</dimen> <!-- 38 × 0.96 -->
    <dimen name="search_input_margin_start">50dp</dimen> <!-- 52 × 0.96 -->
    <dimen name="search_input_margin_top">45dp</dimen> <!-- 47 × 0.96 -->
    <dimen name="search_input_corner_radius">12dp</dimen> <!-- 12 × 0.96 -->
    <dimen name="search_input_padding_start">12dp</dimen> <!-- 12 × 0.96 -->
    <dimen name="search_input_padding_vertical">8dp</dimen> <!-- 8 × 0.96 -->

    <!-- Search icon in input -->
    <dimen name="search_input_icon_size">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_input_icon_margin_start">12dp</dimen> <!-- 12 × 0.96 -->

    <!-- Search text -->
    <dimen name="search_text_size">15sp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_text_margin_start">94dp</dimen> <!-- 98 × 0.96 -->
    <dimen name="search_text_margin_top">54dp</dimen> <!-- 56 × 0.96 -->

    <!-- Recent searches section -->
    <dimen name="search_recent_title_margin_start">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_recent_title_margin_top">100dp</dimen> <!-- 104 × 0.96 -->
    <dimen name="search_recent_title_size">17sp</dimen> <!-- 18 × 0.96 -->

    <!-- Clear recent searches icon -->
    <dimen name="search_clear_icon_size">17dp</dimen> <!-- 18 × 0.96 -->
    <dimen name="search_clear_icon_margin_end">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_clear_icon_margin_top">101dp</dimen> <!-- 105 × 0.96 -->

    <!-- Recent search tags -->
    <dimen name="search_recent_tag_height">31dp</dimen> <!-- 32 × 0.96 -->
    <dimen name="search_recent_tag_padding_horizontal">12dp</dimen> <!-- 12 × 0.96 -->
    <dimen name="search_recent_tag_padding_vertical">6dp</dimen> <!-- 6 × 0.96 -->
    <dimen name="search_recent_tag_margin">4dp</dimen> <!-- 4 × 0.96 -->
    <dimen name="search_recent_tag_text_size">13sp</dimen> <!-- 14 × 0.96 -->
    <dimen name="search_recent_tag_corner_radius">15dp</dimen> <!-- 16 × 0.96 -->

    <!-- Trending searches section -->
    <dimen name="search_trending_title_size">17sp</dimen> <!-- 18 × 0.96 -->
    <dimen name="search_trending_title_margin_start">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_trending_title_margin_top">12dp</dimen> <!-- 12 × 0.96 -->

    <!-- Trending search cards -->
    <dimen name="search_trending_card_width">329dp</dimen> <!-- 343 × 0.96 -->
    <dimen name="search_trending_card_height">92dp</dimen> <!-- 96 × 0.96 -->
    <dimen name="search_trending_card_margin_horizontal">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_trending_card_margin_vertical">6dp</dimen> <!-- 6 × 0.96 -->
    <dimen name="search_trending_card_corner_radius">12dp</dimen> <!-- 12 × 0.96 -->
    <dimen name="search_trending_card_padding">13dp</dimen> <!-- 14 × 0.96 -->

    <!-- Rank background -->
    <dimen name="search_rank_bg_size">27dp</dimen> <!-- 28 × 0.96 -->
    <dimen name="search_rank_bg_corner_radius">6dp</dimen> <!-- 6 × 0.96 -->
    <dimen name="search_rank_text_size">15sp</dimen> <!-- 16 × 0.96 -->

    <!-- Poster in trending card -->
    <dimen name="search_poster_size">65dp</dimen> <!-- 68 × 0.96 -->
    <dimen name="search_poster_corner_radius">8dp</dimen> <!-- 8 × 0.96 -->
    <dimen name="search_poster_margin_start">10dp</dimen> <!-- 10 × 0.96 -->

    <!-- Content in trending card -->
    <dimen name="search_content_margin_start">10dp</dimen> <!-- 10 × 0.96 -->
    <dimen name="search_title_text_size">13sp</dimen> <!-- 14 × 0.96 -->
    <dimen name="search_count_text_size">12sp</dimen> <!-- 12 × 0.96 -->
    <dimen name="search_count_icon_size">15dp</dimen> <!-- 16 × 0.96 -->
    <dimen name="search_count_icon_margin_end">4dp</dimen> <!-- 4 × 0.96 -->
    <dimen name="search_count_margin_top">22dp</dimen> <!-- 23 × 0.96 -->

    <!-- Most Popular Page Specific -->
    <dimen name="most_popular_video_title_text_size">16sp</dimen>

</resources>
