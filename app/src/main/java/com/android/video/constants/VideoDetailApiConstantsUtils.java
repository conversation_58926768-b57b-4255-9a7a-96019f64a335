package com.android.video.constants;

/**
 * 视频详情API常量工具类
 * <p>
 * 定义视频详情相关API的路径、参数名称和其他常量。
 * 继承BaseApiConstantsUtils以获得通用的API常量和工具方法。
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建视频详情API URL
 * String apiUrl = VideoDetailApiConstantsUtils.buildVideoDetailUrl(
 *     EnvironmentConfigUtils.getApiBaseUrl(),
 *     "c655490792384ed887bfe20e56f30c47"
 * );
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class VideoDetailApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== API路径常量 ==========

    /**
     * 获取视频详情API路径
     * <p>
     * 用于获取单个视频的详细信息，包括短剧信息、演员、导演、章节列表等。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>filmLanguageInfoId: 短剧语言信息ID，必填</li>
     * </ul>
     * </p>
     * 
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "filmInfo": {
     *       "filmTitle": "这是英语标题111",
     *       "categoryId": "b0ae69449594450e93118e4e4ed77a78,8024cb2a83ff4182b78d8f38c279e5c0",
     *       "categoryName": "喜剧2, ",
     *       "cover": "https://short-play-cdn.hb.kz-ast.bizmrg.com/short_play/05d09049-560d-4e10-8972-45f8159c036b_cover.jpg",
     *       "languageType": 1,
     *       "filmId": "32f4e3b38fc043febb90017c2babcb90",
     *       "filmLanguageInfoId": "c655490792384ed887bfe20e56f30c47",
     *       "searchNum": null,
     *       "details": "这是简介",
     *       "isLove": 1,
     *       "playNum": 0,
     *       "totalChaptersNum": null
     *     },
     *     "labelNames": "",
     *     "releaseTime": null,
     *     "directorInfo": [...],
     *     "performerInfo": [...],
     *     "playProgressVO": {...},
     *     "chapterList": [...]
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_FILM_DETAIL = "/app/index/getFilmDetail";

    // ========== 请求参数常量 ==========

    /**
     * 短剧语言信息ID参数名
     */
    public static final String PARAM_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    // ========== URL构建工具方法 ==========

    /**
     * 构建视频详情查询URL
     * <p>
     * 根据基础URL和短剧语言信息ID构建完整的视频详情查询URL。
     * </p>
     * 
     * @param baseUrl API基础URL
     * @param filmLanguageInfoId 短剧语言信息ID
     * @return 完整的API URL
     * 
     * @throws IllegalArgumentException 如果参数为null或空字符串
     * 
     * @example
     * <pre>
     * String url = buildVideoDetailUrl(
     *     "https://short-play-api.gymooit.cn/v1", 
     *     "c655490792384ed887bfe20e56f30c47"
     * );
     * // 返回: "https://short-play-api.gymooit.cn/v1/app/index/getFilmDetail?filmLanguageInfoId=c655490792384ed887bfe20e56f30c47"
     * </pre>
     */
    public static String buildVideoDetailUrl(String baseUrl, String filmLanguageInfoId) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }
        
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            throw new IllegalArgumentException("Film language info ID cannot be null or empty");
        }

        // 确保baseUrl不以斜杠结尾
        String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        
        // 构建完整URL
        return cleanBaseUrl + API_GET_FILM_DETAIL + "?" + PARAM_FILM_LANGUAGE_INFO_ID + "=" + filmLanguageInfoId;
    }

    /**
     * 验证短剧语言信息ID格式
     * <p>
     * 检查短剧语言信息ID是否符合预期格式。
     * </p>
     * 
     * @param filmLanguageInfoId 短剧语言信息ID
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidFilmLanguageInfoId(String filmLanguageInfoId) {
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            return false;
        }
        
        // 检查长度（通常为32位十六进制字符串）
        String trimmed = filmLanguageInfoId.trim();
        if (trimmed.length() != 32) {
            return false;
        }
        
        // 检查是否只包含十六进制字符
        return trimmed.matches("^[a-fA-F0-9]+$");
    }

    /**
     * 获取API完整路径
     * <p>
     * 返回包含基础路径的完整API路径。
     * </p>
     * 
     * @param baseUrl API基础URL
     * @return 完整的API路径
     */
    public static String getFullApiPath(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_FILM_DETAIL);
    }
}
