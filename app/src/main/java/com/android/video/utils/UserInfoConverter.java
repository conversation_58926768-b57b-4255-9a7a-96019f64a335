package com.android.video.utils;

import com.android.video.constants.ProfileApiConstantsUtils;
import com.android.video.model.UserInfo;
import com.android.video.model.UserModel;

/**
 * 用户信息转换工具类
 * <p>
 * 提供API返回的UserInfo对象与现有UserModel对象之间的转换功能，
 * 确保新的API数据能够与现有的UI代码兼容。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see UserInfo
 * @see UserModel
 */
public class UserInfoConverter {
    
    /**
     * 将API返回的UserInfo转换为UserModel
     * <p>
     * 将从个人中心API获取的详细用户信息转换为现有UI代码使用的UserModel格式。
     * </p>
     * 
     * @param userInfo API返回的用户信息对象
     * @return 转换后的UserModel对象，如果输入为null则返回默认的访客用户
     */
    public static UserModel convertToUserModel(UserInfo userInfo) {
        if (userInfo == null) {
            // 返回默认的访客用户
            return new UserModel();
        }
        
        UserModel userModel = new UserModel();
        
        // 设置登录状态为true（能获取到用户信息说明已登录）
        userModel.setLoggedIn(true);
        
        // 基本信息映射
        userModel.setUsername(userInfo.getCustomerName() != null ? userInfo.getCustomerName() : "User");
        userModel.setUid(userInfo.getCustomerId() != null ? userInfo.getCustomerId() : "");
        userModel.setPhoneNumber(userInfo.getPhoneNumber() != null ? userInfo.getPhoneNumber() : "");
        userModel.setAvatar(userInfo.getHeadImg() != null ? userInfo.getHeadImg() : "");
        
        // VIP状态映射
        // UserModel中 0=普通用户, 1=VIP用户
        // API中 isVip: 0=否, 1=是
        if (userInfo.isVipUser()) {
            userModel.setUserVip(1); // VIP用户
        } else {
            userModel.setUserVip(0); // 普通用户
        }
        
        // 积分信息
        userModel.setPoints(userInfo.getPoints());

        // VIP相关信息
        userModel.setVipDays(userInfo.getVipDays());
        userModel.setVipExpireDate(userInfo.getVipExpireDate());

        // 邮箱信息（API中没有，保持为空）
        userModel.setEmail("");

        return userModel;
    }
    
    /**
     * 将UserModel转换为UserInfo
     * <p>
     * 将现有的UserModel对象转换为UserInfo格式，主要用于数据同步。
     * 注意：由于UserModel包含的信息较少，转换后的UserInfo只包含基本信息。
     * </p>
     * 
     * @param userModel 现有的用户模型对象
     * @return 转换后的UserInfo对象，如果输入为null则返回null
     */
    public static UserInfo convertToUserInfo(UserModel userModel) {
        if (userModel == null) {
            return null;
        }
        
        UserInfo userInfo = new UserInfo();
        
        // 基本信息映射
        userInfo.setCustomerName(userModel.getUsername());
        userInfo.setCustomerId(userModel.getUid());
        userInfo.setPhoneNumber(userModel.getPhoneNumber());
        userInfo.setHeadImg(userModel.getAvatar());
        userInfo.setPoints(userModel.getPoints());
        
        // VIP状态映射
        if (userModel.isVip()) {
            userInfo.setIsVip(ProfileApiConstantsUtils.VIP_STATUS_YES);
        } else {
            userInfo.setIsVip(ProfileApiConstantsUtils.VIP_STATUS_NO);
        }
        
        return userInfo;
    }
    
    /**
     * 更新UserModel的访问令牌
     * <p>
     * 当从API获取到新的用户令牌时，更新ApiHeaderUtils中的访问令牌。
     * </p>
     * 
     * @param userInfo 包含新令牌的用户信息
     */
    public static void updateAccessTokenFromUserInfo(UserInfo userInfo) {
        if (userInfo != null && userInfo.getUserToken() != null && !userInfo.getUserToken().isEmpty()) {
            ApiHeaderUtils.setAccessToken(userInfo.getUserToken());
        }
    }
    
    /**
     * 创建用户信息摘要字符串
     * <p>
     * 生成用户信息的简要描述，用于日志记录和调试。
     * </p>
     * 
     * @param userInfo 用户信息对象
     * @return 用户信息摘要字符串
     */
    public static String createUserInfoSummary(UserInfo userInfo) {
        if (userInfo == null) {
            return "用户信息为空";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("用户信息摘要: ");
        summary.append("姓名=").append(userInfo.getCustomerName());
        summary.append(", 手机号=").append(maskPhoneNumber(userInfo.getPhoneNumber()));
        summary.append(", 积分=").append(userInfo.getPoints());
        
        if (userInfo.isVipUser()) {
            summary.append(", VIP状态=是");
            if (userInfo.getVipDays() != null) {
                summary.append(", 剩余天数=").append(userInfo.getVipDays());
            }
        } else {
            summary.append(", VIP状态=否");
        }
        
        return summary.toString();
    }
    
    /**
     * 创建UserModel信息摘要字符串
     * <p>
     * 生成UserModel的简要描述，用于日志记录和调试。
     * </p>
     * 
     * @param userModel 用户模型对象
     * @return 用户模型摘要字符串
     */
    public static String createUserModelSummary(UserModel userModel) {
        if (userModel == null) {
            return "用户模型为空";
        }
        
        StringBuilder summary = new StringBuilder();
        summary.append("用户模型摘要: ");
        summary.append("用户名=").append(userModel.getUsername());
        summary.append(", UID=").append(userModel.getUid());
        summary.append(", 手机号=").append(maskPhoneNumber(userModel.getPhoneNumber()));
        summary.append(", VIP=").append(userModel.isVip() ? "是" : "否");
        summary.append(", 积分=").append(userModel.getPoints());
        summary.append(", 登录状态=").append(userModel.isLoggedIn() ? "已登录" : "未登录");
        
        return summary.toString();
    }
    
    /**
     * 掩码手机号码
     * <p>
     * 将手机号码中间部分用*号替换，保护用户隐私。
     * </p>
     * 
     * @param phoneNumber 原始手机号码
     * @return 掩码后的手机号码
     */
    private static String maskPhoneNumber(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 7) {
            return phoneNumber;
        }
        
        int length = phoneNumber.length();
        if (length == 11) {
            // 中国手机号格式：138****1234
            return phoneNumber.substring(0, 3) + "****" + phoneNumber.substring(7);
        } else {
            // 其他格式：保留前3位和后2位
            int keepStart = Math.min(3, length - 2);
            int keepEnd = Math.max(keepStart, length - 2);
            return phoneNumber.substring(0, keepStart) + "****" + phoneNumber.substring(keepEnd);
        }
    }
    
    /**
     * 检查用户信息是否完整
     * <p>
     * 验证用户信息是否包含必要的字段。
     * </p>
     * 
     * @param userInfo 用户信息对象
     * @return 如果用户信息完整则返回true，否则返回false
     */
    public static boolean isUserInfoComplete(UserInfo userInfo) {
        if (userInfo == null) {
            return false;
        }
        
        // 检查必要字段
        return userInfo.getCustomerId() != null && !userInfo.getCustomerId().isEmpty() &&
               userInfo.getCustomerName() != null && !userInfo.getCustomerName().isEmpty();
    }
    
    /**
     * 检查UserModel是否完整
     * <p>
     * 验证UserModel是否包含必要的字段。
     * </p>
     * 
     * @param userModel 用户模型对象
     * @return 如果用户模型完整则返回true，否则返回false
     */
    public static boolean isUserModelComplete(UserModel userModel) {
        if (userModel == null) {
            return false;
        }
        
        // 检查必要字段
        return userModel.getUsername() != null && !userModel.getUsername().isEmpty() &&
               userModel.isLoggedIn();
    }
}
