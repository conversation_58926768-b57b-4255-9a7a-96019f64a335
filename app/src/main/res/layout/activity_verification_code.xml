<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context=".ui.activity.VerificationCodeActivity">

    <!-- Layout Guidelines for responsive design -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.9" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_logo_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_header_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_form_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.95" />

    <!-- Background Image -->
    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/login_bg"
        android:contentDescription="Background"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/iv_back_button"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:src="@drawable/login_btn_back"
        android:contentDescription="@string/back_button"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Logo with responsive positioning -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="@dimen/login_logo_width"
        android:layout_height="@dimen/login_logo_height"
        android:contentDescription="@string/app_logo"
        android:scaleType="centerInside"
        android:src="@drawable/logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.6" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Verification Code"
        android:textColor="@color/login_text_white"
        android:textSize="@dimen/login_title_text_size"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        android:letterSpacing="0.02"
        android:alpha="0"
        app:layout_constraintTop_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintBottom_toTopOf="@+id/tv_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Subtitle -->
    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/chain_spacing_small"
        android:text="We have to sent code verification to Your Phone"
        android:textColor="@color/verification_subtitle_text"
        android:textSize="@dimen/verification_subtitle_text_size"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:alpha="0"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintBottom_toTopOf="@+id/guideline_header_bottom"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end" />

    <!-- OTP Input Container with responsive constraints -->
    <LinearLayout
        android:id="@+id/ll_otp_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/layout_margin_medium"
        android:layout_marginEnd="@dimen/layout_margin_medium"
        android:orientation="horizontal"
        android:gravity="center"
        android:alpha="0"
        app:layout_constraintTop_toTopOf="@+id/guideline_header_bottom"
        app:layout_constraintBottom_toTopOf="@+id/btn_submit"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="0.3"
        app:layout_constraintVertical_chainStyle="packed">

        <!-- OTP Input 1 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_1"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_1"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

        <!-- OTP Input 2 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_2"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_2"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

        <!-- OTP Input 3 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_3"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_3"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

        <!-- OTP Input 4 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_4"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_4"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

        <!-- OTP Input 5 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_5"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_5"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

        <!-- OTP Input 6 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center">

            <EditText
                android:id="@+id/et_otp_6"
                android:layout_width="match_parent"
                android:layout_height="@dimen/layout_margin_xlarge"
                android:background="@android:color/transparent"
                android:gravity="center"
                android:inputType="number"
                android:maxLength="1"
                android:textColor="@color/login_text_white"
                android:textSize="@dimen/verification_otp_text_size"
                android:textStyle="bold"
                android:cursorVisible="true"
                android:textCursorDrawable="@drawable/cursor_transparent" />

            <View
                android:id="@+id/line_6"
                android:layout_width="@dimen/layout_margin_xlarge"
                android:layout_height="0.5dp"
                android:layout_marginTop="@dimen/flow_horizontal_gap"
                android:background="@color/otp_line_normal" />

        </LinearLayout>

    </LinearLayout>

    <!-- Submit Button - Chain start -->
    <Button
        android:id="@+id/btn_submit"
        android:layout_width="0dp"
        android:layout_height="@dimen/send_code_button_height"
        android:layout_marginTop="@dimen/layout_margin_medium"
        android:background="@drawable/send_code_button_bg"
        android:text="Submit"
        android:textColor="@color/login_text_white"
        android:textSize="@dimen/login_button_text_size"
        android:textStyle="bold"
        android:elevation="@dimen/layout_margin_tiny"
        android:alpha="0"
        app:layout_constraintTop_toBottomOf="@+id/ll_otp_container"
        app:layout_constraintBottom_toTopOf="@+id/btn_send_again"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- Send Again Button - Chain member -->
    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btn_send_again"
        android:layout_width="0dp"
        android:layout_height="@dimen/send_code_button_height"
        android:layout_marginTop="@dimen/layout_margin_large"
        android:background="@drawable/send_again_button_bg"
        android:backgroundTint="@null"
        android:text="Send Again"
        android:textAllCaps="false"
        android:textColor="@color/login_button_text_black"
        android:textSize="@dimen/login_button_text_size"
        android:textStyle="bold"
        android:elevation="@dimen/layout_margin_tiny"
        android:visibility="invisible"
        app:layout_constraintTop_toBottomOf="@+id/btn_submit"
        app:layout_constraintBottom_toTopOf="@+id/guideline_form_bottom"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end" />

    <!-- User Agreement Text with proper bottom positioning -->
    <TextView
        android:id="@+id/tv_user_agreement"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/login_agreement_text_margin_horizontal"
        android:layout_marginEnd="@dimen/login_agreement_text_margin_horizontal"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0"
        android:lineSpacingMultiplier="1.43"
        android:text="@string/user_agreement"
        android:textColor="@color/login_agreement_text"
        android:textSize="@dimen/login_agreement_text_size"
        android:textStyle="normal"
        android:paddingTop="@dimen/chain_spacing_small"
        android:paddingBottom="@dimen/chain_spacing_small"
        app:layout_constraintTop_toTopOf="@+id/guideline_form_bottom"
        app:layout_constraintBottom_toTopOf="@+id/guideline_bottom_safe"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="1.0" />

</androidx.constraintlayout.widget.ConstraintLayout>
