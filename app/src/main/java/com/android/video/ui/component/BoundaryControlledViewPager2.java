package com.android.video.ui.component;

import android.content.Context;
import android.util.AttributeSet;
import android.util.Log;
import android.view.MotionEvent;
import android.widget.FrameLayout;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.viewpager2.widget.ViewPager2;
import androidx.recyclerview.widget.RecyclerView;

/**
 * 带边界控制的ViewPager2包装器
 * 防止第一个页面向上滑动和最后一个页面向下滑动
 * <AUTHOR> Team
 */
public class BoundaryControlledViewPager2 extends FrameLayout {

    private static final String TAG = "BoundaryViewPager2";

    private ViewPager2 viewPager2;
    private float initialY = 0f;
    private boolean isFirstPage = false;
    private boolean isLastPage = false;
    private boolean enableBoundaryControl = true;
    private OnBoundaryReachedListener boundaryListener;

    // 防抖机制相关变量
    private long lastTopBoundaryTriggerTime = 0;
    private long lastBottomBoundaryTriggerTime = 0;
    private static final long BOUNDARY_TRIGGER_COOLDOWN = 1000; // 1秒冷却时间

    public interface OnBoundaryReachedListener {
        void onTopBoundaryReached();
        void onBottomBoundaryReached();
    }

    public BoundaryControlledViewPager2(@NonNull Context context) {
        super(context);
        init();
    }

    public BoundaryControlledViewPager2(@NonNull Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    private void init() {
        // 创建ViewPager2实例
        viewPager2 = new ViewPager2(getContext());

        // 设置垂直滑动方向
        viewPager2.setOrientation(ViewPager2.ORIENTATION_VERTICAL);

        addView(viewPager2, new LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));

        // 监听页面变化
        viewPager2.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                updateBoundaryState(position);
            }
        });
    }

    private void updateBoundaryState(int position) {
        if (viewPager2.getAdapter() != null) {
            isFirstPage = (position == 0);
            isLastPage = (position == viewPager2.getAdapter().getItemCount() - 1);

            Log.d(TAG, "Page " + position + " selected. First: " + isFirstPage + ", Last: " + isLastPage);
        }
    }

    // 委托方法 - ViewPager2功能
    public void setAdapter(RecyclerView.Adapter adapter) {
        viewPager2.setAdapter(adapter);
    }

    public RecyclerView.Adapter getAdapter() {
        return viewPager2.getAdapter();
    }

    public void setCurrentItem(int item) {
        viewPager2.setCurrentItem(item);
    }

    public void setCurrentItem(int item, boolean smoothScroll) {
        viewPager2.setCurrentItem(item, smoothScroll);
    }

    public int getCurrentItem() {
        return viewPager2.getCurrentItem();
    }

    public void setOffscreenPageLimit(int limit) {
        viewPager2.setOffscreenPageLimit(limit);
    }

    public void registerOnPageChangeCallback(ViewPager2.OnPageChangeCallback callback) {
        viewPager2.registerOnPageChangeCallback(callback);
    }

    public void unregisterOnPageChangeCallback(ViewPager2.OnPageChangeCallback callback) {
        viewPager2.unregisterOnPageChangeCallback(callback);
    }

    @Override
    public boolean onInterceptTouchEvent(MotionEvent ev) {
        if (!enableBoundaryControl) {
            return super.onInterceptTouchEvent(ev);
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                initialY = ev.getY();
                break;
                
            case MotionEvent.ACTION_MOVE:
                float deltaY = ev.getY() - initialY;
                
                // 检查边界条件
                if (isFirstPage && deltaY > 0) {
                    // 第一页向上滑动（deltaY > 0），阻止滑动
                    Log.d(TAG, "Blocking upward swipe on first page");
                    triggerTopBoundaryEvent();
                    return false; // 不拦截，让子View处理
                }

                if (isLastPage && deltaY < 0) {
                    // 最后一页向下滑动（deltaY < 0），阻止滑动
                    Log.d(TAG, "Blocking downward swipe on last page");
                    triggerBottomBoundaryEvent();
                    return false; // 不拦截，让子View处理
                }
                break;
        }

        return super.onInterceptTouchEvent(ev);
    }

    @Override
    public boolean onTouchEvent(MotionEvent ev) {
        if (!enableBoundaryControl) {
            return super.onTouchEvent(ev);
        }

        switch (ev.getAction()) {
            case MotionEvent.ACTION_DOWN:
                initialY = ev.getY();
                break;
                
            case MotionEvent.ACTION_MOVE:
                float deltaY = ev.getY() - initialY;
                
                // 检查边界条件
                if (isFirstPage && deltaY > 0) {
                    // 第一页向上滑动，阻止滑动
                    Log.d(TAG, "Preventing upward swipe on first page");
                    return true; // 消费事件
                }
                
                if (isLastPage && deltaY < 0) {
                    // 最后一页向下滑动，阻止滑动
                    Log.d(TAG, "Preventing downward swipe on last page");
                    return true; // 消费事件
                }
                break;
        }

        return super.onTouchEvent(ev);
    }

    /**
     * 设置是否启用边界控制
     * @param enabled 是否启用
     */
    public void setBoundaryControlEnabled(boolean enabled) {
        this.enableBoundaryControl = enabled;
        Log.d(TAG, "Boundary control " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * 检查是否启用了边界控制
     * @return 是否启用
     */
    public boolean isBoundaryControlEnabled() {
        return enableBoundaryControl;
    }

    /**
     * 设置边界到达监听器
     * @param listener 监听器
     */
    public void setOnBoundaryReachedListener(OnBoundaryReachedListener listener) {
        this.boundaryListener = listener;
    }

    /**
     * 检查是否在第一页
     * @return 是否在第一页
     */
    public boolean isFirstPage() {
        return isFirstPage;
    }

    /**
     * 检查是否在最后一页
     * @return 是否在最后一页
     */
    public boolean isLastPage() {
        return isLastPage;
    }

    /**
     * 强制更新边界状态
     */
    public void updateBoundaryState() {
        updateBoundaryState(getCurrentItem());
    }

    /**
     * 设置用户输入是否启用
     * @param enabled 是否启用用户输入
     */
    public void setUserInputEnabled(boolean enabled) {
        if (viewPager2 != null) {
            viewPager2.setUserInputEnabled(enabled);
        }
    }

    /**
     * 安全地设置当前页面
     * @param item 页面索引
     * @param smoothScroll 是否平滑滚动
     */
    public void setCurrentItemSafely(int item, boolean smoothScroll) {
        if (viewPager2.getAdapter() != null && item >= 0 && item < viewPager2.getAdapter().getItemCount()) {
            setCurrentItem(item, smoothScroll);
        } else {
            Log.w(TAG, "Invalid item index: " + item);
        }
    }

    /**
     * 移动到下一页（如果可能）
     * @return 是否成功移动
     */
    public boolean moveToNext() {
        if (viewPager2.getAdapter() != null && getCurrentItem() < viewPager2.getAdapter().getItemCount() - 1) {
            setCurrentItem(getCurrentItem() + 1, true);
            return true;
        }
        return false;
    }

    /**
     * 移动到上一页（如果可能）
     * @return 是否成功移动
     */
    public boolean moveToPrevious() {
        if (getCurrentItem() > 0) {
            setCurrentItem(getCurrentItem() - 1, true);
            return true;
        }
        return false;
    }

    /**
     * 获取总页数
     * @return 总页数
     */
    public int getPageCount() {
        return viewPager2.getAdapter() != null ? viewPager2.getAdapter().getItemCount() : 0;
    }

    /**
     * 检查是否可以向下滑动
     * @return 是否可以向下滑动
     */
    public boolean canSwipeDown() {
        return !isLastPage;
    }

    /**
     * 检查是否可以向上滑动
     * @return 是否可以向上滑动
     */
    public boolean canSwipeUp() {
        return !isFirstPage;
    }

    /**
     * 获取当前页面信息
     * @return 页面信息字符串
     */
    public String getCurrentPageInfo() {
        if (viewPager2.getAdapter() != null) {
            return String.format("Page %d of %d", getCurrentItem() + 1, viewPager2.getAdapter().getItemCount());
        }
        return "No adapter";
    }

    /**
     * 重置边界状态
     */
    public void resetBoundaryState() {
        isFirstPage = false;
        isLastPage = false;
        updateBoundaryState(getCurrentItem());
    }

    /**
     * 触发顶部边界事件（带防抖）
     */
    private void triggerTopBoundaryEvent() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastTopBoundaryTriggerTime > BOUNDARY_TRIGGER_COOLDOWN) {
            if (boundaryListener != null) {
                boundaryListener.onTopBoundaryReached();
                lastTopBoundaryTriggerTime = currentTime;
                Log.d(TAG, "Top boundary event triggered");
            }
        } else {
            Log.d(TAG, "Top boundary event ignored due to cooldown");
        }
    }

    /**
     * 触发底部边界事件（带防抖）
     */
    private void triggerBottomBoundaryEvent() {
        long currentTime = System.currentTimeMillis();
        if (currentTime - lastBottomBoundaryTriggerTime > BOUNDARY_TRIGGER_COOLDOWN) {
            if (boundaryListener != null) {
                boundaryListener.onBottomBoundaryReached();
                lastBottomBoundaryTriggerTime = currentTime;
                Log.d(TAG, "Bottom boundary event triggered");
            }
        } else {
            Log.d(TAG, "Bottom boundary event ignored due to cooldown");
        }
    }
}
