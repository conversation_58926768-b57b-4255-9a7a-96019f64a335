package com.android.video.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.android.video.R;
import com.android.video.adapter.VideoPlayerPagerAdapter;
import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.model.VideoModel;
import com.android.video.model.TestVideoModel;
import com.android.video.player.VideoPlayerManager;
import com.android.video.player.VideoPlayerListener;
import com.android.video.player.VideoQualitySelector;
import com.android.video.ui.activity.MainActivity;
import com.android.video.ui.activity.VideoDetailActivity;
import com.android.video.ui.activity.VideoPlayerActivity;
import com.android.video.ui.component.BoundaryControlledViewPager2;
import com.android.video.ui.component.QualitySelectionView;
import com.android.video.ui.component.SpeedSelectionView;
import com.android.video.ui.component.EpisodeSelectionView;
import com.android.video.ui.component.SubtitlePanelView;
import com.android.video.utils.VideoPlayerPreferences;
import com.android.video.utils.VideoResourceManager;
import com.android.video.manager.FloatingVideoManager;
import com.android.video.utils.FloatingVideoPermissionUtils;
import com.android.video.network.NetworkOptimizationManager;
import com.android.video.network.NetworkUtils;
import com.android.video.ui.component.PoorNetworkWarningView;
import com.android.video.network.WaterfallRecommendApiService;
import com.android.video.model.response.WaterfallRecommendDataModel;
import com.android.video.base.BaseMultiLanguageFragment;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 发现页Fragment - 集成全屏视频播放功能
 * 实现类似抖音快手的短视频播放体验
 *
 * <AUTHOR> Team
 */
public class DiscoverFragment extends BaseMultiLanguageFragment implements VideoPlayerListener {

    private static final String TAG = "DiscoverFragment";

    // UI Components
    private BoundaryControlledViewPager2 viewPagerVideos;
    private LinearLayout topControlBar;
    private LinearLayout sideControlButtons;
    private LinearLayout bottomInfoArea;

    // Control Buttons
    private ImageView btnSmallScreen;
    private ImageView btnDownload;
    private ImageView btnLike;
    private ImageView btnShare;
    private ImageView btnComment;
    private ImageView btnDetail;
    private ImageView btnFullscreen;

    // Info Views
    private TextView tvVideoTitle;
    private TextView tvVideoDescription;
    private TextView tvLikeCount;
    private TextView tvShareCount;
    private TextView tvCommentCount;
    private SeekBar progressSeekBar;
    private boolean isUserSeeking = false;
    private boolean isDescriptionExpanded = false;

    // Control Views
    private LinearLayout btnEpisodeSelection;
    private TextView tvEpisodeText;
    private ImageView btnDanmaku;
    private TextView btnSpeedSelection;
    private TextView btnQualitySelection;

    // Data
    private List<VideoModel> videoList;
    private int currentVideoIndex;
    private VideoModel currentVideo;
    private VideoPlayerPagerAdapter pagerAdapter;

    // Player Management
    private VideoPlayerManager playerManager;
    private Handler uiHandler;
    private boolean isControlsVisible = true;
    private Runnable hideControlsRunnable;

    // Player State
    private float currentPlaybackSpeed = 1.0f;
    private VideoQualitySelector.VideoQuality currentQuality = VideoQualitySelector.VideoQuality.QUALITY_720P;
    private boolean isDanmakuEnabled = true;

    // Advanced Control Components
    private QualitySelectionView qualitySelectionView;
    private SpeedSelectionView speedSelectionView;
    private EpisodeSelectionView episodeSelectionView;
    private SubtitlePanelView subtitlePanelView;
    private VideoPlayerPreferences preferences;

    // Fragment State
    private boolean isFragmentVisible = false;
    private boolean isPlayerInitialized = false;
    private boolean isFullscreen = false;
    private boolean areControlsVisibleInFullscreen = true;

    // UI State Management
    private Runnable uiStateCheckRunnable;
    private Runnable hideFullscreenControlsRunnable;

    // Progress Update
    private Runnable progressUpdateRunnable;
    private boolean shouldUpdateProgress = false;

    // Poster Popup Views
    private FrameLayout posterPopupOverlay;
    private LinearLayout posterContainer;
    private ImageView moviePosterImage;
    private ImageView btnClosePoster;

    // VIP Unlock Popup Views
    private FrameLayout vipUnlockPopupOverlay;
    private LinearLayout vipUnlockContainer;
    private TextView tvVipMessage;
    private LinearLayout btnUnlockNow;
    private LinearLayout btnWatchAd;
    private LinearLayout btnOpenVip;
    private TextView tvCoinCount;
    private ImageView btnCloseVipPopup;

    // Watch AD Popup Views
    private FrameLayout watchAdPopupOverlay;
    private LinearLayout watchAdContainer;
    private TextView tvWatchAdCoinCount;

    // Recharge Popup Views
    private FrameLayout rechargePopupOverlay;
    private FrameLayout rechargeContainer;
    private ImageView btnCloseRechargePopup;
    private TextView tvRechargeCurrentCoins;
    private LinearLayout rechargeCard500;
    private LinearLayout rechargeCard1000;
    private LinearLayout rechargeCard2000;
    private LinearLayout rechargeCard3000;
    private LinearLayout rechargeWeeklyCard;
    private LinearLayout rechargeMonthlyCard;
    private int selectedRechargeAmount = 0;
    private boolean isWeeklySelected = false;
    private boolean isMonthlySelected = false;

    // Network Optimization
    private NetworkOptimizationManager networkOptimizationManager;
    private PoorNetworkWarningView poorNetworkWarningView;
    private int currentPreloadCount = 5; // 默认预加载数量

    // Waterfall Recommend API
    private WaterfallRecommendApiService waterfallApiService;
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    public DiscoverFragment() {
        // Required empty public constructor
    }

    public static DiscoverFragment newInstance() {
        return new DiscoverFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        uiHandler = new Handler(Looper.getMainLooper());

        // 初始化进度更新任务
        progressUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (shouldUpdateProgress && !isUserSeeking && pagerAdapter != null) {
                    VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
                    if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                        VideoPlayerManager playerManager = currentFragment.getPlayerManager();
                        long position = playerManager.getCurrentPosition();
                        long duration = playerManager.getDuration();
                        if (duration > 0) {
                            int progress = (int) ((position * 100) / duration);
                            progressSeekBar.setProgress(progress);
                        }
                    }
                }
                if (shouldUpdateProgress) {
                    uiHandler.postDelayed(this, 1000); // 每秒更新一次
                }
            }
        };

        // 初始化UI状态检查任务
        uiStateCheckRunnable = new Runnable() {
            @Override
            public void run() {
                if (isFragmentVisible) {
                    checkAndRestoreUIState();
                    uiHandler.postDelayed(this, 5000); // 每5秒检查一次
                }
            }
        };

        // 初始化全屏控制元素自动隐藏任务
        hideFullscreenControlsRunnable = new Runnable() {
            @Override
            public void run() {
                if (isFullscreen && areControlsVisibleInFullscreen) {
                    hideFullscreenControls();
                }
            }
        };

        Log.d(TAG, "DiscoverFragment created");
    }

    @Override
    public void onResume() {
        super.onResume();
        // 设置返回键监听
        isFragmentVisible = true;
        handleVisibilityChange();
        // 确保控制栏在Resume时可见
        showControlsPermanently();
        // 启动进度更新
        startProgressUpdate();
        // 启动UI状态检查
        startUIStateCheck();

        // 启动网络监控
        if (networkOptimizationManager != null) {
            networkOptimizationManager.startNetworkMonitoring();
            // 重置弱网络警告状态，允许重新显示
            networkOptimizationManager.resetPoorNetworkWarningState("discover");
            // 检查当前网络状况
            if (networkOptimizationManager.shouldShowPoorNetworkWarning("discover")) {
                showPoorNetworkWarning();
            }
        }

        Log.d(TAG, "Fragment resumed");
        if (getView() != null) {
            getView().setFocusableInTouchMode(true);
            getView().requestFocus();
            getView().setOnKeyListener((v, keyCode, event) -> {
                if (keyCode == android.view.KeyEvent.KEYCODE_BACK && event.getAction() == android.view.KeyEvent.ACTION_UP) {
                    if (isFullscreen) {
                        // 如果是全屏状态，退出全屏而不是返回上一页
                        openFullscreenPlayer(); // 这会切换全屏状态
                        return true;
                    }
                }
                return false;
            });
        }
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_discover, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initializeViews(view);
        initializeData();
        initializePlayer();
        initializeAdvancedComponents();
        initializeNetworkOptimization();
        setupViewPager();
        setupClickListeners();
        setupAutoHideControls();
        loadUserPreferences();

        // 确保Discover页面的控制栏初始可见，但不影响VideoPlayerFragment的控制按钮
        showControlsPermanently();

        // 确保VideoPlayerFragment的控制按钮初始为隐藏状态
        ensureVideoControlsHidden();

        // 启动UI状态检查
        startUIStateCheck();

        Log.d(TAG, "DiscoverFragment view created and initialized");
    }

    /**
     * 初始化视图
     */
    private void initializeViews(View view) {
        // ViewPager2
        viewPagerVideos = view.findViewById(R.id.viewPagerVideos);

        // Control Areas
        topControlBar = view.findViewById(R.id.topControlBar);
        sideControlButtons = view.findViewById(R.id.sideControlButtons);
        bottomInfoArea = view.findViewById(R.id.bottomInfoArea);

        // Top Control Buttons
        btnSmallScreen = view.findViewById(R.id.btnSmallScreen);
        btnDownload = view.findViewById(R.id.btnDownload);

        // Side Control Buttons
        btnLike = view.findViewById(R.id.btnLike);
        btnShare = view.findViewById(R.id.btnShare);
        btnComment = view.findViewById(R.id.btnComment);
        btnDetail = view.findViewById(R.id.btnDetail);
        btnFullscreen = view.findViewById(R.id.btnFullscreen);

        // Info Views
        tvVideoTitle = view.findViewById(R.id.tvVideoTitle);
        tvVideoDescription = view.findViewById(R.id.tvVideoDescription);
        tvLikeCount = view.findViewById(R.id.tvLikeCount);
        tvShareCount = view.findViewById(R.id.tvShareCount);
        tvCommentCount = view.findViewById(R.id.tvCommentCount);
        progressSeekBar = view.findViewById(R.id.progressSeekBar);
        setupProgressSeekBar();

        // Control Views
        btnEpisodeSelection = view.findViewById(R.id.btnEpisodeSelection);
        tvEpisodeText = view.findViewById(R.id.tvEpisodeText);
        btnDanmaku = view.findViewById(R.id.btnDanmaku);
        btnSpeedSelection = view.findViewById(R.id.btnSpeedSelection);
        btnQualitySelection = view.findViewById(R.id.btnQualitySelection);

        // Progress Touch Area
        View progressTouchArea = view.findViewById(R.id.progressTouchArea);
        setupProgressTouchArea(progressTouchArea);

        // Poster Popup Views
        posterPopupOverlay = view.findViewById(R.id.posterPopupOverlay);
        posterContainer = view.findViewById(R.id.posterContainer);
        moviePosterImage = view.findViewById(R.id.moviePosterImage);
        btnClosePoster = view.findViewById(R.id.btnClosePoster);

        // VIP Unlock Popup Views
        vipUnlockPopupOverlay = view.findViewById(R.id.vipUnlockPopupOverlay);
        vipUnlockContainer = view.findViewById(R.id.vipUnlockContainer);
        tvVipMessage = view.findViewById(R.id.tvVipMessage);
        btnUnlockNow = view.findViewById(R.id.btnUnlockNow);
        btnWatchAd = view.findViewById(R.id.btnWatchAd);
        btnOpenVip = view.findViewById(R.id.btnOpenVip);
        tvCoinCount = view.findViewById(R.id.tvCoinCount);
        btnCloseVipPopup = view.findViewById(R.id.btnCloseVipPopup);

        // Watch AD Popup Views
        watchAdPopupOverlay = view.findViewById(R.id.watchAdPopupOverlay);
        watchAdContainer = view.findViewById(R.id.watchAdContainer);
        tvWatchAdCoinCount = view.findViewById(R.id.tvWatchAdCoinCount);

        // Recharge Popup Views
        rechargePopupOverlay = view.findViewById(R.id.rechargePopupOverlay);
        rechargeContainer = view.findViewById(R.id.rechargeContainer);
        btnCloseRechargePopup = view.findViewById(R.id.btnCloseRechargePopup);
        tvRechargeCurrentCoins = view.findViewById(R.id.tvRechargeCurrentCoins);
        rechargeCard500 = view.findViewById(R.id.rechargeCard500);
        rechargeCard1000 = view.findViewById(R.id.rechargeCard1000);
        rechargeCard2000 = view.findViewById(R.id.rechargeCard2000);
        rechargeCard3000 = view.findViewById(R.id.rechargeCard3000);
        rechargeWeeklyCard = view.findViewById(R.id.rechargeWeeklyCard);
        rechargeMonthlyCard = view.findViewById(R.id.rechargeMonthlyCard);

        // Set default values
        btnSpeedSelection.setText("1.0x");
        btnQualitySelection.setText("720P");
    }

    /**
     * 初始化数据
     */
    private void initializeData() {
        videoList = new ArrayList<>();

        // 初始化瀑布流推荐API服务
        waterfallApiService = new WaterfallRecommendApiService();

        // 重置分页参数
        currentPage = 1;
        isLoading = false;
        hasMoreData = true;

        // 加载第一页瀑布流推荐数据
        loadWaterfallRecommendData(currentPage, pageSize, false);

        currentVideoIndex = 0;

        Log.d(TAG, "Data initialized, loading waterfall recommend data...");
    }

    /**
     * 加载瀑布流推荐数据
     *
     * @param page 页码
     * @param size 每页数量
     * @param isLoadMore 是否为加载更多
     */
    private void loadWaterfallRecommendData(int page, int size, boolean isLoadMore) {
        if (isLoading) {
            Log.d(TAG, "Already loading, skip request");
            return;
        }

        if (!hasMoreData && isLoadMore) {
            Log.d(TAG, "No more data available");
            return;
        }

        isLoading = true;
        Log.d(TAG, "Loading waterfall recommend data: page=" + page + ", size=" + size + ", isLoadMore=" + isLoadMore);

        waterfallApiService.getWaterfallRecommend(page, size, new WaterfallRecommendApiService.WaterfallRecommendCallback() {
            @Override
            public void onSuccess(WaterfallRecommendDataModel waterfallData) {
                if (getActivity() == null) return;

                getActivity().runOnUiThread(() -> {
                    isLoading = false;

                    Log.d(TAG, "Waterfall recommend data loaded successfully: " + waterfallData.getRecordCount() + " items");

                    // 转换为VideoModel列表
                    List<VideoModel> newVideoList = waterfallData.toVideoModelList();

                    if (isLoadMore) {
                        // 加载更多：追加到现有列表
                        videoList.addAll(newVideoList);
                        Log.d(TAG, "Added " + newVideoList.size() + " more videos, total: " + videoList.size());
                    } else {
                        // 首次加载：替换整个列表
                        videoList.clear();
                        videoList.addAll(newVideoList);

                        // 设置当前视频
                        if (!videoList.isEmpty()) {
                            currentVideo = videoList.get(0);
                        }

                        Log.d(TAG, "Loaded " + newVideoList.size() + " initial videos");
                    }

                    // 更新分页信息
                    hasMoreData = waterfallData.hasMorePages();
                    if (hasMoreData) {
                        currentPage = waterfallData.getNextPage();
                    }

                    // 更新ViewPager适配器
                    if (pagerAdapter != null) {
                        pagerAdapter.updateVideoList(videoList);
                        Log.d(TAG, "Updated ViewPager with " + videoList.size() + " videos");
                    }
                });
            }

            @Override
            public void onError(String errorMessage) {
                if (getActivity() == null) return;

                getActivity().runOnUiThread(() -> {
                    isLoading = false;

                    Log.e(TAG, "Failed to load waterfall recommend data: " + errorMessage);

                    // 如果是第一页加载失败，使用测试数据作为后备
                    if (page == 1 && !isLoadMore) {
                        Log.d(TAG, "First page load failed, using fallback test data");
                        createFallbackVideoData();

                        // 设置当前视频
                        if (!videoList.isEmpty()) {
                            currentVideo = videoList.get(0);
                        }

                        // 更新ViewPager适配器
                        if (pagerAdapter != null) {
                            pagerAdapter.updateVideoList(videoList);
                        }

                        hasMoreData = false; // 测试数据不支持分页
                    }

                    // 显示错误提示
                    if (getContext() != null) {
                        String toastMessage = "数据加载失败" + (page == 1 && !isLoadMore ? "，使用测试数据" : "");
                        Toast.makeText(getContext(), toastMessage, Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }

    /**
     * 创建初始视频数据
     */
    private void createInitialVideoData() {
        // 创建多个初始测试视频，确保有足够的内容用于上下滑动
        for (int i = 1; i <= 5; i++) {
            VideoModel testVideo = new VideoModel();
            testVideo.setId("initial_test_video_" + i);
            testVideo.setTitle("测试视频 " + i);
            testVideo.setDescription("这是第" + i + "个测试视频，用于演示上下滑动功能。");
            testVideo.setLiked(false);
            testVideo.setViewCount(1000 + i * 500);
            testVideo.setCategory(getRandomCategory());
            testVideo.setRating(4.0f + (i % 5) * 0.2f);
            testVideo.setDuration("0" + (i % 3 + 1) + ":30");
            testVideo.setTotalEpisodes(i % 2 == 0 ? 1 : 24);
            testVideo.setCurrentEpisode(1);

            // 添加剧集数据
            addEpisodesToVideo(testVideo, testVideo.getTotalEpisodes());

            videoList.add(testVideo);
        }
    }

    /**
     * 获取随机分类
     */
    private String getRandomCategory() {
        String[] categories = {"电影", "电视剧", "动漫", "纪录片", "综艺"};
        return categories[new Random().nextInt(categories.length)];
    }

    /**
     * 加载完整的视频列表
     */
    private void loadCompleteVideoList() {
        VideoResourceManager resourceManager = VideoResourceManager.getInstance();
        List<TestVideoModel> testVideos = resourceManager.getValidTestVideos();

        Log.d(TAG, "Loading complete video list, found " + testVideos.size() + " test videos");

        if (!testVideos.isEmpty()) {
            // 清空初始视频列表
            videoList.clear();

            // 添加所有测试视频
            for (TestVideoModel testVideo : testVideos) {
                VideoModel videoModel = convertTestVideoToVideoModel(testVideo);
                videoList.add(videoModel);
            }

            // 如果测试视频不够多，添加更多虚拟视频
            if (videoList.size() < 8) {
                addMoreVirtualVideos();
            }
        } else {
            // 如果仍然没有测试视频，添加更多虚拟视频
            addMoreVirtualVideos();
        }

        // 更新ViewPager适配器
        if (pagerAdapter != null) {
            pagerAdapter.updateVideoList(videoList);
            Log.d(TAG, "Updated ViewPager with " + videoList.size() + " videos");
        }

        Log.d(TAG, "Complete video list loaded with " + videoList.size() + " videos");
    }

    /**
     * 添加更多虚拟视频
     */
    private void addMoreVirtualVideos() {
        String[] titles = {
            "星际穿越", "银翼杀手2049", "疾速追杀", "碟中谍", "功夫",
            "大话西游", "寂静之地", "地球脉动", "复仇者联盟", "黑客帝国",
            "盗梦空间", "阿凡达", "泰坦尼克号", "教父", "肖申克的救赎"
        };

        String[] categories = {"科幻电影", "动作电影", "喜剧电影", "恐怖电影", "纪录片", "剧情电影"};

        for (int i = videoList.size(); i < 15; i++) {
            VideoModel video = new VideoModel();
            video.setId("virtual_video_" + i);
            video.setTitle(titles[i % titles.length]);
            video.setDescription("这是一部精彩的" + categories[i % categories.length] + "，为您带来震撼的观影体验。");
            video.setLiked(false);
            video.setViewCount(1000 + i * 300);
            video.setCategory(categories[i % categories.length]);
            video.setRating(3.5f + (i % 15) * 0.1f);
            video.setDuration("0" + (i % 3 + 1) + ":" + (30 + i % 30));

            // 随机设置单集或多集
            if (video.getCategory().contains("电影")) {
                video.setTotalEpisodes(1);
                video.setCurrentEpisode(1);
                addEpisodesToVideo(video, 1);
            } else {
                video.setTotalEpisodes(24 + i % 48);
                video.setCurrentEpisode(1);
                addEpisodesToVideo(video, video.getTotalEpisodes());
            }

            videoList.add(video);
        }
    }

    /**
     * 将TestVideoModel转换为VideoModel
     */
    private VideoModel convertTestVideoToVideoModel(TestVideoModel testVideo) {
        VideoModel videoModel = new VideoModel();
        videoModel.setId(testVideo.getId());
        videoModel.setTitle(testVideo.getTitle());
        videoModel.setDescription(testVideo.getDescription());
        videoModel.setDuration(testVideo.getDurationText());
        videoModel.setRating(testVideo.getRating());
        videoModel.setViewCount(testVideo.getViewCount());
        videoModel.setLiked(testVideo.isLiked());
        videoModel.setCategory(testVideo.getCategory());
        videoModel.setTags(new ArrayList<>(testVideo.getTags()));

        // 添加测试集数数据
        if (testVideo.getCategory().contains("电影")) {
            // 电影类型设置为单集
            videoModel.setTotalEpisodes(1);
            videoModel.setCurrentEpisode(1);
            // 添加单集剧集数据
            addEpisodesToVideo(videoModel, 1);
        } else {
            // 其他类型设置为多集
            videoModel.setTotalEpisodes(72);
            videoModel.setCurrentEpisode(1);
            // 添加多集剧集数据
            addEpisodesToVideo(videoModel, 72);
        }

        return videoModel;
    }

    /**
     * 为视频添加剧集信息
     */
    private void addEpisodesToVideo(VideoModel video, int totalEpisodes) {
        for (int i = 1; i <= totalEpisodes; i++) {
            com.android.video.model.EpisodeModel episode = new com.android.video.model.EpisodeModel("episode_" + video.getId() + "_" + i,
                                                   i, "第" + i + "集");
            // 所有集数都标记为有直播图标
            episode.setLive(true);
            if (i == 1) {
                episode.setSelected(true); // 设置第一集为选中状态
            }
            video.addEpisode(episode);
        }
    }

    /**
     * 创建备用视频数据
     */
    private void createFallbackVideoData() {
        VideoModel testVideo = new VideoModel();
        testVideo.setId("discover_fallback_video_1");
        testVideo.setTitle("发现页测试视频");
        testVideo.setDescription("这是发现页的默认测试视频，用于演示全屏播放功能。");
        testVideo.setLiked(false);
        testVideo.setViewCount(15000);
        testVideo.setCategory("发现");
        testVideo.setRating(4.3f);
        testVideo.setDuration("01:30");
        testVideo.setTotalEpisodes(24);
        testVideo.setCurrentEpisode(1);

        // 添加剧集数据
        addEpisodesToVideo(testVideo, 24);

        videoList.add(testVideo);
        Log.d(TAG, "Created fallback video data for Discover");
    }

    /**
     * 初始化播放器
     */
    private void initializePlayer() {
        // DiscoverFragment不再需要自己的播放器，每个VideoPlayerFragment都有独立的播放器
        isPlayerInitialized = true;
        Log.d(TAG, "Player initialization skipped - using Fragment-level players");
    }

    /**
     * 初始化高级控制组件
     */
    private void initializeAdvancedComponents() {
        if (getContext() == null) return;

        preferences = new VideoPlayerPreferences(getContext());

        // 初始化清晰度选择组件（但不绑定到特定播放器）
        qualitySelectionView = new QualitySelectionView(getContext());
        qualitySelectionView.setSelectedQuality(currentQuality);
        qualitySelectionView.setOnQualitySelectedListener(quality -> {
            currentQuality = quality;
            // 通知当前Fragment更改清晰度
            notifyCurrentFragmentQualityChange(quality);
            // 更新按钮文本
            updateQualityButtonText();
        });

        // 初始化速度选择组件
        speedSelectionView = new SpeedSelectionView(getContext());
        speedSelectionView.setSelectedSpeed(currentPlaybackSpeed);
        speedSelectionView.setOnSpeedSelectedListener(speed -> {
            currentPlaybackSpeed = speed;
            // 通知当前Fragment更改播放速度
            notifyCurrentFragmentSpeedChange(speed);
            // 更新按钮文本
            updateSpeedButtonText();
            preferences.savePlaybackSpeed(speed);
            // 更新按钮文本
            updateSpeedButtonText();
        });

        // 初始化选集选择组件
        episodeSelectionView = new EpisodeSelectionView(getContext());
        episodeSelectionView.setOnEpisodeSelectedListener(episode -> {
            if (currentVideo != null) {
                Log.d(TAG, "Episode selected: " + episode.getEpisodeNumber());
                currentVideo.setCurrentEpisode(episode.getEpisodeNumber());
                loadVideoData(currentVideo);

                // 通知当前Fragment重新加载视频
                reloadCurrentVideo();
            }
        });
        episodeSelectionView.setOnVipUnlockRequestListener(() -> {
            showVipUnlockPopup();
        });

        // 初始化字幕面板
        subtitlePanelView = new SubtitlePanelView(getContext());
        subtitlePanelView.setOnSubtitleSettingsChangeListener(new SubtitlePanelView.OnSubtitleSettingsChangeListener() {
            @Override
            public void onSubtitleEnabledChanged(boolean enabled) {
                isDanmakuEnabled = enabled;
                updateDanmakuButtonState();
                if (preferences != null) {
                    preferences.saveDanmakuEnabled(enabled);
                }

                // 控制当前Fragment的弹幕显示
                VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                if (currentFragment != null) {
                    currentFragment.toggleDanmaku(enabled);
                }

                // 更新按钮状态
                updateDanmakuButtonState();
            }

            @Override
            public void onLanguageChanged(String language) {
                // 字幕语言切换，移除提示信息
            }

            @Override
            public void onPanelClosed() {
                // 面板关闭时的处理
            }
        });

        // 设置字幕面板的初始状态（与用户设置同步）
        subtitlePanelView.setSubtitleEnabled(isDanmakuEnabled);

        // 更新弹幕按钮状态
        updateDanmakuButtonState();

        // 将组件添加到主布局 - 修复：将弹窗组件添加到Fragment布局，字幕面板添加到Activity布局
        ViewGroup fragmentLayout = (ViewGroup) getView();
        ViewGroup activityLayout = getActivity() != null ? (ViewGroup) getActivity().findViewById(android.R.id.content) : null;



        if (fragmentLayout != null && activityLayout != null) {
            // 为弹窗组件设置正确的布局参数，避免影响其他UI元素
            android.widget.FrameLayout.LayoutParams popupParams =
                    new android.widget.FrameLayout.LayoutParams(
                            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT,
                            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
            // 确保弹窗不会覆盖底部按钮区域，设置在左上角
            popupParams.gravity = android.view.Gravity.TOP | android.view.Gravity.START;
            // 设置边距，确保不会影响其他UI元素
            popupParams.setMargins(0, 0, 0, 0);

            qualitySelectionView.setLayoutParams(popupParams);
            speedSelectionView.setLayoutParams(popupParams);

            // 选集弹窗使用底部定位，与弹幕弹窗一致
            android.widget.FrameLayout.LayoutParams episodeParams =
                    new android.widget.FrameLayout.LayoutParams(
                            android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
            episodeParams.gravity = android.view.Gravity.BOTTOM;
            episodeSelectionView.setLayoutParams(episodeParams);

            // 字幕面板设置底部布局参数
            android.widget.FrameLayout.LayoutParams subtitleParams =
                    new android.widget.FrameLayout.LayoutParams(
                            android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                            android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
            subtitleParams.gravity = android.view.Gravity.BOTTOM;
            subtitlePanelView.setLayoutParams(subtitleParams);

            // 确保弹窗组件初始状态不可点击，避免拦截底部按钮的触摸事件
            qualitySelectionView.setClickable(false);
            qualitySelectionView.setFocusable(false);
            speedSelectionView.setClickable(false);
            speedSelectionView.setFocusable(false);
            episodeSelectionView.setClickable(false);
            episodeSelectionView.setFocusable(false);

                // 确保弹窗组件不会拦截触摸事件
                qualitySelectionView.setVisibility(View.GONE);
                speedSelectionView.setVisibility(View.GONE);
                episodeSelectionView.setVisibility(View.GONE);

                // 设置弹窗组件的触摸监听器，确保隐藏时不拦截事件
                qualitySelectionView.setOnTouchListener((v, event) -> {
                    // 如果弹窗不可见，完全不拦截事件
                    if (qualitySelectionView.getVisibility() != View.VISIBLE) {
                        return false;
                    }
                    // 如果弹窗可见，只处理弹窗内部的触摸事件
                    return false;
                });
                speedSelectionView.setOnTouchListener((v, event) -> {
                    if (speedSelectionView.getVisibility() != View.VISIBLE) {
                        return false;
                    }
                    return false;
                });
                episodeSelectionView.setOnTouchListener((v, event) -> {
                    if (episodeSelectionView.getVisibility() != View.VISIBLE) {
                        return false;
                    }
                    return false;
                });

                // 将弹窗组件添加到Activity布局
                activityLayout.addView(qualitySelectionView);
                activityLayout.addView(speedSelectionView);
                activityLayout.addView(episodeSelectionView);

                // 将字幕面板也添加到Activity布局，与VideoPlayerActivity保持一致
                activityLayout.addView(subtitlePanelView);

                // 确保字幕面板初始状态正确（只设置visibility，不设置alpha，让SubtitlePanelView自己管理alpha）
                subtitlePanelView.setVisibility(View.GONE);

                // 设置组件位置
                positionSelectionViews();
            }


        Log.d(TAG, "Advanced components initialized");
    }

    /**
     * 初始化网络优化功能
     */
    private void initializeNetworkOptimization() {
        if (getContext() == null) return;

        // 初始化网络优化管理器
        networkOptimizationManager = NetworkOptimizationManager.getInstance(getContext());

        // 设置网络优化监听器
        networkOptimizationManager.setNetworkOptimizationListener(new NetworkOptimizationManager.NetworkOptimizationListener() {
            @Override
            public void onNetworkQualityChanged(NetworkUtils.NetworkQuality quality) {
                Log.d(TAG, "Network quality changed to: " + quality);
                handleNetworkQualityChange(quality);
            }

            @Override
            public void onPoorNetworkDetected() {
                Log.d(TAG, "Poor network detected");
                showPoorNetworkWarning();
            }

            @Override
            public void onPreloadCountChanged(int count) {
                Log.d(TAG, "Preload count changed to: " + count);
                currentPreloadCount = count;
                updateVideoPreloadStrategy();
            }
        });

        // 初始化弱网络警告视图
        poorNetworkWarningView = new PoorNetworkWarningView(getContext());

        // 将警告视图添加到Fragment的根布局
        ViewGroup rootLayout = (ViewGroup) getView();
        if (rootLayout != null) {
            poorNetworkWarningView.attachToParent(rootLayout);
        }

        // 获取初始预加载数量
        currentPreloadCount = networkOptimizationManager.getRecommendedPreloadCount();

        // 开始网络监控
        networkOptimizationManager.startNetworkMonitoring();

        // 检查是否需要显示弱网络警告
        if (networkOptimizationManager.shouldShowPoorNetworkWarning("discover")) {
            showPoorNetworkWarning();
        }

        Log.d(TAG, "Network optimization initialized with preload count: " + currentPreloadCount);
    }

    /**
     * 处理网络质量变化
     */
    private void handleNetworkQualityChange(NetworkUtils.NetworkQuality quality) {
        if (getActivity() == null) return;

        getActivity().runOnUiThread(() -> {
            switch (quality) {
                case EXCELLENT:
                    Log.d(TAG, "Network quality excellent - enabling full features");
                    break;
                case GOOD:
                    Log.d(TAG, "Network quality good - normal operation");
                    break;
                case FAIR:
                    Log.d(TAG, "Network quality fair - reducing preload");
                    break;
                case POOR:
                case NONE:
                    Log.d(TAG, "Network quality poor/none - minimal preload");
                    showPoorNetworkWarning();
                    break;
            }
        });
    }

    /**
     * 显示弱网络警告
     */
    private void showPoorNetworkWarning() {
        if (poorNetworkWarningView != null && getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                poorNetworkWarningView.showWarning();
                Log.d(TAG, "Poor network warning displayed");
            });
        }
    }

    /**
     * 更新视频预加载策略
     */
    private void updateVideoPreloadStrategy() {
        if (pagerAdapter != null) {
            // 根据网络状况调整预加载策略
            Log.d(TAG, "Updating video preload strategy with count: " + currentPreloadCount);

            // 这里可以通知适配器调整预加载行为
            // 由于当前的ViewPager2适配器设计，我们主要通过调整offscreenPageLimit来控制
            if (currentPreloadCount == 0) {
                // 弱网络环境，不预加载
                viewPagerVideos.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);
            } else {
                // 根据网络状况设置预加载页面数
                int offscreenLimit = Math.min(currentPreloadCount, 3); // 最多预加载3页
                viewPagerVideos.setOffscreenPageLimit(offscreenLimit);
            }
        }
    }

    /**
     * 设置选择组件的位置
     */
    private void positionSelectionViews() {
        // 清晰度选择组件位置
        ViewGroup.MarginLayoutParams qualityParams =
            (ViewGroup.MarginLayoutParams) qualitySelectionView.getLayoutParams();
        qualityParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.video_player_side_button_margin_end);
        qualityParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.video_player_selection_bottom_margin);
        qualitySelectionView.setLayoutParams(qualityParams);

        // 速度选择组件位置
        ViewGroup.MarginLayoutParams speedParams =
            (ViewGroup.MarginLayoutParams) speedSelectionView.getLayoutParams();
        speedParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.video_player_side_button_margin_end) +
                                   getResources().getDimensionPixelSize(R.dimen.video_player_speed_selection_offset);
        speedParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.video_player_selection_bottom_margin);
        speedSelectionView.setLayoutParams(speedParams);

        // 选集选择组件位置（底部）- 与弹幕弹窗保持一致
        ViewGroup.MarginLayoutParams episodeMarginParams =
            (ViewGroup.MarginLayoutParams) episodeSelectionView.getLayoutParams();
        if (episodeMarginParams != null) {
            episodeMarginParams.bottomMargin = 0;
            episodeMarginParams.leftMargin = 0;
            episodeMarginParams.rightMargin = 0;
            episodeSelectionView.setLayoutParams(episodeMarginParams);
        }

        // 字幕面板使用gravity定位，不需要设置margin
    }

    /**
     * 设置ViewPager
     */
    private void setupViewPager() {
        pagerAdapter = new VideoPlayerPagerAdapter(requireActivity(), videoList);
        viewPagerVideos.setAdapter(pagerAdapter);
        viewPagerVideos.setCurrentItem(currentVideoIndex, false);

        // 设置预加载页面数量为0，避免多个Fragment同时播放
        viewPagerVideos.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);

        // 设置页面变化监听
        viewPagerVideos.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
                handlePageScrollStateChanged(state);
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                handlePageSelected(position);
            }
        });

        // 设置滑动边界控制
        setupSwipeBoundaryControl();
    }

    /**
     * 处理页面滚动状态变化
     */
    private void handlePageScrollStateChanged(int state) {
        switch (state) {
            case ViewPager2.SCROLL_STATE_IDLE:
                Log.d(TAG, "ViewPager scroll idle");
                break;
            case ViewPager2.SCROLL_STATE_DRAGGING:
                Log.d(TAG, "ViewPager scroll dragging");
                break;
            case ViewPager2.SCROLL_STATE_SETTLING:
                Log.d(TAG, "ViewPager scroll settling");
                break;
        }
    }

    /**
     * 处理页面选中
     */
    private void handlePageSelected(int position) {
        Log.d(TAG, "Page selected: " + position);

        if (position < 0 || position >= videoList.size()) {
            Log.w(TAG, "Invalid position selected: " + position);
            return;
        }

        // 暂停之前的视频
        if (currentVideoIndex != position && pagerAdapter != null) {
            VideoPlayerFragment previousFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (previousFragment != null && previousFragment.getPlayerManager() != null) {
                previousFragment.getPlayerManager().pause();
                Log.d(TAG, "Paused previous video at position " + currentVideoIndex);
            }
        }

        // 更新当前视频信息
        currentVideoIndex = position;
        currentVideo = videoList.get(position);
        pagerAdapter.setCurrentPosition(position);

        // 更新UI显示
        loadVideoData(currentVideo);

        // 开始播放当前视频
        VideoPlayerFragment currentFragment = pagerAdapter.getFragment(position);
        if (currentFragment != null && currentFragment.getPlayerManager() != null &&
                currentFragment.isPlayerPrepared()) {
            currentFragment.getPlayerManager().play();
            Log.d(TAG, "Started current video at position " + position);
        }

        // 检查是否需要加载更多数据
        checkAndLoadMoreData(position);
    }

    /**
     * 检查并加载更多数据
     *
     * @param currentPosition 当前位置
     */
    private void checkAndLoadMoreData(int currentPosition) {
        // 当滑动到倒数第3个视频时，自动加载下一页
        int loadMoreThreshold = Math.max(3, pageSize / 2);
        int remainingItems = videoList.size() - currentPosition - 1;

        if (remainingItems <= loadMoreThreshold && hasMoreData && !isLoading) {
            Log.d(TAG, "Approaching end of list (remaining: " + remainingItems + "), loading more data...");
            loadWaterfallRecommendData(currentPage, pageSize, true);
        }
    }



    /**
     * 重新加载当前视频（用于选集切换）
     */
    private void reloadCurrentVideo() {
        if (pagerAdapter != null && currentVideoIndex >= 0 && currentVideoIndex < videoList.size()) {
            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (currentFragment != null) {
                Log.d(TAG, "Reloading video for episode change at position: " + currentVideoIndex);

                // 重置Fragment的加载状态，强制重新加载
                currentFragment.resetLoadingState();

                // 重新加载视频数据
                currentFragment.loadVideoData();
            } else {
                Log.w(TAG, "Current fragment is null, cannot reload video");
            }
        }
    }

    /**
     * 设置滑动边界控制
     */
    private void setupSwipeBoundaryControl() {
        // 启用边界控制
        viewPagerVideos.setBoundaryControlEnabled(true);

        // 设置边界到达监听器
        viewPagerVideos.setOnBoundaryReachedListener(new BoundaryControlledViewPager2.OnBoundaryReachedListener() {
            @Override
            public void onTopBoundaryReached() {
                Log.d(TAG, "Top boundary reached - cannot swipe up from first video");
            }

            @Override
            public void onBottomBoundaryReached() {
                Log.d(TAG, "Bottom boundary reached - cannot swipe down from last video");
            }
        });

        // 更新边界状态
        viewPagerVideos.updateBoundaryState();

        Log.d(TAG, "Boundary control setup completed");
    }

    /**
     * 设置进度条拖拽功能
     */
    private void setupProgressSeekBar() {
        progressSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && pagerAdapter != null) {
                    VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
                    if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                        VideoPlayerManager playerManager = currentFragment.getPlayerManager();
                        long duration = playerManager.getDuration();
                        if (duration > 0) {
                            long newPosition = (progress * duration) / 100;
                            playerManager.seekTo(newPosition);
                        }
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                isUserSeeking = true;
                // 切换到拖拽状态的样式
                seekBar.setThumb(getResources().getDrawable(R.drawable.seek_thumb_dragging, null));
                Log.d(TAG, "User started seeking");
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                isUserSeeking = false;
                // 恢复正常状态的样式
                seekBar.setThumb(getResources().getDrawable(R.drawable.seek_thumb_normal, null));
                Log.d(TAG, "User stopped seeking");
            }
        });

        // 增加进度条的触摸区域，提高拖拽灵敏度
        progressSeekBar.setOnTouchListener((v, event) -> {
            // 扩大触摸区域
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                // 确保进度条能够接收到触摸事件
                v.getParent().requestDisallowInterceptTouchEvent(true);
            }
            return false; // 让SeekBar正常处理触摸事件
        });
    }

    /**
     * 设置进度条触摸区域
     */
    private void setupProgressTouchArea(View progressTouchArea) {
        if (progressTouchArea == null) return;

        // 获取系统的最小拖动距离
        final int touchSlop = android.view.ViewConfiguration.get(getContext()).getScaledTouchSlop();
        final float[] startX = new float[1];
        final float[] startY = new float[1];
        final boolean[] isDragging = new boolean[1];

        progressTouchArea.setOnTouchListener((v, event) -> {
            // 将触摸事件转发给进度条
            if (progressSeekBar != null) {
                // 计算相对于进度条的触摸位置
                float touchX = event.getX();
                float touchY = event.getY();
                float touchAreaWidth = v.getWidth();
                float progress = touchX / touchAreaWidth;

                switch (event.getAction()) {
                    case android.view.MotionEvent.ACTION_DOWN:
                        startX[0] = touchX;
                        startY[0] = touchY;
                        isDragging[0] = false;
                        return true;

                    case android.view.MotionEvent.ACTION_MOVE:
                        float deltaX = Math.abs(touchX - startX[0]);
                        float deltaY = Math.abs(touchY - startY[0]);

                        // 只有当移动距离超过最小拖动距离时才开始拖动
                        if (!isDragging[0] && (deltaX > touchSlop || deltaY > touchSlop)) {
                            // 检查是否是水平拖动（进度条操作）
                            if (deltaX > deltaY) {
                                isDragging[0] = true;
                                isUserSeeking = true;
                            } else {
                                // 垂直拖动，不处理
                                return false;
                            }
                        }

                        if (isDragging[0] && isUserSeeking) {
                            int moveProgress = (int) (progress * progressSeekBar.getMax());
                            progressSeekBar.setProgress(moveProgress);
                        }
                        return isDragging[0];

                    case android.view.MotionEvent.ACTION_UP:
                    case android.view.MotionEvent.ACTION_CANCEL:
                        if (isDragging[0] && isUserSeeking) {
                            isUserSeeking = false;
                            int finalProgress = (int) (progress * progressSeekBar.getMax());
                            progressSeekBar.setProgress(finalProgress);

                            // 通知当前Fragment进行实际的seek操作
                            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
                            if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                                long seekPosition = (long) (progress * currentFragment.getPlayerManager().getDuration());
                                currentFragment.getPlayerManager().seekTo(seekPosition);
                            }
                        }
                        isDragging[0] = false;
                        return true;
                }
            }
            return false;
        });

        Log.d(TAG, "Progress touch area setup completed");
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 顶部控制按钮
        btnSmallScreen.setOnClickListener(v -> {
            startFloatingVideoPlay();
        });
        btnDownload.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }
            // 下载功能
        });

        // 收藏按钮
        btnLike.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }
            toggleLike();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 分享按钮
        btnShare.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }
            shareVideo();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 评论按钮
        btnComment.setOnClickListener(v -> {
            openComments();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 详情按钮
        btnDetail.setOnClickListener(v -> {
            openVideoDetail();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 选集按钮
        btnEpisodeSelection.setOnClickListener(v -> {
            Log.d(TAG, "Episode selection button clicked");
            showEpisodeSelection();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 弹幕按钮
        btnDanmaku.setOnClickListener(v -> {
            Log.d(TAG, "Danmaku button clicked");
            if (subtitlePanelView != null) {
                Log.d(TAG, "Calling subtitlePanelView.toggle()");
                subtitlePanelView.toggle();
                Log.d(TAG, "After toggle - visibility: " + subtitlePanelView.getVisibility() + ", isVisible: " + subtitlePanelView.isVisible());
            } else {
                Log.e(TAG, "subtitlePanelView is null");
            }
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 倍速按钮
        btnSpeedSelection.setOnClickListener(v -> {
            speedSelectionView.showAbove(btnSpeedSelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 清晰度按钮
        btnQualitySelection.setOnClickListener(v -> {
            qualitySelectionView.showAbove(btnQualitySelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 描述文本点击展开/收起
        tvVideoDescription.setOnClickListener(v -> toggleDescriptionExpansion());

        // 视频标题点击跳转到视频详情页
        tvVideoTitle.setOnClickListener(v -> openVideoDetail());

        // 原本的海报弹窗逻辑暂时注释
        // tvVideoTitle.setOnClickListener(v -> showPosterPopup());

        // 关闭海报弹窗按钮
        btnClosePoster.setOnClickListener(v -> hidePosterPopup());

        // 点击遮罩层关闭弹窗
        posterPopupOverlay.setOnClickListener(v -> {
            // 检查点击是否在海报容器外
            if (v == posterPopupOverlay) {
                hidePosterPopup();
            }
        });

        // VIP解锁弹窗相关监听器
        btnUnlockNow.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showRechargePopup();
        });

        btnWatchAd.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showWatchAdPopup();
        });

        btnOpenVip.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showRechargePopup();
        });

        btnCloseVipPopup.setOnClickListener(v -> hideVipUnlockPopup());

        // 点击VIP弹窗遮罩层关闭弹窗
        vipUnlockPopupOverlay.setOnClickListener(v -> {
            if (v == vipUnlockPopupOverlay) {
                hideVipUnlockPopup();
            }
        });

        // 观看广告弹窗点击事件
        watchAdContainer.setOnClickListener(v -> {
            hideWatchAdPopup();
        });

        // 点击观看广告弹窗遮罩层关闭弹窗
        watchAdPopupOverlay.setOnClickListener(v -> {
            if (v == watchAdPopupOverlay) {
                hideWatchAdPopup();
            }
        });

        // 充值弹窗相关监听器
        btnCloseRechargePopup.setOnClickListener(v -> hideRechargePopup());

        // 点击充值弹窗遮罩层关闭弹窗
        rechargePopupOverlay.setOnClickListener(v -> {
            if (v == rechargePopupOverlay) {
                hideRechargePopup();
            }
        });

        // 充值金额卡片点击事件
        rechargeCard500.setOnClickListener(v -> selectRechargeAmount(500));
        rechargeCard1000.setOnClickListener(v -> selectRechargeAmount(1000));
        rechargeCard2000.setOnClickListener(v -> selectRechargeAmount(2000));
        rechargeCard3000.setOnClickListener(v -> selectRechargeAmount(3000));

        // VIP卡片点击事件
        rechargeWeeklyCard.setOnClickListener(v -> selectVipPlan("weekly"));
        rechargeMonthlyCard.setOnClickListener(v -> selectVipPlan("monthly"));

        // 全屏按钮
        btnFullscreen.setOnClickListener(v -> openFullscreenPlayer());

        // 复用VideoPlayerActivity的成功触摸事件处理逻辑
        setupFullscreenTouchHandling();
    }

    /**
     * 设置自动隐藏控制栏
     */
    private void setupAutoHideControls() {
        // Discover页面控制栏应该始终可见，不设置自动隐藏
        hideControlsRunnable = () -> {
            // 空实现，不自动隐藏控制栏
            Log.d(TAG, "Auto-hide controls disabled for Discover page");
        };
    }

    /**
     * 加载用户偏好设置
     */
    private void loadUserPreferences() {
        if (preferences != null) {
            currentPlaybackSpeed = preferences.getPlaybackSpeed();
            isDanmakuEnabled = preferences.isDanmakuEnabled();

            // 更新UI显示
            updateSpeedButtonText();
            updateDanmakuButtonState();
        }
    }

    /**
     * 加载视频数据
     */
    private void loadVideoData(VideoModel video) {
        if (video == null) return;

        // 更新UI显示
        tvVideoTitle.setText(video.getDisplayTitle());

        // 生成视频概要
        String description = generateVideoDescription(video);
        tvVideoDescription.setText(description);

        tvLikeCount.setText(video.getFormattedViewCount());
        tvShareCount.setText("Share");
        tvCommentCount.setText("Comment");

        // 更新收藏状态
        updateLikeButtonState(video.isLiked());

        // 更新选集按钮
        updateEpisodeButton(video);

        Log.d(TAG, "Video data loaded: " + video.getTitle());
    }

    /**
     * 生成视频概要
     */
    private String generateVideoDescription(VideoModel video) {
        if (video.getDescription() != null && !video.getDescription().isEmpty()) {
            return video.getDescription();
        }

        // 自动生成概要
        StringBuilder description = new StringBuilder();
        description.append("这是一部精彩的");

        if (video.getCategory() != null) {
            description.append(video.getCategory());
        } else {
            description.append("视频");
        }

        description.append("作品。");

        if (video.getTotalEpisodes() > 1) {
            description.append("共").append(video.getTotalEpisodes()).append("集，");
        }

        description.append("为您带来精彩的观影体验。");

        return description.toString();
    }

    /**
     * 更新集数按钮
     */
    private void updateEpisodeButton(VideoModel video) {
        if (video != null && video.getTotalEpisodes() > 1) {
            String episodeText = "EP." + video.getCurrentEpisode() + "/EP." + video.getTotalEpisodes();

            // 使用SpannableString来设置不同颜色
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到第一个"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色 (EP.1)
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                        0, slashIndex,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色 (/EP.72)
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                        slashIndex, episodeText.length(),
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        } else {
            // 单集视频或无集数信息时显示EP.1/EP.1
            String episodeText = "EP.1/EP.1";
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                        0, slashIndex,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                        slashIndex, episodeText.length(),
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        }
    }

    /**
     * 切换收藏状态
     */
    private void toggleLike() {
        if (currentVideo != null) {
            currentVideo.toggleLike();
            updateLikeButtonState(currentVideo.isLiked());
        }
    }

    /**
     * 分享视频
     */
    private void shareVideo() {
        if (currentVideo != null) {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_TEXT, "分享视频: " + currentVideo.getTitle());
            startActivity(Intent.createChooser(shareIntent, "分享到"));
        }
    }

    /**
     * 打开评论
     */
    private void openComments() {
        // 评论功能
    }

    /**
     * 打开视频详情
     */
    private void openVideoDetail() {
        if (currentVideo != null && getContext() != null) {
            Intent intent = new Intent(getContext(), VideoDetailActivity.class);
            intent.putExtra("video_model", currentVideo);
            startActivity(intent);
        }
    }

    /**
     * 显示选集选择
     */
    private void showEpisodeSelection() {
        Log.d(TAG, "showEpisodeSelection called");
        if (episodeSelectionView != null && currentVideo != null) {
            Log.d(TAG, "Episode selection view and current video are not null");
            Log.d(TAG, "Current video episodes count: " + currentVideo.getEpisodes().size());
            episodeSelectionView.setEpisodeList(currentVideo.getEpisodes());
            episodeSelectionView.setSelectedEpisode(currentVideo.getSelectedEpisode());
            episodeSelectionView.toggle();
            Log.d(TAG, "Episode selection view toggled");
        } else {
            Log.e(TAG, "Episode selection view is null: " + (episodeSelectionView == null) +
                      ", current video is null: " + (currentVideo == null));
        }
    }

    /**
     * 切换弹幕
     */
    private void toggleDanmaku() {
        isDanmakuEnabled = !isDanmakuEnabled;
        updateDanmakuButtonState();
        if (preferences != null) {
            preferences.saveDanmakuEnabled(isDanmakuEnabled);
        }

        // 控制当前Fragment的弹幕显示
        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
        if (currentFragment != null) {
            currentFragment.toggleDanmaku(isDanmakuEnabled);
        }
    }

    /**
     * 打开全屏播放器
     */
    private void openFullscreenPlayer() {
        if (getActivity() != null) {
            if (!isFullscreen) {
                // 进入全屏模式
                getActivity().setRequestedOrientation(android.content.pm.ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
                isFullscreen = true;
                Log.d(TAG, "Entering fullscreen mode");
            } else {
                // 退出全屏模式
                getActivity().setRequestedOrientation(android.content.pm.ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
                isFullscreen = false;
                Log.d(TAG, "Exiting fullscreen mode");
            }
        }
    }

    /**
     * 处理屏幕旋转配置变化
     */
    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        if (newConfig.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE) {
            // 横屏模式：隐藏底部导航栏和一些UI元素
            isFullscreen = true;
            hideUIForFullscreen();
            updateFullscreenButton();
            // 启动全屏控制元素的自动隐藏
            showFullscreenControls();
            Log.d(TAG, "Switched to landscape mode - hiding UI for fullscreen");
        } else {
            // 竖屏模式：恢复UI元素
            isFullscreen = false;
            areControlsVisibleInFullscreen = true;
            showUIForNormalMode();
            updateFullscreenButton();
            // 停止全屏控制元素的自动隐藏
            if (uiHandler != null) {
                uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
            }
            Log.d(TAG, "Switched to portrait mode - restoring UI");
        }
    }

    /**
     * 隐藏UI元素以实现全屏效果
     */
    private void hideUIForFullscreen() {
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            // 隐藏底部导航栏
            View bottomNav = mainActivity.findViewById(R.id.bottom_navigation);
            if (bottomNav != null) {
                bottomNav.setVisibility(View.GONE);
            }
        }

        // 隐藏顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.GONE);
        }

        // 隐藏侧边控制按钮（收藏、分享、评论）
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.GONE);
        }

        // 隐藏视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.GONE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.GONE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.GONE);
        }
    }

    /**
     * 显示UI元素恢复正常模式
     */
    private void showUIForNormalMode() {
        if (getActivity() instanceof MainActivity) {
            MainActivity mainActivity = (MainActivity) getActivity();
            // 显示底部导航栏
            View bottomNav = mainActivity.findViewById(R.id.bottom_navigation);
            if (bottomNav != null) {
                bottomNav.setVisibility(View.VISIBLE);
            }
        }

        // 显示顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.VISIBLE);
        }

        // 显示侧边控制按钮
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.VISIBLE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.VISIBLE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.VISIBLE);
        }

        // 确保所有UI元素都正确显示
        ensureUIElementsVisible();
    }

    /**
     * 更新全屏按钮状态
     */
    private void updateFullscreenButton() {
        if (btnFullscreen != null) {
            if (isFullscreen) {
                btnFullscreen.setImageResource(R.drawable.play_ic_suoxiao);
            } else {
                btnFullscreen.setImageResource(R.drawable.play_ic_quanping);
            }
        }
    }

    /**
     * 显示全屏控制元素 - 修改为始终显示，不自动隐藏
     */
    private void showFullscreenControls() {
        if (!isFullscreen) return;

        areControlsVisibleInFullscreen = true;

        // 显示全屏按钮
        if (btnFullscreen != null) {
            btnFullscreen.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 取消自动隐藏任务，全屏时进度条和底部按钮始终显示
        if (uiHandler != null) {
            uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
        }

        Log.d(TAG, "Fullscreen controls shown - will remain visible");
    }

    /**
     * 隐藏全屏控制元素 - 修改为始终显示进度条和底部按钮
     */
    private void hideFullscreenControls() {
        if (!isFullscreen) return;

        areControlsVisibleInFullscreen = false;

        // 全屏时进度条和底部按钮始终显示，不隐藏
        // 保持进度条可见
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 保持底部信息区域可见
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        Log.d(TAG, "Fullscreen mode - progress bar and bottom controls remain visible");
    }

    /**
     * 切换控制栏可见性
     */
    private void toggleControlsVisibility() {
        if (isControlsVisible) {
            hideControls();
        } else {
            showControls();
        }
    }

    /**
     * 显示控制栏
     */
    private void showControls() {
        topControlBar.setVisibility(View.VISIBLE);
        sideControlButtons.setVisibility(View.VISIBLE);
        bottomInfoArea.setVisibility(View.VISIBLE);
        isControlsVisible = true;
        scheduleHideControls();
    }

    /**
     * 永久显示控制栏（不自动隐藏）
     */
    private void showControlsPermanently() {
        topControlBar.setVisibility(View.VISIBLE);
        sideControlButtons.setVisibility(View.VISIBLE);
        bottomInfoArea.setVisibility(View.VISIBLE);
        isControlsVisible = true;
        // 不调用scheduleHideControls()，保持控制栏始终可见

        // 确保所有关键UI元素都可见
        ensureUIElementsVisible();
    }

    /**
     * 确保所有UI元素都正确显示
     */
    private void ensureUIElementsVisible() {
        // 确保主要布局容器可见
        if (topControlBar != null) topControlBar.setVisibility(View.VISIBLE);
        if (sideControlButtons != null) sideControlButtons.setVisibility(View.VISIBLE);
        if (bottomInfoArea != null) bottomInfoArea.setVisibility(View.VISIBLE);

        // 确保顶部控制按钮可见
        if (btnSmallScreen != null) btnSmallScreen.setVisibility(View.VISIBLE);
        if (btnDownload != null) btnDownload.setVisibility(View.VISIBLE);

        // 确保侧边控制按钮可见
        if (btnLike != null) btnLike.setVisibility(View.VISIBLE);
        if (btnShare != null) btnShare.setVisibility(View.VISIBLE);
        if (btnComment != null) btnComment.setVisibility(View.VISIBLE);
        if (btnFullscreen != null) btnFullscreen.setVisibility(View.VISIBLE);

        // 确保信息显示区域可见
        if (tvVideoTitle != null) tvVideoTitle.setVisibility(View.VISIBLE);
        if (tvVideoDescription != null) tvVideoDescription.setVisibility(View.VISIBLE);
        if (tvLikeCount != null) tvLikeCount.setVisibility(View.VISIBLE);
        if (tvShareCount != null) tvShareCount.setVisibility(View.VISIBLE);
        if (tvCommentCount != null) tvCommentCount.setVisibility(View.VISIBLE);
        if (progressSeekBar != null) progressSeekBar.setVisibility(View.VISIBLE);

        // 确保控制按钮可见
        if (btnDanmaku != null) btnDanmaku.setVisibility(View.VISIBLE);
        if (btnSpeedSelection != null) btnSpeedSelection.setVisibility(View.VISIBLE);
        if (btnQualitySelection != null) btnQualitySelection.setVisibility(View.VISIBLE);

        // 确保弹窗组件正确隐藏（避免影响其他UI）
        if (qualitySelectionView != null && qualitySelectionView.getVisibility() == View.VISIBLE) {
            qualitySelectionView.hide();
        }
        if (speedSelectionView != null && speedSelectionView.getVisibility() == View.VISIBLE) {
            speedSelectionView.hide();
        }
        if (subtitlePanelView != null && subtitlePanelView.getVisibility() == View.VISIBLE) {
            subtitlePanelView.hide();
        }

        Log.d(TAG, "UI elements visibility ensured");
    }

    /**
     * 检查并恢复UI状态
     */
    private void checkAndRestoreUIState() {
        if (!isFragmentVisible || getView() == null) return;

        // 如果是全屏模式，不要恢复UI元素
        if (isFullscreen) {
            return;
        }

        boolean needsRestore = false;

        // 检查主要布局容器是否可见
        if (topControlBar != null && topControlBar.getVisibility() != View.VISIBLE) {
            Log.w(TAG, "Top control bar is not visible, restoring...");
            needsRestore = true;
        }
        if (sideControlButtons != null && sideControlButtons.getVisibility() != View.VISIBLE) {
            Log.w(TAG, "Side control buttons are not visible, restoring...");
            needsRestore = true;
        }
        if (bottomInfoArea != null && bottomInfoArea.getVisibility() != View.VISIBLE) {
            Log.w(TAG, "Bottom info area is not visible, restoring...");
            needsRestore = true;
        }

        // 如果发现UI元素消失，立即恢复
        if (needsRestore) {
            Log.w(TAG, "UI elements disappeared, restoring all UI elements");
            showControlsPermanently();
        }
    }

    /**
     * 启动UI状态检查
     */
    private void startUIStateCheck() {
        if (uiStateCheckRunnable != null && uiHandler != null) {
            uiHandler.postDelayed(uiStateCheckRunnable, 5000); // 5秒后开始检查
        }
    }

    /**
     * 停止UI状态检查
     */
    private void stopUIStateCheck() {
        if (uiStateCheckRunnable != null && uiHandler != null) {
            uiHandler.removeCallbacks(uiStateCheckRunnable);
        }
    }

    /**
     * 确保VideoPlayerFragment的控制按钮初始为隐藏状态
     */
    private void ensureVideoControlsHidden() {
        // 延迟执行，确保ViewPager和Fragment都已初始化
        if (uiHandler != null) {
            uiHandler.postDelayed(() -> {
                if (pagerAdapter != null) {
                    for (int i = 0; i < pagerAdapter.getItemCount(); i++) {
                        VideoPlayerFragment fragment = pagerAdapter.getFragment(i);
                        if (fragment != null) {
                            fragment.hidePlayControlsInitially();
                        }
                    }
                }
                Log.d(TAG, "Ensured video controls are hidden initially");
            }, 100); // 延迟100ms确保Fragment已创建
        }
    }

    /**
     * 隐藏控制栏
     */
    private void hideControls() {
        topControlBar.setVisibility(View.GONE);
        sideControlButtons.setVisibility(View.GONE);
        bottomInfoArea.setVisibility(View.GONE);
        isControlsVisible = false;
        cancelHideControls();
    }

    /**
     * 安排自动隐藏控制栏
     */
    private void scheduleHideControls() {
        cancelHideControls();
        if (hideControlsRunnable != null) {
            uiHandler.postDelayed(hideControlsRunnable, 3000); // 3秒后隐藏
        }
    }

    /**
     * 取消自动隐藏控制栏
     */
    private void cancelHideControls() {
        if (hideControlsRunnable != null) {
            uiHandler.removeCallbacks(hideControlsRunnable);
        }
    }

    /**
     * 更新收藏按钮状态
     */
    private void updateLikeButtonState(boolean isLiked) {
        btnLike.setImageResource(isLiked ? R.drawable.play_ic_like_sel : R.drawable.play_ic_like);
    }

    /**
     * 更新弹幕按钮状态
     */
    private void updateDanmakuButtonState() {
        if (btnDanmaku != null) {
            btnDanmaku.setSelected(isDanmakuEnabled);
            // 保持按钮完全可见和可点击
            btnDanmaku.setAlpha(1.0f);
            // 可以根据需要更改图标或颜色
        }
    }

    /**
     * 更新倍速按钮文本
     */
    private void updateSpeedButtonText() {
        String speedText = currentPlaybackSpeed + "x";
        btnSpeedSelection.setText(speedText);
    }

    /**
     * 更新清晰度按钮文本
     */
    private void updateQualityButtonText() {
        if (btnQualitySelection != null && currentQuality != null) {
            btnQualitySelection.setText(currentQuality.getDisplayName());
        }
    }



    /**
     * 切换描述文本的展开/收起状态
     */
    private void toggleDescriptionExpansion() {
        if (isDescriptionExpanded) {
            // 收起：显示1行
            tvVideoDescription.setMaxLines(1);
            tvVideoDescription.setEllipsize(android.text.TextUtils.TruncateAt.END);
        } else {
            // 展开：显示全部内容
            tvVideoDescription.setMaxLines(Integer.MAX_VALUE);
            tvVideoDescription.setEllipsize(null);
        }
        isDescriptionExpanded = !isDescriptionExpanded;
    }

    // Fragment生命周期管理
    @Override
    public void setUserVisibleHint(boolean isVisibleToUser) {
        super.setUserVisibleHint(isVisibleToUser);
        isFragmentVisible = isVisibleToUser;
        Log.d(TAG, "setUserVisibleHint: " + isVisibleToUser);

        if (!isVisibleToUser) {
            // Fragment不可见时，暂停所有视频播放
            pauseAllVideos();
        }

        handleVisibilityChange();
    }

    // @Override
    // public void onResume() {
    //     super.onResume();
    //     isFragmentVisible = true;
    //     handleVisibilityChange();
    //     // 确保控制栏在Resume时可见
    //     showControlsPermanently();
    //     // 启动进度更新
    //     startProgressUpdate();
    //     // 启动UI状态检查
    //     startUIStateCheck();
    //     Log.d(TAG, "Fragment resumed");
    // }

    @Override
    public void onPause() {
        super.onPause();
        isFragmentVisible = false;

        // 暂停所有视频播放
        pauseAllVideos();

        handleVisibilityChange();
        // 停止进度更新
        stopProgressUpdate();
        // 停止UI状态检查
        stopUIStateCheck();

        // 停止网络监控
        if (networkOptimizationManager != null) {
            networkOptimizationManager.stopNetworkMonitoring();
        }

        // 隐藏弱网络警告
        if (poorNetworkWarningView != null) {
            poorNetworkWarningView.hideImmediately();
        }

        Log.d(TAG, "Fragment paused");
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 清理资源
        cancelHideControls();
        stopUIStateCheck();

        // 不再需要释放playerManager，因为每个Fragment都有自己的播放器

        if (pagerAdapter != null) {
            pagerAdapter.cleanup();
            pagerAdapter = null;
        }

        // 清理网络优化资源
        if (networkOptimizationManager != null) {
            networkOptimizationManager.stopNetworkMonitoring();
            networkOptimizationManager.setNetworkOptimizationListener(null);
        }

        if (poorNetworkWarningView != null) {
            poorNetworkWarningView.cleanup();
            poorNetworkWarningView.detachFromParent();
            poorNetworkWarningView = null;
        }

        // 清理高级组件
        qualitySelectionView = null;
        speedSelectionView = null;
        episodeSelectionView = null;

        Log.d(TAG, "Fragment view destroyed and resources cleaned up");
    }

    /**
     * 处理Fragment可见性变化
     */
    private void handleVisibilityChange() {
        if (isFragmentVisible) {
            // Fragment可见时，更新UI显示
            if (currentVideo != null) {
                loadVideoData(currentVideo);
            }
            // Discover页面控制栏应该始终可见，不自动隐藏
            showControlsPermanently();

            // 使用新的生命周期管理，ViewPager2会自动恢复当前Fragment的播放
            Log.d(TAG, "Fragment visible - ViewPager2 will manage video playback automatically");
        } else {
            // Fragment不可见时取消自动隐藏
            cancelHideControls();
        }
    }

    // VideoPlayerListener接口实现（简化版本，因为不再直接管理播放器）
    @Override
    public void onPrepared() {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlaybackStarted() {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlaybackPaused() {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlaybackStopped() {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlaybackCompleted() {
        uiHandler.post(() -> {
            Log.d(TAG, "Playback completed");
            // 自动切换到下一个视频
            if (currentVideoIndex < videoList.size() - 1) {
                viewPagerVideos.setCurrentItem(currentVideoIndex + 1, true);
            }
        });
    }

    @Override
    public void onPositionChanged(long position, long duration) {
        // 这个方法不再被调用，因为DiscoverFragment不再直接监听播放器
        // 进度更新现在通过定时器从当前Fragment获取
    }

    @Override
    public void onBufferingUpdate(boolean isBuffering, int bufferedPercentage) {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onError(String error) {
        uiHandler.post(() -> {
            Log.e(TAG, "Playback error: " + error);
        });
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {
        // 不需要处理，由各个Fragment自己处理
    }

    @Override
    public void onPlaybackSpeedChanged(float speed) {
        uiHandler.post(() -> {
            currentPlaybackSpeed = speed;
            updateSpeedButtonText();
        });
    }

    @Override
    public void onVolumeChanged(float volume) {
        // 音量变化处理
    }

    /**
     * 获取当前播放的视频
     *
     * @return 当前视频模型
     */
    public VideoModel getCurrentVideo() {
        return currentVideo;
    }

    /**
     * 检查是否正在播放
     *
     * @return 是否正在播放
     */
    public boolean isPlaying() {
        if (pagerAdapter != null) {
            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
            return currentFragment != null && currentFragment.isPlaying();
        }
        return false;
    }

    /**
     * 检查Fragment是否可见
     *
     * @return 是否可见
     */
    public boolean isFragmentVisible() {
        return isFragmentVisible;
    }

    /**
     * 手动刷新视频列表
     */
    public void refreshVideoList() {
        initializeData();
        if (pagerAdapter != null) {
            pagerAdapter.updateVideoList(videoList);
            pagerAdapter.notifyDataSetChanged();
        }
        Log.d(TAG, "Video list refreshed");
    }

    /**
     * 跳转到指定视频
     *
     * @param videoId 视频ID
     */
    public void jumpToVideo(String videoId) {
        if (videoId == null || videoList == null) return;

        for (int i = 0; i < videoList.size(); i++) {
            if (videoId.equals(videoList.get(i).getId())) {
                viewPagerVideos.setCurrentItem(i, true);
                break;
            }
        }
    }

    /**
     * 通知当前Fragment更改清晰度
     */
    private void notifyCurrentFragmentQualityChange(VideoQualitySelector.VideoQuality quality) {
        if (pagerAdapter != null) {
            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                currentFragment.getPlayerManager().setQuality(quality);
            }
        }
    }

    /**
     * 通知当前Fragment更改播放速度
     */
    private void notifyCurrentFragmentSpeedChange(float speed) {
        if (pagerAdapter != null) {
            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                currentFragment.getPlayerManager().setPlaybackSpeed(speed);
            }
        }
    }

    /**
     * 启动进度更新
     */
    private void startProgressUpdate() {
        shouldUpdateProgress = true;
        uiHandler.post(progressUpdateRunnable);
    }

    /**
     * 停止进度更新
     */
    private void stopProgressUpdate() {
        shouldUpdateProgress = false;
        uiHandler.removeCallbacks(progressUpdateRunnable);
    }

    /**
     * 暂停所有视频播放
     * 使用全局播放器管理器确保所有视频都被暂停
     */
    private void pauseAllVideos() {
        GlobalVideoPlayerManager.getInstance().stopAllFragments();
        Log.d(TAG, "pauseAllVideos called - stopped all fragments via GlobalVideoPlayerManager");
    }

    /**
     * 设置全屏模式下的触摸事件处理 - 复用VideoPlayerActivity的成功实现
     */
    private void setupFullscreenTouchHandling() {
        // 使用改进的点击检测来控制全屏时的UI显示
        final float[] downX = new float[1];
        final float[] downY = new float[1];
        final long[] downTime = new long[1];

        viewPagerVideos.setOnTouchListener((v, event) -> {
            if (!isFullscreen) {
                return false; // 非全屏时不处理
            }

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    downX[0] = event.getX();
                    downY[0] = event.getY();
                    downTime[0] = System.currentTimeMillis();

                    // 检查是否点击在按钮区域，如果是则不拦截
                    if (isTouchOnControlButtons(event)) {
                        return false; // 让按钮处理点击事件
                    }
                    break;

                case MotionEvent.ACTION_UP:
                    float upX = event.getX();
                    float upY = event.getY();
                    long upTime = System.currentTimeMillis();

                    // 再次检查是否点击在按钮区域
                    if (isTouchOnControlButtons(event)) {
                        return false; // 让按钮处理点击事件
                    }

                    // 检查是否是点击（移动距离小且时间短）
                    float deltaX = Math.abs(upX - downX[0]);
                    float deltaY = Math.abs(upY - downY[0]);
                    long deltaTime = upTime - downTime[0];

                    if (deltaX < 50 && deltaY < 50 && deltaTime < 500) {
                        // 合并控制逻辑：同时控制视频播放按钮和页面元素
                        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                        boolean videoControlsVisible = currentFragment != null && currentFragment.areControlsVisible();

                        if (videoControlsVisible || areControlsVisibleInFullscreen) {
                            // 如果任一控制元素可见，则隐藏Fragment的播放控制按钮
                            if (currentFragment != null) {
                                currentFragment.hidePlayControls();
                            }
                            // 全屏模式下不隐藏进度条和底部按钮，只隐藏Fragment的播放控制
                            // 在Discover页面全屏时，进度条和底部按钮始终保持可见
                        } else {
                            // 如果都不可见，则显示所有
                            if (currentFragment != null) {
                                currentFragment.showPlayControls();
                            }
                            showFullscreenControls();
                        }
                        return true;
                    }
                    break;
            }
            return false; // 让ViewPager2正常处理滑动事件
        });
    }

    /**
     * 检查触摸点是否在控制按钮区域内
     */
    private boolean isTouchOnControlButtons(MotionEvent event) {
        if (!areControlsVisibleInFullscreen) {
            return false; // 控制元素不可见时，不需要检查
        }

        float x = event.getRawX(); // 使用屏幕坐标
        float y = event.getRawY();

        // 检查所有可见的控制按钮
        View[] buttons = {
            btnSmallScreen, btnDownload, btnLike, btnShare, btnComment,
            btnDetail, btnEpisodeSelection, btnDanmaku, btnSpeedSelection,
            btnQualitySelection, btnFullscreen
        };

        for (View button : buttons) {
            if (button != null && button.getVisibility() == View.VISIBLE) {
                int[] location = new int[2];
                button.getLocationOnScreen(location);
                int left = location[0];
                int top = location[1];
                int right = left + button.getWidth();
                int bottom = top + button.getHeight();

                // 扩大触摸区域，增加容错性
                int padding = 20;
                if (x >= left - padding && x <= right + padding &&
                    y >= top - padding && y <= bottom + padding) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 获取当前显示的VideoPlayerFragment
     */
    private VideoPlayerFragment getCurrentVideoFragment() {
        if (pagerAdapter != null && currentVideoIndex >= 0 && currentVideoIndex < pagerAdapter.getItemCount()) {
            return pagerAdapter.getFragment(currentVideoIndex);
        }
        return null;
    }

    /**
     * 重置全屏自动隐藏定时器
     */
    private void resetFullscreenAutoHideTimer() {
        if (uiHandler != null && isFullscreen && areControlsVisibleInFullscreen) {
            uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
            // 4秒后自动隐藏
            uiHandler.postDelayed(hideFullscreenControlsRunnable, 4000);
            Log.d(TAG, "Fullscreen auto-hide timer reset - 4 seconds");
        }
    }

    /**
     * 显示海报弹窗
     */
    private void showPosterPopup() {
        if (posterPopupOverlay != null) {
            posterPopupOverlay.setVisibility(View.VISIBLE);
            // 使用统一的页面淡入动画
            com.android.video.utils.UIAnimationUtils.animatePageFadeIn(posterPopupOverlay);

            // 使用统一的弹窗缩放动画
            if (posterContainer != null) {
                com.android.video.utils.UIAnimationUtils.animateDialogScaleIn(posterContainer);
            }

            Log.d(TAG, "Poster popup shown");
        }
    }

    /**
     * 隐藏海报弹窗
     */
    private void hidePosterPopup() {
        if (posterPopupOverlay != null && posterPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            posterPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (posterPopupOverlay != null) {
                        posterPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Poster popup hidden");
        }
    }

    /**
     * 显示VIP解锁弹窗
     */
    public void showVipUnlockPopup() {
        if (vipUnlockPopupOverlay != null) {
            vipUnlockPopupOverlay.setVisibility(View.VISIBLE);
            // 使用统一的页面淡入动画
            com.android.video.utils.UIAnimationUtils.animatePageFadeIn(vipUnlockPopupOverlay);

            // 使用统一的弹窗缩放动画
            if (vipUnlockContainer != null) {
                com.android.video.utils.UIAnimationUtils.animateDialogScaleIn(vipUnlockContainer);
            }

            Log.d(TAG, "VIP unlock popup shown");
        }
    }

    /**
     * 隐藏VIP解锁弹窗
     */
    private void hideVipUnlockPopup() {
        if (vipUnlockPopupOverlay != null && vipUnlockPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            vipUnlockPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (vipUnlockPopupOverlay != null) {
                        vipUnlockPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "VIP unlock popup hidden");
        }
    }

    /**
     * 显示观看广告弹窗
     */
    private void showWatchAdPopup() {
        if (watchAdPopupOverlay != null) {
            watchAdPopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            watchAdPopupOverlay.setAlpha(0f);
            watchAdPopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // 观看广告容器缩放动画
            if (watchAdContainer != null) {
                watchAdContainer.setScaleX(0.8f);
                watchAdContainer.setScaleY(0.8f);
                watchAdContainer.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .start();
            }

            Log.d(TAG, "Watch AD popup shown");
        }
    }

    /**
     * 隐藏观看广告弹窗
     */
    private void hideWatchAdPopup() {
        if (watchAdPopupOverlay != null && watchAdPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            watchAdPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (watchAdPopupOverlay != null) {
                        watchAdPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Watch AD popup hidden");
        }
    }

    /**
     * 显示充值弹窗
     */
    private void showRechargePopup() {
        if (rechargePopupOverlay != null) {
            rechargePopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            rechargePopupOverlay.setAlpha(0f);
            rechargePopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // 充值弹窗容器从底部滑入动画
            if (rechargeContainer != null) {
                rechargeContainer.setTranslationY(rechargeContainer.getHeight());
                rechargeContainer.animate()
                    .translationY(0f)
                    .setDuration(300)
                    .start();
            }

            // 重置选择状态
            resetRechargeSelections();

            Log.d(TAG, "Recharge popup shown");
        }
    }

    /**
     * 隐藏充值弹窗
     */
    private void hideRechargePopup() {
        if (rechargePopupOverlay != null && rechargePopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            rechargePopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (rechargePopupOverlay != null) {
                        rechargePopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Recharge popup hidden");
        }
    }

    /**
     * 选择充值金额
     */
    private void selectRechargeAmount(int amount) {
        // 重置所有充值卡片的选择状态
        resetRechargeAmountSelections();

        selectedRechargeAmount = amount;

        // 设置选中状态
        LinearLayout selectedCard = null;
        switch (amount) {
            case 500:
                selectedCard = rechargeCard500;
                break;
            case 1000:
                selectedCard = rechargeCard1000;
                break;
            case 2000:
                selectedCard = rechargeCard2000;
                break;
            case 3000:
                selectedCard = rechargeCard3000;
                break;
        }

        if (selectedCard != null) {
            selectedCard.setSelected(true);
        }


        Log.d(TAG, "Recharge amount selected: " + amount);
    }

    /**
     * 选择VIP计划
     */
    private void selectVipPlan(String plan) {
        // 重置VIP卡片选择状态
        resetVipSelections();

        if ("weekly".equals(plan)) {
            isWeeklySelected = true;
            // 这里可以添加视觉反馈，比如改变卡片样式
            Log.d(TAG, "Weekly VIP plan selected");
        } else if ("monthly".equals(plan)) {
            isMonthlySelected = true;
            // 这里可以添加视觉反馈，比如改变卡片样式
            Log.d(TAG, "Monthly VIP plan selected");
        }
    }

    /**
     * 重置充值选择状态
     */
    private void resetRechargeSelections() {
        resetRechargeAmountSelections();
        resetVipSelections();
    }

    /**
     * 重置充值金额选择状态
     */
    private void resetRechargeAmountSelections() {
        selectedRechargeAmount = 0;
        if (rechargeCard500 != null) rechargeCard500.setSelected(false);
        if (rechargeCard1000 != null) rechargeCard1000.setSelected(false);
        if (rechargeCard2000 != null) rechargeCard2000.setSelected(false);
        if (rechargeCard3000 != null) rechargeCard3000.setSelected(false);
    }

    /**
     * 重置VIP选择状态
     */
    private void resetVipSelections() {
        isWeeklySelected = false;
        isMonthlySelected = false;
        // 这里可以重置VIP卡片的视觉状态
    }

    /**
     * 启动悬浮窗播放
     */
    private void startFloatingVideoPlay() {
        if (currentVideo != null && getActivity() != null) {
            // 先隐藏当前视频播放器，显示黑色背景
            hideCurrentVideoPlayer();

            // 启动悬浮窗播放
            FloatingVideoManager.getInstance().startFromDiscover(getActivity(), currentVideo);
        } else {
            if (getContext() != null) {
                Toast.makeText(getContext(), "当前没有播放的视频", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 隐藏当前视频播放器，显示黑色背景
     */
    private void hideCurrentVideoPlayer() {
        try {
            // 获取当前显示的VideoPlayerFragment
            VideoPlayerFragment currentFragment = getCurrentVideoFragment();
            if (currentFragment != null) {
                // 隐藏Fragment中的PlayerView
                currentFragment.hidePlayerViewForFloating();
            }

            // 也可以直接查找ViewPager中的PlayerView
            if (viewPagerVideos != null) {
                for (int i = 0; i < viewPagerVideos.getChildCount(); i++) {
                    View child = viewPagerVideos.getChildAt(i);
                    hidePlayerViewsInView(child);
                }
            }

            Log.d(TAG, "Current video player hidden for floating window in DiscoverFragment");
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide current video player in DiscoverFragment", e);
        }
    }

    /**
     * 在指定视图中递归查找并隐藏PlayerView
     */
    private void hidePlayerViewsInView(View view) {
        try {
            if (view instanceof com.google.android.exoplayer2.ui.PlayerView) {
                com.google.android.exoplayer2.ui.PlayerView playerView = (com.google.android.exoplayer2.ui.PlayerView) view;
                playerView.setBackgroundColor(android.graphics.Color.BLACK);
                // 暂时移除播放器，显示黑色背景
                if (playerView.getPlayer() != null) {
                    playerView.setPlayer(null);
                }
                Log.d(TAG, "PlayerView hidden and set to black background in DiscoverFragment");
            } else if (view instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    hidePlayerViewsInView(viewGroup.getChildAt(i));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding player views in DiscoverFragment", e);
        }
    }

    @Override
    public void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 处理悬浮窗权限请求结果
        if (requestCode == FloatingVideoPermissionUtils.REQUEST_CODE_OVERLAY_PERMISSION) {
            if (getContext() != null) {
                FloatingVideoManager.getInstance().handlePermissionResult(getContext(), requestCode);
            }
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();

        // 清理悬浮窗权限回调
        FloatingVideoPermissionUtils.clearPermissionCallback();

        // 清理瀑布流推荐API服务
        if (waterfallApiService != null) {
            waterfallApiService.destroy();
            waterfallApiService = null;
        }
    }

    // ==================== 内容保护功能 ====================

    /**
     * 重写父类方法，启用内容保护功能
     * Discover页面需要启用内容保护
     */
    @Override
    protected boolean shouldEnableContentProtection() {
        return true; // Discover页面启用内容保护
    }
}
