package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.FeedbackType;
import java.util.List;

/**
 * 反馈类型适配器
 */
public class FeedbackTypeAdapter extends RecyclerView.Adapter<FeedbackTypeAdapter.ViewHolder> {

    private List<FeedbackType> feedbackTypes;
    private OnTypeSelectedListener listener;

    public interface OnTypeSelectedListener {
        void onTypeSelected(FeedbackType feedbackType);
    }

    public FeedbackTypeAdapter(List<FeedbackType> feedbackTypes, OnTypeSelectedListener listener) {
        this.feedbackTypes = feedbackTypes;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_feedback_type, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        FeedbackType feedbackType = feedbackTypes.get(position);
        holder.bind(feedbackType);
    }

    @Override
    public int getItemCount() {
        return feedbackTypes.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTypeName;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTypeName = itemView.findViewById(R.id.tv_type_name);
        }

        public void bind(FeedbackType feedbackType) {
            tvTypeName.setText(feedbackType.getName());

            // 设置选中状态的样式 - 白色背景是选中状态
            if (feedbackType.isSelected()) {
                tvTypeName.setBackgroundResource(R.drawable.feedback_type_selected_bg);
                tvTypeName.setTextColor(itemView.getContext().getColor(R.color.feedback_type_selected_text));
            } else {
                tvTypeName.setBackgroundResource(R.drawable.feedback_type_normal_bg);
                tvTypeName.setTextColor(itemView.getContext().getColor(R.color.feedback_type_unselected_text));
            }

            // 设置点击事件 - 同时设置itemView和tvTypeName的点击事件
            View.OnClickListener clickListener = v -> {
                if (listener != null) {
                    listener.onTypeSelected(feedbackType);
                }
            };

            itemView.setOnClickListener(clickListener);
            tvTypeName.setOnClickListener(clickListener);
        }
    }
}
