package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.databinding.ActivityVideoDetailBinding;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.VideoModel;
import com.android.video.model.ActorModel;
import com.android.video.model.DirectorModel;
import com.android.video.model.EpisodeModel;
import com.android.video.model.TagModel;
import com.android.video.adapter.SynopsisTagAdapter;
import com.android.video.adapter.ActorAdapter;
import com.android.video.adapter.DirectorAdapter;
import com.android.video.adapter.EpisodeAdapter;
import com.android.video.adapter.EpisodeRangeAdapter;
import com.android.video.adapter.RecommendVideoAdapter;
import com.android.video.decoration.GridSpacingItemDecoration;
import com.android.video.utils.TestValidationUtils;
import com.android.video.utils.NavigationBarUtils;
import com.android.video.network.VideoDetailApiService;
import com.android.video.network.ChapterListApiService;
import com.android.video.model.response.VideoDetailResponseModel;
import com.android.video.model.response.ChapterListResponseModel;
import com.android.video.model.FilmInfo;
import com.android.video.model.DirectorInfo;
import com.android.video.model.PerformerInfo;
import com.android.video.model.ChapterInfo;
import com.android.video.model.PlayProgressVO;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import android.widget.Toast;
import android.view.View;
import android.graphics.Rect;
import android.view.ViewGroup;
import android.widget.Button;
import java.util.ArrayList;
import java.util.List;
import com.android.video.base.BaseMultiLanguageActivity;

/**
 * 视频详情页Activity - 显示视频的详细信息
 * 包含视频海报、基本信息、演员、导演、剧集选择、推荐视频等功能
 * <AUTHOR>
 */
public class VideoDetailActivity extends BaseMultiLanguageActivity {

    private ActivityVideoDetailBinding binding;
    private VideoModel videoModel;
    private Button continuePlayingButton;

    // API相关
    private VideoDetailApiService videoDetailApiService;
    private ChapterListApiService chapterListApiService;
    private String filmLanguageInfoId;
    private String filmTitle; // 用于显示标题

    // 加载状态
    private SwipeRefreshLayout swipeRefreshLoading;

    // 适配器
    private SynopsisTagAdapter synopsisTagAdapter;
    private ActorAdapter actorAdapter;
    private DirectorAdapter directorAdapter;
    private EpisodeAdapter episodeAdapter;
    private EpisodeRangeAdapter episodeRangeAdapter;
    private RecommendVideoAdapter recommendVideoAdapter;

    // 推荐视频列表（从API获取）
    private List<VideoModel> recommendVideoList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        binding = ActivityVideoDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 隐藏ActionBar以获得全屏体验
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        // 初始化API服务
        videoDetailApiService = new VideoDetailApiService();
        chapterListApiService = new ChapterListApiService();

        // 初始化UI组件（必须在receiveVideoData之前，因为可能需要显示加载状态）
        initViews();

        // 设置适配器
        setupAdapters();

        // 设置点击事件
        setupClickListeners();

        // 接收传递的视频数据（可能会触发API调用和加载状态显示）
        receiveVideoData();

        // 如果有videoModel数据，加载视频数据（兼容旧模式）
        if (videoModel != null) {
            loadVideoData();
            validateVideoData();
        }
    }

    /**
     * 接收Intent传递的视频数据
     */
    private void receiveVideoData() {
        Intent intent = getIntent();

        // 优先检查是否传递了filmLanguageInfoId（新的API模式）
        if (intent != null && intent.hasExtra("filmLanguageInfoId")) {
            filmLanguageInfoId = intent.getStringExtra("filmLanguageInfoId");
            filmTitle = intent.getStringExtra("filmTitle"); // 可选的标题，用于显示

            // 如果有filmLanguageInfoId，通过API加载数据
            if (filmLanguageInfoId != null && !filmLanguageInfoId.isEmpty()) {
                loadVideoDetailFromApi();
                return;
            }
        }

        // 兼容旧的video_model模式
        if (intent != null && intent.hasExtra("video_model")) {
            videoModel = (VideoModel) intent.getSerializableExtra("video_model");

            android.util.Log.d("VideoDetailActivity", "兼容模式：接收到VideoModel - " + videoModel.getTitle());

            // 如果有filmLanguageInfoId，尝试通过API重新加载完整数据
            if (videoModel.getFilmLanguageInfoId() != null && !videoModel.getFilmLanguageInfoId().isEmpty()) {
                filmLanguageInfoId = videoModel.getFilmLanguageInfoId();
                filmTitle = videoModel.getTitle();
                android.util.Log.d("VideoDetailActivity", "兼容模式：使用filmLanguageInfoId加载API数据");
                loadVideoDetailFromApi();
                return;
            } else {
                // 没有filmLanguageInfoId，检查是否有filmId可以用于其他API调用
                android.util.Log.w("VideoDetailActivity", "VideoModel缺少filmLanguageInfoId，无法调用章节列表API");

                // 检查是否是Banner数据（通过标签判断）
                boolean isBannerData = false;
                if (videoModel.getTags() != null) {
                    for (String tag : videoModel.getTags()) {
                        if (tag.startsWith("banner_id:") || tag.startsWith("film_id:")) {
                            isBannerData = true;
                            break;
                        }
                    }
                }

                if (isBannerData) {
                    android.util.Log.d("VideoDetailActivity", "检测到Banner数据，显示提示信息");
                    handleApiError("Banner内容暂不支持详情查看，请选择其他视频");
                } else {
                    android.util.Log.d("VideoDetailActivity", "使用VideoModel数据显示基本信息");
                    // 使用现有的VideoModel数据显示基本信息
                    loadVideoData();
                    validateVideoData();
                }
                return;
            }
        }

        // 如果没有传递任何数据，显示错误并返回
        android.util.Log.e("VideoDetailActivity", "没有传递视频数据，无法显示详情页");
        handleApiError("没有传递视频数据");
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        // 设置状态栏适配
        setupStatusBarAdaptation();

        // 初始化RecyclerView
        setupRecyclerViews();

        // 初始化Continue Playing按钮
        initContinuePlayingButton();

        // 初始化加载状态组件
        initLoadingComponents();
    }

    /**
     * 设置状态栏适配
     */
    private void setupStatusBarAdaptation() {
        // 状态栏适配将在布局文件中通过Guideline处理
        // 这里可以添加额外的状态栏处理逻辑
    }

    /**
     * 初始化Continue Playing按钮
     */
    private void initContinuePlayingButton() {
        continuePlayingButton = binding.buttonContinuePlaying;
    }

    /**
     * 初始化加载状态组件
     */
    private void initLoadingComponents() {
        swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);

        // 设置加载动画颜色和位置
        if (swipeRefreshLoading != null) {
            swipeRefreshLoading.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );

            // 设置加载动画的位置，参考MyList的位置（靠上但不是最顶部）
            int screenHeight = getResources().getDisplayMetrics().heightPixels;
            int topOffset = (int) (screenHeight * 0.25f); // 屏幕高度的25%位置
            swipeRefreshLoading.setProgressViewOffset(false, topOffset - 50, topOffset + 50);

            // 设置下拉刷新监听器（可选，用于重试）
            swipeRefreshLoading.setOnRefreshListener(() -> {
                if (filmLanguageInfoId != null && !filmLanguageInfoId.isEmpty()) {
                    loadVideoDetailFromApi();
                }
            });
        }
        // 按钮的导航栏适配将在onApplyNavigationBarInsets中处理
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerViews() {
        // Synopsis标签RecyclerView
        binding.recyclerViewTags.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 演员RecyclerView
        binding.recyclerViewActors.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 导演RecyclerView
        binding.recyclerViewDirectors.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 剧集区间选择RecyclerView
        binding.recyclerViewEpisodeRanges.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 剧集RecyclerView
        binding.recyclerViewEpisodes.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));

        // 为剧集RecyclerView添加12dp间距
        binding.recyclerViewEpisodes.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(Rect outRect, android.view.View view, RecyclerView parent, RecyclerView.State state) {
                int position = parent.getChildAdapterPosition(view);
                if (position > 0) {
                    outRect.left = (int) (12 * getResources().getDisplayMetrics().density); // 12dp转换为px
                }
            }
        });

        // 推荐视频RecyclerView (2x2网格)
        binding.recyclerViewRecommendVideos.setLayoutManager(
            new GridLayoutManager(this, 2));

        // 添加网格间距装饰器 - 12dp间距
        int spacingInPx = (int) (12 * getResources().getDisplayMetrics().density);
        binding.recyclerViewRecommendVideos.addItemDecoration(
            new GridSpacingItemDecoration(2, spacingInPx, false));
    }

    /**
     * 设置适配器
     */
    private void setupAdapters() {
        // Synopsis标签适配器
        synopsisTagAdapter = new SynopsisTagAdapter(new ArrayList<>());
        binding.recyclerViewTags.setAdapter(synopsisTagAdapter);

        // 演员适配器
        actorAdapter = new ActorAdapter(this, new ArrayList<>());
        binding.recyclerViewActors.setAdapter(actorAdapter);

        // 导演适配器
        directorAdapter = new DirectorAdapter(this, new ArrayList<>());
        binding.recyclerViewDirectors.setAdapter(directorAdapter);

        // 剧集适配器
        episodeAdapter = new EpisodeAdapter(new ArrayList<>());
        episodeAdapter.setOnEpisodeClickListener(this::onEpisodeClick);
        binding.recyclerViewEpisodes.setAdapter(episodeAdapter);

        // 推荐视频适配器
        recommendVideoAdapter = new RecommendVideoAdapter(new ArrayList<>());
        recommendVideoAdapter.setOnVideoClickListener(this::onRecommendVideoClick);
        binding.recyclerViewRecommendVideos.setAdapter(recommendVideoAdapter);
    }

    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        // 返回按钮
        binding.buttonBack.setOnClickListener(v -> finish());

        // 分享按钮
        binding.buttonShare.setOnClickListener(v -> shareVideo());

        // 收藏按钮
        binding.buttonLikeScrollable.setOnClickListener(v -> toggleLike());

        // 概要展开/收起
        binding.textSynopsisContent.setOnClickListener(v -> toggleSynopsisExpanded());

        // Continue Playing按钮
        binding.buttonContinuePlaying.setOnClickListener(v -> onContinuePlayingClick());
    }

    /**
     * 加载视频数据
     */
    private void loadVideoData() {
        if (videoModel == null) return;

        // 设置基本信息
        loadBasicInfo();

        // 设置标签
        loadTags();

        // 设置演员信息
        loadActors();

        // 设置导演信息
        loadDirectors();

        // 设置剧集信息
        loadEpisodes();

        // 设置推荐视频
        loadRecommendVideos();
    }

    /**
     * 设置基本信息
     */
    private void loadBasicInfo() {
        // 设置海报
        if (videoModel.getPosterUrl() != null && !videoModel.getPosterUrl().isEmpty()) {
            // 使用Glide加载网络图片
            Glide.with(this)
                    .load(videoModel.getPosterUrl())
                    .placeholder(R.drawable.movie_poster)
                    .error(R.drawable.movie_poster)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(binding.imageVideoPosterScrollable);

            Glide.with(this)
                    .load(videoModel.getPosterUrl())
                    .placeholder(R.drawable.movie_poster)
                    .error(R.drawable.movie_poster)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(binding.imageSmallPosterScrollable);
        } else {
            binding.imageVideoPosterScrollable.setImageResource(R.drawable.movie_poster);
            binding.imageSmallPosterScrollable.setImageResource(R.drawable.movie_poster);
        }

        // 设置标题
        binding.textVideoTitleScrollable.setText(videoModel.getTitle());

        // 设置观看次数
        binding.textViewCountScrollable.setText(formatViewCount(videoModel.getViewCount()));

        // 设置收藏状态
        updateLikeButton();

        // 设置概要内容
        binding.textSynopsisContent.setText(videoModel.getSynopsis());
        updateSynopsisDisplay();
    }

    /**
     * 加载标签
     */
    private void loadTags() {
        // 将字符串标签转换为TagModel列表
        List<TagModel> tagModels = new ArrayList<>();
        for (String tag : videoModel.getTags()) {
            tagModels.add(new TagModel(tag, "video_tag"));
        }
        synopsisTagAdapter.updateTagList(tagModels);
    }

    /**
     * 加载演员信息
     */
    private void loadActors() {
        actorAdapter.updateActors(videoModel.getActors());
    }

    /**
     * 加载导演信息
     */
    private void loadDirectors() {
        directorAdapter.updateDirectors(videoModel.getDirectors());
    }

    /**
     * 加载剧集信息
     */
    private void loadEpisodes() {
        // 设置区间选择器
        List<String> ranges = videoModel.getAllEpisodeRanges();
        episodeRangeAdapter = new EpisodeRangeAdapter(ranges, this::onEpisodeRangeClick);
        binding.recyclerViewEpisodeRanges.setAdapter(episodeRangeAdapter);

        // 加载当前区间的剧集
        List<EpisodeModel> episodesInRange = videoModel.getEpisodesInRange();
        episodeAdapter.updateEpisodeList(episodesInRange);
    }

    /**
     * 区间点击事件
     */
    private void onEpisodeRangeClick(int rangeIndex) {
        // 更新选中的区间
        videoModel.setSelectedEpisodeRange(rangeIndex);

        // 重新加载当前区间的剧集（选中状态已在VideoModel中正确设置）
        List<EpisodeModel> episodesInRange = videoModel.getEpisodesInRange();
        episodeAdapter.updateEpisodeList(episodesInRange);

        // 计算当前选中剧集在新区间中的相对位置
        int globalSelectedIndex = videoModel.getSelectedEpisodeIndex();
        int rangeStartIndex = rangeIndex * 30;
        int relativePosition = globalSelectedIndex - rangeStartIndex;

        // 如果当前选中的剧集在新区间内，设置适配器的选中位置；否则选中第一个
        if (relativePosition >= 0 && relativePosition < episodesInRange.size()) {
            episodeAdapter.setSelectedPosition(relativePosition);
        } else {
            episodeAdapter.setSelectedPosition(0);
            // 同时更新VideoModel中的全局选中索引为新区间的第一个剧集
            videoModel.setSelectedEpisodeIndex(rangeStartIndex);
            // 重新加载剧集列表以更新选中状态
            episodesInRange = videoModel.getEpisodesInRange();
            episodeAdapter.updateEpisodeList(episodesInRange);
        }
    }

    /**
     * 加载推荐视频
     */
    private void loadRecommendVideos() {
        // 只使用从API获取的推荐视频数据
        if (recommendVideoList != null && !recommendVideoList.isEmpty()) {
            recommendVideoAdapter.updateVideoList(recommendVideoList);
            android.util.Log.d("VideoDetailActivity", "使用API推荐视频数据: " + recommendVideoList.size() + " 个推荐");
        } else {
            // 如果API没有返回推荐视频，显示空列表
            recommendVideoAdapter.updateVideoList(new ArrayList<>());
            android.util.Log.d("VideoDetailActivity", "API未返回推荐视频，显示空列表");
        }
    }

    /**
     * 分享视频
     */
    private void shareVideo() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
            return;
        }

        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, "分享视频: " + videoModel.getTitle());
        startActivity(Intent.createChooser(shareIntent, "分享到"));
    }

    /**
     * 切换收藏状态
     */
    private void toggleLike() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
            return;
        }

        videoModel.toggleLike();
        updateLikeButton();
    }

    /**
     * 更新收藏按钮状态
     */
    private void updateLikeButton() {
        if (videoModel.isLiked()) {
            // 已收藏状态：显示movie_ic_no_like图标，表示点击会取消收藏
            binding.imageLikeIconScrollable.setImageResource(R.drawable.movie_ic_no_like);
            binding.textLikeTextScrollable.setText("Add to Like");
        } else {
            // 未收藏状态：显示movie_ic_like图标，表示点击会收藏
            binding.imageLikeIconScrollable.setImageResource(R.drawable.movie_ic_like);
            binding.textLikeTextScrollable.setText("Add to Like");
        }
    }

    /**
     * 切换概要展开状态
     */
    private void toggleSynopsisExpanded() {
        videoModel.toggleSynopsisExpanded();
        updateSynopsisDisplay();
    }

    /**
     * 更新概要显示
     */
    private void updateSynopsisDisplay() {
        if (videoModel.isSynopsisExpanded()) {
            binding.textSynopsisContent.setMaxLines(Integer.MAX_VALUE);
        } else {
            binding.textSynopsisContent.setMaxLines(3);
        }
    }



    /**
     * 剧集点击事件
     */
    private void onEpisodeClick(EpisodeModel episode, int position) {
        // 计算全局索引：当前区间起始索引 + 区间内相对位置
        int globalIndex = videoModel.getSelectedEpisodeRange() * 30 + position;

        // 更新选中的剧集（使用全局索引）
        videoModel.setSelectedEpisodeIndex(globalIndex);

        // 跳转到视频播放器并播放对应剧集
        navigateToVideoPlayerWithEpisode(globalIndex);
    }

    /**
     * 推荐视频点击事件
     */
    private void onRecommendVideoClick(VideoModel video, int position) {
        // 优先使用filmLanguageInfoId跳转（新的API模式）
        if (video.getFilmLanguageInfoId() != null && !video.getFilmLanguageInfoId().isEmpty()) {
            Intent intent = new Intent(this, VideoDetailActivity.class);
            intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
            intent.putExtra("filmTitle", video.getTitle());
            startActivity(intent);

            android.util.Log.d("VideoDetailActivity", "推荐视频跳转到详情页: " + video.getTitle() +
                  ", filmLanguageInfoId: " + video.getFilmLanguageInfoId());
        } else {
            // 如果没有filmLanguageInfoId，使用传统的video_model模式（回退方案）
            Intent intent = new Intent(this, VideoDetailActivity.class);
            intent.putExtra("video_model", video);
            startActivity(intent);

            android.util.Log.d("VideoDetailActivity", "推荐视频使用传统模式跳转: " + video.getTitle());
        }
    }

    /**
     * 格式化观看次数
     */
    private String formatViewCount(long viewCount) {
        if (viewCount >= 1000000) {
            return String.format("%.1fM", viewCount / 1000000.0);
        } else if (viewCount >= 1000) {
            return String.format("%.1fK", viewCount / 1000.0);
        } else {
            return String.valueOf(viewCount);
        }
    }





    @Override
    protected void onApplyNavigationBarInsets(WindowInsetsCompat insets, int navigationBarHeight,
                                            boolean hasNavigationBar, boolean isGestureNavigation) {
        super.onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

        // 根据导航栏情况调整底部内容
        if (hasNavigationBar && !isGestureNavigation) {
            // 按钮导航栏需要为底部内容添加内边距
            binding.nestedScrollView.setPadding(0, 0, 0, navigationBarHeight);
        } else if (hasNavigationBar && isGestureNavigation) {
            // 手势导航栏只需要小的内边距
            binding.nestedScrollView.setPadding(0, 0, 0, navigationBarHeight / 3);
        }

        // 处理Continue Playing按钮的导航栏适配
        adaptContinuePlayingButtonForNavigationBar(hasNavigationBar, isGestureNavigation, navigationBarHeight);
    }

    /**
     * 验证视频数据完整性
     */
    private void validateVideoData() {
        if (videoModel == null) {
            android.util.Log.e("VideoDetailActivity", "VideoModel为null，无法验证数据");
            return;
        }

        TestValidationUtils.TestResult result = TestValidationUtils.validateVideoModelData(videoModel);

        if (result.success) {
            android.util.Log.i("VideoDetailActivity", "视频数据验证通过 - " + videoModel.getTitle());
        } else {
            android.util.Log.w("VideoDetailActivity", "视频数据验证失败 - " + result.message);
            android.util.Log.w("VideoDetailActivity", "详情: " + result.details);
        }

        // 验证UI渲染状态
        validateUIRendering();
    }

    /**
     * 验证UI渲染状态
     */
    private void validateUIRendering() {
        // 延迟执行，确保UI已渲染
        binding.getRoot().post(() -> {
            StringBuilder renderingStatus = new StringBuilder();
            renderingStatus.append("UI渲染状态检查:\n");

            // 检查概要内容
            String synopsisText = binding.textSynopsisContent.getText().toString();
            renderingStatus.append("概要内容: ").append(synopsisText.isEmpty() ? "空" : "已渲染").append("\n");

            // 检查演员列表
            int actorCount = actorAdapter.getItemCount();
            renderingStatus.append("演员数量: ").append(actorCount).append("\n");

            // 检查导演列表
            int directorCount = directorAdapter.getItemCount();
            renderingStatus.append("导演数量: ").append(directorCount).append("\n");

            // 检查标签列表
            int tagCount = synopsisTagAdapter.getItemCount();
            renderingStatus.append("标签数量: ").append(tagCount).append("\n");

            // 检查剧集列表
            int episodeCount = episodeAdapter.getItemCount();
            renderingStatus.append("剧集数量: ").append(episodeCount).append("\n");

            // 检查剧集区间文本
            String episodeRangeText = videoModel.getEpisodeRangeText();
            renderingStatus.append("剧集区间: ").append(episodeRangeText).append("\n");

            android.util.Log.i("VideoDetailActivity", renderingStatus.toString());
        });
    }

    /**
     * Continue Playing按钮点击事件
     */
    private void onContinuePlayingClick() {
        if (videoModel == null) {
            android.util.Log.w("VideoDetailActivity", "VideoModel is null, cannot start playing");
            return;
        }

        // 跳转到VideoPlayerActivity进行播放
        navigateToVideoPlayer();

        android.util.Log.d("VideoDetailActivity", "Continue Playing clicked - navigating to VideoPlayerActivity");
    }

    /**
     * 跳转到视频播放页面
     */
    private void navigateToVideoPlayer() {
        // 创建视频列表，将当前视频作为第一个
        List<VideoModel> videoList = new ArrayList<>();
        videoList.add(videoModel);

        // 添加推荐视频到播放列表
        if (recommendVideoAdapter != null) {
            List<VideoModel> recommendVideos = recommendVideoAdapter.getVideoList();
            for (VideoModel video : recommendVideos) {
                if (!video.getId().equals(videoModel.getId())) {
                    videoList.add(video);
                }
            }
        }

        // 启动VideoPlayerActivity
        Intent intent = new Intent(this, VideoPlayerActivity.class);
        intent.putExtra(VideoPlayerActivity.EXTRA_VIDEO_LIST, (ArrayList<VideoModel>) videoList);
        intent.putExtra(VideoPlayerActivity.EXTRA_CURRENT_INDEX, 0);

        // 如果有选中的剧集，传递剧集信息
        if (videoModel.hasMultipleEpisodes()) {
            intent.putExtra(VideoPlayerActivity.EXTRA_SELECTED_EPISODE, videoModel.getSelectedEpisodeIndex());
        }

        startActivity(intent);
    }

    /**
     * 跳转到视频播放器并播放指定剧集
     */
    private void navigateToVideoPlayerWithEpisode(int episodeIndex) {
        if (videoModel == null) {
            android.util.Log.w("VideoDetailActivity", "VideoModel is null, cannot start playing episode");
            return;
        }

        // 创建视频列表（只包含当前视频）
        List<VideoModel> videoList = new ArrayList<>();
        videoList.add(videoModel);

        // 启动VideoPlayerActivity
        Intent intent = new Intent(this, VideoPlayerActivity.class);
        intent.putExtra(VideoPlayerActivity.EXTRA_VIDEO_LIST, (ArrayList<VideoModel>) videoList);
        intent.putExtra(VideoPlayerActivity.EXTRA_CURRENT_INDEX, 0);
        intent.putExtra(VideoPlayerActivity.EXTRA_SELECTED_EPISODE, episodeIndex);

        startActivity(intent);

        android.util.Log.d("VideoDetailActivity", "Navigating to VideoPlayerActivity with episode: " + episodeIndex);
    }

    /**
     * 适配Continue Playing按钮的导航栏位置
     */
    private void adaptContinuePlayingButtonForNavigationBar(boolean hasNavigationBar,
                                                          boolean isGestureNavigation,
                                                          int navigationBarHeight) {
        if (continuePlayingButton == null) return;

        // 获取导航栏信息
        NavigationBarUtils.NavigationBarInfo navInfo = NavigationBarUtils.getNavigationBarInfo(this);

        // 获取按钮的布局参数
        ViewGroup.LayoutParams layoutParams = continuePlayingButton.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) layoutParams;

            // 获取基础margin（来自dimens.xml）
            int baseMargin = getResources().getDimensionPixelSize(R.dimen.continue_playing_button_margin_bottom);

            if (navInfo.exists) {
                if (!isGestureNavigation) {
                    // 按钮导航：基础margin + 完整导航栏高度
                    marginParams.bottomMargin = baseMargin + navInfo.height;
                    android.util.Log.d("VideoDetailActivity", "按钮导航适配 - bottomMargin: " + marginParams.bottomMargin + "px");
                } else {
                    // 手势导航：基础margin + 导航栏高度的一半，最小48px
                    int gestureNavMargin = Math.max(navInfo.height / 2, 48);
                    marginParams.bottomMargin = baseMargin + gestureNavMargin;
                    android.util.Log.d("VideoDetailActivity", "手势导航适配 - bottomMargin: " + marginParams.bottomMargin + "px");
                }
            } else {
                // 无导航栏：只使用基础margin
                marginParams.bottomMargin = baseMargin;
                android.util.Log.d("VideoDetailActivity", "无导航栏 - bottomMargin: " + marginParams.bottomMargin + "px");
            }

            continuePlayingButton.setLayoutParams(marginParams);
        }
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        if (swipeRefreshLoading != null) {
            swipeRefreshLoading.setVisibility(View.VISIBLE);
            swipeRefreshLoading.setRefreshing(true);
        }

        // 隐藏内容区域
        if (binding.nestedScrollView != null) {
            binding.nestedScrollView.setVisibility(View.GONE);
        }

        android.util.Log.d("VideoDetailActivity", "显示加载状态");
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoadingState() {
        if (swipeRefreshLoading != null) {
            swipeRefreshLoading.setVisibility(View.GONE);
            swipeRefreshLoading.setRefreshing(false);
        }

        // 显示内容区域
        if (binding.nestedScrollView != null) {
            binding.nestedScrollView.setVisibility(View.VISIBLE);
        }

        android.util.Log.d("VideoDetailActivity", "隐藏加载状态");
    }

    /**
     * 通过API加载视频详情数据
     */
    private void loadVideoDetailFromApi() {
        if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
            android.util.Log.e("VideoDetailActivity", "filmLanguageInfoId为空，无法加载视频详情");
            // 显示错误状态，不再使用测试数据
            handleApiError("视频ID为空，无法加载视频详情");
            return;
        }

        // 显示加载状态
        showLoadingState();

        android.util.Log.d("VideoDetailActivity", "开始加载视频详情和章节列表，filmLanguageInfoId: " + filmLanguageInfoId);

        // 先加载视频详情（包含演员、导演信息）
        videoDetailApiService.getVideoDetail(filmLanguageInfoId, new VideoDetailApiService.ApiCallback<VideoDetailResponseModel>() {
            @Override
            public void onSuccess(VideoDetailResponseModel detailResponse) {
                android.util.Log.d("VideoDetailActivity", "视频详情加载成功，开始加载章节列表");

                // 视频详情加载成功后，再加载章节列表
                chapterListApiService.getChapterList(filmLanguageInfoId, new ChapterListApiService.ApiCallback<ChapterListResponseModel>() {
                    @Override
                    public void onSuccess(ChapterListResponseModel chapterResponse) {
                        android.util.Log.d("VideoDetailActivity", "章节列表加载成功，开始合并数据");

                        // 在主线程中合并数据并更新UI
                        runOnUiThread(() -> {
                            try {
                                // 检查Activity是否还存在
                                if (isFinishing() || isDestroyed()) {
                                    android.util.Log.w("VideoDetailActivity", "Activity已销毁，跳过UI更新");
                                    return;
                                }

                                // 合并视频详情和章节列表数据
                                videoModel = mergeVideoDetailAndChapterData(detailResponse, chapterResponse);

                                if (videoModel != null) {
                                    // 隐藏加载状态
                                    hideLoadingState();

                                    // 重新加载视频数据到UI
                                    loadVideoData();

                                    // 验证数据
                                    validateVideoData();

                                    android.util.Log.d("VideoDetailActivity", "视频详情UI更新完成");
                                } else {
                                    android.util.Log.e("VideoDetailActivity", "合并后的VideoModel为空");
                                    handleApiError("视频数据合并失败");
                                }

                            } catch (Exception e) {
                                android.util.Log.e("VideoDetailActivity", "处理API响应时出错", e);
                                handleApiError("数据处理失败: " + e.getMessage());
                            }
                        });
                    }

                    @Override
                    public void onError(String error) {
                        android.util.Log.e("VideoDetailActivity", "章节列表加载失败: " + error);
                        handleApiError("章节列表加载失败: " + error);
                    }
                });
            }

            @Override
            public void onError(String error) {
                android.util.Log.e("VideoDetailActivity", "视频详情加载失败: " + error);
                handleApiError("视频详情加载失败: " + error);
            }
        });
    }

    /**
     * 将章节列表API响应转换为VideoModel对象
     *
     * @param response 章节列表API响应数据
     * @return VideoModel对象
     */
    private VideoModel convertChapterListResponseToVideoModel(ChapterListResponseModel response) {
        try {
            if (response == null) {
                android.util.Log.e("VideoDetailActivity", "章节列表API响应为空");
                return null;
            }

            if (response.getData() == null) {
                android.util.Log.e("VideoDetailActivity", "章节列表API响应数据为空");
                return null;
            }

            ChapterListResponseModel.ChapterListData data = response.getData();
            FilmInfo filmInfo = data.getFilmVo();

            if (filmInfo == null) {
                android.util.Log.e("VideoDetailActivity", "短剧信息为空");
                return null;
            }

            // 创建VideoModel对象（确保所有参数都有默认值）
            String videoId = filmInfo.getFilmLanguageInfoId() != null && !filmInfo.getFilmLanguageInfoId().isEmpty()
                            ? filmInfo.getFilmLanguageInfoId()
                            : (filmLanguageInfoId != null ? filmLanguageInfoId : "unknown");
            String title = filmInfo.getFilmTitle() != null && !filmInfo.getFilmTitle().isEmpty()
                          ? filmInfo.getFilmTitle()
                          : (filmTitle != null && !filmTitle.isEmpty() ? filmTitle : "未知视频");
            String posterUrl = filmInfo.getCover() != null && !filmInfo.getCover().isEmpty()
                              ? filmInfo.getCover()
                              : "";
            String category = filmInfo.getCategoryName() != null && !filmInfo.getCategoryName().isEmpty()
                             ? filmInfo.getCategoryName()
                             : "未分类";

            android.util.Log.d("VideoDetailActivity", "创建VideoModel - ID: " + videoId + ", 标题: " + title);

            VideoModel videoModel = new VideoModel(videoId, title, posterUrl, category);

        // 设置filmLanguageInfoId（重要：用于API调用）
        videoModel.setFilmLanguageInfoId(filmInfo.getFilmLanguageInfoId());
        android.util.Log.d("VideoDetailActivity", "设置filmLanguageInfoId: " + filmInfo.getFilmLanguageInfoId());

        // 设置基本信息
        if (filmInfo.getTotalChaptersNum() != null && filmInfo.getTotalChaptersNum() > 0) {
            videoModel.setTotalEpisodes(filmInfo.getTotalChaptersNum());
        }

        // 设置描述信息（简化处理）
        String details = filmInfo.getDetails() != null ? filmInfo.getDetails() : "";
        videoModel.setDescription(details);
        videoModel.setSynopsis(details);

        // 设置收藏状态（简化处理）
        if (filmInfo.getIsLove() != null && filmInfo.getIsLove() == 1) {
            videoModel.setLiked(true);
        }

        // 设置播放次数
        if (filmInfo.getPlayNum() != null) {
            videoModel.setViewCount(filmInfo.getPlayNum());
        }

        // 设置标签（简化处理，章节列表API可能不包含标签信息）
        try {
            // 添加语言标签（简化处理）
            if (filmInfo.getLanguageType() != null) {
                String languageTag = filmInfo.getLanguageType() == 1 ? "英语" : "俄语";
                videoModel.addTag(languageTag);
            }

            // 添加分类标签
            if (filmInfo.getCategoryName() != null && !filmInfo.getCategoryName().isEmpty()) {
                videoModel.addTag(filmInfo.getCategoryName());
            }
        } catch (Exception e) {
            android.util.Log.w("VideoDetailActivity", "设置标签时出错: " + e.getMessage());
        }

        // 注意：章节列表API不包含演员和导演信息，这些信息需要从其他API获取
        // 这里可以添加默认的演员和导演信息，或者后续通过其他API补充

        // 设置剧集信息（简化处理，避免复杂方法调用）
        if (data.getChapterList() != null && !data.getChapterList().isEmpty()) {
            try {
                int currentEpisodeFromProgress = 1; // 默认第1集

                // 从播放进度中获取当前剧集
                if (data.getPlayProgress() != null && data.getPlayProgress().getChapterEp() != null) {
                    currentEpisodeFromProgress = data.getPlayProgress().getChapterEp();
                    android.util.Log.d("VideoDetailActivity", "从播放进度获取当前剧集: " + currentEpisodeFromProgress);
                }

                for (ChapterInfo chapter : data.getChapterList()) {
                    if (chapter != null && chapter.getChapterEp() != null) {
                        String chapterTitle = "第" + chapter.getChapterEp() + "集"; // 简化标题生成
                        EpisodeModel episode = new EpisodeModel(
                            chapter.getChapterId() != null ? chapter.getChapterId() : "",
                            chapter.getChapterEp(),
                            chapterTitle
                        );
                        // 设置播放状态和锁定信息
                        if (chapter.getIsFinished() != null && chapter.getIsFinished() == 1) {
                            episode.setWatched(true);
                        }
                        // 设置解锁状态
                        episode.setUnlock(chapter.isUnlocked());
                        // 设置付费状态
                        episode.setCharge(chapter.isCharged());
                        // 设置播放进度
                        episode.setProgress(chapter.getProgress() != null ? chapter.getProgress() : 0);
                        // 设置最后播放时间
                        episode.setLastPlayTime(chapter.getLastPlayTime());

                        // 调试日志
                        android.util.Log.d("VideoDetailActivity", "Episode " + chapter.getChapterEp() +
                            ": isCharge=" + chapter.getIsCharge() +
                            ", isUnlock=" + chapter.getIsUnlock() +
                            ", isCharged()=" + chapter.isCharged() +
                            ", isUnlocked()=" + chapter.isUnlocked() +
                            ", episode.isCharge()=" + episode.isCharge() +
                            ", episode.isUnlock()=" + episode.isUnlock());
                        videoModel.addEpisode(episode);
                    }
                }

                // 设置当前剧集
                videoModel.setCurrentEpisode(currentEpisodeFromProgress);
                android.util.Log.d("VideoDetailActivity", "设置当前剧集: " + currentEpisodeFromProgress);

            } catch (Exception e) {
                android.util.Log.w("VideoDetailActivity", "设置剧集信息时出错: " + e.getMessage());
            }
        }

            android.util.Log.d("VideoDetailActivity", "成功转换VideoModel - 标题: " + title + ", 集数: " + videoModel.getTotalEpisodes());

            return videoModel;

        } catch (Exception e) {
            android.util.Log.e("VideoDetailActivity", "转换VideoModel时发生异常", e);
            return null;
        }
    }

    /**
     * 将视频详情API响应转换为VideoModel对象
     *
     * @param response 视频详情API响应数据
     * @return VideoModel对象
     */
    private VideoModel convertVideoDetailResponseToVideoModel(VideoDetailResponseModel response) {
        try {
            if (response == null) {
                android.util.Log.e("VideoDetailActivity", "视频详情API响应为空");
                return null;
            }

            if (response.getData() == null) {
                android.util.Log.e("VideoDetailActivity", "视频详情API响应数据为空");
                return null;
            }

            VideoDetailResponseModel.VideoDetailData data = response.getData();
            FilmInfo filmInfo = data.getFilmInfo();

            if (filmInfo == null) {
                android.util.Log.e("VideoDetailActivity", "短剧信息为空");
                return null;
            }

            // 创建VideoModel对象（确保所有参数都有默认值）
            String videoId = filmInfo.getFilmLanguageInfoId() != null && !filmInfo.getFilmLanguageInfoId().isEmpty()
                            ? filmInfo.getFilmLanguageInfoId()
                            : (filmLanguageInfoId != null ? filmLanguageInfoId : "unknown");
            String title = filmInfo.getFilmTitle() != null && !filmInfo.getFilmTitle().isEmpty()
                          ? filmInfo.getFilmTitle()
                          : (filmTitle != null && !filmTitle.isEmpty() ? filmTitle : "未知视频");
            String posterUrl = filmInfo.getCover() != null && !filmInfo.getCover().isEmpty()
                              ? filmInfo.getCover()
                              : "";
            String category = filmInfo.getCategoryName() != null && !filmInfo.getCategoryName().isEmpty()
                             ? filmInfo.getCategoryName()
                             : "未分类";

            android.util.Log.d("VideoDetailActivity", "创建VideoModel - ID: " + videoId + ", 标题: " + title);

            VideoModel videoModel = new VideoModel(videoId, title, posterUrl, category);

        // 设置filmLanguageInfoId（重要：用于API调用）
        videoModel.setFilmLanguageInfoId(filmInfo.getFilmLanguageInfoId());
        android.util.Log.d("VideoDetailActivity", "设置filmLanguageInfoId: " + filmInfo.getFilmLanguageInfoId());

        // 设置基本信息
        if (filmInfo.getTotalChaptersNum() != null && filmInfo.getTotalChaptersNum() > 0) {
            videoModel.setTotalEpisodes(filmInfo.getTotalChaptersNum());
        }

        // 设置描述信息（使用API返回的details）
        String details = filmInfo.getDetails() != null ? filmInfo.getDetails() : "";
        videoModel.setDescription(details);

        // 设置synopsis（使用API返回的details作为简介内容）
        String synopsis = details; // Synopsis显示视频简介
        videoModel.setSynopsis(synopsis);
        android.util.Log.d("VideoDetailActivity", "设置synopsis为details: " + synopsis);

        // 设置收藏状态（简化处理）
        if (filmInfo.getIsLove() != null && filmInfo.getIsLove() == 1) {
            videoModel.setLiked(true);
        }

        // 设置订阅状态
        if (data.getSubscribeStatus() != null && data.getSubscribeStatus() == 1) {
            videoModel.setSubscribed(true);
            android.util.Log.d("VideoDetailActivity", "设置订阅状态: 已订阅");
        } else {
            videoModel.setSubscribed(false);
            android.util.Log.d("VideoDetailActivity", "设置订阅状态: 未订阅");
        }

        // 设置播放次数
        if (filmInfo.getPlayNum() != null) {
            videoModel.setViewCount(filmInfo.getPlayNum());
        }

        // 设置发布时间
        if (data.getReleaseTime() != null && !data.getReleaseTime().trim().isEmpty()) {
            videoModel.setReleaseDate(data.getReleaseTime());
            android.util.Log.d("VideoDetailActivity", "设置发布时间: " + data.getReleaseTime());
        }

        // 设置标签（只使用API返回的labelNames）
        try {
            // 只使用API返回的labelNames，如果没有就不显示标签
            if (data.getLabelNames() != null && !data.getLabelNames().trim().isEmpty()) {
                String[] labels = data.getLabelNames().split(",");
                for (String label : labels) {
                    String trimmedLabel = label.trim();
                    if (!trimmedLabel.isEmpty()) {
                        videoModel.addTag(trimmedLabel);
                    }
                }
                android.util.Log.d("VideoDetailActivity", "设置API标签: " + data.getLabelNames());
            } else {
                android.util.Log.d("VideoDetailActivity", "API未返回标签，不显示标签");
            }
        } catch (Exception e) {
            android.util.Log.w("VideoDetailActivity", "设置标签时出错: " + e.getMessage());
        }

        // 设置演员和导演信息（如果API返回了这些信息）
        if (data.getPerformerInfo() != null && !data.getPerformerInfo().isEmpty()) {
            try {
                for (PerformerInfo performer : data.getPerformerInfo()) {
                    if (performer != null && performer.getPerformerName() != null) {
                        ActorModel actor = new ActorModel(
                            performer.getPerformerName(),
                            performer.getPerformerName(),
                            performer.getPerformerImg()
                        );
                        videoModel.addActor(actor);
                    }
                }
                android.util.Log.d("VideoDetailActivity", "设置演员信息: " + data.getPerformerInfo().size() + " 个演员");
            } catch (Exception e) {
                android.util.Log.w("VideoDetailActivity", "设置演员信息时出错: " + e.getMessage());
            }
        } else {
            android.util.Log.d("VideoDetailActivity", "API未返回演员信息");
        }

        if (data.getDirectorInfo() != null && !data.getDirectorInfo().isEmpty()) {
            try {
                for (DirectorInfo director : data.getDirectorInfo()) {
                    if (director != null && director.getDirectorName() != null) {
                        DirectorModel directorModel = new DirectorModel(
                            director.getDirectorName(),
                            director.getDirectorName(),
                            director.getDirectorImg()
                        );
                        videoModel.addDirector(directorModel);
                    }
                }
                android.util.Log.d("VideoDetailActivity", "设置导演信息: " + data.getDirectorInfo().size() + " 个导演");
            } catch (Exception e) {
                android.util.Log.w("VideoDetailActivity", "设置导演信息时出错: " + e.getMessage());
            }
        } else {
            android.util.Log.d("VideoDetailActivity", "API未返回导演信息");
        }

        // 设置剧集信息
        if (data.getChapterList() != null && !data.getChapterList().isEmpty()) {
            try {
                int currentEpisodeFromProgress = 1; // 默认第1集

                // 从播放进度中获取当前剧集
                if (data.getPlayProgressVO() != null && data.getPlayProgressVO().getChapterEp() != null) {
                    currentEpisodeFromProgress = data.getPlayProgressVO().getChapterEp();
                    android.util.Log.d("VideoDetailActivity", "从播放进度获取当前剧集: " + currentEpisodeFromProgress);
                }

                for (ChapterInfo chapter : data.getChapterList()) {
                    if (chapter != null && chapter.getChapterEp() != null) {
                        String chapterTitle = "第" + chapter.getChapterEp() + "集"; // 简化标题生成
                        EpisodeModel episode = new EpisodeModel(
                            chapter.getChapterId() != null ? chapter.getChapterId() : "",
                            chapter.getChapterEp(),
                            chapterTitle
                        );
                        // 设置播放状态和锁定信息
                        if (chapter.getIsFinished() != null && chapter.getIsFinished() == 1) {
                            episode.setWatched(true);
                        }
                        // 设置解锁状态
                        episode.setUnlock(chapter.isUnlocked());
                        // 设置付费状态
                        episode.setCharge(chapter.isCharged());
                        // 设置播放进度
                        episode.setProgress(chapter.getProgress() != null ? chapter.getProgress() : 0);
                        // 设置最后播放时间
                        episode.setLastPlayTime(chapter.getLastPlayTime());
                        videoModel.addEpisode(episode);
                    }
                }

                // 设置当前剧集
                videoModel.setCurrentEpisode(currentEpisodeFromProgress);
                android.util.Log.d("VideoDetailActivity", "设置当前剧集: " + currentEpisodeFromProgress);

            } catch (Exception e) {
                android.util.Log.w("VideoDetailActivity", "设置剧集信息时出错: " + e.getMessage());
            }
        }

        // 设置推荐视频（使用API返回的similarFilmList）
        if (data.getSimilarFilmList() != null && !data.getSimilarFilmList().isEmpty()) {
            try {
                List<VideoModel> recommendVideos = new ArrayList<>();
                for (FilmInfo similarFilm : data.getSimilarFilmList()) {
                    if (similarFilm != null && similarFilm.getFilmTitle() != null) {
                        VideoModel recommendVideo = new VideoModel(
                            similarFilm.getFilmLanguageInfoId() != null ? similarFilm.getFilmLanguageInfoId() : "",
                            similarFilm.getFilmTitle(),
                            similarFilm.getCover() != null ? similarFilm.getCover() : "",
                            similarFilm.getCategoryName() != null ? similarFilm.getCategoryName() : ""
                        );

                        // 设置基本信息
                        recommendVideo.setFilmLanguageInfoId(similarFilm.getFilmLanguageInfoId());
                        if (similarFilm.getDetails() != null) {
                            recommendVideo.setDescription(similarFilm.getDetails());
                        }
                        if (similarFilm.getPlayNum() != null) {
                            recommendVideo.setViewCount(similarFilm.getPlayNum());
                        }
                        if (similarFilm.getTotalChaptersNum() != null) {
                            recommendVideo.setTotalEpisodes(similarFilm.getTotalChaptersNum());
                        }
                        if (similarFilm.getIsLove() != null && similarFilm.getIsLove() == 1) {
                            recommendVideo.setLiked(true);
                        }

                        recommendVideos.add(recommendVideo);
                    }
                }

                // 将推荐视频设置到VideoModel中（如果VideoModel有相关方法）
                // 这里可能需要根据VideoModel的实际结构来调整
                android.util.Log.d("VideoDetailActivity", "设置推荐视频: " + recommendVideos.size() + " 个推荐");

                // 注意：这里需要将推荐视频传递给推荐视频适配器
                // 可以通过全局变量或其他方式传递给UI更新方法
                this.recommendVideoList = recommendVideos;

            } catch (Exception e) {
                android.util.Log.w("VideoDetailActivity", "设置推荐视频时出错: " + e.getMessage());
            }
        }

            android.util.Log.d("VideoDetailActivity", "成功转换VideoModel - 标题: " + title + ", 集数: " + videoModel.getTotalEpisodes());

            return videoModel;

        } catch (Exception e) {
            android.util.Log.e("VideoDetailActivity", "转换VideoModel时发生异常", e);
            return null;
        }
    }

    /**
     * 合并视频详情和章节列表数据
     *
     * @param detailResponse 视频详情API响应
     * @param chapterResponse 章节列表API响应
     * @return 合并后的VideoModel对象
     */
    private VideoModel mergeVideoDetailAndChapterData(VideoDetailResponseModel detailResponse, ChapterListResponseModel chapterResponse) {
        try {
            // 首先使用章节列表数据创建基础VideoModel
            VideoModel videoModel = convertChapterListResponseToVideoModel(chapterResponse);

            if (videoModel == null) {
                android.util.Log.e("VideoDetailActivity", "章节列表数据转换失败");
                return null;
            }

            // 然后从视频详情中补充完整信息
            if (detailResponse != null && detailResponse.getData() != null) {
                VideoDetailResponseModel.VideoDetailData detailData = detailResponse.getData();

                // 更新Synopsis（使用视频详情API返回的labelNames）
                if (detailData.getLabelNames() != null && !detailData.getLabelNames().trim().isEmpty()) {
                    String synopsis = detailData.getLabelNames().trim();
                    // 如果标签以逗号结尾，去掉最后的逗号
                    if (synopsis.endsWith(",")) {
                        synopsis = synopsis.substring(0, synopsis.length() - 1).trim();
                    }
                    videoModel.setSynopsis(synopsis);
                    android.util.Log.d("VideoDetailActivity", "从视频详情更新synopsis为labelNames: " + synopsis);
                }

                // 更新Tags（使用视频详情API返回的labelNames）
                if (detailData.getLabelNames() != null && !detailData.getLabelNames().trim().isEmpty()) {
                    try {
                        // 创建新的标签列表
                        List<String> newTags = new ArrayList<>();

                        // 解析标签字符串（假设以逗号分隔）
                        String[] tags = detailData.getLabelNames().split(",");
                        for (String tag : tags) {
                            if (tag != null && !tag.trim().isEmpty()) {
                                newTags.add(tag.trim());
                            }
                        }

                        // 设置新的标签列表
                        videoModel.setTags(newTags);
                        android.util.Log.d("VideoDetailActivity", "从视频详情更新tags: " + detailData.getLabelNames());
                    } catch (Exception e) {
                        android.util.Log.w("VideoDetailActivity", "更新标签时出错: " + e.getMessage());
                    }
                }

                // 更新推荐视频（More Like This）
                if (detailData.getSimilarFilmList() != null && !detailData.getSimilarFilmList().isEmpty()) {
                    try {
                        // 清空现有推荐视频列表
                        if (recommendVideoList == null) {
                            recommendVideoList = new ArrayList<>();
                        } else {
                            recommendVideoList.clear();
                        }

                        // 转换推荐视频数据
                        for (FilmInfo similarFilm : detailData.getSimilarFilmList()) {
                            if (similarFilm != null) {
                                VideoModel recommendVideo = new VideoModel(
                                    similarFilm.getFilmLanguageInfoId() != null ? similarFilm.getFilmLanguageInfoId() : "",
                                    similarFilm.getFilmTitle() != null ? similarFilm.getFilmTitle() : "未知视频",
                                    similarFilm.getCover() != null ? similarFilm.getCover() : "",
                                    similarFilm.getCategoryName() != null ? similarFilm.getCategoryName() : "未分类"
                                );

                                // 设置基本信息
                                recommendVideo.setFilmLanguageInfoId(similarFilm.getFilmLanguageInfoId());
                                if (similarFilm.getTotalChaptersNum() != null) {
                                    recommendVideo.setTotalEpisodes(similarFilm.getTotalChaptersNum());
                                }
                                if (similarFilm.getPlayNum() != null) {
                                    recommendVideo.setViewCount(similarFilm.getPlayNum());
                                }

                                recommendVideoList.add(recommendVideo);
                            }
                        }

                        android.util.Log.d("VideoDetailActivity", "从视频详情添加推荐视频: " + detailData.getSimilarFilmList().size() + " 个推荐");
                    } catch (Exception e) {
                        android.util.Log.w("VideoDetailActivity", "添加推荐视频时出错: " + e.getMessage());
                    }
                }

                // 添加演员信息
                if (detailData.getPerformerInfo() != null && !detailData.getPerformerInfo().isEmpty()) {
                    try {
                        for (PerformerInfo performer : detailData.getPerformerInfo()) {
                            if (performer != null && performer.getPerformerName() != null) {
                                ActorModel actor = new ActorModel(
                                    performer.getPerformerName(),
                                    performer.getPerformerName(),
                                    performer.getPerformerImg()
                                );
                                videoModel.addActor(actor);
                            }
                        }
                        android.util.Log.d("VideoDetailActivity", "从视频详情添加演员信息: " + detailData.getPerformerInfo().size() + " 个演员");
                    } catch (Exception e) {
                        android.util.Log.w("VideoDetailActivity", "添加演员信息时出错: " + e.getMessage());
                    }
                }

                // 添加导演信息
                if (detailData.getDirectorInfo() != null && !detailData.getDirectorInfo().isEmpty()) {
                    try {
                        for (DirectorInfo director : detailData.getDirectorInfo()) {
                            if (director != null && director.getDirectorName() != null) {
                                DirectorModel directorModel = new DirectorModel(
                                    director.getDirectorName(),
                                    director.getDirectorName(),
                                    director.getDirectorImg()
                                );
                                videoModel.addDirector(directorModel);
                            }
                        }
                        android.util.Log.d("VideoDetailActivity", "从视频详情添加导演信息: " + detailData.getDirectorInfo().size() + " 个导演");
                    } catch (Exception e) {
                        android.util.Log.w("VideoDetailActivity", "添加导演信息时出错: " + e.getMessage());
                    }
                }
            }

            return videoModel;

        } catch (Exception e) {
            android.util.Log.e("VideoDetailActivity", "合并视频数据时出错", e);
            return null;
        }
    }

    /**
     * 处理API错误
     *
     * @param error 错误信息
     */
    private void handleApiError(String error) {
        android.util.Log.e("VideoDetailActivity", "处理API错误: " + error);

        // 隐藏加载状态
        hideLoadingState();

        // 显示错误提示给用户
        Toast.makeText(this, "加载视频详情失败: " + error, Toast.LENGTH_LONG).show();

        // 返回上一页
        finish();
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 取消所有API请求
        if (videoDetailApiService != null) {
            videoDetailApiService.cancelAllRequests();
        }
        if (chapterListApiService != null) {
            chapterListApiService.cancelAllRequests();
        }
    }
}
