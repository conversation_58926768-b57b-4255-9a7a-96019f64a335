<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 主背景层 -->
    <item>
        <shape android:shape="rectangle">
            <!-- VIP卡片背景颜色 - 使用渐变色 -->
            <gradient
                android:startColor="#FFE4B5"
                android:endColor="#DEB887"
                android:angle="135"
                android:type="linear" />

            <!-- 圆角 - 使用响应式尺寸 -->
            <corners android:radius="@dimen/profile_vip_card_corner_radius" />
        </shape>
    </item>

    <!-- 可选的边框层 -->
    <item>
        <shape android:shape="rectangle">
            <stroke
                android:width="@dimen/profile_vip_card_stroke_width"
                android:color="#40000000" />
            <corners android:radius="@dimen/profile_vip_card_corner_radius" />
        </shape>
    </item>

</layer-list>
