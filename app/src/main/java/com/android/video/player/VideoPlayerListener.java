package com.android.video.player;

/**
 * 视频播放器状态监听接口
 * 监听播放器的各种状态变化
 * <AUTHOR>
 */
public interface VideoPlayerListener {
    
    /**
     * 播放器准备完成
     */
    void onPrepared();
    
    /**
     * 播放开始
     */
    void onPlaybackStarted();
    
    /**
     * 播放暂停
     */
    void onPlaybackPaused();
    
    /**
     * 播放停止
     */
    void onPlaybackStopped();
    
    /**
     * 播放完成
     */
    void onPlaybackCompleted();
    
    /**
     * 播放位置变化
     * @param position 当前位置（毫秒）
     * @param duration 总时长（毫秒）
     */
    void onPositionChanged(long position, long duration);
    
    /**
     * 缓冲状态变化
     * @param isBuffering 是否正在缓冲
     * @param bufferedPercentage 缓冲百分比
     */
    void onBufferingUpdate(boolean isBuffering, int bufferedPercentage);
    
    /**
     * 播放速度变化
     * @param speed 新的播放速度
     */
    void onPlaybackSpeedChanged(float speed);
    
    /**
     * 视频尺寸变化
     * @param width 视频宽度
     * @param height 视频高度
     */
    void onVideoSizeChanged(int width, int height);
    
    /**
     * 播放错误
     * @param error 错误信息
     */
    void onError(String error);
    
    /**
     * 播放器状态变化
     * @param playWhenReady 是否准备播放
     * @param playbackState 播放状态
     */
    void onPlayerStateChanged(boolean playWhenReady, int playbackState);
    
    /**
     * 音量变化
     * @param volume 音量值
     */
    void onVolumeChanged(float volume);
}
