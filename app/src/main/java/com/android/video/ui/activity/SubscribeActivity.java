package com.android.video.ui.activity;

import android.graphics.LinearGradient;
import android.graphics.Paint;
import android.graphics.Shader;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.content.ContextCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.model.VipConfigModel;
import com.android.video.model.response.VipConfigListResponseModel;
import com.android.video.model.response.RedeemCodeResponseModel;
import com.android.video.model.VipPurchaseResponse;
import com.android.video.constants.VipApiConstantsUtils;
import com.android.video.network.VipConfigApiService;
import com.android.video.payment.PaymentManager;
import com.android.video.ui.dialog.RedemptionCodeDialog;
import com.android.video.ui.dialog.PaymentMethodDialog;

import java.util.ArrayList;
import java.util.List;

/**
 * 订阅页面Activity - 显示VIP订阅选项和特权
 * <AUTHOR>
 */
public class SubscribeActivity extends BaseFullScreenActivity {

    private static final String TAG = "SubscribeActivity";

    private TextView btnSubscribe;
    private ImageView btnBack;
    private View btnRedeemCode;
    private LinearLayout vipCardsContainer;
    private SwipeRefreshLayout swipeRefreshLoading;

    // VIP配置相关
    private VipConfigApiService vipConfigApiService;
    private List<VipConfigModel> vipConfigs;
    private List<View> vipCardViews;
    private int selectedPlan = 0;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        try {
            Log.d(TAG, "onCreate: starting SubscribeActivity");
            setContentView(R.layout.activity_subscribe);
            Log.d(TAG, "onCreate: layout set successfully");

            initViews();
            Log.d(TAG, "onCreate: views initialized");

            setupClickListeners();
            Log.d(TAG, "onCreate: click listeners set up");

            initVipConfigService();
            Log.d(TAG, "onCreate: VIP config service initialized");

            // 延迟加载VIP配置，避免在onCreate中进行网络请求
            runOnUiThread(() -> {
                try {
                    loadVipConfigs();
                } catch (Exception e) {
                    Log.e(TAG, "onCreate: error loading VIP configs", e);
                    Toast.makeText(SubscribeActivity.this, "Failed to load VIP plans", Toast.LENGTH_SHORT).show();
                }
            });

            Log.d(TAG, "onCreate: SubscribeActivity created successfully");
        } catch (Exception e) {
            Log.e(TAG, "onCreate: critical error", e);
            Toast.makeText(this, "Error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            e.printStackTrace();
            finish();
        }
    }

    private void initViews() {
        try {
            // 导航栏
            btnBack = findViewById(R.id.btn_back);
            btnRedeemCode = findViewById(R.id.layout_redeem_code);

            // VIP卡片容器
            vipCardsContainer = findViewById(R.id.vip_cards_container);

            // Loading容器
            swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);

            // 订阅按钮
            btnSubscribe = findViewById(R.id.btn_subscribe);

            // 初始化VIP卡片列表
            vipConfigs = new ArrayList<>();
            vipCardViews = new ArrayList<>();

            // 检查关键View是否为null
            if (vipCardsContainer == null || swipeRefreshLoading == null) {
                Toast.makeText(this, "Required views not found", Toast.LENGTH_SHORT).show();
                return;
            }

            // 设置SwipeRefreshLayout
            setupSwipeRefresh();

            // 设置Subscribe标题渐变颜色
            TextView subscribeTitle = findViewById(R.id.tv_subscribe_title);
            if (subscribeTitle != null) {
                subscribeTitle.post(() -> {
                    int width = subscribeTitle.getWidth();
                    if (width > 0) {
                        LinearGradient gradient = new LinearGradient(
                            0, 0, width, 0,
                            new int[]{
                                0xFFFFDF96, // #FFDF96 开始颜色
                                0xFFFFF8E6  // #FFF8E6 结束颜色
                            },
                            null,
                            Shader.TileMode.CLAMP
                        );
                        subscribeTitle.getPaint().setShader(gradient);
                    }
                });
            }

        } catch (Exception e) {
            Toast.makeText(this, "initViews error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            e.printStackTrace();
        }
    }

    private void setupClickListeners() {
        try {
            if (btnBack != null) {
                btnBack.setOnClickListener(v -> finish());
            }

            if (btnRedeemCode != null) {
                btnRedeemCode.setOnClickListener(v -> showRedemptionCodeDialog());
            }

            if (btnSubscribe != null) {
                btnSubscribe.setOnClickListener(v -> handleSubscribe());
            }
        } catch (Exception e) {
            Toast.makeText(this, "setupClickListeners error: " + e.getMessage(), Toast.LENGTH_LONG).show();
            e.printStackTrace();
        }
    }

    /**
     * 初始化VIP配置服务
     */
    private void initVipConfigService() {
        try {
            Log.d(TAG, "initVipConfigService: initializing VIP config service");
            vipConfigApiService = new VipConfigApiService();
            Log.d(TAG, "initVipConfigService: VIP config service initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "initVipConfigService: error initializing service", e);
            Toast.makeText(this, "Failed to initialize VIP service: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        if (swipeRefreshLoading != null) {
            // 设置刷新颜色
            swipeRefreshLoading.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );

            // 禁用下拉刷新手势，只用于显示加载状态
            swipeRefreshLoading.setEnabled(false);
        }
    }

    /**
     * 显示加载状态
     */
    private void showLoading() {
        try {
            Log.d(TAG, "showLoading: showing loading state");
            if (swipeRefreshLoading != null) {
                swipeRefreshLoading.setVisibility(View.VISIBLE);
                swipeRefreshLoading.setRefreshing(true);
                Log.d(TAG, "showLoading: swipe refresh loading visible");
            } else {
                Log.w(TAG, "showLoading: swipe refresh loading is null");
            }
            if (vipCardsContainer != null) {
                vipCardsContainer.setVisibility(View.GONE);
                Log.d(TAG, "showLoading: vip cards container hidden");
            } else {
                Log.w(TAG, "showLoading: vip cards container is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "showLoading: error showing loading", e);
        }
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoading() {
        try {
            Log.d(TAG, "hideLoading: hiding loading state");
            if (swipeRefreshLoading != null) {
                swipeRefreshLoading.setRefreshing(false);
                swipeRefreshLoading.setVisibility(View.GONE);
                Log.d(TAG, "hideLoading: swipe refresh loading hidden");
            } else {
                Log.w(TAG, "hideLoading: swipe refresh loading is null");
            }
            if (vipCardsContainer != null) {
                vipCardsContainer.setVisibility(View.VISIBLE);
                Log.d(TAG, "hideLoading: vip cards container visible");
            } else {
                Log.w(TAG, "hideLoading: vip cards container is null");
            }
        } catch (Exception e) {
            Log.e(TAG, "hideLoading: error hiding loading", e);
        }
    }

    /**
     * 加载VIP配置列表
     */
    private void loadVipConfigs() {
        try {
            Log.d(TAG, "loadVipConfigs: starting to load VIP configs");

            if (vipConfigApiService == null) {
                Log.e(TAG, "loadVipConfigs: vipConfigApiService is null");
                Toast.makeText(this, "Service not initialized", Toast.LENGTH_SHORT).show();
                return;
            }

            // 显示加载状态
            showLoading();
        } catch (Exception e) {
            Log.e(TAG, "loadVipConfigs: error in initial setup", e);
            Toast.makeText(this, "Error loading VIP configs: " + e.getMessage(), Toast.LENGTH_LONG).show();
            return;
        }

        vipConfigApiService.getVipConfigList(new VipConfigApiService.VipConfigListCallback() {
            @Override
            public void onSuccess(VipConfigListResponseModel response) {
                // 隐藏加载状态
                hideLoading();

                Log.d(TAG, "loadVipConfigs: success, got " + response.getConfigCount() + " configs");

                if (response.hasValidData()) {
                    vipConfigs = response.getValidConfigs();
                    createVipCards();
                    selectPlan(0); // 默认选择第一个
                } else {
                    Log.w(TAG, "loadVipConfigs: no valid VIP configs found");
                    Toast.makeText(SubscribeActivity.this, "No VIP plans available", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onError(String errorMessage) {
                // 隐藏加载状态
                hideLoading();

                Log.e(TAG, "loadVipConfigs: error - " + errorMessage);
                Toast.makeText(SubscribeActivity.this, "Failed to load VIP plans: " + errorMessage, Toast.LENGTH_SHORT).show();

                // 显示默认的VIP卡片或错误状态
                showErrorState();
            }
        });
    }

    /**
     * 显示错误状态
     */
    private void showErrorState() {
        try {
            Log.d(TAG, "showErrorState: showing error state");

            if (vipCardsContainer != null) {
                vipCardsContainer.removeAllViews();

                // 添加错误提示
                TextView errorText = new TextView(this);
                errorText.setText("Failed to load VIP plans. Please try again later.");
                errorText.setTextColor(0xFFFFFFFF);
                errorText.setTextSize(16);
                errorText.setGravity(android.view.Gravity.CENTER);
                errorText.setPadding(32, 32, 32, 32);

                vipCardsContainer.addView(errorText);
                vipCardsContainer.setVisibility(View.VISIBLE);
            }
        } catch (Exception e) {
            Log.e(TAG, "showErrorState: error showing error state", e);
        }
    }

    /**
     * 创建VIP卡片视图
     */
    private void createVipCards() {
        if (vipCardsContainer == null || vipConfigs == null || vipConfigs.isEmpty()) {
            Log.w(TAG, "createVipCards: container or configs is null/empty");
            return;
        }

        // 清除现有卡片
        vipCardsContainer.removeAllViews();
        vipCardViews.clear();

        LayoutInflater inflater = LayoutInflater.from(this);

        for (int i = 0; i < vipConfigs.size(); i++) {
            VipConfigModel config = vipConfigs.get(i);

            // 加载卡片布局
            View cardView = inflater.inflate(R.layout.item_vip_card, vipCardsContainer, false);

            // 绑定数据
            bindVipCardData(cardView, config, i);

            // 添加到容器
            vipCardsContainer.addView(cardView);
            vipCardViews.add(cardView);

            Log.d(TAG, "createVipCards: created card for " + config.getVipName());
        }
    }

    /**
     * 绑定VIP卡片数据
     */
    private void bindVipCardData(View cardView, VipConfigModel config, int index) {
        try {
            // 获取卡片内的视图
            TextView tvVipName = cardView.findViewById(R.id.tv_vip_name);
            TextView tvVipSubtitle = cardView.findViewById(R.id.tv_vip_subtitle);
            TextView tvCurrentPrice = cardView.findViewById(R.id.tv_current_price);
            TextView tvPriceUnit = cardView.findViewById(R.id.tv_price_unit);
            TextView tvVipDescription = cardView.findViewById(R.id.tv_vip_description);

            // 折扣相关视图
            LinearLayout layoutDiscount = cardView.findViewById(R.id.layout_discount);
            TextView tvDiscount = cardView.findViewById(R.id.tv_discount);
            TextView tvOriginalPrice = cardView.findViewById(R.id.tv_original_price);

            // 边框和卡片内容
            View vipCardBorder = cardView.findViewById(R.id.vip_card_border);
            LinearLayout vipCardContent = cardView.findViewById(R.id.vip_card_content);

            // 设置基本信息
            if (tvVipName != null) {
                tvVipName.setText(config.getVipName());
            }

            if (tvVipSubtitle != null) {
                tvVipSubtitle.setText(config.getDurationDescription());
            }

            if (tvCurrentPrice != null) {
                tvCurrentPrice.setText(config.getFormattedPrice());
            }

            if (tvPriceUnit != null) {
                tvPriceUnit.setText(config.getDurationUnitText());
            }

            if (tvVipDescription != null) {
                tvVipDescription.setText(config.getBenefitsDescription());
            }

            // 处理折扣显示
            if (config.hasDiscount() && layoutDiscount != null) {
                layoutDiscount.setVisibility(View.VISIBLE);

                if (tvDiscount != null) {
                    tvDiscount.setText(config.getDiscountText());
                }

                if (tvOriginalPrice != null) {
                    tvOriginalPrice.setText(config.getFormattedOriginalPrice());
                    tvOriginalPrice.setPaintFlags(tvOriginalPrice.getPaintFlags() | Paint.STRIKE_THRU_TEXT_FLAG);
                }
            } else if (layoutDiscount != null) {
                layoutDiscount.setVisibility(View.GONE);
            }

            // 设置点击事件
            if (vipCardContent != null) {
                vipCardContent.setOnClickListener(v -> selectPlan(index));
            }

            Log.d(TAG, "bindVipCardData: bound data for " + config.getVipName());

        } catch (Exception e) {
            Log.e(TAG, "bindVipCardData: error binding data for " + config.getVipName(), e);
        }
    }

    /**
     * 选择VIP套餐
     */
    private void selectPlan(int planIndex) {
        if (planIndex < 0 || planIndex >= vipCardViews.size()) {
            Log.w(TAG, "selectPlan: invalid plan index " + planIndex);
            return;
        }

        selectedPlan = planIndex;
        updateCardSelection();

        Log.d(TAG, "selectPlan: selected plan " + planIndex + " - " +
                  (vipConfigs != null && planIndex < vipConfigs.size() ? vipConfigs.get(planIndex).getVipName() : "unknown"));
    }

    /**
     * 更新卡片选择状态
     */
    private void updateCardSelection() {
        for (int i = 0; i < vipCardViews.size(); i++) {
            View cardView = vipCardViews.get(i);
            View border = cardView.findViewById(R.id.vip_card_border);
            LinearLayout content = cardView.findViewById(R.id.vip_card_content);

            boolean isSelected = (i == selectedPlan);

            // 控制边框显示
            if (border != null) {
                border.setVisibility(isSelected ? View.VISIBLE : View.INVISIBLE);
            }

            // 设置选中状态
            if (content != null) {
                content.setSelected(isSelected);
            }
        }
    }

    private void handleSubscribe() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
            return;
        }

        if (vipConfigs == null || selectedPlan >= vipConfigs.size()) {
            Toast.makeText(this, "No VIP plan selected", Toast.LENGTH_SHORT).show();
            return;
        }

        VipConfigModel selectedConfig = vipConfigs.get(selectedPlan);
        String planName = selectedConfig.getVipName();
        String price = selectedConfig.getFormattedPrice();

        Log.d(TAG, "handleSubscribe: subscribing to " + planName + " (ID: " + selectedConfig.getVipId() + ")");

        // 显示支付方式选择弹窗
        showVipPurchaseDialog(planName, price, selectedConfig);
    }

    /**
     * 显示VIP购买支付弹窗
     */
    private void showVipPurchaseDialog(String planName, String price, VipConfigModel vipConfig) {
        PaymentMethodDialog paymentDialog = new PaymentMethodDialog(this, new PaymentMethodDialog.OnPaymentMethodSelectedListener() {
            @Override
            public void onPaymentMethodSelected(String paymentMethod) {
                handleVipPaymentMethodSelection(paymentMethod, planName, price, vipConfig);
            }
        });
        paymentDialog.show();
    }

    /**
     * 处理VIP支付方式选择
     */
    private void handleVipPaymentMethodSelection(String paymentMethod, String planName, String price, VipConfigModel vipConfig) {
        Log.d(TAG, "handleVipPaymentMethodSelection: payment method: " + paymentMethod +
                  ", plan: " + planName + ", price: " + price);

        // 根据支付方式处理VIP购买逻辑
        switch (paymentMethod) {
            case "google":
                // 处理Google Pay支付
                handleVipPurchase(vipConfig, VipApiConstantsUtils.PAY_TYPE_GOOGLE);
                break;
            case "onevision":
                // 处理OneVision支付
                handleVipPurchase(vipConfig, "onevision");
                break;
            default:
                Toast.makeText(this, "Unknown payment method: " + paymentMethod, Toast.LENGTH_SHORT).show();
                break;
        }
    }

    /**
     * 处理VIP购买
     */
    private void handleVipPurchase(VipConfigModel vipConfig, String payType) {
        Log.d(TAG, "handleVipPurchase: processing VIP purchase for " + vipConfig.getVipName() +
                  " with payType: " + payType);

        if (vipConfigApiService == null) {
            Log.e(TAG, "handleVipPurchase: vipConfigApiService is null");
            Toast.makeText(this, "Service not initialized", Toast.LENGTH_SHORT).show();
            return;
        }

        // 显示加载状态
        showLoading();

        // 获取支付金额（使用实际价格，如果没有则使用原价）
        double payAmount = vipConfig.getActualPrice() != null ?
                          vipConfig.getActualPrice() : vipConfig.getPrice();

        vipConfigApiService.purchaseVip(
            vipConfig.getVipId(),
            payAmount,
            payType,
            new VipConfigApiService.VipPurchaseCallback() {
                @Override
                public void onSuccess(VipPurchaseResponse response) {
                    // 隐藏加载状态
                    hideLoading();

                    Log.d(TAG, "handleVipPurchase: purchase success - orderNo: " + response.getOrderNo());

                    // 处理支付响应
                    handleVipPurchaseSuccess(response, payType);
                }

                @Override
                public void onError(String errorMessage) {
                    // 隐藏加载状态
                    hideLoading();

                    Log.e(TAG, "handleVipPurchase: purchase failed: " + errorMessage);
                    Toast.makeText(SubscribeActivity.this,
                                  "Purchase failed: " + errorMessage,
                                  Toast.LENGTH_LONG).show();
                }
            }
        );
    }

    /**
     * 处理VIP购买成功
     */
    private void handleVipPurchaseSuccess(VipPurchaseResponse response, String payType) {
        Log.d(TAG, "handleVipPurchaseSuccess: orderNo=" + response.getOrderNo() + ", payType=" + payType);

        if (VipApiConstantsUtils.PAY_TYPE_GOOGLE.equals(payType)) {
            // 处理Google Pay支付
            if (response.hasGooglePayParams()) {
                // 使用PaymentManager处理Google Pay支付
                PaymentManager paymentManager = new PaymentManager(this);
                paymentManager.processVipPayment(response, payType, new PaymentManager.PaymentCallback() {
                    @Override
                    public void onPaymentSuccess(String orderNo, String paymentMethod, String transactionId) {
                        Log.d(TAG, "VIP支付成功 - 订单号: " + orderNo + ", 交易ID: " + transactionId);
                        Toast.makeText(SubscribeActivity.this,
                                      "VIP purchase successful!",
                                      Toast.LENGTH_LONG).show();

                        // 可以在这里刷新用户VIP状态
                        // refreshUserVipStatus();
                    }

                    @Override
                    public void onPaymentError(String orderNo, String paymentMethod, String errorMessage) {
                        Log.e(TAG, "VIP支付失败 - 订单号: " + orderNo + ", 错误: " + errorMessage);
                        Toast.makeText(SubscribeActivity.this,
                                      "Payment failed: " + errorMessage,
                                      Toast.LENGTH_LONG).show();
                    }

                    @Override
                    public void onPaymentCancelled(String orderNo, String paymentMethod) {
                        Log.d(TAG, "VIP支付取消 - 订单号: " + orderNo);
                        Toast.makeText(SubscribeActivity.this,
                                      "Payment cancelled",
                                      Toast.LENGTH_SHORT).show();
                    }
                });
            } else {
                Log.w(TAG, "Google Pay参数缺失，无法启动支付");
                Toast.makeText(this, "Google Pay parameters missing", Toast.LENGTH_SHORT).show();
            }
        } else {
            // 其他支付方式的处理
            Toast.makeText(this, "Payment method not supported yet", Toast.LENGTH_SHORT).show();
        }
    }

    private void showRedemptionCodeDialog() {
        RedemptionCodeDialog dialog = new RedemptionCodeDialog(this, new RedemptionCodeDialog.OnRedeemCodeListener() {
            @Override
            public void onRedeemCode(String code) {
                handleRedemptionCode(code);
            }
        });
        dialog.show();
    }

    private void handleRedemptionCode(String code) {
        Log.d(TAG, "handleRedemptionCode: processing code: " + code);

        if (vipConfigApiService == null) {
            Log.e(TAG, "handleRedemptionCode: vipConfigApiService is null");
            Toast.makeText(this, "Service not initialized", Toast.LENGTH_SHORT).show();
            return;
        }

        // 显示加载状态
        showLoading();

        vipConfigApiService.redeemCode(code, new VipConfigApiService.RedeemCodeCallback() {
            @Override
            public void onSuccess(RedeemCodeResponseModel response) {
                // 隐藏加载状态
                hideLoading();

                Log.d(TAG, "handleRedemptionCode: response received, code: " + response.getCode());

                if (response.isSuccess()) {
                    // 兑换成功
                    String message = response.getUserFriendlyMessage();
                    Toast.makeText(SubscribeActivity.this, message, Toast.LENGTH_LONG).show();
                    Log.d(TAG, "handleRedemptionCode: redemption successful");

                    // 可以在这里刷新用户信息或VIP状态
                    // TODO: 刷新用户VIP状态

                } else {
                    // 兑换失败
                    String errorMessage = response.getUserFriendlyMessage();
                    Toast.makeText(SubscribeActivity.this, errorMessage, Toast.LENGTH_LONG).show();
                    Log.w(TAG, "handleRedemptionCode: redemption failed - " + errorMessage);
                }
            }

            @Override
            public void onError(String errorMessage) {
                // 隐藏加载状态
                hideLoading();

                Log.e(TAG, "handleRedemptionCode: error - " + errorMessage);
                Toast.makeText(SubscribeActivity.this, "Redemption failed: " + errorMessage, Toast.LENGTH_LONG).show();
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理资源
        if (vipConfigApiService != null) {
            vipConfigApiService.shutdown();
            vipConfigApiService = null;
        }

        if (vipConfigs != null) {
            vipConfigs.clear();
        }

        if (vipCardViews != null) {
            vipCardViews.clear();
        }

        Log.d(TAG, "onDestroy: resources cleaned up");
    }
}
