package com.android.video.ui.component;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.android.video.R;

/**
 * 字幕设置面板组件
 * 提供字幕开关和语言选择功能
 * <AUTHOR>
 */
public class SubtitlePanelView extends LinearLayout {
    
    private ImageView btnClose;
    private ImageView btnSwitch;
    private LinearLayout languageEnglish;
    private LinearLayout languageRussian;
    private TextView tvEnglish;
    private TextView tvRussian;
    
    private boolean isVisible = false;
    private boolean isSubtitleEnabled = false;
    private String selectedLanguage = "English"; // "English" or "Russian"
    
    private OnSubtitleSettingsChangeListener listener;
    
    public interface OnSubtitleSettingsChangeListener {
        void onSubtitleEnabledChanged(boolean enabled);
        void onLanguageChanged(String language);
        void onPanelClosed();
    }
    
    public SubtitlePanelView(Context context) {
        super(context);
        init();
    }
    
    public SubtitlePanelView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public SubtitlePanelView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        setOrientation(VERTICAL);

        // 设置布局参数，确保在底部显示
        LayoutParams params = new LayoutParams(
            LayoutParams.MATCH_PARENT,
            getResources().getDimensionPixelSize(R.dimen.subtitle_panel_height)
        );
        setLayoutParams(params);

        // 加载布局
        LayoutInflater.from(getContext()).inflate(R.layout.subtitle_panel, this, true);

        // 初始化视图
        initViews();

        // 设置点击监听器
        setupClickListeners();

        // 初始状态为隐藏
        setVisibility(GONE);
        setAlpha(0f);
    }
    
    private void initViews() {
        btnClose = findViewById(R.id.btn_close);
        btnSwitch = findViewById(R.id.btn_switch);
        languageEnglish = findViewById(R.id.language_english);
        languageRussian = findViewById(R.id.language_russian);
        tvEnglish = findViewById(R.id.tv_english);
        tvRussian = findViewById(R.id.tv_russian);
        
        // 设置初始状态
        updateSwitchButton();
        updateLanguageSelection();
    }
    
    private void setupClickListeners() {
        // 关闭按钮
        btnClose.setOnClickListener(v -> {
            hide();
            if (listener != null) {
                listener.onPanelClosed();
            }
        });
        
        // 开关按钮
        btnSwitch.setOnClickListener(v -> {
            isSubtitleEnabled = !isSubtitleEnabled;
            updateSwitchButton();
            if (listener != null) {
                listener.onSubtitleEnabledChanged(isSubtitleEnabled);
            }
        });
        
        // 英语选项
        languageEnglish.setOnClickListener(v -> {
            if (!selectedLanguage.equals("English")) {
                selectedLanguage = "English";
                updateLanguageSelection();
                if (listener != null) {
                    listener.onLanguageChanged(selectedLanguage);
                }
            }
        });
        
        // 俄语选项
        languageRussian.setOnClickListener(v -> {
            if (!selectedLanguage.equals("Russian")) {
                selectedLanguage = "Russian";
                updateLanguageSelection();
                if (listener != null) {
                    listener.onLanguageChanged(selectedLanguage);
                }
            }
        });
    }
    
    private void updateSwitchButton() {
        if (isSubtitleEnabled) {
            btnSwitch.setImageResource(R.drawable.play_kai); // 开启状态显示开启图标
        } else {
            btnSwitch.setImageResource(R.drawable.play_guan); // 关闭状态显示关闭图标
        }
    }
    
    private void updateLanguageSelection() {
        if (selectedLanguage.equals("English")) {
            // 英语选中
            languageEnglish.setBackground(getResources().getDrawable(R.drawable.subtitle_language_selected_gradient_bg));
            tvEnglish.setTextColor(getResources().getColor(R.color.subtitle_panel_language_selected_text));
            tvEnglish.setTypeface(tvEnglish.getTypeface(), android.graphics.Typeface.BOLD);
            
            // 添加选中图标
            tvEnglish.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_xuanze, 0, 0, 0);
            tvEnglish.setCompoundDrawablePadding(getResources().getDimensionPixelSize(R.dimen.subtitle_panel_language_icon_margin_start));
            
            // 俄语未选中
            languageRussian.setBackground(getResources().getDrawable(R.drawable.subtitle_language_unselected_bg));
            tvRussian.setTextColor(getResources().getColor(R.color.subtitle_panel_text_primary));
            tvRussian.setTypeface(tvRussian.getTypeface(), android.graphics.Typeface.NORMAL);
            tvRussian.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        } else {
            // 俄语选中
            languageRussian.setBackground(getResources().getDrawable(R.drawable.subtitle_language_selected_gradient_bg));
            tvRussian.setTextColor(getResources().getColor(R.color.subtitle_panel_language_selected_text));
            tvRussian.setTypeface(tvRussian.getTypeface(), android.graphics.Typeface.BOLD);
            
            // 添加选中图标
            tvRussian.setCompoundDrawablesWithIntrinsicBounds(R.drawable.ic_xuanze, 0, 0, 0);
            tvRussian.setCompoundDrawablePadding(getResources().getDimensionPixelSize(R.dimen.subtitle_panel_language_icon_margin_start));
            
            // 英语未选中
            languageEnglish.setBackground(getResources().getDrawable(R.drawable.subtitle_language_unselected_bg));
            tvEnglish.setTextColor(getResources().getColor(R.color.subtitle_panel_text_primary));
            tvEnglish.setTypeface(tvEnglish.getTypeface(), android.graphics.Typeface.NORMAL);
            tvEnglish.setCompoundDrawablesWithIntrinsicBounds(0, 0, 0, 0);
        }
    }
    
    /**
     * 显示面板
     */
    public void show() {
        if (isVisible) return;

        setVisibility(VISIBLE);

        // 强制测量布局
        measure(View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED));

        // 确保面板位于底部
        post(() -> {
            ViewGroup parent = (ViewGroup) getParent();
            if (parent != null) {
                // 确保面板在最顶层
                bringToFront();

                // 重置位置
                setTranslationX(0);
                setTranslationY(0);

                // 简单的淡入动画，因为位置已经通过ConstraintLayout约束设置
                ObjectAnimator fadeIn = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
                fadeIn.setDuration(300);
                fadeIn.start();

                isVisible = true;

                // 添加点击外部关闭功能
                setupOutsideClickListener();
            }
        });
    }

    /**
     * 设置外部点击监听器
     */
    private void setupOutsideClickListener() {
        // 使用自身的触摸监听器来处理外部点击
        setOnTouchListener((v, event) -> {
            // 不拦截自身的触摸事件
            return false;
        });

        // 延迟设置父容器监听器，避免与其他弹窗冲突
        post(() -> {
            ViewGroup parent = (ViewGroup) getParent();
            if (parent != null) {
                parent.setOnTouchListener((v, event) -> {
                    if (isVisible && event.getAction() == android.view.MotionEvent.ACTION_DOWN) {
                        // 检查点击是否在面板外部
                        int[] location = new int[2];
                        getLocationOnScreen(location);

                        float x = event.getRawX();
                        float y = event.getRawY();

                        if (x < location[0] || x > location[0] + getWidth() ||
                            y < location[1] || y > location[1] + getHeight()) {
                            hide();
                            return true;
                        }
                    }
                    return false;
                });
            }
        });
    }
    
    /**
     * 隐藏面板
     */
    public void hide() {
        if (!isVisible) return;

        ViewGroup parent = (ViewGroup) getParent();

        // 向底部滑出动画
        ObjectAnimator slideOut = ObjectAnimator.ofFloat(this, "translationY",
            getTranslationY(),
            parent != null ? parent.getHeight() : (getHeight() > 0 ? getHeight() : getResources().getDimensionPixelSize(R.dimen.subtitle_panel_height)));
        slideOut.setDuration(300);

        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        fadeOut.setDuration(300);

        slideOut.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(GONE);
                removeOutsideClickListener();
            }
        });

        slideOut.start();
        fadeOut.start();

        isVisible = false;
    }

    /**
     * 移除外部点击监听器
     */
    private void removeOutsideClickListener() {
        ViewGroup parent = (ViewGroup) getParent();
        if (parent != null) {
            parent.setOnTouchListener(null);
        }
    }
    
    /**
     * 切换显示/隐藏
     */
    public void toggle() {
        if (isVisible) {
            hide();
        } else {
            show();
        }
    }
    
    // Getter和Setter方法
    public boolean isSubtitleEnabled() {
        return isSubtitleEnabled;
    }
    
    public void setSubtitleEnabled(boolean enabled) {
        this.isSubtitleEnabled = enabled;
        updateSwitchButton();
    }
    
    public String getSelectedLanguage() {
        return selectedLanguage;
    }
    
    public void setSelectedLanguage(String language) {
        this.selectedLanguage = language;
        updateLanguageSelection();
    }
    
    public void setOnSubtitleSettingsChangeListener(OnSubtitleSettingsChangeListener listener) {
        this.listener = listener;
    }
    
    public boolean isVisible() {
        return isVisible;
    }
}
