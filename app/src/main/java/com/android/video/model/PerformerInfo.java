package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 演员信息数据模型
 * <p>
 * 用于映射API返回的演员信息JSON数据结构。
 * 包含演员的姓名和头像信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class PerformerInfo {
    
    /**
     * 演员名称
     */
    @SerializedName("performerName")
    private String performerName;
    
    /**
     * 演员头像URL
     */
    @SerializedName("performerImg")
    private String performerImg;

    /**
     * 默认构造函数
     */
    public PerformerInfo() {
    }

    /**
     * 完整构造函数
     * 
     * @param performerName 演员名称
     * @param performerImg 演员头像URL
     */
    public PerformerInfo(String performerName, String performerImg) {
        this.performerName = performerName;
        this.performerImg = performerImg;
    }

    // ========== Getter和Setter方法 ==========

    public String getPerformerName() {
        return performerName;
    }

    public void setPerformerName(String performerName) {
        this.performerName = performerName;
    }

    public String getPerformerImg() {
        return performerImg;
    }

    public void setPerformerImg(String performerImg) {
        this.performerImg = performerImg;
    }

    @Override
    public String toString() {
        return "PerformerInfo{" +
                "performerName='" + performerName + '\'' +
                ", performerImg='" + performerImg + '\'' +
                '}';
    }
}
