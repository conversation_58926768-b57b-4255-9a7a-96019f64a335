package com.android.video.utils;

import android.util.Log;
import com.android.video.constants.BaseApiConstantsUtils;

/**
 * Token配置工具类
 * <p>
 * 用于管理和验证API访问令牌的配置。
 * 提供token检查、更新和调试功能。
 * </p>
 * 
 * <AUTHOR> Team
 */
public class TokenConfigUtils {

    private static final String TAG = "TokenConfigUtils";

    /**
     * 验证当前token配置
     * <p>
     * 检查当前使用的X-Access-Token是否正确配置。
     * </p>
     */
    public static void validateTokenConfiguration() {
        String currentToken = ApiHeaderUtils.getCurrentAccessToken();
        String headerName = BaseApiConstantsUtils.HEADER_ACCESS_TOKEN;

        Log.i(TAG, "=== Token Configuration Validation ===");
        Log.i(TAG, "Header Name: " + headerName);
        Log.i(TAG, "Current Token: " + (currentToken != null ? maskToken(currentToken) : "null"));
        Log.i(TAG, "Token Length: " + (currentToken != null ? currentToken.length() : 0));
        Log.i(TAG, "Token Valid: " + ApiHeaderUtils.isAccessTokenValid());
        Log.i(TAG, "Using TokenManager: true");

        // 检查是否使用硬编码token
        if (isUsingDefaultToken()) {
            Log.i(TAG, "⚠️ 检测到硬编码token，建议重新初始化");
        } else if (currentToken != null) {
            Log.i(TAG, "✅ 使用动态token（来自TokenManager）");
        } else {
            Log.i(TAG, "⚠️ 当前没有token，需要初始化");
        }
        
        Log.i(TAG, "=====================================");
    }

    /**
     * 设置测试环境token
     * <p>
     * 为测试环境设置特定的访问令牌。
     * </p>
     * 
     * @param testToken 测试环境的访问令牌
     */
    public static void setTestToken(String testToken) {
        if (testToken == null || testToken.trim().isEmpty()) {
            Log.w(TAG, "⚠️ Test token is null or empty, using default token");
            ApiHeaderUtils.resetAccessToken();
        } else {
            Log.i(TAG, "🔧 Setting test token: " + testToken);
            ApiHeaderUtils.setAccessToken(testToken);
            Log.i(TAG, "✅ Test token set successfully");
        }
    }

    /**
     * 重置为默认token
     */
    public static void resetToDefaultToken() {
        Log.i(TAG, "🔄 Resetting token (clearing current token)");
        ApiHeaderUtils.resetAccessToken();
        Log.i(TAG, "✅ Token reset completed, will use TokenManager for next request");
    }

    /**
     * 获取当前token信息（用于调试）
     * 
     * @return token信息字符串
     */
    public static String getCurrentTokenInfo() {
        String currentToken = ApiHeaderUtils.getCurrentAccessToken();
        return String.format("Token: %s (Length: %d)", 
                currentToken != null ? currentToken : "null", 
                currentToken != null ? currentToken.length() : 0);
    }

    /**
     * 检查token是否为默认值
     * 
     * @return 是否为默认token
     */
    public static boolean isUsingDefaultToken() {
        String currentToken = ApiHeaderUtils.getCurrentAccessToken();
        // 检查是否使用了任何已知的硬编码token
        return currentToken != null && (
            "deprecated_hardcoded_token".equals(currentToken) ||
            "05358c87b76645feaae46740fac753c9".equals(currentToken) ||
            "deprecated_dev_test_token".equals(currentToken) ||
            "deprecated_test_env_token".equals(currentToken)
        );
    }

    /**
     * 常用测试token常量
     * <p>
     * @deprecated 不再使用硬编码token，现在通过TokenManager动态管理token
     * 这些常量仅作为历史记录保留
     * </p>
     */
    @Deprecated
    public static class TestTokens {
        /**
         * @deprecated 不再使用硬编码token
         * 开发环境测试token
         */
        @Deprecated
        public static final String DEV_TEST_TOKEN = "deprecated_dev_test_token";

        /**
         * @deprecated 不再使用硬编码token
         * 测试环境token
         */
        @Deprecated
        public static final String TEST_ENV_TOKEN = "deprecated_test_env_token";

        /**
         * @deprecated 不再使用硬编码token
         * 生产环境token（通过TokenManager动态获取）
         */
        @Deprecated
        public static final String PROD_TOKEN_PLACEHOLDER = "use_token_manager_instead";
    }

    /**
     * 根据环境设置合适的token
     * <p>
     * @deprecated 不再使用硬编码token设置，现在通过TokenManager动态管理token
     * 此方法仅作为调试信息保留
     * </p>
     */
    @Deprecated
    public static void setTokenForCurrentEnvironment() {
        Log.i(TAG, "🌍 setTokenForCurrentEnvironment() 已弃用");
        Log.i(TAG, "现在使用TokenManager动态管理token，无需手动设置环境token");

        // 不再设置硬编码token
        // setTestToken(TestTokens.DEV_TEST_TOKEN);

        validateTokenConfiguration();
    }

    /**
     * 打印所有可用的测试token
     * @deprecated 不再使用硬编码token，现在通过TokenManager动态管理token
     */
    @Deprecated
    public static void printAvailableTestTokens() {
        Log.i(TAG, "=== printAvailableTestTokens() 已弃用 ===");
        Log.i(TAG, "现在使用TokenManager动态管理token");
        Log.i(TAG, "请使用TokenManager.getCurrentToken()获取当前token");
        Log.i(TAG, "=============================");
    }

    /**
     * 脱敏处理token用于日志输出
     */
    private static String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }
}
