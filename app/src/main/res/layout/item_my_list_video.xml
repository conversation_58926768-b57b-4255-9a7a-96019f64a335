<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <!-- 海报容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/card_view"
        android:layout_width="@dimen/my_list_grid_poster_width"
        android:layout_height="@dimen/my_list_grid_poster_height"
        app:cardCornerRadius="@dimen/my_list_grid_poster_corner_radius"
        app:cardElevation="0dp"
        app:cardBackgroundColor="#FF2A2A2A"
        app:cardUseCompatPadding="true"
        app:cardPreventCornerOverlap="false">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <!-- 海报图片 -->
            <ImageView
                android:id="@+id/iv_poster"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleType="centerCrop"
                android:src="@drawable/movie_poster" />

            <!-- 喜欢图标 -->
            <ImageView
                android:id="@+id/iv_like"
                android:layout_width="@dimen/my_list_grid_like_icon_size"
                android:layout_height="@dimen/my_list_grid_like_icon_size"
                android:layout_alignParentStart="true"
                android:layout_alignParentTop="true"
                android:layout_marginStart="@dimen/my_list_grid_like_margin_start"
                android:layout_marginTop="@dimen/my_list_grid_like_margin_top"
                android:src="@drawable/ic_like"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

        </RelativeLayout>

    </androidx.cardview.widget.CardView>

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="@dimen/my_list_grid_title_width"
        android:layout_height="@dimen/my_list_grid_title_height"
        android:layout_marginTop="@dimen/my_list_grid_title_margin_top"
        android:text="Eternal Love"
        android:textColor="#ffffffff"
        android:textSize="@dimen/my_list_grid_title_text_size"
        android:maxLines="1"
        android:ellipsize="end"
        android:fontFamily="sans-serif" />

</LinearLayout>
