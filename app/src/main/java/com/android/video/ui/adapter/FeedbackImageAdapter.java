package com.android.video.ui.adapter;

import android.net.Uri;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import java.util.List;

/**
 * 反馈图片上传适配器
 */
public class FeedbackImageAdapter extends RecyclerView.Adapter<FeedbackImageAdapter.ViewHolder> {

    private List<String> images;
    private OnImageClickListener listener;
    private static final int MAX_IMAGE_COUNT = 3;

    public interface OnImageClickListener {
        void onImageClick(int position);
    }

    public FeedbackImageAdapter(List<String> images, OnImageClickListener listener) {
        this.images = images;
        this.listener = listener;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_feedback_image, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        if (position < images.size()) {
            // 显示已上传的图片
            holder.bindImage(images.get(position));
        } else if (position == images.size() && images.size() < MAX_IMAGE_COUNT) {
            // 显示添加图片按钮
            holder.bindAddButton();
        }
    }

    @Override
    public int getItemCount() {
        // 如果图片数量小于最大值，显示添加按钮
        return images.size() < MAX_IMAGE_COUNT ? images.size() + 1 : images.size();
    }

    class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivImage;
        private ImageView ivAddIcon;
        private TextView tvImageCount;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivImage = itemView.findViewById(R.id.iv_image);
            ivAddIcon = itemView.findViewById(R.id.iv_add_icon);
            tvImageCount = itemView.findViewById(R.id.tv_image_count);
        }

        public void bindImage(String imagePath) {
            ivImage.setVisibility(View.VISIBLE);
            ivAddIcon.setVisibility(View.GONE);
            tvImageCount.setVisibility(View.GONE);

            // 使用原生方法加载图片
            try {
                Uri imageUri = Uri.parse(imagePath);
                ivImage.setImageURI(imageUri);
                ivImage.setScaleType(ImageView.ScaleType.CENTER_CROP);
            } catch (Exception e) {
                // 如果加载失败，显示默认图标
                ivImage.setImageResource(R.drawable.ic_pic);
                ivImage.setScaleType(ImageView.ScaleType.CENTER);
            }

            // 设置点击事件
            setClickListener();
        }

        public void bindAddButton() {
            ivImage.setVisibility(View.GONE);
            ivAddIcon.setVisibility(View.VISIBLE);
            tvImageCount.setVisibility(View.VISIBLE);

            // 更新图片计数显示
            tvImageCount.setText(images.size() + "/" + MAX_IMAGE_COUNT);

            // 设置点击事件
            setClickListener();
        }

        private void setClickListener() {
            View.OnClickListener clickListener = v -> {
                if (listener != null) {
                    listener.onImageClick(getAdapterPosition());
                }
            };

            // 为多个视图设置点击事件，确保点击响应
            itemView.setOnClickListener(clickListener);
            ivImage.setOnClickListener(clickListener);
            ivAddIcon.setOnClickListener(clickListener);

            // 设置itemView为可点击
            itemView.setClickable(true);
            itemView.setFocusable(true);
        }
    }
}
