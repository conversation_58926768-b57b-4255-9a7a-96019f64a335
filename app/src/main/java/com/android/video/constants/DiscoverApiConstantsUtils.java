package com.android.video.constants;

/**
 * 发现模块API常量类 - 定义章节列表相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理章节列表相关的API常量，
 * 包括获取章节列表、播放进度等功能的API路径和参数定义。
 * </p>
 *
 * <p>
 * 包含的功能：
 * <ul>
 *   <li>获取章节列表</li>
 *   <li>播放进度信息</li>
 *   <li>短剧详细信息</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取章节列表API URL
 * String url = DiscoverApiConstantsUtils.buildChapterListUrl(baseUrl, filmLanguageInfoId);
 *
 * // 使用参数常量
 * requestParams.put(DiscoverApiConstantsUtils.PARAM_FILM_LANGUAGE_INFO_ID, filmLanguageInfoId);
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class DiscoverApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== API路径常量 ==========

    /**
     * 获取章节列表API路径
     * <p>
     * 根据短剧语言信息ID获取章节列表，包括短剧信息、播放进度和章节详情。
     * </p>
     *
     * <p>
     * 请求方式：GET<br>
     * 请求参数：filmLanguageInfoId (必填)
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "filmVo": {
     *       "filmTitle": "短剧标题",
     *       "cover": "封面URL",
     *       "details": "简介",
     *       ...
     *     },
     *     "playProgress": {
     *       "chapterId": "章节ID",
     *       "progress": 150,
     *       "chapterEp": 1,
     *       ...
     *     },
     *     "chapterList": [
     *       {
     *         "chapterId": "章节ID",
     *         "chapterEp": 1,
     *         "isCharge": 1,
     *         "points": 100,
     *         "isUnlock": 1,
     *         ...
     *       }
     *     ]
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_CHAPTER_LIST = "/app/player/getChapterList";

    /**
     * 上报播放进度API路径
     * <p>
     * 上报用户的视频播放进度，包括当前播放位置和是否播放完成。
     * </p>
     *
     * <p>
     * 请求方式：POST<br>
     * 请求体：JSON格式
     * </p>
     *
     * <p>
     * 请求格式：
     * <pre>
     * {
     *   "filmLanguageInfoId": "c655490792384ed887bfe20e56f30c47",
     *   "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
     *   "progress": 150,
     *   "isFinished": 0,
     *   "duration": 3600
     * }
     * </pre>
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "Report success."
     * }
     * </pre>
     * </p>
     */
    public static final String API_REPORT_PLAY_PROGRESS = "/app/player/reportPlayProgress";

    /**
     * 上报下载进度API路径
     * <p>
     * 上报用户的视频下载进度，包括下载记录ID、下载进度和下载状态。
     * </p>
     *
     * <p>
     * 请求方式：POST<br>
     * 请求体：JSON格式
     * </p>
     *
     * <p>
     * 请求格式：
     * <pre>
     * {
     *   "downloadRecordId": "2d9bf4f909fef05528998764daa94c60",
     *   "downloadProgress": 50,
     *   "downloadStatus": 2
     * }
     * </pre>
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "Report success."
     * }
     * </pre>
     * </p>
     */
    public static final String API_REPORT_DOWNLOAD_PROGRESS = "/app/player/reportDownloadProgress";

    /**
     * 获取播放地址API路径
     * <p>
     * 根据章节ID、分辨率和操作类型获取视频播放地址。
     * </p>
     *
     * <p>
     * 请求方式：GET<br>
     * 请求参数：chapterId, resolution, operationType (必填)
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
     *     "videoUrl": "http://test.mp4",
     *     "expireTime": 1752918434930,
     *     "downloadRecordId": "292f45974e231e177271f8fc1c9e639c",
     *     "subtitles": [...],
     *     "qualities": [...]
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_PLAY_URL = "/app/player/getPlayUrl";

    /**
     * 积分解锁章节API路径
     * <p>
     * 用于使用积分解锁指定章节的播放权限。
     * </p>
     *
     * <p>
     * 请求方式：POST<br>
     * 请求体：JSON格式
     * </p>
     *
     * <p>
     * 请求格式：
     * <pre>
     * {
     *   "chapterId": "dda0c118ea34677f173dbca87f81d6e1",
     *   "filmLanguageInfoId": "c655490792384ed887bfe20e56f30c47"
     * }
     * </pre>
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "Unlock success."
     * }
     * </pre>
     * </p>
     */
    public static final String API_UNLOCK_CHAPTER = "/app/player/unlock";

    /**
     * 添加/取消喜欢API路径
     * <p>
     * 用于添加或取消对短剧的喜欢状态。
     * </p>
     *
     * <p>
     * 请求方式：POST<br>
     * 请求体：JSON格式
     * </p>
     *
     * <p>
     * 请求格式：
     * <pre>
     * {
     *   "filmId": "短剧ID",
     *   "isLove": 1
     * }
     * </pre>
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success"
     * }
     * </pre>
     * </p>
     */
    public static final String API_ADD_LOVE = "/app/discover/addLove";

    /**
     * 瀑布流推荐API路径
     * <p>
     * 用于获取瀑布流推荐的短剧列表，支持分页。
     * </p>
     *
     * <p>
     * 请求方式：GET<br>
     * 请求参数：page, size (必填)
     * </p>
     *
     * <p>
     * 响应格式：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "records": [
     *       {
     *         "filmInfo": {...},
     *         "firstChapter": {...},
     *         "recommendationScore": 0,
     *         "qualities": [...]
     *       }
     *     ],
     *     "total": 4,
     *     "size": 10,
     *     "current": 1,
     *     "pages": 1
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_WATERFALL_RECOMMEND = "/app/index/waterfallRecommend";

    // ========== 请求参数常量 ==========

    /**
     * 短剧语言信息ID参数名
     */
    public static final String PARAM_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    /**
     * 章节ID参数名
     */
    public static final String PARAM_CHAPTER_ID = "chapterId";

    /**
     * 播放进度参数名（单位：秒）
     */
    public static final String PARAM_PROGRESS = "progress";

    /**
     * 是否完成播放参数名（0=未完成, 1=完成）
     */
    public static final String PARAM_IS_FINISHED = "isFinished";

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 当前章节总时长参数名（单位：秒）
     */
    public static final String PARAM_DURATION = "duration";

    /**
     * 分辨率参数名
     */
    public static final String PARAM_RESOLUTION = "resolution";

    /**
     * 操作类型参数名（1=播放, 2=下载）
     */
    public static final String PARAM_OPERATION_TYPE = "operationType";

    /**
     * 短剧ID参数名
     */
    public static final String PARAM_FILM_ID = "filmId";

    /**
     * 是否喜欢参数名（0=取消喜欢, 1=添加喜欢）
     */
    public static final String PARAM_IS_LOVE = "isLove";

    // ========== URL构建工具方法 ==========

    /**
     * 构建获取章节列表的完整URL
     * <p>
     * 根据基础URL和短剧语言信息ID构建完整的API请求URL。
     * </p>
     *
     * @param baseUrl API基础URL
     * @param filmLanguageInfoId 短剧语言信息ID
     * @return 完整的API请求URL
     *
     * @throws IllegalArgumentException 如果参数为空或无效
     *
     * @example
     * <pre>
     * String url = buildChapterListUrl("https://api.example.com/v1", "c655490792384ed887bfe20e56f30c47");
     * // 返回: "https://api.example.com/v1/app/player/getChapterList?filmLanguageInfoId=c655490792384ed887bfe20e56f30c47"
     * </pre>
     */
    public static String buildChapterListUrl(String baseUrl, String filmLanguageInfoId) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            throw new IllegalArgumentException("Film language info ID cannot be null or empty");
        }

        return buildApiUrl(baseUrl, API_GET_CHAPTER_LIST) +
               "?" + PARAM_FILM_LANGUAGE_INFO_ID + "=" + filmLanguageInfoId.trim();
    }

    /**
     * 构建上报播放进度的完整URL
     * <p>
     * 根据基础URL构建上报播放进度的API请求URL。
     * </p>
     *
     * @param baseUrl API基础URL
     * @return 完整的API请求URL
     *
     * @throws IllegalArgumentException 如果参数为空或无效
     *
     * @example
     * <pre>
     * String url = buildReportPlayProgressUrl("https://api.example.com/v1");
     * // 返回: "https://api.example.com/v1/app/player/reportPlayProgress"
     * </pre>
     */
    public static String buildReportPlayProgressUrl(String baseUrl) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        return buildApiUrl(baseUrl, API_REPORT_PLAY_PROGRESS);
    }

    /**
     * 构建上报下载进度的完整URL
     * <p>
     * 根据基础URL构建上报下载进度的API请求URL。
     * </p>
     *
     * @param baseUrl API基础URL
     * @return 完整的API请求URL
     *
     * @throws IllegalArgumentException 如果参数为空或无效
     *
     * @example
     * <pre>
     * String url = buildReportDownloadProgressUrl("https://api.example.com/v1");
     * // 返回: "https://api.example.com/v1/app/player/reportDownloadProgress"
     * </pre>
     */
    public static String buildReportDownloadProgressUrl(String baseUrl) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        return buildApiUrl(baseUrl, API_REPORT_DOWNLOAD_PROGRESS);
    }

    /**
     * 构建获取播放地址的完整URL
     * <p>
     * 根据基础URL、短剧语言信息ID、章节ID、分辨率和操作类型构建获取播放地址的API请求URL。
     * </p>
     *
     * @param baseUrl API基础URL
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param chapterId 章节ID
     * @param resolution 分辨率（auto/480/720/1080）
     * @param operationType 操作类型（1=播放, 2=下载）
     * @return 完整的API请求URL
     *
     * @throws IllegalArgumentException 如果参数为空或无效
     *
     * @example
     * <pre>
     * String url = buildGetPlayUrlUrl("https://api.example.com/v1", "8863ca60734448489846a076097435b8", "ce5b54521a2020c4019ca491f8cc2a9a", "720", 1);
     * // 返回: "https://api.example.com/v1/app/player/getPlayUrl?filmLanguageInfoId=8863ca60734448489846a076097435b8&chapterId=ce5b54521a2020c4019ca491f8cc2a9a&resolution=720&operationType=1"
     * </pre>
     */
    public static String buildGetPlayUrlUrl(String baseUrl, String filmLanguageInfoId, String chapterId, String resolution, int operationType) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            throw new IllegalArgumentException("Film language info ID cannot be null or empty");
        }

        if (chapterId == null || chapterId.trim().isEmpty()) {
            throw new IllegalArgumentException("Chapter ID cannot be null or empty");
        }

        if (resolution == null || resolution.trim().isEmpty()) {
            throw new IllegalArgumentException("Resolution cannot be null or empty");
        }

        if (operationType != 1 && operationType != 2) {
            throw new IllegalArgumentException("Operation type must be 1 (play) or 2 (download)");
        }

        return buildApiUrl(baseUrl, API_GET_PLAY_URL) +
               "?" + PARAM_FILM_LANGUAGE_INFO_ID + "=" + filmLanguageInfoId.trim() +
               "&" + PARAM_CHAPTER_ID + "=" + chapterId.trim() +
               "&" + PARAM_RESOLUTION + "=" + resolution.trim() +
               "&" + PARAM_OPERATION_TYPE + "=" + operationType;
    }

    /**
     * 构建瀑布流推荐的完整URL
     * <p>
     * 根据基础URL、页码和每页数量构建瀑布流推荐的API请求URL。
     * </p>
     *
     * @param baseUrl API基础URL
     * @param page 页码（从1开始）
     * @param size 每页数量
     * @return 完整的API请求URL
     *
     * @throws IllegalArgumentException 如果参数无效
     *
     * @example
     * <pre>
     * String url = buildWaterfallRecommendUrl("https://api.example.com/v1", 1, 10);
     * // 返回: "https://api.example.com/v1/app/index/waterfallRecommend?page=1&size=10"
     * </pre>
     */
    public static String buildWaterfallRecommendUrl(String baseUrl, int page, int size) {
        if (baseUrl == null || baseUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("Base URL cannot be null or empty");
        }

        if (page < 1) {
            throw new IllegalArgumentException("Page must be greater than 0");
        }

        if (size < 1 || size > 100) {
            throw new IllegalArgumentException("Size must be between 1 and 100");
        }

        return buildApiUrl(baseUrl, API_WATERFALL_RECOMMEND) +
               "?" + PARAM_PAGE + "=" + page +
               "&" + PARAM_SIZE + "=" + size;
    }

    /**
     * 验证短剧语言信息ID格式
     * <p>
     * 检查短剧语言信息ID是否符合预期格式。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @return 如果格式有效返回true，否则返回false
     */
    public static boolean isValidFilmLanguageInfoId(String filmLanguageInfoId) {
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            return false;
        }

        // 检查长度（通常为32位十六进制字符串）
        String trimmed = filmLanguageInfoId.trim();
        if (trimmed.length() != 32) {
            return false;
        }

        // 检查是否只包含十六进制字符
        return trimmed.matches("^[a-fA-F0-9]+$");
    }

    /**
     * 获取API完整路径
     * <p>
     * 返回包含基础路径的完整API路径。
     * </p>
     *
     * @param baseUrl API基础URL
     * @return 完整的API路径
     */
    public static String getFullApiPath(String baseUrl) {
        return buildApiUrl(baseUrl, API_GET_CHAPTER_LIST);
    }
}
