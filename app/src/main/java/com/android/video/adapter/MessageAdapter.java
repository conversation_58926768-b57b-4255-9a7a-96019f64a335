package com.android.video.adapter;

import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.constants.MessageApiConstantsUtils;
import com.android.video.model.MessageItemModel;


import java.util.List;

/**
 * Message通知适配器 - 支持MessageItemModel数据模型
 * <p>
 * 此适配器用于显示消息推送列表，支持两种类型的消息：
 * <ul>
 *   <li>带视频信息的订阅消息</li>
 *   <li>不带视频信息的系统消息</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class MessageAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_WITH_VIDEO = 1;
    private static final int TYPE_WITHOUT_VIDEO = 2;

    private List<MessageItemModel> messageList;
    private OnMessageClickListener onMessageClickListener;

    /**
     * 消息点击监听接口
     */
    public interface OnMessageClickListener {
        /**
         * Go按钮点击事件
         *
         * @param message 消息数据
         */
        void onGoButtonClick(MessageItemModel message);
    }

    /**
     * 构造函数
     *
     * @param messageList 消息列表
     * @param listener 点击监听器
     */
    public MessageAdapter(List<MessageItemModel> messageList, OnMessageClickListener listener) {
        this.messageList = messageList;
        this.onMessageClickListener = listener;
    }

    @Override
    public int getItemViewType(int position) {
        MessageItemModel message = messageList.get(position);
        // 订阅消息且有视频信息时显示带视频的布局
        return (message.getMessageType() == MessageApiConstantsUtils.MESSAGE_TYPE_SUBSCRIPTION &&
                !TextUtils.isEmpty(message.getFilmTitle())) ? TYPE_WITH_VIDEO : TYPE_WITHOUT_VIDEO;
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_WITH_VIDEO) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_message_with_video, parent, false);
            return new WithVideoViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_message_without_video, parent, false);
            return new WithoutVideoViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        MessageItemModel message = messageList.get(position);

        if (holder instanceof WithVideoViewHolder) {
            ((WithVideoViewHolder) holder).bind(message);
        } else if (holder instanceof WithoutVideoViewHolder) {
            ((WithoutVideoViewHolder) holder).bind(message);
        }
    }

    @Override
    public int getItemCount() {
        return messageList != null ? messageList.size() : 0;
    }

    /**
     * 更新数据
     */
    public void updateData(List<MessageItemModel> newData) {
        this.messageList = newData;
        notifyDataSetChanged();
    }

    /**
     * 带视频信息的ViewHolder - 用于订阅消息
     */
    public class WithVideoViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTitle;
        private TextView tvDateTime;
        private TextView tvContent;
        private ImageView ivVideoPoster;
        private TextView tvVideoTitle;
        private TextView tvEpisode;
        private TextView tvGoButton;

        public WithVideoViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvDateTime = itemView.findViewById(R.id.tv_date_time);
            tvContent = itemView.findViewById(R.id.tv_content);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            tvEpisode = itemView.findViewById(R.id.tv_episode);
            tvGoButton = itemView.findViewById(R.id.tv_go_button);
        }

        public void bind(MessageItemModel message) {
            if (message == null) return;

            // 设置基本信息
            tvTitle.setText(MessageApiConstantsUtils.getMessageTypeDescription(message.getMessageType()));
            tvDateTime.setText(message.getCreateTime());
            tvContent.setText(message.getMessageTitle());

            // 设置视频信息
            tvVideoTitle.setText(message.getFilmTitle());

            // 设置集数信息
            if (message.getTotalChaptersNum() != null && message.getTotalChaptersNum() > 0) {
                tvEpisode.setText("EP." + message.getTotalChaptersNum());
            } else {
                tvEpisode.setText("");
            }

            // 设置海报图片
            if (!TextUtils.isEmpty(message.getCover())) {
                // 使用Glide加载网络图片
                Glide.with(itemView.getContext())
                        .load(message.getCover())
                        .placeholder(R.drawable.movie_poster) // 加载中显示的占位图
                        .error(R.drawable.movie_poster) // 加载失败显示的图片
                        .diskCacheStrategy(DiskCacheStrategy.ALL) // 缓存策略
                        .centerCrop() // 居中裁剪
                        .into(ivVideoPoster);
            } else {
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }

            // Go按钮点击事件
            tvGoButton.setVisibility(View.VISIBLE);
            tvGoButton.setOnClickListener(v -> {
                if (onMessageClickListener != null) {
                    onMessageClickListener.onGoButtonClick(message);
                }
            });

            // 控制未读红点显示
            View unreadDot = itemView.findViewById(R.id.message_unread_dot);
            if (unreadDot != null) {
                unreadDot.setVisibility(message.isRead() ? View.GONE : View.VISIBLE);
            }
        }
    }

    /**
     * 无视频信息的ViewHolder - 用于系统消息
     */
    public class WithoutVideoViewHolder extends RecyclerView.ViewHolder {
        private TextView tvTitle;
        private TextView tvDateTime;
        private TextView tvContent;

        public WithoutVideoViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvDateTime = itemView.findViewById(R.id.tv_date_time);
            tvContent = itemView.findViewById(R.id.tv_content);
            // 移除GO按钮引用，因为布局中已删除
        }

        public void bind(MessageItemModel message) {
            if (message == null) return;

            // 设置基本信息
            tvTitle.setText(MessageApiConstantsUtils.getMessageTypeDescription(message.getMessageType()));
            tvDateTime.setText(message.getCreateTime());
            tvContent.setText(message.getMessageTitle());

            // 整个消息项点击事件
            itemView.setOnClickListener(v -> {
                if (onMessageClickListener != null) {
                    onMessageClickListener.onGoButtonClick(message);
                }
            });

            // 控制未读红点显示
            View unreadDot = itemView.findViewById(R.id.message_unread_dot);
            if (unreadDot != null) {
                unreadDot.setVisibility(message.isRead() ? View.GONE : View.VISIBLE);
            }
        }
    }
}
