<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <!-- 标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Log off"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 警告图标 -->
        <ImageView
            android:id="@+id/iv_warning"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="24dp"
            android:src="@drawable/log_off_tixing" />

        <!-- 描述文本 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="@dimen/log_off_description_width"
            android:layout_height="@dimen/log_off_description_height"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/log_off_description_margin_top"
            android:layout_marginStart="@dimen/log_off_item_margin_horizontal"
            android:layout_marginEnd="@dimen/log_off_item_margin_horizontal"
            android:text="Cancelling an account will permanently become invalid and irretrievable, and will result in the loss of the following benefits"
            android:textColor="#ccffffff"
            android:textSize="14sp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:lineSpacingExtra="2dp" />

        <!-- 警告卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="24dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/log_off_warning_card_bg"
            android:orientation="vertical"
            android:padding="16dp">

            <!-- 第1条 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:minHeight="26dp"
                android:text="1. The account will not be able to log in"
                android:textColor="#ffffffff"
                android:textSize="14sp"
                android:fontFamily="sans-serif"
                android:gravity="center_vertical" />

            <!-- 第2条 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:minHeight="26dp"
                android:text="2. Unlocked skits will no longer be available for viewing"
                android:textColor="#ffffffff"
                android:textSize="14sp"
                android:fontFamily="sans-serif"
                android:gravity="center_vertical" />

            <!-- 第3条 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="16dp"
                android:minHeight="26dp"
                android:text="3. Subscribed members cannot continue to use"
                android:textColor="#ffffffff"
                android:textSize="14sp"
                android:fontFamily="sans-serif"
                android:gravity="center_vertical" />

            <!-- 第4条 -->
            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="26dp"
                android:text="4. Personal information and order information are permanently invalid"
                android:textColor="#ffffffff"
                android:textSize="14sp"
                android:fontFamily="sans-serif"
                android:gravity="center_vertical" />

        </LinearLayout>

        <!-- 占位空间，让确认文本往下移动 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- 确认复选框 -->
        <FrameLayout
            android:id="@+id/layout_checkbox"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/log_off_item_margin_horizontal"
            android:layout_marginEnd="@dimen/log_off_item_margin_horizontal"
            android:layout_marginBottom="@dimen/log_off_checkbox_margin_top"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:paddingTop="8dp"
            android:paddingBottom="8dp">

            <!-- 确认文本（使用LeadingMarginSpan实现悬挂缩进） -->
            <TextView
                android:id="@+id/tv_confirm"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="I confirm that I have read and understood the above information, and voluntarily waive all data, rights, assets, and services in my account"
                android:textColor="#ffffffff"
                android:textSize="@dimen/log_off_checkbox_text_size"
                android:fontFamily="sans-serif"
                android:lineSpacingExtra="@dimen/log_off_checkbox_text_line_spacing"
                android:gravity="top" />

            <!-- 复选框图标（绝对定位在左上角） -->
            <ImageView
                android:id="@+id/iv_checkbox"
                android:layout_width="@dimen/log_off_checkbox_icon_size"
                android:layout_height="@dimen/log_off_checkbox_icon_size"
                android:layout_gravity="start|top"
                android:layout_marginTop="2dp"
                android:src="@drawable/ic_xuanze0" />

        </FrameLayout>

        <!-- Delete Account按钮 -->
        <TextView
            android:id="@+id/btn_delete_account"
            android:layout_width="match_parent"
            android:layout_height="42dp"
            android:layout_marginBottom="38dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="@drawable/log_off_delete_button_bg"
            android:text="Delete Account"
            android:textColor="#ff141414"
            android:textSize="16sp"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:enabled="false"
            android:alpha="0.5"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

</FrameLayout>
