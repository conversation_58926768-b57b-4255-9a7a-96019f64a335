package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.TagModel;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 收藏页面多选标签RecyclerView适配器
 * 支持多选功能，与首页单选TagAdapter完全独立
 * <AUTHOR>
 */
public class FavoriteTagAdapter extends RecyclerView.Adapter<FavoriteTagAdapter.FavoriteTagViewHolder> {

    private List<TagModel> tagList;
    private Set<Integer> selectedPositions; // 存储多选状态
    private OnMultiTagSelectionListener onMultiTagSelectionListener;
    
    /**
     * 多选标签点击监听接口
     */
    public interface OnMultiTagSelectionListener {
        void onTagSelectionChanged(TagModel tag, int position, boolean isSelected);
    }
    
    /**
     * 构造函数
     */
    public FavoriteTagAdapter() {
        this.tagList = new ArrayList<>();
        this.selectedPositions = new HashSet<>();
    }
    
    /**
     * 构造函数
     * @param tagList 标签列表
     */
    public FavoriteTagAdapter(List<TagModel> tagList) {
        this.tagList = tagList != null ? tagList : new ArrayList<>();
        this.selectedPositions = new HashSet<>();
    }
    
    /**
     * 设置多选标签监听器
     * @param listener 监听器
     */
    public void setOnMultiTagSelectionListener(OnMultiTagSelectionListener listener) {
        this.onMultiTagSelectionListener = listener;
    }
    
    /**
     * 更新标签列表
     * @param newTagList 新的标签列表
     */
    public void updateTagList(List<TagModel> newTagList) {
        this.tagList = newTagList != null ? newTagList : new ArrayList<>();
        // 清除之前的选中状态
        selectedPositions.clear();
        notifyDataSetChanged();
    }
    
    /**
     * 切换选中状态
     * @param position 位置
     */
    public void toggleSelection(int position) {
        if (position >= 0 && position < tagList.size()) {
            if (selectedPositions.contains(position)) {
                selectedPositions.remove(position);
                tagList.get(position).setSelected(false);
            } else {
                selectedPositions.add(position);
                tagList.get(position).setSelected(true);
            }
            notifyItemChanged(position);
            
            // 回调监听器
            if (onMultiTagSelectionListener != null) {
                TagModel tag = tagList.get(position);
                onMultiTagSelectionListener.onTagSelectionChanged(tag, position, tag.isSelected());
            }
        }
    }
    
    /**
     * 设置选中状态
     * @param position 位置
     * @param selected 是否选中
     */
    public void setSelected(int position, boolean selected) {
        if (position >= 0 && position < tagList.size()) {
            if (selected) {
                selectedPositions.add(position);
                tagList.get(position).setSelected(true);
            } else {
                selectedPositions.remove(position);
                tagList.get(position).setSelected(false);
            }
            notifyItemChanged(position);
        }
    }
    
    /**
     * 检查是否选中
     * @param position 位置
     * @return 是否选中
     */
    public boolean isSelected(int position) {
        return selectedPositions.contains(position);
    }
    
    /**
     * 获取所有选中的标签
     * @return 选中的标签列表
     */
    public List<TagModel> getSelectedTags() {
        List<TagModel> selectedTags = new ArrayList<>();
        for (Integer position : selectedPositions) {
            if (position < tagList.size()) {
                selectedTags.add(tagList.get(position));
            }
        }
        return selectedTags;
    }
    
    /**
     * 获取选中标签的名称列表
     * @return 选中标签名称列表
     */
    public List<String> getSelectedTagNames() {
        List<String> selectedNames = new ArrayList<>();
        for (Integer position : selectedPositions) {
            if (position < tagList.size()) {
                selectedNames.add(tagList.get(position).getName());
            }
        }
        return selectedNames;
    }
    
    /**
     * 获取选中数量
     * @return 选中数量
     */
    public int getSelectedCount() {
        return selectedPositions.size();
    }
    
    /**
     * 清除所有选中状态
     */
    public void clearSelection() {
        selectedPositions.clear();
        for (TagModel tag : tagList) {
            tag.setSelected(false);
        }
        notifyDataSetChanged();
    }
    
    /**
     * 全选
     */
    public void selectAll() {
        selectedPositions.clear();
        for (int i = 0; i < tagList.size(); i++) {
            selectedPositions.add(i);
            tagList.get(i).setSelected(true);
        }
        notifyDataSetChanged();
    }
    
    @NonNull
    @Override
    public FavoriteTagViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_favorite_tag, parent, false);
        return new FavoriteTagViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FavoriteTagViewHolder holder, int position) {
        TagModel tag = tagList.get(position);
        holder.bind(tag, position);
    }
    
    @Override
    public int getItemCount() {
        return tagList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class FavoriteTagViewHolder extends RecyclerView.ViewHolder {
        
        private TextView tvTagName;
        
        public FavoriteTagViewHolder(@NonNull View itemView) {
            super(itemView);
            tvTagName = itemView.findViewById(R.id.tv_tag_name);
        }
        
        /**
         * 绑定数据
         * @param tag 标签数据
         * @param position 位置
         */
        public void bind(TagModel tag, int position) {
            if (tag == null) {
                return;
            }
            
            // 设置标签名称
            tvTagName.setText(tag.getDisplayName());

            // 设置选中状态和文本颜色
            boolean isSelected = selectedPositions.contains(position);
            tvTagName.setSelected(isSelected);
            tag.setSelected(isSelected);
            
            if (isSelected) {
                tvTagName.setTextColor(0xFF000000); // 选中时黑色文本
            } else {
                tvTagName.setTextColor(0xFFFFFFFF); // 未选中时白色文本
            }

            // 设置点击事件
            tvTagName.setOnClickListener(v -> {
                toggleSelection(position);
            });
        }
    }
}
