<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <TextView
        android:id="@+id/tv_featured_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_categories_margin_start"
        android:text="Featured Section"
        android:textColor="@color/white"
        android:textSize="@dimen/home_module_title_text_size"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <ImageView
        android:id="@+id/iv_featured_next"
        android:layout_width="@dimen/home_next_icon_size"
        android:layout_height="@dimen/home_next_icon_size"
        android:layout_marginEnd="@dimen/home_see_all_margin_end"
        android:alpha="0.64"
        android:scaleType="centerInside"
        android:src="@drawable/home_ic_next"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <TextView
        android:id="@+id/tv_featured_see_all"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/home_next_icon_margin"
        android:text="See All"
        android:textColor="@color/white"
        android:textSize="@dimen/home_module_see_all_text_size"
        android:fontFamily="sans-serif-light"
        android:alpha="0.64"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@id/iv_featured_next"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
