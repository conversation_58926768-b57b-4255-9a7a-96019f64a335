package com.android.video.config;

/**
 * 全局配置管理工具类 - 定义全局配置常量和参数
 * <AUTHOR> Team
 */
public class GlobalConfigUtils {

    // ========== 网络配置 ==========
    
    /**
     * HTTP连接超时时间（秒）
     */
    public static final int CONNECT_TIMEOUT_SECONDS = 30;

    /**
     * HTTP读取超时时间（秒）
     */
    public static final int READ_TIMEOUT_SECONDS = 30;

    /**
     * HTTP写入超时时间（秒）
     */
    public static final int WRITE_TIMEOUT_SECONDS = 30;

    /**
     * 最大重试次数
     */
    public static final int MAX_RETRY_COUNT = 3;

    // ========== 请求头配置 ==========

    /**
     * Content-Type: JSON
     */
    public static final String CONTENT_TYPE_JSON = "application/json";

    /**
     * Content-Type: Form
     */
    public static final String CONTENT_TYPE_FORM = "application/x-www-form-urlencoded";

    /**
     * 访问令牌请求头名称
     */
    public static final String HEADER_ACCESS_TOKEN = "X-Access-Token";

    /**
     * 用户代理请求头
     */
    public static final String HEADER_USER_AGENT = "VideoPlayer-Android/1.0";

    /**
     * 设备ID请求头名称
     */
    public static final String HEADER_DEVICE_ID = "X-Device-ID";

    // ========== 缓存配置 ==========

    /**
     * 标签数据缓存时间（毫秒）- 1小时
     */
    public static final long LABELS_CACHE_DURATION = 60 * 60 * 1000L;

    /**
     * 用户信息缓存时间（毫秒）- 24小时
     */
    public static final long USER_INFO_CACHE_DURATION = 24 * 60 * 60 * 1000L;

    // ========== 分页配置 ==========

    /**
     * 默认分页大小
     */
    public static final int DEFAULT_PAGE_SIZE = 10;

    /**
     * 最大分页大小
     */
    public static final int MAX_PAGE_SIZE = 50;

    // ========== 语言配置 ==========

    /**
     * 英语语言类型
     */
    public static final int LANGUAGE_TYPE_ENGLISH = 1;

    /**
     * 俄语语言类型
     */
    public static final int LANGUAGE_TYPE_RUSSIAN = 2;

    // ========== 位置配置 ==========

    /**
     * 首页位置
     */
    public static final int LOCATION_HOME = 1;

    /**
     * 分类详情位置
     */
    public static final int LOCATION_CATEGORY_DETAIL = 2;

    // ========== 工具方法 ==========

    /**
     * 获取网络连接超时时间（毫秒）
     * @return 超时时间毫秒数
     */
    public static long getConnectTimeoutMillis() {
        return CONNECT_TIMEOUT_SECONDS * 1000L;
    }

    /**
     * 获取网络读取超时时间（毫秒）
     * @return 超时时间毫秒数
     */
    public static long getReadTimeoutMillis() {
        return READ_TIMEOUT_SECONDS * 1000L;
    }

    /**
     * 获取网络写入超时时间（毫秒）
     * @return 超时时间毫秒数
     */
    public static long getWriteTimeoutMillis() {
        return WRITE_TIMEOUT_SECONDS * 1000L;
    }

    /**
     * 获取默认请求头User-Agent
     * @return User-Agent字符串
     */
    public static String getDefaultUserAgent() {
        return HEADER_USER_AGENT;
    }

    /**
     * 验证分页大小是否有效
     * @param pageSize 分页大小
     * @return 有效的分页大小
     */
    public static int validatePageSize(int pageSize) {
        if (pageSize <= 0) {
            return DEFAULT_PAGE_SIZE;
        }
        return Math.min(pageSize, MAX_PAGE_SIZE);
    }
}
