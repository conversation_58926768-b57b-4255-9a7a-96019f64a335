package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.List;

/**
 * Best For You模块RecyclerView适配器
 * <AUTHOR>
 */
public class BestForYouAdapter extends RecyclerView.Adapter<BestForYouAdapter.BestForYouViewHolder> {
    
    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    
    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }
    
    /**
     * 构造函数
     */
    public BestForYouAdapter() {
        this.videoList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public BestForYouAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }
    
    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }
    
    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList != null ? newVideoList : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 添加视频到列表
     * @param video 视频数据
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }
    
    /**
     * 移除指定位置的视频
     * @param position 位置
     */
    public void removeVideo(int position) {
        if (position >= 0 && position < videoList.size()) {
            videoList.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }
    
    @NonNull
    @Override
    public BestForYouViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_best_for_you_video, parent, false);
        return new BestForYouViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull BestForYouViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }
    
    @Override
    public int getItemCount() {
        return videoList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class BestForYouViewHolder extends RecyclerView.ViewHolder {
        
        private ImageView ivVideoPoster;
        private TextView tvVideoTitle;
        private View viewGradientOverlay;
        private TextView tvMatchPercentage;
        private TextView tvBestForYouLabel;
        
        public BestForYouViewHolder(@NonNull View itemView) {
            super(itemView);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            viewGradientOverlay = itemView.findViewById(R.id.view_gradient_overlay);
            tvMatchPercentage = itemView.findViewById(R.id.tv_match_percentage);
            tvBestForYouLabel = itemView.findViewById(R.id.tv_best_for_you_label);
        }
        
        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }
            
            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .centerCrop()
                    .into(ivVideoPoster);
            } else {
                // 如果没有URL，使用默认图片
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }
            
            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());
            
            // 设置合适度百分比
            if (video.getMatchPercentage() > 0) {
                tvMatchPercentage.setText(video.getMatchPercentageText());
                tvMatchPercentage.setVisibility(View.VISIBLE);
            } else {
                tvMatchPercentage.setVisibility(View.GONE);
            }
            
            // 设置"认为你喜欢"标签
//            tvBestForYouLabel.setText("认为你喜欢");
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(), 
                            "Best For You视频点击: " + video.getDisplayTitle() + 
                            " (合适度: " + video.getMatchPercentageText() + ")", 
                            Toast.LENGTH_SHORT).show();
                }
            });
            
            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                Toast.makeText(itemView.getContext(), 
                        "长按: " + video.getDisplayTitle() + 
                        "\n合适度: " + video.getMatchPercentageText() + 
                        "\n分类: " + video.getCategory(), 
                        Toast.LENGTH_SHORT).show();
                return true;
            });
        }
    }
}
