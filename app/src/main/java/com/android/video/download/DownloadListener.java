package com.android.video.download;

/**
 * 下载监听器接口
 * <p>
 * 定义下载过程中的各种回调方法，用于监听下载状态变化、进度更新等。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public interface DownloadListener {

    /**
     * 下载开始
     *
     * @param downloadInfo 下载信息
     */
    void onDownloadStart(DownloadInfo downloadInfo);

    /**
     * 下载进度更新
     *
     * @param downloadInfo 下载信息
     * @param downloadedSize 已下载大小
     * @param totalSize 总大小
     * @param progress 进度百分比（0-100）
     */
    void onDownloadProgress(DownloadInfo downloadInfo, long downloadedSize, long totalSize, int progress);

    /**
     * 下载暂停
     *
     * @param downloadInfo 下载信息
     */
    void onDownloadPause(DownloadInfo downloadInfo);

    /**
     * 下载恢复
     *
     * @param downloadInfo 下载信息
     */
    void onDownloadResume(DownloadInfo downloadInfo);

    /**
     * 下载完成
     *
     * @param downloadInfo 下载信息
     */
    void onDownloadComplete(DownloadInfo downloadInfo);

    /**
     * 下载失败
     *
     * @param downloadInfo 下载信息
     * @param errorMessage 错误信息
     */
    void onDownloadFailed(DownloadInfo downloadInfo, String errorMessage);

    /**
     * 下载取消
     *
     * @param downloadInfo 下载信息
     */
    void onDownloadCancelled(DownloadInfo downloadInfo);

    /**
     * 下载重试
     *
     * @param downloadInfo 下载信息
     * @param retryCount 重试次数
     */
    void onDownloadRetry(DownloadInfo downloadInfo, int retryCount);

    /**
     * 简化的下载监听器适配器
     * <p>
     * 提供默认的空实现，子类可以选择性地重写需要的方法。
     * </p>
     */
    abstract class SimpleDownloadListener implements DownloadListener {

        @Override
        public void onDownloadStart(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadProgress(DownloadInfo downloadInfo, long downloadedSize, long totalSize, int progress) {
            // 默认空实现
        }

        @Override
        public void onDownloadPause(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadResume(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadComplete(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadFailed(DownloadInfo downloadInfo, String errorMessage) {
            // 默认空实现
        }

        @Override
        public void onDownloadCancelled(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadRetry(DownloadInfo downloadInfo, int retryCount) {
            // 默认空实现
        }
    }

    /**
     * 全局下载监听器接口
     * <p>
     * 用于监听所有下载任务的状态变化。
     * </p>
     */
    interface GlobalDownloadListener {

        /**
         * 下载队列状态变化
         *
         * @param activeDownloads 活跃下载数量
         * @param pendingDownloads 等待下载数量
         * @param totalDownloads 总下载数量
         */
        void onDownloadQueueChanged(int activeDownloads, int pendingDownloads, int totalDownloads);

        /**
         * 下载任务添加
         *
         * @param downloadInfo 下载信息
         */
        void onDownloadAdded(DownloadInfo downloadInfo);

        /**
         * 下载任务移除
         *
         * @param downloadInfo 下载信息
         */
        void onDownloadRemoved(DownloadInfo downloadInfo);

        /**
         * 所有下载完成
         */
        void onAllDownloadsComplete();

        /**
         * 网络状态变化
         *
         * @param isNetworkAvailable 网络是否可用
         * @param isWifiConnected 是否连接WiFi
         */
        void onNetworkStateChanged(boolean isNetworkAvailable, boolean isWifiConnected);
    }

    /**
     * 简化的全局下载监听器适配器
     */
    abstract class SimpleGlobalDownloadListener implements GlobalDownloadListener {

        @Override
        public void onDownloadQueueChanged(int activeDownloads, int pendingDownloads, int totalDownloads) {
            // 默认空实现
        }

        @Override
        public void onDownloadAdded(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onDownloadRemoved(DownloadInfo downloadInfo) {
            // 默认空实现
        }

        @Override
        public void onAllDownloadsComplete() {
            // 默认空实现
        }

        @Override
        public void onNetworkStateChanged(boolean isNetworkAvailable, boolean isWifiConnected) {
            // 默认空实现
        }
    }
}
