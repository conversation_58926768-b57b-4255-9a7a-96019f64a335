package com.android.video.model.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.android.video.constants.AuthApiConstantsUtils;

/**
 * 发送验证码请求模型
 * <AUTHOR> Team
 */
public class SendSmsRequestModel {

    /**
     * 手机号
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_PHONE_NUMBER)
    @Expose
    private String phoneNumber;

    /**
     * 默认构造函数
     */
    public SendSmsRequestModel() {
    }

    /**
     * 构造函数
     * @param phoneNumber 手机号
     */
    public SendSmsRequestModel(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    // ========== Getter和Setter方法 ==========

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    // ========== 便利方法 ==========

    /**
     * 验证请求参数是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return phoneNumber != null && !phoneNumber.trim().isEmpty();
    }

    /**
     * 获取验证失败的原因
     * @return 验证失败原因，如果验证通过则返回null
     */
    public String getValidationError() {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return "Phone number is required";
        }
        return null;
    }

    @Override
    public String toString() {
        return "SendSmsRequestModel{" +
                "phoneNumber='" + phoneNumber + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SendSmsRequestModel that = (SendSmsRequestModel) obj;
        
        return phoneNumber != null ? phoneNumber.equals(that.phoneNumber) : that.phoneNumber == null;
    }

    @Override
    public int hashCode() {
        return phoneNumber != null ? phoneNumber.hashCode() : 0;
    }
}
