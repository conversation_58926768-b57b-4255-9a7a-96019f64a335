package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.android.video.network.SubscribeApiService;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import java.util.ArrayList;
import java.util.List;

/**
 * Coming Soon详情页专用RecyclerView适配器
 * 与MostPopular页面的区别：包含Coming Soon标签、上映时间和订阅按钮
 * <AUTHOR>
 */
public class ComingSoonDetailAdapter extends RecyclerView.Adapter<ComingSoonDetailAdapter.ComingSoonDetailViewHolder> {

    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    private OnSubscriptionChangeListener onSubscriptionChangeListener;

    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }

    /**
     * 订阅状态变化监听接口
     */
    public interface OnSubscriptionChangeListener {
        void onSubscriptionChanged(VideoModel video, int position, boolean isSubscribed);
    }

    /**
     * 构造函数
     */
    public ComingSoonDetailAdapter() {
        this.videoList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public ComingSoonDetailAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }

    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }

    /**
     * 设置订阅状态变化监听器
     * @param listener 订阅监听器
     */
    public void setOnSubscriptionChangeListener(OnSubscriptionChangeListener listener) {
        this.onSubscriptionChangeListener = listener;
    }

    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        if (newVideoList != null) {
            this.videoList.clear();
            this.videoList.addAll(newVideoList);
            notifyDataSetChanged();
        }
    }

    /**
     * 添加视频到列表
     * @param video 要添加的视频
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            this.videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }

    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }

    @NonNull
    @Override
    public ComingSoonDetailViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_coming_soon_detail_video, parent, false);
        return new ComingSoonDetailViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ComingSoonDetailViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class ComingSoonDetailViewHolder extends RecyclerView.ViewHolder {

        private ConstraintLayout clPosterContainer;
        private ImageView ivVideoPoster;
        private ConstraintLayout clInfoContainer;
        private TextView tvVideoTitle;
        private TextView tvComingSoonTag;
        private TextView tvVideoDescription;
        private ConstraintLayout clReleaseTime;
        private ImageView ivTimeIcon;
        private TextView tvReleaseTime;
        private ConstraintLayout clNotifyButton;
        private ImageView ivNotifyIcon;
        private TextView tvNotifyText;

        public ComingSoonDetailViewHolder(@NonNull View itemView) {
            super(itemView);
            clPosterContainer = itemView.findViewById(R.id.cl_poster_container);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            clInfoContainer = itemView.findViewById(R.id.cl_info_container);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            tvComingSoonTag = itemView.findViewById(R.id.tv_coming_soon_tag);
            tvVideoDescription = itemView.findViewById(R.id.tv_video_description);
            clReleaseTime = itemView.findViewById(R.id.cl_release_time);
            ivTimeIcon = itemView.findViewById(R.id.iv_time_icon);
            tvReleaseTime = itemView.findViewById(R.id.tv_release_time);
            clNotifyButton = itemView.findViewById(R.id.cl_notify_button);
            ivNotifyIcon = itemView.findViewById(R.id.iv_notify_icon);
            tvNotifyText = itemView.findViewById(R.id.tv_notify_text);
        }

        public void bind(VideoModel video, int position) {
            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());

            // 设置视频描述（最多2行）
            tvVideoDescription.setText(video.getDescription());

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivVideoPoster);
            } else {
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置上映时间
            String releaseDate = video.getReleaseDate();
            if (releaseDate != null && !releaseDate.isEmpty()) {
                tvReleaseTime.setText(releaseDate);
                clReleaseTime.setVisibility(View.VISIBLE);
            } else {
                tvReleaseTime.setText("2025-06-18");
                clReleaseTime.setVisibility(View.VISIBLE);
            }

            // 设置订阅状态
            updateSubscriptionUI(video.isSubscribed());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(),
                            "Coming Soon视频点击: " + video.getDisplayTitle(),
                            Toast.LENGTH_SHORT).show();
                }
            });

            // 设置订阅按钮点击事件
            clNotifyButton.setOnClickListener(v -> {
                // 检查登录状态
                if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(itemView.getContext())) {
                    return;
                }

                // 防止重复点击
                clNotifyButton.setEnabled(false);

                // 获取短剧ID
                String filmId = video.getId();
                if (filmId == null || filmId.trim().isEmpty()) {
                    Toast.makeText(itemView.getContext(), "短剧ID无效", Toast.LENGTH_SHORT).show();
                    clNotifyButton.setEnabled(true);
                    return;
                }

                // 根据当前状态决定操作类型
                boolean currentlySubscribed = video.isSubscribed();
                SubscribeApiService subscribeService = SubscribeApiService.getInstance();

                SubscribeApiService.SubscribeCallback callback = new SubscribeApiService.SubscribeCallback() {
                    @Override
                    public void onSuccess(boolean isSubscribed, String message) {
                        // 更新视频模型状态
                        video.setSubscribed(isSubscribed);
                        // 更新UI
                        updateSubscriptionUI(isSubscribed);
                        // 重新启用按钮
                        clNotifyButton.setEnabled(true);

                        // 通知监听器
                        if (onSubscriptionChangeListener != null) {
                            onSubscriptionChangeListener.onSubscriptionChanged(video, position, isSubscribed);
                        } else {
                            String displayMessage = isSubscribed ? "已订阅: " : "取消订阅: ";
                            Toast.makeText(itemView.getContext(),
                                    displayMessage + video.getDisplayTitle(),
                                    Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onError(String errorMessage) {
                        // 重新启用按钮
                        clNotifyButton.setEnabled(true);
                        // 显示错误信息
                        Toast.makeText(itemView.getContext(),
                                "操作失败: " + errorMessage,
                                Toast.LENGTH_SHORT).show();
                    }
                };

                // 执行订阅或取消订阅操作
                if (currentlySubscribed) {
                    subscribeService.unsubscribe(filmId, callback);
                } else {
                    subscribeService.subscribe(filmId, callback);
                }
            });

            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                String releaseInfo = video.getReleaseDate() != null ?
                        "\n上映时间: " + video.getReleaseDate() : "";
                String subscriptionInfo = video.isSubscribed() ? "\n状态: 已订阅" : "\n状态: 未订阅";
                Toast.makeText(itemView.getContext(),
                        "长按: " + video.getDisplayTitle() +
                        "\n分类: " + video.getCategory() + releaseInfo + subscriptionInfo,
                        Toast.LENGTH_SHORT).show();
                return true;
            });
        }

        /**
         * 更新订阅状态UI
         * @param isSubscribed 是否已订阅
         */
        private void updateSubscriptionUI(boolean isSubscribed) {
            // 设置按钮选中状态
            clNotifyButton.setSelected(isSubscribed);

            if (isSubscribed) {
                ivNotifyIcon.setImageResource(R.drawable.home_ic_notify_1);
                tvNotifyText.setText("Notified");
                tvNotifyText.setTextColor(0x80FFFFFF); // 半透明白色
            } else {
                ivNotifyIcon.setImageResource(R.drawable.home_ic_notify_0);
                tvNotifyText.setText("Notify me");
                tvNotifyText.setTextColor(0xFFFFFFFF); // 纯白色
            }
        }
    }
}
