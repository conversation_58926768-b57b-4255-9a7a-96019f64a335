package com.android.video.utils;

import android.util.Log;
import com.android.video.constants.BaseApiConstantsUtils;
import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.UnknownHostException;
import javax.net.ssl.SSLException;

/**
 * API异常处理工具类 - 统一处理网络请求异常
 * <AUTHOR> Team
 */
public class ApiExceptionUtils {

    private static final String TAG = "ApiExceptionUtils";

    /**
     * API异常类型枚举
     */
    public enum ApiExceptionType {
        NETWORK_ERROR,      // 网络错误
        TIMEOUT_ERROR,      // 超时错误
        SERVER_ERROR,       // 服务器错误
        PARSE_ERROR,        // 解析错误
        SSL_ERROR,          // SSL错误
        UNKNOWN_ERROR       // 未知错误
    }

    /**
     * API异常信息类
     */
    public static class ApiException extends Exception {
        private final ApiExceptionType type;
        private final String code;
        private final String userMessage;

        public ApiException(ApiExceptionType type, String code, String message, String userMessage) {
            super(message);
            this.type = type;
            this.code = code;
            this.userMessage = userMessage;
        }

        public ApiException(ApiExceptionType type, String code, String message, String userMessage, Throwable cause) {
            super(message, cause);
            this.type = type;
            this.code = code;
            this.userMessage = userMessage;
        }

        public ApiExceptionType getType() {
            return type;
        }

        public String getCode() {
            return code;
        }

        public String getUserMessage() {
            return userMessage;
        }
    }

    /**
     * 处理网络异常
     * @param throwable 异常对象
     * @return API异常信息
     */
    public static ApiException handleException(Throwable throwable) {
        if (throwable == null) {
            return createUnknownException("Unknown error occurred");
        }

        Log.e(TAG, "Handling exception: " + throwable.getClass().getSimpleName() + " - " + throwable.getMessage());

        // 网络连接异常
        if (throwable instanceof ConnectException) {
            return new ApiException(
                ApiExceptionType.NETWORK_ERROR,
                "NETWORK_CONNECTION_ERROR",
                "Connection failed: " + throwable.getMessage(),
                "Network connection failed. Please check your internet connection."
            );
        }

        // 超时异常
        if (throwable instanceof SocketTimeoutException) {
            return new ApiException(
                ApiExceptionType.TIMEOUT_ERROR,
                "NETWORK_TIMEOUT",
                "Request timeout: " + throwable.getMessage(),
                "Request timeout. Please try again."
            );
        }

        // 主机名解析异常
        if (throwable instanceof UnknownHostException) {
            return new ApiException(
                ApiExceptionType.NETWORK_ERROR,
                "UNKNOWN_HOST",
                "Unknown host: " + throwable.getMessage(),
                "Cannot connect to server. Please check your internet connection."
            );
        }

        // SSL异常
        if (throwable instanceof SSLException) {
            return new ApiException(
                ApiExceptionType.SSL_ERROR,
                "SSL_ERROR",
                "SSL error: " + throwable.getMessage(),
                "Secure connection failed. Please try again."
            );
        }

        // 一般IO异常
        if (throwable instanceof IOException) {
            return new ApiException(
                ApiExceptionType.NETWORK_ERROR,
                "IO_ERROR",
                "IO error: " + throwable.getMessage(),
                "Network error occurred. Please try again."
            );
        }

        // JSON解析异常
        if (throwable.getClass().getSimpleName().contains("Json") || 
            throwable.getMessage() != null && throwable.getMessage().contains("JSON")) {
            return new ApiException(
                ApiExceptionType.PARSE_ERROR,
                "JSON_PARSE_ERROR",
                "JSON parse error: " + throwable.getMessage(),
                "Data format error. Please try again."
            );
        }

        // 其他未知异常
        return createUnknownException(throwable.getMessage());
    }

    /**
     * 处理HTTP状态码异常
     * @param httpCode HTTP状态码
     * @param responseBody 响应体
     * @return API异常信息
     */
    public static ApiException handleHttpException(int httpCode, String responseBody) {
        Log.e(TAG, "HTTP error: " + httpCode + ", Response: " + responseBody);

        String userMessage;
        String code;
        ApiExceptionType type;

        switch (httpCode) {
            case 400:
                type = ApiExceptionType.SERVER_ERROR;
                code = BaseApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR;
                userMessage = "Invalid request. Please check your input.";
                break;
            case 401:
                type = ApiExceptionType.SERVER_ERROR;
                code = BaseApiConstantsUtils.RESPONSE_CODE_UNAUTHORIZED;
                userMessage = "Authentication failed. Please login again.";
                break;
            case 403:
                type = ApiExceptionType.SERVER_ERROR;
                code = "403";
                userMessage = "Access denied. You don't have permission.";
                break;
            case 404:
                type = ApiExceptionType.SERVER_ERROR;
                code = BaseApiConstantsUtils.RESPONSE_CODE_ERROR;
                userMessage = "Resource not found.";
                break;
            case 500:
                type = ApiExceptionType.SERVER_ERROR;
                code = BaseApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR;
                userMessage = "Server error. Please try again later.";
                break;
            case 502:
            case 503:
            case 504:
                type = ApiExceptionType.SERVER_ERROR;
                code = String.valueOf(httpCode);
                userMessage = "Server is temporarily unavailable. Please try again later.";
                break;
            default:
                type = ApiExceptionType.SERVER_ERROR;
                code = String.valueOf(httpCode);
                userMessage = "Server error occurred. Please try again.";
                break;
        }

        return new ApiException(
            type,
            code,
            "HTTP " + httpCode + ": " + responseBody,
            userMessage
        );
    }

    /**
     * 处理业务异常
     * @param code 业务错误码
     * @param message 错误消息
     * @return API异常信息
     */
    public static ApiException handleBusinessException(String code, String message) {
        Log.e(TAG, "Business error: " + code + " - " + message);

        String userMessage;
        
        // 根据业务错误码提供用户友好的消息
        if (BaseApiConstantsUtils.RESPONSE_CODE_PARAM_ERROR.equals(code)) {
            userMessage = "Invalid parameters. Please check your input.";
        } else if (BaseApiConstantsUtils.RESPONSE_CODE_UNAUTHORIZED.equals(code)) {
            userMessage = "Authentication failed. Please login again.";
        } else if (BaseApiConstantsUtils.RESPONSE_CODE_SERVER_ERROR.equals(code)) {
            userMessage = "Server error. Please try again later.";
        } else {
            userMessage = message != null && !message.isEmpty() ? message : "Operation failed. Please try again.";
        }

        return new ApiException(
            ApiExceptionType.SERVER_ERROR,
            code,
            "Business error: " + message,
            userMessage
        );
    }

    /**
     * 创建未知异常
     * @param message 错误消息
     * @return API异常信息
     */
    private static ApiException createUnknownException(String message) {
        return new ApiException(
            ApiExceptionType.UNKNOWN_ERROR,
            "UNKNOWN_ERROR",
            "Unknown error: " + message,
            "An unexpected error occurred. Please try again."
        );
    }

    /**
     * 获取异常的用户友好消息
     * @param throwable 异常对象
     * @return 用户友好的错误消息
     */
    public static String getUserFriendlyMessage(Throwable throwable) {
        ApiException apiException = handleException(throwable);
        return apiException.getUserMessage();
    }

    /**
     * 检查异常是否为网络相关
     * @param throwable 异常对象
     * @return 是否为网络异常
     */
    public static boolean isNetworkException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        return throwable instanceof ConnectException ||
               throwable instanceof SocketTimeoutException ||
               throwable instanceof UnknownHostException ||
               throwable instanceof IOException;
    }

    /**
     * 检查异常是否可重试
     * @param throwable 异常对象
     * @return 是否可重试
     */
    public static boolean isRetryableException(Throwable throwable) {
        if (throwable == null) {
            return false;
        }
        
        return throwable instanceof SocketTimeoutException ||
               throwable instanceof ConnectException ||
               throwable instanceof UnknownHostException;
    }

    /**
     * 记录异常信息
     * @param tag 日志标签
     * @param throwable 异常对象
     */
    public static void logException(String tag, Throwable throwable) {
        if (throwable != null) {
            ApiException apiException = handleException(throwable);
            Log.e(tag, "API Exception - Type: " + apiException.getType() + 
                      ", Code: " + apiException.getCode() + 
                      ", Message: " + apiException.getMessage());
        }
    }
}
