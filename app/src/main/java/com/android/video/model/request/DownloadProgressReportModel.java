package com.android.video.model.request;

import com.google.gson.annotations.SerializedName;

/**
 * 下载进度上报请求数据模型
 * <p>
 * 用于向服务器上报用户的视频下载进度信息。
 * 包含下载记录ID、下载进度和下载状态等信息。
 * </p>
 * 
 * <p>
 * 请求格式：
 * <pre>
 * {
 *   "downloadRecordId": "2d9bf4f909fef05528998764daa94c60",
 *   "downloadProgress": 50,
 *   "downloadStatus": 2
 * }
 * </pre>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * DownloadProgressReportModel request = new DownloadProgressReportModel(
 *     "2d9bf4f909fef05528998764daa94c60",
 *     50,
 *     2
 * );
 * 
 * // 或者使用Builder模式
 * DownloadProgressReportModel request = new DownloadProgressReportModel.Builder()
 *     .downloadRecordId("2d9bf4f909fef05528998764daa94c60")
 *     .downloadProgress(50)
 *     .downloadStatus(2)
 *     .build();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadProgressReportModel {

    // ========== 下载状态常量 ==========

    /**
     * 下载状态：排队中
     */
    public static final int STATUS_QUEUED = 1;

    /**
     * 下载状态：下载中
     */
    public static final int STATUS_DOWNLOADING = 2;

    /**
     * 下载状态：已完成
     */
    public static final int STATUS_COMPLETED = 3;

    /**
     * 下载状态：下载失败
     */
    public static final int STATUS_FAILED = 4;

    // ========== 字段定义 ==========

    /**
     * 下载记录ID
     */
    @SerializedName("downloadRecordId")
    private String downloadRecordId;

    /**
     * 下载进度（0-100）
     */
    @SerializedName("downloadProgress")
    private int downloadProgress;

    /**
     * 下载状态
     * 1=排队中, 2=下载中, 3=已完成, 4=下载失败
     */
    @SerializedName("downloadStatus")
    private int downloadStatus;

    /**
     * 默认构造函数
     */
    public DownloadProgressReportModel() {
    }

    /**
     * 完整构造函数
     *
     * @param downloadRecordId 下载记录ID
     * @param downloadProgress 下载进度（0-100）
     * @param downloadStatus 下载状态（1=排队中, 2=下载中, 3=已完成, 4=下载失败）
     */
    public DownloadProgressReportModel(String downloadRecordId, int downloadProgress, int downloadStatus) {
        this.downloadRecordId = downloadRecordId;
        this.downloadProgress = downloadProgress;
        this.downloadStatus = downloadStatus;
    }

    // ========== Getter和Setter方法 ==========

    public String getDownloadRecordId() {
        return downloadRecordId;
    }

    public void setDownloadRecordId(String downloadRecordId) {
        this.downloadRecordId = downloadRecordId;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }

    public int getDownloadStatus() {
        return downloadStatus;
    }

    public void setDownloadStatus(int downloadStatus) {
        this.downloadStatus = downloadStatus;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否下载完成
     *
     * @return 如果下载完成返回true，否则返回false
     */
    public boolean isDownloadCompleted() {
        return downloadStatus == STATUS_COMPLETED;
    }

    /**
     * 检查是否正在下载
     *
     * @return 如果正在下载返回true，否则返回false
     */
    public boolean isDownloading() {
        return downloadStatus == STATUS_DOWNLOADING;
    }

    /**
     * 检查是否下载失败
     *
     * @return 如果下载失败返回true，否则返回false
     */
    public boolean isDownloadFailed() {
        return downloadStatus == STATUS_FAILED;
    }

    /**
     * 检查是否在排队中
     *
     * @return 如果在排队中返回true，否则返回false
     */
    public boolean isQueued() {
        return downloadStatus == STATUS_QUEUED;
    }

    /**
     * 获取下载状态描述
     *
     * @return 下载状态的文字描述
     */
    public String getStatusDescription() {
        switch (downloadStatus) {
            case STATUS_QUEUED:
                return "排队中";
            case STATUS_DOWNLOADING:
                return "下载中";
            case STATUS_COMPLETED:
                return "已完成";
            case STATUS_FAILED:
                return "下载失败";
            default:
                return "未知状态";
        }
    }

    /**
     * 验证请求数据是否有效
     *
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return downloadRecordId != null && !downloadRecordId.trim().isEmpty()
                && downloadProgress >= 0 && downloadProgress <= 100
                && (downloadStatus >= STATUS_QUEUED && downloadStatus <= STATUS_FAILED);
    }

    /**
     * 获取验证错误信息
     *
     * @return 如果数据无效，返回错误信息；否则返回null
     */
    public String getValidationError() {
        if (downloadRecordId == null || downloadRecordId.trim().isEmpty()) {
            return "downloadRecordId cannot be null or empty";
        }
        if (downloadProgress < 0 || downloadProgress > 100) {
            return "downloadProgress must be between 0 and 100";
        }
        if (downloadStatus < STATUS_QUEUED || downloadStatus > STATUS_FAILED) {
            return "downloadStatus must be between 1 and 4";
        }
        return null;
    }

    @Override
    public String toString() {
        return "DownloadProgressReportModel{" +
                "downloadRecordId='" + downloadRecordId + '\'' +
                ", downloadProgress=" + downloadProgress +
                ", downloadStatus=" + downloadStatus +
                " (" + getStatusDescription() + ")" +
                '}';
    }

    /**
     * Builder模式构建器
     */
    public static class Builder {
        private String downloadRecordId;
        private int downloadProgress;
        private int downloadStatus;

        public Builder downloadRecordId(String downloadRecordId) {
            this.downloadRecordId = downloadRecordId;
            return this;
        }

        public Builder downloadProgress(int downloadProgress) {
            this.downloadProgress = downloadProgress;
            return this;
        }

        public Builder downloadStatus(int downloadStatus) {
            this.downloadStatus = downloadStatus;
            return this;
        }

        /**
         * 设置下载状态为排队中
         */
        public Builder statusQueued() {
            this.downloadStatus = STATUS_QUEUED;
            return this;
        }

        /**
         * 设置下载状态为下载中
         */
        public Builder statusDownloading() {
            this.downloadStatus = STATUS_DOWNLOADING;
            return this;
        }

        /**
         * 设置下载状态为已完成
         */
        public Builder statusCompleted() {
            this.downloadStatus = STATUS_COMPLETED;
            return this;
        }

        /**
         * 设置下载状态为下载失败
         */
        public Builder statusFailed() {
            this.downloadStatus = STATUS_FAILED;
            return this;
        }

        public DownloadProgressReportModel build() {
            return new DownloadProgressReportModel(downloadRecordId, downloadProgress, downloadStatus);
        }
    }
}
