<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="180dp"
    android:background="@drawable/floating_video_window_bg">

    <!-- 视频播放器容器 -->
    <FrameLayout
        android:id="@+id/video_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="2dp">

        <!-- ExoPlayer PlayerView -->
        <com.google.android.exoplayer2.ui.PlayerView
            android:id="@+id/player_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="#000000" />

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/loading_indicator"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_gravity="center"
            android:indeterminateTint="#ffffff"
            android:visibility="gone" />

        <!-- 播放/暂停按钮覆盖层 -->
        <ImageView
            android:id="@+id/play_pause_overlay"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center"
            android:background="@drawable/floating_video_play_button_bg"
            android:src="@drawable/play_ic_play"
            android:scaleType="center"
            android:visibility="gone" />

    </FrameLayout>

    <!-- 控制按钮区域 -->
    <LinearLayout
        android:id="@+id/control_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|center"
        android:orientation="horizontal"
        android:background="@drawable/floating_video_control_bg"
        android:paddingHorizontal="12dp"
        android:paddingVertical="8dp"
        android:layout_marginBottom="12dp"
        android:visibility="gone">

        <!-- 播放/暂停按钮 -->
        <ImageView
            android:id="@+id/btn_play_pause"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/play_ic_play"
            android:background="@drawable/floating_video_play_button_bg"
            android:scaleType="center"
            android:layout_marginEnd="12dp"
            android:padding="6dp" />

        <!-- 返回应用按钮 -->
        <ImageView
            android:id="@+id/btn_return_app"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_open_in_full"
            android:background="@drawable/floating_video_play_button_bg"
            android:scaleType="center"
            android:layout_marginEnd="12dp"
            android:padding="6dp" />

        <!-- 关闭按钮 -->
        <ImageView
            android:id="@+id/btn_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_close"
            android:background="@drawable/floating_video_play_button_bg"
            android:scaleType="center"
            android:padding="6dp" />

    </LinearLayout>

    <!-- 拖拽手柄（不可见，用于拖拽检测） -->
    <View
        android:id="@+id/drag_handle"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@android:color/transparent" />

</FrameLayout>
