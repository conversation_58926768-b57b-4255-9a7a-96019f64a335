package com.android.video.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.ActorModel;
import java.util.ArrayList;
import java.util.List;

/**
 * 演员列表RecyclerView适配器
 * 用于显示视频详情页面的演员信息，包括头像和姓名
 * <AUTHOR>
 */
public class ActorAdapter extends RecyclerView.Adapter<ActorAdapter.ActorViewHolder> {

    private Context context;
    private List<ActorModel> actorList;
    private OnActorClickListener onActorClickListener;

    /**
     * 演员点击监听接口
     */
    public interface OnActorClickListener {
        void onActorClick(ActorModel actor, int position);
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public ActorAdapter(Context context) {
        this.context = context;
        this.actorList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param actorList 演员列表
     */
    public ActorAdapter(Context context, List<ActorModel> actorList) {
        this.context = context;
        this.actorList = actorList != null ? actorList : new ArrayList<>();
    }

    /**
     * 设置演员点击监听器
     * @param listener 点击监听器
     */
    public void setOnActorClickListener(OnActorClickListener listener) {
        this.onActorClickListener = listener;
    }

    /**
     * 更新演员列表
     * @param newActorList 新的演员列表
     */
    public void updateActors(List<ActorModel> newActorList) {
        this.actorList = newActorList != null ? newActorList : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 添加演员到列表
     * @param actor 演员数据
     */
    public void addActor(ActorModel actor) {
        if (actor != null && !actor.isEmpty()) {
            actorList.add(actor);
            notifyItemInserted(actorList.size() - 1);
        }
    }

    /**
     * 移除指定位置的演员
     * @param position 位置
     */
    public void removeActor(int position) {
        if (position >= 0 && position < actorList.size()) {
            actorList.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * 获取演员列表
     * @return 演员列表
     */
    public List<ActorModel> getActorList() {
        return new ArrayList<>(actorList);
    }

    @NonNull
    @Override
    public ActorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_actor, parent, false);
        return new ActorViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ActorViewHolder holder, int position) {
        ActorModel actor = actorList.get(position);
        holder.bind(actor, position);
    }

    @Override
    public int getItemCount() {
        return actorList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class ActorViewHolder extends RecyclerView.ViewHolder {

        private ImageView ivActorAvatar;
        private TextView tvActorName;

        public ActorViewHolder(@NonNull View itemView) {
            super(itemView);
            ivActorAvatar = itemView.findViewById(R.id.iv_actor_avatar);
            tvActorName = itemView.findViewById(R.id.tv_actor_name);
        }

        /**
         * 绑定数据
         * @param actor 演员数据
         * @param position 位置
         */
        public void bind(ActorModel actor, int position) {
            if (actor == null) {
                return;
            }

            // 设置演员头像 (使用movie_poster作为占位图)
            if (actor.hasAvatar()) {
                // 使用Glide加载网络图片
                Glide.with(itemView.getContext())
                        .load(actor.getAvatarUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivActorAvatar);
            } else {
                ivActorAvatar.setImageResource(R.drawable.movie_poster);
            }

            // 设置演员姓名
            tvActorName.setText(actor.getDisplayName());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onActorClickListener != null) {
                    onActorClickListener.onActorClick(actor, position);
                }
            });

            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                // 可以显示演员详细信息或其他操作
                return true;
            });
        }
    }
}
