<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/video_detail_episode_tag_width"
    android:layout_height="wrap_content"
    android:clickable="true"
    android:focusable="true">

    <!-- 选集标签主体 -->
    <FrameLayout
        android:id="@+id/ll_episode_content"
        android:layout_width="@dimen/video_detail_episode_tag_width"
        android:layout_height="@dimen/video_detail_episode_tag_height"
        android:background="@drawable/detail_episode_selector">

        <!-- 水平布局：文字和直播图标 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:orientation="horizontal"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_episode_number"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:textSize="@dimen/video_detail_episode_text_size"
                android:textColor="@android:color/white"
                android:textStyle="bold"
                android:text="1" />

            <!-- 直播图标在文字右边 -->
            <ImageView
                android:id="@+id/iv_live_icon"
                android:layout_width="@dimen/video_detail_episode_icon_size"
                android:layout_height="@dimen/video_detail_episode_icon_size"
                android:layout_marginStart="@dimen/video_detail_episode_icon_margin_start"
                android:src="@drawable/movie_ic_zhibo"
                android:visibility="gone"
                android:scaleType="centerInside" />

        </LinearLayout>

        <!-- 锁定图标浮在文字上方 -->
        <ImageView
            android:id="@+id/iv_lock_icon"
            android:layout_width="@dimen/video_detail_episode_icon_size"
            android:layout_height="@dimen/video_detail_episode_icon_size"
            android:layout_gravity="center"
            android:src="@drawable/ic_lock"
            android:visibility="gone"
            android:scaleType="centerInside" />

    </FrameLayout>

</RelativeLayout>
