package com.android.video.utils;

import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Matrix;
import android.media.ExifInterface;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 图片压缩工具类
 * <p>
 * 提供图片压缩功能，主要用于头像上传前的图片处理。
 * 支持按文件大小和图片尺寸进行压缩。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class ImageCompressUtils {

    private static final String TAG = "ImageCompressUtils";
    
    // 默认最大文件大小：1MB
    private static final long DEFAULT_MAX_SIZE = 1024 * 1024;
    
    // 默认最大图片宽度
    private static final int DEFAULT_MAX_WIDTH = 1080;
    
    // 默认最大图片高度
    private static final int DEFAULT_MAX_HEIGHT = 1080;

    /**
     * 压缩图片文件到指定大小以下
     * 
     * @param sourceFile 源图片文件
     * @param targetFile 目标压缩文件
     * @param maxSizeBytes 最大文件大小（字节）
     * @return 是否压缩成功
     */
    public static boolean compressImage(File sourceFile, File targetFile, long maxSizeBytes) {
        if (sourceFile == null || !sourceFile.exists()) {
            Log.e(TAG, "源文件不存在");
            return false;
        }

        try {
            Log.d(TAG, "开始压缩图片: " + sourceFile.getAbsolutePath());
            Log.d(TAG, "原始文件大小: " + sourceFile.length() + " bytes");
            Log.d(TAG, "目标大小限制: " + maxSizeBytes + " bytes");

            // 如果原文件已经小于限制大小，直接复制
            if (sourceFile.length() <= maxSizeBytes) {
                return copyFile(sourceFile, targetFile);
            }

            // 获取图片的原始尺寸
            BitmapFactory.Options options = new BitmapFactory.Options();
            options.inJustDecodeBounds = true;
            BitmapFactory.decodeFile(sourceFile.getAbsolutePath(), options);
            
            int originalWidth = options.outWidth;
            int originalHeight = options.outHeight;
            Log.d(TAG, "原始图片尺寸: " + originalWidth + "x" + originalHeight);

            // 计算合适的采样率
            int sampleSize = calculateSampleSize(originalWidth, originalHeight, DEFAULT_MAX_WIDTH, DEFAULT_MAX_HEIGHT);
            Log.d(TAG, "计算的采样率: " + sampleSize);

            // 解码图片
            options.inJustDecodeBounds = false;
            options.inSampleSize = sampleSize;
            options.inPreferredConfig = Bitmap.Config.RGB_565; // 使用RGB_565减少内存占用
            
            Bitmap bitmap = BitmapFactory.decodeFile(sourceFile.getAbsolutePath(), options);
            if (bitmap == null) {
                Log.e(TAG, "无法解码图片");
                return false;
            }

            // 处理图片旋转
            bitmap = rotateImageIfRequired(bitmap, sourceFile.getAbsolutePath());

            // 尝试不同的压缩质量
            int quality = 90;
            boolean success = false;
            
            while (quality >= 10 && !success) {
                success = saveBitmapToFile(bitmap, targetFile, quality);
                
                if (success && targetFile.length() <= maxSizeBytes) {
                    Log.d(TAG, "压缩成功! 质量: " + quality + ", 文件大小: " + targetFile.length() + " bytes");
                    break;
                } else if (success) {
                    Log.d(TAG, "质量 " + quality + " 压缩后仍然过大: " + targetFile.length() + " bytes");
                    success = false;
                }
                
                quality -= 10;
            }

            // 如果降低质量还是太大，尝试缩小尺寸
            if (!success || targetFile.length() > maxSizeBytes) {
                Log.d(TAG, "降低质量无效，尝试缩小尺寸");
                success = compressWithResize(bitmap, targetFile, maxSizeBytes);
            }

            bitmap.recycle();
            return success;

        } catch (Exception e) {
            Log.e(TAG, "图片压缩失败", e);
            return false;
        }
    }

    /**
     * 压缩图片文件（使用默认1MB限制）
     */
    public static boolean compressImage(File sourceFile, File targetFile) {
        return compressImage(sourceFile, targetFile, DEFAULT_MAX_SIZE);
    }

    /**
     * 计算采样率
     */
    private static int calculateSampleSize(int width, int height, int maxWidth, int maxHeight) {
        int sampleSize = 1;
        
        if (height > maxHeight || width > maxWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;
            
            while ((halfHeight / sampleSize) >= maxHeight && (halfWidth / sampleSize) >= maxWidth) {
                sampleSize *= 2;
            }
        }
        
        return sampleSize;
    }

    /**
     * 处理图片旋转（根据EXIF信息）
     */
    private static Bitmap rotateImageIfRequired(Bitmap bitmap, String imagePath) {
        try {
            ExifInterface exif = new ExifInterface(imagePath);
            int orientation = exif.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_NORMAL);
            
            switch (orientation) {
                case ExifInterface.ORIENTATION_ROTATE_90:
                    return rotateBitmap(bitmap, 90);
                case ExifInterface.ORIENTATION_ROTATE_180:
                    return rotateBitmap(bitmap, 180);
                case ExifInterface.ORIENTATION_ROTATE_270:
                    return rotateBitmap(bitmap, 270);
                default:
                    return bitmap;
            }
        } catch (IOException e) {
            Log.w(TAG, "无法读取EXIF信息", e);
            return bitmap;
        }
    }

    /**
     * 旋转Bitmap
     */
    private static Bitmap rotateBitmap(Bitmap bitmap, float degrees) {
        Matrix matrix = new Matrix();
        matrix.postRotate(degrees);
        return Bitmap.createBitmap(bitmap, 0, 0, bitmap.getWidth(), bitmap.getHeight(), matrix, true);
    }

    /**
     * 保存Bitmap到文件
     */
    private static boolean saveBitmapToFile(Bitmap bitmap, File file, int quality) {
        try (FileOutputStream fos = new FileOutputStream(file)) {
            return bitmap.compress(Bitmap.CompressFormat.JPEG, quality, fos);
        } catch (IOException e) {
            Log.e(TAG, "保存图片失败", e);
            return false;
        }
    }

    /**
     * 通过缩小尺寸进行压缩
     */
    private static boolean compressWithResize(Bitmap originalBitmap, File targetFile, long maxSizeBytes) {
        float scale = 0.8f;
        
        while (scale > 0.1f) {
            int newWidth = (int) (originalBitmap.getWidth() * scale);
            int newHeight = (int) (originalBitmap.getHeight() * scale);
            
            Log.d(TAG, "尝试缩放到: " + newWidth + "x" + newHeight + " (缩放比例: " + scale + ")");
            
            Bitmap scaledBitmap = Bitmap.createScaledBitmap(originalBitmap, newWidth, newHeight, true);
            
            boolean success = saveBitmapToFile(scaledBitmap, targetFile, 80);
            scaledBitmap.recycle();
            
            if (success && targetFile.length() <= maxSizeBytes) {
                Log.d(TAG, "缩放压缩成功! 最终大小: " + targetFile.length() + " bytes");
                return true;
            }
            
            scale -= 0.1f;
        }
        
        Log.e(TAG, "无法将图片压缩到指定大小");
        return false;
    }

    /**
     * 复制文件
     */
    private static boolean copyFile(File sourceFile, File targetFile) {
        try (java.io.FileInputStream fis = new java.io.FileInputStream(sourceFile);
             java.io.FileOutputStream fos = new java.io.FileOutputStream(targetFile)) {
            
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            
            Log.d(TAG, "文件复制成功，大小: " + targetFile.length() + " bytes");
            return true;
            
        } catch (IOException e) {
            Log.e(TAG, "文件复制失败", e);
            return false;
        }
    }

    /**
     * 获取压缩后的文件大小（KB）
     */
    public static String getFileSizeKB(File file) {
        if (file == null || !file.exists()) {
            return "0KB";
        }
        return (file.length() / 1024) + "KB";
    }

    /**
     * 检查文件是否需要压缩
     */
    public static boolean needsCompression(File file, long maxSizeBytes) {
        return file != null && file.exists() && file.length() > maxSizeBytes;
    }
}
