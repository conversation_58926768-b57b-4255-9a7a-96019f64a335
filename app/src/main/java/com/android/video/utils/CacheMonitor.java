package com.android.video.utils;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.network.ApiClientUtils;

import java.io.File;

/**
 * 缓存监控工具
 * 监控和报告缓存使用情况
 * <AUTHOR> Team
 */
public class CacheMonitor {
    
    private static final String TAG = "CacheMonitor";
    private static final long MONITOR_INTERVAL = 30000; // 30秒监控一次
    
    private static CacheMonitor instance;
    private Context context;
    private Handler handler;
    private Runnable monitorRunnable;
    private boolean isMonitoring = false;
    
    // 缓存统计信息
    private long lastHttpCacheSize = 0;
    private long lastImageCacheSize = 0;
    
    public interface CacheMonitorListener {
        void onCacheStatsUpdated(CacheStats stats);
        void onCacheWarning(String warning);
    }
    
    private CacheMonitorListener listener;
    
    private CacheMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.handler = new Handler(Looper.getMainLooper());
        initializeMonitoring();
    }
    
    public static synchronized CacheMonitor getInstance(Context context) {
        if (instance == null) {
            instance = new CacheMonitor(context);
        }
        return instance;
    }
    
    /**
     * 设置监控监听器
     */
    public void setListener(CacheMonitorListener listener) {
        this.listener = listener;
    }
    
    /**
     * 开始监控
     */
    public void startMonitoring() {
        if (!isMonitoring) {
            isMonitoring = true;
            handler.post(monitorRunnable);
            Log.d(TAG, "Cache monitoring started");
        }
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (isMonitoring) {
            isMonitoring = false;
            handler.removeCallbacks(monitorRunnable);
            Log.d(TAG, "Cache monitoring stopped");
        }
    }
    
    /**
     * 初始化监控
     */
    private void initializeMonitoring() {
        monitorRunnable = new Runnable() {
            @Override
            public void run() {
                if (isMonitoring) {
                    collectCacheStats();
                    handler.postDelayed(this, MONITOR_INTERVAL);
                }
            }
        };
    }
    
    /**
     * 收集缓存统计信息
     */
    private void collectCacheStats() {
        try {
            // HTTP缓存统计
            long httpCacheSize = ApiClientUtils.getCacheSize();
            File httpCacheDir = ApiClientUtils.getCacheDirectory();
            
            // 图片缓存统计（估算）
            long imageCacheSize = getImageCacheSize();
            
            // 创建统计对象
            CacheStats stats = new CacheStats(
                httpCacheSize,
                imageCacheSize,
                httpCacheDir != null ? httpCacheDir.getAbsolutePath() : "Unknown",
                System.currentTimeMillis()
            );
            
            // 检查缓存变化
            checkCacheChanges(stats);
            
            // 通知监听器
            if (listener != null) {
                listener.onCacheStatsUpdated(stats);
            }
            
            // 更新上次记录
            lastHttpCacheSize = httpCacheSize;
            lastImageCacheSize = imageCacheSize;
            
        } catch (Exception e) {
            Log.e(TAG, "Error collecting cache stats", e);
        }
    }
    
    /**
     * 检查缓存变化
     */
    private void checkCacheChanges(CacheStats stats) {
        // 检查HTTP缓存增长
        if (stats.httpCacheSize > lastHttpCacheSize) {
            long growth = stats.httpCacheSize - lastHttpCacheSize;
            Log.d(TAG, "HTTP cache grew by " + formatBytes(growth));
        }
        
        // 检查缓存大小警告
        long totalCacheSize = stats.httpCacheSize + stats.imageCacheSize;
        if (totalCacheSize > 100 * 1024 * 1024) { // 100MB
            String warning = "Total cache size is large: " + formatBytes(totalCacheSize);
            Log.w(TAG, warning);
            if (listener != null) {
                listener.onCacheWarning(warning);
            }
        }
    }
    
    /**
     * 获取图片缓存大小（估算）
     */
    private long getImageCacheSize() {
        try {
            // Glide缓存目录
            File imageCacheDir = new File(context.getCacheDir(), "image_cache");
            return calculateDirectorySize(imageCacheDir);
        } catch (Exception e) {
            Log.e(TAG, "Error calculating image cache size", e);
            return 0;
        }
    }
    
    /**
     * 计算目录大小
     */
    private long calculateDirectorySize(File directory) {
        if (directory == null || !directory.exists() || !directory.isDirectory()) {
            return 0;
        }
        
        long size = 0;
        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    size += calculateDirectorySize(file);
                } else {
                    size += file.length();
                }
            }
        }
        return size;
    }
    
    /**
     * 格式化字节数
     */
    private String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.1f KB", bytes / 1024.0);
        } else if (bytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
        }
    }
    
    /**
     * 获取当前缓存统计信息
     */
    public void getCurrentCacheStats(CacheStatsCallback callback) {
        new Thread(() -> {
            try {
                long httpCacheSize = ApiClientUtils.getCacheSize();
                long imageCacheSize = getImageCacheSize();
                File httpCacheDir = ApiClientUtils.getCacheDirectory();
                
                CacheStats stats = new CacheStats(
                    httpCacheSize,
                    imageCacheSize,
                    httpCacheDir != null ? httpCacheDir.getAbsolutePath() : "Unknown",
                    System.currentTimeMillis()
                );
                
                handler.post(() -> callback.onStatsReady(stats));
            } catch (Exception e) {
                handler.post(() -> callback.onError(e));
            }
        }).start();
    }
    
    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        public final long httpCacheSize;
        public final long imageCacheSize;
        public final String httpCacheDirectory;
        public final long timestamp;
        
        public CacheStats(long httpCacheSize, long imageCacheSize, 
                         String httpCacheDirectory, long timestamp) {
            this.httpCacheSize = httpCacheSize;
            this.imageCacheSize = imageCacheSize;
            this.httpCacheDirectory = httpCacheDirectory;
            this.timestamp = timestamp;
        }
        
        public long getTotalCacheSize() {
            return httpCacheSize + imageCacheSize;
        }
        
        @Override
        public String toString() {
            return "CacheStats{" +
                    "httpCache=" + formatBytes(httpCacheSize) +
                    ", imageCache=" + formatBytes(imageCacheSize) +
                    ", total=" + formatBytes(getTotalCacheSize()) +
                    ", directory='" + httpCacheDirectory + '\'' +
                    '}';
        }
        
        private String formatBytes(long bytes) {
            if (bytes < 1024) {
                return bytes + "B";
            } else if (bytes < 1024 * 1024) {
                return String.format("%.1fKB", bytes / 1024.0);
            } else {
                return String.format("%.1fMB", bytes / (1024.0 * 1024.0));
            }
        }
    }
    
    /**
     * 缓存统计回调接口
     */
    public interface CacheStatsCallback {
        void onStatsReady(CacheStats stats);
        void onError(Exception e);
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        stopMonitoring();
        listener = null;
    }
}
