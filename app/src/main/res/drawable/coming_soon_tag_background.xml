<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 主背景 -->
    <item>
        <shape android:shape="rectangle">
            <!-- 背景色 rgba(185, 54, 207, 0.14) -->
            <solid android:color="#24B936CF" />
            <!-- 圆角 6dp -->
            <corners android:radius="6dp" />
        </shape>
    </item>
    
    <!-- 内阴影效果 -->
    <item
        android:left="1dp"
        android:top="1dp"
        android:right="1dp"
        android:bottom="1dp">
        <shape android:shape="rectangle">
            <!-- 内阴影颜色 rgba(255, 255, 255, 0.50) -->
            <stroke 
                android:width="1dp"
                android:color="#80FFFFFF" />
            <!-- 圆角 5dp (比外层小1dp) -->
            <corners android:radius="5dp" />
        </shape>
    </item>
    
</layer-list>
