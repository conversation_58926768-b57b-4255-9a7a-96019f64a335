package com.android.video.model.response;

import com.android.video.model.*;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 视频详情响应数据模型
 * <p>
 * 用于映射视频详情API返回的JSON数据结构。
 * 包含视频的详细信息，如短剧信息、演员、导演、章节列表等。
 * </p>
 *
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "filmInfo": {...},
 *     "labelNames": "",
 *     "releaseTime": null,
 *     "directorInfo": [...],
 *     "performerInfo": [...],
 *     "playProgressVO": {...},
 *     "chapterList": [...]
 *   }
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VideoDetailResponseModel {

    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;

    /**
     * 视频详情数据
     */
    @SerializedName("data")
    private VideoDetailData data;

    /**
     * 默认构造函数
     */
    public VideoDetailResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 视频详情数据
     */
    public VideoDetailResponseModel(String code, String message, VideoDetailData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public VideoDetailData getData() {
        return data;
    }

    public void setData(VideoDetailData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    @Override
    public String toString() {
        return "VideoDetailResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 视频详情数据
     * <p>
     * 包含视频的完整详情信息。
     * </p>
     */
    public static class VideoDetailData {

        /**
         * 短剧信息
         */
        @SerializedName("filmInfo")
        private FilmInfo filmInfo;

        /**
         * 标签名称
         */
        @SerializedName("labelNames")
        private String labelNames;

        /**
         * 发布时间
         */
        @SerializedName("releaseTime")
        private String releaseTime;

        /**
         * 导演信息列表
         */
        @SerializedName("directorInfo")
        private List<DirectorInfo> directorInfo;

        /**
         * 演员信息列表
         */
        @SerializedName("performerInfo")
        private List<PerformerInfo> performerInfo;

        /**
         * 播放进度信息
         */
        @SerializedName("playProgressVO")
        private PlayProgressVO playProgressVO;

        /**
         * 章节列表
         */
        @SerializedName("chapterList")
        private List<ChapterInfo> chapterList;

        /**
         * 相似影片列表（推荐视频）
         */
        @SerializedName("similarFilmList")
        private List<FilmInfo> similarFilmList;

        /**
         * 订阅状态
         * 0=未订阅 1=已订阅
         */
        @SerializedName("subscribeStatus")
        private Integer subscribeStatus;

        /**
         * 默认构造函数
         */
        public VideoDetailData() {
        }

        // ========== Getter和Setter方法 ==========

        public FilmInfo getFilmInfo() {
            return filmInfo;
        }

        public void setFilmInfo(FilmInfo filmInfo) {
            this.filmInfo = filmInfo;
        }

        public String getLabelNames() {
            return labelNames;
        }

        public void setLabelNames(String labelNames) {
            this.labelNames = labelNames;
        }

        public String getReleaseTime() {
            return releaseTime;
        }

        public void setReleaseTime(String releaseTime) {
            this.releaseTime = releaseTime;
        }

        public List<DirectorInfo> getDirectorInfo() {
            return directorInfo;
        }

        public void setDirectorInfo(List<DirectorInfo> directorInfo) {
            this.directorInfo = directorInfo;
        }

        public List<PerformerInfo> getPerformerInfo() {
            return performerInfo;
        }

        public void setPerformerInfo(List<PerformerInfo> performerInfo) {
            this.performerInfo = performerInfo;
        }

        public PlayProgressVO getPlayProgressVO() {
            return playProgressVO;
        }

        public void setPlayProgressVO(PlayProgressVO playProgressVO) {
            this.playProgressVO = playProgressVO;
        }

        public List<ChapterInfo> getChapterList() {
            return chapterList;
        }

        public void setChapterList(List<ChapterInfo> chapterList) {
            this.chapterList = chapterList;
        }

        public List<FilmInfo> getSimilarFilmList() {
            return similarFilmList;
        }

        public void setSimilarFilmList(List<FilmInfo> similarFilmList) {
            this.similarFilmList = similarFilmList;
        }

        public Integer getSubscribeStatus() {
            return subscribeStatus;
        }

        public void setSubscribeStatus(Integer subscribeStatus) {
            this.subscribeStatus = subscribeStatus;
        }

        @Override
        public String toString() {
            return "VideoDetailData{" +
                    "filmInfo=" + filmInfo +
                    ", labelNames='" + labelNames + '\'' +
                    ", releaseTime='" + releaseTime + '\'' +
                    ", directorInfo=" + directorInfo +
                    ", performerInfo=" + performerInfo +
                    ", playProgressVO=" + playProgressVO +
                    ", chapterList=" + chapterList +
                    ", similarFilmList=" + similarFilmList +
                    ", subscribeStatus=" + subscribeStatus +
                    '}';
        }
    }
}
