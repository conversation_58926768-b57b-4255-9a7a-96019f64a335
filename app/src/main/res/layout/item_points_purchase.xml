<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/points_purchase_card_height"
    android:layout_gravity="center_horizontal"
    android:layout_marginBottom="@dimen/points_purchase_card_margin_bottom"
    android:background="@drawable/points_purchase_card_bg"
    android:orientation="vertical"
    android:padding="@dimen/points_purchase_card_padding">

    <!-- 上半部分：套餐名称和价格 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="8dp">

        <!-- 套餐名称 -->
        <TextView
            android:id="@+id/tv_package_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_toStartOf="@id/tv_price"
            android:layout_alignParentTop="true"
            android:layout_marginEnd="8dp"
            android:layout_marginTop="4dp"
            android:text="This is the name of a points package"
            android:textColor="#ffffffff"
            android:textSize="@dimen/points_purchase_title_text_size"
            android:fontFamily="sans-serif"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_alignParentTop="true"
            android:layout_marginTop="4dp"
            android:text="-$2000"
            android:textColor="#F12626"
            android:textSize="@dimen/points_purchase_price_text_size"
            android:fontFamily="sans-serif" />

    </RelativeLayout>

    <!-- 下半部分：日期时间和积分信息 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <!-- 日期时间 -->
        <TextView
            android:id="@+id/tv_date_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="06/18/2025 10:03AM"
            android:textColor="#80ffffff"
            android:textSize="@dimen/points_purchase_date_text_size"
            android:fontFamily="sans-serif" />

        <!-- 积分信息 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 积分图标 -->
            <ImageView
                android:id="@+id/iv_points_icon"
                android:layout_width="@dimen/points_purchase_icon_size"
                android:layout_height="@dimen/points_purchase_icon_size"
                android:layout_marginEnd="@dimen/points_purchase_icon_margin_end"
                android:src="@drawable/jinbi"
                android:scaleType="centerCrop" />

            <!-- 积分数量 -->
            <TextView
                android:id="@+id/tv_points"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="3000"
                android:textColor="#ffffffff"
                android:textSize="@dimen/points_purchase_points_text_size"
                android:fontFamily="sans-serif" />

            <!-- 赠送积分 -->
            <TextView
                android:id="@+id/tv_bonus_points"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/points_purchase_bonus_margin_start"
                android:text="+30gift"
                android:textColor="#ffffd388"
                android:textSize="@dimen/points_purchase_bonus_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </RelativeLayout>

</LinearLayout>
