package com.android.video.ui.activity;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.inputmethod.InputMethodManager;
import android.view.animation.OvershootInterpolator;
import android.widget.Button;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.content.ContextCompat;
import com.google.android.material.card.MaterialCardView;
import com.android.video.R;
import com.android.video.utils.PhoneNumberValidatorUtils;
import com.android.video.utils.UserSessionUtils;
import com.android.video.model.UserModel;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.AuthApiUtils;

/**
 * Phone Login Activity - Handles phone number login functionality
 * 继承BaseFullScreenActivity以获得统一的全屏状态栏处理
 * <AUTHOR>
 */
public class PhoneLoginActivity extends BaseFullScreenActivity {

    private ConstraintLayout clRootLayout;
    private MaterialCardView cvPhoneInputCard;
    private ConstraintLayout clPhoneInputContainer;
    private EditText etPhoneNumber;
    private Button btnSendCode;
    private TextView tvUserAgreement;
    private TextView tvTitle;
    private ImageView ivLogo;
    private ImageView ivBackButton;
    private ImageView ivBackground;
    private ImageView ivCountryFlag;
    private TextView tvCountryCode;
    private ImageView ivDropdownArrow;
    private Handler mainHandler;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_phone_login);

        mainHandler = new Handler(Looper.getMainLooper());

        initViews();
        setupClickListeners();
        setupAnimations();
        setupUserAgreementText();
        setupInputFocusAnimation();
        setupBackPressedCallback();
    }



    private void initViews() {
        clRootLayout = findViewById(R.id.cl_root_layout);
        cvPhoneInputCard = findViewById(R.id.cv_phone_input_card);
        clPhoneInputContainer = findViewById(R.id.cl_phone_input_container);
        etPhoneNumber = findViewById(R.id.et_phone_number);
        btnSendCode = findViewById(R.id.btn_send_code);
        tvUserAgreement = findViewById(R.id.tv_user_agreement);
        tvTitle = findViewById(R.id.tv_title);
        ivLogo = findViewById(R.id.iv_logo);
        ivBackButton = findViewById(R.id.iv_back_button);
        ivBackground = findViewById(R.id.iv_background);
        ivCountryFlag = findViewById(R.id.iv_country_flag);
        tvCountryCode = findViewById(R.id.tv_country_code);
        ivDropdownArrow = findViewById(R.id.iv_dropdown_arrow);
    }

    private void setupClickListeners() {
        btnSendCode.setOnClickListener(v -> {
            animateButton(v);
            handleSendCode();
        });

        ivBackButton.setOnClickListener(v -> handleBackButton());

        // Root layout click to clear focus from EditText
        clRootLayout.setOnClickListener(v -> {
            etPhoneNumber.clearFocus();
            // Hide soft keyboard
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.hideSoftInputFromWindow(etPhoneNumber.getWindowToken(), 0);
            }
        });

        // Make the entire input container clickable to focus on EditText
        clPhoneInputContainer.setOnClickListener(v -> {
            etPhoneNumber.requestFocus();
            // Show soft keyboard
            InputMethodManager imm = (InputMethodManager) getSystemService(Context.INPUT_METHOD_SERVICE);
            if (imm != null) {
                imm.showSoftInput(etPhoneNumber, InputMethodManager.SHOW_IMPLICIT);
            }
        });

        // Country selection click handler (for future implementation)
        View.OnClickListener countrySelectionListener = v -> {
            // TODO: Implement country selection dialog
            Toast.makeText(this, "Country selection - Coming Soon!", Toast.LENGTH_SHORT).show();
        };
        
        ivCountryFlag.setOnClickListener(countrySelectionListener);
        tvCountryCode.setOnClickListener(countrySelectionListener);
        ivDropdownArrow.setOnClickListener(countrySelectionListener);

        // Setup real-time phone number validation
        setupPhoneNumberValidation();
    }

    private void setupAnimations() {
        // Logo entrance animation
        animateLogoEntrance();

        // Add subtle entrance animations for components
        animateTextEntrance(tvTitle, 300);
        animateButtonEntrance(clPhoneInputContainer, 400);
        animateButtonEntrance(btnSendCode, 500);

        // Animate text elements
        animateTextEntrance(tvUserAgreement, 600);
    }

    private void animateLogoEntrance() {
        ivLogo.setAlpha(0f);
        ivLogo.setScaleX(0.3f);
        ivLogo.setScaleY(0.3f);

        AnimatorSet logoAnimator = new AnimatorSet();
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(ivLogo, "alpha", 0f, 1f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(ivLogo, "scaleX", 0.3f, 1.1f, 1f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(ivLogo, "scaleY", 0.3f, 1.1f, 1f);

        logoAnimator.playTogether(alphaAnimator, scaleXAnimator, scaleYAnimator);
        logoAnimator.setDuration(800);
        logoAnimator.setInterpolator(new OvershootInterpolator(1.2f));
        logoAnimator.start();
    }

    private void animateTextEntrance(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(30f);

        view.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(500)
                .setStartDelay(delay)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void animateButtonEntrance(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(50f);
        
        view.animate()
                .alpha(1f)
                .translationY(0f)
                .setDuration(600)
                .setStartDelay(delay)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .start();
    }

    private void animateButton(View view) {
        // 使用统一的按钮点击动画（0.9倍缩放，0.2秒）
        com.android.video.utils.UIAnimationUtils.animateButtonClick(view);
    }

    private void setupInputFocusAnimation() {
        etPhoneNumber.setOnFocusChangeListener((v, hasFocus) -> {
            if (hasFocus) {
                // Change background to focused state manually
                clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_focused);

                // Animate CardView elevation for real shadow effect with custom shadow
                AnimatorSet focusAnimatorSet = new AnimatorSet();

                // Elevation animation for shadow
                ObjectAnimator elevationAnimator = ObjectAnimator.ofFloat(cvPhoneInputCard, "cardElevation", 0f, 6f);
                elevationAnimator.setDuration(300);
                elevationAnimator.setInterpolator(new AccelerateDecelerateInterpolator());

                // Set shadow color for the focused state (API 28+)
                if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.P) {
                    cvPhoneInputCard.setOutlineAmbientShadowColor(0x3ddf00ff); // rgba(223, 0, 255, 0.24)
                    cvPhoneInputCard.setOutlineSpotShadowColor(0x3ddf00ff);
                }

                // Add subtle translation for shadow offset effect (X:2dp, Y:1.5dp)
                ObjectAnimator translationXAnimator = ObjectAnimator.ofFloat(cvPhoneInputCard, "translationX", 0f, 2f);
                ObjectAnimator translationYAnimator = ObjectAnimator.ofFloat(cvPhoneInputCard, "translationY", 0f, 1.5f);
                translationXAnimator.setDuration(300);
                translationYAnimator.setDuration(300);
                translationXAnimator.setInterpolator(new AccelerateDecelerateInterpolator());
                translationYAnimator.setInterpolator(new AccelerateDecelerateInterpolator());

                // Play all animations together
                focusAnimatorSet.playTogether(elevationAnimator, translationXAnimator, translationYAnimator);
                focusAnimatorSet.start();

            } else {
                // Change background to normal state manually
                clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_normal);

                // Immediately return to normal state without animation
                cvPhoneInputCard.setCardElevation(0f);
                cvPhoneInputCard.setTranslationX(0f);
                cvPhoneInputCard.setTranslationY(0f);
            }
        });
    }

    private void handleSendCode() {
        String phoneNumber = etPhoneNumber.getText().toString().trim();
        String countryCode = tvCountryCode.getText().toString().trim();

        // 检查是否为Super Admin手机号，直接登录 - 已弃用静态认证
        /*
        if ("18407151430".equals(phoneNumber)) {
            handleSuperAdminDirectLogin(phoneNumber);
            return;
        }
        */

        // Validate phone number using our validator
        PhoneNumberValidatorUtils.ValidationResult result =
            PhoneNumberValidatorUtils.validatePhoneNumber(phoneNumber, countryCode);

        if (!result.isValid()) {
            // Show appropriate error message based on validation result
            String errorMessage = getValidationErrorMessage(result, countryCode);
            Toast.makeText(this, errorMessage, Toast.LENGTH_LONG).show();
            etPhoneNumber.requestFocus();

            // Highlight the input field to show error state
            highlightInputError();
            return;
        }

        // Format the phone number for display
        String formattedNumber = PhoneNumberValidatorUtils.formatPhoneNumber(phoneNumber, countryCode);
        String fullPhoneNumber = countryCode + " " + formattedNumber;

        // 所有环境都使用后端API - 已弃用静态认证
        /*
        // 环境判断：dev环境使用静态认证，prod/test环境调用API - 已弃用
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境：显示模拟发送成功消息
            Toast.makeText(this, "Verification code sent to " + fullPhoneNumber + " (Dev Mode)",
                          Toast.LENGTH_LONG).show();

            // 直接导航到验证码页面
            mainHandler.postDelayed(() -> navigateToVerificationCode(fullPhoneNumber), 1500);
        } else {
            // prod/test环境：调用发送验证码API
            sendSmsCodeViaApi(phoneNumber, fullPhoneNumber);
        }
        */

        // 所有环境都调用发送验证码API
        sendSmsCodeViaApi(phoneNumber, fullPhoneNumber);
    }

    /**
     * 通过API发送验证码
     * @param phoneNumber 手机号
     * @param fullPhoneNumber 完整格式化的手机号
     */
    private void sendSmsCodeViaApi(String phoneNumber, String fullPhoneNumber) {
        // 显示加载状态
        btnSendCode.setEnabled(false);
        btnSendCode.setText("Sending...");

        // 调用发送验证码API
        AuthApiUtils.sendSmsCode(this, phoneNumber, new AuthApiUtils.ApiCallback<String>() {
            @Override
            public void onSuccess(String result) {
                // 恢复按钮状态
                btnSendCode.setEnabled(true);
                btnSendCode.setText("Send Code");

                // 显示成功消息
                Toast.makeText(PhoneLoginActivity.this,
                              "Verification code sent to " + fullPhoneNumber,
                              Toast.LENGTH_LONG).show();

                // 导航到验证码页面
                mainHandler.postDelayed(() -> navigateToVerificationCode(fullPhoneNumber), 1500);
            }

            @Override
            public void onError(String errorMessage) {
                // 恢复按钮状态
                btnSendCode.setEnabled(true);
                btnSendCode.setText("Send Code");

                // 显示错误消息
                Toast.makeText(PhoneLoginActivity.this,
                              "Failed to send verification code: " + errorMessage,
                              Toast.LENGTH_LONG).show();

                // 高亮输入框显示错误状态
                highlightInputError();
            }
        });
    }

    /**
     * Get appropriate error message based on validation result and country code
     */
    private String getValidationErrorMessage(PhoneNumberValidatorUtils.ValidationResult result, String countryCode) {
        if (result.getErrorMessage() != null && !result.getErrorMessage().isEmpty()) {
            return result.getErrorMessage();
        }

        // Fallback to localized error messages
        switch (countryCode) {
            case "+1":
                return getString(R.string.phone_validation_invalid_us);
            case "+7":
                return getString(R.string.phone_validation_invalid_russia);
            case "+86":
                return getString(R.string.phone_validation_invalid_china);
            default:
                return getString(R.string.phone_validation_invalid_format);
        }
    }

    /**
     * Highlight input field to show error state
     */
    private void highlightInputError() {
        // Temporarily change the border color to indicate error
        clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_error);

        // Reset to normal state after 2 seconds
        mainHandler.postDelayed(() -> {
            if (!etPhoneNumber.hasFocus()) {
                clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_normal);
            }
        }, 2000);
    }

    /**
     * Setup real-time phone number validation
     */
    private void setupPhoneNumberValidation() {
        etPhoneNumber.addTextChangedListener(new android.text.TextWatcher() {
            private Handler validationHandler = new Handler(Looper.getMainLooper());
            private Runnable validationRunnable;

            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {
                // Not needed
            }

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                // Cancel previous validation if user is still typing
                if (validationRunnable != null) {
                    validationHandler.removeCallbacks(validationRunnable);
                }
            }

            @Override
            public void afterTextChanged(android.text.Editable s) {
                // Validate after user stops typing for 500ms
                validationRunnable = () -> {
                    String phoneNumber = s.toString().trim();
                    String countryCode = tvCountryCode.getText().toString().trim();

                    if (!phoneNumber.isEmpty()) {
                        PhoneNumberValidatorUtils.ValidationResult result =
                            PhoneNumberValidatorUtils.validatePhoneNumber(phoneNumber, countryCode);

                        // Update UI based on validation result
                        updateInputValidationState(result.isValid());
                    } else {
                        // Reset to normal state when empty
                        updateInputValidationState(true);
                    }
                };

                validationHandler.postDelayed(validationRunnable, 500);
            }
        });
    }

    /**
     * Update input field visual state based on validation result
     */
    private void updateInputValidationState(boolean isValid) {
        if (!etPhoneNumber.hasFocus()) {
            return; // Don't update if not focused
        }

        if (isValid) {
            // Show valid state (focused state)
            clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_focused);
        } else {
            // Show error state
            clPhoneInputContainer.setBackgroundResource(R.drawable.phone_input_error);
        }
    }

    private void handleBackButton() {
        // Navigate back to login selection screen
        finish();
    }

    private void navigateToVerificationCode(String phoneNumber) {
        Intent intent = VerificationCodeActivity.createIntent(this, phoneNumber);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.slide_in_left, android.R.anim.slide_out_right);
        startActivity(intent, options.toBundle());
        finish(); // Close phone login activity
    }

    /**
     * @deprecated 此方法已弃用，所有环境都使用后端API认证
     * 处理Super Admin直接登录
     */
    @Deprecated
    private void handleSuperAdminDirectLogin(String phoneNumber) {
        Toast.makeText(this, "Super Admin detected - Direct login", Toast.LENGTH_SHORT).show();

        // 执行用户认证和登录
        UserModel user = UserSessionUtils.performLogin(this, phoneNumber);

        if (user != null && user.isLoggedIn()) {
            String welcomeMessage = "Welcome back, " + user.getUsername() + " (VIP)!";
            Toast.makeText(this, welcomeMessage, Toast.LENGTH_LONG).show();

            // 检查用户是否有收藏标签
            if (UserSessionUtils.hasFavoriteTags(this)) {
                // 用户已有收藏标签，直接跳转到主页面
                mainHandler.postDelayed(this::navigateToMainActivity, 1500);
            } else {
                // 用户没有收藏标签，跳转到收藏选择页面
                mainHandler.postDelayed(this::navigateToFavoriteSelectionActivity, 1500);
            }
        } else {
            Toast.makeText(this, "Login failed, please try again", Toast.LENGTH_SHORT).show();
        }
    }

    private void navigateToMainActivity() {
        Intent intent = new Intent(PhoneLoginActivity.this, MainActivity.class);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.fade_in, android.R.anim.fade_out);
        startActivity(intent, options.toBundle());
        finish(); // Close phone login activity
    }

    private void navigateToFavoriteSelectionActivity() {
        Intent intent = new Intent(PhoneLoginActivity.this, FavoriteSelectionActivity.class);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.slide_in_left, android.R.anim.slide_out_right);
        startActivity(intent, options.toBundle());
        finish(); // Close phone login activity
    }

    private void setupUserAgreementText() {
        String fullText = "By logging in, you agree to the user agreement and privacy policy.";
        SpannableString spannableString = new SpannableString(fullText);

        // Get colors
        int grayColor = ContextCompat.getColor(this, R.color.login_text_gray);
        int whiteColor = ContextCompat.getColor(this, R.color.login_text_white);

        // Set different colors for different parts
        // "By logging in, you agree to the " - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 0, 32, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "user agreement" - white
        spannableString.setSpan(new ForegroundColorSpan(whiteColor), 32, 46, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // " and " - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 46, 51, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "privacy policy" - white
        spannableString.setSpan(new ForegroundColorSpan(whiteColor), 51, 65, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        // "." - gray
        spannableString.setSpan(new ForegroundColorSpan(grayColor), 65, 66, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

        tvUserAgreement.setText(spannableString);
    }



    private void setupBackPressedCallback() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                // Navigate back to login selection screen
                finish();
            }
        };
        getOnBackPressedDispatcher().addCallback(this, callback);
    }
}
