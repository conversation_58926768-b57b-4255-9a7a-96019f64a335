package com.android.video.auth;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.LoginResponseModel;

/**
 * VKontakte登录工具类
 * <p>
 * 提供VKontakte OAuth2登录功能，使用WebView实现授权流程。
 * 支持通过VKontakte账号进行用户认证和登录。
 * </p>
 * 
 * <AUTHOR> Team
 */
public class VKontakteLoginUtils {

    private static final String TAG = "VKontakteLoginUtils";

    // VKontakte OAuth2 配置
    private static final String VK_CLIENT_ID = "dev_vk_client_id_2024"; // 开发环境临时ID
    private static final String VK_REDIRECT_URI = "https://oauth.vk.com/blank.html";
    private static final String VK_SCOPE = "email"; // 请求邮箱权限
    private static final String VK_RESPONSE_TYPE = "code";

    // VKontakte OAuth2 URLs
    private static final String VK_AUTH_URL = "https://oauth.vk.com/authorize";
    private static final String VK_TOKEN_URL = "https://oauth.vk.com/access_token";

    // 状态管理
    private static boolean isInitialized = false;
    private static VKontakteLoginCallback currentCallback;

    /**
     * VKontakte登录回调接口
     */
    public interface VKontakteLoginCallback {
        /**
         * 登录成功回调
         * @param loginResponse 登录响应数据
         */
        void onSuccess(LoginResponseModel loginResponse);

        /**
         * 登录失败回调
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);

        /**
         * 登录取消回调
         */
        void onCancel();
    }

    /**
     * 初始化VKontakte登录工具
     * @param activity 当前Activity
     */
    public static void initialize(Activity activity) {
        if (isInitialized) {
            Log.d(TAG, "VKontakte login utils already initialized");
            return;
        }

        Log.d(TAG, "Initializing VKontakte login utils");

        // 检查配置
        if (VK_CLIENT_ID.equals("your_vk_client_id")) {
            Log.w(TAG, "VKontakte Client ID not configured. Please set VK_CLIENT_ID.");
        }

        isInitialized = true;
        Log.d(TAG, "VKontakte login utils initialized successfully");
    }

    /**
     * 开始VKontakte登录
     * @param activity 当前Activity
     * @param callback 登录回调
     */
    public static void startLogin(Activity activity, VKontakteLoginCallback callback) {
        Log.d(TAG, "Starting VKontakte login");

        if (!isInitialized) {
            initialize(activity);
        }

        if (VK_CLIENT_ID.equals("your_vk_client_id")) {
            if (callback != null) {
                callback.onError("VKontakte Client ID not configured");
            }
            return;
        }

        currentCallback = callback;

        // 开发环境：使用模拟登录
        if (VK_CLIENT_ID.equals("dev_vk_client_id_2024")) {
            Log.d(TAG, "Using development mode - simulating VKontakte login");
            simulateSuccessfulLogin();
            return;
        }

        // 构建授权URL
        String authUrl = buildAuthUrl();
        Log.d(TAG, "VKontakte auth URL: " + authUrl);

        // 显示WebView进行授权
        showAuthWebView(activity, authUrl);
    }

    /**
     * 构建VKontakte授权URL
     * @return 授权URL
     */
    private static String buildAuthUrl() {
        Uri.Builder builder = Uri.parse(VK_AUTH_URL).buildUpon();
        builder.appendQueryParameter("client_id", VK_CLIENT_ID);
        builder.appendQueryParameter("redirect_uri", VK_REDIRECT_URI);
        builder.appendQueryParameter("scope", VK_SCOPE);
        builder.appendQueryParameter("response_type", VK_RESPONSE_TYPE);
        builder.appendQueryParameter("v", "5.131"); // VK API版本
        return builder.toString();
    }

    /**
     * 显示授权WebView
     * @param activity 当前Activity
     * @param authUrl 授权URL
     */
    private static void showAuthWebView(Activity activity, String authUrl) {
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("Login with VKontakte");

        // 创建WebView
        WebView webView = new WebView(activity);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setDomStorageEnabled(true);

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.d(TAG, "WebView loading URL: " + url);

                if (url.startsWith(VK_REDIRECT_URI)) {
                    // 处理回调URL
                    handleAuthCallback(url);
                    return true;
                }

                return false;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "WebView page finished: " + url);
            }
        });

        builder.setView(webView);
        builder.setNegativeButton("Cancel", (dialog, which) -> {
            Log.d(TAG, "VKontakte login cancelled by user");
            if (currentCallback != null) {
                currentCallback.onCancel();
                currentCallback = null;
            }
            dialog.dismiss();
        });

        AlertDialog dialog = builder.create();
        dialog.show();

        // 加载授权页面
        webView.loadUrl(authUrl);
    }

    /**
     * 处理授权回调
     * @param callbackUrl 回调URL
     */
    private static void handleAuthCallback(String callbackUrl) {
        Log.d(TAG, "Handling VKontakte auth callback: " + callbackUrl);

        Uri uri = Uri.parse(callbackUrl);
        String code = uri.getQueryParameter("code");
        String error = uri.getQueryParameter("error");

        if (error != null) {
            Log.e(TAG, "VKontakte auth error: " + error);
            if (currentCallback != null) {
                currentCallback.onError("VKontakte authorization failed: " + error);
                currentCallback = null;
            }
            return;
        }

        if (code != null) {
            Log.d(TAG, "VKontakte auth code received: " + code);
            // 使用授权码进行登录
            performVKLogin(code);
        } else {
            Log.e(TAG, "No authorization code received from VKontakte");
            if (currentCallback != null) {
                currentCallback.onError("No authorization code received");
                currentCallback = null;
            }
        }
    }

    /**
     * 使用VKontakte授权码进行登录
     * @param authCode 授权码
     */
    private static void performVKLogin(String authCode) {
        Log.d(TAG, "Performing VKontakte login with auth code");

        // 这里应该调用后端API进行VKontakte登录
        // 暂时使用模拟数据
        if (EnvironmentConfigUtils.isDevelopment()) {
            // 开发环境：模拟成功登录
            simulateSuccessfulLogin();
        } else {
            // 生产环境：调用实际API
            // TODO: 实现实际的VKontakte登录API调用
            if (currentCallback != null) {
                currentCallback.onError("VKontakte login API not implemented yet");
                currentCallback = null;
            }
        }
    }

    /**
     * 模拟成功登录（开发环境使用）
     */
    private static void simulateSuccessfulLogin() {
        Log.d(TAG, "Simulating successful VKontakte login");

        // 创建模拟的登录响应
        LoginResponseModel loginResponse = new LoginResponseModel();
        loginResponse.setCustomerId("vk_user_123");
        loginResponse.setCustomerName("VK User");
        loginResponse.setUserToken("vk_token_" + System.currentTimeMillis());
        loginResponse.setLoginType(2); // VKontakte登录类型
        loginResponse.setDeviceId("vk_device_id");

        if (currentCallback != null) {
            currentCallback.onSuccess(loginResponse);
            currentCallback = null;
        }
    }

    /**
     * 清理回调，防止内存泄漏
     */
    public static void clearCallback() {
        Log.d(TAG, "Clearing VKontakte login callback");
        currentCallback = null;
    }

    /**
     * 检查VKontakte应用是否已安装
     * @param activity 当前Activity
     * @return 是否已安装VKontakte应用
     */
    public static boolean isVKAppInstalled(Activity activity) {
        try {
            activity.getPackageManager().getPackageInfo("com.vkontakte.android", 0);
            return true;
        } catch (Exception e) {
            Log.d(TAG, "VKontakte app not installed: " + e.getMessage());
            return false;
        }
    }

    /**
     * 获取VKontakte登录状态信息（用于调试）
     * @param activity 当前Activity
     * @return 状态信息字符串
     */
    public static String getLoginStatusInfo(Activity activity) {
        StringBuilder info = new StringBuilder();
        info.append("VKontakte Login Status (WebView Implementation):\n");
        info.append("Utils Initialized: ").append(isInitialized).append("\n");
        info.append("VKontakte App Installed: ").append(isVKAppInstalled(activity)).append("\n");
        info.append("Client ID: ").append(VK_CLIENT_ID.equals("your_vk_client_id") ? "Not Configured" : "Configured").append("\n");
        info.append("Redirect URI: ").append(VK_REDIRECT_URI).append("\n");
        info.append("Implementation: WebView-based OAuth2 flow");

        return info.toString();
    }
}
