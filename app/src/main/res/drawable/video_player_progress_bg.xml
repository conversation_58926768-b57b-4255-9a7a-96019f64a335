<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- Background -->
    <item android:id="@android:id/background">
        <shape android:shape="rectangle">
            <solid android:color="@color/video_player_progress_bg" />
            <corners android:radius="@dimen/video_player_progress_corner_radius" />
        </shape>
    </item>
    
    <!-- Secondary Progress (buffered) -->
    <item android:id="@android:id/secondaryProgress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="#66FFFFFF" />
                <corners android:radius="@dimen/video_player_progress_corner_radius" />
            </shape>
        </clip>
    </item>
    
    <!-- Primary Progress (watched) -->
    <item android:id="@android:id/progress">
        <clip>
            <shape android:shape="rectangle">
                <solid android:color="@color/video_player_progress_watched" />
                <corners android:radius="@dimen/video_player_progress_corner_radius" />
            </shape>
        </clip>
    </item>
    
</layer-list>
