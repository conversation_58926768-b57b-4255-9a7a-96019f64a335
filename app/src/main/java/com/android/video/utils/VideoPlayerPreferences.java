package com.android.video.utils;

import android.content.Context;
import android.content.SharedPreferences;

import com.android.video.player.VideoQualitySelector;

/**
 * 视频播放器设置持久化工具类
 * 保存和恢复用户的播放设置
 * <AUTHOR>
 */
public class VideoPlayerPreferences {
    
    private static final String PREF_NAME = "video_player_preferences";
    private static final String KEY_PLAYBACK_SPEED = "playback_speed";
    private static final String KEY_VIDEO_QUALITY = "video_quality";
    private static final String KEY_DANMAKU_ENABLED = "danmaku_enabled";
    private static final String KEY_AUTO_PLAY = "auto_play";
    private static final String KEY_VOLUME = "volume";
    private static final String KEY_LAST_POSITION = "last_position_";
    
    private SharedPreferences preferences;
    private SharedPreferences.Editor editor;
    
    public VideoPlayerPreferences(Context context) {
        preferences = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        editor = preferences.edit();
    }
    
    /**
     * 保存播放速度
     */
    public void savePlaybackSpeed(float speed) {
        editor.putFloat(KEY_PLAYBACK_SPEED, speed);
        editor.apply();
    }
    
    /**
     * 获取播放速度
     */
    public float getPlaybackSpeed() {
        return preferences.getFloat(KEY_PLAYBACK_SPEED, 1.0f);
    }
    
    /**
     * 保存视频清晰度
     */
    public void saveVideoQuality(VideoQualitySelector.VideoQuality quality) {
        editor.putString(KEY_VIDEO_QUALITY, quality.name());
        editor.apply();
    }
    
    /**
     * 获取视频清晰度
     */
    public VideoQualitySelector.VideoQuality getVideoQuality() {
        String qualityName = preferences.getString(KEY_VIDEO_QUALITY, 
            VideoQualitySelector.VideoQuality.QUALITY_1080P.name());
        try {
            return VideoQualitySelector.VideoQuality.valueOf(qualityName);
        } catch (IllegalArgumentException e) {
            return VideoQualitySelector.VideoQuality.QUALITY_1080P;
        }
    }
    
    /**
     * 保存弹幕开关状态
     */
    public void saveDanmakuEnabled(boolean enabled) {
        editor.putBoolean(KEY_DANMAKU_ENABLED, enabled);
        editor.apply();
    }
    
    /**
     * 获取弹幕开关状态
     */
    public boolean isDanmakuEnabled() {
        return preferences.getBoolean(KEY_DANMAKU_ENABLED, true);
    }
    
    /**
     * 保存自动播放设置
     */
    public void saveAutoPlay(boolean autoPlay) {
        editor.putBoolean(KEY_AUTO_PLAY, autoPlay);
        editor.apply();
    }
    
    /**
     * 获取自动播放设置
     */
    public boolean isAutoPlay() {
        return preferences.getBoolean(KEY_AUTO_PLAY, true);
    }
    
    /**
     * 保存音量设置
     */
    public void saveVolume(float volume) {
        editor.putFloat(KEY_VOLUME, volume);
        editor.apply();
    }
    
    /**
     * 获取音量设置
     */
    public float getVolume() {
        return preferences.getFloat(KEY_VOLUME, 1.0f);
    }
    
    /**
     * 保存视频播放位置
     */
    public void saveVideoPosition(String videoId, long position) {
        editor.putLong(KEY_LAST_POSITION + videoId, position);
        editor.apply();
    }
    
    /**
     * 获取视频播放位置
     */
    public long getVideoPosition(String videoId) {
        return preferences.getLong(KEY_LAST_POSITION + videoId, 0);
    }
    
    /**
     * 清除视频播放位置
     */
    public void clearVideoPosition(String videoId) {
        editor.remove(KEY_LAST_POSITION + videoId);
        editor.apply();
    }
    
    /**
     * 清除所有设置
     */
    public void clearAll() {
        editor.clear();
        editor.apply();
    }
    
    /**
     * 获取所有设置的摘要
     */
    public String getSettingsSummary() {
        StringBuilder summary = new StringBuilder();
        summary.append("播放速度: ").append(getPlaybackSpeed()).append("x\n");
        summary.append("视频清晰度: ").append(getVideoQuality().getDisplayName()).append("\n");
        summary.append("弹幕: ").append(isDanmakuEnabled() ? "开启" : "关闭").append("\n");
        summary.append("自动播放: ").append(isAutoPlay() ? "开启" : "关闭").append("\n");
        summary.append("音量: ").append((int)(getVolume() * 100)).append("%");
        return summary.toString();
    }
}
