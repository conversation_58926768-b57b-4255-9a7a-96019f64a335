package com.android.video.utils;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.Paint;
import android.graphics.Rect;
import android.media.MediaMetadataRetriever;
import android.net.Uri;
import android.util.Log;
import com.android.video.R;
import com.android.video.model.TestVideoModel;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

/**
 * 测试视频生成器工具类
 * 负责生成测试视频的元数据和缩略图
 * <AUTHOR> Team
 */
public class TestVideoGenerator {

    private static final String TAG = "TestVideoGenerator";
    private Context context;
    private Random random;

    public TestVideoGenerator(Context context) {
        this.context = context;
        this.random = new Random();
    }

    /**
     * 生成完整的测试视频列表
     * @return 测试视频列表
     */
    public List<TestVideoModel> generateTestVideos() {
        List<TestVideoModel> videos = new ArrayList<>();

        // 动态扫描raw文件夹下的所有视频文件
        videos.addAll(loadRawVideoResources());

        // 如果没有找到视频文件，生成虚拟测试视频作为备用
        if (videos.isEmpty()) {
            Log.w(TAG, "No video resources found in raw folder, generating virtual test videos");
            videos.addAll(generateVirtualTestVideos());
        }

        return videos;
    }

    /**
     * 动态加载raw文件夹下的视频资源
     * @return 视频资源列表
     */
    private List<TestVideoModel> loadRawVideoResources() {
        List<TestVideoModel> videos = new ArrayList<>();

        try {
            // 使用反射获取R.raw类中的所有字段
            Field[] fields = R.raw.class.getFields();

            for (Field field : fields) {
                String fieldName = field.getName();

                // 检查是否为视频文件（以movie开头或包含video关键字）
                if (isVideoFile(fieldName)) {
                    try {
                        int resourceId = field.getInt(null);
                        TestVideoModel video = createTestVideoFromResource(resourceId, fieldName);
                        videos.add(video);
                        Log.d(TAG, "Loaded video resource: " + fieldName + " (ID: " + resourceId + ")");
                    } catch (IllegalAccessException e) {
                        Log.e(TAG, "Failed to access resource field: " + fieldName, e);
                    }
                }
            }

            Log.d(TAG, "Successfully loaded " + videos.size() + " video resources from raw folder");

        } catch (Exception e) {
            Log.e(TAG, "Failed to load raw video resources", e);
        }

        return videos;
    }

    /**
     * 检查字段名是否为视频文件
     * @param fieldName 字段名
     * @return 是否为视频文件
     */
    private boolean isVideoFile(String fieldName) {
        return fieldName.startsWith("movie") ||
               fieldName.contains("video") ||
               fieldName.endsWith("mp4") ||
               fieldName.endsWith("avi") ||
               fieldName.endsWith("mkv");
    }

    /**
     * 从资源ID创建TestVideoModel
     * @param resourceId 资源ID
     * @param fieldName 字段名
     * @return TestVideoModel实例
     */
    private TestVideoModel createTestVideoFromResource(int resourceId, String fieldName) {
        // 生成友好的标题
        String title = generateFriendlyTitle(fieldName);

        TestVideoModel video = new TestVideoModel(
            "test_video_" + fieldName,
            title,
            "这是从raw资源加载的测试视频：" + fieldName,
            fieldName + ".mp4",
            resourceId,
            "1080P",
            "mp4"
        );

        // 设置基本属性
        video.setCategory(getVideoCategory(fieldName));
        video.setTags(Arrays.asList("测试", "raw资源", getVideoType(fieldName)));
        video.setRating(3.5f + new Random().nextFloat() * 1.5f); // 3.5-5.0随机评分
        video.setViewCount(100 + new Random().nextInt(2000)); // 100-2100随机播放次数
        video.setDuration(estimateVideoDuration(video));
        video.setFileSize(estimateFileSize(video));

        // 尝试获取实际的视频信息
        enrichVideoMetadata(video);

        return video;
    }

    /**
     * 生成友好的标题
     */
    private String generateFriendlyTitle(String fieldName) {
        if (fieldName.equals("movie")) {
            return "主要测试视频";
        } else if (fieldName.startsWith("movie")) {
            String number = fieldName.substring(5); // 去掉"movie"前缀
            return "测试视频 " + number;
        } else {
            return fieldName.replace("_", " ").toUpperCase();
        }
    }

    /**
     * 获取视频分类
     */
    private String getVideoCategory(String fieldName) {
        if (fieldName.contains("movie")) {
            return "电影";
        } else if (fieldName.contains("short")) {
            return "短片";
        } else if (fieldName.contains("demo")) {
            return "演示";
        } else {
            return "测试视频";
        }
    }

    /**
     * 获取视频类型
     */
    private String getVideoType(String fieldName) {
        if (fieldName.contains("hd") || fieldName.contains("1080")) {
            return "高清";
        } else if (fieldName.contains("4k")) {
            return "超清";
        } else {
            return "标清";
        }
    }

    /**
     * 创建主要测试视频
     */
    private TestVideoModel createMainTestVideo() {
        TestVideoModel video = new TestVideoModel(
            "test_video_main",
            "主要测试视频",
            "这是主要的测试视频文件，用于验证播放器的核心功能",
            "movie.mp4",
            R.raw.movie,
            "720P",
            "mp4"
        );

        video.setCategory("电影");
        video.setTags(Arrays.asList("测试", "电影", "主要", "核心功能"));
        video.setRating(4.5f);
        video.setViewCount(1250);
        video.setDuration(estimateVideoDuration(video));
        video.setFileSize(estimateFileSize(video));

        // 尝试获取实际的视频信息
        enrichVideoMetadata(video);

        return video;
    }

    /**
     * 生成虚拟测试视频（用于UI测试）
     */
    private List<TestVideoModel> generateVirtualTestVideos() {
        List<TestVideoModel> videos = new ArrayList<>();

        // 短视频测试
        TestVideoModel shortVideo = new TestVideoModel(
            "test_video_short",
            "短视频测试",
            "用于测试短视频播放功能的测试文件",
            "short_test.mp4",
            R.raw.movie, // 复用现有资源
            "1080P",
            "mp4"
        );
        shortVideo.setCategory("短片");
        shortVideo.setTags(Arrays.asList("测试", "短片", "高清"));
        shortVideo.setRating(4.2f);
        shortVideo.setViewCount(890);
        shortVideo.setDuration(30000); // 30秒
        shortVideo.setFileSize(5 * 1024 * 1024); // 5MB
        videos.add(shortVideo);

        // 长视频测试
        TestVideoModel longVideo = new TestVideoModel(
            "test_video_long",
            "长视频测试",
            "用于测试长视频播放和进度控制功能",
            "long_test.mp4",
            R.raw.movie, // 复用现有资源
            "720P",
            "mp4"
        );
        longVideo.setCategory("电影");
        longVideo.setTags(Arrays.asList("测试", "电影", "长视频"));
        longVideo.setRating(4.7f);
        longVideo.setViewCount(2340);
        longVideo.setDuration(7200000); // 2小时
        longVideo.setFileSize(800 * 1024 * 1024); // 800MB
        videos.add(longVideo);

        // 高清视频测试
        TestVideoModel hdVideo = new TestVideoModel(
            "test_video_hd",
            "高清视频测试",
            "用于测试高清视频播放和画质切换功能",
            "hd_test.mp4",
            R.raw.movie, // 复用现有资源
            "1080P",
            "mp4"
        );
        hdVideo.setCategory("纪录片");
        hdVideo.setTags(Arrays.asList("测试", "高清", "1080P", "纪录片"));
        hdVideo.setRating(4.8f);
        hdVideo.setViewCount(1560);
        hdVideo.setDuration(3600000); // 1小时
        hdVideo.setFileSize(1200 * 1024 * 1024); // 1.2GB
        videos.add(hdVideo);

        // 动画视频测试
        TestVideoModel animationVideo = new TestVideoModel(
            "test_video_animation",
            "动画视频测试",
            "用于测试动画视频播放功能",
            "animation_test.mp4",
            R.raw.movie, // 复用现有资源
            "720P",
            "mp4"
        );
        animationVideo.setCategory("动画");
        animationVideo.setTags(Arrays.asList("测试", "动画", "卡通"));
        animationVideo.setRating(4.3f);
        animationVideo.setViewCount(670);
        animationVideo.setDuration(1500000); // 25分钟
        animationVideo.setFileSize(300 * 1024 * 1024); // 300MB
        videos.add(animationVideo);

        // 音乐视频测试
        TestVideoModel musicVideo = new TestVideoModel(
            "test_video_music",
            "音乐视频测试",
            "用于测试音乐视频播放功能",
            "music_test.mp4",
            R.raw.movie, // 复用现有资源
            "720P",
            "mp4"
        );
        musicVideo.setCategory("音乐视频");
        musicVideo.setTags(Arrays.asList("测试", "音乐", "MV"));
        musicVideo.setRating(4.1f);
        musicVideo.setViewCount(1890);
        musicVideo.setDuration(240000); // 4分钟
        musicVideo.setFileSize(80 * 1024 * 1024); // 80MB
        videos.add(musicVideo);

        // 添加更多测试视频用于上下滑动测试
        videos.addAll(generateAdditionalTestVideos());

        return videos;
    }

    /**
     * 生成额外的测试视频用于上下滑动测试
     */
    private List<TestVideoModel> generateAdditionalTestVideos() {
        List<TestVideoModel> videos = new ArrayList<>();

        // 科幻电影系列
        TestVideoModel sciFiVideo1 = new TestVideoModel(
            "test_video_scifi_1",
            "星际穿越",
            "一部关于时间、空间和人类情感的科幻史诗",
            "scifi_1.mp4",
            R.raw.movie,
            "4K",
            "mp4"
        );
        sciFiVideo1.setCategory("科幻电影");
        sciFiVideo1.setTags(Arrays.asList("科幻", "太空", "时间旅行"));
        sciFiVideo1.setRating(4.8f);
        sciFiVideo1.setViewCount(2500);
        sciFiVideo1.setDuration(2700000); // 45分钟
        sciFiVideo1.setFileSize(800 * 1024 * 1024); // 800MB
        videos.add(sciFiVideo1);

        TestVideoModel sciFiVideo2 = new TestVideoModel(
            "test_video_scifi_2",
            "银翼杀手2049",
            "未来世界中人类与复制人的哲学思辨",
            "scifi_2.mp4",
            R.raw.movie,
            "4K",
            "mp4"
        );
        sciFiVideo2.setCategory("科幻电影");
        sciFiVideo2.setTags(Arrays.asList("科幻", "未来", "哲学"));
        sciFiVideo2.setRating(4.7f);
        sciFiVideo2.setViewCount(2200);
        sciFiVideo2.setDuration(2640000); // 44分钟
        sciFiVideo2.setFileSize(750 * 1024 * 1024); // 750MB
        videos.add(sciFiVideo2);

        // 动作电影系列
        TestVideoModel actionVideo1 = new TestVideoModel(
            "test_video_action_1",
            "疾速追杀",
            "一个退休杀手的复仇之路",
            "action_1.mp4",
            R.raw.movie,
            "1080P",
            "mp4"
        );
        actionVideo1.setCategory("动作电影");
        actionVideo1.setTags(Arrays.asList("动作", "复仇", "枪战"));
        actionVideo1.setRating(4.6f);
        actionVideo1.setViewCount(3200);
        actionVideo1.setDuration(1800000); // 30分钟
        actionVideo1.setFileSize(400 * 1024 * 1024); // 400MB
        videos.add(actionVideo1);

        TestVideoModel actionVideo2 = new TestVideoModel(
            "test_video_action_2",
            "碟中谍：不可能的任务",
            "特工伊森·亨特的惊险任务",
            "action_2.mp4",
            R.raw.movie,
            "1080P",
            "mp4"
        );
        actionVideo2.setCategory("动作电影");
        actionVideo2.setTags(Arrays.asList("动作", "特工", "惊险"));
        actionVideo2.setRating(4.5f);
        actionVideo2.setViewCount(2800);
        actionVideo2.setDuration(2100000); // 35分钟
        actionVideo2.setFileSize(500 * 1024 * 1024); // 500MB
        videos.add(actionVideo2);

        // 喜剧电影系列
        TestVideoModel comedyVideo1 = new TestVideoModel(
            "test_video_comedy_1",
            "功夫",
            "周星驰经典喜剧功夫片",
            "comedy_1.mp4",
            R.raw.movie,
            "720P",
            "mp4"
        );
        comedyVideo1.setCategory("喜剧电影");
        comedyVideo1.setTags(Arrays.asList("喜剧", "功夫", "周星驰"));
        comedyVideo1.setRating(4.9f);
        comedyVideo1.setViewCount(5000);
        comedyVideo1.setDuration(1980000); // 33分钟
        comedyVideo1.setFileSize(350 * 1024 * 1024); // 350MB
        videos.add(comedyVideo1);

        TestVideoModel comedyVideo2 = new TestVideoModel(
            "test_video_comedy_2",
            "大话西游",
            "经典无厘头喜剧爱情片",
            "comedy_2.mp4",
            R.raw.movie,
            "720P",
            "mp4"
        );
        comedyVideo2.setCategory("喜剧电影");
        comedyVideo2.setTags(Arrays.asList("喜剧", "爱情", "经典"));
        comedyVideo2.setRating(4.8f);
        comedyVideo2.setViewCount(4500);
        comedyVideo2.setDuration(2040000); // 34分钟
        comedyVideo2.setFileSize(380 * 1024 * 1024); // 380MB
        videos.add(comedyVideo2);

        // 恐怖电影系列
        TestVideoModel horrorVideo1 = new TestVideoModel(
            "test_video_horror_1",
            "寂静之地",
            "在无声世界中的生存恐怖",
            "horror_1.mp4",
            R.raw.movie,
            "1080P",
            "mp4"
        );
        horrorVideo1.setCategory("恐怖电影");
        horrorVideo1.setTags(Arrays.asList("恐怖", "惊悚", "生存"));
        horrorVideo1.setRating(4.4f);
        horrorVideo1.setViewCount(1800);
        horrorVideo1.setDuration(1620000); // 27分钟
        horrorVideo1.setFileSize(300 * 1024 * 1024); // 300MB
        videos.add(horrorVideo1);

        // 纪录片系列
        TestVideoModel docVideo1 = new TestVideoModel(
            "test_video_doc_1",
            "地球脉动",
            "BBC自然纪录片经典之作",
            "doc_1.mp4",
            R.raw.movie,
            "4K",
            "mp4"
        );
        docVideo1.setCategory("纪录片");
        docVideo1.setTags(Arrays.asList("纪录片", "自然", "BBC"));
        docVideo1.setRating(4.9f);
        docVideo1.setViewCount(3500);
        docVideo1.setDuration(3000000); // 50分钟
        docVideo1.setFileSize(1000 * 1024 * 1024); // 1GB
        videos.add(docVideo1);

        return videos;
    }

    /**
     * 估算视频时长
     */
    private long estimateVideoDuration(TestVideoModel video) {
        // 对于实际的视频文件，尝试获取真实时长
        try {
            Uri uri = Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(context, uri);
            String duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            retriever.release();
            
            if (duration != null) {
                return Long.parseLong(duration);
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to get actual video duration for " + video.getTitle(), e);
        }

        // 如果无法获取实际时长，返回估算值
        return 120000; // 默认2分钟
    }

    /**
     * 估算文件大小
     */
    private long estimateFileSize(TestVideoModel video) {
        // 对于实际的视频文件，尝试获取真实文件大小
        try {
            Uri uri = Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
            // 这里可以添加获取实际文件大小的逻辑
            // 由于是资源文件，可能需要其他方法
        } catch (Exception e) {
            Log.w(TAG, "Failed to get actual file size for " + video.getTitle(), e);
        }

        // 根据分辨率和时长估算文件大小
        long duration = video.getDuration();
        String resolution = video.getResolution();
        
        long estimatedSize;
        switch (resolution) {
            case "480P":
                estimatedSize = duration * 500; // 约500KB/秒
                break;
            case "720P":
                estimatedSize = duration * 1000; // 约1MB/秒
                break;
            case "1080P":
                estimatedSize = duration * 2000; // 约2MB/秒
                break;
            case "4K":
                estimatedSize = duration * 8000; // 约8MB/秒
                break;
            default:
                estimatedSize = duration * 1000; // 默认1MB/秒
        }
        
        return estimatedSize;
    }

    /**
     * 丰富视频元数据
     */
    private void enrichVideoMetadata(TestVideoModel video) {
        try {
            Uri uri = Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(context, uri);

            // 获取视频时长
            String duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            if (duration != null) {
                video.setDuration(Long.parseLong(duration));
            }

            // 获取视频宽度和高度
            String width = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_WIDTH);
            String height = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_VIDEO_HEIGHT);
            if (width != null && height != null) {
                int w = Integer.parseInt(width);
                int h = Integer.parseInt(height);
                video.setResolution(determineResolution(w, h));
            }

            // 生成缩略图
            generateThumbnail(video, retriever);

            retriever.release();
            Log.d(TAG, "Enriched metadata for video: " + video.getTitle());
        } catch (Exception e) {
            Log.e(TAG, "Failed to enrich metadata for video: " + video.getTitle(), e);
        }
    }

    /**
     * 根据宽高确定分辨率
     */
    private String determineResolution(int width, int height) {
        if (height >= 2160) return "4K";
        else if (height >= 1080) return "1080P";
        else if (height >= 720) return "720P";
        else if (height >= 480) return "480P";
        else return "标清";
    }

    /**
     * 生成视频缩略图
     */
    private void generateThumbnail(TestVideoModel video, MediaMetadataRetriever retriever) {
        try {
            // 获取视频帧作为缩略图
            Bitmap frame = retriever.getFrameAtTime(1000000); // 1秒处的帧
            if (frame != null) {
                // 缩放到合适的缩略图尺寸
                Bitmap thumbnail = Bitmap.createScaledBitmap(frame, 320, 180, true);
                
                // 保存缩略图到内部存储
                String thumbnailPath = saveThumbnailToFile(video.getId(), thumbnail);
                video.setThumbnailPath(thumbnailPath);
                
                frame.recycle();
                thumbnail.recycle();
                
                Log.d(TAG, "Generated thumbnail for video: " + video.getTitle());
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to generate thumbnail for video: " + video.getTitle(), e);
            // 生成默认缩略图
            generateDefaultThumbnail(video);
        }
    }

    /**
     * 生成默认缩略图
     */
    private void generateDefaultThumbnail(TestVideoModel video) {
        try {
            // 创建一个简单的默认缩略图
            Bitmap defaultThumbnail = Bitmap.createBitmap(320, 180, Bitmap.Config.ARGB_8888);
            Canvas canvas = new Canvas(defaultThumbnail);
            
            // 绘制背景
            Paint backgroundPaint = new Paint();
            backgroundPaint.setColor(Color.GRAY);
            canvas.drawRect(0, 0, 320, 180, backgroundPaint);
            
            // 绘制文字
            Paint textPaint = new Paint();
            textPaint.setColor(Color.WHITE);
            textPaint.setTextSize(24);
            textPaint.setAntiAlias(true);
            
            String text = video.getTitle();
            Rect textBounds = new Rect();
            textPaint.getTextBounds(text, 0, text.length(), textBounds);
            
            float x = (320 - textBounds.width()) / 2f;
            float y = (180 + textBounds.height()) / 2f;
            canvas.drawText(text, x, y, textPaint);
            
            // 保存默认缩略图
            String thumbnailPath = saveThumbnailToFile(video.getId() + "_default", defaultThumbnail);
            video.setThumbnailPath(thumbnailPath);
            
            defaultThumbnail.recycle();
            
            Log.d(TAG, "Generated default thumbnail for video: " + video.getTitle());
        } catch (Exception e) {
            Log.e(TAG, "Failed to generate default thumbnail for video: " + video.getTitle(), e);
        }
    }

    /**
     * 保存缩略图到文件
     */
    private String saveThumbnailToFile(String videoId, Bitmap thumbnail) {
        try {
            File thumbnailDir = new File(context.getCacheDir(), "video_thumbnails");
            if (!thumbnailDir.exists()) {
                thumbnailDir.mkdirs();
            }
            
            File thumbnailFile = new File(thumbnailDir, videoId + "_thumbnail.jpg");
            FileOutputStream fos = new FileOutputStream(thumbnailFile);
            thumbnail.compress(Bitmap.CompressFormat.JPEG, 80, fos);
            fos.close();
            
            return thumbnailFile.getAbsolutePath();
        } catch (IOException e) {
            Log.e(TAG, "Failed to save thumbnail to file", e);
            return null;
        }
    }

    /**
     * 验证视频文件
     */
    public boolean validateVideoFile(TestVideoModel video) {
        try {
            Uri uri = Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
            MediaMetadataRetriever retriever = new MediaMetadataRetriever();
            retriever.setDataSource(context, uri);
            
            String duration = retriever.extractMetadata(MediaMetadataRetriever.METADATA_KEY_DURATION);
            boolean isValid = duration != null && Long.parseLong(duration) > 0;
            
            retriever.release();
            video.setValid(isValid);
            
            return isValid;
        } catch (Exception e) {
            Log.e(TAG, "Failed to validate video file: " + video.getTitle(), e);
            video.setValid(false);
            return false;
        }
    }

    /**
     * 清理缓存的缩略图
     */
    public void clearThumbnailCache() {
        try {
            File thumbnailDir = new File(context.getCacheDir(), "video_thumbnails");
            if (thumbnailDir.exists()) {
                File[] files = thumbnailDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        file.delete();
                    }
                }
            }
            Log.d(TAG, "Thumbnail cache cleared");
        } catch (Exception e) {
            Log.e(TAG, "Failed to clear thumbnail cache", e);
        }
    }
}
