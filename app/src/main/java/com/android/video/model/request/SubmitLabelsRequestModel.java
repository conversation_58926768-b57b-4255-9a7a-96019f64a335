package com.android.video.model.request;

import com.google.gson.annotations.Expose;
import java.util.List;
import java.util.ArrayList;

/**
 * 提交用户标签请求模型
 * <AUTHOR> Team
 */
public class SubmitLabelsRequestModel {

    /**
     * 标签ID数组
     */
    @Expose
    private List<String> labelIds;

    /**
     * 默认构造函数
     */
    public SubmitLabelsRequestModel() {
        this.labelIds = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param labelIds 标签ID列表
     */
    public SubmitLabelsRequestModel(List<String> labelIds) {
        this.labelIds = labelIds != null ? new ArrayList<>(labelIds) : new ArrayList<>();
    }

    // ========== Getter和Setter方法 ==========

    public List<String> getLabelIds() {
        return labelIds;
    }

    public void setLabelIds(List<String> labelIds) {
        this.labelIds = labelIds != null ? new ArrayList<>(labelIds) : new ArrayList<>();
    }

    // ========== 便利方法 ==========

    /**
     * 添加标签ID
     * @param labelId 标签ID
     */
    public void addLabelId(String labelId) {
        if (labelId != null && !labelId.trim().isEmpty()) {
            if (labelIds == null) {
                labelIds = new ArrayList<>();
            }
            if (!labelIds.contains(labelId)) {
                labelIds.add(labelId);
            }
        }
    }

    /**
     * 移除标签ID
     * @param labelId 标签ID
     */
    public void removeLabelId(String labelId) {
        if (labelIds != null && labelId != null) {
            labelIds.remove(labelId);
        }
    }

    /**
     * 清空所有标签ID
     */
    public void clearLabelIds() {
        if (labelIds != null) {
            labelIds.clear();
        }
    }

    /**
     * 获取标签数量
     * @return 标签数量
     */
    public int getLabelCount() {
        return labelIds != null ? labelIds.size() : 0;
    }

    /**
     * 检查是否包含指定标签ID
     * @param labelId 标签ID
     * @return 是否包含
     */
    public boolean containsLabelId(String labelId) {
        return labelIds != null && labelIds.contains(labelId);
    }

    /**
     * 验证请求参数是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return labelIds != null && !labelIds.isEmpty();
    }

    /**
     * 获取验证失败的原因
     * @return 验证失败原因，如果验证通过则返回null
     */
    public String getValidationError() {
        if (labelIds == null || labelIds.isEmpty()) {
            return "At least one label must be selected";
        }
        return null;
    }

    /**
     * 检查是否为空
     * @return 是否为空
     */
    public boolean isEmpty() {
        return labelIds == null || labelIds.isEmpty();
    }

    @Override
    public String toString() {
        return "SubmitLabelsRequestModel{" +
                "labelIds=" + labelIds +
                ", count=" + getLabelCount() +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        SubmitLabelsRequestModel that = (SubmitLabelsRequestModel) obj;
        
        return labelIds != null ? labelIds.equals(that.labelIds) : that.labelIds == null;
    }

    @Override
    public int hashCode() {
        return labelIds != null ? labelIds.hashCode() : 0;
    }
}
