package com.android.video.utils;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.widget.ImageView;

import androidx.annotation.Nullable;

import com.android.video.R;
import com.android.video.network.NetworkUtils;
import com.android.video.ui.component.ImageLoadingAnimationManager;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;

/**
 * 优化的图片加载工具类
 * 根据网络状况自动调整加载策略
 * <AUTHOR> Team
 */
public class OptimizedImageLoader {
    
    private static final String TAG = "OptimizedImageLoader";
    
    /**
     * 图片加载策略枚举
     */
    public enum LoadStrategy {
        HIGH_QUALITY,    // 高质量：原图 + 完整缓存
        BALANCED,        // 平衡：适中质量 + 智能缓存
        DATA_SAVER,      // 省流：低质量 + 最小缓存
        CACHE_ONLY       // 仅缓存：只使用已缓存的图片
    }
    
    /**
     * 智能加载图片
     * 根据网络状况自动选择最佳策略
     */
    public static void loadImageSmart(Context context, String imageUrl, ImageView imageView) {
        LoadStrategy strategy = getOptimalStrategy(context);
        loadImageWithStrategy(context, imageUrl, imageView, strategy, null);
    }
    
    /**
     * 智能加载图片（带占位图）
     */
    public static void loadImageSmart(Context context, String imageUrl, ImageView imageView, 
                                    Integer placeholderResId) {
        LoadStrategy strategy = getOptimalStrategy(context);
        loadImageWithStrategy(context, imageUrl, imageView, strategy, placeholderResId);
    }
    
    /**
     * 使用指定策略加载图片
     */
    public static void loadImageWithStrategy(Context context, String imageUrl, ImageView imageView,
                                           LoadStrategy strategy, Integer placeholderResId) {
        if (context == null || imageView == null || imageUrl == null || imageUrl.isEmpty()) {
            return;
        }
        
        var glideRequest = Glide.with(context)
                .load(imageUrl)
                .listener(createRequestListener(imageView, strategy));
        
        // 根据策略配置Glide
        switch (strategy) {
            case HIGH_QUALITY:
                glideRequest = glideRequest
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .skipMemoryCache(false);
                break;
                
            case BALANCED:
                glideRequest = glideRequest
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .skipMemoryCache(false);
                break;
                
            case DATA_SAVER:
                glideRequest = glideRequest
                        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                        .skipMemoryCache(false)
                        .override(400, 600); // 限制图片尺寸
                break;
                
            case CACHE_ONLY:
                glideRequest = glideRequest
                        .diskCacheStrategy(DiskCacheStrategy.AUTOMATIC)
                        .onlyRetrieveFromCache(true);
                break;
        }
        
        // 设置占位图和错误图
        if (placeholderResId != null) {
            glideRequest = glideRequest
                    .placeholder(placeholderResId)
                    .error(placeholderResId);
        } else {
            glideRequest = glideRequest
                    .placeholder(R.drawable.movie_poster)
                    .error(R.drawable.movie_poster);
        }
        
        glideRequest.into(imageView);
    }
    
    /**
     * 预加载图片
     */
    public static void preloadImage(Context context, String imageUrl) {
        if (context == null || imageUrl == null || imageUrl.isEmpty()) {
            return;
        }
        
        LoadStrategy strategy = getOptimalStrategy(context);
        
        var glideRequest = Glide.with(context).load(imageUrl);
        
        switch (strategy) {
            case HIGH_QUALITY:
                glideRequest = glideRequest.diskCacheStrategy(DiskCacheStrategy.ALL);
                break;
            case BALANCED:
                glideRequest = glideRequest.diskCacheStrategy(DiskCacheStrategy.AUTOMATIC);
                break;
            case DATA_SAVER:
                glideRequest = glideRequest
                        .diskCacheStrategy(DiskCacheStrategy.RESOURCE)
                        .override(400, 600);
                break;
            case CACHE_ONLY:
                return; // 不进行预加载
        }
        
        glideRequest.preload();
    }
    
    /**
     * 批量预加载图片
     */
    public static void preloadImages(Context context, String[] imageUrls) {
        if (context == null || imageUrls == null) {
            return;
        }
        
        // 根据网络状况决定是否进行批量预加载
        if (NetworkUtils.isPoorNetwork(context)) {
            return; // 弱网络不预加载
        }
        
        for (String url : imageUrls) {
            preloadImage(context, url);
        }
    }
    
    /**
     * 获取最佳加载策略
     */
    private static LoadStrategy getOptimalStrategy(Context context) {
        NetworkUtils.NetworkQuality quality = NetworkUtils.getDetailedNetworkQuality(context);
        NetworkUtils.NetworkType type = NetworkUtils.getNetworkType(context);
        
        switch (quality) {
            case EXCELLENT:
                return LoadStrategy.HIGH_QUALITY;
                
            case GOOD:
                return type == NetworkUtils.NetworkType.WIFI ? 
                       LoadStrategy.HIGH_QUALITY : LoadStrategy.BALANCED;
                
            case FAIR:
                return LoadStrategy.BALANCED;
                
            case POOR:
                return LoadStrategy.DATA_SAVER;
                
            case NONE:
            default:
                return LoadStrategy.CACHE_ONLY;
        }
    }
    
    /**
     * 创建请求监听器
     */
    private static RequestListener<Drawable> createRequestListener(ImageView imageView, LoadStrategy strategy) {
        return new RequestListener<Drawable>() {
            @Override
            public boolean onLoadFailed(@Nullable GlideException e, Object model, 
                                      Target<Drawable> target, boolean isFirstResource) {
                // 加载失败时的处理
                return false; // 让Glide处理错误显示
            }
            
            @Override
            public boolean onResourceReady(Drawable resource, Object model, 
                                         Target<Drawable> target, DataSource dataSource, 
                                         boolean isFirstResource) {
                // 根据数据源和策略应用动画
                if (shouldApplyAnimation(dataSource, strategy)) {
                    ImageLoadingAnimationManager.AnimationType animationType = 
                            getAnimationTypeForStrategy(strategy);
                    ImageLoadingAnimationManager.applyLoadingAnimation(imageView, animationType);
                }
                return false; // 让Glide继续处理
            }
        };
    }
    
    /**
     * 判断是否应该应用动画
     */
    private static boolean shouldApplyAnimation(DataSource dataSource, LoadStrategy strategy) {
        // 只有从网络加载且非省流模式时才应用动画
        return dataSource == DataSource.REMOTE && strategy != LoadStrategy.DATA_SAVER;
    }
    
    /**
     * 根据策略获取动画类型
     */
    private static ImageLoadingAnimationManager.AnimationType getAnimationTypeForStrategy(LoadStrategy strategy) {
        switch (strategy) {
            case HIGH_QUALITY:
                return ImageLoadingAnimationManager.AnimationType.SCALE_IN;
            case BALANCED:
                return ImageLoadingAnimationManager.AnimationType.FADE_IN;
            case DATA_SAVER:
                return ImageLoadingAnimationManager.AnimationType.NONE;
            case CACHE_ONLY:
            default:
                return ImageLoadingAnimationManager.AnimationType.FADE_IN;
        }
    }
    
    /**
     * 清除图片缓存
     */
    public static void clearImageCache(Context context) {
        if (context != null) {
            // 清除内存缓存（主线程）
            Glide.get(context).clearMemory();
            
            // 清除磁盘缓存（后台线程）
            new Thread(() -> {
                Glide.get(context).clearDiskCache();
            }).start();
        }
    }
    
    /**
     * 获取缓存大小
     */
    public static void getCacheSize(Context context, CacheSizeCallback callback) {
        if (context == null || callback == null) {
            return;
        }
        
        new Thread(() -> {
            try {
                // 这里可以计算Glide缓存大小
                // 由于Glide没有直接提供API，我们提供一个估算值
                long estimatedSize = 50 * 1024 * 1024; // 50MB估算值
                callback.onCacheSizeCalculated(estimatedSize);
            } catch (Exception e) {
                callback.onError(e);
            }
        }).start();
    }
    
    /**
     * 缓存大小回调接口
     */
    public interface CacheSizeCallback {
        void onCacheSizeCalculated(long sizeInBytes);
        void onError(Exception e);
    }
    
    /**
     * 暂停图片加载（用于列表滚动优化）
     */
    public static void pauseRequests(Context context) {
        if (context != null) {
            Glide.with(context).pauseRequests();
        }
    }
    
    /**
     * 恢复图片加载
     */
    public static void resumeRequests(Context context) {
        if (context != null) {
            Glide.with(context).resumeRequests();
        }
    }
}
