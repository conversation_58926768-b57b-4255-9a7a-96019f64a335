package com.android.video.model.response;

import com.android.video.model.FeaturedModel;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * 推荐位列表响应数据模型
 * <p>
 * 用于映射推荐位列表API返回的JSON数据结构。
 * 包含响应状态码、消息和推荐位数据列表。
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": [
 *     {
 *       "featuredId": "8ede502598bd409983ab67b09bde5436",
 *       "featuredName": "Categories",
 *       "films": [...]
 *     }
 *   ]
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class FeaturedListResponseModel {
    
    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 推荐位数据列表
     */
    @SerializedName("data")
    private List<FeaturedModel> data;

    /**
     * 默认构造函数
     */
    public FeaturedListResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data 推荐位数据列表
     */
    public FeaturedListResponseModel(String code, String message, List<FeaturedModel> data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<FeaturedModel> getData() {
        return data;
    }

    public void setData(List<FeaturedModel> data) {
        this.data = data;
    }

    // ========== 便利方法 ==========

    /**
     * 检查响应是否成功
     * 
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取推荐位数量
     * 
     * @return 推荐位数量，如果data为null则返回0
     */
    public int getFeaturedCount() {
        return data != null ? data.size() : 0;
    }

    /**
     * 检查是否有推荐位数据
     * 
     * @return 如果有推荐位数据返回true，否则返回false
     */
    public boolean hasFeatured() {
        return data != null && !data.isEmpty();
    }

    /**
     * 根据推荐位名称查找推荐位
     * 
     * @param featuredName 推荐位名称
     * @return 匹配的推荐位，如果没找到返回null
     */
    public FeaturedModel getFeaturedByName(String featuredName) {
        if (data == null || featuredName == null) return null;
        
        return data.stream()
                .filter(featured -> featuredName.equalsIgnoreCase(featured.getFeaturedName()))
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取Categories推荐位
     * 
     * @return Categories推荐位，如果没找到返回null
     */
    public FeaturedModel getCategoriesFeatured() {
        if (data == null) return null;
        
        return data.stream()
                .filter(FeaturedModel::isCategoriesFeatured)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取Coming Soon推荐位
     * 
     * @return Coming Soon推荐位，如果没找到返回null
     */
    public FeaturedModel getComingSoonFeatured() {
        if (data == null) return null;
        
        return data.stream()
                .filter(FeaturedModel::isComingSoonFeatured)
                .findFirst()
                .orElse(null);
    }

    /**
     * 获取Popular series推荐位
     * 
     * @return Popular series推荐位，如果没找到返回null
     */
    public FeaturedModel getPopularSeriesFeatured() {
        if (data == null) return null;
        
        return data.stream()
                .filter(FeaturedModel::isPopularSeriesFeatured)
                .findFirst()
                .orElse(null);
    }

    @Override
    public String toString() {
        return "FeaturedListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + (data != null ? data.size() + " featured sections" : "null") +
                '}';
    }
}
