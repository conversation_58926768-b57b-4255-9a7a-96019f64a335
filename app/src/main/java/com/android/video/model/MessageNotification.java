package com.android.video.model;

/**
 * Message Notification数据模型
 */
public class MessageNotification {
    private String title;           // 标题 (如 Message Notification)
    private String dateTime;        // 日期时间
    private String content;         // 内容描述
    private String videoTitle;      // 视频标题 (如 Eternal Love)
    private String episode;         // 集数 (如 EP.10)
    private String posterUrl;       // 海报图片URL
    private int posterRes;          // 海报图片资源ID
    private boolean hasVideo;       // 是否包含视频信息
    private boolean hasGoButton;    // 是否显示Go按钮

    public MessageNotification() {
    }

    // 带视频信息的构造函数
    public MessageNotification(String title, String dateTime, String content, 
                             String videoTitle, String episode, int posterRes, boolean hasGoButton) {
        this.title = title;
        this.dateTime = dateTime;
        this.content = content;
        this.videoTitle = videoTitle;
        this.episode = episode;
        this.posterRes = posterRes;
        this.hasVideo = true;
        this.hasGoButton = hasGoButton;
    }

    // 无视频信息的构造函数
    public MessageNotification(String title, String dateTime, String content) {
        this.title = title;
        this.dateTime = dateTime;
        this.content = content;
        this.hasVideo = false;
        this.hasGoButton = false;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getVideoTitle() {
        return videoTitle;
    }

    public void setVideoTitle(String videoTitle) {
        this.videoTitle = videoTitle;
    }

    public String getEpisode() {
        return episode;
    }

    public void setEpisode(String episode) {
        this.episode = episode;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public int getPosterRes() {
        return posterRes;
    }

    public void setPosterRes(int posterRes) {
        this.posterRes = posterRes;
    }

    public boolean isHasVideo() {
        return hasVideo;
    }

    public void setHasVideo(boolean hasVideo) {
        this.hasVideo = hasVideo;
    }

    public boolean isHasGoButton() {
        return hasGoButton;
    }

    public void setHasGoButton(boolean hasGoButton) {
        this.hasGoButton = hasGoButton;
    }
}
