package com.android.video.callback;

/**
 * 本地化回调接口
 * 用于监听语言切换和翻译加载状态变化
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public interface LocalizationCallback {
    
    /**
     * 语言切换成功
     * @param newLanguageType 新的语言类型 (1=英语, 2=俄语, 3=Kaza语)
     */
    void onLanguageChanged(int newLanguageType);
    
    /**
     * 翻译数据加载完成
     */
    void onTranslationLoaded();
    
    /**
     * 操作失败
     * @param errorMessage 错误信息
     */
    void onError(String errorMessage);
}
