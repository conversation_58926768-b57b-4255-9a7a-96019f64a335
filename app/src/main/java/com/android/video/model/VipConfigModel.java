package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * VIP配置数据模型
 * <p>
 * 用于表示单个VIP套餐配置的数据结构，包含VIP名称、时长、价格、权益等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VipConfigModel {

    /**
     * VIP配置ID
     */
    @SerializedName("vipId")
    private String vipId;

    /**
     * VIP名称，如周卡、月卡等
     */
    @SerializedName("vipName")
    private String vipName;

    /**
     * VIP时长文本描述，如7天、30天等
     */
    @SerializedName("durationText")
    private String durationText;

    /**
     * VIP对应的实际天数
     */
    @SerializedName("durationDays")
    private int durationDays;

    /**
     * VIP原价
     */
    @SerializedName("price")
    private double price;

    /**
     * 实际价格（折扣后的价格）
     */
    @SerializedName("actualPrice")
    private Double actualPrice;

    /**
     * 赠送积分
     */
    @SerializedName("giftPoints")
    private int giftPoints;

    /**
     * 会员权益，支持多选，逗号分隔存储
     */
    @SerializedName("memberBenefits")
    private String memberBenefits;

    /**
     * 额外积分
     */
    @SerializedName("extraPoints")
    private Integer extraPoints;

    /**
     * VIP配置状态标识，1表示启用
     */
    @SerializedName("status")
    private int status;

    /**
     * 记录创建时间
     */
    @SerializedName("createTime")
    private String createTime;

    /**
     * 记录最后更新时间
     */
    @SerializedName("updateTime")
    private String updateTime;

    /**
     * 折扣比例(1-100)，如80表示八折，为null表示没有折扣
     */
    @SerializedName("discount")
    private Integer discount;

    /**
     * 默认构造函数
     */
    public VipConfigModel() {
    }

    /**
     * 完整构造函数
     */
    public VipConfigModel(String vipId, String vipName, String durationText, int durationDays,
                         double price, Double actualPrice, int giftPoints, String memberBenefits,
                         Integer extraPoints, int status, String createTime, String updateTime,
                         Integer discount) {
        this.vipId = vipId;
        this.vipName = vipName;
        this.durationText = durationText;
        this.durationDays = durationDays;
        this.price = price;
        this.actualPrice = actualPrice;
        this.giftPoints = giftPoints;
        this.memberBenefits = memberBenefits;
        this.extraPoints = extraPoints;
        this.status = status;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.discount = discount;
    }

    // Getters and Setters
    public String getVipId() {
        return vipId;
    }

    public void setVipId(String vipId) {
        this.vipId = vipId;
    }

    public String getVipName() {
        return vipName;
    }

    public void setVipName(String vipName) {
        this.vipName = vipName;
    }

    public String getDurationText() {
        return durationText;
    }

    public void setDurationText(String durationText) {
        this.durationText = durationText;
    }

    public int getDurationDays() {
        return durationDays;
    }

    public void setDurationDays(int durationDays) {
        this.durationDays = durationDays;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public Double getActualPrice() {
        return actualPrice;
    }

    public void setActualPrice(Double actualPrice) {
        this.actualPrice = actualPrice;
    }

    public int getGiftPoints() {
        return giftPoints;
    }

    public void setGiftPoints(int giftPoints) {
        this.giftPoints = giftPoints;
    }

    public String getMemberBenefits() {
        return memberBenefits;
    }

    public void setMemberBenefits(String memberBenefits) {
        this.memberBenefits = memberBenefits;
    }

    public Integer getExtraPoints() {
        return extraPoints;
    }

    public void setExtraPoints(Integer extraPoints) {
        this.extraPoints = extraPoints;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    /**
     * 获取显示价格（优先使用actualPrice，如果为null则使用price）
     */
    public double getDisplayPrice() {
        return actualPrice != null ? actualPrice : price;
    }

    /**
     * 是否有折扣
     */
    public boolean hasDiscount() {
        return discount != null && discount > 0 && discount < 100;
    }

    /**
     * 获取折扣文本
     */
    public String getDiscountText() {
        if (hasDiscount()) {
            int discountPercent = 100 - discount;
            return "(-" + discountPercent + "%)";
        }
        return "";
    }

    /**
     * 获取格式化的价格文本
     */
    public String getFormattedPrice() {
        return String.format("$%.2f", getDisplayPrice());
    }

    /**
     * 获取格式化的原价文本
     */
    public String getFormattedOriginalPrice() {
        if (hasDiscount()) {
            return String.format("$%.2f", price);
        }
        return "";
    }

    /**
     * 获取时长单位文本
     */
    public String getDurationUnitText() {
        return "/" + durationDays + "days";
    }

    /**
     * 获取时长描述文本（使用durationDays）
     */
    public String getDurationDescription() {
        return "Unlock all series for " + durationDays + " days";
    }

    /**
     * 获取权益描述文本
     */
    public String getBenefitsDescription() {
        if (memberBenefits == null || memberBenefits.isEmpty()) {
            return "Unlock VIP privileges";
        }

        StringBuilder description = new StringBuilder("Unlock VIP ");
        description.append(durationDays).append(" days");
        if (giftPoints > 0) {
            description.append(" and receive ").append(giftPoints).append(" points as a bonus");
        }
        return description.toString();
    }

    @Override
    public String toString() {
        return "VipConfigModel{" +
                "vipId='" + vipId + '\'' +
                ", vipName='" + vipName + '\'' +
                ", durationText='" + durationText + '\'' +
                ", durationDays=" + durationDays +
                ", price=" + price +
                ", actualPrice=" + actualPrice +
                ", giftPoints=" + giftPoints +
                ", memberBenefits='" + memberBenefits + '\'' +
                ", extraPoints=" + extraPoints +
                ", status=" + status +
                ", discount=" + discount +
                '}';
    }
}
