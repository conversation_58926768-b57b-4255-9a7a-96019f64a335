package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 下载进度API响应模型
 * <AUTHOR> Team
 */
public class DownloadProgressResponseModel {
    
    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 响应数据
     */
    @SerializedName("data")
    private DownloadProgressData data;

    public DownloadProgressResponseModel() {
    }

    public DownloadProgressResponseModel(String code, String message, DownloadProgressData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public DownloadProgressData getData() {
        return data;
    }

    public void setData(DownloadProgressData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 检查是否有数据
     * @return 是否有数据
     */
    public boolean hasData() {
        return data != null;
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null && !message.trim().isEmpty() ? message : "Unknown error";
    }

    @Override
    public String toString() {
        return "DownloadProgressResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 下载进度数据模型
     */
    public static class DownloadProgressData {
        
        /**
         * 下载记录ID
         */
        @SerializedName("downloadRecordId")
        private String downloadRecordId;
        
        /**
         * 下载状态 (0: 未开始, 1: 下载中, 2: 已完成, 3: 下载失败, 4: 已暂停)
         */
        @SerializedName("status")
        private int status;
        
        /**
         * 下载进度 (0-100)
         */
        @SerializedName("progress")
        private int progress;
        
        /**
         * 已下载大小 (字节)
         */
        @SerializedName("downloadedSize")
        private long downloadedSize;
        
        /**
         * 总文件大小 (字节)
         */
        @SerializedName("totalSize")
        private long totalSize;
        
        /**
         * 下载速度 (字节/秒)
         */
        @SerializedName("downloadSpeed")
        private long downloadSpeed;
        
        /**
         * 剩余时间 (秒)
         */
        @SerializedName("remainingTime")
        private long remainingTime;

        public DownloadProgressData() {
        }

        // Getters and Setters
        public String getDownloadRecordId() {
            return downloadRecordId;
        }

        public void setDownloadRecordId(String downloadRecordId) {
            this.downloadRecordId = downloadRecordId;
        }

        public int getStatus() {
            return status;
        }

        public void setStatus(int status) {
            this.status = status;
        }

        public int getProgress() {
            return progress;
        }

        public void setProgress(int progress) {
            this.progress = progress;
        }

        public long getDownloadedSize() {
            return downloadedSize;
        }

        public void setDownloadedSize(long downloadedSize) {
            this.downloadedSize = downloadedSize;
        }

        public long getTotalSize() {
            return totalSize;
        }

        public void setTotalSize(long totalSize) {
            this.totalSize = totalSize;
        }

        public long getDownloadSpeed() {
            return downloadSpeed;
        }

        public void setDownloadSpeed(long downloadSpeed) {
            this.downloadSpeed = downloadSpeed;
        }

        public long getRemainingTime() {
            return remainingTime;
        }

        public void setRemainingTime(long remainingTime) {
            this.remainingTime = remainingTime;
        }

        /**
         * 检查是否正在下载
         * @return 是否正在下载
         */
        public boolean isDownloading() {
            return status == 1;
        }

        /**
         * 检查是否下载完成
         * @return 是否下载完成
         */
        public boolean isCompleted() {
            return status == 2;
        }

        /**
         * 检查是否下载失败
         * @return 是否下载失败
         */
        public boolean isFailed() {
            return status == 3;
        }

        /**
         * 检查是否已暂停
         * @return 是否已暂停
         */
        public boolean isPaused() {
            return status == 4;
        }

        /**
         * 获取格式化的文件大小
         * @param sizeInBytes 字节大小
         * @return 格式化的文件大小字符串
         */
        public String getFormattedSize(long sizeInBytes) {
            if (sizeInBytes <= 0) {
                return "0 B";
            }

            final String[] units = {"B", "KB", "MB", "GB", "TB"};
            int digitGroups = (int) (Math.log10(sizeInBytes) / Math.log10(1024));

            if (digitGroups >= units.length) {
                digitGroups = units.length - 1;
            }

            double size = sizeInBytes / Math.pow(1024, digitGroups);
            return String.format("%.1f %s", size, units[digitGroups]);
        }

        /**
         * 获取格式化的已下载大小
         * @return 格式化的已下载大小字符串
         */
        public String getFormattedDownloadedSize() {
            return getFormattedSize(downloadedSize);
        }

        /**
         * 获取格式化的总大小
         * @return 格式化的总大小字符串
         */
        public String getFormattedTotalSize() {
            return getFormattedSize(totalSize);
        }

        @Override
        public String toString() {
            return "DownloadProgressData{" +
                    "downloadRecordId='" + downloadRecordId + '\'' +
                    ", status=" + status +
                    ", progress=" + progress +
                    ", downloadedSize=" + downloadedSize +
                    ", totalSize=" + totalSize +
                    ", downloadSpeed=" + downloadSpeed +
                    ", remainingTime=" + remainingTime +
                    '}';
        }
    }
}
