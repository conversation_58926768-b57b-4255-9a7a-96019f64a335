package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import androidx.annotation.NonNull;
import androidx.cardview.widget.CardView;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.android.video.utils.OptimizedImageLoader;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.List;

/**
 * 轮播图适配器
 * <AUTHOR>
 */
public class CarouselAdapter extends RecyclerView.Adapter<CarouselAdapter.CarouselViewHolder> {

    private List<VideoModel> videoList; // 显示用的循环列表
    private List<VideoModel> originalVideoList; // 原始数据列表
    private OnCarouselItemClickListener onItemClickListener;

    public interface OnCarouselItemClickListener {
        void onItemClick(VideoModel video, int position);
        void onPlayClick(VideoModel video, int position);
    }

    public CarouselAdapter(List<VideoModel> videoList) {
        this.videoList = videoList;
    }

    public void setOnItemClickListener(OnCarouselItemClickListener listener) {
        this.onItemClickListener = listener;
    }

    @NonNull
    @Override
    public CarouselViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_carousel_poster, parent, false);
        return new CarouselViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CarouselViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return videoList != null ? videoList.size() : 0;
    }

    public class CarouselViewHolder extends RecyclerView.ViewHolder {
        private CardView cvPoster;
        private ImageView ivPoster;
        private LinearLayout llPlayButton;

        public CarouselViewHolder(@NonNull View itemView) {
            super(itemView);
            cvPoster = itemView.findViewById(R.id.cv_poster);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            llPlayButton = itemView.findViewById(R.id.ll_play_button);
        }

        public void bind(VideoModel video, int position) {
            if (video == null) return;

            // 设置海报图片 (使用优化的图片加载器)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                OptimizedImageLoader.loadImageSmart(
                    itemView.getContext(),
                    posterUrl,
                    ivPoster,
                    R.drawable.movie_poster
                );
            } else {
                // 如果没有URL，使用默认图片
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 将ViewHolder设置为tag，供PageTransformer使用
            itemView.setTag(this);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    // 传递真实位置而不是循环位置
                    int realPosition = getRealPosition(position);
                    onItemClickListener.onItemClick(video, realPosition);
                }
            });

            llPlayButton.setOnClickListener(v -> {
                if (onItemClickListener != null) {
                    // 传递真实位置而不是循环位置
                    int realPosition = getRealPosition(position);
                    onItemClickListener.onPlayClick(video, realPosition);
                }
            });
        }

        public void setSelected(boolean isSelected) {
            // 显示或隐藏Play按钮
            llPlayButton.setVisibility(isSelected ? View.VISIBLE : View.GONE);

            // 动态调整CardView尺寸
            ViewGroup.LayoutParams layoutParams = cvPoster.getLayoutParams();
            if (isSelected) {
                // 选中时：246dp x 327dp
                layoutParams.width = (int) (246 * itemView.getContext().getResources().getDisplayMetrics().density);
                layoutParams.height = (int) (327 * itemView.getContext().getResources().getDisplayMetrics().density);
            } else {
                // 未选中时：保持较小尺寸 236dp x 260dp
                layoutParams.width = (int) (236 * itemView.getContext().getResources().getDisplayMetrics().density);
                layoutParams.height = (int) (260 * itemView.getContext().getResources().getDisplayMetrics().density);
            }
            cvPoster.setLayoutParams(layoutParams);
        }
    }

    /**
     * 更新视频列表并创建无限循环数据
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.originalVideoList = newVideoList != null ? new ArrayList<>(newVideoList) : new ArrayList<>();
        this.videoList = createInfiniteList(this.originalVideoList);
        notifyDataSetChanged();
    }

    /**
     * 创建无限循环列表
     * 格式：[原始数据] + [原始数据] + [原始数据] = 3倍数据实现循环
     * @param originalList 原始数据列表
     * @return 循环数据列表
     */
    private List<VideoModel> createInfiniteList(List<VideoModel> originalList) {
        if (originalList == null || originalList.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果只有1个项目，直接返回原始列表（不需要循环）
        if (originalList.size() == 1) {
            return new ArrayList<>(originalList);
        }

        List<VideoModel> infiniteList = new ArrayList<>();

        // 添加3份相同的数据实现循环
        infiniteList.addAll(originalList); // 第一份
        infiniteList.addAll(originalList); // 第二份（真实显示区域）
        infiniteList.addAll(originalList); // 第三份

        return infiniteList;
    }

    /**
     * 获取原始数据的大小
     * @return 原始数据大小
     */
    public int getOriginalSize() {
        return originalVideoList != null ? originalVideoList.size() : 0;
    }

    /**
     * 获取初始显示位置（第二份数据的开始位置）
     * @return 初始位置
     */
    public int getInitialPosition() {
        int originalSize = getOriginalSize();
        return originalSize > 1 ? originalSize : 0;
    }

    /**
     * 根据循环位置获取真实位置
     * @param position 循环位置
     * @return 真实位置
     */
    public int getRealPosition(int position) {
        int originalSize = getOriginalSize();
        if (originalSize <= 1) {
            return 0;
        }
        return position % originalSize;
    }

    /**
     * 检查是否需要进行循环跳转
     * @param position 当前位置
     * @return 需要跳转的目标位置，-1表示不需要跳转
     */
    public int getLoopJumpPosition(int position) {
        int originalSize = getOriginalSize();
        if (originalSize <= 1) {
            return -1; // 单个项目不需要循环
        }

        // 如果滚动到第一份数据的最后一个位置，跳转到第二份数据的最后一个位置
        if (position == originalSize - 1) {
            return originalSize * 2 - 1;
        }

        // 如果滚动到第三份数据的第一个位置，跳转到第二份数据的第一个位置
        if (position == originalSize * 2) {
            return originalSize;
        }

        return -1; // 不需要跳转
    }
}
