package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * VIP兑换码兑换API响应模型
 * <p>
 * 用于解析VIP兑换码兑换API的响应数据，包含响应码、消息和兑换结果。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class RedeemCodeResponseModel {

    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;

    /**
     * 兑换结果数据（可能为null）
     */
    @SerializedName("data")
    private Object data;

    /**
     * 默认构造函数
     */
    public RedeemCodeResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code    响应码
     * @param message 响应消息
     * @param data    兑换结果数据
     */
    public RedeemCodeResponseModel(String code, String message, Object data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Object getData() {
        return data;
    }

    public void setData(Object data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取用户友好的错误消息
     *
     * @return 错误消息，如果成功则返回成功消息
     */
    public String getUserFriendlyMessage() {
        if (isSuccess()) {
            return message != null ? message : "Redemption successful";
        } else {
            // 根据不同的错误码返回用户友好的消息
            if ("400".equals(code)) {
                return "Invalid redemption code";
            } else if ("404".equals(code)) {
                return "Redemption code not found or expired";
            } else if ("409".equals(code)) {
                return "Redemption code has already been used";
            } else if ("500".equals(code)) {
                return "Server error, please try again later";
            } else {
                return message != null ? message : "Redemption failed";
            }
        }
    }

    /**
     * 检查是否有数据
     *
     * @return 如果有数据则返回true，否则返回false
     */
    public boolean hasData() {
        return data != null;
    }

    @Override
    public String toString() {
        return "RedeemCodeResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
