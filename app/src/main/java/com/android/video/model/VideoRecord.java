package com.android.video.model;

/**
 * Video Record记录数据模型
 */
public class VideoRecord {
    private String title;           // 视频标题
    private String episode;         // 集数 (如 EP.1)
    private String description;     // 描述 (如 Unlock 1 Episode)
    private String dateTime;        // 日期时间
    private String cost;            // 消费金额 (如 -20)
    private String posterUrl;       // 海报图片URL
    private int posterRes;          // 海报图片资源ID

    public VideoRecord() {
    }

    public VideoRecord(String title, String episode, String description, String dateTime, String cost, int posterRes) {
        this.title = title;
        this.episode = episode;
        this.description = description;
        this.dateTime = dateTime;
        this.cost = cost;
        this.posterRes = posterRes;
    }

    public VideoRecord(String title, String episode, String description, String dateTime, String cost, String posterUrl) {
        this.title = title;
        this.episode = episode;
        this.description = description;
        this.dateTime = dateTime;
        this.cost = cost;
        this.posterUrl = posterUrl;
    }

    // Getters and Setters
    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getEpisode() {
        return episode;
    }

    public void setEpisode(String episode) {
        this.episode = episode;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getCost() {
        return cost;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public int getPosterRes() {
        return posterRes;
    }

    public void setPosterRes(int posterRes) {
        this.posterRes = posterRes;
    }
}
