package com.android.video.model;

import android.net.Uri;
import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

/**
 * 测试视频数据模型类
 * 专门用于管理本地测试视频资源
 * <AUTHOR> Team
 */
public class TestVideoModel implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private String title;
    private String description;
    private String fileName;           // 视频文件名
    private int resourceId;            // 资源ID (R.raw.xxx)
    private Uri videoUri;              // 视频URI
    private String thumbnailPath;      // 缩略图路径
    private long duration;             // 视频时长(毫秒)
    private String durationText;       // 格式化的时长文本
    private String resolution;         // 分辨率 (720P, 1080P, 4K)
    private long fileSize;             // 文件大小(字节)
    private String fileSizeText;       // 格式化的文件大小
    private String format;             // 视频格式 (mp4, avi, mkv)
    private boolean isValid;           // 文件是否有效
    private String category;           // 视频分类
    private List<String> tags;         // 标签列表
    private float rating;              // 评分
    private long viewCount;            // 播放次数
    private boolean isLiked;           // 是否收藏
    private int currentPosition;       // 当前播放位置(毫秒)
    private boolean isCompleted;       // 是否播放完成

    /**
     * 默认构造函数
     */
    public TestVideoModel() {
        this.id = "";
        this.title = "";
        this.description = "";
        this.fileName = "";
        this.resourceId = 0;
        this.videoUri = null;
        this.thumbnailPath = "";
        this.duration = 0;
        this.durationText = "00:00";
        this.resolution = "720P";
        this.fileSize = 0;
        this.fileSizeText = "0 MB";
        this.format = "mp4";
        this.isValid = false;
        this.category = "测试视频";
        this.tags = new ArrayList<>();
        this.rating = 0.0f;
        this.viewCount = 0;
        this.isLiked = false;
        this.currentPosition = 0;
        this.isCompleted = false;
    }

    /**
     * 完整构造函数
     */
    public TestVideoModel(String id, String title, String fileName, int resourceId) {
        this();
        this.id = id;
        this.title = title;
        this.fileName = fileName;
        this.resourceId = resourceId;
        this.isValid = true;
    }

    /**
     * 便利构造函数 - 基本信息
     */
    public TestVideoModel(String id, String title, String description, String fileName, 
                         int resourceId, String resolution, String format) {
        this(id, title, fileName, resourceId);
        this.description = description;
        this.resolution = resolution;
        this.format = format;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public int getResourceId() {
        return resourceId;
    }

    public void setResourceId(int resourceId) {
        this.resourceId = resourceId;
    }

    public Uri getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(Uri videoUri) {
        this.videoUri = videoUri;
    }

    public String getThumbnailPath() {
        return thumbnailPath;
    }

    public void setThumbnailPath(String thumbnailPath) {
        this.thumbnailPath = thumbnailPath;
    }

    public long getDuration() {
        return duration;
    }

    public void setDuration(long duration) {
        this.duration = duration;
        this.durationText = formatDuration(duration);
    }

    public String getDurationText() {
        return durationText;
    }

    public void setDurationText(String durationText) {
        this.durationText = durationText;
    }

    public String getResolution() {
        return resolution;
    }

    public void setResolution(String resolution) {
        this.resolution = resolution;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
        this.fileSizeText = formatFileSize(fileSize);
    }

    public String getFileSizeText() {
        return fileSizeText;
    }

    public void setFileSizeText(String fileSizeText) {
        this.fileSizeText = fileSizeText;
    }

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public boolean isValid() {
        return isValid;
    }

    public void setValid(boolean valid) {
        isValid = valid;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags != null ? tags : new ArrayList<>();
    }

    public float getRating() {
        return rating;
    }

    public void setRating(float rating) {
        this.rating = rating;
    }

    public long getViewCount() {
        return viewCount;
    }

    public void setViewCount(long viewCount) {
        this.viewCount = viewCount;
    }

    public boolean isLiked() {
        return isLiked;
    }

    public void setLiked(boolean liked) {
        isLiked = liked;
    }

    public int getCurrentPosition() {
        return currentPosition;
    }

    public void setCurrentPosition(int currentPosition) {
        this.currentPosition = currentPosition;
    }

    public boolean isCompleted() {
        return isCompleted;
    }

    public void setCompleted(boolean completed) {
        isCompleted = completed;
    }

    /**
     * 便利方法
     */
    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty() && !tags.contains(tag)) {
            tags.add(tag);
        }
    }

    public void toggleLike() {
        this.isLiked = !this.isLiked;
    }

    public void incrementViewCount() {
        this.viewCount++;
    }

    /**
     * 格式化时长
     * @param durationMs 时长(毫秒)
     * @return 格式化的时长字符串 (如: 01:23:45 或 05:30)
     */
    public static String formatDuration(long durationMs) {
        long seconds = durationMs / 1000;
        long hours = seconds / 3600;
        long minutes = (seconds % 3600) / 60;
        long secs = seconds % 60;

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, secs);
        } else {
            return String.format("%02d:%02d", minutes, secs);
        }
    }

    /**
     * 格式化文件大小
     * @param sizeBytes 文件大小(字节)
     * @return 格式化的文件大小字符串 (如: 15.2 MB, 1.5 GB)
     */
    public static String formatFileSize(long sizeBytes) {
        if (sizeBytes >= 1024 * 1024 * 1024) {
            return String.format("%.1f GB", sizeBytes / (1024.0 * 1024.0 * 1024.0));
        } else if (sizeBytes >= 1024 * 1024) {
            return String.format("%.1f MB", sizeBytes / (1024.0 * 1024.0));
        } else if (sizeBytes >= 1024) {
            return String.format("%.1f KB", sizeBytes / 1024.0);
        } else {
            return sizeBytes + " B";
        }
    }

    /**
     * 获取播放进度百分比
     * @return 播放进度 (0.0 - 1.0)
     */
    public float getPlayProgress() {
        if (duration <= 0) return 0.0f;
        return (float) currentPosition / duration;
    }

    /**
     * 获取播放进度文本
     * @return 播放进度文本 (如: 05:30 / 15:20)
     */
    public String getPlayProgressText() {
        return formatDuration(currentPosition) + " / " + durationText;
    }

    /**
     * 检查是否为有效的视频文件
     * @return 是否有效
     */
    public boolean validateVideo() {
        boolean valid = resourceId > 0 && 
                       fileName != null && !fileName.isEmpty() &&
                       title != null && !title.isEmpty();
        setValid(valid);
        return valid;
    }

    @Override
    public String toString() {
        return "TestVideoModel{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", fileName='" + fileName + '\'' +
                ", resolution='" + resolution + '\'' +
                ", duration=" + duration +
                ", isValid=" + isValid +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        TestVideoModel that = (TestVideoModel) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
