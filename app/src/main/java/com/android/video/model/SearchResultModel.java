package com.android.video.model;

/**
 * 搜索结果数据模型
 */
public class SearchResultModel {
    private int rank;
    private String title;
    private String searchCount;
    private int posterResId;

    // 新增字段 - 用于API搜索结果
    private String filmId;
    private String filmLanguageInfoId;
    private String posterUrl;
    private String categoryId;
    private String categoryName;
    private int languageType;
    
    public SearchResultModel() {}
    
    public SearchResultModel(int rank, String title, String searchCount, int posterResId) {
        this.rank = rank;
        this.title = title;
        this.searchCount = searchCount;
        this.posterResId = posterResId;
    }
    
    // Getters and Setters
    public int getRank() {
        return rank;
    }
    
    public void setRank(int rank) {
        this.rank = rank;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getSearchCount() {
        return searchCount;
    }
    
    public void setSearchCount(String searchCount) {
        this.searchCount = searchCount;
    }
    
    public int getPosterResId() {
        return posterResId;
    }
    
    public void setPosterResId(int posterResId) {
        this.posterResId = posterResId;
    }

    // 新增字段的getter和setter方法
    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getPosterUrl() {
        return posterUrl;
    }

    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }
}
