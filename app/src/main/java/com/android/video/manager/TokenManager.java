package com.android.video.manager;

import android.content.Context;
import android.util.Log;
import com.android.video.cache.DataCacheManager;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.InitDeviceResponseModel;
import com.android.video.model.response.LoginResponseModel;
import com.android.video.utils.ApiHeaderUtils;

/**
 * Token管理器
 * <p>
 * 负责管理应用的访问令牌，包括：
 * 1. 从缓存中读取和存储token
 * 2. 应用启动时自动初始化token
 * 3. 登录时更新token
 * 4. 提供当前有效的token给API调用
 * </p>
 */
public class TokenManager {
    private static final String TAG = "TokenManager";
    
    // 缓存键名
    private static final String CACHE_KEY_USER_TOKEN = "user_token";
    private static final String CACHE_KEY_DEVICE_ID = "device_id";
    private static final String CACHE_KEY_CUSTOMER_ID = "customer_id";
    private static final String CACHE_KEY_TOKEN_TIMESTAMP = "token_timestamp";
    
    // Token缓存时间（1年）
    private static final long TOKEN_CACHE_DURATION = 365 * 24 * 60 * 60 * 1000L;
    
    private static TokenManager instance;
    private Context context;
    private DataCacheManager cacheManager;
    private boolean isInitializing = false;
    
    private TokenManager(Context context) {
        this.context = context.getApplicationContext();
        this.cacheManager = DataCacheManager.getInstance(context);
    }
    
    public static synchronized TokenManager getInstance(Context context) {
        if (instance == null) {
            instance = new TokenManager(context);
        }
        return instance;
    }
    
    /**
     * 初始化Token管理器
     * <p>
     * 应用启动时调用，检查缓存中的token，如果没有则调用初始化设备接口
     * </p>
     */
    public void initialize(TokenInitCallback callback) {
        Log.d(TAG, "开始初始化Token管理器...");
        
        if (isInitializing) {
            Log.w(TAG, "Token管理器正在初始化中，跳过重复初始化");
            if (callback != null) {
                callback.onInitialized(false, "正在初始化中");
            }
            return;
        }
        
        // 检查缓存中的token
        String cachedToken = getCachedToken();
        String cachedDeviceId = getCachedDeviceId();
        
        if (isTokenValid(cachedToken)) {
            Log.d(TAG, "发现有效的缓存token，直接使用: " + maskToken(cachedToken));
            // 设置到ApiHeaderUtils中
            ApiHeaderUtils.setAccessToken(cachedToken);

            if (callback != null) {
                callback.onInitialized(true, "使用缓存token");
            }
            return;
        }
        
        Log.d(TAG, "缓存中没有有效token，调用初始化设备接口...");
        isInitializing = true;
        
        // 调用初始化设备接口
        AuthApiUtils.initDevice(context, new AuthApiUtils.ApiCallback<InitDeviceResponseModel>() {
            @Override
            public void onSuccess(InitDeviceResponseModel response) {
                isInitializing = false;
                
                if (response != null && response.getUserToken() != null) {
                    Log.d(TAG, "初始化设备接口调用成功，保存token到缓存: " + maskToken(response.getUserToken()));

                    // 保存token和deviceId到缓存
                    saveTokenToCache(response.getUserToken(), response.getDeviceId(), null);

                    // 设置到ApiHeaderUtils中
                    ApiHeaderUtils.setAccessToken(response.getUserToken());

                    if (callback != null) {
                        callback.onInitialized(true, "初始化设备成功");
                    }
                } else {
                    Log.e(TAG, "初始化设备接口返回数据无效");
                    if (callback != null) {
                        callback.onInitialized(false, "初始化设备返回数据无效");
                    }
                }
            }
            
            @Override
            public void onError(String errorMessage) {
                isInitializing = false;
                Log.e(TAG, "初始化设备接口调用失败: " + errorMessage);
                
                if (callback != null) {
                    callback.onInitialized(false, "初始化设备失败: " + errorMessage);
                }
            }
        });
    }
    
    /**
     * 更新token（登录时调用）
     * <p>
     * 登录成功后调用此方法更新token和相关信息
     * </p>
     */
    public void updateToken(LoginResponseModel loginResponse) {
        if (loginResponse == null) {
            Log.w(TAG, "登录响应为空，无法更新token");
            return;
        }

        String oldToken = getCachedToken();
        String newToken = loginResponse.getUserToken();
        String newDeviceId = loginResponse.getDeviceId();
        String customerId = loginResponse.getCustomerId();

        Log.d(TAG, "开始更新token...");
        Log.d(TAG, "旧token: " + maskToken(oldToken));
        Log.d(TAG, "新token: " + maskToken(newToken));

        if (newToken != null && !newToken.trim().isEmpty()) {
            // 检查新旧token是否相同
            if (newToken.equals(oldToken)) {
                Log.w(TAG, "新token与旧token相同，可能存在问题");
            }

            // 保存到缓存（会覆盖旧token）
            saveTokenToCache(newToken, newDeviceId, customerId);

            // 设置到ApiHeaderUtils中（会覆盖内存中的token）
            ApiHeaderUtils.setAccessToken(newToken);

            Log.d(TAG, "Token更新成功，新token已保存到缓存和内存");

            // 验证更新是否成功
            String verifyToken = getCachedToken();
            Log.d(TAG, "验证缓存中的token: " + maskToken(verifyToken));
        } else {
            Log.w(TAG, "新token为空，无法更新");
        }
    }
    
    /**
     * 获取当前有效的token
     */
    public String getCurrentToken() {
        // 直接从缓存中获取token，避免与ApiHeaderUtils循环调用
        String cachedToken = getCachedToken();

        if (isTokenValid(cachedToken)) {
            return cachedToken;
        }

        // 如果缓存中没有有效token，返回null
        // 这将触发应用重新初始化token
        return null;
    }
    
    /**
     * 获取当前的设备ID
     */
    public String getCurrentDeviceId() {
        return getCachedDeviceId();
    }
    
    /**
     * 获取当前的用户ID
     */
    public String getCurrentCustomerId() {
        return cacheManager.getCachedData(CACHE_KEY_CUSTOMER_ID, String.class);
    }
    
    /**
     * 清除token缓存（退出登录时调用）
     */
    public void clearToken() {
        Log.d(TAG, "清除token缓存");
        
        cacheManager.clearCachedData(CACHE_KEY_USER_TOKEN);
        cacheManager.clearCachedData(CACHE_KEY_DEVICE_ID);
        cacheManager.clearCachedData(CACHE_KEY_CUSTOMER_ID);
        cacheManager.clearCachedData(CACHE_KEY_TOKEN_TIMESTAMP);
        
        // 重置ApiHeaderUtils中的token
        ApiHeaderUtils.resetAccessToken();
    }
    
    /**
     * 检查token是否需要刷新
     */
    public boolean needsTokenRefresh() {
        String token = getCachedToken();
        return !isTokenValid(token);
    }

    /**
     * 直接设置token（用于设置默认token）
     */
    public void setCurrentToken(String token) {
        if (token != null && !token.trim().isEmpty()) {
            Log.d(TAG, "设置当前token: " + maskToken(token));

            // 保存到缓存
            cacheManager.cacheData(CACHE_KEY_USER_TOKEN, token, TOKEN_CACHE_DURATION);
            cacheManager.cacheData(CACHE_KEY_TOKEN_TIMESTAMP, System.currentTimeMillis(), TOKEN_CACHE_DURATION);

            // 更新ApiHeaderUtils中的token
            ApiHeaderUtils.setAccessToken(token);
        } else {
            Log.w(TAG, "尝试设置空token，忽略操作");
        }
    }
    
    // ========== 私有方法 ==========
    
    /**
     * 从缓存中获取token
     */
    private String getCachedToken() {
        return cacheManager.getCachedData(CACHE_KEY_USER_TOKEN, String.class);
    }
    
    /**
     * 从缓存中获取设备ID
     */
    private String getCachedDeviceId() {
        return cacheManager.getCachedData(CACHE_KEY_DEVICE_ID, String.class);
    }
    
    /**
     * 保存token到缓存
     */
    private void saveTokenToCache(String token, String deviceId, String customerId) {
        if (token != null && !token.trim().isEmpty()) {
            // 保存token
            cacheManager.cacheData(CACHE_KEY_USER_TOKEN, token, TOKEN_CACHE_DURATION);
            
            // 保存时间戳
            cacheManager.cacheData(CACHE_KEY_TOKEN_TIMESTAMP, System.currentTimeMillis(), TOKEN_CACHE_DURATION);
            
            Log.d(TAG, "Token已保存到缓存");
        }
        
        if (deviceId != null && !deviceId.trim().isEmpty()) {
            cacheManager.cacheData(CACHE_KEY_DEVICE_ID, deviceId, TOKEN_CACHE_DURATION);
            Log.d(TAG, "DeviceId已保存到缓存");
        }
        
        if (customerId != null && !customerId.trim().isEmpty()) {
            cacheManager.cacheData(CACHE_KEY_CUSTOMER_ID, customerId, TOKEN_CACHE_DURATION);
            Log.d(TAG, "CustomerId已保存到缓存");
        }
    }
    
    /**
     * 检查token是否有效
     */
    private boolean isTokenValid(String token) {
        if (token == null || token.trim().isEmpty()) {
            return false;
        }
        
        // 检查是否是硬编码的默认token（这些token应该被认为是无效的）
        if ("05358c87b76645feaae46740fac753c9".equals(token) ||
            "deprecated_hardcoded_token".equals(token) ||
            "deprecated_dev_test_token".equals(token) ||
            "deprecated_test_env_token".equals(token) ||
            "use_token_manager_instead".equals(token) ||
            "your_test_environment_token_here".equals(token) ||
            "will_be_obtained_from_login_api".equals(token)) {
            Log.w(TAG, "检测到硬编码token，认为无效");
            return false;
        }
        
        // 检查token长度（有效token通常有一定长度）
        if (token.length() < 16) {
            Log.w(TAG, "Token长度过短，认为无效");
            return false;
        }
        
        return true;
    }

    /**
     * 脱敏处理token用于日志输出
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }

    /**
     * Token初始化回调接口
     */
    public interface TokenInitCallback {
        void onInitialized(boolean success, String message);
    }
}
