<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/bottom_sheet_bg"
    android:paddingTop="@dimen/bottom_sheet_padding_top">

    <!-- Header with handle bar and close button -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/bottom_sheet_padding_horizontal"
        android:paddingEnd="@dimen/bottom_sheet_padding_horizontal">

        <!-- Handle bar -->
        <View
            android:layout_width="@dimen/bottom_sheet_handle_width"
            android:layout_height="@dimen/bottom_sheet_handle_height"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="@dimen/bottom_sheet_handle_margin_bottom"
            android:background="@drawable/bottom_sheet_handle_bg" />

        <!-- Close button -->
        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="@dimen/bottom_sheet_close_button_size"
            android:layout_height="@dimen/bottom_sheet_close_button_size"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:src="@drawable/dw_ic_close"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

    </RelativeLayout>

    <!-- Video info card -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/download_detail_card_height"
        android:layout_marginStart="@dimen/download_detail_card_margin_horizontal"
        android:layout_marginEnd="@dimen/download_detail_card_margin_horizontal"
        android:layout_marginTop="@dimen/download_detail_card_margin_top"
        android:background="@android:color/transparent">

        <!-- Video poster -->
        <ImageView
            android:id="@+id/iv_poster"
            android:layout_width="@dimen/download_detail_poster_width"
            android:layout_height="@dimen/download_detail_poster_height"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/download_poster_bg"
            android:clipToOutline="true" />

        <!-- Content area (right of poster) -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_toEndOf="@id/iv_poster"
            android:layout_toStartOf="@id/btn_play"
            android:layout_marginStart="@dimen/download_detail_content_margin_start"
            android:layout_centerVertical="true"
            android:layout_marginEnd="@dimen/download_detail_content_margin_end"
            android:orientation="vertical">

            <!-- Video title -->
            <TextView
                android:id="@+id/tv_video_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="30 years frozen"
                android:textColor="#ffffffff"
                android:textSize="@dimen/download_detail_title_text_size"
                android:maxLines="1"
                android:ellipsize="end"
                android:fontFamily="sans-serif" />

            <!-- Downloaded status -->
            <TextView
                android:id="@+id/tv_downloaded_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/download_detail_status_margin_top"
                android:text="Downloaded/Total episodes"
                android:textColor="#59ffffff"
                android:textSize="@dimen/download_detail_status_text_size"
                android:fontFamily="sans-serif" />

            <!-- Progress and file size container -->
            <LinearLayout
                android:id="@+id/ll_episode_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/download_detail_progress_margin_top"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:clickable="true"
                android:focusable="true"
                android:background="?android:attr/selectableItemBackgroundBorderless">

                <!-- Downloaded count (red) -->
                <TextView
                    android:id="@+id/tv_downloaded_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="EP.24"
                    android:textColor="#fff12626"
                    android:textSize="@dimen/download_detail_count_text_size"
                    android:fontFamily="sans-serif" />

                <!-- Separator -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="/"
                    android:textColor="#a3ffffff"
                    android:textSize="@dimen/download_detail_count_text_size"
                    android:fontFamily="sans-serif" />

                <!-- Total count -->
                <TextView
                    android:id="@+id/tv_total_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="EP.72"
                    android:textColor="#a3ffffff"
                    android:textSize="@dimen/download_detail_count_text_size"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- File size -->
            <TextView
                android:id="@+id/tv_total_file_size"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/download_detail_file_size_margin_top"
                android:text="187MB"
                android:textColor="#8fffffff"
                android:textSize="@dimen/download_detail_file_size_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Play button -->
        <LinearLayout
            android:id="@+id/btn_play"
            android:layout_width="@dimen/download_detail_play_button_width"
            android:layout_height="@dimen/download_detail_play_button_height"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:gravity="center"
            android:background="@drawable/download_play_button_bg"
            android:clickable="true"
            android:focusable="true">

            <!-- Play icon -->
            <ImageView
                android:layout_width="@dimen/download_detail_play_icon_size"
                android:layout_height="@dimen/download_detail_play_icon_size"
                android:layout_marginEnd="@dimen/download_detail_play_icon_margin_end"
                android:src="@drawable/list_ic_play" />

            <!-- Play text -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Play"
                android:textColor="#ffffffff"
                android:textSize="@dimen/download_detail_play_text_size"
                android:gravity="center"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </RelativeLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/bottom_sheet_divider_height"
        android:layout_marginTop="@dimen/bottom_sheet_divider_margin_top"
        android:layout_marginBottom="@dimen/bottom_sheet_divider_margin_bottom"
        android:background="@drawable/bottom_sheet_divider_bg" />

    <!-- Episodes list -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_episodes"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/download_detail_episodes_padding_horizontal"
        android:paddingEnd="@dimen/download_detail_episodes_padding_horizontal"
        android:paddingBottom="@dimen/download_detail_episodes_padding_bottom"
        android:clipToPadding="false"
        android:nestedScrollingEnabled="false"
        android:maxHeight="@dimen/bottom_sheet_episodes_max_height" />

</LinearLayout>
