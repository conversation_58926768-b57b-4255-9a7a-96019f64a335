package com.android.video.network;

import android.util.Log;

import com.android.video.constants.DiscoverApiConstantsUtils;
import com.android.video.model.response.ChapterListResponseModel;
import com.android.video.model.response.PlayProgressReportResponseModel;
import com.android.video.model.response.PlayUrlResponseModel;
import com.android.video.model.request.PlayProgressReportModel;
import com.android.video.utils.ApiHeaderUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 章节列表API服务类
 * <p>
 * 负责处理章节列表相关的网络请求，包括获取章节列表、播放进度等功能。
 * 使用OkHttp进行网络请求，Gson进行JSON解析。
 * </p>
 * 
 * <p>
 * 主要功能：
 * <ul>
 *   <li>获取章节列表 - {@link #getChapterList(String, ApiCallback)}</li>
 *   <li>上报播放进度 - {@link #reportPlayProgress(PlayProgressReportModel, ApiCallback)}</li>
 *   <li>获取播放地址 - {@link #getPlayUrl(String, String, int, ApiCallback)}</li>
 *   <li>自动处理请求头和错误处理</li>
 *   <li>异步回调机制</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * ChapterListApiService apiService = new ChapterListApiService();
 * apiService.getChapterList("c655490792384ed887bfe20e56f30c47", new ChapterListApiService.ApiCallback&lt;ChapterListResponseModel&gt;() {
 *     {@literal @}Override
 *     public void onSuccess(ChapterListResponseModel response) {
 *         // 处理成功响应
 *         if (response.isSuccess() && response.getData() != null) {
 *             List&lt;ChapterInfo&gt; chapters = response.getData().getChapterList();
 *             // 更新UI
 *         }
 *     }
 *     
 *     {@literal @}Override
 *     public void onError(String error) {
 *         // 处理错误
 *         Log.e(TAG, "获取章节列表失败: " + error);
 *     }
 * });
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see DiscoverApiConstantsUtils
 * @see ChapterListResponseModel
 */
public class ChapterListApiService {

    private static final String TAG = "ChapterListApiService";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient httpClient;
    private final Gson gson;

    /**
     * 构造函数
     */
    public ChapterListApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.httpClient = httpClient;
        this.gson = new Gson();
    }

    /**
     * API回调接口
     *
     * @param <T> 响应数据类型
     */
    public interface ApiCallback<T> {
        /**
         * 请求成功回调
         *
         * @param response 响应数据
         */
        void onSuccess(T response);

        /**
         * 请求失败回调
         *
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 获取章节列表
     * <p>
     * 根据短剧语言信息ID获取章节列表，包括短剧信息、播放进度和章节详情。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param callback 回调接口
     */
    public void getChapterList(String filmLanguageInfoId, ApiCallback<ChapterListResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            callback.onError("filmLanguageInfoId is not valid");
            return;
        }

        // 验证ID格式
        if (!DiscoverApiConstantsUtils.isValidFilmLanguageInfoId(filmLanguageInfoId)) {
            Log.w(TAG, "Invalid filmLanguageInfoId format: " + filmLanguageInfoId);
            // 不阻止请求，让服务器处理
        }

        // 构建请求URL
        String apiUrl = DiscoverApiConstantsUtils.buildChapterListUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            filmLanguageInfoId
        );

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "发起章节列表请求: " + apiUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 异步执行请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "章节列表响应: " + responseBody);

                        // 解析JSON响应
                        ChapterListResponseModel responseModel = gson.fromJson(responseBody, ChapterListResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            callback.onSuccess(responseModel);
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 上报播放进度
     * <p>
     * 向服务器上报用户的视频播放进度，包括当前播放位置和是否播放完成。
     * </p>
     *
     * @param reportModel 播放进度上报数据
     * @param callback 回调接口
     */
    public void reportPlayProgress(PlayProgressReportModel reportModel, ApiCallback<PlayProgressReportResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (reportModel == null) {
            callback.onError("Report model is null");
            return;
        }

        if (!reportModel.isValid()) {
            String error = reportModel.getValidationError();
            Log.e(TAG, "Invalid report model: " + error);
            callback.onError("Invalid report data: " + error);
            return;
        }

        // 构建请求URL
        String apiUrl = DiscoverApiConstantsUtils.buildReportPlayProgressUrl(
            EnvironmentConfigUtils.getApiBaseUrl()
        );

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 将请求模型转换为JSON
        String jsonBody = gson.toJson(reportModel);
        RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "发起播放进度上报请求: " + apiUrl);
        Log.d(TAG, "请求体: " + jsonBody);
        Log.d(TAG, "请求头: " + headers.toString());

        // 异步执行请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "播放进度上报网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "播放进度上报响应: " + responseBody);

                        // 解析JSON响应
                        PlayProgressReportResponseModel responseModel = gson.fromJson(responseBody, PlayProgressReportResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            Log.d(TAG, "播放进度上报成功: " + responseModel.getData());
                            callback.onSuccess(responseModel);
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "播放进度上报API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "播放进度上报HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "播放进度上报响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 获取播放地址
     * <p>
     * 根据短剧语言信息ID、章节ID、分辨率和操作类型获取视频播放地址。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param chapterId 章节ID
     * @param resolution 分辨率（auto/480/720/1080）
     * @param operationType 操作类型（1=播放, 2=下载）
     * @param callback 回调接口
     */
    public void getPlayUrl(String filmLanguageInfoId, String chapterId, String resolution, int operationType, ApiCallback<PlayUrlResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            callback.onError("Film language info ID cannot be null or empty");
            return;
        }

        if (chapterId == null || chapterId.trim().isEmpty()) {
            callback.onError("Chapter ID cannot be null or empty");
            return;
        }

        if (resolution == null || resolution.trim().isEmpty()) {
            callback.onError("Resolution cannot be null or empty");
            return;
        }

        if (operationType != 1 && operationType != 2) {
            callback.onError("Operation type must be 1 (play) or 2 (download)");
            return;
        }

        // 构建请求URL
        String apiUrl = DiscoverApiConstantsUtils.buildGetPlayUrlUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            filmLanguageInfoId,
            chapterId,
            resolution,
            operationType
        );

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取播放地址请求详情 ===");
        Log.d(TAG, "完整URL: " + apiUrl);
        Log.d(TAG, "请求参数详情:");
        Log.d(TAG, "  - filmLanguageInfoId: " + filmLanguageInfoId);
        Log.d(TAG, "  - chapterId: " + chapterId);
        Log.d(TAG, "  - resolution: " + resolution);
        Log.d(TAG, "  - operationType: " + operationType);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 验证URL构建是否正确
        String expectedUrl = EnvironmentConfigUtils.getApiBaseUrl() + "/app/player/getPlayUrl?filmLanguageInfoId=" + filmLanguageInfoId + "&chapterId=" + chapterId + "&resolution=" + resolution + "&operationType=" + operationType;
        Log.d(TAG, "预期URL: " + expectedUrl);
        Log.d(TAG, "实际URL: " + apiUrl);
        Log.d(TAG, "URL匹配: " + apiUrl.equals(expectedUrl));
        Log.d(TAG, "================================");

        // 异步执行请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "获取播放地址网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "获取播放地址响应: " + responseBody);

                        // 解析JSON响应
                        PlayUrlResponseModel responseModel = gson.fromJson(responseBody, PlayUrlResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            // 检查播放地址数据是否有效
                            if (responseModel.getData() != null && responseModel.getData().hasValidVideoUrl()) {
                                Log.d(TAG, "获取播放地址成功: " + responseModel.getData().getVideoUrl());
                                callback.onSuccess(responseModel);
                            } else {
                                String errorMsg = "播放地址数据无效";
                                Log.e(TAG, errorMsg);
                                callback.onError(errorMsg);
                            }
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "获取播放地址API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "获取播放地址HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "获取播放地址响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 取消所有进行中的请求
     * <p>
     * 在Activity或Fragment销毁时调用，避免内存泄漏。
     * </p>
     */
    public void cancelAllRequests() {
        if (httpClient != null) {
            httpClient.dispatcher().cancelAll();
            Log.d(TAG, "已取消所有进行中的请求");
        }
    }

    /**
     * 检查网络连接状态
     * <p>
     * 简单的网络状态检查，可以在发起请求前调用。
     * </p>
     * 
     * @return 如果网络可用返回true，否则返回false
     */
    public boolean isNetworkAvailable() {
        // 这里可以添加更复杂的网络检查逻辑
        // 目前返回true，假设网络总是可用的
        return true;
    }
}
