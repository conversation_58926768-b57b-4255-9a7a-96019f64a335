package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * Google Pay支付参数模型
 * <p>
 * 用于封装Google Pay支付所需的参数信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class GooglePayParams {

    /**
     * 产品ID
     */
    @SerializedName("productId")
    private String productId;

    /**
     * 订单ID
     */
    @SerializedName("orderId")
    private String orderId;

    /**
     * 价格
     */
    @SerializedName("price")
    private double price;

    /**
     * 货币代码
     */
    @SerializedName("currencyCode")
    private String currencyCode;

    /**
     * 开发者载荷
     */
    @SerializedName("developerPayload")
    private String developerPayload;

    /**
     * 商户ID
     */
    @SerializedName("merchantId")
    private String merchantId;

    /**
     * 商户名称
     */
    @SerializedName("merchantName")
    private String merchantName;

    /**
     * 时间戳
     */
    @SerializedName("timestamp")
    private long timestamp;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public GooglePayParams() {
    }

    /**
     * 完整构造函数
     */
    public GooglePayParams(String productId, String orderId, double price, String currencyCode,
                          String developerPayload, String merchantId, String merchantName, long timestamp) {
        this.productId = productId;
        this.orderId = orderId;
        this.price = price;
        this.currencyCode = currencyCode;
        this.developerPayload = developerPayload;
        this.merchantId = merchantId;
        this.merchantName = merchantName;
        this.timestamp = timestamp;
    }

    // ========== Getter和Setter方法 ==========

    public String getProductId() {
        return productId;
    }

    public void setProductId(String productId) {
        this.productId = productId;
    }

    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    public double getPrice() {
        return price;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public String getCurrencyCode() {
        return currencyCode;
    }

    public void setCurrencyCode(String currencyCode) {
        this.currencyCode = currencyCode;
    }

    public String getDeveloperPayload() {
        return developerPayload;
    }

    public void setDeveloperPayload(String developerPayload) {
        this.developerPayload = developerPayload;
    }

    public String getMerchantId() {
        return merchantId;
    }

    public void setMerchantId(String merchantId) {
        this.merchantId = merchantId;
    }

    public String getMerchantName() {
        return merchantName;
    }

    public void setMerchantName(String merchantName) {
        this.merchantName = merchantName;
    }

    public long getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(long timestamp) {
        this.timestamp = timestamp;
    }

    // ========== 业务方法 ==========

    /**
     * 验证参数是否有效
     * @return 如果参数有效则返回true，否则返回false
     */
    public boolean isValid() {
        return productId != null && !productId.trim().isEmpty() &&
               orderId != null && !orderId.trim().isEmpty() &&
               price > 0 &&
               currencyCode != null && !currencyCode.trim().isEmpty() &&
               merchantId != null && !merchantId.trim().isEmpty() &&
               merchantName != null && !merchantName.trim().isEmpty();
    }

    /**
     * 获取格式化的价格字符串
     * @return 格式化的价格字符串
     */
    public String getFormattedPrice() {
        return String.format("%.2f %s", price, currencyCode);
    }

    @Override
    public String toString() {
        return "GooglePayParams{" +
                "productId='" + productId + '\'' +
                ", orderId='" + orderId + '\'' +
                ", price=" + price +
                ", currencyCode='" + currencyCode + '\'' +
                ", developerPayload='" + developerPayload + '\'' +
                ", merchantId='" + merchantId + '\'' +
                ", merchantName='" + merchantName + '\'' +
                ", timestamp=" + timestamp +
                '}';
    }
}
