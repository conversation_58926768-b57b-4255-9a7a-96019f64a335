package com.android.video.model;

import java.io.Serializable;

/**
 * 剧集数据模型类
 * <AUTHOR>
 */
public class EpisodeModel implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private int episodeNumber;
    private String title;
    private String description;
    private String thumbnailUrl;
    private String duration;
    private boolean isWatched;
    private boolean isSelected;
    private boolean isLive;

    // 新增字段用于My List历史记录功能
    private int progress; // 播放进度（秒）
    private boolean isUnlock; // 是否解锁
    private boolean isCharge; // 是否付费
    private String lastPlayTime; // 最后播放时间

    /**
     * 默认构造函数
     */
    public EpisodeModel() {
        this.id = "";
        this.episodeNumber = 1;
        this.title = "";
        this.description = "";
        this.thumbnailUrl = "";
        this.duration = "";
        this.isWatched = false;
        this.isSelected = false;
        this.isLive = false;

        // 新增字段的默认值
        this.progress = 0;
        this.isUnlock = true; // 默认解锁（免费章节）
        this.isCharge = false; // 默认免费
        this.lastPlayTime = null;
    }

    /**
     * 完整构造函数
     */
    public EpisodeModel(String id, int episodeNumber, String title, String description, 
                       String thumbnailUrl, String duration, boolean isWatched, boolean isLive) {
        this.id = id;
        this.episodeNumber = episodeNumber;
        this.title = title;
        this.description = description;
        this.thumbnailUrl = thumbnailUrl;
        this.duration = duration;
        this.isWatched = isWatched;
        this.isSelected = false;
        this.isLive = isLive;

        // 新增字段的默认值
        this.progress = 0;
        this.isUnlock = true; // 默认解锁（免费章节）
        this.isCharge = false; // 默认免费
        this.lastPlayTime = null;
    }

    /**
     * 便利构造函数 - 基本信息
     */
    public EpisodeModel(String id, int episodeNumber, String title) {
        this.id = id;
        this.episodeNumber = episodeNumber;
        this.title = title;
        this.description = "";
        this.thumbnailUrl = "";
        this.duration = "";
        this.isWatched = false;
        this.isSelected = false;
        this.isLive = false;

        // 新增字段的默认值
        this.progress = 0;
        this.isUnlock = true; // 默认解锁（免费章节）
        this.isCharge = false; // 默认免费
        this.lastPlayTime = null;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public int getEpisodeNumber() {
        return episodeNumber;
    }
    
    public void setEpisodeNumber(int episodeNumber) {
        this.episodeNumber = episodeNumber;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public String getThumbnailUrl() {
        return thumbnailUrl;
    }
    
    public void setThumbnailUrl(String thumbnailUrl) {
        this.thumbnailUrl = thumbnailUrl;
    }
    
    public String getDuration() {
        return duration;
    }
    
    public void setDuration(String duration) {
        this.duration = duration;
    }
    
    public boolean isWatched() {
        return isWatched;
    }
    
    public void setWatched(boolean watched) {
        isWatched = watched;
    }
    
    public boolean isSelected() {
        return isSelected;
    }
    
    public void setSelected(boolean selected) {
        isSelected = selected;
    }
    
    public boolean isLive() {
        return isLive;
    }
    
    public void setLive(boolean live) {
        isLive = live;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public boolean isUnlock() {
        return isUnlock;
    }

    public void setUnlock(boolean unlock) {
        isUnlock = unlock;
    }

    public boolean isCharge() {
        return isCharge;
    }

    public void setCharge(boolean charge) {
        isCharge = charge;
    }

    public String getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(String lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    /**
     * 便利方法
     */
    public String getDisplayTitle() {
        return title != null && !title.isEmpty() ? title : "Episode " + episodeNumber;
    }
    
    public String getEpisodeText() {
        return String.valueOf(episodeNumber);
    }
    
    public void toggleSelection() {
        this.isSelected = !this.isSelected;
    }
    
    public void toggleWatched() {
        this.isWatched = !this.isWatched;
    }
    
    @Override
    public String toString() {
        return "EpisodeModel{" +
                "id='" + id + '\'' +
                ", episodeNumber=" + episodeNumber +
                ", title='" + title + '\'' +
                ", isWatched=" + isWatched +
                ", isSelected=" + isSelected +
                ", isLive=" + isLive +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        EpisodeModel that = (EpisodeModel) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
