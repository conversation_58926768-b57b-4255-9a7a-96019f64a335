<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/search_trending_card_height"
    android:layout_marginVertical="@dimen/search_trending_card_margin_vertical"
    android:background="@drawable/search_trending_card_background"
    android:clickable="true"
    android:focusable="true">

    <!-- 排序背景 -->
    <FrameLayout
        android:id="@+id/fl_rank_background"
        android:layout_width="@dimen/search_rank_bg_size"
        android:layout_height="@dimen/search_rank_bg_size"
        android:layout_marginStart="@dimen/search_trending_card_padding"
        android:background="@drawable/search_rank_background"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- 排序数字 -->
        <TextView
            android:id="@+id/tv_rank"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:text="1"
            android:textColor="#ffffffff"
            android:textSize="@dimen/search_rank_text_size"
            android:textStyle="bold" />

    </FrameLayout>

    <!-- 海报 -->
    <ImageView
        android:id="@+id/iv_poster"
        android:layout_width="@dimen/search_poster_size"
        android:layout_height="@dimen/search_poster_size"
        android:layout_marginStart="@dimen/search_poster_margin_start"
        android:background="@drawable/search_poster_background"
        android:scaleType="fitXY"
        android:src="@drawable/movie_poster"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toEndOf="@id/fl_rank_background"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 内容区域 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/search_content_margin_start"
        android:layout_marginEnd="@dimen/search_trending_card_padding"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="@id/iv_poster"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@id/iv_poster"
        app:layout_constraintTop_toTopOf="@id/iv_poster">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Eternal Love"
            android:textColor="#ffffffff"
            android:textSize="@dimen/search_title_text_size"
            android:maxLines="2"
            android:ellipsize="end" />

        <!-- 搜索次数区域 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/search_count_margin_top"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <!-- 搜索图标 -->
            <ImageView
                android:id="@+id/iv_search_icon"
                android:layout_width="@dimen/search_count_icon_size"
                android:layout_height="@dimen/search_count_icon_size"
                android:layout_marginEnd="@dimen/search_count_icon_margin_end"
                android:src="@drawable/home_ic_searched" />

            <!-- 搜索次数 -->
            <TextView
                android:id="@+id/tv_search_count"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="138.7k"
                android:textColor="#ccffffff"
                android:textSize="@dimen/search_count_text_size" />

        </LinearLayout>

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
