package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import com.android.video.manager.TokenManager;

/**
 * Token诊断工具类
 * <p>
 * 用于诊断和修复token相关问题
 * </p>
 */
public class TokenDiagnosticUtils {
    private static final String TAG = "TokenDiagnostic";
    
    /**
     * 执行完整的token诊断
     */
    public static void performFullDiagnosis(Context context) {
        Log.i(TAG, "=== 开始Token诊断 ===");
        
        // 1. 检查ApiHeaderUtils状态
        diagnosisApiHeaderUtils(context);
        
        // 2. 检查TokenManager状态
        diagnosisTokenManager(context);
        
        // 3. 检查token一致性
        diagnosisTokenConsistency(context);
        
        // 4. 尝试修复问题
        attemptTokenFix(context);
        
        Log.i(TAG, "=== Token诊断完成 ===");
    }
    
    /**
     * 诊断ApiHeaderUtils状态
     */
    private static void diagnosisApiHeaderUtils(Context context) {
        Log.i(TAG, "--- 诊断ApiHeaderUtils ---");
        
        try {
            String currentToken = ApiHeaderUtils.getCurrentAccessToken();
            Log.i(TAG, "ApiHeaderUtils.getCurrentAccessToken(): " + maskToken(currentToken));
            
            boolean isValid = ApiHeaderUtils.isAccessTokenValid();
            Log.i(TAG, "ApiHeaderUtils.isAccessTokenValid(): " + isValid);
            
            // 检查是否使用硬编码token
            if (currentToken != null && isHardcodedToken(currentToken)) {
                Log.e(TAG, "❌ 检测到硬编码token: " + maskToken(currentToken));
            }
            
        } catch (Exception e) {
            Log.e(TAG, "ApiHeaderUtils诊断失败: " + e.getMessage());
        }
    }
    
    /**
     * 诊断TokenManager状态
     */
    private static void diagnosisTokenManager(Context context) {
        Log.i(TAG, "--- 诊断TokenManager ---");
        
        try {
            TokenManager tokenManager = TokenManager.getInstance(context);
            String cachedToken = tokenManager.getCurrentToken();
            Log.i(TAG, "TokenManager.getCurrentToken(): " + maskToken(cachedToken));
            
            String deviceId = tokenManager.getCurrentDeviceId();
            Log.i(TAG, "TokenManager.getCurrentDeviceId(): " + deviceId);
            
            String customerId = tokenManager.getCurrentCustomerId();
            Log.i(TAG, "TokenManager.getCurrentCustomerId(): " + customerId);
            
            boolean needsRefresh = tokenManager.needsTokenRefresh();
            Log.i(TAG, "TokenManager.needsTokenRefresh(): " + needsRefresh);
            
        } catch (Exception e) {
            Log.e(TAG, "TokenManager诊断失败: " + e.getMessage());
        }
    }
    
    /**
     * 诊断token一致性
     */
    private static void diagnosisTokenConsistency(Context context) {
        Log.i(TAG, "--- 诊断Token一致性 ---");
        
        try {
            String apiHeaderToken = ApiHeaderUtils.getCurrentAccessToken();
            TokenManager tokenManager = TokenManager.getInstance(context);
            String managerToken = tokenManager.getCurrentToken();
            
            Log.i(TAG, "ApiHeaderUtils token: " + maskToken(apiHeaderToken));
            Log.i(TAG, "TokenManager token: " + maskToken(managerToken));
            
            if (apiHeaderToken == null && managerToken == null) {
                Log.w(TAG, "⚠️ 两个地方都没有token");
            } else if (apiHeaderToken == null) {
                Log.w(TAG, "⚠️ ApiHeaderUtils没有token，但TokenManager有token");
            } else if (managerToken == null) {
                Log.w(TAG, "⚠️ TokenManager没有token，但ApiHeaderUtils有token");
            } else if (!apiHeaderToken.equals(managerToken)) {
                Log.w(TAG, "⚠️ 两个地方的token不一致");
            } else {
                Log.i(TAG, "✅ Token一致性检查通过");
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Token一致性诊断失败: " + e.getMessage());
        }
    }
    
    /**
     * 尝试修复token问题
     */
    private static void attemptTokenFix(Context context) {
        Log.i(TAG, "--- 尝试修复Token问题 ---");
        
        try {
            TokenManager tokenManager = TokenManager.getInstance(context);
            String managerToken = tokenManager.getCurrentToken();
            String apiHeaderToken = ApiHeaderUtils.getCurrentAccessToken();
            
            // 如果TokenManager有token但ApiHeaderUtils没有，同步过去
            if (managerToken != null && !managerToken.trim().isEmpty() && 
                (apiHeaderToken == null || apiHeaderToken.trim().isEmpty())) {
                Log.i(TAG, "🔧 同步TokenManager的token到ApiHeaderUtils");
                ApiHeaderUtils.setAccessToken(managerToken);
                Log.i(TAG, "✅ Token同步完成");
            }
            
            // 如果都没有token，尝试重新初始化
            if ((managerToken == null || managerToken.trim().isEmpty()) && 
                (apiHeaderToken == null || apiHeaderToken.trim().isEmpty())) {
                Log.i(TAG, "🔧 两个地方都没有token，尝试重新初始化TokenManager");
                tokenManager.initialize(new TokenManager.TokenInitCallback() {
                    @Override
                    public void onInitialized(boolean success, String message) {
                        if (success) {
                            Log.i(TAG, "✅ TokenManager重新初始化成功: " + message);
                        } else {
                            Log.e(TAG, "❌ TokenManager重新初始化失败: " + message);
                        }
                    }
                });
            }
            
            // 如果检测到硬编码token，清除并重新初始化
            if (apiHeaderToken != null && isHardcodedToken(apiHeaderToken)) {
                Log.i(TAG, "🔧 检测到硬编码token，清除并重新初始化");
                ApiHeaderUtils.resetAccessToken();
                tokenManager.clearToken();
                tokenManager.initialize(new TokenManager.TokenInitCallback() {
                    @Override
                    public void onInitialized(boolean success, String message) {
                        if (success) {
                            Log.i(TAG, "✅ 硬编码token清除后重新初始化成功: " + message);
                        } else {
                            Log.e(TAG, "❌ 硬编码token清除后重新初始化失败: " + message);
                        }
                    }
                });
            }
            
        } catch (Exception e) {
            Log.e(TAG, "Token修复失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否是硬编码token
     */
    private static boolean isHardcodedToken(String token) {
        if (token == null) return false;
        
        return "deprecated_hardcoded_token".equals(token) ||
               "05358c87b76645feaae46740fac753c9".equals(token) ||
               "deprecated_dev_test_token".equals(token) ||
               "deprecated_test_env_token".equals(token) ||
               "use_token_manager_instead".equals(token);
    }
    
    /**
     * 脱敏处理token用于日志输出
     */
    private static String maskToken(String token) {
        if (token == null) return "null";
        if (token.length() <= 8) return "***";
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }
    
    /**
     * 强制重置所有token
     */
    public static void forceResetAllTokens(Context context) {
        Log.i(TAG, "🔄 强制重置所有token...");
        
        try {
            // 清除ApiHeaderUtils中的token
            ApiHeaderUtils.resetAccessToken();
            
            // 清除TokenManager中的token
            TokenManager tokenManager = TokenManager.getInstance(context);
            tokenManager.clearToken();
            
            Log.i(TAG, "✅ 所有token已清除");
            
            // 重新初始化
            tokenManager.initialize(new TokenManager.TokenInitCallback() {
                @Override
                public void onInitialized(boolean success, String message) {
                    if (success) {
                        Log.i(TAG, "✅ 强制重置后重新初始化成功: " + message);
                    } else {
                        Log.e(TAG, "❌ 强制重置后重新初始化失败: " + message);
                    }
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "强制重置token失败: " + e.getMessage());
        }
    }
}
