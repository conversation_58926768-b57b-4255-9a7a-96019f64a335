package com.android.video.model;

/**
 * 反馈类型模型
 */
public class FeedbackType {
    private String feedbackTypeId;
    private String feedbackTypeName;
    private int isDelete;
    private String createTime;
    private String updateTime;
    private boolean isSelected; // 本地选中状态，不来自API

    /**
     * 构造方法 - 用于API响应数据
     */
    public FeedbackType(String feedbackTypeId, String feedbackTypeName, int isDelete, String createTime, String updateTime) {
        this.feedbackTypeId = feedbackTypeId;
        this.feedbackTypeName = feedbackTypeName;
        this.isDelete = isDelete;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.isSelected = false; // 默认未选中
    }

    /**
     * 构造方法 - 用于本地测试数据（保持向后兼容）
     */
    public FeedbackType(String id, String name, boolean isSelected) {
        this.feedbackTypeId = id;
        this.feedbackTypeName = name;
        this.isSelected = isSelected;
        this.isDelete = 1; // 默认为正常状态
    }

    // Getter和Setter方法

    public String getFeedbackTypeId() {
        return feedbackTypeId;
    }

    public void setFeedbackTypeId(String feedbackTypeId) {
        this.feedbackTypeId = feedbackTypeId;
    }

    public String getFeedbackTypeName() {
        return feedbackTypeName;
    }

    public void setFeedbackTypeName(String feedbackTypeName) {
        this.feedbackTypeName = feedbackTypeName;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    // 兼容性方法（保持向后兼容）

    /**
     * 获取ID（兼容旧代码）
     */
    public String getId() {
        return feedbackTypeId;
    }

    /**
     * 设置ID（兼容旧代码）
     */
    public void setId(String id) {
        this.feedbackTypeId = id;
    }

    /**
     * 获取名称（兼容旧代码）
     */
    public String getName() {
        return feedbackTypeName;
    }

    /**
     * 设置名称（兼容旧代码）
     */
    public void setName(String name) {
        this.feedbackTypeName = name;
    }
}
