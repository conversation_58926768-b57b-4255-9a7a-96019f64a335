package com.android.video.model.response;

import com.android.video.model.VideoModel;
import com.android.video.model.WaterfallRecommendItemModel;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * 瀑布流推荐分页数据模型
 * <p>
 * 用于映射瀑布流推荐API返回的分页数据结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>records: 瀑布流推荐记录列表</li>
 *   <li>total: 总记录数</li>
 *   <li>size: 每页数量</li>
 *   <li>current: 当前页码</li>
 *   <li>pages: 总页数</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * WaterfallRecommendDataModel dataModel = gson.fromJson(jsonObject, WaterfallRecommendDataModel.class);
 *
 * // 获取推荐数据
 * List&lt;WaterfallRecommendItemModel&gt; recommendList = dataModel.getRecords();
 * boolean hasMore = dataModel.hasMorePages();
 * 
 * // 转换为VideoModel列表
 * List&lt;VideoModel&gt; videoList = dataModel.toVideoModelList();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallRecommendDataModel {
    
    /**
     * 瀑布流推荐记录列表
     */
    @SerializedName("records")
    private List<WaterfallRecommendItemModel> records;
    
    /**
     * 总记录数
     */
    @SerializedName("total")
    private int total;
    
    /**
     * 每页数量
     */
    @SerializedName("size")
    private int size;
    
    /**
     * 当前页码
     */
    @SerializedName("current")
    private int current;
    
    /**
     * 排序信息
     */
    @SerializedName("orders")
    private List<Object> orders;
    
    /**
     * 是否优化count SQL
     */
    @SerializedName("optimizeCountSql")
    private boolean optimizeCountSql;
    
    /**
     * 是否命中count
     */
    @SerializedName("hitCount")
    private boolean hitCount;
    
    /**
     * count ID
     */
    @SerializedName("countId")
    private String countId;
    
    /**
     * 最大限制
     */
    @SerializedName("maxLimit")
    private String maxLimit;
    
    /**
     * 是否搜索count
     */
    @SerializedName("searchCount")
    private boolean searchCount;
    
    /**
     * 总页数
     */
    @SerializedName("pages")
    private int pages;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallRecommendDataModel() {
        this.records = new ArrayList<>();
        this.orders = new ArrayList<>();
    }

    /**
     * 简化构造函数
     */
    public WaterfallRecommendDataModel(List<WaterfallRecommendItemModel> records, int total, int size, int current, int pages) {
        this.records = records != null ? records : new ArrayList<>();
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = pages;
        this.orders = new ArrayList<>();
        this.optimizeCountSql = true;
        this.hitCount = false;
        this.searchCount = true;
    }

    // ========== Getter和Setter方法 ==========

    public List<WaterfallRecommendItemModel> getRecords() {
        return records;
    }

    public void setRecords(List<WaterfallRecommendItemModel> records) {
        this.records = records != null ? records : new ArrayList<>();
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public List<Object> getOrders() {
        return orders;
    }

    public void setOrders(List<Object> orders) {
        this.orders = orders != null ? orders : new ArrayList<>();
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql;
    }

    public void setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
    }

    public boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(boolean hitCount) {
        this.hitCount = hitCount;
    }

    public String getCountId() {
        return countId;
    }

    public void setCountId(String countId) {
        this.countId = countId;
    }

    public String getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(String maxLimit) {
        this.maxLimit = maxLimit;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否有更多页面
     * @return 如果有更多页面则返回true
     */
    public boolean hasMorePages() {
        return current < pages;
    }

    /**
     * 检查是否为空数据
     * @return 如果没有记录则返回true
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取记录数量
     * @return 当前页面的记录数量
     */
    public int getRecordCount() {
        return records != null ? records.size() : 0;
    }

    /**
     * 获取下一页页码
     * @return 下一页页码，如果没有更多页面则返回-1
     */
    public int getNextPage() {
        return hasMorePages() ? current + 1 : -1;
    }

    /**
     * 转换为VideoModel列表
     * @return VideoModel列表
     */
    public List<VideoModel> toVideoModelList() {
        List<VideoModel> videoList = new ArrayList<>();
        
        if (records != null) {
            for (WaterfallRecommendItemModel item : records) {
                if (item != null && item.hasValidVideoData()) {
                    VideoModel videoModel = item.toVideoModel();
                    if (videoModel != null) {
                        videoList.add(videoModel);
                    }
                }
            }
        }
        
        return videoList;
    }

    /**
     * 获取有效的推荐项目列表
     * @return 有效的推荐项目列表
     */
    public List<WaterfallRecommendItemModel> getValidRecords() {
        List<WaterfallRecommendItemModel> validRecords = new ArrayList<>();
        
        if (records != null) {
            for (WaterfallRecommendItemModel item : records) {
                if (item != null && item.hasValidVideoData()) {
                    validRecords.add(item);
                }
            }
        }
        
        return validRecords;
    }

    @Override
    public String toString() {
        return "WaterfallRecommendDataModel{" +
                "records=" + (records != null ? records.size() : 0) + " items" +
                ", total=" + total +
                ", size=" + size +
                ", current=" + current +
                ", pages=" + pages +
                ", hasMorePages=" + hasMorePages() +
                '}';
    }
}
