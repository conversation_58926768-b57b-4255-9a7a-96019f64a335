package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.VideoRecord;

import java.util.List;

/**
 * Video Record记录适配器
 */
public class VideoRecordAdapter extends RecyclerView.Adapter<VideoRecordAdapter.ViewHolder> {

    private List<VideoRecord> videoRecordList;

    public VideoRecordAdapter(List<VideoRecord> videoRecordList) {
        this.videoRecordList = videoRecordList;
    }

    @NonNull
    @Override
    public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_video_record, parent, false);
        return new ViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
        VideoRecord videoRecord = videoRecordList.get(position);
        holder.bind(videoRecord);
    }

    @Override
    public int getItemCount() {
        return videoRecordList != null ? videoRecordList.size() : 0;
    }

    /**
     * 更新数据
     */
    public void updateData(List<VideoRecord> newData) {
        this.videoRecordList = newData;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    public static class ViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private TextView tvTitle;
        private TextView tvCost;
        private TextView tvEpisode;
        private TextView tvDescription;
        private TextView tvDateTime;
        private ImageView ivCostIcon;

        public ViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvCost = itemView.findViewById(R.id.tv_cost);
            tvEpisode = itemView.findViewById(R.id.tv_episode);
            tvDescription = itemView.findViewById(R.id.tv_description);
            tvDateTime = itemView.findViewById(R.id.tv_date_time);
            ivCostIcon = itemView.findViewById(R.id.iv_cost_icon);
        }

        /**
         * 绑定数据
         */
        public void bind(VideoRecord videoRecord) {
            if (videoRecord == null) {
                return;
            }

            tvTitle.setText(videoRecord.getTitle());
            tvCost.setText(videoRecord.getCost());
            tvEpisode.setText(videoRecord.getEpisode());
            tvDescription.setText(videoRecord.getDescription());
            tvDateTime.setText(videoRecord.getDateTime());

            // 设置海报图片
            if (videoRecord.getPosterRes() != 0) {
                ivPoster.setImageResource(videoRecord.getPosterRes());
            } else if (videoRecord.getPosterUrl() != null && !videoRecord.getPosterUrl().isEmpty()) {
                // 使用Glide加载网络图片
                Glide.with(itemView.getContext())
                        .load(videoRecord.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }
        }
    }
}
