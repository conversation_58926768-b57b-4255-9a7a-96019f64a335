package com.android.video.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.Signature;
import android.provider.Settings;
import android.util.Log;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.UUID;

/**
 * 设备ID生成和管理工具类 - 生成唯一且持久的设备标识
 * <AUTHOR> Team
 */
public class DeviceIdUtils {

    private static final String TAG = "DeviceIdUtils";
    private static final String PREF_NAME = "VideoPlayerDeviceInfo";
    private static final String KEY_DEVICE_ID = "deviceId";
    private static final String KEY_DEVICE_UUID = "deviceUuid";
    
    private static String cachedDeviceId = null;

    /**
     * 获取设备唯一标识
     * @param context 上下文
     * @return 设备ID字符串
     */
    public static String getDeviceId(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot generate device ID");
            return generateFallbackId();
        }

        // 如果已缓存，直接返回
        if (cachedDeviceId != null && !cachedDeviceId.isEmpty()) {
            return cachedDeviceId;
        }

        // 尝试从SharedPreferences获取已保存的设备ID
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        String savedDeviceId = prefs.getString(KEY_DEVICE_ID, null);
        
        if (savedDeviceId != null && !savedDeviceId.isEmpty()) {
            cachedDeviceId = savedDeviceId;
            Log.d(TAG, "Retrieved saved device ID");
            return cachedDeviceId;
        }

        // 生成新的设备ID
        String newDeviceId = generateDeviceId(context);
        
        // 保存到SharedPreferences
        saveDeviceId(context, newDeviceId);
        
        cachedDeviceId = newDeviceId;
        Log.d(TAG, "Generated and saved new device ID");
        return cachedDeviceId;
    }

    /**
     * 生成设备ID
     * @param context 上下文
     * @return 生成的设备ID
     */
    private static String generateDeviceId(Context context) {
        try {
            // 策略1: 使用ANDROID_ID
            String androidId = getAndroidId(context);
            if (isValidId(androidId)) {
                String deviceId = generateHashedId(androidId, context);
                if (deviceId != null) {
                    Log.d(TAG, "Generated device ID using ANDROID_ID");
                    return deviceId;
                }
            }

            // 策略2: 使用UUID作为备用方案
            String uuid = generateUuidBasedId(context);
            Log.d(TAG, "Generated device ID using UUID fallback");
            return uuid;

        } catch (Exception e) {
            Log.e(TAG, "Error generating device ID: " + e.getMessage());
            return generateFallbackId();
        }
    }

    /**
     * 获取Android ID
     * @param context 上下文
     * @return Android ID
     */
    private static String getAndroidId(Context context) {
        try {
            String androidId = Settings.Secure.getString(context.getContentResolver(), Settings.Secure.ANDROID_ID);
            Log.d(TAG, "Retrieved ANDROID_ID: " + (androidId != null ? "exists" : "null"));
            return androidId;
        } catch (Exception e) {
            Log.e(TAG, "Failed to get ANDROID_ID: " + e.getMessage());
            return null;
        }
    }

    /**
     * 验证ID是否有效
     * @param id 要验证的ID
     * @return 是否有效
     */
    private static boolean isValidId(String id) {
        // 检查是否为null、空字符串或已知的无效值
        return id != null && 
               !id.isEmpty() && 
               !"9774d56d682e549c".equals(id) && // Android模拟器常见值
               !"unknown".equals(id) &&
               !"000000000000000".equals(id);
    }

    /**
     * 生成基于哈希的设备ID
     * @param baseId 基础ID
     * @param context 上下文
     * @return 哈希后的设备ID
     */
    private static String generateHashedId(String baseId, Context context) {
        try {
            // 获取应用签名信息增强唯一性
            String packageName = context.getPackageName();
            String signature = getAppSignature(context);
            
            // 组合信息生成哈希
            String combined = baseId + packageName + signature;
            
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(combined.getBytes());
            
            // 转换为十六进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            // 返回前16位作为设备ID
            return hexString.toString().substring(0, 16);
            
        } catch (NoSuchAlgorithmException e) {
            Log.e(TAG, "SHA-256 algorithm not available: " + e.getMessage());
            return null;
        }
    }

    /**
     * 获取应用签名信息
     * @param context 上下文
     * @return 签名字符串
     */
    private static String getAppSignature(Context context) {
        try {
            PackageInfo packageInfo = context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), PackageManager.GET_SIGNATURES);
            
            if (packageInfo.signatures != null && packageInfo.signatures.length > 0) {
                Signature signature = packageInfo.signatures[0];
                return String.valueOf(signature.hashCode());
            }
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "Package not found: " + e.getMessage());
        }
        return "default_signature";
    }

    /**
     * 生成基于UUID的设备ID
     * @param context 上下文
     * @return UUID设备ID
     */
    private static String generateUuidBasedId(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        String savedUuid = prefs.getString(KEY_DEVICE_UUID, null);
        
        if (savedUuid != null && !savedUuid.isEmpty()) {
            return savedUuid;
        }
        
        // 生成新的UUID
        String newUuid = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        
        // 保存UUID
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_DEVICE_UUID, newUuid);
        editor.apply();
        
        return newUuid;
    }

    /**
     * 生成备用设备ID
     * @return 备用设备ID
     */
    private static String generateFallbackId() {
        return "fallback_" + System.currentTimeMillis();
    }

    /**
     * 保存设备ID到SharedPreferences
     * @param context 上下文
     * @param deviceId 设备ID
     */
    private static void saveDeviceId(Context context, String deviceId) {
        try {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.putString(KEY_DEVICE_ID, deviceId);
            editor.apply();
            Log.d(TAG, "Device ID saved to SharedPreferences");
        } catch (Exception e) {
            Log.e(TAG, "Failed to save device ID: " + e.getMessage());
        }
    }

    /**
     * 清除缓存的设备ID（用于测试）
     */
    public static void clearCache() {
        cachedDeviceId = null;
        Log.d(TAG, "Device ID cache cleared");
    }

    /**
     * 清除所有设备ID信息（用于测试）
     * @param context 上下文
     */
    public static void clearAllDeviceInfo(Context context) {
        if (context != null) {
            SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs.edit();
            editor.clear();
            editor.apply();
            clearCache();
            Log.d(TAG, "All device info cleared");
        }
    }

    /**
     * 获取设备ID信息（用于调试）
     * @param context 上下文
     * @return 设备ID信息字符串
     */
    public static String getDeviceIdInfo(Context context) {
        if (context == null) {
            return "Context is null";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("Device ID: ").append(getDeviceId(context)).append("\n");
        info.append("Android ID: ").append(getAndroidId(context)).append("\n");
        info.append("Package Name: ").append(context.getPackageName()).append("\n");
        info.append("App Signature: ").append(getAppSignature(context));
        
        return info.toString();
    }
}
