<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/feedback_type_margin_end"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_type_name"
        android:layout_width="wrap_content"
        android:layout_height="@dimen/feedback_type_selected_height"
        android:background="@drawable/feedback_type_normal_bg"
        android:textColor="@color/feedback_type_unselected_text"
        android:textSize="16sp"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:layout_marginEnd="@dimen/feedback_type_margin_end"
        android:layout_marginBottom="8dp"
        android:maxLines="1"
        android:ellipsize="end"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
