package com.android.video.utils;

import android.content.Context;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import com.android.video.manager.LocalizationManager;
import java.util.ArrayList;
import java.util.List;

/**
 * 翻译测试工具类
 * 用于调试和验证翻译功能
 */
public class TranslationTestUtils {
    
    private static final String TAG = "TranslationTestUtils";
    
    /**
     * 测试翻译功能
     */
    public static void testTranslation(Context context) {
        Log.d(TAG, "=== 开始翻译功能测试 ===");
        
        try {
            LocalizationManager manager = LocalizationManager.getInstance(context);
            
            // 测试当前语言
            int currentLang = manager.getCurrentLanguageType();
            Log.d(TAG, "当前语言类型: " + currentLang);
            
            // 测试常用翻译键
            String[] testKeys = {
                "home", "profile", "language", "message", "bill", "setting",
                "guest", "log_in", "unlimited_access_to_all_series"
            };
            
            for (String key : testKeys) {
                String translation = manager.getString(key);
                Log.d(TAG, "翻译测试 [" + key + "]: " + translation);
            }
            
            // 测试所有语言
            for (int lang = 1; lang <= 3; lang++) {
                Log.d(TAG, "--- 语言 " + lang + " ---");
                for (String key : testKeys) {
                    String translation = manager.getStringForLanguage(key, lang);
                    Log.d(TAG, "  " + key + ": " + translation);
                }
            }
            
        } catch (Exception e) {
            Log.e(TAG, "翻译测试失败", e);
        }
        
        Log.d(TAG, "=== 翻译功能测试完成 ===");
    }
    
    /**
     * 扫描View中的所有文本
     */
    public static void scanViewTexts(View rootView) {
        Log.d(TAG, "=== 开始扫描View中的文本 ===");
        
        List<String> allTexts = new ArrayList<>();
        collectAllTexts(rootView, allTexts);
        
        Log.d(TAG, "发现 " + allTexts.size() + " 个文本元素:");
        for (int i = 0; i < allTexts.size(); i++) {
            Log.d(TAG, "  " + (i + 1) + ". " + allTexts.get(i));
        }
        
        Log.d(TAG, "=== 文本扫描完成 ===");
    }
    
    /**
     * 递归收集所有文本
     */
    private static void collectAllTexts(View view, List<String> texts) {
        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            String text = textView.getText().toString().trim();
            if (!text.isEmpty() && !texts.contains(text)) {
                texts.add(text);
            }
        } else if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                collectAllTexts(viewGroup.getChildAt(i), texts);
            }
        }
    }
    
    /**
     * 验证文本映射覆盖率
     */
    public static void validateTextMappingCoverage(View rootView) {
        Log.d(TAG, "=== 验证文本映射覆盖率 ===");
        
        List<String> allTexts = new ArrayList<>();
        collectAllTexts(rootView, allTexts);
        
        int mappedCount = 0;
        int unmappedCount = 0;
        
        for (String text : allTexts) {
            boolean hasMappingKey = GlobalTextUpdater.getAllTextMappings().containsKey(text);
            if (hasMappingKey) {
                mappedCount++;
                Log.d(TAG, "✓ 已映射: " + text);
            } else {
                unmappedCount++;
                Log.w(TAG, "✗ 未映射: " + text);
            }
        }
        
        float coverage = allTexts.size() > 0 ? (float) mappedCount / allTexts.size() * 100 : 0;
        Log.d(TAG, "映射覆盖率: " + String.format("%.1f%%", coverage) + 
                   " (" + mappedCount + "/" + allTexts.size() + ")");
        
        Log.d(TAG, "=== 验证完成 ===");
    }
    
    /**
     * 强制更新指定View的所有文本
     */
    public static void forceUpdateViewTexts(View rootView, Context context) {
        Log.d(TAG, "=== 强制更新View文本 ===");
        
        try {
            GlobalTextUpdater.updateViewTexts(rootView, context);
            Log.d(TAG, "文本更新完成");
        } catch (Exception e) {
            Log.e(TAG, "文本更新失败", e);
        }
        
        Log.d(TAG, "=== 更新完成 ===");
    }
}
