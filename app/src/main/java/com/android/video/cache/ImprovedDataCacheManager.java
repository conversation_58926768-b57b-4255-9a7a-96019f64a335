package com.android.video.cache;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 改进的数据缓存管理器
 * 解决缓存数据不一致和污染问题
 * <AUTHOR> Team
 */
public class ImprovedDataCacheManager {
    
    private static final String TAG = "ImprovedDataCacheManager";
    private static final String CACHE_PREFS_NAME = "improved_data_cache_prefs";
    private static final String CACHE_VERSION_SUFFIX = "_version";
    private static final String CACHE_TIMESTAMP_SUFFIX = "_timestamp";
    private static final String CACHE_SIZE_SUFFIX = "_size";
    
    // 缓存版本，用于检测数据一致性
    private static final int CURRENT_CACHE_VERSION = 1;
    
    private static ImprovedDataCacheManager instance;
    private Context context;
    private SharedPreferences cachePrefs;
    private Gson gson;
    
    // 读写锁，确保线程安全
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();
    private final ReentrantReadWriteLock.ReadLock readLock = lock.readLock();
    private final ReentrantReadWriteLock.WriteLock writeLock = lock.writeLock();
    
    // 缓存操作计数器，用于检测并发问题
    private final ConcurrentHashMap<String, Integer> operationCounters = new ConcurrentHashMap<>();
    
    private ImprovedDataCacheManager(Context context) {
        this.context = context.getApplicationContext();
        this.cachePrefs = context.getSharedPreferences(CACHE_PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        
        Log.d(TAG, "ImprovedDataCacheManager initialized");
    }
    
    public static synchronized ImprovedDataCacheManager getInstance(Context context) {
        if (instance == null) {
            instance = new ImprovedDataCacheManager(context);
        }
        return instance;
    }
    
    /**
     * 原子性缓存数据操作
     * 支持缓存空数据（null值会被转换为特殊标记）
     */
    public <T> boolean cacheDataAtomic(String key, T data) {
        if (key == null) {
            Log.w(TAG, "Cannot cache null key");
            return false;
        }
        
        writeLock.lock();
        try {
            // 增加操作计数器
            int operationId = operationCounters.compute(key, (k, v) -> (v == null) ? 1 : v + 1);
            
            Log.d(TAG, "开始原子性缓存操作: " + key + " (操作ID: " + operationId + ")");
            
            // 先清除旧数据
            clearCachedDataInternal(key);
            
            // 序列化数据
            String jsonData = gson.toJson(data);
            long currentTime = System.currentTimeMillis();
            
            // 计算数据大小（用于验证）
            int dataSize = jsonData.length();
            
            // 原子性写入所有相关数据
            SharedPreferences.Editor editor = cachePrefs.edit();
            editor.putString(key, jsonData);
            editor.putInt(key + CACHE_VERSION_SUFFIX, CURRENT_CACHE_VERSION);
            editor.putLong(key + CACHE_TIMESTAMP_SUFFIX, currentTime);
            editor.putInt(key + CACHE_SIZE_SUFFIX, dataSize);
            
            // 使用commit()确保原子性，而不是apply()
            boolean success = editor.commit();
            
            if (success) {
                Log.d(TAG, "缓存数据成功: " + key + " (大小: " + dataSize + " 字符, 操作ID: " + operationId + ")");
                return true;
            } else {
                Log.e(TAG, "缓存数据失败: " + key + " (操作ID: " + operationId + ")");
                return false;
            }
            
        } catch (Exception e) {
            Log.e(TAG, "缓存数据时发生异常: " + key, e);
            return false;
        } finally {
            writeLock.unlock();
        }
    }
    
    /**
     * 原子性读取缓存数据
     */
    public <T> T getCachedDataAtomic(String key, Type type) {
        if (key == null || type == null) {
            return null;
        }
        
        readLock.lock();
        try {
            Log.d(TAG, "开始原子性读取缓存: " + key);
            
            // 检查缓存是否存在
            if (!cachePrefs.contains(key)) {
                Log.d(TAG, "缓存不存在: " + key);
                return null;
            }
            
            // 验证缓存版本
            int cacheVersion = cachePrefs.getInt(key + CACHE_VERSION_SUFFIX, -1);
            if (cacheVersion != CURRENT_CACHE_VERSION) {
                Log.w(TAG, "缓存版本不匹配，清除缓存: " + key + " (版本: " + cacheVersion + ")");
                // 异步清除过期版本的缓存
                new Thread(() -> clearCachedData(key)).start();
                return null;
            }
            
            // 读取数据
            String jsonData = cachePrefs.getString(key, null);
            if (jsonData == null) {
                Log.w(TAG, "缓存数据为空: " + key);
                return null;
            }
            
            // 验证数据完整性
            int expectedSize = cachePrefs.getInt(key + CACHE_SIZE_SUFFIX, -1);
            int actualSize = jsonData.length();
            if (expectedSize != -1 && expectedSize != actualSize) {
                Log.e(TAG, "缓存数据大小不匹配，可能已损坏: " + key + 
                      " (期望: " + expectedSize + ", 实际: " + actualSize + ")");
                // 异步清除损坏的缓存
                new Thread(() -> clearCachedData(key)).start();
                return null;
            }
            
            // 反序列化数据
            T data = gson.fromJson(jsonData, type);
            
            long timestamp = cachePrefs.getLong(key + CACHE_TIMESTAMP_SUFFIX, 0);
            Log.d(TAG, "成功读取缓存数据: " + key + " (大小: " + actualSize + " 字符, 时间戳: " + timestamp + ")");
            
            return data;
            
        } catch (Exception e) {
            Log.e(TAG, "读取缓存数据时发生异常: " + key, e);
            // 异步清除可能损坏的缓存
            new Thread(() -> clearCachedData(key)).start();
            return null;
        } finally {
            readLock.unlock();
        }
    }
    
    /**
     * 清除缓存数据
     */
    public void clearCachedData(String key) {
        if (key == null) {
            return;
        }
        
        writeLock.lock();
        try {
            clearCachedDataInternal(key);
            Log.d(TAG, "缓存数据已清除: " + key);
        } finally {
            writeLock.unlock();
        }
    }
    
    /**
     * 内部清除缓存数据方法（不加锁）
     */
    private void clearCachedDataInternal(String key) {
        SharedPreferences.Editor editor = cachePrefs.edit();
        editor.remove(key);
        editor.remove(key + CACHE_VERSION_SUFFIX);
        editor.remove(key + CACHE_TIMESTAMP_SUFFIX);
        editor.remove(key + CACHE_SIZE_SUFFIX);
        editor.commit(); // 使用commit确保立即执行
    }
    
    /**
     * 检查缓存是否有效
     */
    public boolean isCacheValid(String key) {
        if (key == null) {
            return false;
        }
        
        readLock.lock();
        try {
            if (!cachePrefs.contains(key)) {
                return false;
            }
            
            int cacheVersion = cachePrefs.getInt(key + CACHE_VERSION_SUFFIX, -1);
            if (cacheVersion != CURRENT_CACHE_VERSION) {
                return false;
            }
            
            String jsonData = cachePrefs.getString(key, null);
            if (jsonData == null) {
                return false;
            }
            
            int expectedSize = cachePrefs.getInt(key + CACHE_SIZE_SUFFIX, -1);
            int actualSize = jsonData.length();
            
            return expectedSize == actualSize;
            
        } catch (Exception e) {
            Log.e(TAG, "检查缓存有效性时发生异常: " + key, e);
            return false;
        } finally {
            readLock.unlock();
        }
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        writeLock.lock();
        try {
            SharedPreferences.Editor editor = cachePrefs.edit();
            editor.clear();
            editor.commit();
            operationCounters.clear();
            Log.d(TAG, "所有缓存数据已清除");
        } finally {
            writeLock.unlock();
        }
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        readLock.lock();
        try {
            int totalEntries = 0;
            long totalSize = 0;

            for (String key : cachePrefs.getAll().keySet()) {
                if (!key.endsWith(CACHE_VERSION_SUFFIX) &&
                    !key.endsWith(CACHE_TIMESTAMP_SUFFIX) &&
                    !key.endsWith(CACHE_SIZE_SUFFIX)) {
                    totalEntries++;
                    int size = cachePrefs.getInt(key + CACHE_SIZE_SUFFIX, 0);
                    totalSize += size;
                }
            }

            return new CacheStats(totalEntries, totalSize);

        } finally {
            readLock.unlock();
        }
    }

    /**
     * 获取所有缓存数据的键值对
     */
    public Map<String, ?> getAllCacheData() {
        readLock.lock();
        try {
            return cachePrefs.getAll();
        } finally {
            readLock.unlock();
        }
    }

    /**
     * 获取Gson实例（用于外部计算数据大小）
     */
    public Gson getGson() {
        return gson;
    }

    /**
     * 清理指定命名空间的所有缓存
     *
     * @param namespace 命名空间前缀，例如 "home_featured" 或 "mylist"
     */
    public void clearCacheNamespace(String namespace) {
        if (namespace == null || namespace.isEmpty()) {
            Log.w(TAG, "Cannot clear cache with null or empty namespace");
            return;
        }

        writeLock.lock();
        try {
            Log.d(TAG, "开始清理命名空间缓存: " + namespace);

            Set<String> keysToRemove = new HashSet<>();
            String namespacePrefix = namespace + "_";

            // 收集所有匹配的缓存键（包括辅助键）
            for (String key : cachePrefs.getAll().keySet()) {
                if (key.startsWith(namespacePrefix)) {
                    keysToRemove.add(key);
                }
            }

            if (keysToRemove.isEmpty()) {
                Log.d(TAG, "命名空间 " + namespace + " 下没有找到缓存数据");
                return;
            }

            // 原子性删除所有匹配的键
            SharedPreferences.Editor editor = cachePrefs.edit();
            for (String key : keysToRemove) {
                editor.remove(key);
            }
            boolean success = editor.commit();

            if (success) {
                Log.d(TAG, "成功清理命名空间 " + namespace + " 下的 " + keysToRemove.size() + " 个缓存项");
            } else {
                Log.e(TAG, "清理命名空间 " + namespace + " 失败");
            }

        } catch (Exception e) {
            Log.e(TAG, "清理命名空间缓存时发生异常: " + namespace, e);
        } finally {
            writeLock.unlock();
        }
    }

    /**
     * 获取指定命名空间的所有缓存键
     *
     * @param namespace 命名空间前缀，例如 "home_featured" 或 "mylist"
     * @return 匹配的缓存键列表（不包括辅助键如_timestamp、_version、_size）
     */
    public List<String> getCacheKeysByNamespace(String namespace) {
        if (namespace == null || namespace.isEmpty()) {
            Log.w(TAG, "Cannot get cache keys with null or empty namespace");
            return new ArrayList<>();
        }

        readLock.lock();
        try {
            Log.d(TAG, "获取命名空间缓存键: " + namespace);

            List<String> keys = new ArrayList<>();
            String namespacePrefix = namespace + "_";

            for (String key : cachePrefs.getAll().keySet()) {
                if (key.startsWith(namespacePrefix) &&
                    !key.endsWith(CACHE_TIMESTAMP_SUFFIX) &&
                    !key.endsWith(CACHE_VERSION_SUFFIX) &&
                    !key.endsWith(CACHE_SIZE_SUFFIX)) {
                    keys.add(key);
                }
            }

            Log.d(TAG, "命名空间 " + namespace + " 下找到 " + keys.size() + " 个缓存键");
            return keys;

        } catch (Exception e) {
            Log.e(TAG, "获取命名空间缓存键时发生异常: " + namespace, e);
            return new ArrayList<>();
        } finally {
            readLock.unlock();
        }
    }

    /**
     * 检查指定命名空间是否有有效缓存
     *
     * @param namespace 命名空间前缀
     * @return 如果命名空间下有至少一个有效缓存则返回true
     */
    public boolean hasValidCacheInNamespace(String namespace) {
        if (namespace == null || namespace.isEmpty()) {
            return false;
        }

        List<String> keys = getCacheKeysByNamespace(namespace);
        for (String key : keys) {
            if (isCacheValid(key)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        public final int diskCacheSize;
        public final long totalDataSize;
        public final int memoryCacheSize = 0; // 这个实现没有内存缓存
        
        CacheStats(int diskCacheSize, long totalDataSize) {
            this.diskCacheSize = diskCacheSize;
            this.totalDataSize = totalDataSize;
        }
        
        @Override
        public String toString() {
            return "CacheStats{" +
                    "diskCacheSize=" + diskCacheSize +
                    ", totalDataSize=" + totalDataSize + " chars" +
                    '}';
        }
    }
}
