package com.android.video.model.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.android.video.constants.ApiConstantsUtils;
import java.util.List;
import java.util.ArrayList;

/**
 * 标签响应数据模型
 * <AUTHOR> Team
 */
public class LabelsResponseModel {

    /**
     * 标签列表
     */
    @SerializedName("labels")
    @Expose
    private List<LabelItem> labels;

    /**
     * 默认构造函数
     */
    public LabelsResponseModel() {
        this.labels = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param labels 标签列表
     */
    public LabelsResponseModel(List<LabelItem> labels) {
        this.labels = labels != null ? new ArrayList<>(labels) : new ArrayList<>();
    }

    // ========== Getter和Setter方法 ==========

    public List<LabelItem> getLabels() {
        return labels;
    }

    public void setLabels(List<LabelItem> labels) {
        this.labels = labels != null ? new ArrayList<>(labels) : new ArrayList<>();
    }

    // ========== 便利方法 ==========

    /**
     * 获取标签数量
     * @return 标签数量
     */
    public int getLabelCount() {
        return labels != null ? labels.size() : 0;
    }

    /**
     * 检查是否有标签
     * @return 是否有标签
     */
    public boolean hasLabels() {
        return labels != null && !labels.isEmpty();
    }

    /**
     * 根据ID查找标签
     * @param labelId 标签ID
     * @return 标签项，如果未找到返回null
     */
    public LabelItem findLabelById(String labelId) {
        if (labels != null && labelId != null) {
            for (LabelItem label : labels) {
                if (labelId.equals(label.getLabelId())) {
                    return label;
                }
            }
        }
        return null;
    }

    /**
     * 根据名称查找标签
     * @param labelName 标签名称
     * @return 标签项，如果未找到返回null
     */
    public LabelItem findLabelByName(String labelName) {
        if (labels != null && labelName != null) {
            for (LabelItem label : labels) {
                if (labelName.equals(label.getLabelName())) {
                    return label;
                }
            }
        }
        return null;
    }

    /**
     * 添加标签
     * @param label 标签项
     */
    public void addLabel(LabelItem label) {
        if (label != null) {
            if (labels == null) {
                labels = new ArrayList<>();
            }
            labels.add(label);
        }
    }

    /**
     * 移除标签
     * @param labelId 标签ID
     * @return 是否成功移除
     */
    public boolean removeLabel(String labelId) {
        if (labels != null && labelId != null) {
            return labels.removeIf(label -> labelId.equals(label.getLabelId()));
        }
        return false;
    }

    /**
     * 清空所有标签
     */
    public void clearLabels() {
        if (labels != null) {
            labels.clear();
        }
    }

    @Override
    public String toString() {
        return "LabelsResponseModel{" +
                "labels=" + labels +
                ", count=" + getLabelCount() +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        LabelsResponseModel that = (LabelsResponseModel) obj;
        
        return labels != null ? labels.equals(that.labels) : that.labels == null;
    }

    @Override
    public int hashCode() {
        return labels != null ? labels.hashCode() : 0;
    }

    /**
     * 标签项内部类
     */
    public static class LabelItem {

        /**
         * 标签ID
         */
        @SerializedName(ApiConstantsUtils.FIELD_LABEL_ID)
        @Expose
        private String labelId;

        /**
         * 标签名称
         */
        @SerializedName(ApiConstantsUtils.FIELD_LABEL_NAME)
        @Expose
        private String labelName;

        /**
         * 默认构造函数
         */
        public LabelItem() {
        }

        /**
         * 构造函数
         * @param labelId 标签ID
         * @param labelName 标签名称
         */
        public LabelItem(String labelId, String labelName) {
            this.labelId = labelId;
            this.labelName = labelName;
        }

        // ========== Getter和Setter方法 ==========

        public String getLabelId() {
            return labelId;
        }

        public void setLabelId(String labelId) {
            this.labelId = labelId;
        }

        public String getLabelName() {
            return labelName;
        }

        public void setLabelName(String labelName) {
            this.labelName = labelName;
        }

        // ========== 便利方法 ==========

        /**
         * 检查标签是否有效
         * @return 是否有效
         */
        public boolean isValid() {
            return labelId != null && !labelId.trim().isEmpty() &&
                   labelName != null && !labelName.trim().isEmpty();
        }

        @Override
        public String toString() {
            return "LabelItem{" +
                    "labelId='" + labelId + '\'' +
                    ", labelName='" + labelName + '\'' +
                    '}';
        }

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            
            LabelItem labelItem = (LabelItem) obj;
            
            if (labelId != null ? !labelId.equals(labelItem.labelId) : labelItem.labelId != null) return false;
            return labelName != null ? labelName.equals(labelItem.labelName) : labelItem.labelName == null;
        }

        @Override
        public int hashCode() {
            int result = labelId != null ? labelId.hashCode() : 0;
            result = 31 * result + (labelName != null ? labelName.hashCode() : 0);
            return result;
        }
    }
}
