VideoPlayer 静态认证系统说明文档

⚠️ 重要提示：此静态认证系统已弃用！
现在所有环境（包括开发环境）都使用后端API进行认证。
请参考 doc/LOGIN_API_MIGRATION_REPORT.md 了解新的登录接口。

=== 模拟测试功能（已弃用） ===

PhoneLoginActivity模拟测试登录：
- 使用18407151430手机号登录会不发送验证码和验证码页面直接进入主页
- 其他手机号正常走验证码流程

静态UserModel字段说明：
- username: 用户名，Super Admin为"Super Admin"，普通用户为"User+UID后4位"
- uid: 用户唯一标识，Super Admin为"18407151"，普通用户为手机号后8位
- phoneNumber: 用户手机号，存储清理后的纯数字格式
- userVip: VIP状态，0为普通用户，1为VIP用户，Super Admin默认为VIP
- email: 邮箱地址，Super Admin为"<EMAIL>"，普通用户为"<EMAIL>"
- avatar: 头像路径，当前为空字符串
- points: 用户积分，Super Admin为9999，普通用户为100
- isLoggedIn: 登录状态，认证成功后为true

持久化存储逻辑：
- 使用SharedPreferences存储用户会话信息
- 存储文件名为"VideoPlayerUserSession"
- 包含所有用户字段的键值对存储
- 应用启动时检查登录状态，已登录用户直接进入主页
- 用户点击退出登录时清除所有持久化信息

手机号格式处理：
- 支持多种输入格式：纯数字、带国家代码、带格式化符号
- 自动清理空格、括号、横线等格式化字符
- 移除国家代码前缀（+86、+1、+7等）
- Super Admin识别基于清理后的纯数字"18407151430"

认证工具类：
- UserAuthUtils: 静态认证逻辑，根据手机号创建对应用户信息
- UserSessionUtils: 会话管理，处理用户信息的存储和读取
- AuthTestUtils: 测试工具类，用于验证认证逻辑正确性

UI状态管理：
- ProfileFragment根据用户登录状态和VIP状态显示不同UI
- VIP用户显示充值按钮，隐藏Go VIP按钮
- 普通用户显示Go VIP按钮，隐藏充值按钮
- 未登录用户点击Login跳转到登录选择页面
- 已登录用户点击Login变为Logout功能

测试用例：
- 输入"18407151430"应识别为Super Admin
- 输入"+86 184 0715 1430"应识别为Super Admin
- 输入其他手机号应创建普通用户
- 登录后重启应用应直接进入主页
- 退出登录后重启应用应显示登录页面
