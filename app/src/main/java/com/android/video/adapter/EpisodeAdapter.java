package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.model.EpisodeModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 剧集选择RecyclerView适配器
 * 支持单选模式，显示集数和直播图标，实现横向滚动选择
 * <AUTHOR>
 */
public class EpisodeAdapter extends RecyclerView.Adapter<EpisodeAdapter.EpisodeViewHolder> {

    private List<EpisodeModel> episodeList;
    private OnEpisodeClickListener onEpisodeClickListener;
    private int selectedPosition = 0; // 当前选中位置，默认选中第一个

    /**
     * 剧集点击监听接口
     */
    public interface OnEpisodeClickListener {
        void onEpisodeClick(EpisodeModel episode, int position);
    }

    /**
     * 构造函数
     */
    public EpisodeAdapter() {
        this.episodeList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param episodeList 剧集列表
     */
    public EpisodeAdapter(List<EpisodeModel> episodeList) {
        this.episodeList = episodeList != null ? episodeList : new ArrayList<>();
    }

    /**
     * 设置剧集点击监听器
     * @param listener 点击监听器
     */
    public void setOnEpisodeClickListener(OnEpisodeClickListener listener) {
        this.onEpisodeClickListener = listener;
    }

    /**
     * 更新剧集列表
     * @param newEpisodeList 新的剧集列表
     */
    public void updateEpisodeList(List<EpisodeModel> newEpisodeList) {
        this.episodeList = newEpisodeList != null ? newEpisodeList : new ArrayList<>();

        // 查找当前选中的剧集位置（基于剧集数据中的选中状态）
        selectedPosition = -1;
        for (int i = 0; i < episodeList.size(); i++) {
            if (episodeList.get(i).isSelected()) {
                selectedPosition = i;
                break;
            }
        }

        // 如果没有找到选中的剧集，默认选中第一个
        if (selectedPosition == -1 && !episodeList.isEmpty()) {
            selectedPosition = 0;
            episodeList.get(0).setSelected(true);
        }

        notifyDataSetChanged();
    }

    /**
     * 设置选中位置（单选模式）
     * @param position 要选中的位置
     */
    public void setSelectedPosition(int position) {
        if (position >= 0 && position < episodeList.size()) {
            int oldPosition = selectedPosition;
            selectedPosition = position;

            // 更新剧集数据中的选中状态
            for (int i = 0; i < episodeList.size(); i++) {
                episodeList.get(i).setSelected(i == position);
            }

            // 只刷新变化的项目
            if (oldPosition != position) {
                if (oldPosition >= 0 && oldPosition < episodeList.size()) {
                    notifyItemChanged(oldPosition);
                }
                notifyItemChanged(position);
            }
        }
    }

    /**
     * 获取当前选中位置
     * @return 选中位置
     */
    public int getSelectedPosition() {
        return selectedPosition;
    }

    /**
     * 获取选中的剧集
     * @return 选中的剧集
     */
    public EpisodeModel getSelectedEpisode() {
        if (selectedPosition >= 0 && selectedPosition < episodeList.size()) {
            return episodeList.get(selectedPosition);
        }
        return null;
    }



    @NonNull
    @Override
    public EpisodeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_episode, parent, false);
        return new EpisodeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EpisodeViewHolder holder, int position) {
        EpisodeModel episode = episodeList.get(position);
        holder.bind(episode, position);
    }

    @Override
    public int getItemCount() {
        return episodeList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class EpisodeViewHolder extends RecyclerView.ViewHolder {

        private TextView tvEpisodeNumber;
        private ImageView ivLiveIcon;
        private ImageView ivLockIcon;

        public EpisodeViewHolder(@NonNull View itemView) {
            super(itemView);
            tvEpisodeNumber = itemView.findViewById(R.id.tv_episode_number);
            ivLiveIcon = itemView.findViewById(R.id.iv_live_icon);
            ivLockIcon = itemView.findViewById(R.id.iv_lock_icon);
        }

        /**
         * 绑定数据
         * @param episode 剧集数据
         * @param position 位置
         */
        public void bind(EpisodeModel episode, int position) {
            if (episode == null) {
                return;
            }

            // 设置剧集编号
            tvEpisodeNumber.setText(String.valueOf(episode.getEpisodeNumber()));

            // 检查章节是否可观看（付费章节必须解锁后才能观看）
            boolean isAvailable = !episode.isCharge() || episode.isUnlock();

            // 调试日志
            android.util.Log.d("EpisodeAdapter", "Episode " + episode.getEpisodeNumber() +
                ": isCharge=" + episode.isCharge() +
                ", isUnlock=" + episode.isUnlock() +
                ", isAvailable=" + isAvailable);

            if (!isAvailable) {
                // 未解锁状态：显示锁定图标，隐藏直播图标
                if (ivLockIcon != null) {
                    ivLockIcon.setVisibility(View.VISIBLE);
                }
                ivLiveIcon.setVisibility(View.GONE);

                // 设置未解锁的背景和文本颜色
                FrameLayout llEpisodeContent = itemView.findViewById(R.id.ll_episode_content);
                llEpisodeContent.setBackgroundResource(R.drawable.episode_unavailable_bg);
                tvEpisodeNumber.setTextColor(0xFF999999); // 灰色文本表示不可用
            } else {
                // 已解锁状态：隐藏锁定图标，根据选中状态显示直播图标
                if (ivLockIcon != null) {
                    ivLockIcon.setVisibility(View.GONE);
                }

                // 设置直播图标显示状态：只有在选中时才显示图标
                if (episode.isSelected() && episode.isLive()) {
                    ivLiveIcon.setVisibility(View.VISIBLE);
                    ivLiveIcon.setImageResource(R.drawable.movie_ic_zhibo);
                } else {
                    ivLiveIcon.setVisibility(View.GONE);
                }

                // 设置选中状态（设置到有背景选择器的FrameLayout上）
                FrameLayout llEpisodeContent = itemView.findViewById(R.id.ll_episode_content);
                llEpisodeContent.setBackgroundResource(R.drawable.detail_episode_selector);
                llEpisodeContent.setSelected(episode.isSelected());

                // 设置文本颜色
                if (episode.isSelected()) {
                    tvEpisodeNumber.setTextColor(0xFF000000); // 选中时黑色文本
                } else {
                    tvEpisodeNumber.setTextColor(0xFFFFFFFF); // 未选中时白色文本
                }
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onEpisodeClickListener != null) {
                    if (episode.isCharge() && !episode.isUnlock()) {
                        // 付费章节且未解锁：显示解锁提示或跳转到VIP页面
                        // 这里可以添加VIP解锁弹窗逻辑
                        android.widget.Toast.makeText(v.getContext(), "该集数需要付费解锁后观看", android.widget.Toast.LENGTH_SHORT).show();
                    } else if (position != selectedPosition) {
                        // 免费章节或付费已解锁章节：正常选择逻辑
                        setSelectedPosition(position);
                        onEpisodeClickListener.onEpisodeClick(episode, position);
                    }
                }
            });
        }
    }
}
