<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 视频播放器配置 -->
    
    <!-- 支持的视频格式 -->
    <string-array name="supported_video_formats">
        <item>mp4</item>
        <item>avi</item>
        <item>mkv</item>
        <item>mov</item>
        <item>wmv</item>
        <item>flv</item>
        <item>webm</item>
    </string-array>
    
    <!-- 支持的视频分辨率 -->
    <string-array name="supported_video_resolutions">
        <item>480P</item>
        <item>720P</item>
        <item>1080P</item>
        <item>2K</item>
        <item>4K</item>
        <item>超清</item>
    </string-array>
    
    <!-- 视频分类 -->
    <string-array name="video_categories">
        <item>电影</item>
        <item>电视剧</item>
        <item>短片</item>
        <item>纪录片</item>
        <item>动画</item>
        <item>音乐视频</item>
        <item>测试视频</item>
    </string-array>
    
    <!-- 播放速度选项 -->
    <string-array name="playback_speeds">
        <item>0.5x</item>
        <item>0.75x</item>
        <item>1.0x</item>
        <item>1.25x</item>
        <item>1.5x</item>
        <item>2.0x</item>
    </string-array>
    
    <!-- 播放速度值 -->
    <array name="playback_speed_values">
        <item>0.5</item>
        <item>0.75</item>
        <item>1.0</item>
        <item>1.25</item>
        <item>1.5</item>
        <item>2.0</item>
    </array>
    
    <!-- 视频质量选项 -->
    <string-array name="video_quality_options">
        <item>自动</item>
        <item>720P</item>
        <item>1080P</item>
        <item>超清</item>
    </string-array>
    
    <!-- 测试视频标签 -->
    <string-array name="test_video_tags">
        <item>测试</item>
        <item>演示</item>
        <item>样例</item>
        <item>高清</item>
        <item>短片</item>
        <item>电影</item>
        <item>片段</item>
        <item>预告</item>
    </string-array>
    
    <!-- 视频播放器设置 -->
    <string name="default_video_resolution">720P</string>
    <string name="default_video_format">mp4</string>
    <string name="default_playback_speed">1.0x</string>
    <string name="default_video_category">测试视频</string>
    
    <!-- 文件大小限制 -->
    <integer name="max_video_file_size_mb">100</integer>
    <integer name="min_video_duration_seconds">1</integer>
    <integer name="max_video_duration_seconds">7200</integer> <!-- 2小时 -->
    
    <!-- 缓存设置 -->
    <integer name="video_cache_size_mb">50</integer>
    <integer name="thumbnail_cache_size_mb">10</integer>
    
    <!-- 播放器控制设置 -->
    <integer name="control_hide_timeout_ms">3000</integer>
    <integer name="seek_increment_ms">10000</integer> <!-- 10秒 -->
    <integer name="fast_seek_increment_ms">30000</integer> <!-- 30秒 -->
    
    <!-- 错误重试设置 -->
    <integer name="max_retry_attempts">3</integer>
    <integer name="retry_delay_ms">1000</integer>
    
    <!-- 视频预加载设置 -->
    <bool name="enable_video_preload">true</bool>
    <bool name="enable_thumbnail_generation">true</bool>
    <bool name="enable_auto_play">false</bool>
    <bool name="enable_loop_playback">false</bool>
    
    <!-- 网络设置 -->
    <bool name="allow_mobile_data_streaming">true</bool>
    <bool name="enable_adaptive_bitrate">true</bool>
    
    <!-- 用户界面设置 -->
    <bool name="show_playback_speed_control">true</bool>
    <bool name="show_quality_selector">true</bool>
    <bool name="show_subtitle_control">true</bool>
    <bool name="show_episode_selector">true</bool>
    
    <!-- 测试视频默认信息 -->
    <string name="test_video_default_title">测试视频</string>
    <string name="test_video_default_description">这是一个用于测试播放器功能的视频文件</string>
    <string name="test_video_default_category">测试视频</string>
    <string name="test_video_default_resolution">720P</string>
    <string name="test_video_default_format">mp4</string>
    
    <!-- 错误消息 -->
    <string name="error_video_not_found">视频文件未找到</string>
    <string name="error_video_format_not_supported">不支持的视频格式</string>
    <string name="error_video_file_too_large">视频文件过大</string>
    <string name="error_video_duration_invalid">视频时长无效</string>
    <string name="error_video_resource_invalid">视频资源无效</string>
    <string name="error_video_playback_failed">视频播放失败</string>
    <string name="error_video_loading_failed">视频加载失败</string>
    
    <!-- 成功消息 -->
    <string name="success_video_loaded">视频加载成功</string>
    <string name="success_video_validated">视频验证成功</string>
    <string name="success_video_added">视频添加成功</string>
    <string name="success_video_removed">视频移除成功</string>
    
    <!-- 提示消息 -->
    <string name="hint_video_loading">正在加载视频...</string>
    <string name="hint_video_buffering">正在缓冲...</string>
    <string name="hint_video_preparing">正在准备播放...</string>
    <string name="hint_video_seeking">正在跳转...</string>
    
    <!-- 视频信息格式化 -->
    <string name="format_video_duration">时长: %s</string>
    <string name="format_video_size">大小: %s</string>
    <string name="format_video_resolution">分辨率: %s</string>
    <string name="format_video_format">格式: %s</string>
    <string name="format_video_rating">评分: %.1f</string>
    <string name="format_video_views">播放: %s次</string>
    
    <!-- 播放控制文本 -->
    <string name="control_play">播放</string>
    <string name="control_pause">暂停</string>
    <string name="control_stop">停止</string>
    <string name="control_next">下一个</string>
    <string name="control_previous">上一个</string>
    <string name="control_fast_forward">快进</string>
    <string name="control_rewind">快退</string>
    <string name="control_fullscreen">全屏</string>
    <string name="control_exit_fullscreen">退出全屏</string>
    <string name="control_volume">音量</string>
    <string name="control_brightness">亮度</string>
    <string name="control_speed">播放速度</string>
    <string name="control_quality">画质</string>
    <string name="control_subtitle">字幕</string>
    <string name="control_episode">选集</string>
    
</resources>
