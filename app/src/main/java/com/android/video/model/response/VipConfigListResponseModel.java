package com.android.video.model.response;

import com.android.video.model.VipConfigModel;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

/**
 * VIP配置列表API响应模型
 * <p>
 * 用于解析VIP配置列表API的响应数据，包含响应码、消息和VIP配置数据列表。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VipConfigListResponseModel {

    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;

    /**
     * VIP配置数据列表
     */
    @SerializedName("data")
    private List<VipConfigModel> data;

    /**
     * 默认构造函数
     */
    public VipConfigListResponseModel() {
        this.data = new ArrayList<>();
    }

    /**
     * 完整构造函数
     *
     * @param code    响应码
     * @param message 响应消息
     * @param data    VIP配置数据列表
     */
    public VipConfigListResponseModel(String code, String message, List<VipConfigModel> data) {
        this.code = code;
        this.message = message;
        this.data = data != null ? data : new ArrayList<>();
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<VipConfigModel> getData() {
        return data;
    }

    public void setData(List<VipConfigModel> data) {
        this.data = data != null ? data : new ArrayList<>();
    }

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取有效的VIP配置列表（状态为1的配置）
     *
     * @return 有效的VIP配置列表
     */
    public List<VipConfigModel> getValidConfigs() {
        List<VipConfigModel> validConfigs = new ArrayList<>();
        if (data != null) {
            for (VipConfigModel config : data) {
                if (config.getStatus() == 1) {
                    validConfigs.add(config);
                }
            }
        }
        return validConfigs;
    }

    /**
     * 获取VIP配置数量
     *
     * @return VIP配置数量
     */
    public int getConfigCount() {
        return data != null ? data.size() : 0;
    }

    /**
     * 获取有效VIP配置数量
     *
     * @return 有效VIP配置数量
     */
    public int getValidConfigCount() {
        return getValidConfigs().size();
    }

    /**
     * 检查是否有VIP配置数据
     *
     * @return 如果有VIP配置数据则返回true，否则返回false
     */
    public boolean hasData() {
        return data != null && !data.isEmpty();
    }

    /**
     * 检查是否有有效的VIP配置数据
     *
     * @return 如果有有效的VIP配置数据则返回true，否则返回false
     */
    public boolean hasValidData() {
        return !getValidConfigs().isEmpty();
    }

    /**
     * 获取默认选中的VIP配置（第一个有效配置）
     *
     * @return 默认选中的VIP配置，如果没有有效配置则返回null
     */
    public VipConfigModel getDefaultSelectedConfig() {
        List<VipConfigModel> validConfigs = getValidConfigs();
        return validConfigs.isEmpty() ? null : validConfigs.get(0);
    }

    @Override
    public String toString() {
        return "VipConfigListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + (data != null ? data.size() + " configs" : "null") +
                '}';
    }
}
