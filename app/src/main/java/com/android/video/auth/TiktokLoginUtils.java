package com.android.video.auth;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.content.pm.PackageManager;
import android.util.Log;
import java.util.List;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.LoginResponseModel;
import android.net.Uri;
import android.webkit.WebView;
import android.webkit.WebViewClient;
import android.app.AlertDialog;
import android.content.DialogInterface;

/**
 * TikTok登录工具类 - 使用WebView实现TikTok登录流程
 * 注意：由于TikTok官方SDK不可用，使用WebView方式实现
 * <AUTHOR> Team
 */
public class TiktokLoginUtils {

    private static final String TAG = "TiktokLoginUtils";
    private static final String CLIENT_ID = "dev_tiktok_client_id_2024"; // 开发环境临时ID
    private static final String REDIRECT_URI = "videoplayer://tiktok.callback"; // Android Deep Link URI
    private static final String TIKTOK_AUTH_URL = "https://www.tiktok.com/auth/authorize/";
    private static boolean isInitialized = false;
    private static TiktokLoginCallback currentCallback;
    private static AlertDialog loginDialog;
    private static Activity currentActivity; // 保存当前Activity引用

    /**
     * TikTok登录回调接口
     */
    public interface TiktokLoginCallback {
        void onSuccess(LoginResponseModel loginResponse);
        void onError(String errorMessage);
        void onCancel();
    }

    /**
     * 初始化TikTok登录工具
     * @param activity 当前Activity
     */
    public static void initialize(Activity activity) {
        if (isInitialized) {
            Log.d(TAG, "TikTok login utils already initialized");
            return;
        }

        try {
            // 检查配置
            if (CLIENT_ID.equals("your_tiktok_client_id")) {
                Log.w(TAG, "TikTok Client ID not configured properly");
            }

            isInitialized = true;
            Log.d(TAG, "TikTok login utils initialized successfully");

        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize TikTok login utils: " + e.getMessage());
        }
    }

    /**
     * 开始TikTok登录
     * @param activity 当前Activity
     * @param callback 登录回调
     */
    public static void startLogin(Activity activity, TiktokLoginCallback callback) {
        Log.d(TAG, "Starting TikTok login via WebView");

        if (!isInitialized) {
            initialize(activity);
        }

        currentActivity = activity; // 保存Activity引用
        currentCallback = callback;

        try {
            // 检查配置
            if (CLIENT_ID.equals("your_tiktok_client_id")) {
                if (callback != null) {
                    callback.onError("TikTok Client ID not configured. Please configure CLIENT_ID in TiktokLoginUtils.");
                }
                return;
            }

            // 开发环境：使用模拟登录
            if (CLIENT_ID.equals("dev_tiktok_client_id_2024")) {
                Log.d(TAG, "Using development mode - simulating TikTok login");
                simulateSuccessfulTikTokLogin();
                return;
            }

            // 构建授权URL
            String authUrl = buildAuthUrl();
            Log.d(TAG, "Opening TikTok auth URL: " + authUrl);

            // 创建WebView对话框
            showWebViewDialog(activity, authUrl);

        } catch (Exception e) {
            Log.e(TAG, "TikTok login error: " + e.getMessage());
            if (callback != null) {
                callback.onError("TikTok login failed: " + e.getMessage());
            }
        }
    }

    /**
     * 构建TikTok授权URL
     * @return 授权URL
     */
    private static String buildAuthUrl() {
        Uri.Builder builder = Uri.parse(TIKTOK_AUTH_URL).buildUpon();
        builder.appendQueryParameter("client_key", CLIENT_ID);
        builder.appendQueryParameter("response_type", "code");
        builder.appendQueryParameter("scope", "user.info.basic");
        builder.appendQueryParameter("redirect_uri", REDIRECT_URI);
        builder.appendQueryParameter("state", "tiktok_login_" + System.currentTimeMillis());
        return builder.build().toString();
    }

    /**
     * 显示WebView登录对话框
     * @param activity 当前Activity
     * @param authUrl 授权URL
     */
    private static void showWebViewDialog(Activity activity, String authUrl) {
        WebView webView = new WebView(activity);
        webView.getSettings().setJavaScriptEnabled(true);
        webView.getSettings().setDomStorageEnabled(true);

        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, String url) {
                Log.d(TAG, "WebView loading URL: " + url);

                // 检查是否是重定向URL
                if (url.startsWith(REDIRECT_URI)) {
                    handleRedirectUrl(url);
                    if (loginDialog != null) {
                        loginDialog.dismiss();
                    }
                    return true;
                }

                return false;
            }

            @Override
            public void onPageFinished(WebView view, String url) {
                super.onPageFinished(view, url);
                Log.d(TAG, "WebView page finished loading: " + url);
            }
        });

        // 创建对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(activity);
        builder.setTitle("TikTok Login");
        builder.setView(webView);
        builder.setNegativeButton("Cancel", new DialogInterface.OnClickListener() {
            @Override
            public void onClick(DialogInterface dialog, int which) {
                if (currentCallback != null) {
                    currentCallback.onCancel();
                    currentCallback = null;
                }
            }
        });

        loginDialog = builder.create();
        loginDialog.show();

        // 加载授权URL
        webView.loadUrl(authUrl);
    }

    /**
     * 处理重定向URL，提取授权码
     * @param redirectUrl 重定向URL
     */
    private static void handleRedirectUrl(String redirectUrl) {
        try {
            Uri uri = Uri.parse(redirectUrl);
            String code = uri.getQueryParameter("code");
            String error = uri.getQueryParameter("error");

            if (error != null) {
                Log.e(TAG, "TikTok authorization error: " + error);
                if (currentCallback != null) {
                    currentCallback.onError("TikTok authorization failed: " + error);
                    currentCallback = null;
                }
            } else if (code != null) {
                Log.d(TAG, "Got TikTok auth code: " + code);
                handleAuthSuccess(code);
            } else {
                Log.e(TAG, "No auth code or error in redirect URL");
                if (currentCallback != null) {
                    currentCallback.onError("Invalid TikTok authorization response");
                    currentCallback = null;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error parsing redirect URL: " + e.getMessage());
            if (currentCallback != null) {
                currentCallback.onError("Failed to parse TikTok authorization response");
                currentCallback = null;
            }
        }
    }

    /**
     * 处理授权成功
     * @param authCode 授权码
     */
    private static void handleAuthSuccess(String authCode) {
        if (authCode != null && !authCode.isEmpty()) {
            Log.d(TAG, "Got TikTok auth code, calling backend API");
            // 注意：这里需要Context，但WebView回调中无法直接获取
            // 在实际使用中，可以考虑将Context保存为静态变量或使用Application Context
            callBackendApi(authCode);
        } else {
            Log.e(TAG, "TikTok auth code is null or empty");
            if (currentCallback != null) {
                currentCallback.onError("Failed to get TikTok authorization code");
                currentCallback = null;
            }
        }
    }

    /**
     * 调用后端API完成登录
     * @param authCode 授权码
     */
    private static void callBackendApi(String authCode) {
        Log.d(TAG, "Calling backend API with TikTok auth code");

        if (currentActivity == null) {
            Log.e(TAG, "Current activity is null, cannot call backend API");
            if (currentCallback != null) {
                currentCallback.onError("Internal error: Activity reference lost");
                currentCallback = null;
            }
            return;
        }

        AuthApiUtils.tiktokLogin(currentActivity, authCode, new AuthApiUtils.ApiCallback<LoginResponseModel>() {
            @Override
            public void onSuccess(LoginResponseModel loginResponse) {
                Log.d(TAG, "Backend TikTok login successful");
                if (currentCallback != null) {
                    currentCallback.onSuccess(loginResponse);
                    currentCallback = null;
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Backend TikTok login failed: " + errorMessage);
                if (currentCallback != null) {
                    currentCallback.onError(errorMessage);
                    currentCallback = null;
                }
            }
        });
    }

    /**
     * 处理Activity结果（如果需要）
     * @param requestCode 请求码
     * @param resultCode 结果码
     * @param data Intent数据
     */
    public static void onActivityResult(int requestCode, int resultCode, Intent data) {
        // TikTok SDK通常不需要处理onActivityResult
        // 但保留此方法以备将来使用
        Log.d(TAG, "onActivityResult called: requestCode=" + requestCode + ", resultCode=" + resultCode);
    }

    /**
     * TikTok应用的可能包名
     * 不同地区使用不同的包名
     */
    private static final String[] TIKTOK_PACKAGE_NAMES = {
        "com.zhiliaoapp.musically",  // 美国等地区
        "com.ss.android.ugc.trill"   // 东南亚等地区
    };

    /**
     * 检查TikTok应用是否已安装（Android 11+兼容版本）
     * @param activity 当前Activity
     * @return 是否已安装TikTok应用
     */
    public static boolean isTikTokAppInstalled(Activity activity) {
        PackageManager packageManager = activity.getPackageManager();

        Log.d(TAG, "开始检测TikTok应用安装状态（Android 11+兼容模式）...");
        Log.d(TAG, "Android版本: " + android.os.Build.VERSION.SDK_INT);

        // 方法1: 检查所有可能的TikTok包名（这是最可靠的方法）
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            try {
                packageManager.getPackageInfo(packageName, 0);
                Log.i(TAG, "✅ TikTok app found with package name: " + packageName);
                return true;
            } catch (PackageManager.NameNotFoundException e) {
                Log.d(TAG, "❌ TikTok package not found: " + packageName);
            }
        }

        // 方法2: 检查Intent是否可以解析（检测TikTok的启动Intent）
        Log.d(TAG, "尝试通过Intent检测TikTok...");
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            Intent launchIntent = packageManager.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                Log.i(TAG, "✅ TikTok launch intent found for package: " + packageName);
                return true;
            }
        }

        // 方法3: 尝试创建特定的Intent来检测TikTok
        Log.d(TAG, "尝试通过特定Intent检测TikTok...");
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            try {
                Intent intent = new Intent(Intent.ACTION_MAIN);
                intent.setPackage(packageName);
                intent.addCategory(Intent.CATEGORY_LAUNCHER);

                if (packageManager.queryIntentActivities(intent, 0).size() > 0) {
                    Log.i(TAG, "✅ TikTok found via Intent query: " + packageName);
                    return true;
                }
            } catch (Exception e) {
                Log.d(TAG, "Intent query failed for " + packageName + ": " + e.getMessage());
            }
        }

        // 方法4: 仅在Android 11以下或有QUERY_ALL_PACKAGES权限时扫描所有应用
        if (android.os.Build.VERSION.SDK_INT < 30) {
            Log.d(TAG, "Android版本低于11，尝试扫描所有已安装应用...");
            try {
                List<ApplicationInfo> installedApps = packageManager.getInstalledApplications(0);
                Log.d(TAG, "可以访问 " + installedApps.size() + " 个应用");

                for (ApplicationInfo appInfo : installedApps) {
                    String packageName = appInfo.packageName;
                    if (packageName.contains("tiktok") ||
                        packageName.contains("musically") ||
                        packageName.contains("trill") ||
                        packageName.contains("zhiliaoapp") ||
                        packageName.contains("ss.android.ugc")) {
                        Log.i(TAG, "✅ Found TikTok-related app: " + packageName);
                        return true;
                    }
                }
            } catch (Exception e) {
                Log.e(TAG, "Error scanning installed apps: " + e.getMessage());
            }
        } else {
            Log.d(TAG, "Android 11+，跳过全应用扫描（需要QUERY_ALL_PACKAGES权限）");
        }

        Log.w(TAG, "❌ TikTok app not found using any detection method");
        return false;
    }

    /**
     * 获取已安装的TikTok应用包名（增强版检测）
     * @param activity 当前Activity
     * @return TikTok包名，如果未安装则返回null
     */
    public static String getInstalledTikTokPackageName(Activity activity) {
        PackageManager packageManager = activity.getPackageManager();

        // 方法1: 检查已知的TikTok包名
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            try {
                packageManager.getPackageInfo(packageName, 0);
                Log.d(TAG, "Found TikTok with known package name: " + packageName);
                return packageName;
            } catch (PackageManager.NameNotFoundException e) {
                // 继续检查下一个包名
            }
        }

        // 方法2: 扫描所有应用查找TikTok相关包名
        try {
            List<ApplicationInfo> installedApps = packageManager.getInstalledApplications(0);
            for (ApplicationInfo appInfo : installedApps) {
                String packageName = appInfo.packageName;
                if (packageName.contains("tiktok") ||
                    packageName.contains("musically") ||
                    packageName.contains("trill") ||
                    packageName.contains("zhiliaoapp") ||
                    packageName.contains("ss.android.ugc")) {
                    Log.d(TAG, "Found TikTok-related app by scanning: " + packageName);
                    return packageName;
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error scanning for TikTok package name: " + e.getMessage());
        }

        return null;
    }

    /**
     * 获取TikTok登录状态信息（用于调试）
     * @param activity 当前Activity
     * @return 状态信息字符串
     */
    public static String getLoginStatusInfo(Activity activity) {
        StringBuilder info = new StringBuilder();
        info.append("TikTok Login Status (WebView Implementation):\n");
        info.append("Utils Initialized: ").append(isInitialized).append("\n");
        info.append("TikTok App Installed: ").append(isTikTokAppInstalled(activity)).append("\n");
        info.append("Client ID: ").append(CLIENT_ID.equals("your_tiktok_client_id") ? "Not Configured" : "Configured").append("\n");
        info.append("Redirect URI: ").append(REDIRECT_URI).append(" (Android Deep Link)").append("\n");
        info.append("Implementation: WebView-based (Official SDK unavailable)");

        return info.toString();
    }

    /**
     * 取消当前登录流程
     */
    public static void cancelLogin() {
        if (loginDialog != null && loginDialog.isShowing()) {
            loginDialog.dismiss();
        }
        if (currentCallback != null) {
            currentCallback.onCancel();
            currentCallback = null;
        }
        currentActivity = null; // 清理Activity引用
        Log.d(TAG, "TikTok login cancelled");
    }

    /**
     * 清理回调引用（防止内存泄漏）
     */
    public static void clearCallback() {
        currentCallback = null;
        currentActivity = null; // 清理Activity引用
        if (loginDialog != null && loginDialog.isShowing()) {
            loginDialog.dismiss();
        }
        Log.d(TAG, "TikTok callback cleared");
    }

    /**
     * 调试方法：列出所有可能的TikTok相关应用（Android 11+兼容）
     * @param activity 当前Activity
     */
    public static void debugListTikTokRelatedApps(Activity activity) {
        Log.d(TAG, "=== 调试：TikTok应用检测详情 ===");
        Log.d(TAG, "Android版本: " + android.os.Build.VERSION.SDK_INT);

        PackageManager packageManager = activity.getPackageManager();

        // 首先检查已知的TikTok包名
        Log.d(TAG, "检查已知TikTok包名:");
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            try {
                packageManager.getPackageInfo(packageName, 0);
                Log.d(TAG, "✅ 找到: " + packageName);
            } catch (PackageManager.NameNotFoundException e) {
                Log.d(TAG, "❌ 未找到: " + packageName);
            }
        }

        // 检查Intent启动能力
        Log.d(TAG, "检查Intent启动能力:");
        for (String packageName : TIKTOK_PACKAGE_NAMES) {
            Intent launchIntent = packageManager.getLaunchIntentForPackage(packageName);
            if (launchIntent != null) {
                Log.d(TAG, "✅ 可启动: " + packageName);
            } else {
                Log.d(TAG, "❌ 无法启动: " + packageName);
            }
        }

        // 仅在Android 11以下时扫描所有应用
        if (android.os.Build.VERSION.SDK_INT < 30) {
            Log.d(TAG, "扫描所有已安装应用（Android < 11）:");
            try {
                List<ApplicationInfo> installedApps = packageManager.getInstalledApplications(0);
                Log.d(TAG, "总共可访问 " + installedApps.size() + " 个应用");

                int count = 0;
                for (ApplicationInfo appInfo : installedApps) {
                    String packageName = appInfo.packageName;
                    String appName = appInfo.loadLabel(packageManager).toString();

                    // 检查包名或应用名是否包含TikTok相关关键词
                    if (packageName.toLowerCase().contains("tik") ||
                        packageName.toLowerCase().contains("musically") ||
                        packageName.toLowerCase().contains("trill") ||
                        packageName.toLowerCase().contains("zhiliao") ||
                        packageName.toLowerCase().contains("ss.android") ||
                        appName.toLowerCase().contains("tik") ||
                        appName.toLowerCase().contains("musically")) {

                        Log.d(TAG, String.format("发现相关应用 #%d: %s (%s)", ++count, appName, packageName));
                    }
                }

                if (count == 0) {
                    Log.d(TAG, "扫描结果：未找到任何TikTok相关应用");
                } else {
                    Log.d(TAG, String.format("扫描结果：总共找到 %d 个TikTok相关应用", count));
                }

            } catch (Exception e) {
                Log.e(TAG, "调试扫描应用时出错: " + e.getMessage());
            }
        } else {
            Log.d(TAG, "Android 11+：无法扫描所有应用（需要QUERY_ALL_PACKAGES权限或queries声明）");
            Log.d(TAG, "当前manifest中已声明的TikTok包名应该可以被检测到");
        }

        Log.d(TAG, "=== 调试结束 ===");
    }

    /**
     * 模拟成功的TikTok登录（开发环境使用）
     */
    private static void simulateSuccessfulTikTokLogin() {
        Log.d(TAG, "Simulating successful TikTok login");

        // 延迟1秒模拟网络请求
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            if (currentCallback != null) {
                // 创建模拟的登录响应
                com.android.video.model.response.LoginResponseModel loginResponse =
                    new com.android.video.model.response.LoginResponseModel();

                loginResponse.setCustomerId("tiktok_user_" + System.currentTimeMillis());
                loginResponse.setCustomerName("TikTok User");
                loginResponse.setUserToken("tiktok_token_" + System.currentTimeMillis());
                loginResponse.setLoginType(3); // TikTok登录类型
                loginResponse.setDeviceId("tiktok_device_" + android.os.Build.MODEL);

                Log.d(TAG, "TikTok simulation login successful: " + loginResponse.toString());
                currentCallback.onSuccess(loginResponse);
                currentCallback = null;
            }
        }, 1000);
    }
}
