<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context=".ui.activity.LoginActivity">

    <!-- Layout Guidelines for responsive design -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.9" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_logo_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_buttons_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.95" />

    <!-- Background Image -->
    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/login_bg"
        android:contentDescription="Login Background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/iv_back_button"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:src="@drawable/login_btn_back"
        android:contentDescription="@string/back_button"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Logo with responsive positioning -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="@dimen/login_logo_width"
        android:layout_height="@dimen/login_logo_height"
        android:contentDescription="@string/app_logo"
        android:scaleType="centerInside"
        android:src="@drawable/logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.6" />
    <!-- Login Buttons Chain -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_phone_login"
        android:layout_width="@dimen/login_button_width"
        android:layout_height="@dimen/login_button_height"
        android:background="@drawable/button_phone_selector"
        android:elevation="@dimen/login_button_elevation"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintBottom_toTopOf="@+id/btn_facebook_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_chainStyle="packed"
        app:layout_constraintVertical_bias="0.0">

        <!-- Icon positioned at the left -->
        <ImageView
            android:id="@+id/iv_phone_icon"
            android:layout_width="@dimen/login_icon_size"
            android:layout_height="@dimen/login_icon_size"
            android:layout_marginStart="@dimen/layout_margin_medium"
            android:src="@drawable/login_ic_phone"
            android:contentDescription="Phone Icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Text positioned at the center -->
        <TextView
            android:id="@+id/tv_phone_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/log_in_phone_number"
            android:textColor="@color/login_button_text_black"
            android:textSize="@dimen/login_text_size"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Facebook Login Button - Chain member -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_facebook_login"
        android:layout_width="@dimen/login_button_width"
        android:layout_height="@dimen/login_button_height"
        android:layout_marginTop="@dimen/login_button_margin_bottom"
        android:background="@drawable/button_facebook_selector"
        android:elevation="@dimen/login_button_elevation"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toBottomOf="@+id/btn_phone_login"
        app:layout_constraintBottom_toTopOf="@+id/btn_tiktok_login"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Icon positioned at the left -->
        <ImageView
            android:id="@+id/iv_facebook_icon"
            android:layout_width="@dimen/login_icon_size"
            android:layout_height="@dimen/login_icon_size"
            android:layout_marginStart="@dimen/layout_margin_medium"
            android:src="@drawable/login_ic_facebook"
            android:contentDescription="Facebook Icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Text positioned at the center -->
        <TextView
            android:id="@+id/tv_facebook_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/log_in_with_facebook"
            android:textColor="@color/login_text_white"
            android:textSize="@dimen/login_text_size"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- TikTok Login Button - Chain end -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/btn_tiktok_login"
        android:layout_width="@dimen/login_button_width"
        android:layout_height="@dimen/login_button_height"
        android:layout_marginTop="@dimen/login_button_margin_bottom"
        android:background="@drawable/button_phone_selector"
        android:elevation="@dimen/login_button_elevation"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toBottomOf="@+id/btn_facebook_login"
        app:layout_constraintBottom_toTopOf="@+id/guideline_buttons_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Icon positioned at the left -->
        <ImageView
            android:id="@+id/iv_tiktok_icon"
            android:layout_width="@dimen/login_icon_size"
            android:layout_height="@dimen/login_icon_size"
            android:layout_marginStart="@dimen/layout_margin_medium"
            android:src="@drawable/login_ic_tiktok"
            android:contentDescription="TikTok Icon"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Text positioned at the center -->
        <TextView
            android:id="@+id/tv_tiktok_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/log_in_tiktok"
            android:textColor="@color/login_button_text_black"
            android:textSize="@dimen/login_text_size"
            android:textStyle="bold"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- Bottom Text Section with proper constraints -->
    <TextView
        android:id="@+id/tv_privacy_notice"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/login_privacy_text_margin_horizontal"
        android:layout_marginEnd="@dimen/login_privacy_text_margin_horizontal"
        android:fontFamily="sans-serif"
        android:gravity="start|top"
        android:letterSpacing="0"
        android:lineSpacingMultiplier="1.43"
        android:text="@string/privacy_notice"
        android:textColor="@color/login_privacy_text"
        android:textSize="@dimen/login_privacy_text_size"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="@+id/guideline_buttons_bottom"
        app:layout_constraintBottom_toTopOf="@+id/tv_user_agreement"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_chainStyle="packed" />

    <TextView
        android:id="@+id/tv_user_agreement"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/login_agreement_text_margin_horizontal"
        android:layout_marginEnd="@dimen/login_agreement_text_margin_horizontal"
        android:layout_marginTop="@dimen/login_agreement_text_margin_top"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0"
        android:lineSpacingMultiplier="1.43"
        android:text="@string/user_agreement"
        android:textColor="@color/login_agreement_text"
        android:textSize="@dimen/login_agreement_text_size"
        android:textStyle="normal"
        app:layout_constraintTop_toBottomOf="@+id/tv_privacy_notice"
        app:layout_constraintBottom_toTopOf="@+id/guideline_bottom_safe"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end" />

</androidx.constraintlayout.widget.ConstraintLayout>
