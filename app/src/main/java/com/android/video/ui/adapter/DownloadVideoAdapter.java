package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.DownloadVideo;

import java.util.List;

/**
 * 下载视频适配器
 * <AUTHOR>
 */
public class DownloadVideoAdapter extends RecyclerView.Adapter<DownloadVideoAdapter.DownloadVideoViewHolder> {

    private List<DownloadVideo> downloadVideos;
    private OnDownloadVideoClickListener listener;

    public interface OnDownloadVideoClickListener {
        void onPlayClick(DownloadVideo video);
        void onDeleteClick(DownloadVideo video);
        void onVideoClick(DownloadVideo video);
    }

    public DownloadVideoAdapter(List<DownloadVideo> downloadVideos, OnDownloadVideoClickListener listener) {
        this.downloadVideos = downloadVideos;
        this.listener = listener;
    }

    @NonNull
    @Override
    public DownloadVideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_download_video, parent, false);
        return new DownloadVideoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DownloadVideoViewHolder holder, int position) {
        DownloadVideo video = downloadVideos.get(position);
        holder.bind(video);
    }

    @Override
    public int getItemCount() {
        return downloadVideos != null ? downloadVideos.size() : 0;
    }

    public void updateVideos(List<DownloadVideo> newVideos) {
        this.downloadVideos = newVideos;
        notifyDataSetChanged();
    }

    public void removeVideo(int position) {
        if (position >= 0 && position < downloadVideos.size()) {
            downloadVideos.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, downloadVideos.size());
        }
    }

    public class DownloadVideoViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private ImageView ivDelete;
        private TextView tvTitle;
        private TextView tvDownloadedStatus;
        private TextView tvDownloadedCount;
        private TextView tvTotalCount;
        private LinearLayout btnPlay;
        private LinearLayout llEpisodeProgress;

        public DownloadVideoViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            ivDelete = itemView.findViewById(R.id.iv_delete);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvDownloadedStatus = itemView.findViewById(R.id.tv_downloaded_status);
            tvDownloadedCount = itemView.findViewById(R.id.tv_downloaded_count);
            tvTotalCount = itemView.findViewById(R.id.tv_total_count);
            btnPlay = itemView.findViewById(R.id.btn_play);
            llEpisodeProgress = itemView.findViewById(R.id.ll_episode_progress);
        }

        public void bind(DownloadVideo video) {
            // 设置标题
            tvTitle.setText(video.getTitle());

            // 设置下载状态文本
            tvDownloadedStatus.setText("Downloaded/Total episodes");

            // 设置下载进度（EP.格式）
            tvDownloadedCount.setText("EP." + video.getDownloadedEpisodes());
            tvTotalCount.setText("EP." + video.getTotalEpisodes());

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onVideoClick(video);
                }
            });

            ivDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteClick(video);
                }
            });

            btnPlay.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPlayClick(video);
                }
            });

            // 集数进度容器点击事件（与play按钮相同）
            llEpisodeProgress.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPlayClick(video);
                }
            });

            // 根据下载状态调整UI
            if (video.isDownloading()) {
                // 正在下载状态
                tvDownloadedStatus.setText("Downloading... " + video.getDownloadProgress() + "%");
                btnPlay.setEnabled(false);
                btnPlay.setAlpha(0.5f);
            } else if (video.isFullyDownloaded()) {
                // 已完全下载
                tvDownloadedStatus.setText("Downloaded/Total episodes");
                btnPlay.setEnabled(true);
                btnPlay.setAlpha(1.0f);
            } else {
                // 部分下载
                tvDownloadedStatus.setText("Partially downloaded");
                btnPlay.setEnabled(true);
                btnPlay.setAlpha(1.0f);
            }
        }
    }
}
