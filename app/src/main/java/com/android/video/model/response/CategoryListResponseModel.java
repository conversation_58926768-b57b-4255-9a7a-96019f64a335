package com.android.video.model.response;

import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.HomeApiConstantsUtils;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 分类短剧列表响应数据模型
 * <p>
 * 用于解析分类短剧列表API的完整响应结构，包含分页信息。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_CATEGORIES}
 * </p>
 *
 * <p>
 * 响应结构说明：
 * <ul>
 *   <li>code: 响应状态码</li>
 *   <li>message: 响应消息</li>
 *   <li>data: 分页数据对象，包含records、total、size、current、pages</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * CategoryListResponseModel response = gson.fromJson(jsonString, CategoryListResponseModel.class);
 *
 * if (response.isSuccess()) {
 *     CategoryListDataModel data = response.getData();
 *     List&lt;CategoryWithFilmsModel&gt; categories = data.getRecords();
 *     int total = data.getTotal();
 *     int currentPage = data.getCurrent();
 *     // 处理分类数据
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_CATEGORIES
 * @see CategoryWithFilmsModel
 */
public class CategoryListResponseModel {

    /**
     * 响应状态码
     */
    @SerializedName(BaseApiConstantsUtils.FIELD_CODE)
    @Expose
    private String code;

    /**
     * 响应消息
     */
    @SerializedName(BaseApiConstantsUtils.FIELD_MESSAGE)
    @Expose
    private String message;

    /**
     * 响应数据
     */
    @SerializedName(BaseApiConstantsUtils.FIELD_DATA)
    @Expose
    private CategoryListDataModel data;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public CategoryListResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应状态码
     * @param message 响应消息
     * @param data 响应数据
     */
    public CategoryListResponseModel(String code, String message, CategoryListDataModel data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public CategoryListDataModel getData() {
        return data;
    }

    public void setData(CategoryListDataModel data) {
        this.data = data;
    }

    // ========== 工具方法 ==========

    /**
     * 检查响应是否成功
     *
     * @return 如果响应成功则返回true，否则返回false
     */
    public boolean isSuccess() {
        return BaseApiConstantsUtils.isSuccessCode(code);
    }

    /**
     * 检查是否有数据
     *
     * @return 如果有数据则返回true，否则返回false
     */
    public boolean hasData() {
        return data != null && data.hasRecords();
    }

    /**
     * 获取分类列表
     *
     * @return 分类列表，如果没有数据则返回null
     */
    public List<CategoryWithFilmsModel> getCategories() {
        return hasData() ? data.getRecords() : null;
    }

    /**
     * 获取总记录数
     *
     * @return 总记录数
     */
    public int getTotal() {
        return data != null ? data.getTotal() : 0;
    }

    /**
     * 获取当前页码
     *
     * @return 当前页码
     */
    public int getCurrentPage() {
        return data != null ? data.getCurrent() : 1;
    }

    /**
     * 获取总页数
     *
     * @return 总页数
     */
    public int getTotalPages() {
        return data != null ? data.getPages() : 1;
    }

    /**
     * 检查是否还有下一页
     *
     * @return 如果还有下一页则返回true，否则返回false
     */
    public boolean hasNextPage() {
        return data != null && data.hasNextPage();
    }

    @Override
    public String toString() {
        return "CategoryListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 分类列表数据模型（内部类）
     * <p>
     * 包含分页信息的数据结构。
     * </p>
     */
    public static class CategoryListDataModel {

        /**
         * 分类记录列表
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_RECORDS)
        @Expose
        private List<CategoryWithFilmsModel> records;

        /**
         * 总记录数
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_TOTAL)
        @Expose
        private int total;

        /**
         * 每页数量
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_SIZE)
        @Expose
        private int size;

        /**
         * 当前页码
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_CURRENT)
        @Expose
        private int current;

        /**
         * 总页数
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_PAGES)
        @Expose
        private int pages;

        // ========== 构造函数 ==========

        public CategoryListDataModel() {
        }

        // ========== Getter和Setter方法 ==========

        public List<CategoryWithFilmsModel> getRecords() {
            return records;
        }

        public void setRecords(List<CategoryWithFilmsModel> records) {
            this.records = records;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public int getPages() {
            return pages;
        }

        public void setPages(int pages) {
            this.pages = pages;
        }

        // ========== 工具方法 ==========

        /**
         * 检查是否有记录
         *
         * @return 如果有记录则返回true，否则返回false
         */
        public boolean hasRecords() {
            return records != null && !records.isEmpty();
        }

        /**
         * 获取记录数量
         *
         * @return 记录数量
         */
        public int getRecordCount() {
            return records != null ? records.size() : 0;
        }

        /**
         * 检查是否还有下一页
         *
         * @return 如果还有下一页则返回true，否则返回false
         */
        public boolean hasNextPage() {
            return current < pages;
        }

        /**
         * 检查是否为第一页
         *
         * @return 如果是第一页则返回true，否则返回false
         */
        public boolean isFirstPage() {
            return current <= 1;
        }

        /**
         * 检查是否为最后一页
         *
         * @return 如果是最后一页则返回true，否则返回false
         */
        public boolean isLastPage() {
            return current >= pages;
        }

        @Override
        public String toString() {
            return "CategoryListDataModel{" +
                    "records=" + (records != null ? records.size() + " records" : "null") +
                    ", total=" + total +
                    ", size=" + size +
                    ", current=" + current +
                    ", pages=" + pages +
                    '}';
        }
    }
}
