package com.android.video.utils;

import android.content.ActivityNotFoundException;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.util.Log;

/**
 * 支付工具类
 * <p>
 * 提供通用的支付收银台调起方法，支持OneVisionPay等第三方支付平台。
 * 该工具类负责处理支付URL跳转、Intent启动、异常处理等通用支付逻辑，
 * 可被项目中任何需要支付功能的地方复用。
 * </p>
 * 
 * <p>
 * 主要功能：
 * <ul>
 *   <li>调起系统浏览器打开支付页面</li>
 *   <li>验证支付URL格式</li>
 *   <li>处理Intent启动异常</li>
 *   <li>提供详细的日志记录</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 调起支付页面
 * PaymentUtils.openPaymentPage(context, "https://checkout.onevisionpay.com/...", "ORDER123456789");
 * 
 * // 验证支付URL
 * boolean valid = PaymentUtils.isPaymentUrlValid("https://checkout.onevisionpay.com/...");
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class PaymentUtils {
    
    private static final String TAG = "PaymentUtils";
    
    /**
     * 调起支付页面
     * <p>
     * 使用系统默认浏览器打开支付页面URL，通常用于调起第三方支付收银台。
     * 该方法会自动处理Intent启动异常，确保应用不会因为无法打开浏览器而崩溃。
     * </p>
     * 
     * @param context 上下文，用于启动Intent
     * @param paymentPageUrl 支付页面URL，通常是第三方支付平台的收银台链接
     * @param orderNo 订单编号，用于日志记录和问题追踪
     * @return 如果成功启动支付页面则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean success = PaymentUtils.openPaymentPage(
     *     context, 
     *     "https://checkout.onevisionpay.com/pay?token=abc123", 
     *     "ORDER123456789"
     * );
     * if (success) {
     *     Log.d(TAG, "支付页面启动成功");
     * } else {
     *     Log.e(TAG, "支付页面启动失败");
     * }
     * </pre>
     */
    public static boolean openPaymentPage(Context context, String paymentPageUrl, String orderNo) {
        // 参数验证
        if (context == null) {
            Log.e(TAG, "调起支付页面失败: Context为空");
            return false;
        }
        
        if (paymentPageUrl == null || paymentPageUrl.trim().isEmpty()) {
            Log.e(TAG, "调起支付页面失败: 支付URL为空");
            return false;
        }
        
        // 验证URL格式
        if (!isPaymentUrlValid(paymentPageUrl)) {
            Log.e(TAG, "调起支付页面失败: 支付URL格式无效 - " + paymentPageUrl);
            return false;
        }
        
        try {
            Log.d(TAG, "=== 调起支付页面 ===");
            Log.d(TAG, "订单编号: " + (orderNo != null ? orderNo : "未提供"));
            Log.d(TAG, "支付URL: " + paymentPageUrl);
            Log.d(TAG, "==================");
            
            // 创建Intent
            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setData(Uri.parse(paymentPageUrl));
            
            // 添加标志，确保在新任务中启动
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            
            // 启动Intent
            context.startActivity(intent);
            
            Log.d(TAG, "支付页面启动成功 - 订单: " + orderNo);
            return true;
            
        } catch (ActivityNotFoundException e) {
            Log.e(TAG, "调起支付页面失败: 未找到可处理该URL的应用", e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "调起支付页面失败: 未知异常", e);
            return false;
        }
    }
    
    /**
     * 验证支付URL是否有效
     * <p>
     * 检查支付URL的格式是否正确，包括协议、域名等基本验证。
     * 该方法主要用于在调起支付页面前进行预检查。
     * </p>
     * 
     * @param paymentPageUrl 支付页面URL
     * @return 如果URL格式有效则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean valid = PaymentUtils.isPaymentUrlValid("https://checkout.onevisionpay.com/pay");  // true
     * boolean valid = PaymentUtils.isPaymentUrlValid("http://example.com");                     // true
     * boolean valid = PaymentUtils.isPaymentUrlValid("invalid-url");                            // false
     * boolean valid = PaymentUtils.isPaymentUrlValid("");                                       // false
     * boolean valid = PaymentUtils.isPaymentUrlValid(null);                                     // false
     * </pre>
     */
    public static boolean isPaymentUrlValid(String paymentPageUrl) {
        if (paymentPageUrl == null || paymentPageUrl.trim().isEmpty()) {
            return false;
        }
        
        String url = paymentPageUrl.trim();
        
        try {
            Uri uri = Uri.parse(url);
            String scheme = uri.getScheme();
            String host = uri.getHost();
            
            // 检查协议是否为http或https
            if (!"http".equalsIgnoreCase(scheme) && !"https".equalsIgnoreCase(scheme)) {
                Log.d(TAG, "URL协议无效: " + scheme);
                return false;
            }
            
            // 检查是否有有效的主机名
            if (host == null || host.trim().isEmpty()) {
                Log.d(TAG, "URL主机名无效: " + host);
                return false;
            }
            
            Log.d(TAG, "URL验证通过: " + url);
            return true;
            
        } catch (Exception e) {
            Log.d(TAG, "URL解析失败: " + url, e);
            return false;
        }
    }
    
    /**
     * 获取支付URL的域名
     * <p>
     * 从支付URL中提取域名部分，用于日志记录或安全检查。
     * </p>
     * 
     * @param paymentPageUrl 支付页面URL
     * @return 域名字符串，如果无法提取则返回null
     * 
     * @example
     * <pre>
     * String host = PaymentUtils.getPaymentUrlHost("https://checkout.onevisionpay.com/pay");  // "checkout.onevisionpay.com"
     * String host = PaymentUtils.getPaymentUrlHost("invalid-url");                            // null
     * </pre>
     */
    public static String getPaymentUrlHost(String paymentPageUrl) {
        if (!isPaymentUrlValid(paymentPageUrl)) {
            return null;
        }
        
        try {
            Uri uri = Uri.parse(paymentPageUrl.trim());
            return uri.getHost();
        } catch (Exception e) {
            Log.d(TAG, "提取URL主机名失败: " + paymentPageUrl, e);
            return null;
        }
    }
    
    /**
     * 检查设备是否支持打开URL
     * <p>
     * 检查设备上是否有可以处理指定URL的应用程序。
     * </p>
     * 
     * @param context 上下文
     * @param url 要检查的URL
     * @return 如果设备支持打开该URL则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean supported = PaymentUtils.canOpenUrl(context, "https://example.com");
     * if (!supported) {
     *     // 提示用户安装浏览器或其他应用
     * }
     * </pre>
     */
    public static boolean canOpenUrl(Context context, String url) {
        if (context == null || !isPaymentUrlValid(url)) {
            return false;
        }
        
        try {
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(url));
            return intent.resolveActivity(context.getPackageManager()) != null;
        } catch (Exception e) {
            Log.d(TAG, "检查URL支持性失败: " + url, e);
            return false;
        }
    }
}
