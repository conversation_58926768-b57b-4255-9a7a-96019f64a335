<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/subtitle_panel_height"
    android:background="@drawable/subtitle_panel_bg">

    <!-- Close Button -->
    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="@dimen/subtitle_panel_close_button_size"
        android:layout_height="@dimen/subtitle_panel_close_button_size"
        android:layout_marginTop="@dimen/subtitle_panel_close_button_margin_top"
        android:layout_marginEnd="@dimen/subtitle_panel_close_button_margin_end"
        android:src="@drawable/ic_close"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/subtitle_panel_title_margin_top"
        android:text="Subtitles"
        android:textColor="@color/subtitle_panel_text_primary"
        android:textSize="@dimen/subtitle_panel_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Subtitle Text -->
    <TextView
        android:id="@+id/tv_subtitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/subtitle_panel_subtitle_margin_top"
        android:text="Do you want to enable subtitles"
        android:textColor="@color/subtitle_panel_text_secondary"
        android:textSize="@dimen/subtitle_panel_subtitle_text_size"
        android:fontFamily="sans-serif"
        android:textStyle="normal"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Switch Button -->
    <ImageView
        android:id="@+id/btn_switch"
        android:layout_width="@dimen/subtitle_panel_switch_size"
        android:layout_height="@dimen/subtitle_panel_switch_size"
        android:layout_marginTop="@dimen/subtitle_panel_switch_margin_top"
        android:src="@drawable/play_kai"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/subtitle_switch"
        app:layout_constraintTop_toBottomOf="@id/tv_subtitle"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- English Language Option -->
    <LinearLayout
        android:id="@+id/language_english"
        android:layout_width="@dimen/subtitle_panel_language_width"
        android:layout_height="@dimen/subtitle_panel_language_height"
        android:layout_marginTop="@dimen/subtitle_panel_language_margin_top"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/subtitle_language_unselected_bg"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toBottomOf="@id/btn_switch"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_english"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="English"
            android:textColor="@color/subtitle_panel_text_primary"
            android:textSize="@dimen/subtitle_panel_language_text_size"
            android:fontFamily="sans-serif"
            android:textStyle="normal" />

    </LinearLayout>

    <!-- Russian Language Option -->
    <LinearLayout
        android:id="@+id/language_russian"
        android:layout_width="@dimen/subtitle_panel_language_width"
        android:layout_height="@dimen/subtitle_panel_language_height"
        android:layout_marginTop="@dimen/subtitle_panel_language_item_margin_top"
        android:orientation="horizontal"
        android:gravity="center"
        android:background="@drawable/subtitle_language_unselected_bg"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toBottomOf="@id/language_english"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <TextView
            android:id="@+id/tv_russian"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="руская"
            android:textColor="@color/subtitle_panel_text_primary"
            android:textSize="@dimen/subtitle_panel_language_text_size"
            android:fontFamily="sans-serif"
            android:textStyle="normal" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
