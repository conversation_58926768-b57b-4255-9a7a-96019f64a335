package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.model.Episode;

import java.util.List;

/**
 * 剧集适配器
 * <AUTHOR>
 */
public class EpisodeAdapter extends RecyclerView.Adapter<EpisodeAdapter.EpisodeViewHolder> {

    private List<Episode> episodes;
    private OnEpisodeClickListener listener;

    public interface OnEpisodeClickListener {
        void onPlayClick(Episode episode);
        void onDeleteClick(Episode episode);
        void onEpisodeClick(Episode episode);
    }

    public EpisodeAdapter(List<Episode> episodes, OnEpisodeClickListener listener) {
        this.episodes = episodes;
        this.listener = listener;
    }

    @NonNull
    @Override
    public EpisodeViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_downloaded_episode, parent, false);
        return new EpisodeViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull EpisodeViewHolder holder, int position) {
        Episode episode = episodes.get(position);
        holder.bind(episode);
    }

    @Override
    public int getItemCount() {
        return episodes != null ? episodes.size() : 0;
    }

    public void updateEpisodes(List<Episode> newEpisodes) {
        this.episodes = newEpisodes;
        notifyDataSetChanged();
    }

    public void removeEpisode(int position) {
        if (position >= 0 && position < episodes.size()) {
            episodes.remove(position);
            notifyItemRemoved(position);
            notifyItemRangeChanged(position, episodes.size());
        }
    }

    public class EpisodeViewHolder extends RecyclerView.ViewHolder {
        private TextView tvEpisodeNumber;
        private TextView tvFileSize;
        private ImageView ivPlay;
        private ImageView ivDelete;

        public EpisodeViewHolder(@NonNull View itemView) {
            super(itemView);
            tvEpisodeNumber = itemView.findViewById(R.id.tv_episode_number);
            tvFileSize = itemView.findViewById(R.id.tv_file_size);
            ivPlay = itemView.findViewById(R.id.iv_play);
            ivDelete = itemView.findViewById(R.id.iv_delete);
        }

        public void bind(Episode episode) {
            // 设置剧集编号
            tvEpisodeNumber.setText(episode.getEpisodeNumber());

            // 设置文件大小或下载进度
            tvFileSize.setText(episode.getDisplayText());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onEpisodeClick(episode);
                }
            });

            ivPlay.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPlayClick(episode);
                }
            });

            ivDelete.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onDeleteClick(episode);
                }
            });

            // 根据下载状态调整UI
            if (episode.isDownloaded()) {
                ivPlay.setEnabled(true);
                ivPlay.setAlpha(1.0f);
                tvEpisodeNumber.setAlpha(1.0f);
                tvFileSize.setAlpha(1.0f);
            } else {
                ivPlay.setEnabled(false);
                ivPlay.setAlpha(0.5f);
                tvEpisodeNumber.setAlpha(0.5f);
                tvFileSize.setAlpha(0.5f);
            }
        }
    }
}
