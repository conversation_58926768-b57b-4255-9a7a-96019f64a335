<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 主背景：rgba(255,255,255,0.12) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#1FFFFFFF" />
            <corners android:radius="10dp" />
        </shape>
    </item>
    
    <!-- 内阴影效果：使用stroke模拟内阴影 -->
    <item
        android:left="1.5dp"
        android:top="1.5dp"
        android:right="1.5dp"
        android:bottom="1.5dp">
        <shape android:shape="rectangle">
            <!-- 透明背景，保持主背景可见 -->
            <solid android:color="#00000000" />
            <!-- 内阴影边框：rgba(255,255,255,0.36) -->
            <stroke
                android:width="1dp"
                android:color="#5CFFFFFF" />
            <corners android:radius="8.5dp" />
        </shape>
    </item>

    
</layer-list>
