<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/episode_selection_popup_height"
    android:background="@drawable/episode_selection_popup_bg">

    <!-- Close Button -->
    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="@dimen/episode_selection_close_button_size"
        android:layout_height="@dimen/episode_selection_close_button_size"
        android:layout_marginTop="@dimen/episode_selection_close_button_margin_top"
        android:layout_marginEnd="@dimen/episode_selection_close_button_margin_end"
        android:src="@drawable/ic_close"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/close"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/episode_selection_title_margin_top"
        android:text="Episodes"
        android:textColor="@color/video_player_text_white"
        android:textSize="@dimen/episode_selection_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:textStyle="normal"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Episode Range Selection -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_episode_ranges"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="15dp"
        android:layout_marginTop="@dimen/episode_selection_range_margin_top"
        android:layout_marginEnd="15dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/tv_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Divider -->
    <View
        android:id="@+id/divider"
        android:layout_width="@dimen/episode_selection_divider_width"
        android:layout_height="@dimen/episode_selection_divider_height"
        android:layout_marginTop="0dp"
        android:background="@color/episode_selection_divider"
        app:layout_constraintTop_toBottomOf="@id/recycler_view_episode_ranges"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Episodes Grid -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/recycler_view_episodes"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="15dp"
        android:layout_marginTop="@dimen/episode_selection_episodes_margin_top"
        android:layout_marginEnd="15dp"
        android:layout_marginBottom="16dp"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/divider"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
