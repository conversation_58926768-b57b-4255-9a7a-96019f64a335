package com.android.video.utils;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.AsyncTask;
import android.util.Log;

import java.io.File;
import java.text.DecimalFormat;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 缓存管理工具类
 * 用于计算和清除应用缓存
 */
public class CacheManager {
    
    private static final String TAG = "CacheManager";
    private static ExecutorService executorService = Executors.newFixedThreadPool(2);
    
    /**
     * 缓存大小计算回调接口
     */
    public interface CacheSizeCallback {
        void onCacheSizeCalculated(String formattedSize, long sizeInBytes);
        void onError(String error);
    }
    
    /**
     * 缓存清除回调接口
     */
    public interface ClearCacheCallback {
        void onCacheCleared(boolean success, String message, String newCacheSize);
        void onError(String error);
    }
    
    /**
     * 异步计算缓存大小
     */
    public static void calculateCacheSize(Context context, CacheSizeCallback callback) {
        executorService.execute(() -> {
            try {
                long totalSize = 0;
                
                // 计算内部缓存目录大小
                File internalCacheDir = context.getCacheDir();
                if (internalCacheDir != null && internalCacheDir.exists()) {
                    totalSize += getDirectorySize(internalCacheDir);
                    Log.d(TAG, "Internal cache size: " + formatFileSize(getDirectorySize(internalCacheDir)));
                }
                
                // 计算外部缓存目录大小
                File externalCacheDir = context.getExternalCacheDir();
                if (externalCacheDir != null && externalCacheDir.exists()) {
                    totalSize += getDirectorySize(externalCacheDir);
                    Log.d(TAG, "External cache size: " + formatFileSize(getDirectorySize(externalCacheDir)));
                }
                
                // 计算数据库缓存大小
                File databaseDir = new File(context.getApplicationInfo().dataDir + "/databases");
                if (databaseDir.exists()) {
                    totalSize += getDirectorySize(databaseDir);
                    Log.d(TAG, "Database cache size: " + formatFileSize(getDirectorySize(databaseDir)));
                }
                
                // 计算SharedPreferences文件大小
                File sharedPrefsDir = new File(context.getApplicationInfo().dataDir + "/shared_prefs");
                if (sharedPrefsDir.exists()) {
                    totalSize += getDirectorySize(sharedPrefsDir);
                    Log.d(TAG, "SharedPrefs size: " + formatFileSize(getDirectorySize(sharedPrefsDir)));
                }
                
                final String formattedSize = formatFileSize(totalSize);
                final long finalTotalSize = totalSize;
                
                // 回调到主线程
                if (callback != null) {
                    callback.onCacheSizeCalculated(formattedSize, finalTotalSize);
                }
                
                Log.d(TAG, "Total cache size calculated: " + formattedSize);
                
            } catch (Exception e) {
                Log.e(TAG, "Error calculating cache size: " + e.getMessage());
                if (callback != null) {
                    callback.onError("Failed to calculate cache size: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 异步清除缓存
     */
    public static void clearCache(Context context, ClearCacheCallback callback) {
        clearCache(context, true, callback);
    }
    
    /**
     * 异步清除缓存
     * @param context 上下文
     * @param clearUserPreferences 是否清除用户偏好设置
     * @param callback 回调
     */
    public static void clearCache(Context context, boolean clearUserPreferences, ClearCacheCallback callback) {
        executorService.execute(() -> {
            try {
                final boolean[] success = {true};
                final StringBuilder message = new StringBuilder();

                // Clear internal cache directory
                File internalCacheDir = context.getCacheDir();
                if (internalCacheDir != null && internalCacheDir.exists()) {
                    if (deleteDirectoryContents(internalCacheDir)) {
                        message.append("Internal cache cleared\n");
                        Log.d(TAG, "Internal cache cleared");
                    } else {
                        success[0] = false;
                        message.append("Failed to clear internal cache\n");
                    }
                }

                // Clear external cache directory
                File externalCacheDir = context.getExternalCacheDir();
                if (externalCacheDir != null && externalCacheDir.exists()) {
                    if (deleteDirectoryContents(externalCacheDir)) {
                        message.append("External cache cleared\n");
                        Log.d(TAG, "External cache cleared");
                    } else {
                        success[0] = false;
                        message.append("Failed to clear external cache\n");
                    }
                }

                // Clear specific cache data
                clearSpecificCaches(context, clearUserPreferences);
                message.append("App data cache cleared\n");

                // 重新计算缓存大小
                calculateCacheSize(context, new CacheSizeCallback() {
                    @Override
                    public void onCacheSizeCalculated(String formattedSize, long sizeInBytes) {
                        if (callback != null) {
                            callback.onCacheCleared(success[0], message.toString().trim(), formattedSize);
                        }
                    }

                    @Override
                    public void onError(String error) {
                        if (callback != null) {
                            callback.onCacheCleared(success[0], message.toString().trim(), "0.0B");
                        }
                    }
                });

            } catch (Exception e) {
                Log.e(TAG, "Error clearing cache: " + e.getMessage());
                if (callback != null) {
                    callback.onError("Failed to clear cache: " + e.getMessage());
                }
            }
        });
    }
    
    /**
     * 清除特定的应用缓存数据
     */
    private static void clearSpecificCaches(Context context, boolean clearUserPreferences) {
        try {
            // 清除标签缓存
            SharedPreferences labelPrefs = context.getSharedPreferences("label_cache", Context.MODE_PRIVATE);
            labelPrefs.edit().clear().apply();
            
            // 清除搜索记录缓存
            SharedPreferences searchPrefs = context.getSharedPreferences("search_preferences", Context.MODE_PRIVATE);
            searchPrefs.edit().remove("recent_searches").apply();
            
            // 如果需要，清除用户偏好设置（谨慎操作）
            if (clearUserPreferences) {
                SharedPreferences videoPrefs = context.getSharedPreferences("video_player_preferences", Context.MODE_PRIVATE);
                // 只清除缓存相关的设置，保留用户的播放偏好
                SharedPreferences.Editor editor = videoPrefs.edit();
                // 这里可以选择性地清除某些设置
                // editor.remove("some_cache_key");
                editor.apply();
            }
            
            Log.d(TAG, "Specific caches cleared");
            
        } catch (Exception e) {
            Log.e(TAG, "Error clearing specific caches: " + e.getMessage());
        }
    }
    
    /**
     * 递归计算目录大小
     */
    private static long getDirectorySize(File directory) {
        long size = 0;
        try {
            if (directory == null || !directory.exists()) {
                return 0;
            }
            
            if (directory.isFile()) {
                return directory.length();
            }
            
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isFile()) {
                        size += file.length();
                    } else if (file.isDirectory()) {
                        size += getDirectorySize(file);
                    }
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error calculating directory size: " + e.getMessage());
        }
        return size;
    }
    
    /**
     * 删除目录内容（不删除目录本身）
     */
    private static boolean deleteDirectoryContents(File directory) {
        try {
            if (directory == null || !directory.exists() || !directory.isDirectory()) {
                return false;
            }
            
            File[] files = directory.listFiles();
            if (files != null) {
                for (File file : files) {
                    if (file.isDirectory()) {
                        deleteDirectory(file);
                    } else {
                        file.delete();
                    }
                }
            }
            return true;
        } catch (Exception e) {
            Log.e(TAG, "Error deleting directory contents: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 递归删除目录及其内容
     */
    private static boolean deleteDirectory(File directory) {
        try {
            if (directory == null || !directory.exists()) {
                return false;
            }
            
            if (directory.isDirectory()) {
                File[] files = directory.listFiles();
                if (files != null) {
                    for (File file : files) {
                        deleteDirectory(file);
                    }
                }
            }
            return directory.delete();
        } catch (Exception e) {
            Log.e(TAG, "Error deleting directory: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * 格式化文件大小显示
     */
    public static String formatFileSize(long size) {
        if (size <= 0) return "0.0B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        
        if (digitGroups >= units.length) {
            digitGroups = units.length - 1;
        }
        
        DecimalFormat decimalFormat = new DecimalFormat("#,##0.0");
        return decimalFormat.format(size / Math.pow(1024, digitGroups)) + units[digitGroups];
    }
}
