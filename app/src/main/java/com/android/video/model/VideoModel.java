package com.android.video.model;

import android.net.Uri;
import java.io.Serializable;
import java.util.List;
import java.util.ArrayList;

/**
 * 视频数据模型类
 * <AUTHOR>
 */
public class VideoModel implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private String title;
    private String posterUrl;
    private String category;
    private int totalEpisodes;
    private int currentEpisode;
    private boolean isSelected;
    private String description;
    private float rating;
    private String duration;
    private Uri videoUri;                // 视频URI
    private String filmLanguageInfoId;   // 短剧语言信息ID，用于调用详情接口

    // 新模块扩展字段
    private boolean isSubscribed;        // 订阅状态 (Coming Soon)
    private int matchPercentage;         // 合适度百分比 (Best For You)
    private int ranking;                 // 排名 (Today's Hot)
    private int hotness;                 // 热度值 (Today's Hot) 1-10
    private long viewCount;              // 播放次数 (Popular Series)
    private String releaseDate;          // 上映时间 (Coming Soon)

    // 视频详情页扩展字段
    private List<ActorModel> actors;     // 演员列表
    private List<DirectorModel> directors; // 导演列表
    private List<EpisodeModel> episodes; // 剧集列表
    private List<String> tags;           // 标签列表
    private boolean isLiked;             // 收藏状态
    private String synopsis;             // 概要内容
    private boolean isSynopsisExpanded;  // 概要是否展开
    private int selectedEpisodeRange;    // 选中的剧集区间 (0: 1-30, 1: 31-60, etc.)
    private int selectedEpisodeIndex;    // 选中的剧集索引

    /**
     * 默认构造函数
     */
    public VideoModel() {
        this.id = "";
        this.title = "";
        this.posterUrl = "";
        this.category = "";
        this.totalEpisodes = -1; // -1表示未设置，0表示API返回0集
        this.currentEpisode = 1;
        this.isSelected = false;
        this.description = "";
        this.rating = 0.0f;
        this.duration = "";
        this.videoUri = null;
        this.filmLanguageInfoId = "";
        this.isSubscribed = false;
        this.matchPercentage = 0;
        this.ranking = 0;
        this.hotness = 0;
        this.viewCount = 0;
        this.releaseDate = "";

        // 初始化详情页字段
        this.actors = new ArrayList<>();
        this.directors = new ArrayList<>();
        this.episodes = new ArrayList<>();
        this.tags = new ArrayList<>();
        this.isLiked = false;
        this.synopsis = "";
        this.isSynopsisExpanded = false;
        this.selectedEpisodeRange = 0;
        this.selectedEpisodeIndex = 0;
    }

    /**
     * 完整构造函数
     */
    public VideoModel(String id, String title, String posterUrl, String category, 
                     int totalEpisodes, int currentEpisode, String description, 
                     float rating, String duration) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.category = category;
        this.totalEpisodes = totalEpisodes;
        this.currentEpisode = currentEpisode;
        this.isSelected = false;
        this.description = description;
        this.rating = rating;
        this.duration = duration;
        this.isSubscribed = false;
        this.matchPercentage = 0;
        this.ranking = 0;
        this.hotness = 0;
        this.viewCount = 0;
        this.releaseDate = "";

        // 初始化详情页字段
        this.actors = new ArrayList<>();
        this.directors = new ArrayList<>();
        this.episodes = new ArrayList<>();
        this.tags = new ArrayList<>();
        this.isLiked = false;
        this.synopsis = "";
        this.isSynopsisExpanded = false;
        this.selectedEpisodeRange = 0;
        this.selectedEpisodeIndex = 0;
    }

    /**
     * 便利构造函数 - 基本信息
     */
    public VideoModel(String id, String title, String posterUrl, String category) {
        this.id = id;
        this.title = title;
        this.posterUrl = posterUrl;
        this.category = category;
        this.totalEpisodes = -1; // -1表示未设置，0表示API返回0集
        this.currentEpisode = 1;
        this.isSelected = false;
        this.description = "";
        this.rating = 0.0f;
        this.duration = "";
        this.isSubscribed = false;
        this.matchPercentage = 0;
        this.ranking = 0;
        this.hotness = 0;
        this.viewCount = 0;
        this.releaseDate = "";

        // 初始化详情页字段
        this.actors = new ArrayList<>();
        this.directors = new ArrayList<>();
        this.episodes = new ArrayList<>();
        this.tags = new ArrayList<>();
        this.isLiked = false;
        this.synopsis = "";
        this.isSynopsisExpanded = false;
        this.selectedEpisodeRange = 0;
        this.selectedEpisodeIndex = 0;
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getPosterUrl() {
        return posterUrl;
    }
    
    public void setPosterUrl(String posterUrl) {
        this.posterUrl = posterUrl;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public int getTotalEpisodes() {
        return totalEpisodes;
    }
    
    public void setTotalEpisodes(int totalEpisodes) {
        this.totalEpisodes = totalEpisodes;
    }
    
    public int getCurrentEpisode() {
        return currentEpisode;
    }
    
    public void setCurrentEpisode(int currentEpisode) {
        this.currentEpisode = currentEpisode;

        // 同时更新selectedEpisodeIndex，确保选中状态正确
        if (episodes != null && !episodes.isEmpty()) {
            for (int i = 0; i < episodes.size(); i++) {
                if (episodes.get(i).getEpisodeNumber() == currentEpisode) {
                    this.selectedEpisodeIndex = i;
                    break;
                }
            }
        }
    }
    
    public boolean isSelected() {
        return isSelected;
    }
    
    public void setSelected(boolean selected) {
        isSelected = selected;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public float getRating() {
        return rating;
    }
    
    public void setRating(float rating) {
        this.rating = rating;
    }
    
    public String getDuration() {
        return duration;
    }

    public void setDuration(String duration) {
        this.duration = duration;
    }

    public Uri getVideoUri() {
        return videoUri;
    }

    public void setVideoUri(Uri videoUri) {
        this.videoUri = videoUri;
    }

    // 新模块字段的Getter和Setter方法
    public boolean isSubscribed() {
        return isSubscribed;
    }

    public void setSubscribed(boolean subscribed) {
        isSubscribed = subscribed;
    }

    public int getMatchPercentage() {
        return matchPercentage;
    }

    public void setMatchPercentage(int matchPercentage) {
        this.matchPercentage = matchPercentage;
    }

    public int getRanking() {
        return ranking;
    }

    public void setRanking(int ranking) {
        this.ranking = ranking;
    }

    public int getHotness() {
        return hotness;
    }

    public void setHotness(int hotness) {
        this.hotness = hotness;
    }

    public long getViewCount() {
        return viewCount;
    }

    public void setViewCount(long viewCount) {
        this.viewCount = viewCount;
    }

    public String getReleaseDate() {
        return releaseDate;
    }

    public void setReleaseDate(String releaseDate) {
        this.releaseDate = releaseDate;
    }

    // 详情页字段的Getter和Setter方法
    public List<ActorModel> getActors() {
        return actors;
    }

    public void setActors(List<ActorModel> actors) {
        this.actors = actors != null ? actors : new ArrayList<>();
    }

    public List<DirectorModel> getDirectors() {
        return directors;
    }

    public void setDirectors(List<DirectorModel> directors) {
        this.directors = directors != null ? directors : new ArrayList<>();
    }

    public List<EpisodeModel> getEpisodes() {
        return episodes;
    }

    public void setEpisodes(List<EpisodeModel> episodes) {
        this.episodes = episodes != null ? episodes : new ArrayList<>();
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags != null ? tags : new ArrayList<>();
    }

    public boolean isLiked() {
        return isLiked;
    }

    public void setLiked(boolean liked) {
        isLiked = liked;
    }

    public String getSynopsis() {
        return synopsis;
    }

    public void setSynopsis(String synopsis) {
        this.synopsis = synopsis;
    }

    public boolean isSynopsisExpanded() {
        return isSynopsisExpanded;
    }

    public void setSynopsisExpanded(boolean synopsisExpanded) {
        isSynopsisExpanded = synopsisExpanded;
    }

    public int getSelectedEpisodeRange() {
        return selectedEpisodeRange;
    }

    public void setSelectedEpisodeRange(int selectedEpisodeRange) {
        this.selectedEpisodeRange = selectedEpisodeRange;
    }

    public int getSelectedEpisodeIndex() {
        return selectedEpisodeIndex;
    }

    public void setSelectedEpisodeIndex(int selectedEpisodeIndex) {
        this.selectedEpisodeIndex = selectedEpisodeIndex;

        // 同时更新currentEpisode，确保UI显示正确
        if (episodes != null && selectedEpisodeIndex >= 0 && selectedEpisodeIndex < episodes.size()) {
            this.currentEpisode = episodes.get(selectedEpisodeIndex).getEpisodeNumber();
        } else {
            // 如果没有剧集列表，使用索引+1作为剧集号
            this.currentEpisode = selectedEpisodeIndex + 1;
        }
    }

    /**
     * 便利方法
     */
    public void toggleSelection() {
        this.isSelected = !this.isSelected;
    }
    
    public String getDisplayTitle() {
        return title != null && !title.isEmpty() ? title : "Unknown Video";
    }
    
    public String getProgressText() {
        return "EP." + currentEpisode + "/EP." + totalEpisodes;
    }
    
    public boolean hasMultipleEpisodes() {
        return totalEpisodes > 1;
    }
    
    public float getWatchProgress() {
        if (totalEpisodes <= 0) return 0.0f;
        return (float) currentEpisode / totalEpisodes;
    }

    /**
     * 检查是否有有效的filmLanguageInfoId
     * @return 如果有有效的filmLanguageInfoId则返回true
     */
    public boolean hasFilmLanguageInfoId() {
        return filmLanguageInfoId != null && !filmLanguageInfoId.trim().isEmpty();
    }

    /**
     * 切换订阅状态
     */
    public void toggleSubscription() {
        this.isSubscribed = !this.isSubscribed;
    }

    /**
     * 获取格式化的播放次数
     * @return 格式化的播放次数字符串 (如: 1.2M, 850K, 1.5K)
     */
    public String getFormattedViewCount() {
        if (viewCount >= 1000000) {
            return String.format("%.1fM", viewCount / 1000000.0);
        } else if (viewCount >= 1000) {
            return String.format("%.1fK", viewCount / 1000.0);
        } else {
            return String.valueOf(viewCount);
        }
    }

    /**
     * 获取合适度百分比文本
     * @return 合适度百分比字符串 (如: 95%)
     */
    public String getMatchPercentageText() {
        return matchPercentage + "%";
    }

    /**
     * 获取排名文本
     * @return 排名字符串
     */
    public String getRankingText() {
        return String.valueOf(ranking);
    }

    /**
     * 获取热度文本
     * @return 热度字符串 (如: "热度 8")
     */
    public String getHotnessText() {
        return "热度 " + hotness;
    }

    /**
     * 获取热度等级描述
     * @return 热度等级描述
     */
    public String getHotnessLevel() {
        if (hotness >= 9) return "超热";
        else if (hotness >= 7) return "很热";
        else if (hotness >= 5) return "较热";
        else if (hotness >= 3) return "一般";
        else return "冷门";
    }

    /**
     * 详情页相关便利方法
     */
    public void toggleLike() {
        this.isLiked = !this.isLiked;
    }

    public void toggleSynopsisExpanded() {
        this.isSynopsisExpanded = !this.isSynopsisExpanded;
    }

    public void addActor(ActorModel actor) {
        if (actor != null && !actors.contains(actor)) {
            actors.add(actor);
        }
    }

    public void addDirector(DirectorModel director) {
        if (director != null && !directors.contains(director)) {
            directors.add(director);
        }
    }

    public void addEpisode(EpisodeModel episode) {
        if (episode != null && !episodes.contains(episode)) {
            episodes.add(episode);
        }
    }

    public void addTag(String tag) {
        if (tag != null && !tag.trim().isEmpty() && !tags.contains(tag)) {
            tags.add(tag);
        }
    }

    public String getEpisodeRangeText() {
        int start = selectedEpisodeRange * 30 + 1;
        int end = Math.min((selectedEpisodeRange + 1) * 30, totalEpisodes);
        return start + "-" + end;
    }

    public void nextEpisodeRange() {
        int maxRange = (totalEpisodes - 1) / 30;
        selectedEpisodeRange = (selectedEpisodeRange + 1) % (maxRange + 1);
    }

    /**
     * 获取所有可用的剧集区间
     */
    public java.util.List<String> getAllEpisodeRanges() {
        java.util.List<String> ranges = new java.util.ArrayList<>();
        int maxRange = (totalEpisodes - 1) / 30;

        for (int i = 0; i <= maxRange; i++) {
            int start = i * 30 + 1;
            int end = Math.min((i + 1) * 30, totalEpisodes);
            ranges.add(start + "-" + end);
        }

        return ranges;
    }



    public List<EpisodeModel> getEpisodesInRange() {
        int start = selectedEpisodeRange * 30;
        int end = Math.min((selectedEpisodeRange + 1) * 30, episodes.size());
        if (start >= episodes.size()) return new ArrayList<>();

        // 创建新的列表，确保选中状态正确
        List<EpisodeModel> rangeEpisodes = new ArrayList<>();
        for (int i = start; i < end; i++) {
            EpisodeModel episode = episodes.get(i);
            // 创建副本以避免修改原始数据
            EpisodeModel episodeCopy = new EpisodeModel(episode.getId(), episode.getEpisodeNumber(), episode.getTitle());
            episodeCopy.setLive(episode.isLive());
            episodeCopy.setWatched(episode.isWatched());

            // 复制新增的字段（用于My List历史记录功能）
            episodeCopy.setProgress(episode.getProgress());
            episodeCopy.setUnlock(episode.isUnlock());
            episodeCopy.setCharge(episode.isCharge());
            episodeCopy.setLastPlayTime(episode.getLastPlayTime());

            // 设置选中状态：当前剧集的全局索引是否等于选中的索引
            episodeCopy.setSelected(i == selectedEpisodeIndex);
            rangeEpisodes.add(episodeCopy);
        }

        return rangeEpisodes;
    }

    public EpisodeModel getSelectedEpisode() {
        if (selectedEpisodeIndex >= 0 && selectedEpisodeIndex < episodes.size()) {
            return episodes.get(selectedEpisodeIndex);
        }
        return null;
    }
    
    @Override
    public String toString() {
        return "VideoModel{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", category='" + category + '\'' +
                ", currentEpisode=" + currentEpisode +
                ", totalEpisodes=" + totalEpisodes +
                ", isSelected=" + isSelected +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        VideoModel that = (VideoModel) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }

    public String getVideoUrl() {
        return videoUri != null ? videoUri.toString() : null;
    }
}
