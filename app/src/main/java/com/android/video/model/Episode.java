package com.android.video.model;

/**
 * 剧集数据模型
 * <AUTHOR>
 */
public class Episode {
    private String id;
    private String downloadRecordId; // 下载记录ID，用于删除操作
    private String episodeNumber; // EP 1, EP 2, etc.
    private String fileSize; // 24MB, 12MB, etc.
    private boolean isDownloaded;
    private String filePath;
    private long fileSizeBytes;
    private boolean isDownloading; // 是否正在下载
    private int downloadProgress; // 下载进度 (0-100)

    public Episode() {
    }

    public Episode(String id, String episodeNumber, String fileSize, boolean isDownloaded) {
        this.id = id;
        this.episodeNumber = episodeNumber;
        this.fileSize = fileSize;
        this.isDownloaded = isDownloaded;
        this.filePath = "";
        this.fileSizeBytes = 0;
        this.isDownloading = false;
        this.downloadProgress = isDownloaded ? 100 : 0;
    }

    public Episode(String id, String episodeNumber, String fileSize, boolean isDownloaded,
                  String filePath, long fileSizeBytes) {
        this.id = id;
        this.episodeNumber = episodeNumber;
        this.fileSize = fileSize;
        this.isDownloaded = isDownloaded;
        this.filePath = filePath;
        this.fileSizeBytes = fileSizeBytes;
        this.isDownloading = false;
        this.downloadProgress = isDownloaded ? 100 : 0;
    }

    // Getters and Setters
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDownloadRecordId() {
        return downloadRecordId;
    }

    public void setDownloadRecordId(String downloadRecordId) {
        this.downloadRecordId = downloadRecordId;
    }

    public String getEpisodeNumber() {
        return episodeNumber;
    }

    public void setEpisodeNumber(String episodeNumber) {
        this.episodeNumber = episodeNumber;
    }

    public String getFileSize() {
        return fileSize;
    }

    public void setFileSize(String fileSize) {
        this.fileSize = fileSize;
    }

    public boolean isDownloaded() {
        return isDownloaded;
    }

    public void setDownloaded(boolean downloaded) {
        isDownloaded = downloaded;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public long getFileSizeBytes() {
        return fileSizeBytes;
    }

    public void setFileSizeBytes(long fileSizeBytes) {
        this.fileSizeBytes = fileSizeBytes;
    }

    public boolean isDownloading() {
        return isDownloading;
    }

    public void setDownloading(boolean downloading) {
        isDownloading = downloading;
    }

    public int getDownloadProgress() {
        return downloadProgress;
    }

    public void setDownloadProgress(int downloadProgress) {
        this.downloadProgress = downloadProgress;
    }

    /**
     * 获取格式化的文件大小
     */
    public String getFormattedFileSize() {
        if (fileSizeBytes < 1024) {
            return fileSizeBytes + " B";
        } else if (fileSizeBytes < 1024 * 1024) {
            return String.format("%.1f KB", fileSizeBytes / 1024.0);
        } else if (fileSizeBytes < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", fileSizeBytes / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", fileSizeBytes / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取显示文本（根据下载状态显示文件大小或进度）
     */
    public String getDisplayText() {
        if (isDownloaded) {
            // 已下载完成，显示文件大小
            if (fileSizeBytes > 0) {
                return getFormattedFileSize();
            } else if (fileSize != null && !fileSize.isEmpty() && !"0 B".equals(fileSize)) {
                return fileSize;
            } else {
                // 文件大小未知但已下载，显示已下载状态
                return "Downloaded";
            }
        } else if (isDownloading && downloadProgress > 0) {
            // 正在下载且有有效进度，显示进度
            return downloadProgress + "%";
        } else {
            // 未下载或下载进度为0，显示文件大小信息（如果有的话）
            if (fileSizeBytes > 0) {
                return getFormattedFileSize();
            } else if (fileSize != null && !fileSize.isEmpty() && !"0 B".equals(fileSize)) {
                return fileSize;
            } else {
                return "Pending";
            }
        }
    }
}
