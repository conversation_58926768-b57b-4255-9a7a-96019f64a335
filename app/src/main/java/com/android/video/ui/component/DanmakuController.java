package com.android.video.ui.component;

import android.content.Context;
import android.graphics.Color;
import android.util.Log;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;

import master.flame.danmaku.controller.DrawHandler;
import master.flame.danmaku.controller.IDanmakuView;
import master.flame.danmaku.danmaku.model.BaseDanmaku;
import master.flame.danmaku.danmaku.model.DanmakuTimer;
import master.flame.danmaku.danmaku.model.IDanmakus;
import master.flame.danmaku.danmaku.model.IDisplayer;
import master.flame.danmaku.danmaku.model.android.DanmakuContext;
import master.flame.danmaku.danmaku.model.android.Danmakus;
import master.flame.danmaku.danmaku.parser.BaseDanmakuParser;
import master.flame.danmaku.ui.widget.DanmakuView;

/**
 * 弹幕控制器
 * 管理弹幕的显示、隐藏、样式设置等功能
 */
public class DanmakuController {
    
    private static final String TAG = "DanmakuController";
    
    private Context context;
    private DanmakuView danmakuView;
    private DanmakuContext danmakuContext;
    private BaseDanmakuParser parser;
    private boolean isEnabled = false;
    private boolean isShowing = false;
    
    // 弹幕样式配置
    private float danmakuSpeed = 1.2f; // 稍微加快滚动速度
    private float danmakuAlpha = 0.9f; // 提高透明度，更清晰
    private int danmakuTextSize = 16; // 适中的文字大小
    private boolean showFPS = false;

    // 弹幕颜色配置
    private int[] danmakuColors = {
        Color.WHITE,           // 白色
        0xFFFFD700,           // 金色
        0xFF00BFFF,           // 深天蓝
        0xFF32CD32,           // 酸橙绿
        0xFFFF69B4,           // 热粉红
        0xFF9370DB,           // 中紫色
        0xFFFF6347,           // 番茄红
        0xFF40E0D0            // 青绿色
    };
    
    public DanmakuController(Context context, DanmakuView danmakuView) {
        this.context = context;
        this.danmakuView = danmakuView;
        initializeDanmaku();
    }
    
    /**
     * 初始化弹幕配置
     */
    private void initializeDanmaku() {
        // 设置最大显示行数
        HashMap<Integer, Integer> maxLinesPair = new HashMap<>();
        maxLinesPair.put(BaseDanmaku.TYPE_SCROLL_RL, 5); // 滚动弹幕最大行数
        maxLinesPair.put(BaseDanmaku.TYPE_FIX_TOP, 3);   // 顶部固定弹幕最大行数
        maxLinesPair.put(BaseDanmaku.TYPE_FIX_BOTTOM, 3); // 底部固定弹幕最大行数
        
        // 设置是否禁止重叠
        HashMap<Integer, Boolean> overlappingEnablePair = new HashMap<>();
        overlappingEnablePair.put(BaseDanmaku.TYPE_SCROLL_RL, false);
        overlappingEnablePair.put(BaseDanmaku.TYPE_FIX_TOP, false);
        overlappingEnablePair.put(BaseDanmaku.TYPE_FIX_BOTTOM, false);
        
        danmakuContext = DanmakuContext.create();
        danmakuContext.setDanmakuStyle(IDisplayer.DANMAKU_STYLE_STROKEN, 3) // 描边样式，描边宽度3px
                .setDuplicateMergingEnabled(false) // 禁用重复弹幕合并
                .setScrollSpeedFactor(danmakuSpeed) // 设置滚动速度
                .setScaleTextSize(1.0f) // 文字缩放比例
                .setCacheStuffer(new SpannedCacheStuffer(), null) // 图文混排使用SpannedCacheStuffer
                .setMaximumLines(maxLinesPair) // 设置最大行数
                .preventOverlapping(overlappingEnablePair) // 防止重叠
                .setDanmakuTransparency(danmakuAlpha) // 设置透明度
                .setDanmakuMargin(12); // 设置弹幕边距
        
        // 创建解析器
        parser = createParser();
    }
    
    /**
     * 创建弹幕解析器
     */
    private BaseDanmakuParser createParser() {
        return new BaseDanmakuParser() {
            @Override
            protected IDanmakus parse() {
                return new Danmakus();
            }
        };
    }
    
    /**
     * 启动弹幕
     */
    public void start() {
        if (danmakuView != null && !isShowing) {
            danmakuView.prepare(parser, danmakuContext);
            danmakuView.showFPS(showFPS);
            danmakuView.enableDanmakuDrawingCache(true);
            isShowing = true;
            Log.d(TAG, "Danmaku started");
        }
    }
    
    /**
     * 暂停弹幕
     */
    public void pause() {
        if (danmakuView != null && isShowing) {
            danmakuView.pause();
            Log.d(TAG, "Danmaku paused");
        }
    }
    
    /**
     * 恢复弹幕
     */
    public void resume() {
        if (danmakuView != null && isShowing) {
            danmakuView.resume();
            Log.d(TAG, "Danmaku resumed");
        }
    }
    
    /**
     * 停止弹幕
     */
    public void stop() {
        if (danmakuView != null && isShowing) {
            danmakuView.release();
            isShowing = false;
            Log.d(TAG, "Danmaku stopped");
        }
    }
    
    /**
     * 显示弹幕
     */
    public void show() {
        if (danmakuView != null) {
            danmakuView.show();
            isEnabled = true;
            Log.d(TAG, "Danmaku shown");
        }
    }
    
    /**
     * 隐藏弹幕
     */
    public void hide() {
        if (danmakuView != null) {
            danmakuView.hide();
            // 立即清除所有弹幕
            danmakuView.clearDanmakusOnScreen();
            isEnabled = false;
            Log.d(TAG, "Danmaku hidden and cleared");
        }
    }
    
    /**
     * 切换弹幕显示状态
     */
    public void toggle() {
        if (isEnabled) {
            hide();
        } else {
            show();
        }
    }
    
    /**
     * 添加弹幕
     */
    public void addDanmaku(String text, boolean isGuest) {
        if (danmakuView != null && isShowing) {
            BaseDanmaku danmaku = danmakuContext.mDanmakuFactory.createDanmaku(BaseDanmaku.TYPE_SCROLL_RL);
            if (danmaku != null) {
                danmaku.text = text;
                danmaku.padding = 8; // 增加内边距
                danmaku.priority = 0;  // 可能会被各种过滤器过滤，所以优先级要高一点
                danmaku.isLive = false;
                danmaku.setTime(danmakuView.getCurrentTime() + 1200);
                danmaku.textSize = sp2px(danmakuTextSize);

                // 使用更丰富的颜色配置
                if (isGuest) {
                    danmaku.textColor = Color.WHITE;
                } else {
                    // 随机选择一个颜色
                    java.util.Random random = new java.util.Random();
                    danmaku.textColor = danmakuColors[random.nextInt(danmakuColors.length)];
                }

                // 设置阴影颜色，增强可读性
                danmaku.textShadowColor = 0x80000000; // 半透明黑色阴影

                danmakuView.addDanmaku(danmaku);
            }
        }
    }
    
    /**
     * 添加测试弹幕数据
     */
    public void addTestDanmakus() {
        String[] testTexts = {
            "这个视频太棒了！",
            "主角演技真好",
            "剧情很精彩",
            "画质很清晰",
            "音效不错",
            "期待下一集",
            "太好看了",
            "推荐给朋友",
            "值得收藏",
            "五星好评"
        };
        
        Random random = new Random();
        for (int i = 0; i < testTexts.length; i++) {
            final String text = testTexts[i];
            final boolean isGuest = random.nextBoolean();
            
            // 延迟添加弹幕，模拟真实场景
            danmakuView.postDelayed(() -> addDanmaku(text, isGuest), i * 2000);
        }
    }
    
    /**
     * 设置弹幕速度
     */
    public void setSpeed(float speed) {
        this.danmakuSpeed = speed;
        if (danmakuContext != null) {
            danmakuContext.setScrollSpeedFactor(speed);
        }
    }
    
    /**
     * 设置弹幕透明度
     */
    public void setAlpha(float alpha) {
        this.danmakuAlpha = alpha;
        if (danmakuView != null) {
            danmakuView.setAlpha(alpha);
        }
    }
    
    /**
     * 设置弹幕文字大小
     */
    public void setTextSize(int textSize) {
        this.danmakuTextSize = textSize;
    }
    
    /**
     * 获取弹幕启用状态
     */
    public boolean isEnabled() {
        return isEnabled;
    }
    
    /**
     * 获取弹幕显示状态
     */
    public boolean isShowing() {
        return isShowing;
    }
    
    /**
     * sp转px
     */
    private float sp2px(float spValue) {
        final float fontScale = context.getResources().getDisplayMetrics().scaledDensity;
        return spValue * fontScale + 0.5f;
    }
    
    /**
     * 自定义缓存装载器
     */
    private static class SpannedCacheStuffer extends master.flame.danmaku.danmaku.model.android.SpannedCacheStuffer {
        // 可以在这里自定义缓存逻辑
    }
}
