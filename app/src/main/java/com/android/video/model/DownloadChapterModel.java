package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 下载章节详情数据模型
 * <AUTHOR> Team
 */
public class DownloadChapterModel {

    /**
     * 下载记录ID
     */
    @SerializedName("downloadRecordId")
    private String downloadRecordId;

    /**
     * 章节ID
     */
    @SerializedName("chapterId")
    private String chapterId;

    /**
     * 章节EP
     */
    @SerializedName("chapterEp")
    private int chapterEp;

    /**
     * 文件大小(字节)
     */
    @SerializedName("fileSize")
    private long fileSize;

    /**
     * 客户端存储路径
     */
    @SerializedName("localPath")
    private String localPath;

    public DownloadChapterModel() {
    }

    public DownloadChapterModel(String downloadRecordId, String chapterId, int chapterEp,
                               long fileSize, String localPath) {
        this.downloadRecordId = downloadRecordId;
        this.chapterId = chapterId;
        this.chapterEp = chapterEp;
        this.fileSize = fileSize;
        this.localPath = localPath;
    }

    // Getters and Setters
    public String getDownloadRecordId() {
        return downloadRecordId;
    }

    public void setDownloadRecordId(String downloadRecordId) {
        this.downloadRecordId = downloadRecordId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public int getChapterEp() {
        return chapterEp;
    }

    public void setChapterEp(int chapterEp) {
        this.chapterEp = chapterEp;
    }

    public long getFileSize() {
        return fileSize;
    }

    public void setFileSize(long fileSize) {
        this.fileSize = fileSize;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    /**
     * 获取格式化的文件大小
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        if (fileSize <= 0) {
            return "0 B";
        }

        final String[] units = {"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(fileSize) / Math.log10(1024));

        if (digitGroups >= units.length) {
            digitGroups = units.length - 1;
        }

        double size = fileSize / Math.pow(1024, digitGroups);
        return String.format("%.1f %s", size, units[digitGroups]);
    }

    /**
     * 检查文件是否存在
     * @return 文件是否存在
     */
    public boolean isFileExists() {
        if (localPath == null || localPath.trim().isEmpty()) {
            return false;
        }

        try {
            java.io.File file = new java.io.File(localPath);
            return file.exists() && file.isFile();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 转换为Episode模型
     * @return Episode对象
     */
    public Episode toEpisode() {
        Episode episode = new Episode();
        episode.setId(chapterId);
        episode.setDownloadRecordId(downloadRecordId); // 设置下载记录ID，用于删除操作
        episode.setEpisodeNumber("EP." + chapterEp); // 转换为字符串格式
        episode.setFileSize(getFormattedFileSize()); // 使用格式化的文件大小字符串

        boolean fileExists = isFileExists();
        episode.setDownloaded(fileExists);
        episode.setFilePath(localPath != null ? localPath : "");
        episode.setFileSizeBytes(fileSize); // 设置字节数

        // 设置下载状态和进度
        if (fileExists) {
            // 文件已存在，表示下载完成
            episode.setDownloading(false);
            episode.setDownloadProgress(100);
        } else {
            // 文件不存在，根据是否有下载记录ID判断下载状态
            if (downloadRecordId != null && !downloadRecordId.trim().isEmpty()) {
                // 有下载记录但文件不存在，可能正在下载
                // 这里设置为可能正在下载的状态，具体进度需要通过API查询
                episode.setDownloading(true);
                episode.setDownloadProgress(0); // 初始进度为0，需要通过API更新
            } else {
                // 没有下载记录，表示未开始下载
                episode.setDownloading(false);
                episode.setDownloadProgress(0);
            }
        }

        return episode;
    }

    @Override
    public String toString() {
        return "DownloadChapterModel{" +
                "downloadRecordId='" + downloadRecordId + '\'' +
                ", chapterId='" + chapterId + '\'' +
                ", chapterEp=" + chapterEp +
                ", fileSize=" + fileSize +
                ", localPath='" + localPath + '\'' +
                ", formattedSize='" + getFormattedFileSize() + '\'' +
                ", fileExists=" + isFileExists() +
                '}';
    }
}
