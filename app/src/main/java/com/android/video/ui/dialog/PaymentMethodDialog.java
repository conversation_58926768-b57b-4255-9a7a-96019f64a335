package com.android.video.ui.dialog;

import android.app.Dialog;
import android.content.Context;
import android.os.Bundle;
import android.view.Window;
import android.view.WindowManager;
import android.widget.LinearLayout;
import android.widget.TextView;

import com.android.video.R;

public class PaymentMethodDialog extends Dialog {

    private LinearLayout optionGooglePay;
    private LinearLayout optionOneVision;
    private TextView tvDialogTitle;
    private OnPaymentMethodSelectedListener listener;

    public interface OnPaymentMethodSelectedListener {
        void onPaymentMethodSelected(String paymentMethod);
    }

    public PaymentMethodDialog(Context context) {
        super(context, R.style.CustomDialog);
    }

    public PaymentMethodDialog(Context context, OnPaymentMethodSelectedListener listener) {
        super(context, R.style.CustomDialog);
        this.listener = listener;
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.dialog_payment_method);

        // 设置窗口属性
        Window window = getWindow();
        if (window != null) {
            WindowManager.LayoutParams params = window.getAttributes();
            params.width = WindowManager.LayoutParams.WRAP_CONTENT;
            params.height = WindowManager.LayoutParams.WRAP_CONTENT;
            window.setAttributes(params);
            window.setDimAmount(0.5f);
        }

        initViews();
        setupClickListeners();

        // 添加弹窗进入动画
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleIn(dialogView);
        }
    }

    private void initViews() {
        optionGooglePay = findViewById(R.id.option_google_pay);
        optionOneVision = findViewById(R.id.option_onevision);
        tvDialogTitle = findViewById(R.id.tv_dialog_title);
    }

    private void setupClickListeners() {
        // Google Pay option
        optionGooglePay.setOnClickListener(v -> {
            // 添加按钮点击动画
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);

            if (listener != null) {
                listener.onPaymentMethodSelected("google");
            }
            dismissWithAnimation();
        });

        // OneVision option
        optionOneVision.setOnClickListener(v -> {
            // 添加按钮点击动画
            com.android.video.utils.UIAnimationUtils.animateButtonClick(v);

            if (listener != null) {
                listener.onPaymentMethodSelected("onevision");
            }
            dismissWithAnimation();
        });
    }

    public void setOnPaymentMethodSelectedListener(OnPaymentMethodSelectedListener listener) {
        this.listener = listener;
    }

    /**
     * 带动画的关闭弹窗
     */
    private void dismissWithAnimation() {
        android.view.View dialogView = findViewById(android.R.id.content);
        if (dialogView != null) {
            com.android.video.utils.UIAnimationUtils.animateDialogScaleOut(dialogView, () -> {
                PaymentMethodDialog.super.dismiss();
            });
        } else {
            super.dismiss();
        }
    }
}
