package com.android.video.model.response;

import java.util.List;

/**
 * 账单订单列表响应数据模型
 */
public class BillOrderListResponse {
    private String code;
    private String message;
    private BillOrderListData data;

    public BillOrderListResponse() {
    }

    public BillOrderListResponse(String code, String message, BillOrderListData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public BillOrderListData getData() {
        return data;
    }

    public void setData(BillOrderListData data) {
        this.data = data;
    }

    /**
     * 账单订单列表数据
     */
    public static class BillOrderListData {
        private List<BillOrderItem> records;
        private int total;
        private int size;
        private int current;
        private int pages;
        private boolean optimizeCountSql;
        private boolean hitCount;
        private String countId;
        private Integer maxLimit;
        private boolean searchCount;

        public BillOrderListData() {
        }

        // Getters and Setters
        public List<BillOrderItem> getRecords() {
            return records;
        }

        public void setRecords(List<BillOrderItem> records) {
            this.records = records;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public int getPages() {
            return pages;
        }

        public void setPages(int pages) {
            this.pages = pages;
        }

        public boolean isOptimizeCountSql() {
            return optimizeCountSql;
        }

        public void setOptimizeCountSql(boolean optimizeCountSql) {
            this.optimizeCountSql = optimizeCountSql;
        }

        public boolean isHitCount() {
            return hitCount;
        }

        public void setHitCount(boolean hitCount) {
            this.hitCount = hitCount;
        }

        public String getCountId() {
            return countId;
        }

        public void setCountId(String countId) {
            this.countId = countId;
        }

        public Integer getMaxLimit() {
            return maxLimit;
        }

        public void setMaxLimit(Integer maxLimit) {
            this.maxLimit = maxLimit;
        }

        public boolean isSearchCount() {
            return searchCount;
        }

        public void setSearchCount(boolean searchCount) {
            this.searchCount = searchCount;
        }
    }
}
