package com.android.video.manager;

import android.content.Context;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.util.Log;

import com.android.video.model.VersionInfoModel;
import com.android.video.network.VersionCheckApiService;
import com.android.video.ui.dialog.UpdateDialog;

/**
 * 版本管理器
 * <p>
 * 负责管理应用版本检查、更新提醒等功能。
 * 采用单例模式，确保全局唯一实例。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VersionManager {

    private static final String TAG = "VersionManager";
    
    // SharedPreferences相关常量
    private static final String PREF_NAME = "version_manager";
    private static final String KEY_LAST_CHECK_TIME = "last_check_time";
    private static final String KEY_IGNORED_VERSION = "ignored_version";
    private static final String KEY_CHECK_INTERVAL = "check_interval";
    
    // 默认检查间隔（24小时）
    private static final long DEFAULT_CHECK_INTERVAL = 24 * 60 * 60 * 1000L;
    
    // 单例实例
    private static volatile VersionManager instance;
    
    private final Context context;
    private final VersionCheckApiService apiService;
    private final SharedPreferences preferences;
    
    // 当前检查状态
    private boolean isChecking = false;
    private VersionInfoModel latestVersionInfo;

    /**
     * 版本检查结果监听器
     */
    public interface VersionCheckListener {
        /**
         * 发现新版本
         * @param versionInfo 新版本信息
         */
        void onNewVersionFound(VersionInfoModel versionInfo);
        
        /**
         * 已是最新版本
         * @param currentVersion 当前版本号
         */
        void onLatestVersion(String currentVersion);
        
        /**
         * 检查失败
         * @param error 错误信息
         */
        void onCheckFailed(String error);
    }

    /**
     * 私有构造函数
     * @param context 应用上下文
     */
    private VersionManager(Context context) {
        this.context = context.getApplicationContext();
        this.apiService = new VersionCheckApiService(this.context);
        this.preferences = this.context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
    }

    /**
     * 获取单例实例
     * @param context 上下文
     * @return VersionManager实例
     */
    public static VersionManager getInstance(Context context) {
        if (instance == null) {
            synchronized (VersionManager.class) {
                if (instance == null) {
                    instance = new VersionManager(context);
                }
            }
        }
        return instance;
    }

    /**
     * 检查版本更新
     * <p>
     * 异步检查是否有新版本可用。
     * 会根据检查间隔和用户忽略设置智能决定是否执行检查。
     * </p>
     * 
     * @param listener 检查结果监听器
     */
    public void checkForUpdate(VersionCheckListener listener) {
        checkForUpdate(listener, false);
    }

    /**
     * 检查版本更新
     * @param listener 检查结果监听器
     * @param forceCheck 是否强制检查（忽略时间间隔限制）
     */
    public void checkForUpdate(VersionCheckListener listener, boolean forceCheck) {
        if (isChecking) {
            Log.d(TAG, "版本检查正在进行中，跳过重复检查");
            return;
        }

        // 检查是否需要执行检查
        if (!forceCheck && !shouldCheckForUpdate()) {
            Log.d(TAG, "跳过版本检查：未到检查时间或用户已忽略");
            if (listener != null) {
                String currentVersion = getCurrentVersionName();
                listener.onLatestVersion(currentVersion);
            }
            return;
        }

        isChecking = true;
        Log.d(TAG, "开始检查版本更新...");

        apiService.getVersionInfo(new VersionCheckApiService.ApiCallback<VersionInfoModel>() {
            @Override
            public void onSuccess(VersionInfoModel versionInfo) {
                isChecking = false;
                handleVersionCheckSuccess(versionInfo, listener);
            }

            @Override
            public void onError(String error) {
                isChecking = false;
                Log.e(TAG, "版本检查失败: " + error);
                if (listener != null) {
                    listener.onCheckFailed(error);
                }
            }
        });
    }

    /**
     * 处理版本检查成功结果
     */
    private void handleVersionCheckSuccess(VersionInfoModel versionInfo, VersionCheckListener listener) {
        latestVersionInfo = versionInfo;
        
        // 更新最后检查时间
        updateLastCheckTime();
        
        Log.d(TAG, "版本检查成功: " + versionInfo.toString());

        // 检查版本是否可用
        if (!versionInfo.isAvailable()) {
            Log.w(TAG, "服务器版本不可用（未发布或已删除）");
            if (listener != null) {
                String currentVersion = getCurrentVersionName();
                listener.onLatestVersion(currentVersion);
            }
            return;
        }

        // 比较版本号
        String currentVersion = getCurrentVersionName();
        if (versionInfo.isNewerThan(currentVersion)) {
            // 检查用户是否已忽略此版本
            String ignoredVersion = getIgnoredVersion();
            if (!versionInfo.getVersionCode().equals(ignoredVersion)) {
                Log.i(TAG, "发现新版本: " + versionInfo.getVersionCode() + " (当前: " + currentVersion + ")");
                if (listener != null) {
                    listener.onNewVersionFound(versionInfo);
                }
            } else {
                Log.d(TAG, "用户已忽略版本: " + versionInfo.getVersionCode());
                if (listener != null) {
                    listener.onLatestVersion(currentVersion);
                }
            }
        } else {
            Log.d(TAG, "当前已是最新版本: " + currentVersion);
            if (listener != null) {
                listener.onLatestVersion(currentVersion);
            }
        }
    }

    /**
     * 显示更新弹窗
     * @param activity Activity实例
     * @param versionInfo 版本信息
     */
    public void showUpdateDialog(android.app.Activity activity, VersionInfoModel versionInfo) {
        showUpdateDialog(activity, versionInfo, true); // 默认强制更新
    }

    /**
     * 显示更新弹窗
     * @param activity Activity实例
     * @param versionInfo 版本信息
     * @param isForceUpdate 是否强制更新
     */
    public void showUpdateDialog(android.app.Activity activity, VersionInfoModel versionInfo, boolean isForceUpdate) {
        if (activity == null) {
            Log.w(TAG, "无法显示更新弹窗：Activity为null");
            return;
        }

        UpdateDialog.showUpdateDialog(activity, versionInfo, new UpdateDialog.OnUpdateDialogListener() {
            @Override
            public void onUpdateClicked() {
                handleUpdateClick(versionInfo);
            }

            @Override
            public void onCancelClicked() {
                Log.d(TAG, "用户选择稍后更新");
                // 可以在这里实现忽略版本的逻辑
                // ignoreVersion(versionInfo.getVersionCode());
            }

            @Override
            public void onDialogDismissed() {
                Log.d(TAG, "更新弹窗被关闭");
            }
        }, isForceUpdate);
    }

    /**
     * 显示更新弹窗（兼容旧版本）
     * @param versionInfo 版本信息
     * @deprecated 使用 {@link #showUpdateDialog(android.app.Activity, VersionInfoModel)} 替代
     */
    @Deprecated
    public void showUpdateDialog(VersionInfoModel versionInfo) {
        if (context instanceof android.app.Activity) {
            showUpdateDialog((android.app.Activity) context, versionInfo);
        } else {
            Log.w(TAG, "无法显示更新弹窗：Context不是Activity类型");
        }
    }

    /**
     * 处理用户点击更新按钮
     */
    private void handleUpdateClick(VersionInfoModel versionInfo) {
        Log.d(TAG, "用户点击更新按钮");
        
        // 这里可以实现下载逻辑或跳转到应用商店
        String packageUrl = versionInfo.getPackageUrl();
        if (packageUrl != null && !packageUrl.trim().isEmpty()) {
            // 打开下载链接
            openDownloadUrl(packageUrl);
        } else {
            Log.w(TAG, "下载链接为空，无法执行更新");
        }
    }

    /**
     * 打开下载链接
     */
    private void openDownloadUrl(String url) {
        try {
            android.content.Intent intent = new android.content.Intent(android.content.Intent.ACTION_VIEW);
            intent.setData(android.net.Uri.parse(url));
            intent.addFlags(android.content.Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
            Log.d(TAG, "打开下载链接: " + url);
        } catch (Exception e) {
            Log.e(TAG, "打开下载链接失败", e);
        }
    }

    /**
     * 忽略指定版本
     * @param versionCode 要忽略的版本号
     */
    public void ignoreVersion(String versionCode) {
        preferences.edit()
            .putString(KEY_IGNORED_VERSION, versionCode)
            .apply();
        Log.d(TAG, "已忽略版本: " + versionCode);
    }

    /**
     * 获取当前应用版本名称
     */
    public String getCurrentVersionName() {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionName;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "获取应用版本失败", e);
            return "1.0.0";
        }
    }

    /**
     * 获取当前应用版本号
     */
    public int getCurrentVersionCode() {
        try {
            PackageManager pm = context.getPackageManager();
            PackageInfo packageInfo = pm.getPackageInfo(context.getPackageName(), 0);
            return packageInfo.versionCode;
        } catch (PackageManager.NameNotFoundException e) {
            Log.e(TAG, "获取应用版本号失败", e);
            return 1;
        }
    }

    /**
     * 检查是否应该执行版本检查
     */
    private boolean shouldCheckForUpdate() {
        long lastCheckTime = preferences.getLong(KEY_LAST_CHECK_TIME, 0);
        long checkInterval = preferences.getLong(KEY_CHECK_INTERVAL, DEFAULT_CHECK_INTERVAL);
        long currentTime = System.currentTimeMillis();
        
        return (currentTime - lastCheckTime) >= checkInterval;
    }

    /**
     * 更新最后检查时间
     */
    private void updateLastCheckTime() {
        preferences.edit()
            .putLong(KEY_LAST_CHECK_TIME, System.currentTimeMillis())
            .apply();
    }

    /**
     * 获取被忽略的版本号
     */
    private String getIgnoredVersion() {
        return preferences.getString(KEY_IGNORED_VERSION, "");
    }

    /**
     * 设置检查间隔
     * @param intervalMillis 检查间隔（毫秒）
     */
    public void setCheckInterval(long intervalMillis) {
        preferences.edit()
            .putLong(KEY_CHECK_INTERVAL, intervalMillis)
            .apply();
        Log.d(TAG, "设置检查间隔: " + intervalMillis + "ms");
    }

    /**
     * 获取最新版本信息
     * @return 最新版本信息，如果未检查过则返回null
     */
    public VersionInfoModel getLatestVersionInfo() {
        return latestVersionInfo;
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        if (apiService != null) {
            apiService.cancelAllRequests();
        }
        isChecking = false;
        Log.d(TAG, "VersionManager资源已清理");
    }
}
