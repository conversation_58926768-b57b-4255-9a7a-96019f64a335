package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import android.net.Uri;
import java.util.List;
import java.util.ArrayList;

/**
 * 瀑布流推荐单个项目模型
 * <p>
 * 用于映射瀑布流推荐API返回的单个推荐项目数据结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmInfo: 短剧信息</li>
 *   <li>firstChapter: 第一章节信息</li>
 *   <li>recommendationScore: 推荐分数</li>
 *   <li>qualities: 分辨率选项列表</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallRecommendItemModel {
    
    /**
     * 短剧信息
     */
    @SerializedName("filmInfo")
    private WaterfallFilmInfoModel filmInfo;
    
    /**
     * 第一章节信息
     */
    @SerializedName("firstChapter")
    private WaterfallFirstChapterModel firstChapter;
    
    /**
     * 推荐分数
     */
    @SerializedName("recommendationScore")
    private int recommendationScore;
    
    /**
     * 分辨率选项列表
     */
    @SerializedName("qualities")
    private List<WaterfallQualityModel> qualities;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallRecommendItemModel() {
        this.qualities = new ArrayList<>();
    }

    /**
     * 完整构造函数
     * 
     * @param filmInfo 短剧信息
     * @param firstChapter 第一章节信息
     * @param recommendationScore 推荐分数
     * @param qualities 分辨率选项列表
     */
    public WaterfallRecommendItemModel(WaterfallFilmInfoModel filmInfo, 
                                     WaterfallFirstChapterModel firstChapter,
                                     int recommendationScore, 
                                     List<WaterfallQualityModel> qualities) {
        this.filmInfo = filmInfo;
        this.firstChapter = firstChapter;
        this.recommendationScore = recommendationScore;
        this.qualities = qualities != null ? qualities : new ArrayList<>();
    }

    // ========== Getter和Setter方法 ==========

    public WaterfallFilmInfoModel getFilmInfo() {
        return filmInfo;
    }

    public void setFilmInfo(WaterfallFilmInfoModel filmInfo) {
        this.filmInfo = filmInfo;
    }

    public WaterfallFirstChapterModel getFirstChapter() {
        return firstChapter;
    }

    public void setFirstChapter(WaterfallFirstChapterModel firstChapter) {
        this.firstChapter = firstChapter;
    }

    public int getRecommendationScore() {
        return recommendationScore;
    }

    public void setRecommendationScore(int recommendationScore) {
        this.recommendationScore = recommendationScore;
    }

    public List<WaterfallQualityModel> getQualities() {
        return qualities;
    }

    public void setQualities(List<WaterfallQualityModel> qualities) {
        this.qualities = qualities != null ? qualities : new ArrayList<>();
    }

    // ========== 工具方法 ==========

    /**
     * 转换为VideoModel
     * @return VideoModel实例
     */
    public VideoModel toVideoModel() {
        VideoModel videoModel = new VideoModel();
        
        if (filmInfo != null) {
            videoModel.setId(filmInfo.getFilmId());
            videoModel.setTitle(filmInfo.getFilmTitle());
            videoModel.setPosterUrl(filmInfo.getCover());
            videoModel.setCategory(filmInfo.getCategoryName());
            videoModel.setDescription(filmInfo.getDetails());
            videoModel.setFilmLanguageInfoId(filmInfo.getFilmLanguageInfoId());
            videoModel.setTotalEpisodes(filmInfo.getTotalChapters());
            videoModel.setViewCount(filmInfo.getPlayNum());
            videoModel.setLiked(filmInfo.isLoved());
        }
        
        if (firstChapter != null) {
            videoModel.setCurrentEpisode(firstChapter.getChapterEp());

            // 设置视频URI
            if (firstChapter.getVideoUrl() != null && !firstChapter.getVideoUrl().isEmpty()) {
                try {
                    videoModel.setVideoUri(Uri.parse(firstChapter.getVideoUrl()));
                } catch (Exception e) {
                    // 如果URL解析失败，记录日志但不中断流程
                    android.util.Log.w("WaterfallRecommendItemModel",
                        "Failed to parse video URL: " + firstChapter.getVideoUrl(), e);
                }
            }

            // 设置视频过期时间
            if (firstChapter.getExpirationTime() != null && !firstChapter.getExpirationTime().isEmpty()) {
                videoModel.setVideoExpirationTime(firstChapter.getExpirationTime());
            }
        }
        
        // 设置推荐分数作为评分
        videoModel.setRating(Math.min(5.0f, recommendationScore / 20.0f)); // 将0-100分转换为0-5分
        
        return videoModel;
    }

    /**
     * 获取默认分辨率
     * @return 默认分辨率，优先返回auto，如果没有则返回第一个
     */
    public WaterfallQualityModel getDefaultQuality() {
        if (qualities == null || qualities.isEmpty()) {
            return null;
        }
        
        // 优先返回auto分辨率
        for (WaterfallQualityModel quality : qualities) {
            if (quality.isAutoQuality()) {
                return quality;
            }
        }
        
        // 如果没有auto，返回第一个
        return qualities.get(0);
    }

    /**
     * 获取可用的免费分辨率列表
     * @return 免费分辨率列表
     */
    public List<WaterfallQualityModel> getFreeQualities() {
        List<WaterfallQualityModel> freeQualities = new ArrayList<>();
        if (qualities != null) {
            for (WaterfallQualityModel quality : qualities) {
                if (!quality.isVipRequired()) {
                    freeQualities.add(quality);
                }
            }
        }
        return freeQualities;
    }

    /**
     * 获取需要VIP的分辨率列表
     * @return VIP分辨率列表
     */
    public List<WaterfallQualityModel> getVipQualities() {
        List<WaterfallQualityModel> vipQualities = new ArrayList<>();
        if (qualities != null) {
            for (WaterfallQualityModel quality : qualities) {
                if (quality.isVipRequired()) {
                    vipQualities.add(quality);
                }
            }
        }
        return vipQualities;
    }

    /**
     * 检查是否有有效的视频数据
     * @return 如果有有效的视频数据则返回true
     */
    public boolean hasValidVideoData() {
        return filmInfo != null && 
               filmInfo.getFilmId() != null && 
               !filmInfo.getFilmId().isEmpty() &&
               firstChapter != null &&
               firstChapter.getVideoUrl() != null &&
               !firstChapter.getVideoUrl().isEmpty();
    }

    @Override
    public String toString() {
        return "WaterfallRecommendItemModel{" +
                "filmInfo=" + filmInfo +
                ", firstChapter=" + firstChapter +
                ", recommendationScore=" + recommendationScore +
                ", qualities=" + qualities +
                '}';
    }
}
