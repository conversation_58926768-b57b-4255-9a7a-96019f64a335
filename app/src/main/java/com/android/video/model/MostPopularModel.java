package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 最受欢迎推荐位数据模型
 * <p>
 * 用于映射最受欢迎推荐位API返回的数据结构。
 * 对应新的Featured API接口中的mostPopularList字段。
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmTitle: 短剧标题</li>
 *   <li>cover: 封面图片URL</li>
 *   <li>languageType: 语言类型（1=英语, 2=俄语, 3=Kaza）</li>
 *   <li>filmId: 短剧唯一标识符</li>
 *   <li>filmLanguageInfoId: 短剧语言信息ID</li>
 *   <li>releaseTime: 发布时间</li>
 *   <li>details: 短剧简介</li>
 *   <li>playNum: 播放量</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class MostPopularModel {
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 语言类型（1=英语, 2=俄语, 3=Kaza）
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 短剧唯一标识符
     */
    @SerializedName("filmId")
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 发布时间
     */
    @SerializedName("releaseTime")
    private String releaseTime;
    
    /**
     * 短剧简介
     */
    @SerializedName("details")
    private String details;
    
    /**
     * 播放量
     */
    @SerializedName("playNum")
    private Long playNum;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public MostPopularModel() {
    }

    /**
     * 完整构造函数
     */
    public MostPopularModel(String filmTitle, String cover, Integer languageType, String filmId,
                           String filmLanguageInfoId, String releaseTime, String details, Long playNum) {
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.releaseTime = releaseTime;
        this.details = details;
        this.playNum = playNum;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Long getPlayNum() {
        return playNum;
    }

    public void setPlayNum(Long playNum) {
        this.playNum = playNum;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否有有效的封面图片
     * 
     * @return 如果有有效封面则返回true
     */
    public boolean hasCover() {
        return cover != null && !cover.trim().isEmpty() && !cover.equals("null");
    }

    /**
     * 获取语言类型的文本描述
     * 
     * @return 语言类型描述
     */
    public String getLanguageTypeText() {
        if (languageType == null) {
            return "未知语言";
        }
        switch (languageType) {
            case 1:
                return "英语";
            case 2:
                return "俄语";
            case 3:
                return "Kaza";
            default:
                return "未知语言";
        }
    }

    /**
     * 获取格式化的播放次数
     * 
     * @return 格式化的播放次数字符串
     */
    public String getFormattedPlayNum() {
        if (playNum == null || playNum == 0) {
            return "0";
        }
        
        if (playNum >= 1000000) {
            return String.format("%.1fM", playNum / 1000000.0);
        } else if (playNum >= 1000) {
            return String.format("%.1fK", playNum / 1000.0);
        } else {
            return String.valueOf(playNum);
        }
    }

    /**
     * 检查数据是否有效
     * 
     * @return 如果数据有效则返回true
     */
    public boolean isValid() {
        return filmTitle != null && !filmTitle.trim().isEmpty() &&
               filmId != null && !filmId.trim().isEmpty();
    }

    /**
     * 转换为VideoModel对象
     * 
     * @return VideoModel对象
     */
    public VideoModel toVideoModel() {
        if (!isValid()) {
            return null;
        }

        VideoModel videoModel = new VideoModel();
        videoModel.setId(filmId);
        videoModel.setTitle(filmTitle);
        videoModel.setPosterUrl(cover);
        videoModel.setDescription(details);
        videoModel.setCategory("most_popular");
        
        // 设置播放次数作为热度值
        if (playNum != null) {
            videoModel.setViewCount(playNum);
        }
        
        // 设置其他默认值
        videoModel.setRating(0.0f);
        videoModel.setDuration("");
        videoModel.setSubscribed(false);
        videoModel.setLiked(false);
        videoModel.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");

        return videoModel;
    }

    @Override
    public String toString() {
        return "MostPopularModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", releaseTime='" + releaseTime + '\'' +
                ", details='" + details + '\'' +
                ", playNum=" + playNum +
                '}';
    }
}
