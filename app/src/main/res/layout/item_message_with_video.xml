<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/message_card_with_video_height"
    android:layout_gravity="center_horizontal"
    android:layout_marginBottom="@dimen/message_card_margin_bottom"
    android:background="@drawable/message_card_bg"
    android:orientation="vertical"
    android:padding="@dimen/message_card_padding">

    <!-- 上半部分：通知图标、标题和日期 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 通知图标容器 -->
        <RelativeLayout
            android:layout_width="@dimen/message_notification_icon_size"
            android:layout_height="@dimen/message_notification_icon_size"
            android:layout_marginEnd="@dimen/message_notification_icon_margin_end">

            <!-- 通知图标 -->
            <ImageView
                android:id="@+id/iv_notification_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/message_notification_icon_bg"
                android:src="@drawable/mess_ic_notice"
                android:scaleType="center" />

            <!-- 未读红点 -->
            <View
                android:id="@+id/message_unread_dot"
                android:layout_width="@dimen/message_unread_dot_size"
                android:layout_height="@dimen/message_unread_dot_size"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_marginEnd="2dp"
                android:layout_marginTop="2dp"
                android:background="@drawable/message_notification_dot"
                android:visibility="gone" />

        </RelativeLayout>

        <!-- 标题和日期 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Message Notification"
                android:textColor="#ffffffff"
                android:textSize="@dimen/message_title_text_size"
                android:fontFamily="sans-serif" />

            <!-- 日期时间 -->
            <TextView
                android:id="@+id/tv_date_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="06/18/2025 10:03AM"
                android:textColor="#80ffffff"
                android:textSize="@dimen/message_date_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

    <!-- 中间部分：内容描述 -->
    <TextView
        android:id="@+id/tv_content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="12dp"
        android:text="The new drama you are following has been launched"
        android:textColor="#ffffffff"
        android:textSize="@dimen/message_content_text_size"
        android:fontFamily="sans-serif"
        android:maxLines="3"
        android:ellipsize="end" />

    <!-- 分割线容器 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:gravity="center_horizontal">

        <!-- 分割线 -->
        <View
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:layout_weight="0.9"
            android:background="#33ffffff" />

    </LinearLayout>

    <!-- 下半部分：视频信息 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 视频海报 -->
        <androidx.cardview.widget.CardView
            android:layout_width="@dimen/message_video_poster_width"
            android:layout_height="@dimen/message_video_poster_height"
            app:cardCornerRadius="9dp"
            app:cardElevation="0dp">

            <ImageView
                android:id="@+id/iv_video_poster"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/movie_poster"
                android:scaleType="centerCrop" />

        </androidx.cardview.widget.CardView>

        <!-- 视频信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginStart="@dimen/message_video_content_margin_start"
            android:orientation="vertical"
            android:gravity="center_vertical">

            <!-- 视频标题和Go按钮 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <!-- 视频标题 -->
                <TextView
                    android:id="@+id/tv_video_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Eternal Love"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/message_video_title_text_size"
                    android:fontFamily="sans-serif" />

                <!-- Go按钮 -->
                <TextView
                    android:id="@+id/tv_go_button"
                    android:layout_width="@dimen/message_go_button_width"
                    android:layout_height="@dimen/message_go_button_height"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:background="@drawable/message_go_button_bg"
                    android:text="Go"
                    android:textColor="#ff000000"
                    android:textSize="@dimen/message_go_button_text_size"
                    android:fontFamily="sans-serif"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true" />

            </RelativeLayout>

            <!-- 集数信息 -->
            <TextView
                android:id="@+id/tv_episode"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:text="EP.10"
                android:textColor="#F12626"
                android:textSize="@dimen/message_episode_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
