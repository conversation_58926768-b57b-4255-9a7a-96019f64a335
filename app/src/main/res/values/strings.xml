<resources>
    <string name="app_name">VideoPlayer</string>
    <string name="action_settings">Settings</string>
    <!-- Strings used for fragments for navigation -->
    <string name="first_fragment_label">First Fragment</string>
    <string name="second_fragment_label">Second Fragment</string>
    <string name="next">Next</string>
    <string name="previous">Previous</string>

    <!-- Login Screen Strings -->
    <string name="app_logo">App Logo</string>
    <string name="phone_icon">Phone Icon</string>
    <string name="facebook_icon">Facebook Icon</string>
    <string name="tiktok_icon">TikTok Icon</string>
    <string name="log_in_phone_number">Log in Phone Number</string>
    <string name="log_in_with_facebook">Login with VK</string>
    <string name="log_in_tiktok">Log in TikTok</string>
    <string name="privacy_notice">Dear user, please log in first. Login is only used to protect your account security and viewing records. The platform will not store your relevant information. Please rest assured! We will not store your information, please feel free to log in!</string>
    <string name="user_agreement">By logging in, you agree to the user agreement and privacy policy.</string>
    <string name="back_button">Back Button</string>

    <!-- Phone Number Validation Strings -->
    <string name="phone_validation_empty">Please enter your phone number</string>
    <string name="phone_validation_invalid_us">Invalid US phone number format. Please use format: (XXX) XXX-XXXX</string>
    <string name="phone_validation_invalid_russia">Invalid Russian phone number format. Please use format: 8 XXX XXX-XX-XX</string>
    <string name="phone_validation_invalid_china">Invalid Chinese phone number format. Please use format: 1XX XXXX XXXX</string>
    <string name="phone_validation_invalid_format">Invalid phone number format or unsupported country</string>
    <string name="phone_validation_unsupported_country">Unsupported country code</string>

    <!-- Bottom Navigation Strings -->
    <string name="nav_home">Home</string>
    <string name="nav_discover">Discover</string>
    <string name="nav_my_list">My list</string>
    <string name="nav_profile">Profile</string>

    <!-- Profile Page Strings -->
    <string name="profile_guest">Guest</string>
    <string name="profile_uid">UID:43310879</string>
    <string name="profile_log_in">Log in</string>
    <string name="profile_become_vip">Become a VIP - Enjoy all benefits</string>
    <string name="profile_unlimited_access">Unlimited access to all series</string>
    <string name="profile_go">GO!</string>
    <string name="profile_my_point">My point</string>
    <string name="profile_refill">Refill</string>
    <string name="profile_language">Language</string>
    <string name="profile_message">Message</string>
    <string name="profile_bill">Bill</string>
    <string name="profile_setting">Setting</string>

    <!-- VIP Card Strings -->
    <string name="profile_edit">Edit</string>
    <string name="profile_vip_accepted">VIP accepted</string>
    <string name="profile_automatic_renewal">Automatic renewal</string>
    <string name="profile_renewal_by_week">Renewal by Week</string>
    <string name="profile_watch_videos_1080p">Watch videos and enjoy 1080P</string>
    <string name="profile_expiration_date">Expiration date 06/12/2025</string>

    <string name="lorem_ipsum">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nam in scelerisque sem. Mauris volutpat, dolor id
        interdum ullamcorper, risus dolor egestas lectus, sit amet mattis purus dui nec risus. Maecenas non sodales
        nisi, vel dictum dolor. Class aptent taciti sociosqu ad litora torquent per conubia nostra, per inceptos
        himenaeos. Suspendisse blandit eleifend diam, vel rutrum tellus vulputate quis. Aliquam eget libero aliquet,
        imperdiet nisl a, ornare ex. Sed rhoncus est ut libero porta lobortis. Fusce in dictum tellus.\n\n
        Suspendisse interdum ornare ante. Aliquam nec cursus lorem. Morbi id magna felis. Vivamus egestas, est a
        condimentum egestas, turpis nisl iaculis ipsum, in dictum tellus dolor sed neque. Morbi tellus erat, dapibus ut
        sem a, iaculis tincidunt dui. Interdum et malesuada fames ac ante ipsum primis in faucibus. Curabitur et eros
        porttitor, ultricies urna vitae, molestie nibh. Phasellus at commodo eros, non aliquet metus. Sed maximus nisl
        nec dolor bibendum, vel congue leo egestas.\n\n
        Sed interdum tortor nibh, in sagittis risus mollis quis. Curabitur mi odio, condimentum sit amet auctor at,
        mollis non turpis. Nullam pretium libero vestibulum, finibus orci vel, molestie quam. Fusce blandit tincidunt
        nulla, quis sollicitudin libero facilisis et. Integer interdum nunc ligula, et fermentum metus hendrerit id.
        Vestibulum lectus felis, dictum at lacinia sit amet, tristique id quam. Cras eu consequat dui. Suspendisse
        sodales nunc ligula, in lobortis sem porta sed. Integer id ultrices magna, in luctus elit. Sed a pellentesque
        est.\n\n
        Aenean nunc velit, lacinia sed dolor sed, ultrices viverra nulla. Etiam a venenatis nibh. Morbi laoreet, tortor
        sed facilisis varius, nibh orci rhoncus nulla, id elementum leo dui non lorem. Nam mollis ipsum quis auctor
        varius. Quisque elementum eu libero sed commodo. In eros nisl, imperdiet vel imperdiet et, scelerisque a mauris.
        Pellentesque varius ex nunc, quis imperdiet eros placerat ac. Duis finibus orci et est auctor tincidunt. Sed non
        viverra ipsum. Nunc quis augue egestas, cursus lorem at, molestie sem. Morbi a consectetur ipsum, a placerat
        diam. Etiam vulputate dignissim convallis. Integer faucibus mauris sit amet finibus convallis.\n\n
        Phasellus in aliquet mi. Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis
        egestas. In volutpat arcu ut felis sagittis, in finibus massa gravida. Pellentesque id tellus orci. Integer
        dictum, lorem sed efficitur ullamcorper, libero justo consectetur ipsum, in mollis nisl ex sed nisl. Donec
        maximus ullamcorper sodales. Praesent bibendum rhoncus tellus nec feugiat. In a ornare nulla. Donec rhoncus
        libero vel nunc consequat, quis tincidunt nisl eleifend. Cras bibendum enim a justo luctus vestibulum. Fusce
        dictum libero quis erat maximus, vitae volutpat diam dignissim.
    </string>

    <!-- Home Page Strings -->
    <!-- Section Titles -->
    <string name="home_categories_title">Categories</string>
    <string name="home_continue_watching_title">Continue Watching</string>
    <string name="home_coming_soon_title">Coming Soon</string>
    <string name="home_guess_you_like_title">Guess You Like</string>
    <string name="home_current_hot_title">Current Hot</string>
    <string name="home_today_hot_title">Today Hot</string>
    <string name="home_most_popular_title">Most Popular</string>

    <!-- Button Texts -->
    <string name="home_see_all">See All</string>
    <string name="home_play_now">Play Now</string>
    <string name="home_watch_now">Watch Now</string>
    <string name="home_continue">Continue</string>
    <string name="home_add_to_list">Add to List</string>
    <string name="home_remove_from_list">Remove from List</string>

    <!-- Category Tags -->
    <string name="home_category_all">ALL</string>
    <string name="home_category_animation">动画</string>
    <string name="home_category_movie">电影</string>
    <string name="home_category_tv_series">电视剧</string>
    <string name="home_category_variety">综艺</string>
    <string name="home_category_documentary">纪录片</string>
    <string name="home_category_kids">少儿</string>
    <string name="home_category_sports">体育</string>
    <string name="home_category_news">新闻</string>
    <string name="home_category_music">音乐</string>

    <!-- Video Titles (Test Data) -->
    <string name="home_video_pleasant_goat">喜洋洋与灰太狼</string>
    <string name="home_video_boonie_bears">熊出没</string>
    <string name="home_video_boonie_bears_earth">熊出没·重返地球</string>
    <string name="home_video_piggy_peach">猪猪侠</string>
    <string name="home_video_piggy_adventure">猪猪侠大冒险</string>
    <string name="home_video_super_wings">超级飞侠</string>
    <string name="home_video_super_wings_adventure">超级飞侠环球大冒险</string>
    <string name="home_video_peppa_pig">小猪佩奇</string>

    <!-- Progress Format Strings -->
    <string name="home_episode_progress">EP.%1$d/EP.%2$d</string>
    <string name="home_watch_progress">%1$.1f%% watched</string>
    <string name="home_episodes_total">%d episodes</string>
    <string name="home_duration_format">%d min</string>

    <!-- Toast Messages -->
    <string name="home_toast_search">搜索功能</string>
    <string name="home_toast_play_current">播放当前选中视频</string>
    <string name="home_toast_see_all_categories">查看所有分类</string>
    <string name="home_toast_see_all_continue_watching">查看所有继续观看</string>
    <string name="home_toast_carousel_click">轮播图点击: %1$s (位置: %2$d)</string>
    <string name="home_toast_tag_click">标签点击: %1$s (选中: %2$s)</string>
    <string name="home_toast_video_click">视频点击: %1$s (分类: %2$s)</string>
    <string name="home_toast_continue_watching_click">继续观看: %1$s %2$s</string>
    <string name="home_toast_continue_watching_long_click">长按: %1$s (进度: %2$s)</string>
    <string name="home_toast_data_refreshed">首页数据已刷新</string>

    <!-- Content Descriptions for Accessibility -->
    <string name="home_desc_banner_poster">Banner海报</string>
    <string name="home_desc_home_logo">首页Logo</string>
    <string name="home_desc_search_icon">搜索图标</string>
    <string name="home_desc_play_button">播放按钮</string>
    <string name="home_desc_next_icon">下一页图标</string>
    <string name="home_desc_video_poster">视频海报</string>
    <string name="home_desc_carousel_item">轮播图项目</string>

    <!-- Error Messages -->
    <string name="home_error_load_failed">加载失败，请重试</string>
    <string name="home_error_network">网络连接失败</string>
    <string name="home_error_no_data">暂无数据</string>
    <string name="home_error_video_unavailable">视频暂不可用</string>

    <!-- Status Messages -->
    <string name="home_status_loading">加载中…</string>
    <string name="home_status_refreshing">刷新中…</string>
    <string name="home_status_completed">已看完</string>
    <string name="home_status_new">新片</string>
    <string name="home_status_hot">热门</string>
    <string name="home_status_vip_only">VIP专享</string>

    <!-- Video Player Button Texts -->
    <string name="video_player_danmaku">Danmaku</string>
    <string name="video_player_speed">Speed</string>
    <string name="video_player_quality">Quality</string>
    <string name="video_player_fullscreen">Fullscreen</string>
    <string name="video_player_episode">Episode</string>
    <string name="video_player_small_screen">Small Screen</string>
    <string name="video_player_download">Download</string>
    <string name="video_player_back">Back</string>
    <string name="video_player_play_pause">Play/Pause</string>
    <string name="video_player_seek_forward">Seek Forward 5s</string>
    <string name="video_player_seek_backward">Seek Backward 5s</string>

    <!-- Episode Selection Texts -->
    <string name="episode_format">EP.%d</string>
    <string name="episode_switch_message">Switched to Episode %d</string>
    <string name="episode_no_episodes">No episodes available</string>

    <!-- Third-party Login Configuration -->
    <!-- Facebook App ID - Replace with your actual Facebook App ID -->
    <string name="facebook_app_id">your_facebook_app_id</string>
    <!-- Facebook Client Token - Replace with your actual Facebook Client Token -->
    <string name="facebook_client_token">your_facebook_client_token</string>
    <!-- Facebook Login Protocol Scheme -->
    <string name="fb_login_protocol_scheme">fb_your_facebook_app_id</string>

    <!-- Third-party Login Messages -->
    <string name="third_party_login_facebook_connecting">Connecting to Facebook...</string>
    <string name="third_party_login_tiktok_connecting">Connecting to TikTok...</string>
    <string name="third_party_login_facebook_success">Facebook login successful!</string>
    <string name="third_party_login_tiktok_success">TikTok login successful!</string>
    <string name="third_party_login_facebook_failed">Facebook login failed</string>
    <string name="third_party_login_tiktok_failed">TikTok login failed</string>
    <string name="third_party_login_facebook_cancelled">Facebook login cancelled</string>
    <string name="third_party_login_tiktok_cancelled">TikTok login cancelled</string>
    <string name="third_party_login_tiktok_app_not_installed">TikTok app is not installed. Please install TikTok first.</string>

    <!-- Video Player Strings -->
    <string name="close">关闭</string>
    <string name="subtitle_switch">字幕开关</string>

    <!-- Update Dialog Strings -->
    <string name="update_dialog_title">Time To Update!</string>
    <string name="update_dialog_version">V%s</string>
    <string name="update_dialog_description_line1">For more features and a</string>
    <string name="update_dialog_description_line2">better user experience, upgrade</string>
    <string name="update_dialog_description_line3">your app please.</string>
    <string name="update_dialog_button">Update App</string>

</resources>
