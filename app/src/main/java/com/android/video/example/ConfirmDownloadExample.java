package com.android.video.example;

import android.util.Log;

import com.android.video.model.request.ConfirmDownloadRequestModel;
import com.android.video.model.response.ConfirmDownloadResponseModel;
import com.android.video.network.ConfirmDownloadApiService;

/**
 * 确认下载接口使用示例
 * <p>
 * 演示如何使用ConfirmDownloadApiService来确认下载完成。
 * 包含基本用法、错误处理以及最佳实践。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class ConfirmDownloadExample {

    private static final String TAG = "ConfirmDownloadExample";

    /**
     * 基本使用示例
     * <p>
     * 演示如何创建请求模型并调用确认下载接口。
     * </p>
     */
    public static void basicUsageExample() {
        Log.d(TAG, "=== 基本使用示例 ===");

        // 1. 创建请求数据模型
        ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
                "2d9bf4f909fef05528998764daa94c60", // 下载记录ID
                "a1b2c3d4e5f6",                      // 文件校验和
                "/storage/emulated/0/AppName/videos/101.mp4" // 本地存储路径
        );

        // 2. 获取API服务实例
        ConfirmDownloadApiService apiService = ConfirmDownloadApiService.getInstance();

        // 3. 调用确认下载接口
        apiService.confirmDownload(requestModel, new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                Log.d(TAG, "确认下载成功");
                Log.d(TAG, "响应码: " + response.getCode());
                Log.d(TAG, "响应消息: " + response.getMessage());
                Log.d(TAG, "响应数据: " + response.getData());
                Log.d(TAG, "成功信息: " + response.getSuccessMessage());
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "确认下载失败: " + errorMessage);
                // 在这里处理错误，例如显示错误提示给用户
            }
        });

        Log.d(TAG, "确认下载请求已发送");
    }

    /**
     * 参数验证示例
     * <p>
     * 演示如何进行请求参数验证，避免发送无效请求。
     * </p>
     */
    public static void parameterValidationExample() {
        Log.d(TAG, "=== 参数验证示例 ===");

        // 创建一个无效的请求模型（缺少必要参数）
        ConfirmDownloadRequestModel invalidModel = new ConfirmDownloadRequestModel();
        invalidModel.setDownloadRecordId(""); // 空的下载记录ID
        invalidModel.setFileChecksum("a1b2c3d4e5f6");
        invalidModel.setLocalPath("/storage/emulated/0/AppName/videos/101.mp4");

        // 在发送请求前进行验证
        if (!invalidModel.isValid()) {
            String error = invalidModel.getValidationError();
            Log.e(TAG, "请求参数验证失败: " + error);
            // 在实际应用中，这里应该显示错误提示给用户
            return;
        }

        // 如果验证通过，才发送请求
        ConfirmDownloadApiService apiService = ConfirmDownloadApiService.getInstance();
        apiService.confirmDownload(invalidModel, new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                Log.d(TAG, "确认下载成功: " + response.getSuccessMessage());
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "确认下载失败: " + errorMessage);
            }
        });
    }

    /**
     * 错误处理示例
     * <p>
     * 演示如何处理各种可能的错误情况。
     * </p>
     */
    public static void errorHandlingExample() {
        Log.d(TAG, "=== 错误处理示例 ===");

        ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
                "invalid_record_id", // 可能无效的下载记录ID
                "invalid_checksum",  // 可能无效的校验和
                "/invalid/path"      // 可能无效的路径
        );

        ConfirmDownloadApiService apiService = ConfirmDownloadApiService.getInstance();
        apiService.confirmDownload(requestModel, new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                Log.d(TAG, "确认下载成功: " + response.getSuccessMessage());
                
                // 处理成功情况
                handleConfirmDownloadSuccess(response);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "确认下载失败: " + errorMessage);
                
                // 处理错误情况
                handleConfirmDownloadError(errorMessage);
            }
        });
    }

    /**
     * 处理确认下载成功的情况
     *
     * @param response 成功响应数据
     */
    private static void handleConfirmDownloadSuccess(ConfirmDownloadResponseModel response) {
        // 在实际应用中，这里可能需要：
        // 1. 更新UI显示下载完成状态
        // 2. 更新本地数据库记录
        // 3. 显示成功提示给用户
        // 4. 触发其他相关业务逻辑

        Log.d(TAG, "处理确认下载成功:");
        Log.d(TAG, "- 更新UI状态");
        Log.d(TAG, "- 更新数据库记录");
        Log.d(TAG, "- 显示成功提示: " + response.getSuccessMessage());
    }

    /**
     * 处理确认下载失败的情况
     *
     * @param errorMessage 错误信息
     */
    private static void handleConfirmDownloadError(String errorMessage) {
        // 在实际应用中，这里可能需要：
        // 1. 显示错误提示给用户
        // 2. 记录错误日志
        // 3. 根据错误类型决定是否重试
        // 4. 更新下载状态为失败

        Log.e(TAG, "处理确认下载失败:");
        Log.e(TAG, "- 显示错误提示: " + errorMessage);
        Log.e(TAG, "- 记录错误日志");
        Log.e(TAG, "- 考虑重试机制");
    }

    /**
     * 完整的下载确认流程示例
     * <p>
     * 演示一个完整的下载确认流程，包括参数准备、验证、请求发送和结果处理。
     * </p>
     *
     * @param downloadRecordId 下载记录ID
     * @param fileChecksum 文件校验和
     * @param localPath 本地存储路径
     */
    public static void completeConfirmDownloadFlow(String downloadRecordId, String fileChecksum, String localPath) {
        Log.d(TAG, "=== 完整下载确认流程 ===");
        Log.d(TAG, "开始确认下载: " + downloadRecordId);

        // 1. 创建请求模型
        ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
                downloadRecordId,
                fileChecksum,
                localPath
        );

        // 2. 验证请求参数
        if (!requestModel.isValid()) {
            String error = requestModel.getValidationError();
            Log.e(TAG, "请求参数无效: " + error);
            handleConfirmDownloadError("请求参数无效: " + error);
            return;
        }

        // 3. 发送确认下载请求
        ConfirmDownloadApiService apiService = ConfirmDownloadApiService.getInstance();
        apiService.confirmDownload(requestModel, new ConfirmDownloadApiService.ConfirmDownloadCallback() {
            @Override
            public void onSuccess(ConfirmDownloadResponseModel response) {
                Log.d(TAG, "下载确认流程完成: " + response.getSuccessMessage());
                handleConfirmDownloadSuccess(response);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "下载确认流程失败: " + errorMessage);
                handleConfirmDownloadError(errorMessage);
            }
        });

        Log.d(TAG, "下载确认请求已发送，等待响应...");
    }

    /**
     * 运行所有示例
     */
    public static void runAllExamples() {
        Log.d(TAG, "========== 确认下载接口使用示例 ==========");

        // 基本使用示例
        basicUsageExample();

        // 等待一段时间再运行下一个示例
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 参数验证示例
        parameterValidationExample();

        // 等待一段时间再运行下一个示例
        try {
            Thread.sleep(1000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // 错误处理示例
        errorHandlingExample();

        // 完整流程示例
        completeConfirmDownloadFlow(
                "2d9bf4f909fef05528998764daa94c60",
                "a1b2c3d4e5f6",
                "/storage/emulated/0/AppName/videos/101.mp4"
        );

        Log.d(TAG, "========== 所有示例运行完成 ==========");
    }
}
