package com.android.video.utils;

import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.fragment.app.FragmentManager;

import com.android.video.ui.fragment.ProfileFragment;

/**
 * Profile页面更新助手类
 * <p>
 * 提供便捷的方法来通知ProfileFragment更新用户信息，
 * 主要用于登录成功后刷新Profile页面的用户数据。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see ProfileFragment
 */
public class ProfileUpdateHelper {
    
    private static final String TAG = "ProfileUpdateHelper";
    
    /**
     * 通知ProfileFragment用户登录成功
     * <p>
     * 在用户登录成功后调用此方法，会自动找到当前活动的ProfileFragment
     * 并触发用户信息的API刷新。
     * </p>
     * 
     * @param activity 当前的FragmentActivity
     */
    public static void notifyLoginSuccess(FragmentActivity activity) {
        if (activity == null) {
            android.util.Log.w(TAG, "Activity为null，无法通知Profile更新");
            return;
        }
        
        try {
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            
            // 查找ProfileFragment实例
            ProfileFragment profileFragment = findProfileFragment(fragmentManager);
            
            if (profileFragment != null && profileFragment.isAdded()) {
                // 通知ProfileFragment登录成功
                profileFragment.onLoginSuccess();
                android.util.Log.d(TAG, "已通知ProfileFragment用户登录成功");
            } else {
                android.util.Log.w(TAG, "未找到活动的ProfileFragment");
            }
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "通知Profile更新时发生错误", e);
        }
    }
    
    /**
     * 查找ProfileFragment实例
     * <p>
     * 在FragmentManager中查找ProfileFragment的实例。
     * 支持多种可能的Fragment标签和ID。
     * </p>
     * 
     * @param fragmentManager Fragment管理器
     * @return ProfileFragment实例，如果未找到则返回null
     */
    private static ProfileFragment findProfileFragment(FragmentManager fragmentManager) {
        // 常见的ProfileFragment标签
        String[] possibleTags = {
            "ProfileFragment",
            "profile",
            "Profile",
            "fragment_profile",
            "profile_fragment"
        };
        
        // 尝试通过标签查找
        for (String tag : possibleTags) {
            Fragment fragment = fragmentManager.findFragmentByTag(tag);
            if (fragment instanceof ProfileFragment) {
                return (ProfileFragment) fragment;
            }
        }
        
        // 遍历所有Fragment查找ProfileFragment
        for (Fragment fragment : fragmentManager.getFragments()) {
            if (fragment instanceof ProfileFragment) {
                return (ProfileFragment) fragment;
            }
        }
        
        return null;
    }
    
    /**
     * 强制刷新Profile页面用户信息
     * <p>
     * 强制刷新ProfileFragment的用户信息，无论用户是否刚刚登录。
     * 适用于用户信息可能发生变化的场景。
     * </p>
     * 
     * @param activity 当前的FragmentActivity
     */
    public static void forceRefreshProfile(FragmentActivity activity) {
        if (activity == null) {
            android.util.Log.w(TAG, "Activity为null，无法强制刷新Profile");
            return;
        }
        
        try {
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            ProfileFragment profileFragment = findProfileFragment(fragmentManager);
            
            if (profileFragment != null && profileFragment.isAdded()) {
                // 触发用户信息刷新
                profileFragment.onLoginSuccess(); // 复用登录成功的逻辑
                android.util.Log.d(TAG, "已强制刷新ProfileFragment用户信息");
            } else {
                android.util.Log.w(TAG, "未找到活动的ProfileFragment，无法强制刷新");
            }
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "强制刷新Profile时发生错误", e);
        }
    }
    
    /**
     * 检查ProfileFragment是否存在且活跃
     * <p>
     * 检查当前是否有活跃的ProfileFragment实例。
     * </p>
     * 
     * @param activity 当前的FragmentActivity
     * @return 如果ProfileFragment存在且活跃则返回true，否则返回false
     */
    public static boolean isProfileFragmentActive(FragmentActivity activity) {
        if (activity == null) {
            return false;
        }
        
        try {
            FragmentManager fragmentManager = activity.getSupportFragmentManager();
            ProfileFragment profileFragment = findProfileFragment(fragmentManager);
            
            return profileFragment != null && profileFragment.isAdded() && profileFragment.isVisible();
            
        } catch (Exception e) {
            android.util.Log.e(TAG, "检查ProfileFragment状态时发生错误", e);
            return false;
        }
    }
}
