<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical">

        <!-- 标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Edit profile"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 个人信息卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/edit_profile_card_margin_top"
            android:layout_marginStart="@dimen/edit_profile_item_margin_bottom"
            android:layout_marginEnd="@dimen/edit_profile_item_margin_bottom"
            android:background="@drawable/edit_profile_card_bg"
            android:orientation="vertical"
            android:padding="@dimen/edit_profile_item_padding">

            <!-- Modify avatar -->
            <RelativeLayout
                android:id="@+id/item_modify_avatar"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/edit_profile_item_margin_bottom"
                android:background="?android:attr/selectableItemBackground"
                android:clickable="true"
                android:focusable="true"
                android:paddingTop="8dp"
                android:paddingBottom="8dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/edit_profile_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Modify avatar"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/edit_profile_text_size"
                    android:fontFamily="sans-serif" />

                <!-- 头像 -->
                <ImageView
                    android:id="@+id/iv_avatar"
                    android:layout_width="@dimen/edit_profile_avatar_size"
                    android:layout_height="@dimen/edit_profile_avatar_size"
                    android:layout_toStartOf="@+id/iv_avatar_arrow"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="8dp"
                    android:src="@drawable/profile_norentouxiang"
                    android:background="@drawable/edit_profile_avatar_border"
                    android:scaleType="centerCrop" />

                <!-- 箭头 -->
                <ImageView
                    android:id="@+id/iv_avatar_arrow"
                    android:layout_width="@dimen/edit_profile_arrow_size"
                    android:layout_height="@dimen/edit_profile_arrow_size"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/set_ic_next" />

            </RelativeLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="@dimen/edit_profile_divider_width"
                android:layout_height="@dimen/edit_profile_divider_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/edit_profile_item_margin_bottom"
                android:background="@drawable/edit_profile_divider" />

            <!-- Change nickname -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/edit_profile_item_margin_bottom"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/edit_profile_text_line_height"
                    android:layout_marginBottom="8dp"
                    android:text="Change nickname"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/edit_profile_text_size"
                    android:fontFamily="sans-serif" />

                <EditText
                    android:id="@+id/et_nickname"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/edit_profile_input_height"
                    android:background="@drawable/edit_profile_nickname_input_bg"
                    android:hint="Use123212"
                    android:textColorHint="#80ffffff"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/edit_profile_text_size"
                    android:fontFamily="sans-serif"
                    android:paddingStart="16dp"
                    android:paddingEnd="16dp"
                    android:gravity="center_vertical"
                    android:inputType="text"
                    android:maxLength="50"
                    android:maxLines="1" />

            </LinearLayout>

            <!-- 分割线 -->
            <View
                android:layout_width="@dimen/edit_profile_divider_width"
                android:layout_height="@dimen/edit_profile_divider_height"
                android:layout_gravity="center_horizontal"
                android:layout_marginBottom="@dimen/edit_profile_item_margin_bottom"
                android:background="@drawable/edit_profile_divider" />

            <!-- Contact information -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/edit_profile_text_line_height"
                    android:layout_marginBottom="8dp"
                    android:text="Contact information"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/edit_profile_text_size"
                    android:fontFamily="sans-serif" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/edit_profile_input_height"
                    android:background="@drawable/edit_profile_contact_input_bg">

                    <!-- 国家代码下拉 -->
                    <LinearLayout
                        android:id="@+id/layout_country_code"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_alignParentStart="true"
                        android:background="?android:attr/selectableItemBackground"
                        android:clickable="true"
                        android:focusable="true"
                        android:gravity="center_vertical"
                        android:orientation="horizontal"
                        android:paddingStart="16dp"
                        android:paddingEnd="8dp">

                        <TextView
                            android:id="@+id/tv_country_code"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="+10"
                            android:textColor="#ffffffff"
                            android:textSize="@dimen/edit_profile_text_size"
                            android:fontFamily="sans-serif" />

                        <ImageView
                            android:layout_width="12dp"
                            android:layout_height="12dp"
                            android:layout_marginStart="4dp"
                            android:src="@drawable/edit_ic_xiala" />

                    </LinearLayout>

                    <!-- 手机号输入 -->
                    <EditText
                        android:id="@+id/et_phone"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_toEndOf="@+id/layout_country_code"
                        android:background="@android:color/transparent"
                        android:hint="1201335788"
                        android:textColorHint="#80ffffff"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/edit_profile_text_size"
                        android:fontFamily="sans-serif"
                        android:paddingStart="8dp"
                        android:paddingEnd="16dp"
                        android:gravity="center_vertical"
                        android:inputType="number"
                        android:digits="0123456789"
                        android:maxLines="1" />

                </RelativeLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- Current IP location -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_profile_ip_location_height"
            android:layout_marginTop="@dimen/edit_profile_item_margin_bottom"
            android:layout_marginStart="@dimen/edit_profile_item_margin_bottom"
            android:layout_marginEnd="@dimen/edit_profile_item_margin_bottom"
            android:background="@drawable/edit_profile_ip_location_bg"
            android:padding="@dimen/edit_profile_item_padding">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="@dimen/edit_profile_text_line_height"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="Current IP location"
                android:textColor="#ffffffff"
                android:textSize="@dimen/edit_profile_text_size"
                android:fontFamily="sans-serif" />

            <TextView
                android:id="@+id/tv_ip_location"
                android:layout_width="@dimen/edit_profile_china_text_width"
                android:layout_height="@dimen/edit_profile_text_line_height"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:text="China"
                android:textColor="#ffffffff"
                android:textSize="@dimen/edit_profile_text_size"
                android:fontFamily="sans-serif"
                android:gravity="end" />

        </RelativeLayout>

        <!-- 占位空间，让Save按钮靠近底部 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1" />

        <!-- Save 按钮 -->
        <TextView
            android:id="@+id/btn_save"
            android:layout_width="match_parent"
            android:layout_height="@dimen/edit_profile_save_button_height"
            android:layout_marginBottom="@dimen/edit_profile_save_button_margin_bottom"
            android:layout_marginStart="@dimen/edit_profile_item_margin_bottom"
            android:layout_marginEnd="@dimen/edit_profile_item_margin_bottom"
            android:background="@drawable/edit_profile_save_button_bg"
            android:text="Save"
            android:textColor="#ffffffff"
            android:textSize="@dimen/edit_profile_text_size"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

</FrameLayout>
