package com.android.video.download;

import java.io.Serializable;

/**
 * 下载信息类
 * <p>
 * 封装单个下载任务的所有信息，包括URL、文件路径、进度、状态等。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadInfo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 下载状态枚举
     */
    public enum DownloadStatus {
        PENDING,    // 等待中
        DOWNLOADING, // 下载中
        PAUSED,     // 已暂停
        COMPLETED,  // 已完成
        FAILED,     // 失败
        CANCELLED   // 已取消
    }

    private String downloadId;           // 下载ID（唯一标识）
    private String downloadRecordId;     // 服务器下载记录ID
    private String url;                  // 下载URL
    private String fileName;             // 文件名
    private String filePath;             // 本地文件路径
    private String tempFilePath;         // 临时文件路径
    private long totalSize;              // 文件总大小
    private long downloadedSize;         // 已下载大小
    private int progress;                // 下载进度（0-100）
    private DownloadStatus status;       // 下载状态
    private String errorMessage;         // 错误信息
    private long createTime;             // 创建时间
    private long startTime;              // 开始时间
    private long completeTime;           // 完成时间
    private int retryCount;              // 重试次数
    private String checksum;             // 文件校验和
    private String chapterId;            // 章节ID
    private String filmLanguageInfoId;   // 短剧语言信息ID
    private String title;                // 视频标题
    private String description;          // 描述

    /**
     * 构造函数
     */
    public DownloadInfo() {
        this.downloadId = generateDownloadId();
        this.status = DownloadStatus.PENDING;
        this.createTime = System.currentTimeMillis();
        this.progress = 0;
        this.downloadedSize = 0;
        this.totalSize = 0;
        this.retryCount = 0;
    }

    /**
     * 构造函数
     *
     * @param url 下载URL
     * @param fileName 文件名
     * @param filePath 文件路径
     */
    public DownloadInfo(String url, String fileName, String filePath) {
        this();
        this.url = url;
        this.fileName = fileName;
        this.filePath = filePath;
        this.tempFilePath = filePath + ".tmp";
    }

    /**
     * 生成唯一的下载ID
     *
     * @return 下载ID
     */
    private String generateDownloadId() {
        return "download_" + System.currentTimeMillis() + "_" + hashCode();
    }

    /**
     * 更新下载进度
     *
     * @param downloadedSize 已下载大小
     * @param totalSize 总大小
     */
    public void updateProgress(long downloadedSize, long totalSize) {
        this.downloadedSize = downloadedSize;
        this.totalSize = totalSize;
        
        if (totalSize > 0) {
            this.progress = (int) ((downloadedSize * 100) / totalSize);
        } else {
            this.progress = 0;
        }
    }

    /**
     * 获取下载速度（字节/秒）
     *
     * @return 下载速度
     */
    public long getDownloadSpeed() {
        if (startTime == 0 || status != DownloadStatus.DOWNLOADING) {
            return 0;
        }
        
        long elapsedTime = System.currentTimeMillis() - startTime;
        if (elapsedTime > 0) {
            return (downloadedSize * 1000) / elapsedTime;
        }
        
        return 0;
    }

    /**
     * 获取格式化的下载速度
     *
     * @return 格式化的下载速度字符串
     */
    public String getFormattedDownloadSpeed() {
        long speed = getDownloadSpeed();
        return formatFileSize(speed) + "/s";
    }

    /**
     * 获取剩余时间（秒）
     *
     * @return 剩余时间
     */
    public long getRemainingTime() {
        long speed = getDownloadSpeed();
        if (speed > 0 && totalSize > downloadedSize) {
            return (totalSize - downloadedSize) / speed;
        }
        return -1;
    }

    /**
     * 获取格式化的剩余时间
     *
     * @return 格式化的剩余时间字符串
     */
    public String getFormattedRemainingTime() {
        long remainingTime = getRemainingTime();
        if (remainingTime < 0) {
            return "未知";
        }
        
        if (remainingTime < 60) {
            return remainingTime + "秒";
        } else if (remainingTime < 3600) {
            return (remainingTime / 60) + "分钟";
        } else {
            return (remainingTime / 3600) + "小时";
        }
    }

    /**
     * 格式化文件大小
     *
     * @param size 文件大小（字节）
     * @return 格式化的文件大小字符串
     */
    public static String formatFileSize(long size) {
        if (size < 1024) {
            return size + " B";
        } else if (size < 1024 * 1024) {
            return String.format("%.1f KB", size / 1024.0);
        } else if (size < 1024 * 1024 * 1024) {
            return String.format("%.1f MB", size / (1024.0 * 1024.0));
        } else {
            return String.format("%.1f GB", size / (1024.0 * 1024.0 * 1024.0));
        }
    }

    /**
     * 获取格式化的文件大小
     *
     * @return 格式化的文件大小字符串
     */
    public String getFormattedFileSize() {
        return formatFileSize(totalSize);
    }

    /**
     * 获取格式化的已下载大小
     *
     * @return 格式化的已下载大小字符串
     */
    public String getFormattedDownloadedSize() {
        return formatFileSize(downloadedSize);
    }

    /**
     * 检查是否可以重试
     *
     * @param maxRetryCount 最大重试次数
     * @return 如果可以重试返回true，否则返回false
     */
    public boolean canRetry(int maxRetryCount) {
        return retryCount < maxRetryCount && 
               (status == DownloadStatus.FAILED || status == DownloadStatus.PAUSED);
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 重置重试次数
     */
    public void resetRetryCount() {
        this.retryCount = 0;
    }

    /**
     * 检查下载是否完成
     *
     * @return 如果下载完成返回true，否则返回false
     */
    public boolean isCompleted() {
        return status == DownloadStatus.COMPLETED;
    }

    /**
     * 检查下载是否正在进行
     *
     * @return 如果正在下载返回true，否则返回false
     */
    public boolean isDownloading() {
        return status == DownloadStatus.DOWNLOADING;
    }

    /**
     * 检查下载是否失败
     *
     * @return 如果下载失败返回true，否则返回false
     */
    public boolean isFailed() {
        return status == DownloadStatus.FAILED;
    }

    /**
     * 检查下载是否被取消
     *
     * @return 如果下载被取消返回true，否则返回false
     */
    public boolean isCancelled() {
        return status == DownloadStatus.CANCELLED;
    }

    // ========== Getter和Setter方法 ==========

    public String getDownloadId() {
        return downloadId;
    }

    public void setDownloadId(String downloadId) {
        this.downloadId = downloadId;
    }

    public String getDownloadRecordId() {
        return downloadRecordId;
    }

    public void setDownloadRecordId(String downloadRecordId) {
        this.downloadRecordId = downloadRecordId;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
        this.tempFilePath = filePath + ".tmp";
    }

    public String getTempFilePath() {
        return tempFilePath;
    }

    public void setTempFilePath(String tempFilePath) {
        this.tempFilePath = tempFilePath;
    }

    public long getTotalSize() {
        return totalSize;
    }

    public void setTotalSize(long totalSize) {
        this.totalSize = totalSize;
    }

    public long getDownloadedSize() {
        return downloadedSize;
    }

    public void setDownloadedSize(long downloadedSize) {
        this.downloadedSize = downloadedSize;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = Math.max(0, Math.min(100, progress));
    }

    public DownloadStatus getStatus() {
        return status;
    }

    public void setStatus(DownloadStatus status) {
        this.status = status;
        
        if (status == DownloadStatus.DOWNLOADING && startTime == 0) {
            this.startTime = System.currentTimeMillis();
        } else if (status == DownloadStatus.COMPLETED && completeTime == 0) {
            this.completeTime = System.currentTimeMillis();
        }
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }

    public long getCreateTime() {
        return createTime;
    }

    public void setCreateTime(long createTime) {
        this.createTime = createTime;
    }

    public long getStartTime() {
        return startTime;
    }

    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }

    public long getCompleteTime() {
        return completeTime;
    }

    public void setCompleteTime(long completeTime) {
        this.completeTime = completeTime;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public void setRetryCount(int retryCount) {
        this.retryCount = retryCount;
    }

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "DownloadInfo{" +
                "downloadId='" + downloadId + '\'' +
                ", fileName='" + fileName + '\'' +
                ", progress=" + progress +
                ", status=" + status +
                ", downloadedSize=" + downloadedSize +
                ", totalSize=" + totalSize +
                '}';
    }
}
