package com.android.video.cache;

import android.content.Context;
import android.util.Log;

import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;

/**
 * 智能缓存管理器
 * 包装ImprovedDataCacheManager，自动记录访问时间和管理缓存生命周期
 */
public class SmartCacheManager {
    private static final String TAG = "SmartCacheManager";
    
    private static volatile SmartCacheManager instance;
    private final ImprovedDataCacheManager dataCacheManager;
    private final AdvancedCacheManager advancedCacheManager;
    
    private SmartCacheManager(Context context) {
        this.dataCacheManager = ImprovedDataCacheManager.getInstance(context);
        this.advancedCacheManager = AdvancedCacheManager.getInstance(context);
    }
    
    public static SmartCacheManager getInstance(Context context) {
        if (instance == null) {
            synchronized (SmartCacheManager.class) {
                if (instance == null) {
                    instance = new SmartCacheManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 智能缓存数据（自动记录访问时间）
     * 支持缓存空数据，当后端返回空数据时会清空缓存
     */
    public <T> boolean cacheDataSmart(String key, T data) {
        if (key == null) {
            return false;
        }

        try {
            // 检查数据是否为空（null、空列表、空字符串等）
            boolean isEmptyData = isDataEmpty(data);

            if (isEmptyData) {
                // 数据为空时，清除现有缓存
                Log.d(TAG, "数据为空，清除缓存: " + key);
                dataCacheManager.clearCachedData(key);
                return true;
            }

            // 使用原有的缓存方法
            boolean success = dataCacheManager.cacheDataAtomic(key, data);

            if (success) {
                // 计算数据大小（估算）
                String jsonData = dataCacheManager.getGson().toJson(data);
                long dataSize = jsonData.length();

                // 记录缓存访问
                advancedCacheManager.recordCacheAccess(key, dataSize);

                Log.d(TAG, "智能缓存成功: " + key + " (大小: " + formatFileSize(dataSize) + ")");
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "智能缓存失败: " + key, e);
            return false;
        }
    }
    
    /**
     * 智能读取缓存数据（自动记录访问时间）
     */
    public <T> T getCachedDataSmart(String key, Type type) {
        if (key == null || type == null) {
            return null;
        }
        
        try {
            // 使用原有的读取方法
            T data = dataCacheManager.getCachedDataAtomic(key, type);
            
            if (data != null) {
                // 计算数据大小（估算）
                String jsonData = dataCacheManager.getGson().toJson(data);
                long dataSize = jsonData.length();
                
                // 记录缓存访问
                advancedCacheManager.recordCacheAccess(key, dataSize);
                
                Log.d(TAG, "智能读取缓存成功: " + key + " (大小: " + formatFileSize(dataSize) + ")");
            }
            
            return data;
        } catch (Exception e) {
            Log.e(TAG, "智能读取缓存失败: " + key, e);
            return null;
        }
    }
    
    /**
     * 智能读取缓存数据（泛型版本）
     */
    public <T> T getCachedDataSmart(String key, TypeToken<T> typeToken) {
        return getCachedDataSmart(key, typeToken.getType());
    }

    /**
     * 强制缓存数据（包括空数据）
     * 用于需要明确缓存空数据的场景
     */
    public <T> boolean forceCacheDataSmart(String key, T data) {
        if (key == null) {
            return false;
        }

        try {
            // 强制缓存，即使数据为空
            boolean success = dataCacheManager.cacheDataAtomic(key, data);

            if (success) {
                // 计算数据大小（估算）
                String jsonData = dataCacheManager.getGson().toJson(data);
                long dataSize = jsonData.length();

                // 记录缓存访问
                advancedCacheManager.recordCacheAccess(key, dataSize);

                Log.d(TAG, "强制智能缓存成功: " + key + " (大小: " + formatFileSize(dataSize) + ")");
            }

            return success;
        } catch (Exception e) {
            Log.e(TAG, "强制智能缓存失败: " + key, e);
            return false;
        }
    }
    
    /**
     * 清除缓存数据
     */
    public void clearCachedData(String key) {
        if (key == null) {
            return;
        }
        
        dataCacheManager.clearCachedData(key);
        Log.d(TAG, "清除缓存: " + key);
    }
    
    /**
     * 检查缓存是否有效
     */
    public boolean isCacheValid(String key) {
        return dataCacheManager.isCacheValid(key);
    }
    
    /**
     * 清除指定命名空间的所有缓存
     */
    public void clearCacheNamespace(String namespace) {
        dataCacheManager.clearCacheNamespace(namespace);
        Log.d(TAG, "清除命名空间缓存: " + namespace);
    }
    
    /**
     * 检查指定命名空间是否有有效缓存
     */
    public boolean hasValidCacheInNamespace(String namespace) {
        return dataCacheManager.hasValidCacheInNamespace(namespace);
    }
    
    /**
     * 获取缓存统计信息
     */
    public ImprovedDataCacheManager.CacheStats getCacheStats() {
        return dataCacheManager.getCacheStats();
    }

    /**
     * 判断数据是否为空
     * 支持检测null、空列表、空字符串等情况
     */
    private <T> boolean isDataEmpty(T data) {
        if (data == null) {
            return true;
        }

        // 检查字符串是否为空
        if (data instanceof String) {
            return ((String) data).trim().isEmpty();
        }

        // 检查集合是否为空
        if (data instanceof java.util.Collection) {
            return ((java.util.Collection<?>) data).isEmpty();
        }

        // 检查数组是否为空
        if (data.getClass().isArray()) {
            return java.lang.reflect.Array.getLength(data) == 0;
        }

        // 检查Map是否为空
        if (data instanceof java.util.Map) {
            return ((java.util.Map<?, ?>) data).isEmpty();
        }

        // 对于其他对象类型，不认为是空数据
        return false;
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        dataCacheManager.clearAllCache();
        Log.d(TAG, "清除所有缓存");
    }
    
    /**
     * 手动触发缓存清理
     */
    public void performManualCleanup() {
        Log.d(TAG, "开始手动缓存清理");
        advancedCacheManager.performAutomaticCleanup();
        Log.d(TAG, "手动缓存清理完成");
    }
    
    /**
     * 获取缓存空间使用情况
     */
    public CacheSpaceInfo getCacheSpaceInfo() {
        try {
            ImprovedDataCacheManager.CacheStats stats = getCacheStats();
            long totalCacheSize = stats.totalDataSize;

            // 获取设备总存储空间
            long totalStorage = android.os.Environment.getDataDirectory().getTotalSpace();
            long storageLimit = (long) (totalStorage * 0.20f); // 20%限制

            float usagePercentage = (float) totalCacheSize / storageLimit * 100;

            return new CacheSpaceInfo(
                totalCacheSize,
                storageLimit,
                usagePercentage,
                stats.diskCacheSize
            );
        } catch (Exception e) {
            Log.e(TAG, "获取缓存空间信息失败", e);
            return new CacheSpaceInfo(0, 0, 0, 0);
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long size) {
        if (size <= 0) return "0.0B";
        
        final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
        int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
        return String.format("%.1f%s", size / Math.pow(1024, digitGroups), units[digitGroups]);
    }
    
    /**
     * 缓存空间信息
     */
    public static class CacheSpaceInfo {
        private final long currentSize;
        private final long limitSize;
        private final float usagePercentage;
        private final int entryCount;
        
        public CacheSpaceInfo(long currentSize, long limitSize, float usagePercentage, int entryCount) {
            this.currentSize = currentSize;
            this.limitSize = limitSize;
            this.usagePercentage = usagePercentage;
            this.entryCount = entryCount;
        }
        
        public long getCurrentSize() {
            return currentSize;
        }
        
        public long getLimitSize() {
            return limitSize;
        }
        
        public float getUsagePercentage() {
            return usagePercentage;
        }
        
        public int getEntryCount() {
            return entryCount;
        }
        
        public String getCurrentSizeFormatted() {
            return formatFileSize(currentSize);
        }
        
        public String getLimitSizeFormatted() {
            return formatFileSize(limitSize);
        }
        
        public boolean isNearLimit() {
            return usagePercentage > 80.0f;
        }
        
        public boolean isOverLimit() {
            return usagePercentage > 100.0f;
        }
        
        private static String formatFileSize(long size) {
            if (size <= 0) return "0.0B";
            
            final String[] units = new String[]{"B", "KB", "MB", "GB", "TB"};
            int digitGroups = (int) (Math.log10(size) / Math.log10(1024));
            return String.format("%.1f%s", size / Math.pow(1024, digitGroups), units[digitGroups]);
        }
        
        @Override
        public String toString() {
            return String.format("CacheSpaceInfo{current=%s, limit=%s, usage=%.1f%%, entries=%d}",
                getCurrentSizeFormatted(), getLimitSizeFormatted(), usagePercentage, entryCount);
        }
    }
}
