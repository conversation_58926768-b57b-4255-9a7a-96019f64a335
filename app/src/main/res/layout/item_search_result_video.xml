<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="149.5dp"
    android:layout_marginHorizontal="16dp"
    android:layout_marginVertical="8dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报图片 -->
    <ImageView
        android:id="@+id/iv_poster"
        android:layout_width="112dp"
        android:layout_height="149.5dp"
        android:scaleType="centerCrop"
        android:background="@drawable/search_result_poster_background"
        android:src="@drawable/movie_poster"
        android:clipToOutline="true"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="16dp"
        android:text="Escorting the heiress"
        android:textColor="#ffffffff"
        android:textSize="16sp"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="top"
        app:layout_constraintStart_toEndOf="@id/iv_poster"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 当前剧集 -->
    <TextView
        android:id="@+id/tv_current_episode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="8dp"
        android:text="EP.72"
        android:textColor="#e6ffffff"
        android:textSize="13sp"
        app:layout_constraintStart_toEndOf="@id/iv_poster"
        app:layout_constraintTop_toBottomOf="@id/tv_title" />

    <!-- 总剧集（隐藏，因为设计图中没有显示） -->
    <TextView
        android:id="@+id/tv_total_episode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone" />

    <!-- 介绍文本 -->
    <TextView
        android:id="@+id/tv_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="12dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="16dp"
        android:text="a captivating historical drama set in a grand palace, where love and power collide. The story …"
        android:textColor="#a3ffffff"
        android:textSize="14sp"
        android:maxLines="3"
        android:ellipsize="end"
        app:layout_constraintStart_toEndOf="@id/iv_poster"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@id/tv_current_episode" />

    <!-- 播放按钮（隐藏，因为设计图中没有显示） -->
    <ImageView
        android:id="@+id/btn_play"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

    <!-- 喜欢按钮（隐藏，因为设计图中没有显示） -->
    <ImageView
        android:id="@+id/iv_like"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone" />

</androidx.constraintlayout.widget.ConstraintLayout>
