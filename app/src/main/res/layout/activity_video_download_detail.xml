<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- Header with back button and title -->
        <RelativeLayout
            android:id="@+id/header_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- Back button -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- Title -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="downloaded"
                android:textColor="#ffffffff"
                android:textSize="@dimen/download_page_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- Video info card -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/download_detail_card_height"
            android:layout_marginStart="@dimen/download_detail_card_margin_horizontal"
            android:layout_marginEnd="@dimen/download_detail_card_margin_horizontal"
            android:layout_marginTop="@dimen/download_detail_card_margin_top"
            android:background="@android:color/transparent">

            <!-- Video poster -->
            <ImageView
                android:id="@+id/iv_poster"
                android:layout_width="@dimen/download_detail_poster_width"
                android:layout_height="@dimen/download_detail_poster_height"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:scaleType="centerCrop"
                android:src="@drawable/movie_poster"
                android:background="@drawable/download_poster_bg"
                android:clipToOutline="true" />

            <!-- Content area (right of poster) -->
            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_toEndOf="@id/iv_poster"
                android:layout_toStartOf="@id/btn_play"
                android:layout_marginStart="@dimen/download_detail_content_margin_start"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/download_detail_content_margin_end"
                android:orientation="vertical">

                <!-- Video title -->
                <TextView
                    android:id="@+id/tv_video_title"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/download_detail_title_text_size"
                    android:maxLines="1"
                    android:ellipsize="end"
                    android:fontFamily="sans-serif" />

                <!-- Downloaded status -->
                <TextView
                    android:id="@+id/tv_downloaded_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/download_detail_status_margin_top"
                    android:text="Downloaded/Total episodes"
                    android:textColor="#59ffffff"
                    android:textSize="@dimen/download_detail_status_text_size"
                    android:fontFamily="sans-serif" />

                <!-- Progress and file size container -->
                <LinearLayout
                    android:id="@+id/ll_episode_progress"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/download_detail_progress_margin_top"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:clickable="true"
                    android:focusable="true"
                    android:background="?android:attr/selectableItemBackgroundBorderless">

                    <!-- Downloaded count (red) -->
                    <TextView
                        android:id="@+id/tv_downloaded_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="EP.0"
                        android:textColor="#fff12626"
                        android:textSize="@dimen/download_detail_count_text_size"
                        android:fontFamily="sans-serif" />

                    <!-- Separator -->
                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="/"
                        android:textColor="#a3ffffff"
                        android:textSize="@dimen/download_detail_count_text_size"
                        android:fontFamily="sans-serif" />

                    <!-- Total count -->
                    <TextView
                        android:id="@+id/tv_total_count"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="EP.0"
                        android:textColor="#a3ffffff"
                        android:textSize="@dimen/download_detail_count_text_size"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

                <!-- File size -->
                <TextView
                    android:id="@+id/tv_total_file_size"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/download_detail_file_size_margin_top"
                    android:text="0 B"
                    android:textColor="#8fffffff"
                    android:textSize="@dimen/download_detail_file_size_text_size"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- Play button -->
            <LinearLayout
                android:id="@+id/btn_play"
                android:layout_width="@dimen/download_detail_play_button_width"
                android:layout_height="@dimen/download_detail_play_button_height"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal"
                android:gravity="center"
                android:background="@drawable/download_play_button_bg"
                android:clickable="true"
                android:focusable="true">

                <!-- Play icon -->
                <ImageView
                    android:layout_width="@dimen/download_detail_play_icon_size"
                    android:layout_height="@dimen/download_detail_play_icon_size"
                    android:layout_marginEnd="@dimen/download_detail_play_icon_margin_end"
                    android:src="@drawable/list_ic_play" />

                <!-- Play text -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Play"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/download_detail_play_text_size"
                    android:gravity="center"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

        </RelativeLayout>

        <!-- Episodes list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_episodes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/download_detail_episodes_margin_top"
            android:paddingStart="@dimen/download_detail_episodes_padding_horizontal"
            android:paddingEnd="@dimen/download_detail_episodes_padding_horizontal"
            android:paddingBottom="@dimen/download_detail_episodes_padding_bottom"
            android:clipToPadding="false"
            android:nestedScrollingEnabled="false" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
