package com.android.video.ui.activity;

import android.os.Bundle;
import android.text.TextUtils;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.model.response.MessageDetailResponseModel;
import com.android.video.network.MessageApiService;

public class MessageDetailActivity extends BaseFullScreenActivity {

    private static final String TAG = "MessageDetailActivity";

    private ImageView ivBack;
    private TextView tvMessageTitle;
    private TextView tvDateTime;
    private TextView tvDetailContent;

    // 下拉刷新
    private SwipeRefreshLayout swipeRefreshLayout;
    private LinearLayout llMessageContent;

    private MessageApiService messageApiService;
    private String sendMessageId;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_message_detail);

        initViews();

        // 初始化API服务
        messageApiService = new MessageApiService();

        // 获取传递的参数
        getSendMessageIdFromIntent();

        // 加载消息详情数据
        loadMessageDetailData();
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvMessageTitle = findViewById(R.id.tv_message_title);
        tvDateTime = findViewById(R.id.tv_date_time);
        tvDetailContent = findViewById(R.id.tv_detail_content);

        // 下拉刷新
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        llMessageContent = findViewById(R.id.ll_message_content);

        // 返回按钮点击事件
        ivBack.setOnClickListener(v -> finish());

        // 设置下拉刷新
        setupSwipeRefresh();

        // 初始状态：显示加载
        showLoadingState();
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        if (swipeRefreshLayout != null) {
            // 设置刷新颜色
            swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );

            // 设置下拉刷新监听
            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "下拉刷新触发");
                if (!TextUtils.isEmpty(sendMessageId)) {
                    loadMessageDetailData();
                } else {
                    swipeRefreshLayout.setRefreshing(false);
                }
            });
        }
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }
    }

    /**
     * 停止刷新动画
     */
    private void stopRefreshAnimation() {
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
        }
    }

    /**
     * 从Intent获取消息推送ID
     */
    private void getSendMessageIdFromIntent() {
        sendMessageId = getIntent().getStringExtra("sendMessageId");

        Log.d(TAG, "Intent中的所有extras:");
        if (getIntent().getExtras() != null) {
            for (String key : getIntent().getExtras().keySet()) {
                Object value = getIntent().getExtras().get(key);
                Log.d(TAG, "  " + key + " = " + value);
            }
        }

        if (TextUtils.isEmpty(sendMessageId)) {
            Log.e(TAG, "未获取到消息推送ID");
            Toast.makeText(this, "消息ID无效", Toast.LENGTH_SHORT).show();
            finish();
            return;
        }

        Log.d(TAG, "获取到消息推送ID: " + sendMessageId);

        // 不再预先显示基本信息，等待API加载完成后统一显示
    }

    /**
     * 加载消息详情数据
     */
    private void loadMessageDetailData() {
        if (TextUtils.isEmpty(sendMessageId)) {
            Log.e(TAG, "消息推送ID为空，无法加载详情");
            return;
        }

        Log.d(TAG, "开始加载消息详情，sendMessageId: " + sendMessageId);

        messageApiService.getMessageDetail(sendMessageId, new MessageApiService.ApiCallback<MessageDetailResponseModel>() {
            @Override
            public void onSuccess(MessageDetailResponseModel response) {
                Log.d(TAG, "消息详情加载成功: " + response.toString());

                if (response.getData() != null) {
                    MessageDetailResponseModel.MessageDetailData data = response.getData();

                    // 更新UI
                    runOnUiThread(() -> {
                        // 设置消息标题
                        if (!TextUtils.isEmpty(data.getMessageTitle())) {
                            tvMessageTitle.setText(data.getMessageTitle());
                        }

                        // 设置创建时间
                        if (!TextUtils.isEmpty(data.getCreateTime())) {
                            tvDateTime.setText(data.getCreateTime());
                        }

                        // 设置消息内容
                        if (!TextUtils.isEmpty(data.getMessageContent())) {
                            tvDetailContent.setText(data.getMessageContent());
                        } else {
                            tvDetailContent.setText("暂无详细内容");
                        }

                        // 停止刷新动画
                        stopRefreshAnimation();
                    });
                } else {
                    Log.w(TAG, "消息详情数据为空");
                    runOnUiThread(() -> {
                        tvDetailContent.setText("暂无详细内容");
                        // 停止刷新动画
                        stopRefreshAnimation();
                    });
                }
            }

            @Override
            public void onError(String error) {
                Log.e(TAG, "消息详情加载失败: " + error);

                runOnUiThread(() -> {
                    Toast.makeText(MessageDetailActivity.this, "加载失败: " + error, Toast.LENGTH_SHORT).show();
                    tvDetailContent.setText("加载失败，请稍后重试");
                    // 停止刷新动画
                    stopRefreshAnimation();
                });
            }
        });
    }
}
