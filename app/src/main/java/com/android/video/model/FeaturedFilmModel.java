package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 推荐位影片数据模型
 * <p>
 * 用于映射推荐位API返回的影片信息JSON数据结构。
 * 包含影片的标题、分类、封面、语言类型、影片ID等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class FeaturedFilmModel {

    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;

    /**
     * 分类ID
     */
    @SerializedName("categoryId")
    private String categoryId;

    /**
     * 分类名称
     */
    @SerializedName("categoryName")
    private String categoryName;

    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;

    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;

    /**
     * 短剧ID
     */
    @SerializedName("filmId")
    private String filmId;

    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;

    /**
     * 搜索次数
     */
    @SerializedName("searchNum")
    private Integer searchNum;

    /**
     * 短剧详情
     */
    @SerializedName("details")
    private String details;

    /**
     * 是否收藏
     * 0=未收藏, 1=已收藏
     */
    @SerializedName("isLove")
    private Integer isLove;

    /**
     * 播放次数
     */
    @SerializedName("playNum")
    private Long playNum;

    /**
     * 总章节数
     */
    @SerializedName("totalChaptersNum")
    private Integer totalChaptersNum;

    /**
     * 默认构造函数
     */
    public FeaturedFilmModel() {
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Integer getIsLove() {
        return isLove;
    }

    public void setIsLove(Integer isLove) {
        this.isLove = isLove;
    }

    public Long getPlayNum() {
        return playNum;
    }

    public void setPlayNum(Long playNum) {
        this.playNum = playNum;
    }

    public Integer getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(Integer totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否已收藏
     *
     * @return 如果已收藏返回true，否则返回false
     */
    public boolean isLoved() {
        return isLove != null && isLove == 1;
    }

    /**
     * 获取语言类型文本
     *
     * @return 语言类型文本
     */
    public String getLanguageTypeText() {
        if (languageType == null) return "未知";
        return languageType == 1 ? "英语" : "俄语";
    }

    /**
     * 检查是否有封面图片
     *
     * @return 如果有封面图片返回true，否则返回false
     */
    public boolean hasCover() {
        return cover != null && !cover.trim().isEmpty();
    }

    /**
     * 获取格式化的播放次数
     *
     * @return 格式化的播放次数字符串
     */
    public String getFormattedPlayNum() {
        if (playNum == null || playNum == 0) return "0";

        if (playNum >= 1000000) {
            return String.format("%.1fM", playNum / 1000000.0);
        } else if (playNum >= 1000) {
            return String.format("%.1fK", playNum / 1000.0);
        } else {
            return String.valueOf(playNum);
        }
    }

    /**
     * 转换为VideoModel对象
     *
     * @return VideoModel对象
     */
    public com.android.video.model.VideoModel toVideoModel() {
        com.android.video.model.VideoModel videoModel = new com.android.video.model.VideoModel();
        videoModel.setId(filmId);
        videoModel.setTitle(filmTitle);
        videoModel.setPosterUrl(cover);
        videoModel.setDescription(details);
        videoModel.setCategory(categoryName != null ? categoryName : "");

        // 设置播放次数作为热度值
        if (playNum != null) {
            videoModel.setViewCount(playNum);
        }

        // 设置总章节数
        if (totalChaptersNum != null) {
            videoModel.setTotalEpisodes(totalChaptersNum);
        }

        // 设置喜欢状态
        if (isLove != null) {
            videoModel.setLiked(isLove == 1);
        }

        // 设置其他默认值
        videoModel.setRating(0.0f);
        videoModel.setDuration("");
        videoModel.setSubscribed(false);
        videoModel.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");

        return videoModel;
    }

    @Override
    public String toString() {
        return "FeaturedFilmModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", searchNum=" + searchNum +
                ", details='" + details + '\'' +
                ", isLove=" + isLove +
                ", playNum=" + playNum +
                ", totalChaptersNum=" + totalChaptersNum +
                '}';
    }
}
