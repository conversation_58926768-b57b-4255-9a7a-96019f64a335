package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.adapter.MostPopularAdapter;
import com.android.video.model.VideoModel;
import com.android.video.model.response.DailyRankDataModel;
import com.android.video.network.HomeApiService;
import com.android.video.ui.activity.VideoDetailActivity;
import com.android.video.config.EnvironmentConfigUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Most Popular查看所有页面
 * 显示30个热门视频排行榜
 * <AUTHOR>
 */
public class MostPopularActivity extends BaseFullScreenActivity {

    private static final String TAG = "MostPopularActivity";

    private ImageView ivBack;
    private TextView tvTitle;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView rvMostPopular;
    private MostPopularAdapter adapter;

    // API服务
    private HomeApiService homeApiService;

    // 分页相关
    private int currentPage = 1;
    private int pageSize = 10;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private List<VideoModel> allVideoList = new ArrayList<>();

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_most_popular);

        initViews();
        initApiService();
        setupRecyclerView();
        setupSwipeRefresh();
        loadData();
        setupClickListeners();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        rvMostPopular = findViewById(R.id.rv_most_popular);
    }

    /**
     * 初始化API服务
     */
    private void initApiService() {
        homeApiService = HomeApiService.getInstance();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        adapter = new MostPopularAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        rvMostPopular.setLayoutManager(layoutManager);
        rvMostPopular.setAdapter(adapter);

        // 设置点击监听
        adapter.setOnVideoClickListener(new MostPopularAdapter.OnVideoClickListener() {
            @Override
            public void onVideoClick(VideoModel video, int position) {
                // 跳转到视频详情页
                Intent intent = new Intent(MostPopularActivity.this, VideoDetailActivity.class);
                intent.putExtra("video_id", video.getId());
                intent.putExtra("video_title", video.getTitle());
                startActivity(intent);
            }
        });

        // 设置滚动监听，实现上拉加载更多
        rvMostPopular.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int pastVisibleItems = layoutManager.findFirstVisibleItemPosition();

                    if (!isLoading && hasMoreData &&
                        (visibleItemCount + pastVisibleItems) >= totalItemCount - 2) {
                        // 距离底部还有2个item时开始加载
                        loadMoreData();
                    }
                }
            }
        });
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        swipeRefreshLayout.setColorSchemeResources(
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        );

        swipeRefreshLayout.setOnRefreshListener(() -> {
            Log.d(TAG, "下拉刷新触发");
            refreshData();
        });
    }

    /**
     * 加载数据
     */
    private void loadData() {
        // 显示初始加载动画
        swipeRefreshLayout.setRefreshing(true);
        loadMostPopularData(false);
    }

    /**
     * 刷新数据
     */
    private void refreshData() {
        currentPage = 1;
        hasMoreData = true;
        allVideoList.clear();
        loadMostPopularData(false);
    }

    /**
     * 加载更多数据
     */
    private void loadMoreData() {
        if (!hasMoreData || isLoading) {
            return;
        }
        currentPage++;
        loadMostPopularData(true);
    }

    /**
     * 加载Most Popular数据
     * @param isLoadMore 是否为加载更多
     */
    private void loadMostPopularData(boolean isLoadMore) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;
        Log.d(TAG, "开始加载Most Popular数据 - 页码: " + currentPage + ", 加载更多: " + isLoadMore);

        homeApiService.getMostPopular(currentPage, pageSize, new HomeApiService.DailyRankCallback() {
            @Override
            public void onSuccess(DailyRankDataModel mostPopularData) {
                isLoading = false;
                swipeRefreshLayout.setRefreshing(false);

                Log.d(TAG, "Most Popular数据加载成功，数量: " + mostPopularData.getRecordCount());

                // 将DailyRankDataModel转换为VideoModel列表
                List<VideoModel> newVideoList = mostPopularData.toVideoModelList();

                if (isLoadMore) {
                    // 加载更多：追加到现有列表
                    allVideoList.addAll(newVideoList);
                } else {
                    // 刷新：替换整个列表
                    allVideoList.clear();
                    allVideoList.addAll(newVideoList);
                }

                // 更新适配器
                adapter.updateVideoList(new ArrayList<>(allVideoList));

                // 检查是否还有更多数据
                hasMoreData = mostPopularData.hasMorePages();

                Log.d(TAG, "✅ 更新Most Popular: " + allVideoList.size() + "个视频, 还有更多: " + hasMoreData);

                // 如果是第一页且数据为空，显示提示
                if (currentPage == 1 && allVideoList.isEmpty()) {
                    Toast.makeText(MostPopularActivity.this, "暂无热门视频数据", Toast.LENGTH_SHORT).show();
                }
            }

            @Override
            public void onError(String errorMessage) {
                isLoading = false;
                swipeRefreshLayout.setRefreshing(false);

                Log.e(TAG, "Most Popular数据加载失败: " + errorMessage);

                // 如果是第一页加载失败，使用测试数据作为后备
                if (currentPage == 1) {
                    Log.d(TAG, "第一页加载失败，使用测试数据作为后备");
                    List<VideoModel> testData = generateMostPopularData();
                    allVideoList.clear();
                    allVideoList.addAll(testData);
                    adapter.updateVideoList(new ArrayList<>(allVideoList));
                    hasMoreData = false; // 测试数据不支持分页
                }

                String toastMessage = EnvironmentConfigUtils.isDebugMode()
                    ? "Most Popular加载失败: " + errorMessage
                    : "数据加载失败" + (currentPage == 1 ? "，使用测试数据" : "");
                Toast.makeText(MostPopularActivity.this, toastMessage, Toast.LENGTH_LONG).show();
            }
        });
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());
    }

    /**
     * 生成Most Popular 30个项目的测试数据
     */
    private List<VideoModel> generateMostPopularData() {
        List<VideoModel> popularVideos = new ArrayList<>();

        String[] titles = {
            "热门系列：喜洋洋与灰太狼", "经典动画：功夫熊猫", "热播剧集：权力的游戏",
            "动作大片：速度与激情", "科幻巨制：星际穿越", "爱情电影：泰坦尼克号",
            "悬疑剧：神探夏洛克", "动画电影：冰雪奇缘", "战争片：拯救大兵瑞恩",
            "喜剧片：功夫", "奇幻冒险：指环王", "超级英雄：复仇者联盟",
            "经典武侠：射雕英雄传", "都市爱情：北京遇上西雅图", "青春校园：致我们终将逝去的青春",
            "历史剧：甄嬛传", "古装剧：琅琊榜", "现代都市：欢乐颂",
            "悬疑推理：白夜追凶", "科幻剧：三体", "动作冒险：碟中谍",
            "浪漫喜剧：我的野蛮女友", "惊悚片：寂静之地", "动画系列：海贼王",
            "经典老片：教父", "文艺片：肖申克的救赎", "纪录片：地球脉动",
            "音乐剧：歌剧魅影", "体育励志：摔跤吧爸爸", "家庭温情：寻梦环游记"
        };

        String[] categories = {
            "动画", "电影", "电视剧", "动作", "科幻", "爱情", "悬疑", "动画", "战争", "喜剧",
            "奇幻", "动作", "武侠", "爱情", "青春", "古装", "古装", "都市", "悬疑", "科幻",
            "动作", "喜剧", "惊悚", "动画", "经典", "剧情", "纪录", "音乐", "体育", "动画"
        };

        String[] descriptions = {
            "精彩的动画片，适合全家观看。讲述了喜羊羊和他的朋友们在青青草原上与灰太狼斗智斗勇的有趣故事，充满了欢声笑语和温馨感人的情节，是一部老少皆宜的优秀动画作品。",
            "经典功夫动画，幽默搞笑。熊猫阿宝从一个普通的面条店伙计成长为功夫大师的励志故事，融合了中国传统文化和现代动画技术，带来视觉和心灵的双重享受。",
            "史诗级奇幻剧集，权谋斗争。在维斯特洛大陆上，七大王国为了争夺铁王座而展开激烈的政治斗争，同时北方长城外的异鬼威胁也日益临近，是一部充满阴谋、背叛和英雄主义的史诗巨作。",
            "刺激的赛车动作片。多米尼克和他的团队驾驶着各种超级跑车，在世界各地执行危险任务，每一次任务都充满了高速追逐、爆炸场面和兄弟情谊，是肾上腺素飙升的动作大片。",
            "深度科幻电影，探索宇宙。在地球面临生存危机时，一群宇航员穿越虫洞寻找新的家园，影片探讨了时间、空间、爱情和人类命运等深刻主题，是一部兼具视觉震撼和哲学思考的科幻杰作。",
            "经典爱情故事，感人至深。杰克和露丝在泰坦尼克号上相遇相爱，虽然来自不同的社会阶层，但真爱超越了一切障碍，最终在灾难面前展现出人性的光辉和爱情的伟大。",
            "烧脑悬疑推理剧。天才侦探夏洛克·福尔摩斯运用超凡的观察力和推理能力，与忠实的伙伴华生医生一起破解各种看似不可能的案件，每个案件都充满了意想不到的转折和精彩的推理过程。",
            "迪士尼经典动画电影。艾莎公主拥有冰雪魔法，但因为无法控制而自我封闭，通过妹妹安娜的爱和支持，最终学会了接纳自己，影片传达了关于爱、勇气和自我接纳的美好主题。",
            "真实战争题材电影。二战期间，一支美军小队深入敌后营救被困的伞兵瑞恩，影片真实地展现了战争的残酷和士兵们的英勇，是一部反思战争与人性的经典之作。",
            "周星驰经典喜剧作品。小混混阿星意外获得如来神掌秘籍，从此踏上了功夫之路，影片将无厘头喜剧与功夫动作完美结合，创造了独特的喜剧风格和经典的银幕形象。",
            "中土世界的史诗冒险。霍比特人弗罗多承担起销毁魔戒的重任，与伙伴们踏上危险的旅程，面对黑暗势力的威胁，展现了友谊、勇气和牺牲精神的伟大力量。",
            "超级英雄集结拯救世界。钢铁侠、美国队长、雷神、绿巨人等超级英雄联手对抗外星入侵，在纽约展开史诗般的战斗，保卫地球和人类的安全，是漫威宇宙的里程碑之作。",
            "金庸武侠经典改编。郭靖从蒙古草原的憨厚少年成长为一代大侠，与黄蓉的爱情故事贯穿始终，江湖恩怨、家国情怀交织在一起，展现了武侠世界的侠义精神。",
            "跨国恋情浪漫故事。来自不同文化背景的男女主角在异国他乡相遇相爱，克服了语言、文化和距离的障碍，最终收获了美好的爱情，是一部温馨浪漫的都市爱情片。",
            "青春回忆杀，感动无数人。大学时代的美好回忆、纯真的友谊和初恋的甜蜜，随着时间的流逝而变得珍贵，影片唤起了观众对青春岁月的怀念和对美好时光的珍惜。",
            "宫斗剧巅峰之作。甄嬛从一个单纯的少女逐渐成长为深谙宫廷斗争的皇贵妃，在后宫的尔虞我诈中保护自己和所爱的人，展现了女性的智慧和坚强。",
            "权谋智斗，精彩绝伦。梅长苏以病弱之身回到金陵，运用超凡的智慧为昭雪冤案、扶持明君而精心布局，每一步都充满了智慧的较量和情感的纠葛。",
            "都市女性成长故事。五个性格迥异的女性在上海这座繁华都市中追求事业和爱情，面对生活的挑战和选择，展现了现代女性的独立、坚强和成长历程。",
            "悬疑推理，扣人心弦。刑警队长关宏峰为了洗清弟弟的嫌疑，与神秘顾问周巡联手破案，在黑夜中寻找真相，每个案件都充满了悬念和反转。",
            "硬科幻巨作，思辨深刻。面对三体文明的入侵威胁，人类文明展开了一场跨越时空的生存之战，作品探讨了文明、科技、人性等深刻主题，是中国科幻文学的里程碑。",
            "特工动作，惊险刺激。伊森·亨特和他的IMF小队接受各种不可能完成的任务，运用高科技装备和精湛技能，在全球各地展开惊险刺激的特工行动。",
            "韩式浪漫喜剧经典。性格迥异的男女主角从最初的误会和冲突，到逐渐了解和相爱，充满了搞笑的情节和温馨的浪漫，是韩国爱情电影的代表作品。",
            "无声恐怖，紧张刺激。在一个被怪物入侵的世界里，一家人必须保持绝对的安静才能生存，影片通过声音的巧妙运用营造出极度紧张的氛围和恐怖感。",
            "热血冒险，友情至上。路飞和他的草帽海贼团在伟大航路上追寻传说中的大秘宝，每一次冒险都充满了战斗、友谊和梦想，是一部永不落幕的热血传奇。",
            "黑帮电影教科书。维托·柯里昂从一个普通移民成长为黑手党教父，家族的兴衰荣辱与个人的命运紧密相连，展现了权力、家族和忠诚的复杂关系。",
            "人性救赎，经典永恒。安迪在肖申克监狱中用希望和坚持战胜了绝望，最终获得了自由和救赎，影片深刻地探讨了希望、友谊和人性的力量。",
            "自然纪录片巅峰。通过最先进的摄影技术，展现了地球上各种生物的生存状态和自然环境的壮美，让观众感受到大自然的神奇和生命的珍贵。",
            "音乐剧经典之作。在巴黎歌剧院的地下，神秘的魅影与美丽的歌手克里斯汀之间展开了一段充满激情和悲剧色彩的爱情故事，音乐优美动人，情节扣人心弦。",
            "父爱如山，励志感人。一位父亲为了女儿的摔跤梦想，克服了社会偏见和重重困难，最终培养出了世界冠军，展现了父爱的伟大和梦想的力量。",
            "亲情温暖，梦想追求。小男孩米格尔为了追寻音乐梦想，意外进入了亡灵世界，在那里他了解了家族的历史和亲情的珍贵，最终实现了梦想与亲情的和谐统一。"
        };

        // 播放次数（代表热度，从高到低）
        long[] viewCounts = {
            8500000L, 8200000L, 7900000L, 7600000L, 7300000L, 7000000L, 6700000L, 6400000L, 6100000L, 5800000L,
            5500000L, 5200000L, 4900000L, 4600000L, 4300000L, 4000000L, 3700000L, 3400000L, 3100000L, 2800000L,
            2500000L, 2200000L, 1900000L, 1600000L, 1300000L, 1000000L, 800000L, 600000L, 400000L, 200000L
        };

        for (int i = 0; i < 30; i++) {
            VideoModel video = new VideoModel(
                "most_popular_" + (i + 1),
                titles[i],
                "",
                categories[i]
            );

            // 设置描述
            video.setDescription(descriptions[i]);
            // 设置播放次数（热度值）
            video.setViewCount(viewCounts[i]);
            // 设置排名
            video.setRanking(i + 1);

            popularVideos.add(video);
        }

        // 按播放次数从高到低排序
        Collections.sort(popularVideos, new Comparator<VideoModel>() {
            @Override
            public int compare(VideoModel v1, VideoModel v2) {
                return Long.compare(v2.getViewCount(), v1.getViewCount());
            }
        });

        return popularVideos;
    }
}
