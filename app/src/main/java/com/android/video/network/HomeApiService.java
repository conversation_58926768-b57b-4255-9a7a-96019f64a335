package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.model.BannerModel;
import com.android.video.model.FeaturedModel;
import com.android.video.model.SearchResultModel;
import com.android.video.model.response.BannerListResponseModel;
import com.android.video.model.response.CategoryModel;
import com.android.video.model.response.CategoryWithFilmsModel;
import com.android.video.model.response.CategoryFilmModel;
import com.android.video.model.response.CategoryListResponseModel;
import com.android.video.model.response.FeaturedListResponseModel;
import com.android.video.model.DailyRankModel;
import com.android.video.model.response.DailyRankDataModel;
import com.android.video.model.WorthWaitingModel;
import com.android.video.model.response.WorthWaitingDataModel;
import com.android.video.model.GuessYouLikeModel;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 首页模块API服务类
 * <p>
 * 提供首页相关的网络请求功能，包括搜索短剧、获取banner、分类等。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils
 * @see SearchResultModel
 */
public class HomeApiService {

    private static final String TAG = "HomeApiService";

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    // 单例实例
    private static volatile HomeApiService instance;

    /**
     * 搜索结果回调接口
     */
    public interface SearchCallback {
        /**
         * 搜索成功
         * @param searchResults 搜索结果列表
         * @param total 总记录数
         * @param pages 总页数
         */
        void onSuccess(List<SearchResultModel> searchResults, int total, int pages);

        /**
         * 搜索失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * Banner列表回调接口
     */
    public interface BannerCallback {
        /**
         * 获取Banner列表成功
         * @param banners Banner列表
         */
        void onSuccess(List<BannerModel> banners);

        /**
         * 获取Banner列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 推荐位列表回调接口
     */
    public interface FeaturedCallback {
        /**
         * 获取推荐位列表成功
         * @param featuredList 推荐位列表
         */
        void onSuccess(List<FeaturedModel> featuredList);

        /**
         * 获取推荐位列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 搜索历史回调接口
     */
    public interface SearchHistoryCallback {
        /**
         * 获取搜索历史成功
         * @param searchHistory 搜索历史列表
         */
        void onSuccess(List<String> searchHistory);

        /**
         * 获取搜索历史失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造方法
     */
    private HomeApiService() {
        // 使用ApiClientUtils提供的配置好的客户端（包含缓存支持）
        this.client = ApiClientUtils.getHttpClient();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
        this.gson = new Gson();

        Log.d(TAG, "HomeApiService initialized with cached HTTP client");
    }

    /**
     * 获取单例实例
     * @return HomeApiService实例
     */
    public static HomeApiService getInstance() {
        if (instance == null) {
            synchronized (HomeApiService.class) {
                if (instance == null) {
                    instance = new HomeApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 搜索短剧
     * <p>
     * 根据关键词搜索短剧内容，支持分页查询。
     * </p>
     *
     * @param searchWord 搜索关键词
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @param callback 回调接口
     */
    public void searchVideos(String searchWord, int pageNum, int pageSize, SearchCallback callback) {
        // 参数验证
        if (!HomeApiConstantsUtils.isValidSearchWord(searchWord)) {
            if (callback != null) {
                callback.onError("搜索关键词无效");
            }
            return;
        }

        if (pageNum < 1) pageNum = 1;
        if (pageSize < 1 || pageSize > 50) pageSize = 10;

        // 构建搜索URL
        String searchUrl = HomeApiConstantsUtils.buildSearchUrl(EnvironmentConfigUtils.getApiBaseUrl(), searchWord, pageNum, pageSize);

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(searchUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 搜索短剧请求 ===");
        Log.d(TAG, "搜索关键词: " + searchWord);
        Log.d(TAG, "页码: " + pageNum);
        Log.d(TAG, "每页数量: " + pageSize);
        Log.d(TAG, "请求URL: " + searchUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "搜索短剧请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 搜索短剧API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseSearchResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (搜索接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (搜索参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "搜索短剧响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析搜索API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseSearchResponse(String responseBody, SearchCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "搜索API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "搜索失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取data字段
            JSONObject dataObject = jsonResponse.optJSONObject(BaseApiConstantsUtils.FIELD_DATA);
            if (dataObject == null) {
                Log.e(TAG, "搜索响应中没有data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误");
                    }
                });
                return;
            }

            // 解析搜索结果
            List<SearchResultModel> searchResults = new ArrayList<>();
            JSONArray recordsArray = dataObject.optJSONArray(HomeApiConstantsUtils.FIELD_RECORDS);
            
            if (recordsArray != null) {
                for (int i = 0; i < recordsArray.length(); i++) {
                    JSONObject recordObject = recordsArray.getJSONObject(i);
                    SearchResultModel searchResult = parseSearchResultItem(recordObject);
                    if (searchResult != null) {
                        searchResults.add(searchResult);
                    }
                }
            }

            // 获取分页信息
            int total = dataObject.optInt(HomeApiConstantsUtils.FIELD_TOTAL, 0);
            int pages = dataObject.optInt(HomeApiConstantsUtils.FIELD_PAGES, 0);

            Log.d(TAG, "搜索解析成功: " + searchResults.size() + " 条结果, 总计: " + total + " 条, 总页数: " + pages);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(searchResults, total, pages);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "解析搜索响应失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 解析单个搜索结果项
     *
     * @param recordObject 搜索结果JSON对象
     * @return SearchResultModel对象，解析失败时返回null
     */
    private SearchResultModel parseSearchResultItem(JSONObject recordObject) {
        try {
            String filmTitle = recordObject.optString(HomeApiConstantsUtils.FIELD_FILM_TITLE, "");
            String filmId = recordObject.optString(HomeApiConstantsUtils.FIELD_FILM_ID, "");
            String filmLanguageInfoId = recordObject.optString(HomeApiConstantsUtils.FIELD_FILM_LANGUAGE_INFO_ID, "");
            String cover = recordObject.optString(HomeApiConstantsUtils.FIELD_COVER, "");
            String categoryId = recordObject.optString(HomeApiConstantsUtils.FIELD_CATEGORY_ID, "");
            String categoryName = recordObject.optString(HomeApiConstantsUtils.FIELD_CATEGORY_NAME, "");
            int languageType = recordObject.optInt(HomeApiConstantsUtils.PARAM_LANGUAGE_TYPE, 0);

            // 创建SearchResultModel对象
            SearchResultModel searchResult = new SearchResultModel();
            searchResult.setTitle(filmTitle);
            searchResult.setFilmId(filmId);
            searchResult.setFilmLanguageInfoId(filmLanguageInfoId);
            searchResult.setPosterUrl(cover);
            searchResult.setCategoryId(categoryId);
            searchResult.setCategoryName(categoryName);
            searchResult.setLanguageType(languageType);

            return searchResult;

        } catch (Exception e) {
            Log.e(TAG, "解析搜索结果项失败", e);
            return null;
        }
    }

    /**
     * 获取搜索历史
     * <p>
     * 异步获取用户的搜索历史记录。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getSearchHistory(SearchHistoryCallback callback) {
        Log.d(TAG, "getSearchHistory called");

        if (callback == null) {
            Log.e(TAG, "SearchHistoryCallback is null");
            return;
        }

        // 开发环境也调用真实API，不再使用模拟数据

        // 构建搜索历史URL
        String searchHistoryUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_SEARCH_HISTORY;

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(searchHistoryUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取搜索历史请求 ===");
        Log.d(TAG, "请求URL: " + searchHistoryUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取搜索历史请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 搜索历史API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseSearchHistoryResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "搜索历史响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析搜索历史API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseSearchHistoryResponse(String responseBody, SearchHistoryCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "搜索历史API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "获取搜索历史失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取data字段（应该是字符串数组）
            JSONArray dataArray = jsonResponse.optJSONArray(BaseApiConstantsUtils.FIELD_DATA);
            if (dataArray == null) {
                Log.e(TAG, "搜索历史响应中没有data字段或data不是数组");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误");
                    }
                });
                return;
            }

            // 解析搜索历史列表
            List<String> searchHistory = new ArrayList<>();
            for (int i = 0; i < dataArray.length(); i++) {
                String historyItem = dataArray.optString(i);
                if (historyItem != null && !historyItem.trim().isEmpty()) {
                    searchHistory.add(historyItem);
                }
            }

            Log.d(TAG, "搜索历史解析成功，共 " + searchHistory.size() + " 条记录");

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(searchHistory);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "搜索历史JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 清除搜索历史
     * <p>
     * 异步清除用户的搜索历史记录。
     * </p>
     *
     * @param callback 回调接口
     */
    public void clearSearchHistory(ClearSearchHistoryCallback callback) {
        Log.d(TAG, "clearSearchHistory called");

        if (callback == null) {
            Log.e(TAG, "ClearSearchHistoryCallback is null");
            return;
        }

        // 构建清除搜索历史URL
        String clearHistoryUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_CLEAR_SEARCH_HISTORY;

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建DELETE请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(clearHistoryUrl)
            .delete();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 清除搜索历史请求 ===");
        Log.d(TAG, "请求URL: " + clearHistoryUrl);
        Log.d(TAG, "请求方法: DELETE");
        Log.d(TAG, "请求头数量: " + headers.size());
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "清除搜索历史请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 清除搜索历史API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseClearSearchHistoryResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "清除搜索历史响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析清除搜索历史API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseClearSearchHistoryResponse(String responseBody, ClearSearchHistoryCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "清除搜索历史API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "清除搜索历史失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取data字段（清除成功的消息）
            String data = jsonResponse.optString(BaseApiConstantsUtils.FIELD_DATA, "搜索历史已清除");

            Log.d(TAG, "清除搜索历史成功: " + data);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(data);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "清除搜索历史JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 获取Banner列表
     * <p>
     * 根据位置参数获取Banner轮播图列表。
     * </p>
     *
     * @param location 位置参数 (1=首页, 2=分类详情)
     * @param callback 回调接口
     */
    public void getBanners(int location, BannerCallback callback) {
        Log.d(TAG, "getBanners called with location: " + location);

        if (callback == null) {
            Log.e(TAG, "BannerCallback is null");
            return;
        }

        // 参数验证
        if (!HomeApiConstantsUtils.isValidLocation(location)) {
            callback.onError("无效的位置参数: " + location);
            return;
        }

        // 构建Banner API URL
        String bannerUrl = EnvironmentConfigUtils.getApiBaseUrl() +
                          HomeApiConstantsUtils.API_GET_BANNERS +
                          "?" + HomeApiConstantsUtils.PARAM_LOCATION + "=" + location;

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(bannerUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "发起Banner请求: " + bannerUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 发起异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);

                // 在主线程中回调
                mainHandler.post(() -> callback.onError(errorMsg));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "Banner响应: " + responseBody);

                        // 解析JSON响应
                        BannerListResponseModel responseModel = gson.fromJson(responseBody, BannerListResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            final List<BannerModel> banners = responseModel.getData() != null
                                                             ? responseModel.getData()
                                                             : new ArrayList<>();

                            // 在主线程中回调
                            mainHandler.post(() -> callback.onSuccess(banners));
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            mainHandler.post(() -> callback.onError(errorMsg));
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        mainHandler.post(() -> callback.onError(errorMsg));
                    }
                } catch (Exception e) {
                    String errorMsg = "响应解析失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    mainHandler.post(() -> callback.onError(errorMsg));
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 获取推荐位列表
     * <p>
     * 获取首页推荐位的内容列表，包括Categories、Coming Soon、Popular series等。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getFeatured(FeaturedCallback callback) {
        Log.d(TAG, "getFeatured called");

        if (callback == null) {
            Log.e(TAG, "FeaturedCallback is null");
            return;
        }

        // 构建Featured API URL
        String featuredUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_FEATURED;

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(featuredUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "发起Featured请求: " + featuredUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 发起异步请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);

                // 在主线程中回调
                mainHandler.post(() -> callback.onError(errorMsg));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "Featured响应: " + responseBody);

                        // 解析JSON响应
                        FeaturedListResponseModel responseModel = gson.fromJson(responseBody, FeaturedListResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            final List<FeaturedModel> featuredList = responseModel.getData() != null
                                                                   ? responseModel.getData()
                                                                   : new ArrayList<>();

                            // 在主线程中回调
                            mainHandler.post(() -> callback.onSuccess(featuredList));
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            mainHandler.post(() -> callback.onError(errorMsg));
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        mainHandler.post(() -> callback.onError(errorMsg));
                    }
                } catch (Exception e) {
                    String errorMsg = "响应解析失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    mainHandler.post(() -> callback.onError(errorMsg));
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 获取分类标签列表
     * <p>
     * 获取所有分类标签的列表信息，包括分类ID、名称、权重、语言等。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getCategoryList(CategoryListCallback callback) {
        Log.d(TAG, "getCategoryList called");

        if (callback == null) {
            Log.e(TAG, "CategoryListCallback is null");
            return;
        }

        // 构建分类标签列表API URL
        String categoryListUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_CATEGORY_LIST;

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(categoryListUrl)
            .get();

        Log.d(TAG, "请求分类列表: " + categoryListUrl);

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取分类标签列表请求 ===");
        Log.d(TAG, "请求URL: " + categoryListUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取分类标签列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 分类标签列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseCategoryListResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (分类标签接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "分类标签列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析分类标签列表API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseCategoryListResponse(String responseBody, CategoryListCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "分类标签列表API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "获取分类标签列表失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取data字段（应该是数组）
            JSONArray dataArray = jsonResponse.optJSONArray(BaseApiConstantsUtils.FIELD_DATA);
            if (dataArray == null) {
                Log.e(TAG, "分类标签列表响应中没有data字段或data不是数组");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误");
                    }
                });
                return;
            }

            // 解析分类标签列表
            List<CategoryModel> categories = new ArrayList<>();
            for (int i = 0; i < dataArray.length(); i++) {
                JSONObject categoryObject = dataArray.getJSONObject(i);
                CategoryModel category = parseCategoryItem(categoryObject);
                if (category != null) {
                    categories.add(category);
                }
            }

            Log.d(TAG, "分类标签列表解析成功，共 " + categories.size() + " 个分类");

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(categories);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "分类标签列表JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 解析单个分类标签项
     *
     * @param categoryObject 分类标签JSON对象
     * @return CategoryModel对象，解析失败时返回null
     */
    private CategoryModel parseCategoryItem(JSONObject categoryObject) {
        try {
            String categoryId = categoryObject.optString(HomeApiConstantsUtils.FIELD_CATEGORY_ID, "");
            String categoryName = categoryObject.optString(HomeApiConstantsUtils.FIELD_CATEGORY_NAME, "");
            int weight = categoryObject.optInt(HomeApiConstantsUtils.FIELD_WEIGHT, 0);
            String language = categoryObject.optString(HomeApiConstantsUtils.FIELD_LANGUAGE, "");
            int isDelete = categoryObject.optInt(HomeApiConstantsUtils.FIELD_IS_DELETE, 0);
            String createTime = categoryObject.optString(HomeApiConstantsUtils.FIELD_CREATE_TIME, "");
            String updateTime = categoryObject.optString(HomeApiConstantsUtils.FIELD_UPDATE_TIME, "");

            // 创建CategoryModel对象
            CategoryModel category = new CategoryModel();
            category.setCategoryId(categoryId);
            category.setCategoryName(categoryName);
            category.setWeight(weight);
            category.setLanguage(language);
            category.setIsDelete(isDelete);
            category.setCreateTime(createTime);
            category.setUpdateTime(updateTime);

            return category;

        } catch (Exception e) {
            Log.e(TAG, "解析分类标签项失败", e);
            return null;
        }
    }

    /**
     * 获取分类短剧列表（支持分页）
     * <p>
     * 获取分类信息以及对应的短剧内容列表，支持分页查询。
     * </p>
     *
     * @param categoryId 分类ID，为空或null时查询所有分类
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param callback 回调接口
     */
    public void getCategoriesWithFilms(String categoryId, int page, int size, CategoryWithFilmsCallback callback) {
        Log.d(TAG, "getCategoriesWithFilms called - categoryId: " + categoryId + ", page: " + page + ", size: " + size);

        if (callback == null) {
            Log.e(TAG, "CategoryWithFilmsCallback is null");
            return;
        }

        // 参数验证
        if (page < 1) page = 1;
        if (size < 1 || size > 50) size = 10;

        // 构建分类短剧列表API URL
        String categoriesUrl = buildCategoriesWithFilmsUrl(categoryId, page, size);

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建GET请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(categoriesUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取分类短剧列表请求 ===");
        Log.d(TAG, "请求URL: " + categoriesUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "分类ID: " + (categoryId != null ? categoryId : "ALL"));
        Log.d(TAG, "页码: " + page);
        Log.d(TAG, "每页数量: " + size);
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取分类短剧列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 分类短剧列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseCategoriesWithFilmsResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (分类短剧接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "分类短剧列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 获取今日热门排行榜
     * <p>
     * 获取今日热门短剧排行榜列表，支持分页查询。
     * </p>
     *
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param callback 回调接口
     */
    public void getDailyRank(int page, int size, DailyRankCallback callback) {
        Log.d(TAG, "getDailyRank called with page: " + page + ", size: " + size);

        if (callback == null) {
            Log.e(TAG, "DailyRankCallback is null");
            return;
        }

        // 参数验证
        if (page < 1) page = 1;
        if (size < 1 || size > 50) size = 10;

        // 构建今日热门API URL
        String dailyRankUrl = buildDailyRankUrl(page, size);

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(dailyRankUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 今日热门API请求 ===");
        Log.d(TAG, "请求URL: " + dailyRankUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "今日热门API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 今日热门API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDailyRankResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (今日热门接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "今日热门响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 构建今日热门API URL
     *
     * @param page 页码
     * @param size 每页数量
     * @return 完整的API URL
     */
    private String buildDailyRankUrl(int page, int size) {
        String baseUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_DAILY_RANK;

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");

        // 添加page参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_PAGE).append("=").append(page).append("&");

        // 添加size参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_SIZE).append("=").append(size);

        return urlBuilder.toString();
    }

    /**
     * 获取即将来袭推荐位
     * <p>
     * 获取即将来袭(Coming Soon)推荐位列表，支持分页查询。
     * </p>
     *
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param callback 回调接口
     */
    public void getWorthWaiting(int page, int size, WorthWaitingCallback callback) {
        Log.d(TAG, "getWorthWaiting called with page: " + page + ", size: " + size);

        if (callback == null) {
            Log.e(TAG, "WorthWaitingCallback is null");
            return;
        }

        // 参数验证
        if (page < 1) page = 1;
        if (size < 1 || size > 50) size = 10;

        // 构建即将来袭API URL
        String worthWaitingUrl = buildWorthWaitingUrl(page, size);

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(worthWaitingUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 即将来袭API请求 ===");
        Log.d(TAG, "请求URL: " + worthWaitingUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "即将来袭API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 即将来袭API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseWorthWaitingResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (即将来袭接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "即将来袭响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 构建即将来袭API URL
     *
     * @param page 页码
     * @param size 每页数量
     * @return 完整的API URL
     */
    private String buildWorthWaitingUrl(int page, int size) {
        String baseUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_WORTH_WAITING;

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");

        // 添加page参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_PAGE).append("=").append(page).append("&");

        // 添加size参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_SIZE).append("=").append(size);

        return urlBuilder.toString();
    }

    /**
     * 构建分类短剧列表API URL
     *
     * @param categoryId 分类ID
     * @param page 页码
     * @param size 每页数量
     * @return 完整的API URL
     */
    private String buildCategoriesWithFilmsUrl(String categoryId, int page, int size) {
        String baseUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_CATEGORIES;

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");

        // 添加categoryId参数（可选）
        if (categoryId != null && !categoryId.trim().isEmpty()) {
            urlBuilder.append(HomeApiConstantsUtils.PARAM_CATEGORY_ID).append("=").append(categoryId).append("&");
        } else {
            urlBuilder.append(HomeApiConstantsUtils.PARAM_CATEGORY_ID).append("=&");
        }

        // 添加分页参数
        urlBuilder.append(HomeApiConstantsUtils.PARAM_PAGE).append("=").append(page).append("&");
        urlBuilder.append(HomeApiConstantsUtils.PARAM_SIZE).append("=").append(size);

        return urlBuilder.toString();
    }

    /**
     * 解析今日热门API响应
     *
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDailyRankResponse(String responseBody, DailyRankCallback callback) {
        try {
            Log.d(TAG, "开始解析今日热门响应数据");

            // 解析基础响应结构
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应码
            String code = jsonResponse.has(BaseApiConstantsUtils.FIELD_CODE) ?
                    jsonResponse.get(BaseApiConstantsUtils.FIELD_CODE).getAsString() : "";

            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                String message = jsonResponse.has(BaseApiConstantsUtils.FIELD_MESSAGE) ?
                        jsonResponse.get(BaseApiConstantsUtils.FIELD_MESSAGE).getAsString() : "未知错误";

                Log.e(TAG, "今日热门API返回错误: code=" + code + ", message=" + message);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("API错误: " + message + " (code: " + code + ")");
                    }
                });
                return;
            }

            // 解析data字段
            if (!jsonResponse.has(BaseApiConstantsUtils.FIELD_DATA)) {
                Log.e(TAG, "今日热门响应缺少data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误：缺少data字段");
                    }
                });
                return;
            }

            JsonObject dataObject = jsonResponse.getAsJsonObject(BaseApiConstantsUtils.FIELD_DATA);

            // 使用Gson解析DailyRankDataModel
            DailyRankDataModel dailyRankData = gson.fromJson(dataObject, DailyRankDataModel.class);

            if (dailyRankData == null) {
                Log.e(TAG, "今日热门数据解析失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
                return;
            }

            Log.d(TAG, "今日热门数据解析成功: " + dailyRankData.toString());

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(dailyRankData);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "今日热门响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析异常: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 解析即将来袭API响应
     *
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseWorthWaitingResponse(String responseBody, WorthWaitingCallback callback) {
        try {
            Log.d(TAG, "开始解析即将来袭响应数据");

            // 解析基础响应结构
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应码
            String code = jsonResponse.has(BaseApiConstantsUtils.FIELD_CODE) ?
                    jsonResponse.get(BaseApiConstantsUtils.FIELD_CODE).getAsString() : "";

            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                String message = jsonResponse.has(BaseApiConstantsUtils.FIELD_MESSAGE) ?
                        jsonResponse.get(BaseApiConstantsUtils.FIELD_MESSAGE).getAsString() : "未知错误";

                Log.e(TAG, "即将来袭API返回错误: code=" + code + ", message=" + message);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("API错误: " + message + " (code: " + code + ")");
                    }
                });
                return;
            }

            // 解析data字段
            if (!jsonResponse.has(BaseApiConstantsUtils.FIELD_DATA)) {
                Log.e(TAG, "即将来袭响应缺少data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误：缺少data字段");
                    }
                });
                return;
            }

            JsonObject dataObject = jsonResponse.getAsJsonObject(BaseApiConstantsUtils.FIELD_DATA);

            // 使用Gson解析WorthWaitingDataModel
            WorthWaitingDataModel worthWaitingData = gson.fromJson(dataObject, WorthWaitingDataModel.class);

            if (worthWaitingData == null) {
                Log.e(TAG, "即将来袭数据解析失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
                return;
            }

            Log.d(TAG, "即将来袭数据解析成功: " + worthWaitingData.toString());

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(worthWaitingData);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "即将来袭响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析异常: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 解析分类短剧列表API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseCategoriesWithFilmsResponse(String responseBody, CategoryWithFilmsCallback callback) {
        try {
            // 使用Gson解析响应
            CategoryListResponseModel response = gson.fromJson(responseBody, CategoryListResponseModel.class);

            if (response == null) {
                Log.e(TAG, "分类短剧列表响应解析失败：响应为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据解析失败");
                    }
                });
                return;
            }

            if (!response.isSuccess()) {
                Log.e(TAG, "分类短剧列表API返回错误码: " + response.getCode());
                String message = response.getMessage() != null ? response.getMessage() : "获取分类短剧列表失败";
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            Log.d(TAG, "分类短剧列表解析成功");
            if (response.hasData()) {
                Log.d(TAG, "总记录数: " + response.getTotal());
                Log.d(TAG, "当前页: " + response.getCurrentPage());
                Log.d(TAG, "总页数: " + response.getTotalPages());
                Log.d(TAG, "分类数量: " + response.getCategories().size());
            }

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(response);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "分类短剧列表JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 分类标签列表回调接口
     */
    public interface CategoryListCallback {
        /**
         * 获取分类标签列表成功
         * @param categories 分类标签列表
         */
        void onSuccess(List<CategoryModel> categories);

        /**
         * 获取分类标签列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 分类短剧列表回调接口
     */
    public interface CategoryWithFilmsCallback {
        /**
         * 获取分类短剧列表成功
         * @param response 分类短剧列表响应数据
         */
        void onSuccess(CategoryListResponseModel response);

        /**
         * 获取分类短剧列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 清除搜索历史回调接口
     */
    public interface ClearSearchHistoryCallback {
        void onSuccess(String message);
        void onError(String errorMessage);
    }

    /**
     * 今日热门排行榜回调接口
     * 注意：此接口也用于最受欢迎列表，因为数据结构相同
     */
    public interface DailyRankCallback {
        /**
         * 获取今日热门排行榜成功
         * @param dailyRankData 今日热门排行榜数据
         */
        void onSuccess(DailyRankDataModel dailyRankData);

        /**
         * 获取今日热门排行榜失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 即将来袭推荐位回调接口
     */
    public interface WorthWaitingCallback {
        /**
         * 获取即将来袭推荐位成功
         * @param worthWaitingData 即将来袭推荐位数据
         */
        void onSuccess(WorthWaitingDataModel worthWaitingData);

        /**
         * 获取即将来袭推荐位失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 猜你喜欢推荐位回调接口
     */
    public interface GuessYouLikeCallback {
        /**
         * 获取猜你喜欢推荐位成功
         * @param guessYouLikeList 猜你喜欢推荐位数据列表
         */
        void onSuccess(List<GuessYouLikeModel> guessYouLikeList);

        /**
         * 获取猜你喜欢推荐位失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 热门搜索回调接口
     */
    public interface TrendingSearchesCallback {
        /**
         * 获取热门搜索成功
         * @param trendingSearches 热门搜索列表
         */
        void onSuccess(List<SearchResultModel> trendingSearches);

        /**
         * 获取热门搜索失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    // ========== 最受欢迎列表API方法 ==========

    /**
     * 获取最受欢迎列表
     * <p>
     * 获取最受欢迎短剧列表，支持分页查询。
     * </p>
     *
     * @param page 页码，从1开始
     * @param size 每页数量，默认10条
     * @param callback 回调接口
     */
    public void getMostPopular(int page, int size, DailyRankCallback callback) {
        Log.d(TAG, "getMostPopular called with page: " + page + ", size: " + size);

        if (callback == null) {
            Log.e(TAG, "DailyRankCallback is null");
            return;
        }

        // 参数验证
        if (page < 1) page = 1;
        if (size < 1 || size > 50) size = 10;

        // 构建最受欢迎API URL
        String mostPopularUrl = buildMostPopularUrl(page, size);

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(mostPopularUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 最受欢迎API请求 ===");
        Log.d(TAG, "请求URL: " + mostPopularUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "最受欢迎API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 最受欢迎API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据，复用今日热门的解析方法
                            parseDailyRankResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (最受欢迎接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "最受欢迎响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 构建最受欢迎API URL
     *
     * @param page 页码
     * @param size 每页数量
     * @return 完整的API URL
     */
    private String buildMostPopularUrl(int page, int size) {
        String baseUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_MOST_POPULAR;

        StringBuilder urlBuilder = new StringBuilder(baseUrl);
        urlBuilder.append("?");

        // 添加page参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_PAGE).append("=").append(page).append("&");

        // 添加size参数（必填）
        urlBuilder.append(HomeApiConstantsUtils.PARAM_SIZE).append("=").append(size);

        return urlBuilder.toString();
    }

    // ========== 猜你喜欢推荐位API方法 ==========

    /**
     * 获取猜你喜欢推荐位
     * <p>
     * 获取猜你喜欢(Best For You)推荐位列表，基于用户偏好推荐。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getGuessYouLike(GuessYouLikeCallback callback) {
        Log.d(TAG, "getGuessYouLike called");

        if (callback == null) {
            Log.e(TAG, "GuessYouLikeCallback is null");
            return;
        }

        // 构建猜你喜欢API URL
        String guessYouLikeUrl = buildGuessYouLikeUrl();

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建GET请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(guessYouLikeUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 猜你喜欢API请求 ===");
        Log.d(TAG, "请求URL: " + guessYouLikeUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "请求头: " + headers);
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "猜你喜欢API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 猜你喜欢API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseGuessYouLikeResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (猜你喜欢接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "猜你喜欢响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 构建猜你喜欢API URL
     *
     * @return 完整的API URL
     */
    private String buildGuessYouLikeUrl() {
        return EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_GUESS_YOU_LIKE;
    }

    /**
     * 解析猜你喜欢API响应
     *
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseGuessYouLikeResponse(String responseBody, GuessYouLikeCallback callback) {
        try {
            Log.d(TAG, "开始解析猜你喜欢响应数据");

            // 解析基础响应结构
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应码
            String code = jsonResponse.has(BaseApiConstantsUtils.FIELD_CODE) ?
                    jsonResponse.get(BaseApiConstantsUtils.FIELD_CODE).getAsString() : "";

            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                String message = jsonResponse.has(BaseApiConstantsUtils.FIELD_MESSAGE) ?
                        jsonResponse.get(BaseApiConstantsUtils.FIELD_MESSAGE).getAsString() : "未知错误";

                Log.e(TAG, "猜你喜欢API返回错误: code=" + code + ", message=" + message);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("API错误: " + message + " (code: " + code + ")");
                    }
                });
                return;
            }

            // 解析data字段
            if (!jsonResponse.has(BaseApiConstantsUtils.FIELD_DATA)) {
                Log.e(TAG, "猜你喜欢响应缺少data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误：缺少data字段");
                    }
                });
                return;
            }

            JsonArray dataArray = jsonResponse.getAsJsonArray(BaseApiConstantsUtils.FIELD_DATA);

            // 使用Gson解析GuessYouLikeModel列表
            List<GuessYouLikeModel> guessYouLikeList = new ArrayList<>();
            for (int i = 0; i < dataArray.size(); i++) {
                JsonElement element = dataArray.get(i);
                GuessYouLikeModel model = gson.fromJson(element, GuessYouLikeModel.class);
                if (model != null) {
                    guessYouLikeList.add(model);
                }
            }

            Log.d(TAG, "猜你喜欢数据解析成功: " + guessYouLikeList.size() + " 个项目");

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(guessYouLikeList);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "猜你喜欢响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析异常: " + e.getMessage());
                }
            });
        }
    }

    // ========== 热门搜索API方法 ==========

    /**
     * 获取热门搜索列表
     * <p>
     * 获取用户搜索频率最高的短剧内容列表。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getTrendingSearches(TrendingSearchesCallback callback) {
        Log.d(TAG, "getTrendingSearches called");

        if (callback == null) {
            Log.e(TAG, "TrendingSearchesCallback is null");
            return;
        }

        // 构建热门搜索API URL
        String trendingSearchesUrl = EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_TRENDING_SEARCHES;

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建GET请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(trendingSearchesUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 热门搜索API请求 ===");
        Log.d(TAG, "请求URL: " + trendingSearchesUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "请求头: " + headers);
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "热门搜索API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 热门搜索API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseTrendingSearchesResponse(responseBody, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (热门搜索接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "热门搜索响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析热门搜索API响应
     *
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseTrendingSearchesResponse(String responseBody, TrendingSearchesCallback callback) {
        try {
            Log.d(TAG, "开始解析热门搜索响应数据");

            // 解析基础响应结构
            JsonObject jsonResponse = JsonParser.parseString(responseBody).getAsJsonObject();

            // 检查响应码
            String code = jsonResponse.has(BaseApiConstantsUtils.FIELD_CODE) ?
                    jsonResponse.get(BaseApiConstantsUtils.FIELD_CODE).getAsString() : "";

            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                String message = jsonResponse.has(BaseApiConstantsUtils.FIELD_MESSAGE) ?
                        jsonResponse.get(BaseApiConstantsUtils.FIELD_MESSAGE).getAsString() : "未知错误";

                Log.e(TAG, "热门搜索API返回错误: code=" + code + ", message=" + message);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("API错误: " + message + " (code: " + code + ")");
                    }
                });
                return;
            }

            // 解析data字段
            if (!jsonResponse.has(BaseApiConstantsUtils.FIELD_DATA)) {
                Log.e(TAG, "热门搜索响应缺少data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误：缺少data字段");
                    }
                });
                return;
            }

            JsonArray dataArray = jsonResponse.getAsJsonArray(BaseApiConstantsUtils.FIELD_DATA);

            // 解析热门搜索列表
            List<SearchResultModel> trendingSearches = new ArrayList<>();
            for (int i = 0; i < dataArray.size(); i++) {
                JsonObject item = dataArray.get(i).getAsJsonObject();

                SearchResultModel searchResult = new SearchResultModel();
                searchResult.setRank(i + 1); // 设置排名，从1开始

                // 映射API字段到模型字段
                if (item.has("filmTitle")) {
                    searchResult.setTitle(item.get("filmTitle").getAsString());
                }

                if (item.has("cover")) {
                    String coverUrl = item.get("cover").getAsString();
                    searchResult.setPosterUrl(coverUrl);
                }

                if (item.has("searchNum")) {
                    int searchNum = item.get("searchNum").getAsInt();
                    searchResult.setSearchCount(String.valueOf(searchNum));
                }

                if (item.has("filmId")) {
                    searchResult.setFilmId(item.get("filmId").getAsString());
                }

                if (item.has("filmLanguageInfoId")) {
                    searchResult.setFilmLanguageInfoId(item.get("filmLanguageInfoId").getAsString());
                }

                if (item.has("categoryId") && !item.get("categoryId").isJsonNull()) {
                    searchResult.setCategoryId(item.get("categoryId").getAsString());
                }

                if (item.has("categoryName") && !item.get("categoryName").isJsonNull()) {
                    searchResult.setCategoryName(item.get("categoryName").getAsString());
                }

                if (item.has("languageType") && !item.get("languageType").isJsonNull()) {
                    searchResult.setLanguageType(item.get("languageType").getAsInt());
                }

                trendingSearches.add(searchResult);
            }

            Log.d(TAG, "热门搜索数据解析成功: " + trendingSearches.size() + " 个项目");

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(trendingSearches);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "热门搜索响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析异常: " + e.getMessage());
                }
            });
        }
    }
}
