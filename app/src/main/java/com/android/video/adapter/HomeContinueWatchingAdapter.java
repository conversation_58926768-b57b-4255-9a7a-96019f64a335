package com.android.video.adapter;

import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.ForegroundColorSpan;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.core.content.ContextCompat;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页继续观看RecyclerView适配器
 * <AUTHOR>
 */
public class HomeContinueWatchingAdapter extends RecyclerView.Adapter<HomeContinueWatchingAdapter.ContinueWatchingViewHolder> {
    
    private List<VideoModel> continueWatchingList;
    private OnContinueWatchingClickListener onContinueWatchingClickListener;
    
    /**
     * 继续观看点击监听接口
     */
    public interface OnContinueWatchingClickListener {
        void onContinueWatchingClick(VideoModel video, int position);
        void onContinueWatchingLongClick(VideoModel video, int position);
    }
    
    /**
     * 构造函数
     */
    public HomeContinueWatchingAdapter() {
        this.continueWatchingList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param continueWatchingList 继续观看列表
     */
    public HomeContinueWatchingAdapter(List<VideoModel> continueWatchingList) {
        this.continueWatchingList = continueWatchingList != null ? continueWatchingList : new ArrayList<>();
    }
    
    /**
     * 设置继续观看点击监听器
     * @param listener 点击监听器
     */
    public void setOnContinueWatchingClickListener(OnContinueWatchingClickListener listener) {
        this.onContinueWatchingClickListener = listener;
    }
    
    /**
     * 更新继续观看列表
     * @param newContinueWatchingList 新的继续观看列表
     */
    public void updateContinueWatchingList(List<VideoModel> newContinueWatchingList) {
        this.continueWatchingList = newContinueWatchingList != null ? newContinueWatchingList : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 添加继续观看视频
     * @param video 视频数据
     */
    public void addContinueWatchingVideo(VideoModel video) {
        if (video != null) {
            continueWatchingList.add(video);
            notifyItemInserted(continueWatchingList.size() - 1);
        }
    }
    
    /**
     * 移除继续观看视频
     * @param position 位置
     */
    public void removeContinueWatchingVideo(int position) {
        if (position >= 0 && position < continueWatchingList.size()) {
            continueWatchingList.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    /**
     * 更新视频观看进度
     * @param position 位置
     * @param currentEpisode 当前集数
     */
    public void updateWatchProgress(int position, int currentEpisode) {
        if (position >= 0 && position < continueWatchingList.size()) {
            continueWatchingList.get(position).setCurrentEpisode(currentEpisode);
            notifyItemChanged(position);
        }
    }
    
    /**
     * 更新历史记录列表（从MyHistoryItemModel转换）
     * @param historyList 历史记录列表
     */
    public void updateHistoryList(List<com.android.video.model.MyHistoryItemModel> historyList) {
        if (historyList == null) {
            this.continueWatchingList = new ArrayList<>();
        } else {
            this.continueWatchingList = new ArrayList<>();
            for (com.android.video.model.MyHistoryItemModel historyItem : historyList) {
                if (historyItem != null) {
                    // 使用toVideoModel()方法转换
                    VideoModel video = historyItem.toVideoModel();
                    if (video != null) {
                        this.continueWatchingList.add(video);
                    }
                }
            }
        }
        notifyDataSetChanged();
    }

    /**
     * 获取历史记录列表
     * @return 历史记录列表（转换为MyHistoryItemModel格式）
     */
    public List<com.android.video.model.MyHistoryItemModel> getHistoryList() {
        List<com.android.video.model.MyHistoryItemModel> historyList = new ArrayList<>();
        for (VideoModel video : continueWatchingList) {
            // 创建一个基本的MyHistoryItemModel
            com.android.video.model.MyHistoryItemModel historyItem =
                new com.android.video.model.MyHistoryItemModel();
            historyItem.setFilmId(video.getId());
            historyItem.setFilmTitle(video.getTitle());
            historyItem.setCover(video.getPosterUrl());
            historyItem.setDetails(video.getDescription());
            historyItem.setTotalChaptersNum(video.getTotalEpisodes());
            historyItem.setLastWatchChapterNum(video.getCurrentEpisode());
            historyItem.setFilmLanguageInfoId(video.getFilmLanguageInfoId());
            historyList.add(historyItem);
        }
        return historyList;
    }

    /**
     * 获取继续观看列表
     * @return 继续观看列表
     */
    public List<VideoModel> getContinueWatchingList() {
        return new ArrayList<>(continueWatchingList);
    }
    
    @NonNull
    @Override
    public ContinueWatchingViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_home_continue_watching, parent, false);
        return new ContinueWatchingViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull ContinueWatchingViewHolder holder, int position) {
        VideoModel video = continueWatchingList.get(position);
        holder.bind(video, position);
    }
    
    @Override
    public int getItemCount() {
        return continueWatchingList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class ContinueWatchingViewHolder extends RecyclerView.ViewHolder {
        
        private ImageView ivContinuePoster;
        private TextView tvContinueTitle;
        private TextView tvContinueProgress;
        
        public ContinueWatchingViewHolder(@NonNull View itemView) {
            super(itemView);
            ivContinuePoster = itemView.findViewById(R.id.iv_continue_poster);
            tvContinueTitle = itemView.findViewById(R.id.tv_continue_title);
            tvContinueProgress = itemView.findViewById(R.id.tv_continue_progress);
        }
        
        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }
            
            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .centerCrop()
                    .into(ivContinuePoster);
            } else {
                // 如果没有URL，使用默认图片
                ivContinuePoster.setImageResource(R.drawable.movie_poster);
            }
            
            // 设置视频标题
            tvContinueTitle.setText(video.getDisplayTitle());
            
            // 设置观看进度 - 使用SpannableString实现分段颜色
            String progressText = video.getProgressText();
            SpannableString spannableProgress = new SpannableString(progressText);

            // 找到"/"的位置来分割已观看和未观看部分
            int slashIndex = progressText.indexOf("/");
            if (slashIndex != -1) {
                // 获取颜色资源
                int watchedColor = ContextCompat.getColor(itemView.getContext(), R.color.continue_watching_watched_color);
                int unwatchedColor = ContextCompat.getColor(itemView.getContext(), R.color.continue_watching_unwatched_color);

                // 对"EP.1"部分应用红色
                spannableProgress.setSpan(new ForegroundColorSpan(watchedColor), 0, slashIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);

                // 对"/EP.72"部分应用白色半透明
                spannableProgress.setSpan(new ForegroundColorSpan(unwatchedColor), slashIndex, progressText.length(), Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            }

            tvContinueProgress.setText(spannableProgress);
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onContinueWatchingClickListener != null) {
                    onContinueWatchingClickListener.onContinueWatchingClick(video, position);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(), 
                            "继续观看: " + video.getDisplayTitle() + " " + video.getProgressText(), 
                            Toast.LENGTH_SHORT).show();
                }
            });
            
            // 设置长按事件
            itemView.setOnLongClickListener(v -> {
                if (onContinueWatchingClickListener != null) {
                    onContinueWatchingClickListener.onContinueWatchingLongClick(video, position);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(),
                            "长按: " + video.getDisplayTitle() + "\n进度: " + video.getProgressText() +
                            "\n观看进度: " + String.format("%.1f%%", video.getWatchProgress() * 100),
                            Toast.LENGTH_LONG).show();
                }
                return true;
            });
        }
    }
}
