package com.android.video.service;

import android.Manifest;
import android.content.Context;
import android.content.pm.PackageManager;
import android.location.Address;
import android.location.Geocoder;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import androidx.core.app.ActivityCompat;

import java.io.IOException;
import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 位置服务类
 * <p>
 * 提供获取用户当前位置和IP位置的功能，支持GPS定位和网络定位。
 * 包含权限检查、位置监听和地址解析功能。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class LocationService {
    
    private static final String TAG = "LocationService";
    
    // 位置更新的最小时间间隔（毫秒）
    private static final long MIN_TIME_BETWEEN_UPDATES = 60000; // 1分钟
    
    // 位置更新的最小距离（米）
    private static final float MIN_DISTANCE_CHANGE_FOR_UPDATES = 100; // 100米
    
    // 位置获取超时时间（毫秒）
    private static final long LOCATION_TIMEOUT = 30000; // 30秒
    
    private final Context context;
    private final LocationManager locationManager;
    private final Geocoder geocoder;
    private final Handler mainHandler;
    private final ExecutorService executor;
    
    /**
     * 位置信息回调接口
     */
    public interface LocationCallback {
        /**
         * 位置获取成功
         * @param locationInfo 位置信息
         */
        void onLocationSuccess(LocationInfo locationInfo);
        
        /**
         * 位置获取失败
         * @param errorMessage 错误信息
         */
        void onLocationError(String errorMessage);
    }
    
    /**
     * 位置信息数据类
     */
    public static class LocationInfo {
        private double latitude;
        private double longitude;
        private String country;
        private String city;
        private String address;
        private String provider;
        private long timestamp;
        
        public LocationInfo(double latitude, double longitude, String country, 
                          String city, String address, String provider) {
            this.latitude = latitude;
            this.longitude = longitude;
            this.country = country;
            this.city = city;
            this.address = address;
            this.provider = provider;
            this.timestamp = System.currentTimeMillis();
        }
        
        // Getters
        public double getLatitude() { return latitude; }
        public double getLongitude() { return longitude; }
        public String getCountry() { return country; }
        public String getCity() { return city; }
        public String getAddress() { return address; }
        public String getProvider() { return provider; }
        public long getTimestamp() { return timestamp; }
        
        /**
         * 获取格式化的位置显示文本
         * @return 格式化的位置字符串
         */
        public String getFormattedLocation() {
            if (city != null && !city.isEmpty() && country != null && !country.isEmpty()) {
                return city + ", " + country;
            } else if (country != null && !country.isEmpty()) {
                return country;
            } else if (city != null && !city.isEmpty()) {
                return city;
            } else {
                return "Unknown Location";
            }
        }
        
        @Override
        public String toString() {
            return "LocationInfo{" +
                    "latitude=" + latitude +
                    ", longitude=" + longitude +
                    ", country='" + country + '\'' +
                    ", city='" + city + '\'' +
                    ", address='" + address + '\'' +
                    ", provider='" + provider + '\'' +
                    ", timestamp=" + timestamp +
                    '}';
        }
    }
    
    /**
     * 构造方法
     * @param context 上下文
     */
    public LocationService(Context context) {
        this.context = context.getApplicationContext();
        this.locationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
        this.geocoder = new Geocoder(context, Locale.getDefault());
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }
    
    /**
     * 检查位置权限
     * @return 如果有位置权限则返回true，否则返回false
     */
    public boolean hasLocationPermission() {
        return ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_FINE_LOCATION) == PackageManager.PERMISSION_GRANTED ||
               ActivityCompat.checkSelfPermission(context, Manifest.permission.ACCESS_COARSE_LOCATION) == PackageManager.PERMISSION_GRANTED;
    }
    
    /**
     * 获取当前位置
     * @param callback 位置回调接口
     */
    public void getCurrentLocation(LocationCallback callback) {
        if (!hasLocationPermission()) {
            callback.onLocationError("Location permission not granted");
            return;
        }
        
        if (locationManager == null) {
            callback.onLocationError("Location manager not available");
            return;
        }
        
        Log.d(TAG, "开始获取当前位置");
        
        // 创建位置监听器
        LocationListener locationListener = new LocationListener() {
            @Override
            public void onLocationChanged(Location location) {
                Log.d(TAG, "位置更新: " + location.toString());
                locationManager.removeUpdates(this);
                processLocation(location, callback);
            }
            
            @Override
            public void onStatusChanged(String provider, int status, Bundle extras) {
                Log.d(TAG, "位置提供者状态变化: " + provider + ", status: " + status);
            }
            
            @Override
            public void onProviderEnabled(String provider) {
                Log.d(TAG, "位置提供者启用: " + provider);
            }
            
            @Override
            public void onProviderDisabled(String provider) {
                Log.d(TAG, "位置提供者禁用: " + provider);
            }
        };
        
        try {
            // 尝试从GPS获取位置
            if (locationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
                Log.d(TAG, "使用GPS提供者获取位置");
                locationManager.requestLocationUpdates(
                    LocationManager.GPS_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES,
                    MIN_DISTANCE_CHANGE_FOR_UPDATES,
                    locationListener
                );
            }
            
            // 尝试从网络获取位置
            if (locationManager.isProviderEnabled(LocationManager.NETWORK_PROVIDER)) {
                Log.d(TAG, "使用网络提供者获取位置");
                locationManager.requestLocationUpdates(
                    LocationManager.NETWORK_PROVIDER,
                    MIN_TIME_BETWEEN_UPDATES,
                    MIN_DISTANCE_CHANGE_FOR_UPDATES,
                    locationListener
                );
            }
            
            // 设置超时处理
            mainHandler.postDelayed(() -> {
                locationManager.removeUpdates(locationListener);
                
                // 尝试获取最后已知位置
                Location lastKnownLocation = getLastKnownLocation();
                if (lastKnownLocation != null) {
                    Log.d(TAG, "使用最后已知位置");
                    processLocation(lastKnownLocation, callback);
                } else {
                    callback.onLocationError("Location timeout - no location available");
                }
            }, LOCATION_TIMEOUT);
            
        } catch (SecurityException e) {
            Log.e(TAG, "位置权限错误", e);
            callback.onLocationError("Location permission error: " + e.getMessage());
        } catch (Exception e) {
            Log.e(TAG, "获取位置时发生错误", e);
            callback.onLocationError("Location error: " + e.getMessage());
        }
    }
    
    /**
     * 获取最后已知位置
     * @return 最后已知位置，如果没有则返回null
     */
    private Location getLastKnownLocation() {
        if (!hasLocationPermission() || locationManager == null) {
            return null;
        }
        
        try {
            Location gpsLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
            Location networkLocation = locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
            
            // 选择更新的位置
            if (gpsLocation != null && networkLocation != null) {
                return gpsLocation.getTime() > networkLocation.getTime() ? gpsLocation : networkLocation;
            } else if (gpsLocation != null) {
                return gpsLocation;
            } else {
                return networkLocation;
            }
        } catch (SecurityException e) {
            Log.e(TAG, "获取最后已知位置时权限错误", e);
            return null;
        }
    }
    
    /**
     * 处理位置信息
     * @param location 位置对象
     * @param callback 回调接口
     */
    private void processLocation(Location location, LocationCallback callback) {
        executor.execute(() -> {
            try {
                String country = "Unknown";
                String city = "Unknown";
                String address = "Unknown";
                
                if (geocoder != null && Geocoder.isPresent()) {
                    List<Address> addresses = geocoder.getFromLocation(
                        location.getLatitude(), 
                        location.getLongitude(), 
                        1
                    );
                    
                    if (addresses != null && !addresses.isEmpty()) {
                        Address addr = addresses.get(0);
                        country = addr.getCountryName() != null ? addr.getCountryName() : "Unknown";
                        city = addr.getLocality() != null ? addr.getLocality() :
                               (addr.getAdminArea() != null ? addr.getAdminArea() : "Unknown");
                        address = addr.getAddressLine(0) != null ? addr.getAddressLine(0) : "Unknown";
                        
                        Log.d(TAG, "地址解析成功: " + country + ", " + city);
                    }
                }
                
                LocationInfo locationInfo = new LocationInfo(
                    location.getLatitude(),
                    location.getLongitude(),
                    country,
                    city,
                    address,
                    location.getProvider()
                );
                
                mainHandler.post(() -> callback.onLocationSuccess(locationInfo));
                
            } catch (IOException e) {
                Log.e(TAG, "地址解析失败", e);
                
                // 即使地址解析失败，也返回基本的位置信息
                LocationInfo locationInfo = new LocationInfo(
                    location.getLatitude(),
                    location.getLongitude(),
                    "Unknown",
                    "Unknown",
                    "Unknown",
                    location.getProvider()
                );
                
                mainHandler.post(() -> callback.onLocationSuccess(locationInfo));
            }
        });
    }
    
    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
