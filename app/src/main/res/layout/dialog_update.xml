<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/dialog_container"
    android:layout_width="@dimen/update_dialog_width"
    android:layout_height="@dimen/update_dialog_height"
    android:background="@drawable/update_bg"
    android:clickable="true"
    android:focusable="true">

    <!-- Title: Time To Update! -->
    <TextView
        android:id="@+id/tv_update_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/update_dialog_title"
        android:textColor="@android:color/white"
        android:textSize="@dimen/update_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:letterSpacing="0.15"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/update_title_margin_top" />

    <!-- Version Container -->
    <LinearLayout
        android:id="@+id/ll_version_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        app:layout_constraintTop_toBottomOf="@id/tv_update_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/update_version_margin_top">

        <!-- Left line -->
        <View
            android:layout_width="@dimen/update_line_width"
            android:layout_height="@dimen/update_line_height"
            android:background="#1FFFFFFF"
            android:layout_marginEnd="@dimen/update_line_margin" />

        <!-- Version text -->
        <TextView
            android:id="@+id/tv_version"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/update_dialog_version"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/update_version_text_size"
            android:fontFamily="sans-serif" />

        <!-- Right line -->
        <View
            android:layout_width="@dimen/update_line_width"
            android:layout_height="@dimen/update_line_height"
            android:background="#1FFFFFFF"
            android:layout_marginStart="@dimen/update_line_margin" />

    </LinearLayout>

    <!-- Description Container -->
    <LinearLayout
        android:id="@+id/ll_description_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintTop_toBottomOf="@id/ll_version_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/update_description_margin_top"
        android:layout_marginStart="@dimen/update_description_margin_horizontal"
        android:layout_marginEnd="@dimen/update_description_margin_horizontal">

        <!-- First line with rocket icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center">

            <ImageView
                android:id="@+id/iv_rocket"
                android:layout_width="@dimen/update_rocket_size"
                android:layout_height="@dimen/update_rocket_size"
                android:src="@drawable/update_roket"
                android:scaleType="centerInside" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/update_dialog_description_line1"
                android:textColor="#CCFFFFFF"
                android:textSize="@dimen/update_description_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Second line -->
        <TextView
            android:id="@+id/tv_description_line2"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/update_dialog_description_line2"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/update_description_text_size"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:layout_marginTop="@dimen/update_description_line_spacing" />

        <!-- Third line -->
        <TextView
            android:id="@+id/tv_description_line3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="@string/update_dialog_description_line3"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/update_description_text_size"
            android:fontFamily="sans-serif"
            android:gravity="center"
            android:layout_marginTop="@dimen/update_description_line_spacing" />

    </LinearLayout>

    <!-- Button Container -->
    <LinearLayout
        android:id="@+id/ll_button_container"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center"
        app:layout_constraintTop_toBottomOf="@id/ll_description_container"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/update_button_margin_top">

        <!-- Cancel Button (hidden by default) -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_cancel"
            android:layout_width="120dp"
            android:layout_height="@dimen/update_button_height"
            android:text="稍后提醒"
            android:textColor="#CCFFFFFF"
            android:textSize="@dimen/update_button_text_size"
            android:fontFamily="sans-serif-medium"
            android:background="@android:color/transparent"
            android:stateListAnimator="@null"
            android:elevation="0dp"
            android:layout_marginEnd="16dp"
            android:visibility="gone" />

        <!-- Update Button -->
        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_update"
            android:layout_width="@dimen/update_button_width"
            android:layout_height="@dimen/update_button_height"
            android:text="@string/update_dialog_button"
            android:textColor="@android:color/white"
            android:textSize="@dimen/update_button_text_size"
            android:fontFamily="sans-serif-medium"
            android:background="@drawable/update_button_selector"
            android:stateListAnimator="@null"
            android:elevation="0dp" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
