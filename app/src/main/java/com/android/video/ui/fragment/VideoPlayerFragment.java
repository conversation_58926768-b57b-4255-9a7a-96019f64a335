package com.android.video.ui.fragment;

import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.GestureDetector;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.model.ChapterInfo;
import com.android.video.model.VideoModel;
import com.android.video.model.TestVideoModel;
import com.android.video.model.request.PlayProgressReportModel;
import com.android.video.model.response.PlayProgressReportResponseModel;
import com.android.video.model.response.PlayUrlResponseModel;
import com.android.video.network.ChapterListApiService;
import com.android.video.player.VideoPlayerManager;
import com.android.video.player.VideoPlayerListener;
import com.android.video.ui.component.DanmakuController;
import com.android.video.utils.VideoResourceManager;
import com.android.video.utils.VideoPlayerPreferences;
import com.google.android.exoplayer2.ui.PlayerView;
import master.flame.danmaku.ui.widget.DanmakuView;

/**
 * 单个视频播放Fragment
 * 用于ViewPager2中的每个视频页面
 * <AUTHOR> Team
 */
public class VideoPlayerFragment extends BaseFullScreenFragment implements VideoPlayerListener {

    private static final String TAG = "VideoPlayerFragment";
    private static final String ARG_VIDEO_MODEL = "video_model";
    private static final String ARG_VIDEO_POSITION = "video_position";

    // UI Components
    private PlayerView playerView;
    private DanmakuView danmakuView;
    private SwipeRefreshLayout swipeRefreshVideoLoading;
    private ImageView btnPlayPause;
    private ImageView btnSeekBackward;
    private ImageView btnSeekForward;
    private LinearLayout layoutError;

    private LinearLayout layoutVideoInfo;
    private TextView tvErrorMessage;
    private Button btnRetry;
    private View gestureOverlay;

    // Debug Info Views
    private TextView tvVideoTitle;
    private TextView tvVideoResolution;
    private TextView tvVideoDuration;
    private TextView tvVideoPosition;

    // Data
    private VideoModel videoModel;
    private TestVideoModel testVideoModel;
    private int fragmentPosition;
    private VideoPlayerManager playerManager;
    private GlobalVideoPlayerManager globalManager;
    private Handler uiHandler;
    private boolean isPlayerPrepared = false;
    private boolean isVisible = false;
    private boolean isPlaying = false;
    private boolean isVideoLoaded = false;
    private boolean shouldAutoPlay = false;
    private boolean hasAttemptedLoad = false; // 防止重复加载的标志
    private int retryCount = 0; // 重试计数器
    private static final int MAX_RETRY_COUNT = 3; // 最大重试次数

    // Gesture Detection
    private GestureDetector gestureDetector;

    // Controls Management
    private Handler controlsHandler;
    private Runnable hideControlsRunnable;
    private boolean areControlsVisible = false;

    // Danmaku
    private DanmakuController danmakuController;
    private boolean isDanmakuEnabled = true; // 弹幕开关状态，默认开启

    // Play Progress Reporting
    private ChapterListApiService chapterListApiService;
    private Handler progressReportHandler;
    private Runnable progressReportRunnable;
    private static final int PROGRESS_REPORT_INTERVAL = 30000; // 30秒上报一次
    private long lastReportedProgress = 0;
    private boolean isProgressReportingEnabled = true;

    public static VideoPlayerFragment newInstance(VideoModel videoModel, VideoPlayerManager playerManager) {
        VideoPlayerFragment fragment = new VideoPlayerFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_VIDEO_MODEL, videoModel);
        fragment.setArguments(args);
        fragment.playerManager = playerManager;
        return fragment;
    }

    public static VideoPlayerFragment newInstance(VideoModel videoModel, int position) {
        VideoPlayerFragment fragment = new VideoPlayerFragment();
        Bundle args = new Bundle();
        args.putSerializable(ARG_VIDEO_MODEL, videoModel);
        args.putInt(ARG_VIDEO_POSITION, position);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        uiHandler = new Handler(Looper.getMainLooper());

        // 获取全局播放器管理器
        globalManager = GlobalVideoPlayerManager.getInstance();

        // 初始化播放进度上报服务
        chapterListApiService = new ChapterListApiService();
        progressReportHandler = new Handler(Looper.getMainLooper());

        if (getArguments() != null) {
            videoModel = (VideoModel) getArguments().getSerializable(ARG_VIDEO_MODEL);
            fragmentPosition = getArguments().getInt(ARG_VIDEO_POSITION, 0);
        }

        // 初始化手势检测
        initializeGestureDetector();

        Log.d(TAG, "Fragment created for video: " + (videoModel != null ? videoModel.getTitle() : "null"));
    }

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_video_player, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initializeViews(view);
        setupClickListeners();
        setupGestureDetection();

        // 确保控制按钮初始状态为隐藏
        ensureControlsHidden();

        // 初始化独立的VideoPlayerManager
        initializePlayerManager();

        if (videoModel != null) {
            loadVideoData();
        }
    }

    /**
     * 确保控制按钮初始状态为隐藏
     */
    private void ensureControlsHidden() {
        areControlsVisible = false;
        if (btnPlayPause != null) {
            btnPlayPause.setVisibility(View.GONE);
        }
        if (btnSeekBackward != null) {
            btnSeekBackward.setVisibility(View.GONE);
        }
        if (btnSeekForward != null) {
            btnSeekForward.setVisibility(View.GONE);
        }
        Log.d(TAG, "Control buttons initialized as hidden");
    }

    /**
     * 初始化VideoPlayerManager
     */
    private void initializePlayerManager() {
        if (getContext() != null && playerManager == null) {
            playerManager = new VideoPlayerManager(getContext());
            playerManager.setPlayerListener(this);
            Log.d(TAG, "PlayerManager initialized for fragment at position " + fragmentPosition);
        }
    }

    private void initializeViews(View view) {
        playerView = view.findViewById(R.id.playerView);
        danmakuView = view.findViewById(R.id.danmakuView);
        swipeRefreshVideoLoading = view.findViewById(R.id.swipe_refresh_video_loading);
        btnPlayPause = view.findViewById(R.id.btnPlayPause);
        btnSeekBackward = view.findViewById(R.id.btnSeekBackward);
        btnSeekForward = view.findViewById(R.id.btnSeekForward);
        layoutError = view.findViewById(R.id.layoutError);

        layoutVideoInfo = view.findViewById(R.id.layoutVideoInfo);
        tvErrorMessage = view.findViewById(R.id.tvErrorMessage);
        btnRetry = view.findViewById(R.id.btnRetry);
        gestureOverlay = view.findViewById(R.id.gestureOverlay);

        // Debug Info Views
        tvVideoTitle = view.findViewById(R.id.tvVideoTitle);
        tvVideoResolution = view.findViewById(R.id.tvVideoResolution);
        tvVideoDuration = view.findViewById(R.id.tvVideoDuration);
        tvVideoPosition = view.findViewById(R.id.tvVideoPosition);

        // Initialize Controls Handler
        controlsHandler = new Handler(Looper.getMainLooper());
        hideControlsRunnable = this::hidePlayControls;

        // Initialize Danmaku Controller
        if (danmakuView != null) {
            danmakuController = new DanmakuController(getContext(), danmakuView);
        }
    }

    private void setupClickListeners() {
        if (btnPlayPause != null) {
            btnPlayPause.setOnClickListener(v -> {
                Log.d(TAG, "Play/Pause button clicked for fragment " + fragmentPosition);
                // 阻止事件传播到父视图
                v.setPressed(true);
                v.postDelayed(() -> v.setPressed(false), 100);
                togglePlayPause();
                // 不要立即隐藏控件，让用户看到状态变化
                showPlayControls();
            });
            // 确保按钮可以接收点击事件
            btnPlayPause.setClickable(true);
            btnPlayPause.setFocusable(true);
        }

        if (btnSeekBackward != null) {
            btnSeekBackward.setOnClickListener(v -> {
                Log.d(TAG, "Seek backward button clicked for fragment " + fragmentPosition);
                // 阻止事件传播到父视图
                v.setPressed(true);
                v.postDelayed(() -> v.setPressed(false), 100);
                seekRelative(-5000); // 快退5秒
                showPlayControls(); // 重新显示控件并重置定时器
            });
            // 确保按钮可以接收点击事件
            btnSeekBackward.setClickable(true);
            btnSeekBackward.setFocusable(true);
        }

        if (btnSeekForward != null) {
            btnSeekForward.setOnClickListener(v -> {
                Log.d(TAG, "Seek forward button clicked for fragment " + fragmentPosition);
                // 阻止事件传播到父视图
                v.setPressed(true);
                v.postDelayed(() -> v.setPressed(false), 100);
                seekRelative(5000); // 快进5秒
                showPlayControls(); // 重新显示控件并重置定时器
            });
            // 确保按钮可以接收点击事件
            btnSeekForward.setClickable(true);
            btnSeekForward.setFocusable(true);
        }

        if (btnRetry != null) {
            btnRetry.setOnClickListener(v -> retryLoadVideo());
        }

        // 禁用长按显示调试信息功能
        // gestureOverlay.setOnLongClickListener(v -> {
        //     toggleDebugInfo();
        //     return true;
        // });
    }

    private void setupGestureDetection() {
        gestureOverlay.setOnTouchListener((v, event) -> {
            // 检查触摸点是否在控制按钮区域内，如果是则不处理手势
            if (isTouchOnControlButtons(event)) {
                Log.d(TAG, "Touch on control button detected, passing through");
                return false; // 让控制按钮处理触摸事件
            }

            // 处理手势检测
            boolean gestureHandled = gestureDetector.onTouchEvent(event);
            Log.d(TAG, "Gesture handled: " + gestureHandled);

            // 确保总是返回true，这样手势检测器才能正常工作
            return true;
        });

        // 确保手势覆盖层不会阻止子视图接收触摸事件
        gestureOverlay.setClickable(false);
        gestureOverlay.setFocusable(false);
    }

    /**
     * 检查触摸点是否在控制按钮区域内
     */
    private boolean isTouchOnControlButtons(MotionEvent event) {
        if (!areControlsVisible) {
            return false; // 如果控制按钮不可见，则不需要检查
        }

        float x = event.getX();
        float y = event.getY();

        // 检查是否触摸到播放/暂停按钮
        if (btnPlayPause != null && btnPlayPause.getVisibility() == View.VISIBLE) {
            if (isPointInView(btnPlayPause, x, y)) {
                Log.d(TAG, "Touch detected on play/pause button");
                return true;
            }
        }

        // 检查快退按钮
        if (btnSeekBackward != null && btnSeekBackward.getVisibility() == View.VISIBLE) {
            if (isPointInView(btnSeekBackward, x, y)) {
                Log.d(TAG, "Touch detected on seek backward button");
                return true;
            }
        }

        // 检查快进按钮
        if (btnSeekForward != null && btnSeekForward.getVisibility() == View.VISIBLE) {
            if (isPointInView(btnSeekForward, x, y)) {
                Log.d(TAG, "Touch detected on seek forward button");
                return true;
            }
        }

        return false;
    }

    /**
     * 检查点是否在视图内
     */
    private boolean isPointInView(View view, float x, float y) {
        int[] location = new int[2];
        view.getLocationOnScreen(location);
        int[] overlayLocation = new int[2];
        gestureOverlay.getLocationOnScreen(overlayLocation);

        int left = location[0] - overlayLocation[0];
        int top = location[1] - overlayLocation[1];
        int right = left + view.getWidth();
        int bottom = top + view.getHeight();

        // 增加一些触摸容差，使按钮更容易点击
        int touchTolerance = 20; // 20dp的容差
        left -= touchTolerance;
        top -= touchTolerance;
        right += touchTolerance;
        bottom += touchTolerance;

        return x >= left && x <= right && y >= top && y <= bottom;
    }

    private void initializeGestureDetector() {
        gestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                // 单击显示/隐藏控件
                if (areControlsVisible) {
                    hidePlayControls();
                } else {
                    showPlayControls();
                }
                // 不再通知Activity，Fragment独立管理控制按钮
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (playerManager == null) {
                    Log.w(TAG, "Double tap ignored - playerManager is null");
                    return true;
                }

                // 如果播放器还没准备好，给用户提示
                if (!isPlayerPrepared) {
                    Log.w(TAG, "Double tap ignored - player not ready yet");
                    showToast("播放器正在准备中...");
                    return true;
                }

                float x = e.getX();
                float screenWidth = gestureOverlay.getWidth();

                Log.d(TAG, "Double tap detected at x=" + x + ", screenWidth=" + screenWidth);

                if (x < screenWidth / 3) {
                    // 左侧双击：快退15秒
                    seekRelative(-15000);
                } else if (x > screenWidth * 2 / 3) {
                    // 右侧双击：快进15秒
                    seekRelative(15000);
                } else {
                    // 中间双击：暂停/播放
                    Log.d(TAG, "Double tap in center - toggling play/pause");
                    togglePlayPause();
                }

                // 显示控制按钮
                showPlayControls();
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                // 这里可以添加滑动手势处理，但主要的滑动由ViewPager2处理
                return false;
            }
        });
    }

    public void loadVideoData() {
        if (videoModel == null) {
            showError("视频数据为空");
            return;
        }

        // 避免重复加载
        if (isVideoLoaded && isPlayerPrepared) {
            Log.d(TAG, "Video already loaded, skipping reload");
            return;
        }

        showLoading();
        updateDebugInfo();

        // 获取测试视频数据
        VideoResourceManager resourceManager = VideoResourceManager.getInstance();
        if (resourceManager.isInitialized()) {
            // 尝试根据视频ID获取测试视频
            testVideoModel = resourceManager.getTestVideoById(videoModel.getId());
            if (testVideoModel == null && resourceManager.getValidVideoCount() > 0) {
                // 如果没有找到对应的测试视频，使用第一个有效的测试视频
                testVideoModel = resourceManager.getValidTestVideos().get(0);
            }
        }

        // 加载视频
        loadVideo();
    }

    /**
     * 重置加载状态（用于强制重新加载视频）
     */
    public void resetLoadingState() {
        Log.d(TAG, "Resetting loading state for fragment at position " + fragmentPosition);
        isVideoLoaded = false;
        isPlayerPrepared = false;
        hasAttemptedLoad = false; // 重置加载尝试标志，允许重新加载
        retryCount = 0; // 重置重试计数器

        // 停止当前播放
        if (playerManager != null) {
            playerManager.pause();
            playerManager.stop();
        }
    }

    private void loadVideo() {
        if (playerManager == null) {
            showError("播放器未初始化");
            return;
        }

        // 检查是否需要先获取章节列表（针对My List历史记录）
        if (needsChapterListFirst()) {
            loadChapterListFirst();
            return;
        }

        // 检查是否需要从API获取播放地址
        if (shouldLoadFromApi()) {
            loadVideoFromApi();
        } else {
            // 使用本地或测试视频
            loadVideoFromLocal();
        }
    }

    /**
     * 检查是否需要先获取章节列表
     * 针对My List历史记录，如果有filmLanguageInfoId但没有episodes列表，需要先获取章节列表
     */
    private boolean needsChapterListFirst() {
        if (videoModel == null) {
            return false;
        }

        // 如果有filmLanguageInfoId但没有episodes列表，说明是从My List来的历史记录
        boolean hasFilmLanguageInfoId = videoModel.getFilmLanguageInfoId() != null &&
                                       !videoModel.getFilmLanguageInfoId().isEmpty();
        boolean hasEpisodes = videoModel.getEpisodes() != null && !videoModel.getEpisodes().isEmpty();

        return hasFilmLanguageInfoId && !hasEpisodes;
    }

    /**
     * 先获取章节列表，然后再加载视频
     */
    private void loadChapterListFirst() {
        if (videoModel == null || videoModel.getFilmLanguageInfoId() == null) {
            Log.e(TAG, "Cannot load chapter list: missing filmLanguageInfoId");
            loadVideoFromLocal();
            return;
        }

        Log.d(TAG, "Loading chapter list first for filmLanguageInfoId: " + videoModel.getFilmLanguageInfoId());

        // 调用章节列表API
        chapterListApiService.getChapterList(videoModel.getFilmLanguageInfoId(),
            new ChapterListApiService.ApiCallback<com.android.video.model.response.ChapterListResponseModel>() {
                @Override
                public void onSuccess(com.android.video.model.response.ChapterListResponseModel response) {
                    if (response != null && response.isSuccess() && response.getData() != null) {
                        // 将章节信息转换为episodes列表
                        convertChapterListToEpisodes(response.getData().getChapterList());
                        // 现在可以正常加载视频了
                        loadVideo();
                    } else {
                        Log.e(TAG, "Chapter list API failed: " + (response != null ? response.getMessage() : "null response"));
                        loadVideoFromLocal();
                    }
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "Chapter list API error: " + error);
                    loadVideoFromLocal();
                }
            });
    }

    /**
     * 将章节列表转换为episodes列表
     */
    private void convertChapterListToEpisodes(java.util.List<com.android.video.model.ChapterInfo> chapterList) {
        if (chapterList == null || chapterList.isEmpty()) {
            return;
        }

        for (com.android.video.model.ChapterInfo chapterInfo : chapterList) {
            com.android.video.model.EpisodeModel episode = new com.android.video.model.EpisodeModel(
                chapterInfo.getChapterId(),
                chapterInfo.getChapterEp() != null ? chapterInfo.getChapterEp() : 0,
                chapterInfo.getChapterTitle()
            );

            // 设置播放进度和状态信息
            episode.setProgress(chapterInfo.getProgress() != null ? chapterInfo.getProgress() : 0);
            episode.setUnlock(chapterInfo.isUnlocked());
            episode.setCharge(chapterInfo.isCharged());
            episode.setWatched(chapterInfo.isPlayFinished());
            episode.setLastPlayTime(chapterInfo.getLastPlayTime());

            // 设置当前观看的剧集
            if (chapterInfo.getChapterEp() != null && chapterInfo.getChapterEp() == videoModel.getCurrentEpisode()) {
                episode.setSelected(true);
                // 保存当前剧集的播放进度，稍后应用到播放器
                if (episode.getProgress() > 0) {
                    Log.d(TAG, "Found saved progress for episode " + episode.getEpisodeNumber() + ": " + episode.getProgress() + " seconds");
                }
            }

            videoModel.addEpisode(episode);
        }

        Log.d(TAG, "Converted " + chapterList.size() + " chapters to episodes with progress info");
    }

    /**
     * 恢复保存的播放进度
     * 针对My List历史记录，从上次停止的位置继续播放
     */
    private void restoreSavedProgress() {
        if (videoModel == null || playerManager == null) {
            return;
        }

        // 查找当前选中的剧集
        com.android.video.model.EpisodeModel currentEpisode = null;
        if (videoModel.getEpisodes() != null) {
            for (com.android.video.model.EpisodeModel episode : videoModel.getEpisodes()) {
                if (episode.isSelected()) {
                    currentEpisode = episode;
                    break;
                }
            }
        }

        if (currentEpisode != null && currentEpisode.getProgress() > 0) {
            // 检查章节是否可观看（先判断付费，再判断解锁）
            if (currentEpisode.isCharge() && !currentEpisode.isUnlock()) {
                Log.w(TAG, "Episode " + currentEpisode.getEpisodeNumber() + " is charged and locked, cannot restore progress");
                showError("该集数需要付费解锁后观看");
                return;
            }

            long progressMs = currentEpisode.getProgress() * 1000L; // 转换为毫秒
            Log.d(TAG, "Restoring progress for episode " + currentEpisode.getEpisodeNumber() +
                      ": " + currentEpisode.getProgress() + " seconds (" + progressMs + " ms)");

            // 延迟一点时间再seek，确保播放器完全准备好
            uiHandler.postDelayed(() -> {
                if (playerManager != null && isPlayerPrepared) {
                    playerManager.seekTo(progressMs);
                    Log.d(TAG, "Successfully restored progress to " + progressMs + " ms");

                    // 显示提示信息
                    showToast("已恢复到上次观看位置");
                }
            }, 500);
        } else {
            Log.d(TAG, "No saved progress found or progress is 0");
        }
    }

    /**
     * 检查是否应该从API获取播放地址
     */
    private boolean shouldLoadFromApi() {
        // 检查是否有真实的VideoModel且包含必要的API参数
        if (videoModel == null) {
            Log.d(TAG, "No VideoModel, using local video");
            return false;
        }

        String filmLanguageInfoId = videoModel.getFilmLanguageInfoId();
        if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
            Log.d(TAG, "No filmLanguageInfoId, using local video");
            return false;
        }

        String chapterId = getCurrentChapterId();
        if (chapterId == null || chapterId.isEmpty()) {
            Log.d(TAG, "No chapterId, using local video");
            return false;
        }

        Log.d(TAG, "Has API parameters, loading from API - filmLanguageInfoId: " + filmLanguageInfoId + ", chapterId: " + chapterId);
        return true;
    }

    /**
     * 从API获取播放地址并加载视频
     */
    private void loadVideoFromApi() {
        showLoading();

        String chapterId = getCurrentChapterId();
        if (chapterId == null) {
            showError("无法获取章节ID");
            return;
        }

        // 获取用户偏好的分辨率，默认使用auto
        String resolution = getPreferredResolution();
        int operationType = 1; // 1=播放, 2=下载

        // 获取filmLanguageInfoId
        String filmLanguageInfoId = videoModel.getFilmLanguageInfoId();
        if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
            showError("缺少短剧语言信息ID，无法获取播放地址");
            return;
        }

        Log.d(TAG, "=== VideoPlayerFragment 播放地址请求参数 ===");
        Log.d(TAG, "VideoModel信息:");
        if (videoModel != null) {
            Log.d(TAG, "  - Title: " + videoModel.getTitle());
            Log.d(TAG, "  - FilmLanguageInfoId: " + videoModel.getFilmLanguageInfoId());
            Log.d(TAG, "  - CurrentEpisode: " + videoModel.getCurrentEpisode());
            Log.d(TAG, "  - TotalEpisodes: " + videoModel.getTotalEpisodes());
            if (videoModel.getEpisodes() != null) {
                Log.d(TAG, "  - Episodes count: " + videoModel.getEpisodes().size());
                for (int i = 0; i < videoModel.getEpisodes().size(); i++) {
                    com.android.video.model.EpisodeModel ep = videoModel.getEpisodes().get(i);
                    Log.d(TAG, "    Episode " + (i+1) + ": ID=" + ep.getId() + ", Number=" + ep.getEpisodeNumber() + ", Title=" + ep.getTitle());
                }
            }
        }
        Log.d(TAG, "请求参数:");
        Log.d(TAG, "  - filmLanguageInfoId: " + filmLanguageInfoId);
        Log.d(TAG, "  - chapterId: " + chapterId);
        Log.d(TAG, "  - resolution: " + resolution);
        Log.d(TAG, "  - operationType: " + operationType);
        Log.d(TAG, "==========================================");

        chapterListApiService.getPlayUrl(filmLanguageInfoId, chapterId, resolution, operationType,
            new ChapterListApiService.ApiCallback<com.android.video.model.response.PlayUrlResponseModel>() {
                @Override
                public void onSuccess(com.android.video.model.response.PlayUrlResponseModel response) {
                    if (getActivity() == null || isDetached()) {
                        Log.w(TAG, "Fragment is detached, ignoring play URL response");
                        return;
                    }

                    uiHandler.post(() -> {
                        try {
                            if (response.getData() != null && response.getData().hasValidVideoUrl()) {
                                String videoUrl = response.getData().getVideoUrl();

                                // 检查URL是否过期
                                if (response.getData().isExpired()) {
                                    Log.w(TAG, "Video URL is expired, retry count: " + retryCount);

                                    if (retryCount < MAX_RETRY_COUNT) {
                                        retryCount++;
                                        Log.d(TAG, "Auto-retrying after URL expiration (attempt " + retryCount + "/" + MAX_RETRY_COUNT + ")");
                                        // 自动重试获取新的播放地址
                                        uiHandler.postDelayed(() -> {
                                            loadVideoFromApi(); // 直接重新调用API，不重置整个状态
                                        }, 1000 * retryCount); // 递增延迟时间
                                    } else {
                                        Log.e(TAG, "Max retry count reached, showing error");
                                        showError("视频链接已过期，请重试");
                                    }
                                    return;
                                }

                                // 处理字幕信息
                                if (response.getData().hasSubtitles()) {
                                    Log.d(TAG, "Found " + response.getData().getSubtitles().size() + " subtitle(s)");
                                    processSubtitles(response.getData().getSubtitles());
                                }

                                Log.d(TAG, "Got video URL from API: " + videoUrl);
                                retryCount = 0; // 成功获取URL时重置重试计数器
                                loadVideoWithUrl(videoUrl);
                            } else {
                                Log.e(TAG, "Invalid video URL in API response");
                                showError("获取的视频地址无效");
                            }
                        } catch (Exception e) {
                            Log.e(TAG, "Error processing play URL response", e);
                            showError("处理视频地址失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onError(String error) {
                    if (getActivity() == null || isDetached()) {
                        Log.w(TAG, "Fragment is detached, ignoring play URL error");
                        return;
                    }

                    Log.e(TAG, "Failed to get play URL: " + error);
                    uiHandler.post(() -> {
                        // API获取失败时，显示错误信息
                        showError("获取播放地址失败: " + error);
                    });
                }
            });
    }

    /**
     * 从本地或测试资源加载视频
     */
    private void loadVideoFromLocal() {
        try {
            String videoUrl = getVideoUrl();
            if (videoUrl != null) {
                // 检查是否是来自Discover页面的URL，如果是则检查过期时间
                if (isDiscoverPageVideo() && isDiscoverVideoExpired()) {
                    Log.w(TAG, "Discover page video URL is expired, switching to API mode");
                    // 如果Discover页面的URL过期，尝试通过API获取新的播放地址
                    if (shouldLoadFromApi()) {
                        loadVideoFromApi();
                    } else {
                        showError("视频链接已过期且无法获取新地址");
                    }
                    return;
                }

                loadVideoWithUrl(videoUrl);
            } else {
                showError("无法获取视频地址");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to load local video", e);
            showError("视频加载失败: " + e.getMessage());
        }
    }

    /**
     * 使用指定URL加载视频
     */
    private void loadVideoWithUrl(String videoUrl) {
        try {
            if (playerView != null) {
                playerManager.setPlayerView(playerView);
                playerManager.setPlayerListener(this);
                playerManager.setVideoSource(videoUrl);
                Log.d(TAG, "Loading video: " + videoUrl + " for fragment at position " + fragmentPosition);
            } else {
                showError("PlayerView未初始化");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to load video with URL: " + videoUrl, e);
            showError("视频加载失败: " + e.getMessage());
        }
    }

    private String getVideoUrl() {
        // 检查是否是下载的视频（通过检查videoModel的类型或特殊标识）
        if (videoModel != null && isDownloadedVideo()) {
            String localPath = getDownloadedVideoPath();
            if (localPath != null && new java.io.File(localPath).exists()) {
                Log.d(TAG, "Using downloaded video file: " + localPath);
                return "file://" + localPath;
            }
        }

        if (testVideoModel != null) {
            // 优先使用TestVideoModel中的视频URI
            if (testVideoModel.getVideoUri() != null) {
                Log.d(TAG, "Using video URI from TestVideoModel: " + testVideoModel.getVideoUri());
                return testVideoModel.getVideoUri().toString();
            }

            // 如果有资源ID，使用资源ID构建URI
            if (testVideoModel.getResourceId() > 0) {
                Uri uri = Uri.parse("android.resource://" + requireContext().getPackageName() + "/" + testVideoModel.getResourceId());
                Log.d(TAG, "Using resource ID from TestVideoModel: " + testVideoModel.getResourceId() + " -> " + uri);
                return uri.toString();
            }
        }

        // 使用默认的测试视频作为备用
        Uri uri = Uri.parse("android.resource://" + requireContext().getPackageName() + "/" + R.raw.movie);
        Log.d(TAG, "Using default video: " + uri);
        return uri.toString();
    }

    private void togglePlayPause() {
        if (playerManager == null) {
            Log.w(TAG, "Cannot toggle play/pause: playerManager is null");
            showToast("播放器未初始化");
            return;
        }

        // 如果播放器还没准备好，但视频已经加载，给用户更友好的提示
        if (!isPlayerPrepared) {
            if (isVideoLoaded) {
                Log.d(TAG, "Player is preparing, please wait...");
                showToast("播放器正在准备中，请稍候...");
            } else {
                Log.w(TAG, "Video not loaded yet");
                showToast("视频正在加载中，请稍候...");
            }
            return;
        }

        // 直接查询ExoPlayer的实际播放状态，而不是依赖Fragment的isPlaying标志
        boolean actuallyPlaying = playerManager.isPlaying();
        Log.d(TAG, "Toggle play/pause - current state: " + actuallyPlaying + ", fragment position: " + fragmentPosition);

        if (actuallyPlaying) {
            Log.d(TAG, "Pausing playback");
            playerManager.pause();
            globalManager.stopPlaying(this);
        } else {
            Log.d(TAG, "Starting playback");
            // 通过全局管理器开始播放，这会自动停止其他Fragment
            globalManager.startPlaying(this);
            playerManager.play();
        }

        // 立即更新按钮状态
        updatePlayPauseButton();
    }

    private void retryLoadVideo() {
        Log.d(TAG, "Retrying video load - resetting state and forcing reload");
        hideError();

        // 重置加载状态，强制重新获取播放地址
        resetLoadingState();

        // 重新加载视频数据
        loadVideoData();
    }

    private void showLoading() {
        swipeRefreshVideoLoading.setVisibility(View.VISIBLE);
        swipeRefreshVideoLoading.setRefreshing(true);
        btnPlayPause.setVisibility(View.GONE);
        layoutError.setVisibility(View.GONE);
    }

    private void hideLoading() {
        swipeRefreshVideoLoading.setVisibility(View.GONE);
        swipeRefreshVideoLoading.setRefreshing(false);
    }

    private void showError(String message) {
        hideLoading();
        btnPlayPause.setVisibility(View.GONE);

        tvErrorMessage.setText(message);
        layoutError.setVisibility(View.VISIBLE);

        Log.e(TAG, "Video error: " + message);
    }

    private void hideError() {
        layoutError.setVisibility(View.GONE);
    }

    public void showPlayControls() {
        if (btnPlayPause == null || btnSeekBackward == null || btnSeekForward == null) {
            Log.w(TAG, "Control buttons are null, cannot show controls");
            return;
        }

        Log.d(TAG, "Showing play controls for fragment " + fragmentPosition);
        areControlsVisible = true;

        // 更新播放按钮状态
        updatePlayPauseButton();

        btnSeekBackward.setVisibility(View.VISIBLE);
        btnSeekForward.setVisibility(View.VISIBLE);

        // 取消之前的定时器
        if (controlsHandler != null) {
            controlsHandler.removeCallbacks(hideControlsRunnable);
            // 只在播放时才设置自动隐藏，暂停时保持显示
            if (playerManager != null && playerManager.isPlaying()) {
                controlsHandler.postDelayed(hideControlsRunnable, 4000); // 增加到4秒
            }
        }
    }

    /**
     * 更新播放/暂停按钮状态
     */
    private void updatePlayPauseButton() {
        if (btnPlayPause == null) {
            return;
        }

        // 根据当前播放状态设置播放按钮图标和可见性
        if (playerManager != null) {
            boolean actuallyPlaying = playerManager.isPlaying();
            btnPlayPause.setImageResource(actuallyPlaying ? R.drawable.play_ic_pause : R.drawable.play_ic_play);
            btnPlayPause.setVisibility(View.VISIBLE);
            Log.d(TAG, "Updated play/pause button - playing: " + actuallyPlaying + ", prepared: " + isPlayerPrepared);

            // 同步Fragment的播放状态
            isPlaying = actuallyPlaying;
        } else {
            // 如果播放器还没准备好，显示播放按钮
            btnPlayPause.setImageResource(R.drawable.play_ic_play);
            btnPlayPause.setVisibility(View.VISIBLE);
            isPlaying = false;
            Log.d(TAG, "Updated play/pause button - player not ready");
        }
    }

    public void hidePlayControls() {
        if (btnPlayPause == null || btnSeekBackward == null || btnSeekForward == null) {
            return;
        }

        Log.d(TAG, "Hiding play controls for fragment " + fragmentPosition);
        areControlsVisible = false;

        btnPlayPause.setVisibility(View.GONE);
        btnSeekBackward.setVisibility(View.GONE);
        btnSeekForward.setVisibility(View.GONE);

        // 取消定时器
        if (controlsHandler != null) {
            controlsHandler.removeCallbacks(hideControlsRunnable);
        }
    }

    private void seekRelative(long deltaMs) {
        if (playerManager == null || !isPlayerPrepared) {
            Log.w(TAG, "Cannot seek: player not ready");
            showToast("播放器未准备就绪");
            return;
        }

        long currentPosition = playerManager.getCurrentPosition();
        long newPosition = Math.max(0, currentPosition + deltaMs);
        long duration = playerManager.getDuration();

        if (duration > 0) {
            newPosition = Math.min(newPosition, duration);
        }

        Log.d(TAG, "Seeking from " + currentPosition + " to " + newPosition + " (delta: " + deltaMs + ") for fragment " + fragmentPosition);
        playerManager.seekTo(newPosition);

        // 显示反馈信息
        String seekInfo = deltaMs > 0 ? "快进 " + (deltaMs / 1000) + "秒" : "快退 " + (-deltaMs / 1000) + "秒";
        showToast(seekInfo);
    }

    private void showBuffering() {
        Log.d(TAG, "Showing buffering indicator");
        // 缓冲时隐藏控件，但不直接操作btnPlayPause
        hidePlayControls();
    }

    private void hideBuffering() {
        Log.d(TAG, "Hiding buffering indicator");
        // 缓冲结束，不需要特殊处理
    }

    private void updateDebugInfo() {
        if (videoModel != null) {
            tvVideoTitle.setText(videoModel.getTitle());
            tvVideoResolution.setText("分辨率: " + (testVideoModel != null ? testVideoModel.getResolution() : "未知"));
            tvVideoDuration.setText("时长: " + (testVideoModel != null ? testVideoModel.getDurationText() : videoModel.getDuration()));
        }
    }

    private void toggleDebugInfo() {
        if (layoutVideoInfo.getVisibility() == View.VISIBLE) {
            layoutVideoInfo.setVisibility(View.GONE);
        } else {
            layoutVideoInfo.setVisibility(View.VISIBLE);
            updateDebugInfo();
        }
    }

    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }

    // Fragment生命周期管理
    // 注意：setUserVisibleHint已废弃，现在使用BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT
    // 确保只有当前可见的Fragment会调用onResume，其他Fragment调用onPause

    @Override
    public void onResume() {
        super.onResume();
        isVisible = true;
        Log.d(TAG, "onResume called for position " + fragmentPosition + " - Fragment is now visible");

        // 注册到全局管理器
        globalManager.registerFragment(this);

        // 只有当Fragment真正可见时才开始播放
        if (getUserVisibleHint()) {
            // 通过全局管理器开始播放，这会自动停止其他Fragment
            globalManager.startPlaying(this);

            if (!isVideoLoaded) {
                shouldAutoPlay = true;
                Log.d(TAG, "Fragment resuming, will auto-play when video loads");
                // 只有在第一次加载时才调用loadVideoData，避免重复请求
                if (videoModel != null && !hasAttemptedLoad) {
                    hasAttemptedLoad = true;
                    loadVideoData(); // 确保视频开始加载
                }
            } else if (isPlayerPrepared && playerManager != null) {
                Log.d(TAG, "Fragment resuming, starting playback");
                playerManager.play();
            }
        }

        // Resume danmaku
        if (danmakuController != null && danmakuController.isShowing()) {
            danmakuController.resume();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        isVisible = false;
        Log.d(TAG, "onPause called for position " + fragmentPosition + " - Fragment is no longer visible");

        // 通知全局管理器停止播放
        globalManager.stopPlaying(this);

        // 立即暂停播放，防止后台播放
        if (playerManager != null && playerManager.isPlaying()) {
            Log.d(TAG, "Force pausing playback in onPause for position " + fragmentPosition);
            playerManager.pause();
        }

        // Pause danmaku
        if (danmakuController != null && danmakuController.isShowing()) {
            danmakuController.pause();
        }
    }

    // handleVisibilityChange方法已移除，现在使用标准的Fragment生命周期方法

    @Override
    public void onDestroyView() {
        super.onDestroyView();

        // 从全局管理器注销
        globalManager.unregisterFragment(this);

        // 释放VideoPlayerManager资源
        if (playerManager != null) {
            playerManager.setPlayerListener(null);
            playerManager.release();
            playerManager = null;
            Log.d(TAG, "Fragment view destroyed, PlayerManager released");
        }

        // 重置状态，允许重新加载
        isVideoLoaded = false;
        isPlayerPrepared = false;
        isPlaying = false;
        shouldAutoPlay = false;

        // Clean up controls handler
        if (controlsHandler != null) {
            controlsHandler.removeCallbacks(hideControlsRunnable);
            controlsHandler = null;
        }

        // Stop and release danmaku
        if (danmakuController != null) {
            danmakuController.stop();
            danmakuController = null;
        }

        // 清理播放进度上报
        onFragmentDestroyed();
    }

    /**
     * 初始化弹幕
     */
    private void initializeDanmaku() {
        if (danmakuController != null) {
            // 获取用户的弹幕设置
            loadDanmakuSettings();

            // 启动弹幕引擎
            danmakuController.start();

            // 根据设置决定是否显示弹幕
            if (isDanmakuEnabled) {
                danmakuController.show();
                // 只有在弹幕开启时才添加测试弹幕
                danmakuController.addTestDanmakus();
                Log.d(TAG, "Danmaku initialized and shown");
            } else {
                danmakuController.hide();
                Log.d(TAG, "Danmaku initialized but hidden");
            }
        }
    }

    /**
     * 加载弹幕设置
     */
    private void loadDanmakuSettings() {
        try {
            // 尝试从全局设置获取弹幕状态
            if (getContext() != null) {
                VideoPlayerPreferences preferences = new VideoPlayerPreferences(getContext());
                isDanmakuEnabled = preferences.isDanmakuEnabled();
                Log.d(TAG, "Loaded danmaku setting: " + isDanmakuEnabled);
            }
        } catch (Exception e) {
            Log.w(TAG, "Failed to load danmaku settings, using default", e);
            isDanmakuEnabled = true; // 默认开启
        }
    }

    /**
     * 切换弹幕显示状态
     */
    public void toggleDanmaku(boolean enabled) {
        isDanmakuEnabled = enabled;
        Log.d(TAG, "Toggle danmaku: " + enabled);

        if (danmakuController != null) {
            if (enabled) {
                // 确保弹幕引擎已启动
                if (!danmakuController.isShowing()) {
                    danmakuController.start();
                }
                danmakuController.show();
                // 如果弹幕刚开启且没有弹幕内容，添加测试弹幕
                danmakuController.addTestDanmakus();
                Log.d(TAG, "Danmaku shown");
            } else {
                danmakuController.hide();
                Log.d(TAG, "Danmaku hidden");
            }
        }

        // 同时控制字幕显示
        if (playerManager != null) {
            playerManager.setSubtitlesEnabled(enabled);
            Log.d(TAG, "Subtitles " + (enabled ? "enabled" : "disabled"));
        }
    }

    /**
     * 处理字幕信息
     */
    private void processSubtitles(java.util.List<com.android.video.model.SubtitleInfo> subtitles) {
        if (subtitles == null || subtitles.isEmpty()) {
            Log.d(TAG, "No subtitles available");
            return;
        }

        Log.d(TAG, "Processing " + subtitles.size() + " subtitle(s):");
        java.util.List<com.android.video.model.SubtitleInfo> validSubtitles = new java.util.ArrayList<>();

        for (com.android.video.model.SubtitleInfo subtitle : subtitles) {
            Log.d(TAG, "  - Language: " + subtitle.getLanguageTypeDescription() +
                      ", Name: " + subtitle.getSubtitleName() +
                      ", URL: " + subtitle.getSubtitleFileUrl() +
                      ", Valid: " + subtitle.hasValidUrl() +
                      ", Supported: " + subtitle.isSupportedFormat());

            // 检查字幕URL是否有效
            if (!subtitle.hasValidUrl()) {
                Log.w(TAG, "Invalid subtitle URL: " + subtitle.getSubtitleFileUrl());
                continue;
            }

            // 检查字幕格式是否支持
            if (!subtitle.isSupportedFormat()) {
                Log.w(TAG, "Unsupported subtitle format: " + subtitle.getFileExtension());
                continue;
            }

            validSubtitles.add(subtitle);
        }

        if (!validSubtitles.isEmpty()) {
            // 将字幕信息传递给播放器管理器
            if (playerManager != null) {
                playerManager.setSubtitles(validSubtitles);
                Log.d(TAG, "Set " + validSubtitles.size() + " valid subtitle(s) to player manager");
            }
        }

        Log.d(TAG, "Subtitle processing completed. " + validSubtitles.size() + " valid subtitle(s) ready for playback.");
    }

    /**
     * 检查是否是来自Discover页面的视频
     */
    private boolean isDiscoverPageVideo() {
        // 如果videoModel有videoUri但没有filmLanguageInfoId，通常是来自Discover页面的直接URL
        return videoModel != null &&
               videoModel.getVideoUri() != null &&
               (videoModel.getFilmLanguageInfoId() == null || videoModel.getFilmLanguageInfoId().isEmpty());
    }

    /**
     * 检查Discover页面视频是否过期
     */
    private boolean isDiscoverVideoExpired() {
        if (videoModel == null) {
            return false;
        }

        boolean expired = videoModel.isVideoExpired();
        if (expired) {
            Log.w(TAG, "Discover page video URL is expired: " + videoModel.getVideoExpirationTime());
        } else {
            Log.d(TAG, "Discover page video URL is still valid");
        }

        return expired;
    }

    // VideoPlayerListener接口实现
    @Override
    public void onPrepared() {
        uiHandler.post(() -> {
            Log.d(TAG, "onPrepared called - video is ready for position " + fragmentPosition);
            isPlayerPrepared = true;
            isVideoLoaded = true;

            // 立即隐藏加载动画和缓冲指示器
            hideLoading();
            hideBuffering();
            hideError();

            // 初始化弹幕（根据用户设置决定是否启动）
            initializeDanmaku();

            // 恢复播放进度（针对My List历史记录）
            restoreSavedProgress();

            // 使用新的生命周期管理，只有当Fragment处于Resume状态时才自动播放
            if (isVisible && shouldAutoPlay) {
                Log.d(TAG, "Auto-playing video for visible fragment at position " + fragmentPosition);
                playerManager.play();
                shouldAutoPlay = false;
            } else {
                Log.d(TAG, "Video prepared but not auto-playing (visible: " + isVisible + ", shouldAutoPlay: " + shouldAutoPlay + ")");
            }

            // 更新播放按钮状态
            updatePlayPauseButton();
            Log.d(TAG, "Video prepared for: " + (videoModel != null ? videoModel.getTitle() : "unknown"));
        });
    }

    @Override
    public void onPlaybackStarted() {
        uiHandler.post(() -> {
            Log.d(TAG, "onPlaybackStarted called - updating UI state");
            isPlaying = true;

            // 通知全局管理器当前Fragment正在播放
            globalManager.startPlaying(this);

            // 开始播放进度上报
            startProgressReporting();

            // 更新播放按钮状态
            updatePlayPauseButton();

            // 播放时隐藏加载和缓冲指示器
            hideBuffering();
            hideLoading();

            // 播放开始时不自动显示控制按钮，保持隐藏状态
            // 用户需要点击屏幕才能显示控制按钮

            // Resume danmaku when video starts playing
            if (danmakuController != null && danmakuController.isShowing()) {
                danmakuController.resume();
            }

            Log.d(TAG, "Playback started for: " + (videoModel != null ? videoModel.getTitle() : "unknown"));
        });
    }

    @Override
    public void onPlaybackPaused() {
        uiHandler.post(() -> {
            Log.d(TAG, "onPlaybackPaused called - updating UI state");
            isPlaying = false;

            // 更新播放按钮状态
            updatePlayPauseButton();

            // 暂停时显示控件并取消自动隐藏
            showPlayControls();
            if (controlsHandler != null) {
                controlsHandler.removeCallbacks(hideControlsRunnable);
            }

            // Pause danmaku when video is paused
            if (danmakuController != null && danmakuController.isShowing()) {
                danmakuController.pause();
            }

            // 暂停时上报当前进度
            reportCurrentProgress(false);

            Log.d(TAG, "Playback paused, showing controls");
        });
    }

    @Override
    public void onPlaybackStopped() {
        uiHandler.post(() -> {
            isPlaying = false;
            updatePlayPauseButton();

            // 播放完成时上报完成状态
            reportCurrentProgress(true);
            stopProgressReporting();
        });
    }

    @Override
    public void onPlaybackCompleted() {
        uiHandler.post(() -> {
            isPlaying = false;
            updatePlayPauseButton();
        });
    }

    @Override
    public void onPositionChanged(long position, long duration) {
        uiHandler.post(() -> {
            if (layoutVideoInfo.getVisibility() == View.VISIBLE) {
                String positionText = String.format("Position: %d / %d", position / 1000, duration / 1000);
                tvVideoPosition.setText(positionText);
            }
        });
    }

    @Override
    public void onBufferingUpdate(boolean isBuffering, int bufferedPercentage) {
        uiHandler.post(() -> {
            Log.d(TAG, "onBufferingUpdate: isBuffering=" + isBuffering + ", percentage=" + bufferedPercentage);
            if (isBuffering) {
                // 只有在视频还没准备好时才显示缓冲指示器
                if (!isPlayerPrepared) {
                    showBuffering();
                }
            } else {
                // 停止缓冲时，隐藏所有加载相关的指示器
                hideBuffering();
                if (bufferedPercentage >= 100) {
                    hideLoading();
                }
            }
        });
    }

    @Override
    public void onPlaybackSpeedChanged(float speed) {
        // 播放速度变化处理
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {
        uiHandler.post(() -> {
            if (layoutVideoInfo.getVisibility() == View.VISIBLE) {
                tvVideoResolution.setText(String.format("分辨率: %dx%d", width, height));
            }
        });
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        // 播放器状态变化处理
    }

    @Override
    public void onVolumeChanged(float volume) {
        // 音量变化处理
    }

    @Override
    public void onError(String error) {
        uiHandler.post(() -> {
            isVideoLoaded = false;
            isPlayerPrepared = false;
            showError(error);
        });
    }

    // 公共方法
    public void setPlayerManager(VideoPlayerManager playerManager) {
        // 不再接受外部的PlayerManager，每个Fragment使用自己的实例
        Log.d(TAG, "setPlayerManager called but ignored - using internal PlayerManager");
    }

    public VideoModel getVideoModel() {
        return videoModel;
    }

    public boolean isPlayerPrepared() {
        return isPlayerPrepared;
    }

    public boolean isPlaying() {
        return isPlaying;
    }

    // pausePlayback和resumePlayback方法已移除
    // 现在使用标准的Fragment生命周期方法（onResume/onPause）来管理播放状态

    /**
     * 获取VideoPlayerManager实例
     */
    public VideoPlayerManager getPlayerManager() {
        return playerManager;
    }

    /**
     * 获取Fragment位置
     */
    public int getFragmentPosition() {
        return fragmentPosition;
    }

    /**
     * 通知Activity切换控制栏可见性
     */
    private void notifyActivityToggleControls() {
        if (getActivity() instanceof com.android.video.ui.activity.VideoPlayerActivity) {
            ((com.android.video.ui.activity.VideoPlayerActivity) getActivity()).toggleControlsVisibility();
        }
    }

    /**
     * 检查控制按钮是否可见
     */
    public boolean areControlsVisible() {
        return areControlsVisible;
    }

    /**
     * 初始隐藏播放控制按钮 - 供DiscoverFragment调用
     */
    public void hidePlayControlsInitially() {
        if (uiHandler != null) {
            uiHandler.post(() -> {
                areControlsVisible = false;
                if (btnPlayPause != null) {
                    btnPlayPause.setVisibility(View.GONE);
                }
                if (btnSeekBackward != null) {
                    btnSeekBackward.setVisibility(View.GONE);
                }
                if (btnSeekForward != null) {
                    btnSeekForward.setVisibility(View.GONE);
                }
                // 取消任何自动隐藏的定时器
                if (controlsHandler != null) {
                    controlsHandler.removeCallbacks(hideControlsRunnable);
                }
                Log.d(TAG, "Play controls hidden initially for fragment " + fragmentPosition);
            });
        }
    }

    /**
     * 为悬浮窗播放隐藏PlayerView
     */
    public void hidePlayerViewForFloating() {
        try {
            if (playerView != null) {
                // 设置黑色背景
                playerView.setBackgroundColor(android.graphics.Color.BLACK);

                // 暂时移除播放器，显示黑色背景
                if (playerManager != null && playerView.getPlayer() != null) {
                    playerView.setPlayer(null);
                    Log.d(TAG, "PlayerView hidden for floating window at position " + fragmentPosition);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide PlayerView for floating window", e);
        }
    }

    /**
     * 恢复PlayerView显示（从悬浮窗返回时调用）
     */
    public void restorePlayerViewFromFloating() {
        try {
            if (playerView != null && playerManager != null) {
                // 恢复播放器
                playerView.setPlayer(playerManager.getExoPlayer());

                // 移除黑色背景
                playerView.setBackgroundColor(android.graphics.Color.TRANSPARENT);

                // 重新设置播放器监听器，确保进度更新正常
                playerManager.setPlayerListener(this);

                // 如果当前Fragment是可见的，重新启动播放
                if (isVisible && getUserVisibleHint()) {
                    // 通过全局管理器重新开始播放
                    globalManager.startPlaying(this);

                    // 如果播放器准备好了，继续播放
                    if (isPlayerPrepared && playerManager.isPlaying()) {
                        // 播放器已经在播放，确保UI状态正确
                        updatePlayPauseButton();
                    }
                }

                Log.d(TAG, "PlayerView restored from floating window at position " + fragmentPosition);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to restore PlayerView from floating window", e);
        }
    }

    // ========== 播放进度上报相关方法 ==========

    /**
     * 开始播放进度上报
     */
    private void startProgressReporting() {
        if (!isProgressReportingEnabled || videoModel == null) {
            return;
        }

        // 取消之前的上报任务
        stopProgressReporting();

        // 创建定时上报任务
        progressReportRunnable = new Runnable() {
            @Override
            public void run() {
                reportCurrentProgress(false);
                // 继续下一次上报
                if (progressReportHandler != null && isProgressReportingEnabled) {
                    progressReportHandler.postDelayed(this, PROGRESS_REPORT_INTERVAL);
                }
            }
        };

        // 开始定时上报
        progressReportHandler.postDelayed(progressReportRunnable, PROGRESS_REPORT_INTERVAL);
        Log.d(TAG, "Started progress reporting for video: " + videoModel.getTitle());
    }

    /**
     * 停止播放进度上报
     */
    private void stopProgressReporting() {
        if (progressReportHandler != null && progressReportRunnable != null) {
            progressReportHandler.removeCallbacks(progressReportRunnable);
            progressReportRunnable = null;
        }
        Log.d(TAG, "Stopped progress reporting");
    }

    /**
     * 上报当前播放进度
     *
     * @param isFinished 是否播放完成
     */
    private void reportCurrentProgress(boolean isFinished) {
        if (!isProgressReportingEnabled || videoModel == null || playerManager == null) {
            return;
        }

        try {
            // 获取当前播放进度
            long currentPosition = playerManager.getCurrentPosition();
            long duration = playerManager.getDuration();

            // 转换为秒
            int progressSeconds = (int) (currentPosition / 1000);
            int durationSeconds = (int) (duration / 1000);

            // 检查是否需要上报（避免重复上报相同进度）
            if (!isFinished && Math.abs(progressSeconds - lastReportedProgress) < 5) {
                return; // 进度变化小于5秒，不上报
            }

            // 获取必要的参数
            String filmLanguageInfoId = videoModel.getFilmLanguageInfoId();
            String chapterId = getCurrentChapterId();

            if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
                Log.w(TAG, "filmLanguageInfoId is null, cannot report progress");
                return;
            }

            if (chapterId == null || chapterId.isEmpty()) {
                Log.w(TAG, "chapterId is null, cannot report progress");
                return;
            }

            // 创建上报请求
            PlayProgressReportModel reportModel = new PlayProgressReportModel(
                filmLanguageInfoId,
                chapterId,
                progressSeconds,
                isFinished,
                durationSeconds
            );

            Log.d(TAG, "Reporting progress: " + reportModel.toString());

            // 发送上报请求
            chapterListApiService.reportPlayProgress(reportModel, new ChapterListApiService.ApiCallback<PlayProgressReportResponseModel>() {
                @Override
                public void onSuccess(PlayProgressReportResponseModel response) {
                    Log.d(TAG, "Progress report success: " + response.getData());
                    lastReportedProgress = progressSeconds;
                }

                @Override
                public void onError(String error) {
                    Log.w(TAG, "Progress report failed: " + error);
                    // 播放进度上报失败不影响播放体验，只记录日志
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "Error reporting progress", e);
        }
    }

    /**
     * 获取当前章节ID
     *
     * @return 当前章节ID，如果无法获取则返回null
     */
    private String getCurrentChapterId() {
        if (videoModel == null) {
            return null;
        }

        // 尝试从VideoModel中获取当前剧集的章节ID
        if (videoModel.getEpisodes() != null && !videoModel.getEpisodes().isEmpty()) {
            int currentEpisode = videoModel.getCurrentEpisode();
            for (com.android.video.model.EpisodeModel episode : videoModel.getEpisodes()) {
                if (episode.getEpisodeNumber() == currentEpisode) {
                    return episode.getId();
                }
            }
        }

        // 如果无法从剧集列表中获取，尝试通过API动态获取章节ID
        // 对于My List历史记录，需要先获取章节列表来找到对应的章节ID
        if (videoModel.getFilmLanguageInfoId() != null && !videoModel.getFilmLanguageInfoId().isEmpty()) {
            Log.d(TAG, "Episodes list is empty, will need to load chapter list from API first");
            return null; // 返回null，让shouldLoadFromApi()返回false，然后在loadVideo()中处理
        }

        Log.w(TAG, "Cannot determine current chapter ID");
        return null;
    }

    /**
     * 检查是否是下载的视频
     */
    private boolean isDownloadedVideo() {
        // 检查videoModel是否来自下载页面
        if (getActivity() instanceof com.android.video.ui.activity.DownloadVideoPlayerActivity) {
            return true;
        }

        // 检查videoModel的特殊标识
        if (videoModel != null && "Downloaded".equals(videoModel.getCategory())) {
            return true;
        }

        return false;
    }

    /**
     * 获取下载视频的本地路径
     */
    private String getDownloadedVideoPath() {
        if (getActivity() instanceof com.android.video.ui.activity.DownloadVideoPlayerActivity) {
            com.android.video.ui.activity.DownloadVideoPlayerActivity activity =
                (com.android.video.ui.activity.DownloadVideoPlayerActivity) getActivity();

            // 通过Activity获取DownloadVideo对象
            com.android.video.model.DownloadVideo downloadVideo = activity.getDownloadVideo();
            if (downloadVideo != null && downloadVideo.getFilePath() != null) {
                return downloadVideo.getFilePath();
            }
        }

        return null;
    }



    /**
     * 在Fragment销毁时调用
     */
    private void onFragmentDestroyed() {
        // 停止进度上报
        stopProgressReporting();

        // 取消API请求
        if (chapterListApiService != null) {
            chapterListApiService.cancelAllRequests();
        }
    }

    /**
     * 获取用户偏好的视频分辨率
     *
     * @return 分辨率字符串（auto/480/720/1080）
     */
    private String getPreferredResolution() {
        // 这里可以从SharedPreferences或用户设置中获取偏好分辨率
        // 目前默认使用auto，让服务器自动选择最佳分辨率
        return "auto";
    }

    /**
     * 获取播放地址（支持指定分辨率）
     *
     * @param resolution 分辨率（auto/480/720/1080）
     * @param callback 回调接口
     */
    public void getPlayUrlWithResolution(String resolution, ChapterListApiService.ApiCallback<PlayUrlResponseModel> callback) {
        if (!shouldLoadFromApi()) {
            callback.onError("缺少必要的API参数");
            return;
        }

        String filmLanguageInfoId = videoModel.getFilmLanguageInfoId();
        if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
            callback.onError("缺少短剧语言信息ID");
            return;
        }

        String chapterId = getCurrentChapterId();
        if (chapterId == null) {
            callback.onError("无法获取章节ID");
            return;
        }

        int operationType = 1; // 播放
        chapterListApiService.getPlayUrl(filmLanguageInfoId, chapterId, resolution, operationType, callback);
    }

    /**
     * 切换视频分辨率
     *
     * @param resolution 新的分辨率（auto/480/720/1080）
     */
    public void switchResolution(String resolution) {
        if (!shouldLoadFromApi()) {
            showToast("当前视频不支持分辨率切换");
            return;
        }

        showLoading();

        getPlayUrlWithResolution(resolution, new ChapterListApiService.ApiCallback<PlayUrlResponseModel>() {
            @Override
            public void onSuccess(PlayUrlResponseModel response) {
                if (getActivity() == null || isDetached()) {
                    return;
                }

                uiHandler.post(() -> {
                    try {
                        if (response.getData() != null && response.getData().hasValidVideoUrl()) {
                            String videoUrl = response.getData().getVideoUrl();

                            // 记住当前播放位置
                            long currentPosition = playerManager != null ? playerManager.getCurrentPosition() : 0;

                            // 加载新的视频URL
                            loadVideoWithUrl(videoUrl);

                            // 恢复播放位置
                            if (currentPosition > 0 && playerManager != null) {
                                playerManager.seekTo(currentPosition);
                            }

                            showToast("已切换到" + getResolutionDisplayName(resolution));
                        } else {
                            showError("获取的视频地址无效");
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Error switching resolution", e);
                        showError("切换分辨率失败: " + e.getMessage());
                    }
                });
            }

            @Override
            public void onError(String error) {
                if (getActivity() == null || isDetached()) {
                    return;
                }

                Log.e(TAG, "Failed to switch resolution: " + error);
                uiHandler.post(() -> {
                    showError("切换分辨率失败: " + error);
                });
            }
        });
    }

    /**
     * 获取分辨率显示名称
     *
     * @param resolution 分辨率字符串
     * @return 显示名称
     */
    private String getResolutionDisplayName(String resolution) {
        switch (resolution) {
            case "auto":
                return "自动";
            case "480":
                return "480P";
            case "720":
                return "720P";
            case "1080":
                return "1080P";
            default:
                return resolution;
        }
    }

    /**
     * 切换到指定剧集
     *
     * @param episodeNumber 剧集编号
     */
    public void switchToEpisode(int episodeNumber) {
        if (videoModel == null) {
            Log.w(TAG, "VideoModel is null, cannot switch episode");
            return;
        }

        Log.d(TAG, "Switching to episode: " + episodeNumber);

        // 更新VideoModel的当前剧集
        videoModel.setCurrentEpisode(episodeNumber);

        // 停止当前播放
        if (playerManager != null) {
            playerManager.pause();
        }

        // 重新加载视频（这会触发新的播放地址获取）
        loadVideo();

        Log.d(TAG, "Episode switch initiated for episode: " + episodeNumber);
    }


}
