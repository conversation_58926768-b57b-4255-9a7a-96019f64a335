package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.model.response.DownloadProgressResponseModel;
import com.android.video.model.response.DownloadProgressReportResponseModel;
import com.android.video.model.request.DownloadProgressReportModel;
import com.android.video.constants.DiscoverApiConstantsUtils;
import com.android.video.utils.ApiHeaderUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 下载进度API服务类
 * <p>
 * 提供下载进度查询和上报相关的网络请求功能。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadProgressApiService {

    private static final String TAG = "DownloadProgressApiService";
    private static final String API_DOWNLOAD_PROGRESS = "/app/download/progress";
    private static final MediaType JSON_MEDIA_TYPE = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    // 单例实例
    private static volatile DownloadProgressApiService instance;

    /**
     * 下载进度获取回调接口
     */
    public interface DownloadProgressCallback {
        /**
         * 获取下载进度成功
         * @param response 下载进度响应数据
         */
        void onSuccess(DownloadProgressResponseModel response);

        /**
         * 获取下载进度失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 下载进度上报回调接口
     */
    public interface DownloadProgressReportCallback {
        /**
         * 上报下载进度成功
         * @param response 下载进度上报响应数据
         */
        void onSuccess(DownloadProgressReportResponseModel response);

        /**
         * 上报下载进度失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造函数
     */
    private DownloadProgressApiService() {
        this.client = new OkHttpClient.Builder()
                .connectTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .readTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .writeTimeout(30, java.util.concurrent.TimeUnit.SECONDS)
                .build();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newFixedThreadPool(3);
        this.gson = new Gson();
    }

    /**
     * 获取单例实例
     * @return DownloadProgressApiService实例
     */
    public static DownloadProgressApiService getInstance() {
        if (instance == null) {
            synchronized (DownloadProgressApiService.class) {
                if (instance == null) {
                    instance = new DownloadProgressApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 获取下载进度
     * <p>
     * 异步获取指定下载记录的实时进度。
     * </p>
     *
     * @param downloadRecordId 下载记录ID
     * @param callback 回调接口
     */
    public void getDownloadProgress(String downloadRecordId, DownloadProgressCallback callback) {
        // 验证参数
        if (downloadRecordId == null || downloadRecordId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("downloadRecordId不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + API_DOWNLOAD_PROGRESS;

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append("downloadRecordId").append("=").append(downloadRecordId.trim());

        String finalUrl = urlBuilder.toString();

        // 获取请求头（包含认证信息）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建GET请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.header(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取下载进度请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "downloadRecordId: " + downloadRecordId);
        Log.d(TAG, "请求头数量: " + headers.size());
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "下载进度请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 下载进度API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDownloadProgressResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "下载进度响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析下载进度响应数据
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDownloadProgressResponse(String responseBody, DownloadProgressCallback callback) {
        try {
            DownloadProgressResponseModel response = gson.fromJson(responseBody, DownloadProgressResponseModel.class);

            if (response != null) {
                Log.d(TAG, "下载进度解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                });
            } else {
                Log.e(TAG, "下载进度响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "下载进度JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 上报下载进度
     * <p>
     * 向服务器上报用户的视频下载进度，包括下载记录ID、下载进度和下载状态。
     * </p>
     *
     * @param reportModel 下载进度上报数据
     * @param callback 回调接口
     */
    public void reportDownloadProgress(DownloadProgressReportModel reportModel, DownloadProgressReportCallback callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (reportModel == null) {
            callback.onError("Report model is null");
            return;
        }

        if (!reportModel.isValid()) {
            String error = reportModel.getValidationError();
            Log.e(TAG, "Invalid report model: " + error);
            callback.onError("Invalid report data: " + error);
            return;
        }

        // 构建请求URL
        String apiUrl = DiscoverApiConstantsUtils.buildReportDownloadProgressUrl(
            EnvironmentConfigUtils.getApiBaseUrl()
        );

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 将请求模型转换为JSON
        String jsonBody = gson.toJson(reportModel);
        RequestBody requestBody = RequestBody.create(jsonBody, JSON_MEDIA_TYPE);

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 下载进度上报API请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "请求头: " + headers);
        Log.d(TAG, "请求体: " + jsonBody);
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    String errorMsg = "下载进度上报网络请求失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError(errorMsg);
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 下载进度上报API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDownloadProgressReportResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "下载进度上报响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析下载进度上报响应数据
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseDownloadProgressReportResponse(String responseBody, DownloadProgressReportCallback callback) {
        try {
            DownloadProgressReportResponseModel response = gson.fromJson(responseBody, DownloadProgressReportResponseModel.class);

            if (response != null) {
                Log.d(TAG, "下载进度上报解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        if (response.isSuccess()) {
                            Log.d(TAG, "下载进度上报成功: " + response.getData());
                            callback.onSuccess(response);
                        } else {
                            String errorMsg = response.getErrorMessage();
                            Log.e(TAG, "下载进度上报API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    }
                });
            } else {
                Log.e(TAG, "下载进度上报响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "下载进度上报JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }
}
