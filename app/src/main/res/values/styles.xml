<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- Video Player Styles -->
    
    <!-- 视频播放器标题样式 -->
    <style name="VideoPlayerTitleText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif-medium</item>
        <item name="android:textSize">@dimen/video_player_title_text_size</item>
        <item name="android:textColor">@color/video_player_text_white</item>
        <item name="android:textStyle">normal</item>
    </style>

    <!-- 视频播放器描述文本样式 -->
    <style name="VideoPlayerDescriptionText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">@dimen/video_player_description_text_size</item>
        <item name="android:textColor">@color/video_player_text_secondary</item>
        <item name="android:textStyle">normal</item>
    </style>
    
    <!-- 视频播放器侧边按钮文本样式 -->
    <style name="VideoPlayerSideButtonText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">@dimen/video_player_side_text_size</item>
        <item name="android:textColor">@color/video_player_text_white</item>
        <item name="android:textStyle">normal</item>
    </style>
    
    <!-- 视频播放器选集按钮文本样式 -->
    <style name="VideoPlayerEpisodeText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">@dimen/video_player_episode_text_size</item>
        <item name="android:textStyle">normal</item>
    </style>
    
    <!-- 视频播放器选集按钮已观看文本样式 -->
    <style name="VideoPlayerEpisodeWatchedText" parent="VideoPlayerEpisodeText">
        <item name="android:textColor">@color/video_player_like_red</item>
    </style>
    
    <!-- 视频播放器选集按钮未观看文本样式 -->
    <style name="VideoPlayerEpisodeUnwatchedText" parent="VideoPlayerEpisodeText">
        <item name="android:textColor">@color/video_player_text_tertiary</item>
    </style>
    
    <!-- 视频播放器弹出选择列表文本样式 -->
    <style name="VideoPlayerPopupText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">@dimen/video_player_popup_text_size</item>
        <item name="android:textColor">@color/video_player_text_white</item>
        <item name="android:textStyle">normal</item>
        <item name="android:gravity">center</item>
    </style>
    
    <!-- 视频播放器弹出选择列表选中文本样式 -->
    <style name="VideoPlayerPopupSelectedText" parent="VideoPlayerPopupText">
        <item name="android:textColor">@color/video_player_popup_selected_text</item>
    </style>
    
    <!-- 视频播放器控制按钮样式 -->
    <style name="VideoPlayerControlButton">
        <item name="android:layout_width">@dimen/video_player_control_button_size</item>
        <item name="android:layout_height">@dimen/video_player_control_button_size</item>
        <item name="android:background">@drawable/video_player_control_button_bg</item>
        <item name="android:scaleType">center</item>
    </style>
    
    <!-- 视频播放器侧边按钮样式 -->
    <style name="VideoPlayerSideButton">
        <item name="android:layout_width">@dimen/video_player_side_button_size</item>
        <item name="android:layout_height">@dimen/video_player_side_button_size</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:scaleType">center</item>
    </style>
    
    <!-- 视频播放器小控制按钮样式 -->
    <style name="VideoPlayerSmallControlButton">
        <item name="android:layout_width">@dimen/video_player_control_small_button_size</item>
        <item name="android:layout_height">@dimen/video_player_episode_button_height</item>
        <item name="android:background">@drawable/video_player_small_button_bg</item>
        <item name="android:scaleType">center</item>
        <item name="android:clickable">true</item>
        <item name="android:focusable">true</item>
    </style>

    <!-- 测试按钮样式 -->
    <style name="TestButtonStyle" parent="Widget.AppCompat.Button">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">14sp</item>
        <item name="android:textStyle">normal</item>
        <item name="android:backgroundTint">@color/video_player_accent</item>
        <item name="android:layout_height">48dp</item>
        <item name="android:textAllCaps">false</item>
        <item name="cornerRadius">8dp</item>
    </style>

    <!-- 视频播放器控制按钮文本样式 -->
    <style name="VideoPlayerControlButtonText">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:fontFamily">sans-serif</item>
        <item name="android:textSize">@dimen/video_player_episode_text_size</item>
        <item name="android:textColor">@color/video_player_text_white</item>
        <item name="android:textStyle">normal</item>
        <item name="android:gravity">center</item>
    </style>

    <!-- 视频播放器小按钮样式 -->
    <style name="VideoPlayerSmallButton">
        <item name="android:layout_width">@dimen/video_player_control_small_button_size</item>
        <item name="android:layout_height">@dimen/video_player_control_small_button_size</item>
        <item name="android:background">@drawable/video_player_small_button_bg</item>
        <item name="android:scaleType">center</item>
    </style>

</resources>
