package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.PointsApiConstantsUtils;
import com.android.video.constants.ProfileApiConstantsUtils;
import com.android.video.model.PointsPackage;
import com.android.video.model.PointsSerial;
import com.android.video.utils.ApiHeaderUtils;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 积分中心API服务类
 * <p>
 * 提供积分中心相关的网络请求功能，包括获取积分套餐列表等。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see PointsApiConstantsUtils
 * @see PointsPackage
 */
public class PointsApiService {

    private static final String TAG = "PointsApiService";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;

    /**
     * 积分套餐列表获取回调接口
     */
    public interface PointsPackageListCallback {
        /**
         * 获取积分套餐列表成功
         * @param packages 积分套餐列表
         */
        void onSuccess(List<PointsPackage> packages);

        /**
         * 获取积分套餐列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 积分流水列表获取回调接口
     */
    public interface PointsSerialListCallback {
        /**
         * 获取积分流水列表成功
         * @param serials 积分流水列表
         * @param hasMore 是否还有更多数据
         */
        void onSuccess(List<PointsSerial> serials, boolean hasMore);

        /**
         * 获取积分流水列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 积分套餐购买支付回调接口
     */
    public interface PointsPaymentCallback {
        /**
         * 支付请求成功
         * @param orderNo 订单编号
         * @param paymentPageUrl 支付页面URL
         */
        void onSuccess(String orderNo, String paymentPageUrl);

        /**
         * 支付请求失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 构造方法
     */
    public PointsApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }

    /**
     * 获取积分套餐列表
     * <p>
     * 异步获取可购买的积分套餐列表。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getPointsPackageList(PointsPackageListCallback callback) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            PointsApiConstantsUtils.API_GET_POINTS_PACKAGE_LIST
        );

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取积分套餐列表请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取积分套餐列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 积分套餐列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parsePointsPackageListResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "积分套餐列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析积分套餐列表响应
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parsePointsPackageListResponse(String responseBody, PointsPackageListCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString("code", "");
            String message = jsonResponse.optString("message", "");

            Log.d(TAG, "积分套餐列表响应解析 - code: " + code + ", message: " + message);

            if ("200".equals(code)) {
                // 解析data数组
                JSONArray dataArray = jsonResponse.optJSONArray("data");
                if (dataArray != null) {
                    List<PointsPackage> packages = new ArrayList<>();

                    for (int i = 0; i < dataArray.length(); i++) {
                        JSONObject packageObject = dataArray.getJSONObject(i);

                        String packageId = packageObject.optString(PointsApiConstantsUtils.FIELD_PACKAGE_ID, "");
                        String packageName = packageObject.optString(PointsApiConstantsUtils.FIELD_PACKAGE_NAME, "");
                        int points = packageObject.optInt(PointsApiConstantsUtils.FIELD_POINTS, 0);
                        int giftPoints = packageObject.optInt(PointsApiConstantsUtils.FIELD_GIFT_POINTS, 0);
                        double price = packageObject.optDouble(PointsApiConstantsUtils.FIELD_PRICE, 0.0);
                        int isDelete = packageObject.optInt(ProfileApiConstantsUtils.FIELD_IS_DELETE, 1);
                        String createTime = packageObject.optString(ProfileApiConstantsUtils.FIELD_CREATE_TIME, "");
                        String updateTime = packageObject.optString(ProfileApiConstantsUtils.FIELD_UPDATE_TIME, "");

                        // 只添加有效的积分套餐
                        if (PointsApiConstantsUtils.isPackageValid(isDelete)) {
                            PointsPackage pointsPackage = new PointsPackage(packageId, packageName, points,
                                                                           giftPoints, price, isDelete, createTime, updateTime);
                            packages.add(pointsPackage);

                            Log.d(TAG, "解析积分套餐: " + packageName + " (ID: " + packageId + ", 积分: " + points + ", 价格: $" + price + ")");
                        }
                    }

                    Log.d(TAG, "成功解析 " + packages.size() + " 个积分套餐");

                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onSuccess(packages);
                        }
                    });
                } else {
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应数据格式错误：缺少data字段");
                        }
                    });
                }
            } else {
                // 获取失败
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("获取积分套餐列表失败: " + message);
                    }
                });
            }

        } catch (JSONException e) {
            Log.e(TAG, "积分套餐列表JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 获取积分流水列表
     * <p>
     * 异步获取用户的积分收支流水记录，支持分页查询。
     * </p>
     *
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param billType 积分流水类型：1-收入 2-支出
     * @param callback 回调接口
     */
    public void getPointsSerialList(int page, int size, int billType, PointsSerialListCallback callback) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            PointsApiConstantsUtils.API_GET_POINTS_SERIAL_LIST
        );

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append(PointsApiConstantsUtils.PARAM_PAGE).append("=").append(page)
                  .append("&").append(PointsApiConstantsUtils.PARAM_SIZE).append("=").append(size)
                  .append("&").append(PointsApiConstantsUtils.PARAM_BILL_TYPE).append("=").append(billType);

        String finalUrl = urlBuilder.toString();

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取积分流水列表请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "页码: " + page + ", 每页数量: " + size + ", 流水类型: " + billType);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取积分流水列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 积分流水列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parsePointsSerialListResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "积分流水列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析积分流水列表响应
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parsePointsSerialListResponse(String responseBody, PointsSerialListCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString("code", "");
            String message = jsonResponse.optString("message", "");

            Log.d(TAG, "积分流水列表响应解析 - code: " + code + ", message: " + message);

            if ("200".equals(code)) {
                // 解析data对象
                JSONObject dataObject = jsonResponse.optJSONObject("data");
                if (dataObject != null) {
                    // 解析records数组
                    JSONArray recordsArray = dataObject.optJSONArray("records");
                    List<PointsSerial> serials = new ArrayList<>();

                    if (recordsArray != null) {
                        for (int i = 0; i < recordsArray.length(); i++) {
                            JSONObject serialObject = recordsArray.getJSONObject(i);

                            String id = serialObject.optString(PointsApiConstantsUtils.FIELD_SERIAL_ID, "");
                            String userId = serialObject.optString(PointsApiConstantsUtils.FIELD_USER_ID, "");
                            String userName = serialObject.optString(PointsApiConstantsUtils.FIELD_USER_NAME, "");
                            int billType = serialObject.optInt(PointsApiConstantsUtils.FIELD_BILL_TYPE, 1);
                            int points = serialObject.optInt(PointsApiConstantsUtils.FIELD_POINTS_CHANGE, 0);
                            String bizType = serialObject.optString(PointsApiConstantsUtils.FIELD_BIZ_TYPE, "");
                            String bizDesc = serialObject.optString(PointsApiConstantsUtils.FIELD_BIZ_DESC, "");
                            int isDelete = serialObject.optInt(ProfileApiConstantsUtils.FIELD_IS_DELETE, 1);
                            String createTime = serialObject.optString(ProfileApiConstantsUtils.FIELD_CREATE_TIME, "");
                            String updateTime = serialObject.optString(ProfileApiConstantsUtils.FIELD_UPDATE_TIME, "");
                            int prePointsBalance = serialObject.optInt(PointsApiConstantsUtils.FIELD_PRE_POINTS_BALANCE, 0);
                            int postPointsBalance = serialObject.optInt(PointsApiConstantsUtils.FIELD_POST_POINTS_BALANCE, 0);

                            // 只添加有效的积分流水
                            if (isDelete == 1) {
                                PointsSerial pointsSerial = new PointsSerial(id, userId, userName, billType, points,
                                                                           bizType, bizDesc, isDelete, createTime, updateTime,
                                                                           prePointsBalance, postPointsBalance);
                                serials.add(pointsSerial);

                                Log.d(TAG, "解析积分流水: " + bizDesc + " (积分: " + points + ", 类型: " + billType + ")");
                            }
                        }
                    }

                    // 判断是否还有更多数据
                    int current = dataObject.optInt("current", 1);
                    int pages = dataObject.optInt("pages", 1);
                    boolean hasMore = current < pages;

                    Log.d(TAG, "成功解析 " + serials.size() + " 条积分流水，当前页: " + current + ", 总页数: " + pages + ", 还有更多: " + hasMore);

                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onSuccess(serials, hasMore);
                        }
                    });
                } else {
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应数据格式错误：缺少data字段");
                        }
                    });
                }
            } else {
                // 获取失败
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("获取积分流水列表失败: " + message);
                    }
                });
            }

        } catch (JSONException e) {
            Log.e(TAG, "积分流水列表JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 购买积分套餐
     * <p>
     * 异步调用支付接口购买积分套餐，获取OneVisionPay支付链接。
     * </p>
     *
     * @param packageId 积分套餐ID
     * @param payAmount 支付金额
     * @param payType 支付方式（ecom或mc）
     * @param callback 回调接口
     */
    public void purchasePointsPackage(String packageId, double payAmount, String payType, PointsPaymentCallback callback) {
        // 参数验证
        if (!PointsApiConstantsUtils.validatePaymentParams(packageId, payAmount, payType)) {
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("支付参数无效");
                }
            });
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            PointsApiConstantsUtils.API_POINTS_BUY
        );

        // 获取请求头（包含认证信息）
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建JSON请求体
        JSONObject requestJson = new JSONObject();
        try {
            requestJson.put(PointsApiConstantsUtils.PARAM_PACKAGE_ID, packageId);
            requestJson.put(PointsApiConstantsUtils.PARAM_PAY_AMOUNT, payAmount);
            requestJson.put(PointsApiConstantsUtils.PARAM_PAY_TYPE, payType);
        } catch (JSONException e) {
            Log.e(TAG, "构建支付请求JSON失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("请求参数构建失败: " + e.getMessage());
                }
            });
            return;
        }

        RequestBody requestBody = RequestBody.create(requestJson.toString(), JSON);

        // 构建POST请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.header(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 积分套餐支付请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "套餐ID: " + packageId);
        Log.d(TAG, "支付金额: " + payAmount);
        Log.d(TAG, "支付方式: " + payType + " (" + PointsApiConstantsUtils.getPayTypeDescription(payType) + ")");
        Log.d(TAG, "请求体: " + requestJson.toString());
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "积分套餐支付请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 积分套餐支付API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parsePaymentResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "积分套餐支付响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析支付响应数据
     * <p>
     * 解析支付接口返回的JSON数据，提取订单号和支付页面URL。
     * </p>
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parsePaymentResponse(String responseBody, PointsPaymentCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "支付请求失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("支付失败: " + message);
                    }
                });
                return;
            }

            // 获取data字段
            JSONObject dataObject = jsonResponse.optJSONObject(BaseApiConstantsUtils.FIELD_DATA);
            if (dataObject == null) {
                Log.e(TAG, "支付响应中没有data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("支付响应数据格式错误");
                    }
                });
                return;
            }

            // 提取订单号和支付页面URL
            String orderNo = dataObject.optString(PointsApiConstantsUtils.FIELD_ORDER_NO);
            String paymentPageUrl = dataObject.optString(PointsApiConstantsUtils.FIELD_PAYMENT_PAGE_URL);

            if (orderNo.isEmpty() || paymentPageUrl.isEmpty()) {
                Log.e(TAG, "支付响应中缺少必要字段 - orderNo: " + orderNo + ", paymentPageUrl: " + paymentPageUrl);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("支付响应数据不完整");
                    }
                });
                return;
            }

            Log.d(TAG, "支付请求成功 - 订单号: " + orderNo + ", 支付链接: " + paymentPageUrl);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(orderNo, paymentPageUrl);
                }
            });

        } catch (JSONException e) {
            Log.e(TAG, "支付响应JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 获取单例实例
     */
    private static PointsApiService instance;

    public static synchronized PointsApiService getInstance() {
        if (instance == null) {
            instance = new PointsApiService();
        }
        return instance;
    }
}
