package com.android.video.config;

import android.content.Context;
import android.util.Log;

import com.bumptech.glide.Glide;
import com.bumptech.glide.GlideBuilder;
import com.bumptech.glide.Registry;
import com.bumptech.glide.annotation.GlideModule;
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory;
import com.bumptech.glide.load.engine.cache.LruResourceCache;
import com.bumptech.glide.load.engine.cache.MemorySizeCalculator;
import com.bumptech.glide.module.AppGlideModule;

/**
 * 视频播放器应用的Glide配置模块
 * 优化图片缓存策略和加载性能
 * <AUTHOR> Team
 */
@GlideModule
public final class VideoPlayerGlideModule extends AppGlideModule {
    
    private static final String TAG = "VideoPlayerGlideModule";
    
    // 磁盘缓存大小：250MB
    private static final int DISK_CACHE_SIZE = 250 * 1024 * 1024;
    
    @Override
    public void applyOptions(Context context, GlideBuilder builder) {
        Log.d(TAG, "Configuring Glide options");
        
        // 配置内存缓存
        configureMemoryCache(context, builder);
        
        // 配置磁盘缓存
        configureDiskCache(context, builder);
        
        // 配置日志级别
        if (EnvironmentConfigUtils.isDebugMode()) {
            builder.setLogLevel(Log.DEBUG);
        } else {
            builder.setLogLevel(Log.ERROR);
        }
        
        Log.d(TAG, "Glide configuration completed");
    }
    
    /**
     * 配置内存缓存
     */
    private void configureMemoryCache(Context context, GlideBuilder builder) {
        MemorySizeCalculator calculator = new MemorySizeCalculator.Builder(context)
                .setMemoryCacheScreens(2) // 缓存2个屏幕的图片
                .setBitmapPoolScreens(3)  // Bitmap池缓存3个屏幕
                .build();
        
        // 设置内存缓存大小
        builder.setMemoryCache(new LruResourceCache(calculator.getMemoryCacheSize()));
        
        Log.d(TAG, "Memory cache configured: " + calculator.getMemoryCacheSize() + " bytes");
    }
    
    /**
     * 配置磁盘缓存
     */
    private void configureDiskCache(Context context, GlideBuilder builder) {
        // 使用内部缓存目录，系统会自动管理
        builder.setDiskCache(new InternalCacheDiskCacheFactory(context, "image_cache", DISK_CACHE_SIZE));
        
        Log.d(TAG, "Disk cache configured: " + DISK_CACHE_SIZE + " bytes");
    }
    
    @Override
    public void registerComponents(Context context, Glide glide, Registry registry) {
        // 这里可以注册自定义的组件，如自定义的ModelLoader等
        // 目前使用默认配置
        Log.d(TAG, "Glide components registered");
    }
    
    @Override
    public boolean isManifestParsingEnabled() {
        // 禁用清单解析以提高启动性能
        return false;
    }
}
