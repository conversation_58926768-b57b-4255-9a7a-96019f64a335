<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/continue_playing_button_pressed" />
            <corners android:radius="@dimen/continue_playing_button_corner_radius" />
        </shape>
    </item>

    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/continue_playing_button_bg" />
            <corners android:radius="@dimen/continue_playing_button_corner_radius" />
        </shape>
    </item>

</selector>
