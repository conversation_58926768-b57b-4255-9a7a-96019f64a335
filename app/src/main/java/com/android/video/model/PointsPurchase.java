package com.android.video.model;

/**
 * Points purchase记录数据模型
 */
public class PointsPurchase {
    private String packageName;     // 积分套餐名称
    private String dateTime;        // 日期时间
    private String price;           // 价格
    private int points;             // 积分数量
    private int bonusPoints;        // 赠送积分

    public PointsPurchase() {
    }

    public PointsPurchase(String packageName, String dateTime, String price, int points, int bonusPoints) {
        this.packageName = packageName;
        this.dateTime = dateTime;
        this.price = price;
        this.points = points;
        this.bonusPoints = bonusPoints;
    }

    // Getters and Setters
    public String getPackageName() {
        return packageName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public int getBonusPoints() {
        return bonusPoints;
    }

    public void setBonusPoints(int bonusPoints) {
        this.bonusPoints = bonusPoints;
    }
}
