package com.android.video.model.response;

import com.android.video.model.SubtitleInfo;
import com.android.video.model.VideoQualityInfo;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 播放地址响应数据模型
 * <p>
 * 用于映射播放地址API返回的JSON数据结构。
 * 包含视频URL、过期时间、字幕信息和质量选项等详细信息。
 * </p>
 *
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
 *     "videoUrl": "http://test.mp4",
 *     "expireTime": 1752918434930,
 *     "downloadRecordId": "292f45974e231e177271f8fc1c9e639c",
 *     "subtitles": [...],
 *     "qualities": [...]
 *   }
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class PlayUrlResponseModel {

    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;

    /**
     * 播放地址数据
     */
    @SerializedName("data")
    private PlayUrlData data;

    /**
     * 默认构造函数
     */
    public PlayUrlResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 播放地址数据
     */
    public PlayUrlResponseModel(String code, String message, PlayUrlData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public PlayUrlData getData() {
        return data;
    }

    public void setData(PlayUrlData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取错误信息
     *
     * @return 如果请求失败，返回错误信息；否则返回null
     */
    public String getErrorMessage() {
        return isSuccess() ? null : message;
    }

    @Override
    public String toString() {
        return "PlayUrlResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 播放地址数据
     * <p>
     * 包含视频播放所需的完整信息。
     * </p>
     */
    public static class PlayUrlData {

        /**
         * 章节ID
         */
        @SerializedName("chapterId")
        private String chapterId;

        /**
         * 视频URL（加密）
         */
        @SerializedName("videoUrl")
        private String videoUrl;

        /**
         * 过期时间（字符串格式：yyyy-MM-dd HH:mm:ss）
         */
        @SerializedName("expireTime")
        private String expireTime;

        /**
         * 下载记录ID
         */
        @SerializedName("downloadRecordId")
        private String downloadRecordId;

        /**
         * 字幕信息列表
         */
        @SerializedName("subtitles")
        private List<SubtitleInfo> subtitles;

        /**
         * 质量选项列表
         */
        @SerializedName("qualities")
        private List<VideoQualityInfo> qualities;

        /**
         * 默认构造函数
         */
        public PlayUrlData() {
        }

        /**
         * 完整构造函数
         */
        public PlayUrlData(String chapterId, String videoUrl, String expireTime,
                          String downloadRecordId, List<SubtitleInfo> subtitles,
                          List<VideoQualityInfo> qualities) {
            this.chapterId = chapterId;
            this.videoUrl = videoUrl;
            this.expireTime = expireTime;
            this.downloadRecordId = downloadRecordId;
            this.subtitles = subtitles;
            this.qualities = qualities;
        }

        // ========== Getter和Setter方法 ==========

        public String getChapterId() {
            return chapterId;
        }

        public void setChapterId(String chapterId) {
            this.chapterId = chapterId;
        }

        public String getVideoUrl() {
            return videoUrl;
        }

        public void setVideoUrl(String videoUrl) {
            this.videoUrl = videoUrl;
        }

        public String getExpireTime() {
            return expireTime;
        }

        public void setExpireTime(String expireTime) {
            this.expireTime = expireTime;
        }

        public String getDownloadRecordId() {
            return downloadRecordId;
        }

        public void setDownloadRecordId(String downloadRecordId) {
            this.downloadRecordId = downloadRecordId;
        }

        public List<SubtitleInfo> getSubtitles() {
            return subtitles;
        }

        public void setSubtitles(List<SubtitleInfo> subtitles) {
            this.subtitles = subtitles;
        }

        public List<VideoQualityInfo> getQualities() {
            return qualities;
        }

        public void setQualities(List<VideoQualityInfo> qualities) {
            this.qualities = qualities;
        }

        // ========== 便利方法 ==========

        /**
         * 检查视频URL是否有效
         *
         * @return 如果URL有效返回true，否则返回false
         */
        public boolean hasValidVideoUrl() {
            return videoUrl != null && !videoUrl.trim().isEmpty();
        }

        /**
         * 检查是否已过期
         *
         * @return 如果已过期返回true，否则返回false
         */
        public boolean isExpired() {
            if (expireTime == null || expireTime.trim().isEmpty()) {
                return false;
            }

            try {
                // 解析时间字符串格式：yyyy-MM-dd HH:mm:ss
                java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
                java.util.Date expireDate = format.parse(expireTime);
                if (expireDate != null) {
                    return System.currentTimeMillis() > expireDate.getTime();
                }
            } catch (Exception e) {
                // 解析失败时，认为未过期（安全策略）
                android.util.Log.w("PlayUrlResponseModel", "Failed to parse expire time: " + expireTime, e);
            }

            return false;
        }

        /**
         * 获取剩余有效时间（毫秒）
         *
         * @return 剩余有效时间，如果已过期返回0
         */
        public long getRemainingTime() {
            if (expireTime == null || expireTime.trim().isEmpty()) {
                return Long.MAX_VALUE;
            }

            try {
                // 解析时间字符串格式：yyyy-MM-dd HH:mm:ss
                java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
                java.util.Date expireDate = format.parse(expireTime);
                if (expireDate != null) {
                    long remaining = expireDate.getTime() - System.currentTimeMillis();
                    return Math.max(0, remaining);
                }
            } catch (Exception e) {
                // 解析失败时，返回最大值（安全策略）
                android.util.Log.w("PlayUrlResponseModel", "Failed to parse expire time: " + expireTime, e);
            }

            return Long.MAX_VALUE;
        }

        /**
         * 检查是否有字幕
         *
         * @return 如果有字幕返回true，否则返回false
         */
        public boolean hasSubtitles() {
            return subtitles != null && !subtitles.isEmpty();
        }

        /**
         * 检查是否有质量选项
         *
         * @return 如果有质量选项返回true，否则返回false
         */
        public boolean hasQualities() {
            return qualities != null && !qualities.isEmpty();
        }

        /**
         * 获取过期时间的时间戳格式
         *
         * @return 过期时间的时间戳（毫秒），如果解析失败返回null
         */
        public Long getExpireTimeAsTimestamp() {
            if (expireTime == null || expireTime.trim().isEmpty()) {
                return null;
            }

            try {
                // 解析时间字符串格式：yyyy-MM-dd HH:mm:ss
                java.text.SimpleDateFormat format = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
                java.util.Date expireDate = format.parse(expireTime);
                if (expireDate != null) {
                    return expireDate.getTime();
                }
            } catch (Exception e) {
                android.util.Log.w("PlayUrlResponseModel", "Failed to parse expire time: " + expireTime, e);
            }

            return null;
        }

        /**
         * 根据语言类型查找字幕
         *
         * @param languageType 语言类型（1=英语, 2=俄语）
         * @return 找到的字幕信息，如果未找到返回null
         */
        public SubtitleInfo findSubtitleByLanguage(int languageType) {
            if (subtitles == null) {
                return null;
            }
            
            for (SubtitleInfo subtitle : subtitles) {
                if (subtitle.getLanguageType() != null && subtitle.getLanguageType() == languageType) {
                    return subtitle;
                }
            }
            return null;
        }

        /**
         * 根据分辨率类型查找质量选项
         *
         * @param type 分辨率类型（auto/480/720/1080）
         * @return 找到的质量信息，如果未找到返回null
         */
        public VideoQualityInfo findQualityByType(String type) {
            if (qualities == null || type == null) {
                return null;
            }
            
            for (VideoQualityInfo quality : qualities) {
                if (type.equals(quality.getType())) {
                    return quality;
                }
            }
            return null;
        }

        @Override
        public String toString() {
            return "PlayUrlData{" +
                    "chapterId='" + chapterId + '\'' +
                    ", videoUrl='" + videoUrl + '\'' +
                    ", expireTime=" + expireTime +
                    ", downloadRecordId='" + downloadRecordId + '\'' +
                    ", subtitles=" + (subtitles != null ? subtitles.size() + " items" : "null") +
                    ", qualities=" + (qualities != null ? qualities.size() + " items" : "null") +
                    ", isExpired=" + isExpired() +
                    '}';
        }
    }
}
