<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginStart="@dimen/interest_card_margin_horizontal"
    android:layout_marginEnd="@dimen/interest_card_margin_horizontal"
    android:layout_marginBottom="@dimen/interest_card_margin_bottom"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报容器 -->
    <RelativeLayout
        android:layout_width="@dimen/interest_poster_width"
        android:layout_height="@dimen/interest_poster_height">

        <!-- 海报图片 -->
        <ImageView
            android:id="@+id/iv_poster"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@drawable/interest_poster_bg"
            android:src="@drawable/movie_poster"
            android:clipToOutline="true" />

    </RelativeLayout>

    <!-- 右侧内容容器 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/interest_content_margin_start">

        <!-- 标题 -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="@dimen/interest_title_height"
            android:text="Escorting the heiress"
            android:textColor="#ffffffff"
            android:textSize="@dimen/interest_title_text_size"
            android:maxLines="1"
            android:ellipsize="end"
            android:fontFamily="sans-serif" />

        <!-- Coming Soon 标签 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="@dimen/interest_status_tag_height"
            android:layout_marginTop="@dimen/interest_status_tag_margin_top"
            android:background="@drawable/interest_status_tag_bg"
            android:gravity="center"
            android:paddingHorizontal="12dp">

            <TextView
                android:id="@+id/tv_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="coming soon"
                android:textColor="#ffb936cf"
                android:textSize="@dimen/interest_status_text_size"
                android:gravity="center"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- 描述文本 -->
        <TextView
            android:id="@+id/tv_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/interest_description_margin_top"
            android:text="a captivating historical drama set in a grand palace, where love …"
            android:textColor="#a3ffffff"
            android:textSize="@dimen/interest_description_text_size"
            android:maxLines="2"
            android:ellipsize="end"
            android:fontFamily="sans-serif" />

        <!-- 时间信息容器 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/interest_time_container_margin_top"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 时钟图标 -->
            <ImageView
                android:id="@+id/iv_time_icon"
                android:layout_width="@dimen/interest_time_icon_size"
                android:layout_height="@dimen/interest_time_icon_size"
                android:src="@drawable/list_ic_time" />

            <!-- 时间文本 -->
            <TextView
                android:id="@+id/tv_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/interest_time_text_margin_start"
                android:text="2025-06-18"
                android:textColor="#ffffffff"
                android:textSize="@dimen/interest_time_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

        <!-- Cancel Notify 按钮 -->
        <LinearLayout
            android:id="@+id/btn_cancel_notify"
            android:layout_width="@dimen/interest_cancel_button_width"
            android:layout_height="@dimen/interest_cancel_button_height"
            android:layout_marginTop="@dimen/interest_cancel_button_margin_top"
            android:background="@drawable/interest_cancel_button_bg"
            android:orientation="horizontal"
            android:gravity="center"
            android:clickable="true"
            android:focusable="true">

            <!-- 取消图标 -->
            <ImageView
                android:id="@+id/iv_cancel_icon"
                android:layout_width="@dimen/interest_cancel_icon_size"
                android:layout_height="@dimen/interest_cancel_icon_size"
                android:src="@drawable/list_ic_cancel_notify" />

            <!-- 按钮文本 -->
            <TextView
                android:id="@+id/tv_cancel_text"
                android:layout_width="@dimen/interest_cancel_text_width"
                android:layout_height="@dimen/interest_cancel_text_height"
                android:layout_marginStart="@dimen/interest_cancel_text_margin_start"
                android:text="Cancel Notify"
                android:textColor="#ffffffff"
                android:textSize="@dimen/interest_cancel_text_size"
                android:gravity="center"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
