<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/redeem_dialog_width"
    android:layout_height="@dimen/redeem_dialog_height"
    android:orientation="vertical"
    android:background="@drawable/redeem_dialog_background"
    android:gravity="center_horizontal"
    android:padding="@dimen/redeem_dialog_padding">

    <!-- Title -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/redeem_dialog_title_margin_top"
        android:text="Redemption code"
        android:textColor="#ffffffff"
        android:textSize="@dimen/redeem_dialog_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:gravity="center" />

    <!-- Description -->
    <TextView
        android:id="@+id/tv_dialog_description"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/redeem_dialog_description_margin_top"
        android:text="Please enter the redemption code to\nredeem the membership duration"
        android:textColor="#ccffffff"
        android:textSize="@dimen/redeem_dialog_description_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:lineSpacingExtra="@dimen/redeem_dialog_line_spacing" />

    <!-- Input Field -->
    <EditText
        android:id="@+id/et_redeem_code"
        android:layout_width="@dimen/redeem_dialog_input_width"
        android:layout_height="@dimen/redeem_dialog_input_height"
        android:layout_marginTop="@dimen/redeem_dialog_element_margin_top"
        android:background="@drawable/redeem_input_background"
        android:hint="Please enter"
        android:textColorHint="#80ffffff"
        android:textColor="#ffffffff"
        android:textSize="@dimen/redeem_dialog_description_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:inputType="text"
        android:maxLines="1"
        android:paddingHorizontal="@dimen/redeem_dialog_input_padding_horizontal" />

    <!-- Exchange Button -->
    <TextView
        android:id="@+id/btn_exchange"
        android:layout_width="@dimen/redeem_dialog_button_width"
        android:layout_height="@dimen/redeem_dialog_button_height"
        android:layout_marginTop="@dimen/redeem_dialog_element_margin_top"
        android:background="@drawable/redeem_button_background"
        android:text="Exchange"
        android:textColor="#ffffffff"
        android:textSize="@dimen/redeem_dialog_button_text_size"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
