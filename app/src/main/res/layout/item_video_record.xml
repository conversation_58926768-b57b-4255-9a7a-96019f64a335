<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/video_record_card_height"
    android:layout_gravity="center_horizontal"
    android:layout_marginBottom="@dimen/video_record_card_margin_bottom"
    android:background="@drawable/video_record_card_bg"
    android:orientation="horizontal"
    android:padding="@dimen/video_record_card_padding">

    <!-- 左侧：视频海报 -->
    <androidx.cardview.widget.CardView
        android:layout_width="@dimen/video_record_poster_width"
        android:layout_height="@dimen/video_record_poster_height"
        android:layout_gravity="center_vertical"
        app:cardCornerRadius="9dp"
        app:cardElevation="0dp">

        <ImageView
            android:id="@+id/iv_poster"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/movie_poster"
            android:scaleType="centerCrop" />

    </androidx.cardview.widget.CardView>

    <!-- 右侧：视频信息 -->
    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:layout_marginStart="@dimen/video_record_content_margin_start"
        android:orientation="vertical"
        android:gravity="center_vertical">

        <!-- 上半部分：标题和消费金额 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="2">

            <!-- 视频标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:text="Eternal Love"
                android:textColor="#ffffffff"
                android:textSize="@dimen/video_record_title_text_size"
                android:fontFamily="sans-serif" />

            <!-- 消费金额和积分图标 -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <!-- 消费金额 -->
                <TextView
                    android:id="@+id/tv_cost"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="-20"
                    android:textColor="#F12626"
                    android:textSize="@dimen/video_record_cost_text_size"
                    android:fontFamily="sans-serif" />

                <!-- 积分图标 -->
                <ImageView
                    android:id="@+id/iv_cost_icon"
                    android:layout_width="@dimen/video_record_cost_icon_size"
                    android:layout_height="@dimen/video_record_cost_icon_size"
                    android:layout_marginStart="@dimen/video_record_cost_icon_margin_start"
                    android:src="@drawable/jinbi"
                    android:scaleType="centerCrop" />

            </LinearLayout>

        </RelativeLayout>

        <!-- 中间部分：集数信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 集数标签 -->
            <TextView
                android:id="@+id/tv_episode"
                android:layout_width="@dimen/video_record_episode_width"
                android:layout_height="@dimen/video_record_episode_height"
                android:text="EP.1"
                android:textColor="#F12626"
                android:textSize="@dimen/video_record_episode_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </LinearLayout>

        <!-- 下半部分：描述和日期时间 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="2"
            android:orientation="vertical"
            android:gravity="bottom">

            <!-- 描述 -->
            <TextView
                android:id="@+id/tv_description"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Unlock 1 Episode"
                android:textColor="#80ffffff"
                android:textSize="@dimen/video_record_description_text_size"
                android:fontFamily="sans-serif"
                android:maxLines="1"
                android:ellipsize="end" />

            <!-- 日期时间 -->
            <TextView
                android:id="@+id/tv_date_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="06/18/2025 10:03AM"
                android:textColor="#80ffffff"
                android:textSize="@dimen/video_record_date_text_size"
                android:fontFamily="sans-serif"
                android:maxLines="1" />

        </LinearLayout>

    </LinearLayout>

</LinearLayout>
