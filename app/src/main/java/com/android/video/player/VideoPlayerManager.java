package com.android.video.player;

import android.content.Context;
import android.net.Uri;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.google.android.exoplayer2.ExoPlayer;
import com.google.android.exoplayer2.MediaItem;
import com.google.android.exoplayer2.PlaybackException;
import com.google.android.exoplayer2.Player;
import com.google.android.exoplayer2.source.MediaSource;
import com.google.android.exoplayer2.source.ProgressiveMediaSource;
import com.google.android.exoplayer2.ui.PlayerView;
import com.google.android.exoplayer2.upstream.DefaultDataSource;
import com.google.android.exoplayer2.DefaultLoadControl;
import com.google.android.exoplayer2.DefaultRenderersFactory;
import com.google.android.exoplayer2.util.Util;
import com.google.android.exoplayer2.video.VideoSize;


import java.util.ArrayList;
import java.util.List;

/**
 * ExoPlayer封装管理类
 * 提供视频播放的核心功能实现
 * <AUTHOR>
 */
public class VideoPlayerManager implements VideoPlayerController, VideoQualitySelector {
    
    private static final String TAG = "VideoPlayerManager";
    
    private Context context;
    private ExoPlayer exoPlayer;
    private PlayerView playerView;
    private VideoPlayerListener playerListener;
    private OnQualityChangeListener qualityChangeListener;
    
    private Handler progressHandler;
    private Runnable progressRunnable;
    
    private VideoQuality currentQuality = VideoQuality.QUALITY_1080P;
    private String currentVideoUrl;
    private boolean isInitialized = false;
    private java.util.List<com.android.video.model.SubtitleInfo> currentSubtitles;
    private boolean subtitlesEnabled = false;
    
    public VideoPlayerManager(Context context) {
        this.context = context;
        this.progressHandler = new Handler(Looper.getMainLooper());
        initializePlayer();
    }
    
    /**
     * 初始化播放器
     */
    private void initializePlayer() {
        if (exoPlayer == null) {
            // 创建硬件加速渲染器工厂，优化视频渲染
            DefaultRenderersFactory renderersFactory = new DefaultRenderersFactory(context)
                .setEnableDecoderFallback(true)
                .setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_PREFER)
                .setEnableAudioFloatOutput(false); // 避免音频处理延迟

            // 配置缓冲控制参数以优化性能
            DefaultLoadControl loadControl = new DefaultLoadControl.Builder()
                .setBufferDurationsMs(15000, 50000, 1500, 5000) // 最小缓冲、最大缓冲、播放缓冲、重新缓冲
                .build();

            // 使用硬件加速配置创建ExoPlayer
            exoPlayer = new ExoPlayer.Builder(context)
                .setRenderersFactory(renderersFactory)
                .setLoadControl(loadControl)
                .build();

            exoPlayer.addListener(new PlayerEventListener());

            // 视频监听器功能已集成到PlayerEventListener中

            isInitialized = true;
            
            // 初始化进度更新任务
            progressRunnable = new Runnable() {
                @Override
                public void run() {
                    if (exoPlayer != null && playerListener != null) {
                        long position = exoPlayer.getCurrentPosition();
                        long duration = exoPlayer.getDuration();
                        playerListener.onPositionChanged(position, duration);
                        progressHandler.postDelayed(this, 1000); // 每秒更新一次
                    }
                }
            };
        }
    }
    
    /**
     * 设置PlayerView
     * @param playerView 播放器视图
     */
    public void setPlayerView(PlayerView playerView) {
        this.playerView = playerView;
        if (exoPlayer != null && playerView != null) {
            playerView.setPlayer(exoPlayer);
        }
    }
    
    /**
     * 设置播放器监听器
     * @param listener 监听器
     */
    public void setPlayerListener(VideoPlayerListener listener) {
        this.playerListener = listener;
    }
    
    // VideoPlayerController 接口实现
    
    @Override
    public void play() {
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(true);
            startProgressUpdates();
        }
    }
    
    @Override
    public void pause() {
        if (exoPlayer != null) {
            exoPlayer.setPlayWhenReady(false);
            stopProgressUpdates();
        }
    }
    
    @Override
    public void stop() {
        if (exoPlayer != null) {
            exoPlayer.stop();
            stopProgressUpdates();
        }
    }
    
    @Override
    public void seekTo(long positionMs) {
        if (exoPlayer != null) {
            exoPlayer.seekTo(positionMs);
        }
    }
    
    @Override
    public void setPlaybackSpeed(float speed) {
        if (exoPlayer != null) {
            exoPlayer.setPlaybackSpeed(speed);
            if (playerListener != null) {
                playerListener.onPlaybackSpeedChanged(speed);
            }
        }
    }
    
    @Override
    public void setVolume(float volume) {
        if (exoPlayer != null) {
            exoPlayer.setVolume(volume);
            if (playerListener != null) {
                playerListener.onVolumeChanged(volume);
            }
        }
    }
    
    @Override
    public long getCurrentPosition() {
        return exoPlayer != null ? exoPlayer.getCurrentPosition() : 0;
    }
    
    @Override
    public long getDuration() {
        return exoPlayer != null ? exoPlayer.getDuration() : 0;
    }
    
    @Override
    public boolean isPlaying() {
        return exoPlayer != null && exoPlayer.isPlaying();
    }
    
    @Override
    public void setVideoSource(String videoUrl) {
        if (exoPlayer == null) {
            Log.e(TAG, "ExoPlayer is null, cannot set video source");
            if (playerListener != null) {
                playerListener.onError("播放器未初始化");
            }
            return;
        }

        if (videoUrl == null || videoUrl.isEmpty()) {
            Log.e(TAG, "Video URL is null or empty");
            if (playerListener != null) {
                playerListener.onError("视频地址无效");
            }
            return;
        }

        try {
            this.currentVideoUrl = videoUrl;
            Log.d(TAG, "Setting video source: " + videoUrl);

            // 创建MediaItem，如果有字幕则添加字幕轨道
            MediaItem.Builder mediaItemBuilder = MediaItem.fromUri(Uri.parse(videoUrl)).buildUpon();

            // 添加字幕轨道
            if (currentSubtitles != null && !currentSubtitles.isEmpty()) {
                java.util.List<MediaItem.SubtitleConfiguration> subtitleConfigs = new java.util.ArrayList<>();
                for (com.android.video.model.SubtitleInfo subtitle : currentSubtitles) {
                    if (subtitle.hasValidUrl() && subtitle.isSupportedFormat()) {
                        MediaItem.SubtitleConfiguration subtitleConfig =
                            new MediaItem.SubtitleConfiguration.Builder(Uri.parse(subtitle.getSubtitleFileUrl()))
                                .setMimeType(subtitle.getMimeType())
                                .setLanguage(subtitle.getLanguageCode())
                                .setLabel(subtitle.getLanguageTypeDescription())
                                .build();
                        subtitleConfigs.add(subtitleConfig);
                        Log.d(TAG, "Added subtitle track: " + subtitle.getLanguageTypeDescription());
                    }
                }
                if (!subtitleConfigs.isEmpty()) {
                    mediaItemBuilder.setSubtitleConfigurations(subtitleConfigs);
                }
            }

            MediaItem mediaItem = mediaItemBuilder.build();

            // 设置媒体源
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();

            Log.d(TAG, "Video source set successfully: " + videoUrl);
        } catch (Exception e) {
            Log.e(TAG, "Failed to set video source: " + videoUrl, e);
            if (playerListener != null) {
                playerListener.onError("视频加载失败: " + e.getMessage());
            }
        }
    }
    
    @Override
    public void release() {
        stopProgressUpdates();
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }
        isInitialized = false;
    }
    
    @Override
    public void prepare() {
        if (exoPlayer != null) {
            exoPlayer.prepare();
        }
    }

    /**
     * 预加载视频源（不立即播放）
     * @param videoUrl 视频地址
     */
    public void preloadVideoSource(String videoUrl) {
        if (exoPlayer == null) {
            Log.e(TAG, "ExoPlayer is null, cannot preload video source");
            return;
        }

        if (videoUrl == null || videoUrl.isEmpty()) {
            Log.e(TAG, "Video URL is null or empty for preload");
            return;
        }

        try {
            Log.d(TAG, "Preloading video source: " + videoUrl);

            // 创建MediaItem
            MediaItem mediaItem = MediaItem.fromUri(Uri.parse(videoUrl));

            // 设置媒体源但不自动播放
            exoPlayer.setMediaItem(mediaItem);
            exoPlayer.prepare();
            exoPlayer.setPlayWhenReady(false); // 确保不自动播放

            Log.d(TAG, "Video source preloaded successfully: " + videoUrl);
        } catch (Exception e) {
            Log.e(TAG, "Failed to preload video source: " + videoUrl, e);
        }
    }
    
    @Override
    public void reset() {
        if (exoPlayer != null) {
            exoPlayer.stop();
            exoPlayer.clearMediaItems();
        }
    }
    
    // VideoQualitySelector 接口实现
    
    @Override
    public List<VideoQuality> getAvailableQualities() {
        List<VideoQuality> qualities = new ArrayList<>();
        qualities.add(VideoQuality.QUALITY_720P);
        qualities.add(VideoQuality.QUALITY_1080P);
        qualities.add(VideoQuality.QUALITY_ULTRA);
        return qualities;
    }
    
    @Override
    public VideoQuality getCurrentQuality() {
        return currentQuality;
    }
    
    @Override
    public void setQuality(VideoQuality quality) {
        if (quality != null && quality != currentQuality) {
            VideoQuality oldQuality = currentQuality;
            currentQuality = quality;
            
            if (qualityChangeListener != null) {
                qualityChangeListener.onQualityChangeStarted(quality);
                qualityChangeListener.onQualityChanged(oldQuality, quality);
                qualityChangeListener.onQualityChangeCompleted(quality);
            }
        }
    }
    
    @Override
    public void setOnQualityChangeListener(OnQualityChangeListener listener) {
        this.qualityChangeListener = listener;
    }
    
    /**
     * 开始进度更新
     */
    private void startProgressUpdates() {
        stopProgressUpdates();
        progressHandler.post(progressRunnable);
    }
    
    /**
     * 停止进度更新
     */
    private void stopProgressUpdates() {
        progressHandler.removeCallbacks(progressRunnable);
    }
    
    /**
     * ExoPlayer事件监听器
     */
    private class PlayerEventListener implements Player.Listener {
        
        @Override
        public void onPlaybackStateChanged(int playbackState) {
            if (playerListener != null) {
                playerListener.onPlayerStateChanged(exoPlayer.getPlayWhenReady(), playbackState);

                switch (playbackState) {
                    case Player.STATE_BUFFERING:
                        // 缓冲中
                        Log.d(TAG, "Player state: BUFFERING");
                        playerListener.onBufferingUpdate(true, 0);
                        break;
                    case Player.STATE_READY:
                        // 准备就绪，停止缓冲
                        Log.d(TAG, "Player state: READY, playWhenReady=" + exoPlayer.getPlayWhenReady());
                        playerListener.onBufferingUpdate(false, 100);

                        // 立即调用onPrepared，确保加载动画隐藏
                        playerListener.onPrepared();

                        // 如果设置了播放，确保立即开始播放
                        if (exoPlayer.getPlayWhenReady()) {
                            // 延迟一点确保视频渲染准备完成
                            new Handler(Looper.getMainLooper()).postDelayed(() -> {
                                if (exoPlayer != null && exoPlayer.getPlayWhenReady() && playerListener != null) {
                                    playerListener.onPlaybackStarted();
                                }
                            }, 100);
                        }
                        break;
                    case Player.STATE_ENDED:
                        Log.d(TAG, "Player state: ENDED");
                        playerListener.onPlaybackCompleted();
                        stopProgressUpdates();
                        break;
                    case Player.STATE_IDLE:
                        // 空闲状态，停止缓冲
                        Log.d(TAG, "Player state: IDLE");
                        playerListener.onBufferingUpdate(false, 0);
                        break;
                }
            }
        }
        
        @Override
        public void onIsPlayingChanged(boolean isPlaying) {
            Log.d(TAG, "onIsPlayingChanged: " + isPlaying + ", playWhenReady: " + exoPlayer.getPlayWhenReady() + ", state: " + exoPlayer.getPlaybackState());
            if (playerListener != null) {
                if (isPlaying) {
                    // 开始播放时，确保隐藏加载动画和缓冲指示器
                    playerListener.onBufferingUpdate(false, 100);
                    playerListener.onPlaybackStarted();
                    startProgressUpdates();
                } else {
                    // 只有在不是因为缓冲而暂停时才通知UI暂停
                    if (exoPlayer.getPlaybackState() != Player.STATE_BUFFERING) {
                        playerListener.onPlaybackPaused();
                    }
                    stopProgressUpdates();
                }
            }
        }
        
        @Override
        public void onPlayerError(PlaybackException error) {
            Log.e(TAG, "ExoPlayer error occurred", error);

            String errorMessage = "播放出错";
            boolean shouldRetryWithSoftwareDecoder = false;

            if (error != null) {
                // 根据错误类型提供更友好的错误信息
                switch (error.errorCode) {
                    case PlaybackException.ERROR_CODE_IO_BAD_HTTP_STATUS:
                    case PlaybackException.ERROR_CODE_IO_NETWORK_CONNECTION_FAILED:
                        errorMessage = "网络连接失败，请检查网络设置";
                        break;
                    case PlaybackException.ERROR_CODE_IO_FILE_NOT_FOUND:
                        errorMessage = "视频文件不存在";
                        break;
                    case PlaybackException.ERROR_CODE_PARSING_CONTAINER_MALFORMED:
                    case PlaybackException.ERROR_CODE_PARSING_MANIFEST_MALFORMED:
                        errorMessage = "视频格式不支持";
                        break;
                    case PlaybackException.ERROR_CODE_DECODER_INIT_FAILED:
                    case PlaybackException.ERROR_CODE_DECODER_QUERY_FAILED:
                        errorMessage = "视频解码器初始化失败，尝试软件解码";
                        shouldRetryWithSoftwareDecoder = true;
                        break;
                    case PlaybackException.ERROR_CODE_VIDEO_FRAME_PROCESSING_FAILED:
                        errorMessage = "视频帧处理失败，尝试软件解码";
                        shouldRetryWithSoftwareDecoder = true;
                        break;
                    default:
                        errorMessage = "播放出错: " + error.getMessage();
                        break;
                }
            }

            // 如果是解码器相关错误，尝试使用软件解码器重试
            if (shouldRetryWithSoftwareDecoder && currentVideoUrl != null) {
                Log.w(TAG, "Retrying with software decoder due to hardware decoder failure");
                retryWithSoftwareDecoder();
            } else if (playerListener != null) {
                playerListener.onError(errorMessage);
            }
        }
    }

    /**
     * 使用软件解码器重试播放
     */
    private void retryWithSoftwareDecoder() {
        if (exoPlayer != null) {
            exoPlayer.release();
            exoPlayer = null;
        }

        // 创建软件解码器工厂
        DefaultRenderersFactory renderersFactory = new DefaultRenderersFactory(context)
            .setEnableDecoderFallback(true)
            .setExtensionRendererMode(DefaultRenderersFactory.EXTENSION_RENDERER_MODE_OFF)
            .setEnableAudioFloatOutput(false);

        // 配置缓冲控制参数
        DefaultLoadControl loadControl = new DefaultLoadControl.Builder()
            .setBufferDurationsMs(15000, 50000, 1500, 5000)
            .build();

        // 使用软件解码配置创建ExoPlayer
        exoPlayer = new ExoPlayer.Builder(context)
            .setRenderersFactory(renderersFactory)
            .setLoadControl(loadControl)
            .build();

        exoPlayer.addListener(new PlayerEventListener());

        // 视频监听器功能已集成到PlayerEventListener中

        // 重新设置PlayerView
        if (playerView != null) {
            playerView.setPlayer(exoPlayer);
        }

        // 重新设置视频源
        if (currentVideoUrl != null) {
            setVideoSource(currentVideoUrl);
        }
    }

    /**
     * 获取播放器实例
     * @return ExoPlayer实例
     */
    public ExoPlayer getExoPlayer() {
        return exoPlayer;
    }

    /**
     * 检查播放器是否已初始化
     * @return 是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 设置字幕信息
     * @param subtitles 字幕信息列表
     */
    public void setSubtitles(java.util.List<com.android.video.model.SubtitleInfo> subtitles) {
        this.currentSubtitles = subtitles;
        Log.d(TAG, "Set subtitles: " + (subtitles != null ? subtitles.size() : 0) + " tracks");
    }

    /**
     * 启用或禁用字幕
     * @param enabled 是否启用字幕
     */
    public void setSubtitlesEnabled(boolean enabled) {
        this.subtitlesEnabled = enabled;
        if (exoPlayer != null) {
            // 控制字幕轨道的显示
            for (int i = 0; i < exoPlayer.getCurrentTracks().getGroups().size(); i++) {
                com.google.android.exoplayer2.Tracks.Group group = exoPlayer.getCurrentTracks().getGroups().get(i);
                if (group.getType() == com.google.android.exoplayer2.C.TRACK_TYPE_TEXT) {
                    // 找到字幕轨道，设置选择状态
                    com.google.android.exoplayer2.trackselection.TrackSelectionParameters.Builder builder =
                        exoPlayer.getTrackSelectionParameters().buildUpon();
                    if (enabled) {
                        // 启用字幕轨道
                        builder.setTrackTypeDisabled(com.google.android.exoplayer2.C.TRACK_TYPE_TEXT, false);
                    } else {
                        // 禁用字幕轨道
                        builder.setTrackTypeDisabled(com.google.android.exoplayer2.C.TRACK_TYPE_TEXT, true);
                    }
                    exoPlayer.setTrackSelectionParameters(builder.build());
                    break;
                }
            }
        }
        Log.d(TAG, "Subtitles " + (enabled ? "enabled" : "disabled"));
    }

    /**
     * 检查字幕是否启用
     * @return 字幕是否启用
     */
    public boolean isSubtitlesEnabled() {
        return subtitlesEnabled;
    }
}
