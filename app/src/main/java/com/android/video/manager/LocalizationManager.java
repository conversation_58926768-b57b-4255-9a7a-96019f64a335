package com.android.video.manager;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.content.res.AssetManager;
import android.content.res.Configuration;
import android.util.Log;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;
import java.util.Locale;
import com.android.video.cache.ImprovedDataCacheManager;
import com.android.video.model.TranslationDataModel;
import com.android.video.utils.UserSessionUtils;
import com.android.video.callback.LocalizationCallback;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.lang.reflect.Type;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 本地化管理器
 * 负责加载翻译数据、管理语言切换和提供文本获取接口
 * 支持英语、俄语、Kaza语三种语言
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class LocalizationManager {
    
    private static final String TAG = "LocalizationManager";
    private static final String TRANSLATIONS_FILE = "translations.json";
    private static final String CACHE_KEY_TRANSLATIONS = "localization_translations";

    // 广播常量
    public static final String ACTION_LANGUAGE_CHANGED = "com.android.video.LANGUAGE_CHANGED";
    public static final String EXTRA_LANGUAGE_TYPE = "language_type";
    public static final String ACTION_RESTART_ACTIVITY = "com.android.video.RESTART_ACTIVITY";
    
    // 单例实例
    private static volatile LocalizationManager instance;
    
    // 核心组件
    private Context context;
    private ImprovedDataCacheManager cacheManager;
    private Gson gson;
    
    // 翻译数据
    private TranslationDataModel translationData;
    private int currentLanguageType = 1; // 默认英语
    
    // 内存缓存，提高性能
    private final ConcurrentHashMap<String, String> memoryCache = new ConcurrentHashMap<>();
    
    // 语言切换监听器
    private LocalizationCallback localizationCallback;
    
    /**
     * 私有构造函数
     */
    private LocalizationManager(Context context) {
        this.context = context.getApplicationContext();
        this.cacheManager = ImprovedDataCacheManager.getInstance(context);
        this.gson = new Gson();
        
        // 获取当前语言设置
        this.currentLanguageType = UserSessionUtils.getLanguageType(context);
        
        Log.d(TAG, "LocalizationManager initialized with language type: " + currentLanguageType);
    }
    
    /**
     * 获取单例实例
     * 使用双重检查锁定确保线程安全
     */
    public static LocalizationManager getInstance(Context context) {
        if (instance == null) {
            synchronized (LocalizationManager.class) {
                if (instance == null) {
                    instance = new LocalizationManager(context);
                }
            }
        }
        return instance;
    }
    
    /**
     * 设置语言切换监听器
     */
    public void setLanguageChangeListener(LocalizationCallback callback) {
        this.localizationCallback = callback;
    }
    
    /**
     * 加载翻译数据
     * 优先从缓存读取，缓存无效时从assets读取
     */
    public void loadTranslations() {
        Log.d(TAG, "开始加载翻译数据...");
        
        try {
            // 先尝试从缓存读取
            Type type = new TypeToken<TranslationDataModel>(){}.getType();
            TranslationDataModel cachedData = cacheManager.getCachedDataAtomic(CACHE_KEY_TRANSLATIONS, type);
            
            if (cachedData != null && isTranslationDataValid(cachedData)) {
                Log.d(TAG, "从缓存加载翻译数据成功");
                this.translationData = cachedData;
                updateMemoryCache();
                notifyTranslationLoaded();
                return;
            }
            
            // 缓存无效，从assets读取
            Log.d(TAG, "缓存无效，从assets文件加载翻译数据");
            loadTranslationsFromAssets();
            
        } catch (Exception e) {
            Log.e(TAG, "加载翻译数据失败", e);
            notifyError("加载翻译数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 从assets文件加载翻译数据
     */
    private void loadTranslationsFromAssets() throws IOException {
        AssetManager assetManager = context.getAssets();
        InputStream inputStream = null;
        BufferedReader reader = null;
        
        try {
            inputStream = assetManager.open(TRANSLATIONS_FILE);
            reader = new BufferedReader(new InputStreamReader(inputStream, "UTF-8"));
            
            StringBuilder jsonBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
            
            String jsonString = jsonBuilder.toString();
            Log.d(TAG, "翻译文件读取完成，大小: " + jsonString.length() + " 字符");
            
            // 解析JSON数据
            this.translationData = gson.fromJson(jsonString, TranslationDataModel.class);
            
            if (isTranslationDataValid(translationData)) {
                // 缓存到磁盘
                boolean cacheSuccess = cacheManager.cacheDataAtomic(CACHE_KEY_TRANSLATIONS, translationData);
                Log.d(TAG, "翻译数据缓存结果: " + cacheSuccess);
                
                // 更新内存缓存
                updateMemoryCache();
                
                Log.d(TAG, "翻译数据加载成功");
                notifyTranslationLoaded();
            } else {
                throw new IllegalStateException("翻译数据无效或不完整");
            }
            
        } finally {
            if (reader != null) {
                try { reader.close(); } catch (IOException e) { /* ignore */ }
            }
            if (inputStream != null) {
                try { inputStream.close(); } catch (IOException e) { /* ignore */ }
            }
        }
    }
    
    /**
     * 验证翻译数据是否有效
     */
    private boolean isTranslationDataValid(TranslationDataModel data) {
        if (data == null) {
            return false;
        }
        
        // 检查三种语言的数据是否都存在且不为空
        Map<String, String> english = data.getEnglish();
        Map<String, String> russian = data.getRussian();
        Map<String, String> kaza = data.getKaza();
        
        boolean isValid = english != null && !english.isEmpty() &&
                         russian != null && !russian.isEmpty() &&
                         kaza != null && !kaza.isEmpty();
        
        Log.d(TAG, "翻译数据验证结果: " + isValid + 
                  " (英语: " + (english != null ? english.size() : 0) + 
                  ", 俄语: " + (russian != null ? russian.size() : 0) + 
                  ", Kaza语: " + (kaza != null ? kaza.size() : 0) + ")");
        
        return isValid;
    }
    
    /**
     * 更新内存缓存
     */
    private void updateMemoryCache() {
        if (translationData == null) {
            return;
        }
        
        memoryCache.clear();
        Map<String, String> currentTranslations = translationData.getTranslationByLanguageType(currentLanguageType);
        if (currentTranslations != null) {
            memoryCache.putAll(currentTranslations);
            Log.d(TAG, "内存缓存已更新，当前语言: " + currentLanguageType + ", 条目数: " + memoryCache.size());
        }
    }
    
    /**
     * 切换语言
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     */
    public void setLanguage(int languageType) {
        if (languageType < 1 || languageType > 3) {
            Log.w(TAG, "无效的语言类型: " + languageType + ", 使用默认英语");
            languageType = 1;
        }
        
        if (this.currentLanguageType == languageType) {
            Log.d(TAG, "语言类型未变化: " + languageType);
            return;
        }
        
        Log.d(TAG, "切换语言: " + this.currentLanguageType + " -> " + languageType);
        
        this.currentLanguageType = languageType;
        
        // 保存到用户设置
        UserSessionUtils.saveLanguageType(context, languageType);
        
        // 更新内存缓存
        updateMemoryCache();
        
        // 通知监听器
        notifyLanguageChanged(languageType);
    }
    
    /**
     * 获取当前语言类型
     */
    public int getCurrentLanguageType() {
        return currentLanguageType;
    }
    
    /**
     * 获取翻译文本
     * @param key 翻译键
     * @return 翻译文本，如果不存在则返回键本身
     */
    public String getString(String key) {
        if (key == null || key.trim().isEmpty()) {
            return key;
        }
        
        // 优先从内存缓存获取
        String cachedText = memoryCache.get(key);
        if (cachedText != null) {
            return cachedText;
        }
        
        // 从翻译数据获取
        if (translationData != null) {
            String translation = translationData.getTranslation(currentLanguageType, key);
            if (translation != null && !translation.equals(key)) {
                // 缓存到内存
                memoryCache.put(key, translation);
                return translation;
            }
        }
        
        // 如果没有找到翻译，返回键本身
        Log.w(TAG, "未找到翻译: " + key + " (语言: " + currentLanguageType + ")");
        return key;
    }
    
    /**
     * 获取格式化翻译文本
     * @param key 翻译键
     * @param args 格式化参数
     * @return 格式化后的翻译文本
     */
    public String getString(String key, Object... args) {
        String template = getString(key);
        if (args == null || args.length == 0) {
            return template;
        }
        
        try {
            return String.format(template, args);
        } catch (Exception e) {
            Log.w(TAG, "格式化翻译文本失败: " + key, e);
            return template;
        }
    }
    
    /**
     * 检查是否有指定键的翻译
     */
    public boolean hasTranslation(String key) {
        if (key == null || translationData == null) {
            return false;
        }

        Map<String, String> translations = translationData.getTranslationByLanguageType(currentLanguageType);
        return translations != null && translations.containsKey(key);
    }

    /**
     * 获取指定语言的翻译文本（不受当前语言设置影响）
     * @param key 翻译键
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     * @return 翻译文本，如果不存在则返回键本身
     */
    public String getStringForLanguage(String key, int languageType) {
        if (key == null || key.trim().isEmpty()) {
            return key;
        }

        if (translationData != null) {
            String translation = translationData.getTranslation(languageType, key);
            if (translation != null && !translation.equals(key)) {
                return translation;
            }
        }

        return key;
    }

    /**
     * 切换语言并重启Activity（更彻底的解决方案）
     * @param languageType 语言类型
     * @param activity 当前Activity（可选，用于重启）
     */
    public void setLanguageAndRestart(int languageType, Activity activity) {
        // 先设置语言
        setLanguage(languageType);

        // 更新系统Locale
        updateSystemLocale(languageType);

        // 发送重启Activity的广播
        if (activity != null) {
            Intent restartIntent = new Intent(ACTION_RESTART_ACTIVITY);
            restartIntent.putExtra(EXTRA_LANGUAGE_TYPE, languageType);
            LocalBroadcastManager.getInstance(context).sendBroadcast(restartIntent);

            // 如果是MainActivity，需要特殊处理导航状态
            if (activity instanceof com.android.video.ui.activity.MainActivity) {
                com.android.video.ui.activity.MainActivity mainActivity =
                    (com.android.video.ui.activity.MainActivity) activity;

                // 重启Activity并导航到首页
                Intent intent = new Intent(activity, com.android.video.ui.activity.MainActivity.class);
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP | Intent.FLAG_ACTIVITY_NEW_TASK);
                intent.putExtra("navigate_to_home", true);
                activity.startActivity(intent);
                activity.finish();
            } else {
                // 其他Activity直接重启
                activity.recreate();
            }
        }
    }

    /**
     * 更新系统Locale
     */
    private void updateSystemLocale(int languageType) {
        try {
            Locale locale = getLocaleForLanguageType(languageType);

            Configuration config = context.getResources().getConfiguration();
            config.setLocale(locale);

            // 更新应用的Configuration
            context.getResources().updateConfiguration(config, context.getResources().getDisplayMetrics());

            Log.d(TAG, "系统Locale已更新为: " + locale.toString());

        } catch (Exception e) {
            Log.e(TAG, "更新系统Locale失败", e);
        }
    }

    /**
     * 根据语言类型获取Locale
     */
    private Locale getLocaleForLanguageType(int languageType) {
        switch (languageType) {
            case 1:
                return Locale.ENGLISH;
            case 2:
                return new Locale("ru"); // 俄语
            case 3:
                return new Locale("kk"); // 哈萨克语
            default:
                return Locale.ENGLISH;
        }
    }
    
    /**
     * 获取当前语言的描述
     */
    public String getCurrentLanguageDescription() {
        return UserSessionUtils.getLanguageDescription(context);
    }
    
    /**
     * 清除缓存
     */
    public void clearCache() {
        memoryCache.clear();
        cacheManager.clearCachedData(CACHE_KEY_TRANSLATIONS);
        Log.d(TAG, "翻译缓存已清除");
    }
    
    /**
     * 通知翻译加载完成
     */
    private void notifyTranslationLoaded() {
        if (localizationCallback != null) {
            localizationCallback.onTranslationLoaded();
        }
    }

    /**
     * 通知语言切换
     */
    private void notifyLanguageChanged(int languageType) {
        Log.d(TAG, "通知语言切换: " + languageType);

        // 通知注册的回调
        if (localizationCallback != null) {
            localizationCallback.onLanguageChanged(languageType);
        } else {
            Log.w(TAG, "LocalizationCallback为null，无法通知语言切换");
        }

        // 发送全局广播，确保所有Activity和Fragment都能收到通知
        Intent intent = new Intent(ACTION_LANGUAGE_CHANGED);
        intent.putExtra(EXTRA_LANGUAGE_TYPE, languageType);
        LocalBroadcastManager.getInstance(context).sendBroadcast(intent);
        Log.d(TAG, "已发送语言切换广播: " + languageType);

        // 强制清除内存缓存，确保使用新语言
        memoryCache.clear();
        updateMemoryCache();

        Log.d(TAG, "语言切换通知完成，内存缓存已更新");
    }

    /**
     * 通知错误
     */
    private void notifyError(String errorMessage) {
        if (localizationCallback != null) {
            localizationCallback.onError(errorMessage);
        }
    }
}
