package com.android.video.ui.component;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.graphics.Color;
import android.graphics.drawable.GradientDrawable;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.TypedValue;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.TextView;

import com.android.video.R;

/**
 * 弱网络警告提示组件
 * 在页面顶部显示网络状况不佳的提示信息
 * <AUTHOR> Team
 */
public class PoorNetworkWarningView extends FrameLayout {
    
    private static final String TAG = "PoorNetworkWarningView";
    private static final int SHOW_DURATION_MS = 3000; // 显示3秒
    private static final int ANIMATION_DURATION_MS = 300; // 动画时长300ms
    
    private TextView warningTextView;
    private Handler mainHandler;
    private Runnable hideRunnable;
    private boolean isShowing = false;
    
    public PoorNetworkWarningView(Context context) {
        super(context);
        init();
    }
    
    public PoorNetworkWarningView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public PoorNetworkWarningView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        mainHandler = new Handler(Looper.getMainLooper());
        setupView();
        setupHideRunnable();
        
        // 初始状态为隐藏
        setVisibility(View.GONE);
        setAlpha(0f);
    }
    
    private void setupView() {
        // 设置背景
        GradientDrawable background = new GradientDrawable();
        background.setColor(Color.parseColor("#E53E3E")); // 红色背景
        background.setCornerRadius(0); // 不设置圆角，占满宽度
        setBackground(background);
        
        // 设置布局参数
        LayoutParams layoutParams = new LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.WRAP_CONTENT
        );
        layoutParams.gravity = Gravity.TOP;
        setLayoutParams(layoutParams);
        
        // 设置内边距
        int paddingVertical = dpToPx(8);
        int paddingHorizontal = dpToPx(16);
        setPadding(paddingHorizontal, paddingVertical, paddingHorizontal, paddingVertical);
        
        // 创建文本视图
        warningTextView = new TextView(getContext());
        warningTextView.setText("The current network is poor");
        warningTextView.setTextColor(Color.WHITE);
        warningTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 12);
        warningTextView.setGravity(Gravity.CENTER);
        
        // 设置文本视图的布局参数
        LayoutParams textParams = new LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.WRAP_CONTENT
        );
        textParams.gravity = Gravity.CENTER;
        warningTextView.setLayoutParams(textParams);
        
        addView(warningTextView);
    }
    
    private void setupHideRunnable() {
        hideRunnable = new Runnable() {
            @Override
            public void run() {
                hideWithAnimation();
            }
        };
    }
    
    /**
     * 显示弱网络警告
     */
    public void showWarning() {
        if (isShowing) {
            // 如果正在显示，重置计时器
            resetHideTimer();
            return;
        }
        
        isShowing = true;
        
        // 取消之前的隐藏任务
        mainHandler.removeCallbacks(hideRunnable);
        
        // 显示动画
        setVisibility(View.VISIBLE);
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
        fadeIn.setDuration(ANIMATION_DURATION_MS);
        fadeIn.start();
        
        // 设置自动隐藏
        mainHandler.postDelayed(hideRunnable, SHOW_DURATION_MS);
    }
    
    /**
     * 隐藏警告（带动画）
     */
    private void hideWithAnimation() {
        if (!isShowing) {
            return;
        }
        
        ObjectAnimator fadeOut = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        fadeOut.setDuration(ANIMATION_DURATION_MS);
        fadeOut.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(View.GONE);
                isShowing = false;
            }
        });
        fadeOut.start();
    }
    
    /**
     * 立即隐藏警告（无动画）
     */
    public void hideImmediately() {
        mainHandler.removeCallbacks(hideRunnable);
        setVisibility(View.GONE);
        setAlpha(0f);
        isShowing = false;
    }
    
    /**
     * 重置隐藏计时器
     */
    private void resetHideTimer() {
        mainHandler.removeCallbacks(hideRunnable);
        mainHandler.postDelayed(hideRunnable, SHOW_DURATION_MS);
    }
    
    /**
     * 检查是否正在显示
     */
    public boolean isShowing() {
        return isShowing;
    }
    
    /**
     * 设置警告文本
     */
    public void setWarningText(String text) {
        if (warningTextView != null) {
            warningTextView.setText(text);
        }
    }
    
    /**
     * 设置文本大小
     */
    public void setWarningTextSize(float sizeSp) {
        if (warningTextView != null) {
            warningTextView.setTextSize(TypedValue.COMPLEX_UNIT_SP, sizeSp);
        }
    }
    
    /**
     * 设置背景颜色
     */
    public void setWarningBackgroundColor(int color) {
        GradientDrawable background = new GradientDrawable();
        background.setColor(color);
        background.setCornerRadius(0);
        setBackground(background);
    }
    
    /**
     * 设置文本颜色
     */
    public void setWarningTextColor(int color) {
        if (warningTextView != null) {
            warningTextView.setTextColor(color);
        }
    }
    
    /**
     * dp转px
     */
    private int dpToPx(int dp) {
        return (int) TypedValue.applyDimension(
            TypedValue.COMPLEX_UNIT_DIP,
            dp,
            getContext().getResources().getDisplayMetrics()
        );
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        if (mainHandler != null) {
            mainHandler.removeCallbacks(hideRunnable);
        }
    }
    
    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        cleanup();
    }
    
    /**
     * 添加到指定的父视图
     * @param parent 父视图
     */
    public void attachToParent(ViewGroup parent) {
        if (getParent() != null) {
            ((ViewGroup) getParent()).removeView(this);
        }
        
        // 设置布局参数，确保在顶部显示
        LayoutParams params = new LayoutParams(
            LayoutParams.MATCH_PARENT,
            LayoutParams.WRAP_CONTENT
        );
        params.gravity = Gravity.TOP;
        
        parent.addView(this, params);
    }
    
    /**
     * 从父视图中移除
     */
    public void detachFromParent() {
        if (getParent() != null) {
            ((ViewGroup) getParent()).removeView(this);
        }
    }
}
