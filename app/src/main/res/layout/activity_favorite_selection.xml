<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- Layout Guidelines for responsive design -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.05" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.95" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_header_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.25" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_footer_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.85" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.95" />

    <!-- Top shadow overlay -->
    <View
        android:id="@+id/view_top_shadow"
        android:layout_width="0dp"
        android:layout_height="@dimen/favorite_selection_top_shadow_height"
        android:background="@drawable/top_shadow_gradient"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/iv_back_button"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:background="?attr/selectableItemBackgroundBorderless"
        android:contentDescription="@string/back_button"
        android:src="@drawable/login_btn_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Header Content Chain -->
    <!-- Title Text -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/favorite_selection_title_margin_top"
        android:fontFamily="sans-serif-medium"
        android:gravity="center"
        android:letterSpacing="0.018"
        android:text="Hello, welcome to Qdrama_"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/favorite_selection_title_text_size"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end" />

    <!-- Description Text -->
    <TextView
        android:id="@+id/tv_description"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/favorite_selection_description_margin_top"
        android:fontFamily="sans-serif-light"
        android:gravity="center"
        android:letterSpacing="0.018"
        android:text="Please select your favorite film and television category to help us recommend videos that meet your taste"
        android:textColor="#CCFFFFFF"
        android:textSize="@dimen/favorite_selection_description_text_size"
        app:layout_constraintTop_toBottomOf="@+id/tv_title"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end" />

    <!-- ViewPager2 for Tags with responsive constraints -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_tags"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginStart="12dp"
        android:layout_marginEnd="12dp"
        android:layout_marginTop="44dp"
        app:layout_constraintTop_toBottomOf="@+id/tv_description"
        app:layout_constraintBottom_toTopOf="@+id/ll_page_indicator"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.0"
        app:layout_constraintVertical_chainStyle="packed" />

    <!-- Page Indicator with Chain -->
    <LinearLayout
        android:id="@+id/ll_page_indicator"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/vp_tags"
        app:layout_constraintBottom_toTopOf="@+id/btn_complete_selection"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.5" />

    <!-- Complete Selection Button with proper bottom positioning -->
    <Button
        android:id="@+id/btn_complete_selection"
        android:layout_width="0dp"
        android:layout_height="@dimen/favorite_selection_complete_button_height"
        android:layout_marginStart="@dimen/favorite_selection_complete_button_margin_horizontal"
        android:layout_marginEnd="@dimen/favorite_selection_complete_button_margin_horizontal"
        android:layout_marginTop="@dimen/favorite_selection_indicator_button_spacing"
        android:background="@drawable/button_purple_selector"
        android:fontFamily="sans-serif-medium"
        android:text="Complete selection"
        android:textAllCaps="false"
        android:textColor="@color/white"
        android:textSize="15sp"
        app:layout_constraintTop_toBottomOf="@+id/ll_page_indicator"
        app:layout_constraintBottom_toTopOf="@+id/guideline_bottom_safe"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="0.8" />

</androidx.constraintlayout.widget.ConstraintLayout>
