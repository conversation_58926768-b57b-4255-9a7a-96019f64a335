package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 删除章节API响应模型
 * <AUTHOR> Team
 */
public class DeleteChapterResponseModel {
    
    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 响应数据
     */
    @SerializedName("data")
    private String data;

    public DeleteChapterResponseModel() {
    }

    public DeleteChapterResponseModel(String code, String message, String data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getData() {
        return data;
    }

    public void setData(String data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null && !message.trim().isEmpty() ? message : "Unknown error";
    }

    /**
     * 检查是否删除成功
     * @return 是否删除成功
     */
    public boolean isDeleteSuccess() {
        return isSuccess() && "successfully delete".equals(data);
    }

    @Override
    public String toString() {
        return "DeleteChapterResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data='" + data + '\'' +
                ", isSuccess=" + isSuccess() +
                ", isDeleteSuccess=" + isDeleteSuccess() +
                '}';
    }
}
