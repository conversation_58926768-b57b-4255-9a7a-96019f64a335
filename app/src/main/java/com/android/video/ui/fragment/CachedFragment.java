package com.android.video.ui.fragment;

import android.os.Bundle;
import android.util.Log;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.android.video.cache.DataCacheManager;

/**
 * 带缓存功能的Fragment基类
 * 提供统一的缓存管理功能，避免Fragment切换时重复加载数据
 * <AUTHOR> Team
 */
public abstract class CachedFragment extends BaseFullScreenFragment {
    
    private static final String TAG = "CachedFragment";
    
    // 缓存状态
    protected boolean dataLoaded = false;
    protected boolean isDataCached = false;
    
    // 缓存管理器
    protected DataCacheManager cacheManager;
    
    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        // 初始化缓存管理器
        if (getContext() != null) {
            cacheManager = DataCacheManager.getInstance(getContext());
        }
        
        Log.d(TAG, getClass().getSimpleName() + " onCreate called");
    }
    
    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, getClass().getSimpleName() + " onResume called");
        
        // 智能数据加载策略
        if (shouldLoadData()) {
            Log.d(TAG, "需要加载数据，开始加载...");
            loadDataWithCache();
        } else {
            Log.d(TAG, "使用缓存数据，无需重新加载");
            onDataRestored();
        }
    }
    
    /**
     * 判断是否需要加载数据
     * 子类可以重写此方法来自定义加载策略
     */
    protected boolean shouldLoadData() {
        return !dataLoaded || !isDataCached || !hasValidCachedData();
    }
    
    /**
     * 检查是否有有效的缓存数据
     * 子类需要实现此方法来检查具体的缓存数据
     */
    protected abstract boolean hasValidCachedData();
    
    /**
     * 带缓存支持的数据加载
     */
    protected void loadDataWithCache() {
        // 先尝试从缓存恢复数据
        if (restoreDataFromCache()) {
            Log.d(TAG, "成功从缓存恢复数据");
            isDataCached = true;
            dataLoaded = true;
            onDataRestored();
            return;
        }
        
        // 缓存中没有数据，进行网络请求
        Log.d(TAG, "缓存中无数据，开始网络请求");
        loadDataFromNetwork();
    }
    
    /**
     * 从缓存恢复数据
     * 子类需要实现此方法来恢复具体的缓存数据
     */
    protected abstract boolean restoreDataFromCache();
    
    /**
     * 从网络加载数据
     * 子类需要实现此方法来进行网络请求
     */
    protected abstract void loadDataFromNetwork();
    
    /**
     * 保存数据到缓存
     * 子类需要实现此方法来保存具体的数据
     */
    protected abstract void saveDataToCache();
    
    /**
     * 数据恢复完成回调
     * 子类可以重写此方法来处理数据恢复后的UI更新
     */
    protected void onDataRestored() {
        // 默认实现为空，子类可以重写
    }
    
    /**
     * 数据加载成功回调
     * 在网络请求成功后调用此方法来保存数据并更新状态
     */
    protected void onDataLoadSuccess() {
        saveDataToCache();
        dataLoaded = true;
        isDataCached = true;
        Log.d(TAG, "数据加载成功并已缓存");
    }
    
    /**
     * 刷新数据（清除缓存并重新加载）
     */
    protected void refreshData() {
        Log.d(TAG, "刷新数据，清除缓存");
        
        // 清除缓存状态
        isDataCached = false;
        dataLoaded = false;
        
        // 清除缓存数据
        clearCacheData();
        
        // 重新加载数据
        loadDataFromNetwork();
    }
    
    /**
     * 清除缓存数据
     * 子类需要实现此方法来清除具体的缓存数据
     */
    protected abstract void clearCacheData();
    
    /**
     * 获取缓存键前缀
     * 子类可以重写此方法来提供自定义的缓存键前缀
     */
    protected String getCacheKeyPrefix() {
        return getClass().getSimpleName().toLowerCase();
    }
    
    /**
     * 生成缓存键
     */
    protected String generateCacheKey(String suffix) {
        return getCacheKeyPrefix() + "_" + suffix;
    }
    
    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, getClass().getSimpleName() + " onDestroyView called");
        
        // 注意：不要在这里重置缓存状态，因为Fragment可能会被重新创建
        // 缓存状态应该在Fragment实例级别保持
    }
    
    @Override
    public void onDestroy() {
        super.onDestroy();
        Log.d(TAG, getClass().getSimpleName() + " onDestroy called");
        
        // 清理缓存管理器引用
        cacheManager = null;
    }
}
