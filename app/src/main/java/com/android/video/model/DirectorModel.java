package com.android.video.model;

import java.io.Serializable;

/**
 * 导演数据模型类
 * <AUTHOR>
 */
public class DirectorModel implements Serializable {

    private static final long serialVersionUID = 1L;
    
    private String id;
    private String name;
    private String avatarUrl;
    private String biography;
    private String nationality;

    /**
     * 默认构造函数
     */
    public DirectorModel() {
        this.id = "";
        this.name = "";
        this.avatarUrl = "";
        this.biography = "";
        this.nationality = "";
    }

    /**
     * 完整构造函数
     */
    public DirectorModel(String id, String name, String avatarUrl, String biography, String nationality) {
        this.id = id;
        this.name = name;
        this.avatarUrl = avatarUrl;
        this.biography = biography;
        this.nationality = nationality;
    }

    /**
     * 便利构造函数 - 基本信息
     */
    public DirectorModel(String id, String name, String avatarUrl) {
        this.id = id;
        this.name = name;
        this.avatarUrl = avatarUrl;
        this.biography = "";
        this.nationality = "";
    }

    // Getter和Setter方法
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAvatarUrl() {
        return avatarUrl;
    }
    
    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }
    
    public String getBiography() {
        return biography;
    }
    
    public void setBiography(String biography) {
        this.biography = biography;
    }
    
    public String getNationality() {
        return nationality;
    }
    
    public void setNationality(String nationality) {
        this.nationality = nationality;
    }

    /**
     * 便利方法
     */
    public String getDisplayName() {
        return name != null && !name.isEmpty() ? name : "Unknown Director";
    }
    
    public boolean isEmpty() {
        return name == null || name.trim().isEmpty();
    }
    
    public boolean hasAvatar() {
        return avatarUrl != null && !avatarUrl.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "DirectorModel{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", nationality='" + nationality + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        DirectorModel that = (DirectorModel) obj;
        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
