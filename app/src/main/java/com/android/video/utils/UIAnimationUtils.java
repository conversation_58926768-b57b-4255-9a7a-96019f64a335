package com.android.video.utils;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.OvershootInterpolator;

/**
 * UI动画工具类
 * <p>
 * 根据设计需求实现统一的UI动画效果
 * </p>
 */
public class UIAnimationUtils {
    
    // 动画时长常量（毫秒）
    public static final int BUTTON_CLICK_DURATION = 200;
    public static final int PAGE_TRANSITION_DURATION = 300;
    public static final int BACKGROUND_TRANSITION_DURATION = 200;
    public static final int DIALOG_ANIMATION_DURATION = 200;
    
    // 缩放比例常量
    public static final float BUTTON_SCALE_RATIO = 0.9f;
    
    /**
     * 按钮点击反馈动画
     * <p>
     * 需求：缩放比例 0.9 倍，响应时间 0.2 秒
     * </p>
     */
    public static void animateButtonClick(View button) {
        if (button == null) return;
        
        // 取消之前的动画
        button.clearAnimation();
        
        // 创建缩放动画
        ObjectAnimator scaleXDown = ObjectAnimator.ofFloat(button, "scaleX", 1f, BUTTON_SCALE_RATIO);
        ObjectAnimator scaleYDown = ObjectAnimator.ofFloat(button, "scaleY", 1f, BUTTON_SCALE_RATIO);
        ObjectAnimator scaleXUp = ObjectAnimator.ofFloat(button, "scaleX", BUTTON_SCALE_RATIO, 1f);
        ObjectAnimator scaleYUp = ObjectAnimator.ofFloat(button, "scaleY", BUTTON_SCALE_RATIO, 1f);
        
        // 设置动画时长
        scaleXDown.setDuration(BUTTON_CLICK_DURATION / 2);
        scaleYDown.setDuration(BUTTON_CLICK_DURATION / 2);
        scaleXUp.setDuration(BUTTON_CLICK_DURATION / 2);
        scaleYUp.setDuration(BUTTON_CLICK_DURATION / 2);
        
        // 设置插值器
        AccelerateDecelerateInterpolator interpolator = new AccelerateDecelerateInterpolator();
        scaleXDown.setInterpolator(interpolator);
        scaleYDown.setInterpolator(interpolator);
        scaleXUp.setInterpolator(interpolator);
        scaleYUp.setInterpolator(interpolator);
        
        // 组合动画
        AnimatorSet downSet = new AnimatorSet();
        downSet.playTogether(scaleXDown, scaleYDown);
        
        AnimatorSet upSet = new AnimatorSet();
        upSet.playTogether(scaleXUp, scaleYUp);
        
        AnimatorSet fullSet = new AnimatorSet();
        fullSet.playSequentially(downSet, upSet);
        fullSet.start();
    }
    
    /**
     * 页面淡入动画
     * <p>
     * 需求：淡入淡出过渡动画，过渡时长 0.3 秒
     * </p>
     */
    public static void animatePageFadeIn(View view) {
        if (view == null) return;
        
        view.setAlpha(0f);
        view.animate()
                .alpha(1f)
                .setDuration(PAGE_TRANSITION_DURATION)
                .setInterpolator(new DecelerateInterpolator())
                .start();
    }
    
    /**
     * 页面淡出动画
     */
    public static void animatePageFadeOut(View view, Runnable onComplete) {
        if (view == null) return;
        
        view.animate()
                .alpha(0f)
                .setDuration(PAGE_TRANSITION_DURATION)
                .setInterpolator(new AccelerateDecelerateInterpolator())
                .withEndAction(onComplete)
                .start();
    }
    
    /**
     * 弹窗从中心放大动画
     * <p>
     * 需求：从中心放大的过渡效果，时长 0.2 秒
     * </p>
     */
    public static void animateDialogScaleIn(View dialog) {
        if (dialog == null) return;
        
        // 设置初始状态
        dialog.setScaleX(0f);
        dialog.setScaleY(0f);
        dialog.setAlpha(0f);
        
        // 创建动画
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(dialog, "scaleX", 0f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(dialog, "scaleY", 0f, 1f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(dialog, "alpha", 0f, 1f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY, alpha);
        animatorSet.setDuration(DIALOG_ANIMATION_DURATION);
        animatorSet.setInterpolator(new OvershootInterpolator(1.1f));
        animatorSet.start();
    }
    
    /**
     * 弹窗向中心缩小动画
     * <p>
     * 需求：向中心缩小的过渡效果，时长 0.2 秒
     * </p>
     */
    public static void animateDialogScaleOut(View dialog, Runnable onComplete) {
        if (dialog == null) return;
        
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(dialog, "scaleX", 1f, 0f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(dialog, "scaleY", 1f, 0f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(dialog, "alpha", 1f, 0f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(scaleX, scaleY, alpha);
        animatorSet.setDuration(DIALOG_ANIMATION_DURATION);
        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
        
        if (onComplete != null) {
            animatorSet.addListener(new android.animation.AnimatorListenerAdapter() {
                @Override
                public void onAnimationEnd(android.animation.Animator animation) {
                    onComplete.run();
                }
            });
        }
        
        animatorSet.start();
    }
    
    /**
     * 加载状态动画
     * <p>
     * 需求：分页加载时显示APP图 + "Swipe up to see more"
     * </p>
     */
    public static void animateLoadingState(View loadingView, boolean show) {
        if (loadingView == null) return;
        
        if (show) {
            loadingView.setVisibility(View.VISIBLE);
            loadingView.setAlpha(0f);
            loadingView.setTranslationY(50f);
            
            loadingView.animate()
                    .alpha(1f)
                    .translationY(0f)
                    .setDuration(300)
                    .setInterpolator(new DecelerateInterpolator())
                    .start();
        } else {
            loadingView.animate()
                    .alpha(0f)
                    .translationY(-50f)
                    .setDuration(200)
                    .setInterpolator(new AccelerateDecelerateInterpolator())
                    .withEndAction(() -> loadingView.setVisibility(View.GONE))
                    .start();
        }
    }
    
    /**
     * 模糊到清晰过渡效果（简化版本）
     * <p>
     * 需求：从后台切换至前台时，添加 0.2 秒的模糊到清晰的过渡效果
     * </p>
     */
    public static void animateBlurToClear(View view) {
        if (view == null) return;
        
        // 简化实现：使用alpha和scale模拟模糊到清晰效果
        view.setAlpha(0.3f);
        view.setScaleX(1.05f);
        view.setScaleY(1.05f);
        
        view.animate()
                .alpha(1f)
                .scaleX(1f)
                .scaleY(1f)
                .setDuration(BACKGROUND_TRANSITION_DURATION)
                .setInterpolator(new DecelerateInterpolator())
                .start();
    }
}
