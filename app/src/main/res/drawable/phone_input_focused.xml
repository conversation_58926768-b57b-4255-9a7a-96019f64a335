<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Base shape with transparent background and main border -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="0.5dp"
                android:color="#ffb936cf" />
            <corners android:radius="16dp" />
        </shape>
    </item>

    <!-- Inner shadow layer 1 - Simulating outer shadow inward with offset (2dp, 1.5dp) -->
    <item android:top="1dp" android:left="1dp" android:right="0dp" android:bottom="0dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="1dp"
                android:color="#3ddf00ff" />
            <corners android:radius="14dp" />
        </shape>
    </item>

    <!-- Inner shadow layer 2 - Additional depth -->
    <item android:top="2dp" android:left="2dp" android:right="1dp" android:bottom="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="0.5dp"
                android:color="#20df00ff" />
            <corners android:radius="13dp" />
        </shape>
    </item>

    <!-- Inner shadow layer 3 - Subtle inner glow -->
    <item android:top="3dp" android:left="3dp" android:right="2dp" android:bottom="2dp">
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <stroke
                android:width="0.5dp"
                android:color="#10df00ff" />
            <corners android:radius="12dp" />
        </shape>
    </item>

</layer-list>
