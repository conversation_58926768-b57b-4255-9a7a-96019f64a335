<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 聚焦状态 - 紫色边框 -->
    <item android:state_focused="true">
        <shape android:shape="rectangle">
            <stroke android:width="0.5dp" android:color="#ffb936cf" />
            <solid android:color="#14b936cf" />
            <corners android:radius="16dp" />
        </shape>
    </item>

    <!-- 默认状态 - 白色边框 -->
    <item>
        <shape android:shape="rectangle">
            <stroke android:width="0.5dp" android:color="#8fffffff" />
            <solid android:color="#14ffffff" />
            <corners android:radius="16dp" />
        </shape>
    </item>

</selector>
