package com.android.video.ui.activity;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.text.SpannableString;
import android.text.Spanned;
import android.text.style.UnderlineSpan;
import android.text.style.ReplacementSpan;
import android.graphics.Paint;
import android.graphics.Canvas;
import android.view.View;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.widget.Button;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.OnBackPressedCallback;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.core.app.ActivityOptionsCompat;
import androidx.core.content.ContextCompat;
import androidx.viewpager2.widget.ViewPager2;

import com.android.video.R;
import com.android.video.adapter.TagPagerAdapter;
import com.android.video.model.TagModel;
import com.android.video.utils.UserSessionUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.network.LabelApiUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Favorite Selection Activity - Handles favorite tag selection functionality
 * 继承BaseFullScreenActivity以获得统一的全屏状态栏处理
 * <AUTHOR>
 */
public class FavoriteSelectionActivity extends BaseFullScreenActivity {

    // 自定义下划线Span
    private static class CustomUnderlineSpan extends ReplacementSpan {
        private final int underlineColor;
        private final float underlineThickness;
        private final float density;

        public CustomUnderlineSpan(int color, float thickness, float density) {
            this.underlineColor = color;
            this.underlineThickness = thickness;
            this.density = density;
        }

        @Override
        public int getSize(Paint paint, CharSequence text, int start, int end, Paint.FontMetricsInt fm) {
            return (int) paint.measureText(text, start, end);
        }

        @Override
        public void draw(Canvas canvas, CharSequence text, int start, int end, float x, int top, int y, int bottom, Paint paint) {
            // 先绘制下划线（底层）
            Paint underlinePaint = new Paint();
            underlinePaint.setColor(underlineColor);
            underlinePaint.setStrokeWidth(underlineThickness);

            float textWidth = paint.measureText(text, start, end);
            float underlineY = y + paint.descent() + underlineThickness / 2 - 5 * density;
            canvas.drawLine(x, underlineY, x + textWidth, underlineY, underlinePaint);

            // 再绘制文字（上层）- 确保文字颜色为纯白色
            Paint textPaint = new Paint(paint);
            textPaint.setColor(0xFFFFFFFF); // 纯白色
            canvas.drawText(text, start, end, x, y, textPaint);
        }
    }

    private ConstraintLayout clRootLayout;
    private ImageView ivBackButton;
    private TextView tvTitle;
    private TextView tvDescription;
    private ViewPager2 vpTags;
    private LinearLayout llPageIndicator;
    private Button btnCompleteSelection;
    private Handler mainHandler;
    
    private TagPagerAdapter tagPagerAdapter;
    private List<TagModel> allTags;
    private List<View> indicatorDots;
    private boolean isLoadingTags = false;

    private static final int TOTAL_PAGES = 3;
    private static final int TAGS_PER_PAGE = 12;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        setContentView(R.layout.activity_favorite_selection);

        mainHandler = new Handler(Looper.getMainLooper());

        initViews();
        generateTags();
        setupViewPager();
        setupPageIndicator();
        setupClickListeners();
        setupAnimations();
        setupDescriptionText();
        setupBackPressedCallback();
    }



    private void initViews() {
        clRootLayout = findViewById(R.id.cl_root_layout);
        ivBackButton = findViewById(R.id.iv_back_button);
        tvTitle = findViewById(R.id.tv_title);
        tvDescription = findViewById(R.id.tv_description);
        vpTags = findViewById(R.id.vp_tags);
        llPageIndicator = findViewById(R.id.ll_page_indicator);
        btnCompleteSelection = findViewById(R.id.btn_complete_selection);
    }

    private void generateTags() {
        // 环境判断：dev环境使用静态数据，prod/test环境从API获取
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境：使用静态标签数据
            generateStaticTags();
            setupViewPager();
        } else {
            // prod/test环境：从API获取标签数据
            loadTagsFromApi();
        }
    }

    /**
     * 生成静态标签数据（dev环境）
     */
    private void generateStaticTags() {
        allTags = new ArrayList<>();

        // Generate Romance tags (12)
        String[] romanceTags = {
            "Sweet Romance", "First Love", "Campus Love", "Office Romance",
            "Childhood Sweethearts", "Secret Crush", "Love Triangle", "Reunion Love",
            "Fake Dating", "Enemies to Lovers", "Friends to Lovers", "Slow Burn"
        };

        // Generate Toxic Romance tags (12)
        String[] toxicRomanceTags = {
            "Obsessive Love", "Forbidden Love", "Age Gap", "Power Imbalance",
            "Jealousy Drama", "Love Rival", "Betrayal", "Cheating",
            "Manipulation", "Possessive", "Stalking", "Revenge Love"
        };

        // Generate Super Romance tags (12)
        String[] superRomanceTags = {
            "Fantasy Romance", "Time Travel", "Reincarnation", "CEO Romance",
            "Billionaire", "Mafia Romance", "Royal Romance", "Vampire Love",
            "Angel & Demon", "Werewolf", "Magic Romance", "Parallel Universe"
        };

        // Add Romance tags
        for (String tag : romanceTags) {
            allTags.add(new TagModel(tag, "Romance"));
        }

        // Add Toxic Romance tags
        for (String tag : toxicRomanceTags) {
            allTags.add(new TagModel(tag, "Toxic Romance"));
        }

        // Add Super Romance tags
        for (String tag : superRomanceTags) {
            allTags.add(new TagModel(tag, "Super Romance"));
        }
    }

    /**
     * 从API加载标签数据（prod/test环境）
     */
    private void loadTagsFromApi() {
        if (isLoadingTags) {
            return; // 防止重复请求
        }

        isLoadingTags = true;
        showLoadingState();

        LabelApiUtils.getLabels(this, new LabelApiUtils.LabelCallback() {
            @Override
            public void onSuccess(List<TagModel> tags) {
                isLoadingTags = false;
                hideLoadingState();

                allTags = new ArrayList<>(tags);
                setupViewPager();

                Toast.makeText(FavoriteSelectionActivity.this,
                              "Labels loaded successfully",
                              Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String errorMessage) {
                isLoadingTags = false;
                hideLoadingState();

                // 显示错误提示并提供重试选项
                showErrorWithRetry(errorMessage);
            }
        });
    }

    private void setupViewPager() {
        tagPagerAdapter = new TagPagerAdapter(allTags);
        tagPagerAdapter.setOnTagSelectionChangeListener((tag, isSelected) -> {
            // Handle tag selection change
            updateCompleteButtonState();
        });

        vpTags.setAdapter(tagPagerAdapter);
        vpTags.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);

        // Setup page change listener
        vpTags.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                updatePageIndicator(position);
            }
        });
    }

    private void setupPageIndicator() {
        indicatorDots = new ArrayList<>();

        for (int i = 0; i < TOTAL_PAGES; i++) {
            View dot = new View(this);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                (int) (8 * getResources().getDisplayMetrics().density),
                (int) (8 * getResources().getDisplayMetrics().density)
            );
            params.setMargins(
                (int) (9 * getResources().getDisplayMetrics().density),
                0,
                (int) (9 * getResources().getDisplayMetrics().density),
                0
            );
            dot.setLayoutParams(params);
            dot.setBackgroundResource(R.drawable.page_indicator_selector);
            dot.setSelected(i == 0); // First dot selected by default

            indicatorDots.add(dot);
            llPageIndicator.addView(dot);
        }
    }

    private void updatePageIndicator(int selectedPosition) {
        for (int i = 0; i < indicatorDots.size(); i++) {
            indicatorDots.get(i).setSelected(i == selectedPosition);
        }
    }

    private void setupClickListeners() {
        ivBackButton.setOnClickListener(v -> handleBackButton());
        
        btnCompleteSelection.setOnClickListener(v -> {
            animateButton(v);
            handleCompleteSelection();
        });
    }

    private void setupAnimations() {
        // Add entrance animations
        animateViewEntrance(tvTitle, 200);
        animateViewEntrance(tvDescription, 300);
        animateViewEntrance(vpTags, 400);
        animateViewEntrance(llPageIndicator, 500);
        animateViewEntrance(btnCompleteSelection, 600);
    }

    private void animateViewEntrance(View view, long delay) {
        view.setAlpha(0f);
        view.setTranslationY(50f);
        
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(view, "alpha", 0f, 1f);
        ObjectAnimator translateAnimator = ObjectAnimator.ofFloat(view, "translationY", 50f, 0f);
        
        AnimatorSet animatorSet = new AnimatorSet();
        animatorSet.playTogether(alphaAnimator, translateAnimator);
        animatorSet.setDuration(500);
        animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
        animatorSet.setStartDelay(delay);
        animatorSet.start();
    }

    private void animateButton(View button) {
        // 使用统一的按钮点击动画（0.9倍缩放，0.2秒）
        com.android.video.utils.UIAnimationUtils.animateButtonClick(button);
    }

    private void setupDescriptionText() {
        // 设置短标题
        tvTitle.setText("Hello, welcome to Ve");

        // 设置长标题为三行显示
        String text = "Please select your favorite\ncategory to help us\nrecommend more series";
        SpannableString spannableString = new SpannableString(text);

        // Add special styling to "favorite"
        int favoriteStartIndex = text.indexOf("favorite");
        int favoriteEndIndex = favoriteStartIndex + "favorite".length();
        if (favoriteStartIndex != -1) {
            // 设置为白色粗体
            spannableString.setSpan(new android.text.style.ForegroundColorSpan(0xFFFFFFFF), favoriteStartIndex, favoriteEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new android.text.style.StyleSpan(android.graphics.Typeface.BOLD), favoriteStartIndex, favoriteEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            // 添加7dp厚度的紫色下划线
            float density = getResources().getDisplayMetrics().density;
            float thickness = density * 7; // 7dp转换为px
            spannableString.setSpan(new CustomUnderlineSpan(0xFFB936CF, thickness, density), favoriteStartIndex, favoriteEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        // Add special styling to "category"
        int categoryStartIndex = text.indexOf("category");
        int categoryEndIndex = categoryStartIndex + "category".length();
        if (categoryStartIndex != -1) {
            // 设置为白色粗体
            spannableString.setSpan(new android.text.style.ForegroundColorSpan(0xFFFFFFFF), categoryStartIndex, categoryEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            spannableString.setSpan(new android.text.style.StyleSpan(android.graphics.Typeface.BOLD), categoryStartIndex, categoryEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
            // 添加7dp厚度的紫色下划线
            float density = getResources().getDisplayMetrics().density;
            float thickness = density * 7; // 7dp转换为px
            spannableString.setSpan(new CustomUnderlineSpan(0xFFB936CF, thickness, density), categoryStartIndex, categoryEndIndex, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE);
        }

        tvDescription.setText(spannableString);
    }

    private void updateCompleteButtonState() {
        List<String> selectedTagsList = tagPagerAdapter.getSelectedTagNames();
        btnCompleteSelection.setEnabled(!selectedTagsList.isEmpty());
        btnCompleteSelection.setAlpha(selectedTagsList.isEmpty() ? 0.5f : 1.0f);
    }

    private void handleBackButton() {
        finish();
    }

    private void handleCompleteSelection() {
        List<TagModel> selectedTags = tagPagerAdapter.getSelectedTags();

        if (selectedTags.isEmpty()) {
            Toast.makeText(this, "Please select at least one tag", Toast.LENGTH_SHORT).show();
            return;
        }

        // 环境判断：dev环境使用本地保存，prod/test环境提交到API
        if (EnvironmentConfigUtils.isDevelopment()) {
            // dev环境：本地保存
            saveTagsLocally(selectedTags);
        } else {
            // prod/test环境：提交到API
            submitTagsToApi(selectedTags);
        }
    }

    /**
     * 本地保存标签（dev环境）
     * @param selectedTags 选中的标签列表
     */
    private void saveTagsLocally(List<TagModel> selectedTags) {
        // 转换为标签名称列表
        List<String> selectedTagNames = new ArrayList<>();
        for (TagModel tag : selectedTags) {
            selectedTagNames.add(tag.getName());
        }

        // Convert List to Set for UserSessionUtils
        Set<String> selectedTagsSet = new HashSet<>(selectedTagNames);

        // Save selected tags to user session
        UserSessionUtils.saveFavoriteTags(this, selectedTagsSet);

        // Show success message
        Toast.makeText(this, "Favorite tags saved successfully!", Toast.LENGTH_SHORT).show();

        // Navigate to main activity
        navigateToMainActivity();
    }

    /**
     * 提交标签到API（prod/test环境）
     * @param selectedTags 选中的标签列表
     */
    private void submitTagsToApi(List<TagModel> selectedTags) {
        // 显示提交状态
        btnCompleteSelection.setEnabled(false);
        btnCompleteSelection.setText("Submitting...");

        LabelApiUtils.submitLabels(this, selectedTags, new LabelApiUtils.SubmitCallback() {
            @Override
            public void onSuccess() {
                // 恢复按钮状态
                btnCompleteSelection.setEnabled(true);
                btnCompleteSelection.setText("Complete Selection");

                // 同时保存到本地（用于离线访问）
                saveTagsLocally(selectedTags);
            }

            @Override
            public void onError(String errorMessage) {
                // 恢复按钮状态
                btnCompleteSelection.setEnabled(true);
                btnCompleteSelection.setText("Complete Selection");

                // 显示错误提示
                Toast.makeText(FavoriteSelectionActivity.this,
                              "Failed to submit tags: " + errorMessage,
                              Toast.LENGTH_LONG).show();

                // 提供降级选项：仅保存到本地
                showSubmitErrorWithFallback(selectedTags, errorMessage);
            }
        });
    }

    private void navigateToMainActivity() {
        Intent intent = new Intent(FavoriteSelectionActivity.this, MainActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        ActivityOptionsCompat options = ActivityOptionsCompat.makeCustomAnimation(
            this, android.R.anim.slide_in_left, android.R.anim.slide_out_right);
        startActivity(intent, options.toBundle());
        finish();
    }

    private void setupBackPressedCallback() {
        OnBackPressedCallback callback = new OnBackPressedCallback(true) {
            @Override
            public void handleOnBackPressed() {
                handleBackButton();
            }
        };
        getOnBackPressedDispatcher().addCallback(this, callback);
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        // 禁用完成按钮
        btnCompleteSelection.setEnabled(false);
        btnCompleteSelection.setText("Loading...");

        // 显示加载提示
        Toast.makeText(this, "Loading labels...", Toast.LENGTH_SHORT).show();
    }

    /**
     * 隐藏加载状态
     */
    private void hideLoadingState() {
        // 恢复完成按钮
        btnCompleteSelection.setEnabled(true);
        btnCompleteSelection.setText("Complete Selection");
    }

    /**
     * 显示错误并提供重试选项
     * @param errorMessage 错误消息
     */
    private void showErrorWithRetry(String errorMessage) {
        // 显示错误提示
        Toast.makeText(this, "Failed to load labels: " + errorMessage, Toast.LENGTH_LONG).show();

        // 降级到静态数据
        generateStaticTags();
        setupViewPager();

        // 显示降级提示
        Toast.makeText(this, "Using offline labels", Toast.LENGTH_SHORT).show();
    }

    /**
     * 显示提交错误并提供降级选项
     * @param selectedTags 选中的标签
     * @param errorMessage 错误消息
     */
    private void showSubmitErrorWithFallback(List<TagModel> selectedTags, String errorMessage) {
        // 可以在这里显示一个对话框，询问用户是否要仅保存到本地
        // 为了简化，这里直接提供本地保存选项
        Toast.makeText(this, "Would you like to save locally instead?", Toast.LENGTH_LONG).show();

        // 延迟提供本地保存选项
        mainHandler.postDelayed(() -> {
            // 这里可以显示一个确认对话框
            // 为了简化，直接执行本地保存
            saveTagsLocally(selectedTags);
        }, 2000);
    }
}
