<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 (Notified) -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <!-- 背景色 rgba(255, 255, 255, 0.10) -->
            <solid android:color="#1AFFFFFF" />
            <!-- 圆角 14dp -->
            <corners android:radius="14dp" />
        </shape>
    </item>
    
    <!-- 未选中状态 (Notify me) -->
    <item>
        <shape android:shape="rectangle">
            <!-- 半透明白色背景 -->
            <solid android:color="#33FFFFFF" />
            <!-- 圆角 14dp -->
            <corners android:radius="14dp" />
        </shape>
    </item>
    
</selector>
