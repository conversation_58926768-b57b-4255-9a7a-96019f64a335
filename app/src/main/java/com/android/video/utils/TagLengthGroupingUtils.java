package com.android.video.utils;

import android.content.Context;
import android.graphics.Paint;
import android.graphics.Typeface;
import android.util.DisplayMetrics;
import com.android.video.model.TagModel;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * 标签长度分组工具类
 * 实现标签按文本长度智能分组，优化FlexboxLayout中的排列效果
 * <AUTHOR>
 */
public class TagLengthGroupingUtils {
    
    // 标签长度分类阈值（基于字符数）
    private static final int SHORT_TAG_THRESHOLD = 8;  // 短标签：8个字符以下
    private static final int MEDIUM_TAG_THRESHOLD = 15; // 中等标签：8-15个字符
    // 长标签：15个字符以上
    
    // 标签宽度分类阈值（基于像素宽度）
    private static final float SHORT_WIDTH_DP = 80f;   // 短标签宽度阈值（dp）
    private static final float MEDIUM_WIDTH_DP = 120f; // 中等标签宽度阈值（dp）
    
    /**
     * 标签长度类型枚举
     */
    public enum TagLengthType {
        SHORT,   // 短标签
        MEDIUM,  // 中等标签
        LONG     // 长标签
    }
    
    /**
     * 标签分组结果类
     */
    public static class TagGroup {
        private TagLengthType type;
        private List<TagModel> tags;
        
        public TagGroup(TagLengthType type) {
            this.type = type;
            this.tags = new ArrayList<>();
        }
        
        public TagLengthType getType() {
            return type;
        }
        
        public List<TagModel> getTags() {
            return tags;
        }
        
        public void addTag(TagModel tag) {
            tags.add(tag);
        }
        
        public int size() {
            return tags.size();
        }
    }
    
    /**
     * 测量标签文本宽度
     * @param context 上下文
     * @param text 文本内容
     * @param textSize 文本大小（sp）
     * @return 文本宽度（像素）
     */
    public static float measureTagLength(Context context, String text, float textSize) {
        if (text == null || text.isEmpty()) {
            return 0f;
        }
        
        Paint paint = new Paint();
        paint.setTextSize(textSize * context.getResources().getDisplayMetrics().scaledDensity);
        paint.setTypeface(Typeface.DEFAULT);
        paint.setAntiAlias(true);
        
        return paint.measureText(text);
    }
    
    /**
     * 根据字符数判断标签长度类型
     * @param text 标签文本
     * @return 标签长度类型
     */
    public static TagLengthType getTagLengthTypeByCharCount(String text) {
        if (text == null) {
            return TagLengthType.SHORT;
        }
        
        int length = text.length();
        if (length <= SHORT_TAG_THRESHOLD) {
            return TagLengthType.SHORT;
        } else if (length <= MEDIUM_TAG_THRESHOLD) {
            return TagLengthType.MEDIUM;
        } else {
            return TagLengthType.LONG;
        }
    }
    
    /**
     * 根据像素宽度判断标签长度类型
     * @param context 上下文
     * @param text 标签文本
     * @param textSize 文本大小（sp）
     * @return 标签长度类型
     */
    public static TagLengthType getTagLengthTypeByWidth(Context context, String text, float textSize) {
        float widthPx = measureTagLength(context, text, textSize);
        DisplayMetrics metrics = context.getResources().getDisplayMetrics();
        float widthDp = widthPx / metrics.density;
        
        if (widthDp <= SHORT_WIDTH_DP) {
            return TagLengthType.SHORT;
        } else if (widthDp <= MEDIUM_WIDTH_DP) {
            return TagLengthType.MEDIUM;
        } else {
            return TagLengthType.LONG;
        }
    }
    
    /**
     * 按长度对标签进行分组
     * @param context 上下文
     * @param tags 原始标签列表
     * @param textSize 文本大小（sp）
     * @param useWidthMeasurement 是否使用宽度测量（true）还是字符数（false）
     * @return 分组后的标签列表
     */
    public static List<TagGroup> groupTagsByLength(Context context, List<TagModel> tags, 
                                                   float textSize, boolean useWidthMeasurement) {
        if (tags == null || tags.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 创建三个分组
        TagGroup shortGroup = new TagGroup(TagLengthType.SHORT);
        TagGroup mediumGroup = new TagGroup(TagLengthType.MEDIUM);
        TagGroup longGroup = new TagGroup(TagLengthType.LONG);
        
        // 对标签进行分类
        for (TagModel tag : tags) {
            TagLengthType type;
            if (useWidthMeasurement) {
                type = getTagLengthTypeByWidth(context, tag.getDisplayName(), textSize);
            } else {
                type = getTagLengthTypeByCharCount(tag.getDisplayName());
            }
            
            switch (type) {
                case SHORT:
                    shortGroup.addTag(tag);
                    break;
                case MEDIUM:
                    mediumGroup.addTag(tag);
                    break;
                case LONG:
                    longGroup.addTag(tag);
                    break;
            }
        }
        
        // 返回非空的分组
        List<TagGroup> result = new ArrayList<>();
        if (shortGroup.size() > 0) {
            result.add(shortGroup);
        }
        if (mediumGroup.size() > 0) {
            result.add(mediumGroup);
        }
        if (longGroup.size() > 0) {
            result.add(longGroup);
        }
        
        return result;
    }
    
    /**
     * 将分组后的标签重新组织为单一列表（按分组顺序排列）
     * @param tagGroups 分组列表
     * @return 重新排列的标签列表
     */
    public static List<TagModel> flattenGroupedTags(List<TagGroup> tagGroups) {
        List<TagModel> result = new ArrayList<>();
        for (TagGroup group : tagGroups) {
            result.addAll(group.getTags());
        }
        return result;
    }
    
    /**
     * 智能分组标签（推荐方法）
     * 结合字符数和宽度测量，提供最佳的分组效果
     * @param context 上下文
     * @param tags 原始标签列表
     * @param textSize 文本大小（sp）
     * @return 重新排列的标签列表
     */
    public static List<TagModel> smartGroupTags(Context context, List<TagModel> tags, float textSize) {
        // 使用宽度测量进行分组，更准确
        List<TagGroup> groups = groupTagsByLength(context, tags, textSize, true);
        return flattenGroupedTags(groups);
    }
    
    /**
     * 获取标签的显示优先级（用于排序）
     * 短标签优先级最高，长标签优先级最低
     * @param context 上下文
     * @param tag 标签
     * @param textSize 文本大小
     * @return 优先级值（越小优先级越高）
     */
    public static int getTagPriority(Context context, TagModel tag, float textSize) {
        TagLengthType type = getTagLengthTypeByWidth(context, tag.getDisplayName(), textSize);
        switch (type) {
            case SHORT:
                return 1;
            case MEDIUM:
                return 2;
            case LONG:
                return 3;
            default:
                return 4;
        }
    }
}
