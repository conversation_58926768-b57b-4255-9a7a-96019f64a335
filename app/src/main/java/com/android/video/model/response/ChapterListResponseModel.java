package com.android.video.model.response;

import com.android.video.model.ChapterInfo;
import com.android.video.model.DirectorInfo;
import com.android.video.model.FilmInfo;
import com.android.video.model.PerformerInfo;
import com.android.video.model.PlayProgressVO;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 章节列表响应数据模型
 * <p>
 * 用于映射章节列表API返回的JSON数据结构。
 * 包含短剧信息、播放进度和章节列表等详细信息。
 * </p>
 *
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "filmVo": {
 *       "filmTitle": "这是英语标题111",
 *       "cover": "https://www.baidu.com",
 *       "details": "这是简介",
 *       ...
 *     },
 *     "playProgress": {
 *       "playProgressId": "37cfb5f99dc314e885f24552af0a6f1c",
 *       "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
 *       "progress": 150,
 *       ...
 *     },
 *     "chapterList": [
 *       {
 *         "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
 *         "chapterEp": 1,
 *         "isCharge": 1,
 *         "points": 100,
 *         "isUnlock": 1,
 *         ...
 *       }
 *     ]
 *   }
 * }
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class ChapterListResponseModel {

    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;

    /**
     * 章节列表数据
     */
    @SerializedName("data")
    private ChapterListData data;

    /**
     * 默认构造函数
     */
    public ChapterListResponseModel() {
    }

    /**
     * 完整构造函数
     *
     * @param code 响应码
     * @param message 响应消息
     * @param data 章节列表数据
     */
    public ChapterListResponseModel(String code, String message, ChapterListData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public ChapterListData getData() {
        return data;
    }

    public void setData(ChapterListData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     *
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取错误信息
     *
     * @return 如果请求失败，返回错误信息；否则返回null
     */
    public String getErrorMessage() {
        return isSuccess() ? null : message;
    }

    @Override
    public String toString() {
        return "ChapterListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 章节列表数据
     * <p>
     * 包含短剧信息、播放进度和章节列表的完整信息。
     * </p>
     */
    public static class ChapterListData {

        /**
         * 短剧信息
         */
        @SerializedName("filmVo")
        private FilmInfo filmInfo;

        /**
         * 标签名称
         */
        @SerializedName("labelNames")
        private String labelNames;

        /**
         * 发布时间
         */
        @SerializedName("releaseTime")
        private String releaseTime;

        /**
         * 导演信息列表
         */
        @SerializedName("directorInfo")
        private List<DirectorInfo> directorInfo;

        /**
         * 演员信息列表
         */
        @SerializedName("performerInfo")
        private List<PerformerInfo> performerInfo;

        /**
         * 相似影片列表（推荐视频）
         */
        @SerializedName("similarFilmList")
        private List<FilmInfo> similarFilmList;

        /**
         * 订阅状态
         * 0=未订阅, 1=已订阅
         */
        @SerializedName("subscribeStatus")
        private Integer subscribeStatus;

        /**
         * 播放进度信息
         */
        @SerializedName("playProgress")
        private PlayProgressVO playProgressVO;

        /**
         * 章节列表
         */
        @SerializedName("chapterList")
        private List<ChapterInfo> chapterList;

        /**
         * 默认构造函数
         */
        public ChapterListData() {
        }

        /**
         * 完整构造函数
         *
         * @param filmInfo 短剧信息
         * @param labelNames 标签名称
         * @param releaseTime 发布时间
         * @param directorInfo 导演信息列表
         * @param performerInfo 演员信息列表
         * @param similarFilmList 相似影片列表
         * @param subscribeStatus 订阅状态
         * @param playProgressVO 播放进度信息
         * @param chapterList 章节列表
         */
        public ChapterListData(FilmInfo filmInfo, String labelNames, String releaseTime,
                              List<DirectorInfo> directorInfo, List<PerformerInfo> performerInfo,
                              List<FilmInfo> similarFilmList, Integer subscribeStatus,
                              PlayProgressVO playProgressVO, List<ChapterInfo> chapterList) {
            this.filmInfo = filmInfo;
            this.labelNames = labelNames;
            this.releaseTime = releaseTime;
            this.directorInfo = directorInfo;
            this.performerInfo = performerInfo;
            this.similarFilmList = similarFilmList;
            this.subscribeStatus = subscribeStatus;
            this.playProgressVO = playProgressVO;
            this.chapterList = chapterList;
        }

        // ========== Getter和Setter方法 ==========

        public FilmInfo getFilmInfo() {
            return filmInfo;
        }

        public void setFilmInfo(FilmInfo filmInfo) {
            this.filmInfo = filmInfo;
        }

        public String getLabelNames() {
            return labelNames;
        }

        public void setLabelNames(String labelNames) {
            this.labelNames = labelNames;
        }

        public String getReleaseTime() {
            return releaseTime;
        }

        public void setReleaseTime(String releaseTime) {
            this.releaseTime = releaseTime;
        }

        public List<DirectorInfo> getDirectorInfo() {
            return directorInfo;
        }

        public void setDirectorInfo(List<DirectorInfo> directorInfo) {
            this.directorInfo = directorInfo;
        }

        public List<PerformerInfo> getPerformerInfo() {
            return performerInfo;
        }

        public void setPerformerInfo(List<PerformerInfo> performerInfo) {
            this.performerInfo = performerInfo;
        }

        public List<FilmInfo> getSimilarFilmList() {
            return similarFilmList;
        }

        public void setSimilarFilmList(List<FilmInfo> similarFilmList) {
            this.similarFilmList = similarFilmList;
        }

        public Integer getSubscribeStatus() {
            return subscribeStatus;
        }

        public void setSubscribeStatus(Integer subscribeStatus) {
            this.subscribeStatus = subscribeStatus;
        }

        public PlayProgressVO getPlayProgressVO() {
            return playProgressVO;
        }

        public void setPlayProgressVO(PlayProgressVO playProgressVO) {
            this.playProgressVO = playProgressVO;
        }

        public List<ChapterInfo> getChapterList() {
            return chapterList;
        }

        public void setChapterList(List<ChapterInfo> chapterList) {
            this.chapterList = chapterList;
        }

        // ========== 兼容性方法 ==========

        /**
         * 兼容性方法：获取短剧信息（API字段名）
         */
        public FilmInfo getFilmVo() {
            return filmInfo;
        }

        /**
         * 兼容性方法：设置短剧信息（API字段名）
         */
        public void setFilmVo(FilmInfo filmVo) {
            this.filmInfo = filmVo;
        }

        /**
         * 兼容性方法：获取播放进度（API字段名）
         */
        public PlayProgressVO getPlayProgress() {
            return playProgressVO;
        }

        /**
         * 兼容性方法：设置播放进度（API字段名）
         */
        public void setPlayProgress(PlayProgressVO playProgress) {
            this.playProgressVO = playProgress;
        }

        // ========== 便利方法 ==========

        /**
         * 检查是否有章节数据
         *
         * @return 如果有章节数据返回true，否则返回false
         */
        public boolean hasChapters() {
            return chapterList != null && !chapterList.isEmpty();
        }

        /**
         * 获取章节总数
         *
         * @return 章节总数
         */
        public int getChapterCount() {
            return chapterList != null ? chapterList.size() : 0;
        }

        /**
         * 根据章节ID查找章节信息
         *
         * @param chapterId 章节ID
         * @return 找到的章节信息，如果未找到返回null
         */
        public ChapterInfo findChapterById(String chapterId) {
            if (chapterList == null || chapterId == null) {
                return null;
            }
            
            for (ChapterInfo chapter : chapterList) {
                if (chapterId.equals(chapter.getChapterId())) {
                    return chapter;
                }
            }
            return null;
        }

        /**
         * 根据集数查找章节信息
         *
         * @param episodeNumber 集数
         * @return 找到的章节信息，如果未找到返回null
         */
        public ChapterInfo findChapterByEpisode(int episodeNumber) {
            if (chapterList == null) {
                return null;
            }
            
            for (ChapterInfo chapter : chapterList) {
                if (chapter.getChapterEp() != null && chapter.getChapterEp() == episodeNumber) {
                    return chapter;
                }
            }
            return null;
        }

        /**
         * 检查是否已订阅
         *
         * @return 如果已订阅返回true，否则返回false
         */
        public boolean isSubscribed() {
            return subscribeStatus != null && subscribeStatus == 1;
        }

        /**
         * 检查是否有导演信息
         *
         * @return 如果有导演信息返回true，否则返回false
         */
        public boolean hasDirectors() {
            return directorInfo != null && !directorInfo.isEmpty();
        }

        /**
         * 检查是否有演员信息
         *
         * @return 如果有演员信息返回true，否则返回false
         */
        public boolean hasPerformers() {
            return performerInfo != null && !performerInfo.isEmpty();
        }

        /**
         * 检查是否有相似影片
         *
         * @return 如果有相似影片返回true，否则返回false
         */
        public boolean hasSimilarFilms() {
            return similarFilmList != null && !similarFilmList.isEmpty();
        }

        /**
         * 检查是否有标签
         *
         * @return 如果有标签返回true，否则返回false
         */
        public boolean hasLabels() {
            return labelNames != null && !labelNames.trim().isEmpty();
        }

        @Override
        public String toString() {
            return "ChapterListData{" +
                    "filmInfo=" + filmInfo +
                    ", labelNames='" + labelNames + '\'' +
                    ", releaseTime='" + releaseTime + '\'' +
                    ", directorInfo=" + (directorInfo != null ? directorInfo.size() + " directors" : "null") +
                    ", performerInfo=" + (performerInfo != null ? performerInfo.size() + " performers" : "null") +
                    ", similarFilmList=" + (similarFilmList != null ? similarFilmList.size() + " similar films" : "null") +
                    ", subscribeStatus=" + subscribeStatus +
                    ", playProgressVO=" + playProgressVO +
                    ", chapterList=" + (chapterList != null ? chapterList.size() + " chapters" : "null") +
                    '}';
        }
    }
}
