<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:context=".ui.activity.PhoneLoginActivity">

    <!-- Layout Guidelines for responsive design -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_vertical_center"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_start"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.1" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_content_end"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.9" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_logo_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.35" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_form_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.75" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_bottom_safe"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_percent="0.95" />

    <!-- Background Image -->
    <ImageView
        android:id="@+id/iv_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleType="centerCrop"
        android:src="@drawable/login_bg"
        android:contentDescription="Login Background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Back Button -->
    <ImageView
        android:id="@+id/iv_back_button"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:src="@drawable/login_btn_back"
        android:contentDescription="@string/back_button"
        android:clickable="true"
        android:focusable="true"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Logo with responsive positioning -->
    <ImageView
        android:id="@+id/iv_logo"
        android:layout_width="@dimen/login_logo_width"
        android:layout_height="@dimen/login_logo_height"
        android:contentDescription="@string/app_logo"
        android:scaleType="centerInside"
        android:src="@drawable/logo"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintVertical_bias="0.6" />

    <!-- Title -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Log In"
        android:textColor="@color/login_text_white"
        android:textSize="@dimen/login_title_text_size"
        android:textStyle="bold"
        android:fontFamily="sans-serif-medium"
        android:letterSpacing="0.02"
        app:layout_constraintTop_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Phone Input Container with responsive constraints -->
    <com.google.android.material.card.MaterialCardView
        android:id="@+id/cv_phone_input_card"
        android:layout_width="0dp"
        android:layout_height="@dimen/phone_input_height"
        android:layout_marginTop="@dimen/phone_input_margin_top"
        app:cardBackgroundColor="@android:color/transparent"
        app:cardElevation="0dp"
        app:cardCornerRadius="@dimen/layout_margin_medium"
        app:strokeWidth="0dp"
        app:layout_constraintTop_toTopOf="@+id/guideline_logo_bottom"
        app:layout_constraintBottom_toTopOf="@+id/btn_send_code"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="0.2"
        app:layout_constraintVertical_chainStyle="packed">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_phone_input_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/phone_input_selector"
            android:clickable="true"
            android:focusable="true">

        <!-- Country Flag -->
        <ImageView
            android:id="@+id/iv_country_flag"
            android:layout_width="@dimen/chain_spacing_large"
            android:layout_height="@dimen/layout_margin_medium"
            android:layout_marginStart="@dimen/layout_margin_medium"
            android:src="@drawable/american_flag"
            android:contentDescription="Country Flag"
            android:scaleType="centerCrop"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Country Code -->
        <TextView
            android:id="@+id/tv_country_code"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/chain_spacing_small"
            android:text="+1"
            android:textColor="@color/login_text_white"
            android:textSize="@dimen/login_input_text_size"
            app:layout_constraintStart_toEndOf="@+id/iv_country_flag"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Dropdown Arrow -->
        <ImageView
            android:id="@+id/iv_dropdown_arrow"
            android:layout_width="@dimen/flow_vertical_gap"
            android:layout_height="@dimen/flow_vertical_gap"
            android:layout_marginStart="@dimen/chain_spacing_tiny"
            android:src="@drawable/login_ic_select"
            android:contentDescription="Dropdown Arrow"
            app:layout_constraintStart_toEndOf="@+id/tv_country_code"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <!-- Phone Number Input -->
        <EditText
            android:id="@+id/et_phone_number"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_marginStart="@dimen/flow_horizontal_gap"
            android:layout_marginEnd="@dimen/layout_margin_medium"
            android:background="@android:color/transparent"
            android:hint="Phone number"
            android:textColorHint="@color/phone_input_placeholder"
            android:textColor="@color/login_text_white"
            android:textSize="@dimen/login_input_text_size"
            android:inputType="phone"
            android:maxLines="1"
            android:imeOptions="actionNext"
            app:layout_constraintStart_toEndOf="@+id/iv_dropdown_arrow"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

    </com.google.android.material.card.MaterialCardView>

    <!-- Send Code Button - Chain member -->
    <Button
        android:id="@+id/btn_send_code"
        android:layout_width="0dp"
        android:layout_height="@dimen/send_code_button_height"
        android:layout_marginTop="@dimen/send_code_button_margin_top"
        android:background="@drawable/send_code_button_bg"
        android:text="Send Code"
        android:textColor="@color/login_text_white"
        android:textSize="@dimen/login_button_text_size"
        android:textStyle="bold"
        android:elevation="@dimen/layout_margin_tiny"
        app:layout_constraintTop_toBottomOf="@+id/cv_phone_input_card"
        app:layout_constraintBottom_toTopOf="@+id/guideline_form_bottom"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="0.3" />



    <!-- User Agreement Text with proper bottom positioning -->
    <TextView
        android:id="@+id/tv_user_agreement"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/login_agreement_text_margin_horizontal"
        android:layout_marginEnd="@dimen/login_agreement_text_margin_horizontal"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:letterSpacing="0"
        android:lineSpacingMultiplier="1.43"
        android:text="@string/user_agreement"
        android:textColor="@color/login_agreement_text"
        android:textSize="@dimen/login_agreement_text_size"
        android:textStyle="normal"
        android:paddingTop="@dimen/chain_spacing_small"
        android:paddingBottom="@dimen/chain_spacing_small"
        app:layout_constraintTop_toTopOf="@+id/guideline_form_bottom"
        app:layout_constraintBottom_toTopOf="@+id/guideline_bottom_safe"
        app:layout_constraintStart_toStartOf="@+id/guideline_content_start"
        app:layout_constraintEnd_toEndOf="@+id/guideline_content_end"
        app:layout_constraintVertical_bias="1.0" />


</androidx.constraintlayout.widget.ConstraintLayout>
