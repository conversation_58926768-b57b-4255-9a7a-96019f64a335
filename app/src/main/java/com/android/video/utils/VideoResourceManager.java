package com.android.video.utils;

import android.content.Context;
import android.content.res.Resources;
import android.net.Uri;
import android.util.Log;
import com.android.video.R;
import com.android.video.model.TestVideoModel;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 视频资源管理工具类
 * 负责管理本地测试视频资源的加载、验证和访问
 * <AUTHOR> Team
 */
public class VideoResourceManager {

    private static final String TAG = "VideoResourceManager";
    private static VideoResourceManager instance;
    private Context context;
    private List<TestVideoModel> testVideos;
    private boolean isInitialized = false;

    /**
     * 私有构造函数
     */
    private VideoResourceManager() {
        this.testVideos = new ArrayList<>();
    }

    /**
     * 获取单例实例
     */
    public static synchronized VideoResourceManager getInstance() {
        if (instance == null) {
            instance = new VideoResourceManager();
        }
        return instance;
    }

    /**
     * 初始化视频资源管理器
     * @param context 应用上下文
     */
    public void initialize(Context context) {
        this.context = context.getApplicationContext();
        // 先创建一个基本的视频列表，避免阻塞UI
        createBasicVideoList();
        isInitialized = true;
        Log.d(TAG, "VideoResourceManager initialized with " + testVideos.size() + " basic videos");

        // 异步加载完整的视频列表
        loadTestVideosAsync();
    }

    /**
     * 创建基本的视频列表（快速初始化）
     */
    private void createBasicVideoList() {
        testVideos.clear();

        // 只添加一个基本的测试视频，确保快速启动
        TestVideoModel basicVideo = new TestVideoModel(
            "basic_test_video",
            "基础测试视频",
            "这是一个基础的测试视频，用于快速启动应用",
            "movie.mp4", // 文件名
            R.raw.movie, // 使用默认的movie资源
            "1920x1080",
            "mp4"
        );
        basicVideo.setCategory("测试");
        basicVideo.setTags(Arrays.asList("测试", "基础"));
        basicVideo.setRating(4.0f);
        basicVideo.setViewCount(1000);
        basicVideo.setDuration(90000); // 1.5分钟
        basicVideo.setFileSize(50 * 1024 * 1024); // 50MB

        testVideos.add(basicVideo);
    }

    /**
     * 异步加载完整的测试视频列表
     */
    private void loadTestVideosAsync() {
        new Thread(() -> {
            try {
                Log.d(TAG, "Loading full video list in background...");
                loadTestVideos();
                Log.d(TAG, "Full video list loaded with " + testVideos.size() + " videos");
            } catch (Exception e) {
                Log.e(TAG, "Failed to load full video list", e);
            }
        }).start();
    }

    /**
     * 检查是否已初始化
     */
    public boolean isInitialized() {
        return isInitialized;
    }

    /**
     * 加载测试视频资源
     */
    private void loadTestVideos() {
        testVideos.clear();

        // 使用TestVideoGenerator生成完整的测试视频列表
        TestVideoGenerator generator = new TestVideoGenerator(context);
        List<TestVideoModel> generatedVideos = generator.generateTestVideos();
        testVideos.addAll(generatedVideos);

        // 验证所有视频
        validateAllVideos();

        Log.d(TAG, "Loaded " + testVideos.size() + " test videos");
    }

    /**
     * 使用TestVideoGenerator验证视频
     */
    private void validateVideosWithGenerator() {
        if (context == null) {
            Log.w(TAG, "Context is null, cannot validate videos with generator");
            return;
        }

        TestVideoGenerator generator = new TestVideoGenerator(context);
        for (TestVideoModel video : testVideos) {
            generator.validateVideoFile(video);
        }
    }

    /**
     * 验证所有视频资源
     */
    private void validateAllVideos() {
        if (context == null) {
            Log.w(TAG, "Context is null, cannot validate videos");
            return;
        }

        Resources resources = context.getResources();
        for (TestVideoModel video : testVideos) {
            try {
                // 验证资源是否存在
                Uri uri = Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
                video.setVideoUri(uri);
                video.setValid(true);
                
                Log.d(TAG, "Video validated: " + video.getTitle() + " -> " + uri.toString());
            } catch (Exception e) {
                video.setValid(false);
                Log.e(TAG, "Failed to validate video: " + video.getTitle(), e);
            }
        }
    }

    /**
     * 获取所有测试视频
     * @return 测试视频列表
     */
    public List<TestVideoModel> getAllTestVideos() {
        if (!isInitialized) {
            Log.w(TAG, "VideoResourceManager not initialized");
            return new ArrayList<>();
        }
        return new ArrayList<>(testVideos);
    }

    /**
     * 获取有效的测试视频
     * @return 有效的测试视频列表
     */
    public List<TestVideoModel> getValidTestVideos() {
        List<TestVideoModel> validVideos = new ArrayList<>();
        for (TestVideoModel video : testVideos) {
            if (video.isValid()) {
                validVideos.add(video);
            }
        }
        return validVideos;
    }

    /**
     * 根据ID获取测试视频
     * @param videoId 视频ID
     * @return 测试视频对象，如果未找到返回null
     */
    public TestVideoModel getTestVideoById(String videoId) {
        if (videoId == null || videoId.isEmpty()) {
            return null;
        }

        for (TestVideoModel video : testVideos) {
            if (videoId.equals(video.getId())) {
                return video;
            }
        }
        return null;
    }

    /**
     * 根据分类获取测试视频
     * @param category 视频分类
     * @return 指定分类的测试视频列表
     */
    public List<TestVideoModel> getTestVideosByCategory(String category) {
        List<TestVideoModel> categoryVideos = new ArrayList<>();
        if (category == null || category.isEmpty()) {
            return categoryVideos;
        }

        for (TestVideoModel video : testVideos) {
            if (category.equals(video.getCategory()) && video.isValid()) {
                categoryVideos.add(video);
            }
        }
        return categoryVideos;
    }

    /**
     * 生成视频URI
     * @param video 测试视频对象
     * @return 视频URI
     */
    public Uri generateVideoUri(TestVideoModel video) {
        if (context == null || video == null || video.getResourceId() <= 0) {
            return null;
        }

        try {
            return Uri.parse("android.resource://" + context.getPackageName() + "/" + video.getResourceId());
        } catch (Exception e) {
            Log.e(TAG, "Failed to generate URI for video: " + video.getTitle(), e);
            return null;
        }
    }

    /**
     * 验证视频文件格式
     * @param fileName 文件名
     * @return 是否为支持的格式
     */
    public boolean isValidVideoFormat(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return false;
        }

        String lowerFileName = fileName.toLowerCase();
        return lowerFileName.endsWith(".mp4") || 
               lowerFileName.endsWith(".avi") || 
               lowerFileName.endsWith(".mkv") || 
               lowerFileName.endsWith(".mov") || 
               lowerFileName.endsWith(".wmv") || 
               lowerFileName.endsWith(".flv") || 
               lowerFileName.endsWith(".webm");
    }

    /**
     * 获取支持的视频格式列表
     * @return 支持的格式列表
     */
    public List<String> getSupportedFormats() {
        return Arrays.asList("mp4", "avi", "mkv", "mov", "wmv", "flv", "webm");
    }

    /**
     * 添加新的测试视频
     * @param video 测试视频对象
     * @return 是否添加成功
     */
    public boolean addTestVideo(TestVideoModel video) {
        if (video == null || !video.validateVideo()) {
            Log.w(TAG, "Invalid video, cannot add");
            return false;
        }

        // 检查是否已存在相同ID的视频
        if (getTestVideoById(video.getId()) != null) {
            Log.w(TAG, "Video with ID " + video.getId() + " already exists");
            return false;
        }

        testVideos.add(video);
        Log.d(TAG, "Test video added: " + video.getTitle());
        return true;
    }

    /**
     * 移除测试视频
     * @param videoId 视频ID
     * @return 是否移除成功
     */
    public boolean removeTestVideo(String videoId) {
        TestVideoModel video = getTestVideoById(videoId);
        if (video != null) {
            testVideos.remove(video);
            Log.d(TAG, "Test video removed: " + video.getTitle());
            return true;
        }
        return false;
    }

    /**
     * 获取视频总数
     * @return 视频总数
     */
    public int getVideoCount() {
        return testVideos.size();
    }

    /**
     * 获取有效视频总数
     * @return 有效视频总数
     */
    public int getValidVideoCount() {
        int count = 0;
        for (TestVideoModel video : testVideos) {
            if (video.isValid()) {
                count++;
            }
        }
        return count;
    }

    /**
     * 清空所有测试视频
     */
    public void clearAllVideos() {
        testVideos.clear();
        Log.d(TAG, "All test videos cleared");
    }

    /**
     * 重新加载视频资源
     */
    public void reloadVideos() {
        if (context != null) {
            loadTestVideos();
            Log.d(TAG, "Videos reloaded");
        }
    }

    /**
     * 获取错误处理信息
     * @return 错误信息列表
     */
    public List<String> getErrorMessages() {
        List<String> errors = new ArrayList<>();

        if (!isInitialized) {
            errors.add("VideoResourceManager not initialized");
        }

        if (context == null) {
            errors.add("Context is null");
        }

        for (TestVideoModel video : testVideos) {
            if (!video.isValid()) {
                errors.add("Invalid video: " + video.getTitle());
            }
        }

        return errors;
    }

    /**
     * 清理缩略图缓存
     */
    public void clearThumbnailCache() {
        if (context != null) {
            TestVideoGenerator generator = new TestVideoGenerator(context);
            generator.clearThumbnailCache();
            Log.d(TAG, "Thumbnail cache cleared");
        }
    }

    /**
     * 重新生成所有视频的缩略图
     */
    public void regenerateThumbnails() {
        if (context != null) {
            TestVideoGenerator generator = new TestVideoGenerator(context);
            for (TestVideoModel video : testVideos) {
                if (video.isValid()) {
                    generator.validateVideoFile(video);
                }
            }
            Log.d(TAG, "Thumbnails regenerated for all videos");
        }
    }

    /**
     * 获取视频统计信息
     * @return 统计信息字符串
     */
    public String getStatistics() {
        int totalVideos = getVideoCount();
        int validVideos = getValidVideoCount();
        int invalidVideos = totalVideos - validVideos;

        return String.format("总视频数: %d, 有效: %d, 无效: %d",
                           totalVideos, validVideos, invalidVideos);
    }

    /**
     * 检查资源完整性
     * @return 是否完整
     */
    public boolean checkResourceIntegrity() {
        boolean allValid = true;
        for (TestVideoModel video : testVideos) {
            if (!video.isValid()) {
                allValid = false;
                Log.w(TAG, "Invalid video found: " + video.getTitle());
            }
        }
        return allValid;
    }
}
