package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.model.DailyRankModel;
import com.android.video.model.VideoModel;
import com.android.video.constants.HomeApiConstantsUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 今日热门排行榜分页数据模型
 * <p>
 * 用于映射今日热门排行榜API返回的分页数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_DAILY_RANK}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>records: 今日热门排行榜记录列表</li>
 *   <li>total: 总记录数</li>
 *   <li>size: 每页数量</li>
 *   <li>current: 当前页码</li>
 *   <li>pages: 总页数</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * DailyRankDataModel dataModel = gson.fromJson(jsonObject, DailyRankDataModel.class);
 *
 * // 获取排行榜数据
 * List&lt;DailyRankModel&gt; rankList = dataModel.getRecords();
 * boolean hasMore = dataModel.hasMorePages();
 * 
 * // 转换为VideoModel列表
 * List&lt;VideoModel&gt; videoList = dataModel.toVideoModelList();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_DAILY_RANK
 * @see DailyRankModel
 */
public class DailyRankDataModel {
    
    /**
     * 今日热门排行榜记录列表
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_RECORDS)
    private List<DailyRankModel> records;
    
    /**
     * 总记录数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_TOTAL)
    private int total;
    
    /**
     * 每页数量
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_SIZE)
    private int size;
    
    /**
     * 当前页码
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CURRENT)
    private int current;
    
    /**
     * 排序信息
     */
    @SerializedName("orders")
    private List<Object> orders;

    /**
     * 是否优化count SQL
     */
    @SerializedName("optimizeCountSql")
    private boolean optimizeCountSql;

    /**
     * 是否命中count
     */
    @SerializedName("hitCount")
    private boolean hitCount;

    /**
     * count ID
     */
    @SerializedName("countId")
    private String countId;

    /**
     * 最大限制
     */
    @SerializedName("maxLimit")
    private String maxLimit;

    /**
     * 是否搜索count
     */
    @SerializedName("searchCount")
    private boolean searchCount;
    
    /**
     * 总页数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_PAGES)
    private int pages;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public DailyRankDataModel() {
        this.records = new ArrayList<>();
        this.orders = new ArrayList<>();
    }

    /**
     * 简化构造函数
     */
    public DailyRankDataModel(List<DailyRankModel> records, int total, int size, int current, int pages) {
        this.records = records != null ? records : new ArrayList<>();
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = pages;
        this.orders = new ArrayList<>();
        this.optimizeCountSql = true;
        this.hitCount = false;
        this.searchCount = true;
    }

    // ========== Getter和Setter方法 ==========

    public List<DailyRankModel> getRecords() {
        return records;
    }

    public void setRecords(List<DailyRankModel> records) {
        this.records = records != null ? records : new ArrayList<>();
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public List<Object> getOrders() {
        return orders;
    }

    public void setOrders(List<Object> orders) {
        this.orders = orders != null ? orders : new ArrayList<>();
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql;
    }

    public void setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
    }

    public boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(boolean hitCount) {
        this.hitCount = hitCount;
    }

    public String getCountId() {
        return countId;
    }

    public void setCountId(String countId) {
        this.countId = countId;
    }

    public String getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(String maxLimit) {
        this.maxLimit = maxLimit;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否有更多页面
     * @return 如果有更多页面则返回true
     */
    public boolean hasMorePages() {
        return current < pages;
    }

    /**
     * 检查是否为空数据
     * @return 如果没有记录则返回true
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取记录数量
     * @return 当前页面的记录数量
     */
    public int getRecordCount() {
        return records != null ? records.size() : 0;
    }

    /**
     * 获取下一页页码
     * @return 下一页页码，如果没有更多页面则返回-1
     */
    public int getNextPage() {
        return hasMorePages() ? current + 1 : -1;
    }

    /**
     * 转换为VideoModel列表
     * @return VideoModel列表，包含排名信息
     */
    public List<VideoModel> toVideoModelList() {
        List<VideoModel> videoModels = new ArrayList<>();
        if (records != null) {
            for (int i = 0; i < records.size(); i++) {
                DailyRankModel rankModel = records.get(i);
                // 计算全局排名：(当前页码-1) * 每页数量 + 当前位置 + 1
                int globalRanking = (current - 1) * size + i + 1;
                VideoModel videoModel = rankModel.toVideoModel(globalRanking);
                videoModels.add(videoModel);
            }
        }
        return videoModels;
    }

    /**
     * 添加记录到列表
     * @param rankModel 要添加的排行榜记录
     */
    public void addRecord(DailyRankModel rankModel) {
        if (records == null) {
            records = new ArrayList<>();
        }
        records.add(rankModel);
    }

    /**
     * 合并其他页面的数据
     * @param otherData 其他页面的数据
     */
    public void mergeData(DailyRankDataModel otherData) {
        if (otherData != null && otherData.getRecords() != null) {
            if (records == null) {
                records = new ArrayList<>();
            }
            records.addAll(otherData.getRecords());
        }
    }

    @Override
    public String toString() {
        return "DailyRankDataModel{" +
                "records=" + (records != null ? records.size() : 0) + " items" +
                ", total=" + total +
                ", size=" + size +
                ", current=" + current +
                ", pages=" + pages +
                ", hasMorePages=" + hasMorePages() +
                '}';
    }
}
