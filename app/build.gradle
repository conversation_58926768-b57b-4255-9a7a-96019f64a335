plugins {
    id 'com.android.application'
}

android {
    namespace 'com.android.video'
    compileSdk 34

    defaultConfig {
        applicationId "com.android.video"
        minSdk 24
        targetSdk 34
        versionCode 3
        versionName "1.0.3"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        debug {
            minifyEnabled false
            buildConfigField "String", "API_BASE_URL", "\"https://short-play-api.gymooit.cn/v1\""
            buildConfigField "String", "ENVIRONMENT", "\"dev\""
            buildConfigField "boolean", "ENABLE_API_MODE", "false"
        }
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
            buildConfigField "String", "API_BASE_URL", "\"https://short-play-api.gymooit.cn/v1\""
            buildConfigField "String", "ENVIRONMENT", "\"prod\""
            buildConfigField "boolean", "ENABLE_API_MODE", "true"
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_11
        targetCompatibility JavaVersion.VERSION_11
    }
    buildFeatures {
        viewBinding true
        buildConfig true
    }

    lint {
        abortOnError false
        checkReleaseBuilds false
    }
}

dependencies {

    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'com.google.android.material:material:1.10.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'androidx.navigation:navigation-fragment:2.6.0'
    implementation 'androidx.navigation:navigation-ui:2.6.0'

    // ViewPager2 for tag pagination
    implementation 'androidx.viewpager2:viewpager2:1.0.0'

    // SwipeRefreshLayout for pull-to-refresh functionality
    implementation 'androidx.swiperefreshlayout:swiperefreshlayout:1.1.0'

    // Google Flexbox for adaptive tag layout
    implementation 'com.google.android.flexbox:flexbox:3.0.0'

    // Google Play Services for Google Pay
    implementation 'com.google.android.gms:play-services-wallet:19.2.1'

    // ExoPlayer for video playback
    implementation 'com.google.android.exoplayer:exoplayer:2.19.1'

    // DanmakuFlameMaster for danmaku functionality
    implementation 'com.github.bilibili:DanmakuFlameMaster:0.9.25'

    // Network libraries
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
    implementation 'com.squareup.okhttp3:logging-interceptor:4.12.0'
    implementation 'com.google.code.gson:gson:2.10.1'

    // Image loading library
    implementation('com.github.bumptech.glide:glide:4.16.0') {
        exclude group: 'androidx.annotation', module: 'annotation-jvm'
    }
    implementation 'jp.wasabeef:glide-transformations:4.3.0'

    // Third-party login SDKs
    implementation 'com.facebook.android:facebook-login:18.0.3'
    // TODO: TikTok SDK repository appears to be unavailable, implement alternative solution
    // implementation 'com.tiktok.open.sdk:tiktok-open-sdk-core:2.3.0'

    testImplementation 'junit:junit:4.13.2'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
}
