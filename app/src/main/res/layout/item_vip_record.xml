<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/vip_record_card_height"
    android:layout_gravity="center_horizontal"
    android:layout_marginBottom="@dimen/vip_record_card_margin_bottom"
    android:background="@drawable/vip_record_card_bg"
    android:orientation="vertical"
    android:padding="@dimen/vip_record_card_padding">

    <!-- 上半部分：购买类型和价格 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 购买类型 -->
        <TextView
            android:id="@+id/tv_purchase_type"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="Purchase weekly VIP card"
            android:textColor="#ffffffff"
            android:textSize="@dimen/vip_record_title_text_size"
            android:fontFamily="sans-serif" />

        <!-- 价格 -->
        <TextView
            android:id="@+id/tv_price"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:text="-$39"
            android:textColor="#F12626"
            android:textSize="@dimen/vip_record_price_text_size"
            android:fontFamily="sans-serif" />

    </RelativeLayout>

    <!-- 下半部分：日期时间和支付方式 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">

        <!-- 日期时间 -->
        <TextView
            android:id="@+id/tv_date_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentStart="true"
            android:layout_centerVertical="true"
            android:text="06/18/2025 10:03AM"
            android:textColor="#80ffffff"
            android:textSize="@dimen/vip_record_date_text_size"
            android:fontFamily="sans-serif" />

        <!-- 支付方式 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:orientation="horizontal"
            android:gravity="center_vertical">

            <!-- 支付方式图标 -->
            <ImageView
                android:id="@+id/iv_payment_icon"
                android:layout_width="@dimen/vip_record_payment_icon_size"
                android:layout_height="@dimen/vip_record_payment_icon_size"
                android:layout_marginEnd="@dimen/vip_record_payment_icon_margin_end"
                android:src="@drawable/bill_ic_paymethon"
                android:scaleType="centerCrop" />

            <!-- 支付方式文字 -->
            <TextView
                android:id="@+id/tv_payment_method"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Apple pay"
                android:textColor="#80ffffff"
                android:textSize="@dimen/vip_record_payment_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </RelativeLayout>

</LinearLayout>
