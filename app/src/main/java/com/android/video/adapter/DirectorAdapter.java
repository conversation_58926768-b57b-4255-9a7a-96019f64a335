package com.android.video.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.DirectorModel;
import java.util.ArrayList;
import java.util.List;

/**
 * 导演列表RecyclerView适配器
 * 用于显示视频详情页面的导演信息，包括头像和姓名
 * <AUTHOR>
 */
public class DirectorAdapter extends RecyclerView.Adapter<DirectorAdapter.DirectorViewHolder> {

    private Context context;
    private List<DirectorModel> directorList;
    private OnDirectorClickListener onDirectorClickListener;

    /**
     * 导演点击监听接口
     */
    public interface OnDirectorClickListener {
        void onDirectorClick(DirectorModel director, int position);
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public DirectorAdapter(Context context) {
        this.context = context;
        this.directorList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param context 上下文
     * @param directorList 导演列表
     */
    public DirectorAdapter(Context context, List<DirectorModel> directorList) {
        this.context = context;
        this.directorList = directorList != null ? directorList : new ArrayList<>();
    }

    /**
     * 设置导演点击监听器
     * @param listener 点击监听器
     */
    public void setOnDirectorClickListener(OnDirectorClickListener listener) {
        this.onDirectorClickListener = listener;
    }

    /**
     * 更新导演列表
     * @param newDirectorList 新的导演列表
     */
    public void updateDirectors(List<DirectorModel> newDirectorList) {
        this.directorList = newDirectorList != null ? newDirectorList : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 添加导演到列表
     * @param director 导演数据
     */
    public void addDirector(DirectorModel director) {
        if (director != null && !director.isEmpty()) {
            directorList.add(director);
            notifyItemInserted(directorList.size() - 1);
        }
    }

    /**
     * 移除指定位置的导演
     * @param position 位置
     */
    public void removeDirector(int position) {
        if (position >= 0 && position < directorList.size()) {
            directorList.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * 获取导演列表
     * @return 导演列表
     */
    public List<DirectorModel> getDirectorList() {
        return new ArrayList<>(directorList);
    }

    @NonNull
    @Override
    public DirectorViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(context)
                .inflate(R.layout.item_director, parent, false);
        return new DirectorViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull DirectorViewHolder holder, int position) {
        DirectorModel director = directorList.get(position);
        holder.bind(director, position);
    }

    @Override
    public int getItemCount() {
        return directorList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class DirectorViewHolder extends RecyclerView.ViewHolder {

        private ImageView ivDirectorAvatar;
        private TextView tvDirectorName;

        public DirectorViewHolder(@NonNull View itemView) {
            super(itemView);
            ivDirectorAvatar = itemView.findViewById(R.id.iv_director_avatar);
            tvDirectorName = itemView.findViewById(R.id.tv_director_name);
        }

        /**
         * 绑定数据
         * @param director 导演数据
         * @param position 位置
         */
        public void bind(DirectorModel director, int position) {
            if (director == null) {
                return;
            }

            // 设置导演头像 (使用movie_poster作为占位图)
            if (director.hasAvatar()) {
                // 使用Glide加载网络图片
                Glide.with(itemView.getContext())
                        .load(director.getAvatarUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivDirectorAvatar);
            } else {
                ivDirectorAvatar.setImageResource(R.drawable.movie_poster);
            }

            // 设置导演姓名
            tvDirectorName.setText(director.getDisplayName());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onDirectorClickListener != null) {
                    onDirectorClickListener.onDirectorClick(director, position);
                }
            });

            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                // 可以显示导演详细信息或其他操作
                return true;
            });
        }
    }
}
