<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/feedback_type_margin_end"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="@dimen/feedback_image_size"
        android:layout_height="@dimen/feedback_image_size"
        android:background="@drawable/feedback_image_bg">

        <!-- 已上传的图片 -->
        <ImageView
            android:id="@+id/iv_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:background="@android:color/transparent"
            android:visibility="gone" />

        <!-- 添加图片图标 -->
        <ImageView
            android:id="@+id/iv_add_icon"
            android:layout_width="@dimen/feedback_image_icon_size"
            android:layout_height="@dimen/feedback_image_icon_size"
            android:layout_centerInParent="true"
            android:src="@drawable/ic_pic"
            android:visibility="visible" />

        <!-- 图片计数 -->
        <TextView
            android:id="@+id/tv_image_count"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentBottom="true"
            android:layout_centerHorizontal="true"
            android:layout_marginBottom="12dp"
            android:text="0/3"
            android:textColor="@color/feedback_char_count_color"
            android:textSize="14sp"
            android:visibility="visible" />

    </RelativeLayout>

</LinearLayout>
