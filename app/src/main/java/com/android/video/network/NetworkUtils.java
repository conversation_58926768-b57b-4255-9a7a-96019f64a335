package com.android.video.network;

import android.content.Context;
import android.net.ConnectivityManager;
import android.net.Network;
import android.net.NetworkCapabilities;
import android.net.NetworkInfo;
import android.os.Build;
import android.util.Log;

/**
 * 网络状态检测工具类 - 提供网络连接状态和类型检测功能
 * <AUTHOR> Team
 */
public class NetworkUtils {

    private static final String TAG = "NetworkUtils";

    /**
     * 网络类型枚举
     */
    public enum NetworkType {
        NONE,       // 无网络连接
        WIFI,       // WiFi连接
        MOBILE,     // 移动数据连接
        ETHERNET,   // 以太网连接
        OTHER       // 其他类型连接
    }

    /**
     * 检查网络是否可用
     * @param context 上下文
     * @return 网络是否可用
     */
    public static boolean isNetworkAvailable(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot check network availability");
            return false;
        }

        ConnectivityManager connectivityManager = 
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            Log.e(TAG, "ConnectivityManager is null");
            return false;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0 (API 23) 及以上版本
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork == null) {
                return false;
            }
            
            NetworkCapabilities networkCapabilities = 
                    connectivityManager.getNetworkCapabilities(activeNetwork);
            
            return networkCapabilities != null && 
                   (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI) ||
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR) ||
                    networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET));
        } else {
            // Android 6.0 以下版本
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            return activeNetworkInfo != null && activeNetworkInfo.isConnected();
        }
    }

    /**
     * 获取当前网络类型
     * @param context 上下文
     * @return 网络类型
     */
    public static NetworkType getNetworkType(Context context) {
        if (context == null) {
            Log.e(TAG, "Context is null, cannot get network type");
            return NetworkType.NONE;
        }

        ConnectivityManager connectivityManager = 
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager == null) {
            Log.e(TAG, "ConnectivityManager is null");
            return NetworkType.NONE;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            // Android 6.0 (API 23) 及以上版本
            Network activeNetwork = connectivityManager.getActiveNetwork();
            if (activeNetwork == null) {
                return NetworkType.NONE;
            }
            
            NetworkCapabilities networkCapabilities = 
                    connectivityManager.getNetworkCapabilities(activeNetwork);
            
            if (networkCapabilities == null) {
                return NetworkType.NONE;
            }
            
            if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_WIFI)) {
                return NetworkType.WIFI;
            } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_CELLULAR)) {
                return NetworkType.MOBILE;
            } else if (networkCapabilities.hasTransport(NetworkCapabilities.TRANSPORT_ETHERNET)) {
                return NetworkType.ETHERNET;
            } else {
                return NetworkType.OTHER;
            }
        } else {
            // Android 6.0 以下版本
            NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
            if (activeNetworkInfo == null || !activeNetworkInfo.isConnected()) {
                return NetworkType.NONE;
            }
            
            int type = activeNetworkInfo.getType();
            switch (type) {
                case ConnectivityManager.TYPE_WIFI:
                    return NetworkType.WIFI;
                case ConnectivityManager.TYPE_MOBILE:
                    return NetworkType.MOBILE;
                case ConnectivityManager.TYPE_ETHERNET:
                    return NetworkType.ETHERNET;
                default:
                    return NetworkType.OTHER;
            }
        }
    }

    /**
     * 检查是否为WiFi连接
     * @param context 上下文
     * @return 是否为WiFi连接
     */
    public static boolean isWifiConnected(Context context) {
        return getNetworkType(context) == NetworkType.WIFI;
    }

    /**
     * 检查是否为移动数据连接
     * @param context 上下文
     * @return 是否为移动数据连接
     */
    public static boolean isMobileConnected(Context context) {
        return getNetworkType(context) == NetworkType.MOBILE;
    }

    /**
     * 检查是否为以太网连接
     * @param context 上下文
     * @return 是否为以太网连接
     */
    public static boolean isEthernetConnected(Context context) {
        return getNetworkType(context) == NetworkType.ETHERNET;
    }

    /**
     * 获取网络连接信息（用于调试）
     * @param context 上下文
     * @return 网络连接信息字符串
     */
    public static String getNetworkInfo(Context context) {
        if (context == null) {
            return "Context is null";
        }

        StringBuilder info = new StringBuilder();
        info.append("Network Status:\n");
        info.append("Available: ").append(isNetworkAvailable(context) ? "Yes" : "No").append("\n");
        info.append("Type: ").append(getNetworkType(context).name()).append("\n");
        
        ConnectivityManager connectivityManager = 
                (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);
        
        if (connectivityManager != null) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network activeNetwork = connectivityManager.getActiveNetwork();
                if (activeNetwork != null) {
                    NetworkCapabilities capabilities = 
                            connectivityManager.getNetworkCapabilities(activeNetwork);
                    if (capabilities != null) {
                        info.append("Download Speed: ")
                            .append(capabilities.getLinkDownstreamBandwidthKbps())
                            .append(" Kbps\n");
                        info.append("Upload Speed: ")
                            .append(capabilities.getLinkUpstreamBandwidthKbps())
                            .append(" Kbps");
                    }
                }
            } else {
                NetworkInfo activeNetworkInfo = connectivityManager.getActiveNetworkInfo();
                if (activeNetworkInfo != null) {
                    info.append("Extra Info: ").append(activeNetworkInfo.getExtraInfo());
                }
            }
        }
        
        return info.toString();
    }

    /**
     * 网络类型转换为字符串
     * @param networkType 网络类型
     * @return 网络类型字符串
     */
    public static String networkTypeToString(NetworkType networkType) {
        switch (networkType) {
            case WIFI:
                return "WiFi";
            case MOBILE:
                return "Mobile Data";
            case ETHERNET:
                return "Ethernet";
            case OTHER:
                return "Other";
            case NONE:
            default:
                return "No Connection";
        }
    }

    /**
     * 检查网络连接质量
     * @param context 上下文
     * @return 网络质量描述
     */
    public static String getNetworkQuality(Context context) {
        if (!isNetworkAvailable(context)) {
            return "No Connection";
        }

        NetworkType type = getNetworkType(context);
        switch (type) {
            case WIFI:
            case ETHERNET:
                return "Good";
            case MOBILE:
                return "Fair";
            case OTHER:
                return "Unknown";
            default:
                return "Poor";
        }
    }

    /**
     * 网络质量枚举
     */
    public enum NetworkQuality {
        EXCELLENT,  // 优秀网络
        GOOD,       // 良好网络
        FAIR,       // 一般网络
        POOR,       // 弱网络
        NONE        // 无网络
    }

    /**
     * 获取详细的网络质量评估
     * @param context 上下文
     * @return 网络质量枚举
     */
    public static NetworkQuality getDetailedNetworkQuality(Context context) {
        if (!isNetworkAvailable(context)) {
            return NetworkQuality.NONE;
        }

        NetworkType type = getNetworkType(context);
        switch (type) {
            case WIFI:
            case ETHERNET:
                return NetworkQuality.EXCELLENT;
            case MOBILE:
                // 对于移动网络，可以进一步检测信号强度
                return getMobileNetworkQuality(context);
            case OTHER:
                return NetworkQuality.FAIR;
            default:
                return NetworkQuality.POOR;
        }
    }

    /**
     * 获取移动网络质量
     * @param context 上下文
     * @return 移动网络质量
     */
    private static NetworkQuality getMobileNetworkQuality(Context context) {
        try {
            ConnectivityManager connectivityManager =
                    (ConnectivityManager) context.getSystemService(Context.CONNECTIVITY_SERVICE);

            if (connectivityManager == null) {
                return NetworkQuality.POOR;
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                Network activeNetwork = connectivityManager.getActiveNetwork();
                if (activeNetwork == null) {
                    return NetworkQuality.POOR;
                }

                NetworkCapabilities networkCapabilities =
                        connectivityManager.getNetworkCapabilities(activeNetwork);

                if (networkCapabilities == null) {
                    return NetworkQuality.POOR;
                }

                // 检查网络带宽
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.Q) {
                    int downstreamBandwidth = networkCapabilities.getLinkDownstreamBandwidthKbps();
                    if (downstreamBandwidth > 10000) { // > 10Mbps
                        return NetworkQuality.GOOD;
                    } else if (downstreamBandwidth > 2000) { // > 2Mbps
                        return NetworkQuality.FAIR;
                    } else {
                        return NetworkQuality.POOR;
                    }
                }

                // 对于较老的Android版本，基于网络类型判断
                return NetworkQuality.FAIR;
            } else {
                // Android 6.0以下版本的处理
                return NetworkQuality.FAIR;
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting mobile network quality", e);
            return NetworkQuality.POOR;
        }
    }

    /**
     * 检查是否为弱网络环境
     * @param context 上下文
     * @return 是否为弱网络
     */
    public static boolean isPoorNetwork(Context context) {
        NetworkQuality quality = getDetailedNetworkQuality(context);
        return quality == NetworkQuality.POOR || quality == NetworkQuality.NONE;
    }

    /**
     * 检查是否为移动数据网络
     * @param context 上下文
     * @return 是否为移动数据网络
     */
    public static boolean isMobileDataNetwork(Context context) {
        return getNetworkType(context) == NetworkType.MOBILE;
    }

    /**
     * 获取推荐的视频预加载数量
     * @param context 上下文
     * @return 推荐的预加载视频数量
     */
    public static int getRecommendedPreloadCount(Context context) {
        if (isPoorNetwork(context)) {
            return 0; // 弱网络不预加载
        }

        NetworkType networkType = getNetworkType(context);
        switch (networkType) {
            case WIFI:
            case ETHERNET:
                return 5; // WiFi环境预加载5个视频
            case MOBILE:
                return 3; // 移动数据预加载3个视频
            default:
                return 1; // 其他情况预加载1个视频
        }
    }

    /**
     * 检查网络是否稳定
     * @param context 上下文
     * @return 网络是否稳定
     */
    public static boolean isNetworkStable(Context context) {
        // 简单的网络稳定性检查
        // 在实际应用中，可以通过ping测试或连续的网络质量检测来判断
        NetworkQuality quality = getDetailedNetworkQuality(context);
        return quality == NetworkQuality.EXCELLENT || quality == NetworkQuality.GOOD;
    }
}
