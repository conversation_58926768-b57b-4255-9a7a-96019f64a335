package com.android.video.utils;

import android.content.Context;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.manager.TokenManager;
import java.util.HashMap;
import java.util.Map;

/**
 * API请求头工具类 - 提供统一的请求头管理功能
 * <p>
 * 此工具类用于管理API请求的通用请求头，
 * 包括访问令牌、设备信息等认证和标识信息。
 * </p>
 * 
 * <p>
 * 主要功能：
 * <ul>
 *   <li>统一管理全局请求头</li>
 *   <li>提供默认访问令牌设置</li>
 *   <li>支持动态更新访问令牌</li>
 *   <li>提供请求头验证功能</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 获取默认请求头
 * Map&lt;String, String&gt; headers = ApiHeaderUtils.getDefaultHeaders();
 * 
 * // 设置自定义访问令牌
 * ApiHeaderUtils.setAccessToken("your_custom_token");
 * 
 * // 获取包含自定义令牌的请求头
 * Map&lt;String, String&gt; customHeaders = ApiHeaderUtils.getDefaultHeaders();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class ApiHeaderUtils {

    /**
     * 当前使用的访问令牌
     * <p>
     * 优先使用TokenManager中的动态token，
     * 如果TokenManager未初始化则使用临时token。
     * </p>
     */
    private static String currentAccessToken = null;

    /**
     * 应用上下文（用于获取TokenManager实例）
     */
    private static Context applicationContext;

    /**
     * 初始化ApiHeaderUtils
     * <p>
     * 设置应用上下文，用于获取TokenManager实例
     * </p>
     */
    public static void initialize(Context context) {
        applicationContext = context.getApplicationContext();
    }

    /**
     * 获取默认的API请求头
     * <p>
     * 返回包含访问令牌的默认请求头Map。
     * 所有API请求都应该使用这些请求头。
     * 优先使用TokenManager中的动态token。
     * </p>
     *
     * @return 包含默认请求头的Map
     *
     * @example
     * <pre>
     * Map&lt;String, String&gt; headers = ApiHeaderUtils.getDefaultHeaders();
     * // 结果: {"X-Access-Token": "dynamic_token_from_cache"}
     * </pre>
     */
    public static Map<String, String> getDefaultHeaders() {
        Map<String, String> headers = new HashMap<>();
        String token = getCurrentAccessToken();
        if (token != null && !token.trim().isEmpty()) {
            headers.put(BaseApiConstantsUtils.HEADER_ACCESS_TOKEN, token);
            android.util.Log.d("ApiHeaderUtils", "Added token to headers: " + maskToken(token));
        } else {
            android.util.Log.w("ApiHeaderUtils", "No valid token available, headers will not include X-Access-Token");
        }
        return headers;
    }

    /**
     * 脱敏处理token用于日志输出
     */
    private static String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }

    /**
     * 获取基础的API请求头（不包含认证信息）
     * <p>
     * 返回不包含访问令牌的基础请求头Map。
     * 用于不需要认证的API请求，如获取协议信息等公开接口。
     * </p>
     *
     * @return 包含基础请求头的Map
     *
     * @example
     * <pre>
     * Map&lt;String, String&gt; headers = ApiHeaderUtils.getBasicHeaders();
     * // 结果: {"Content-Type": "application/json"}
     * </pre>
     */
    public static Map<String, String> getBasicHeaders() {
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        headers.put("Accept", "application/json");
        return headers;
    }

    /**
     * 设置访问令牌
     * <p>
     * 更新当前使用的访问令牌。
     * 通常在用户登录成功后调用此方法设置真实的访问令牌。
     * </p>
     * 
     * @param token 新的访问令牌
     * 
     * @example
     * <pre>
     * // 登录成功后设置真实令牌
     * ApiHeaderUtils.setAccessToken("real_token_from_login_api");
     * </pre>
     */
    public static void setAccessToken(String token) {
        if (BaseApiConstantsUtils.isValidAccessToken(token)) {
            currentAccessToken = token;
        }
    }

    /**
     * 获取当前访问令牌
     * <p>
     * 优先返回内存中设置的token，
     * 如果没有则从TokenManager获取动态token。
     * </p>
     *
     * @return 当前访问令牌字符串
     */
    public static String getCurrentAccessToken() {
        // 优先返回内存中的token（避免循环调用）
        if (currentAccessToken != null && !currentAccessToken.trim().isEmpty()) {
            return currentAccessToken;
        }

        // 如果内存中没有token，从TokenManager获取
        if (applicationContext != null) {
            try {
                TokenManager tokenManager = TokenManager.getInstance(applicationContext);
                String dynamicToken = tokenManager.getCurrentToken();
                if (dynamicToken != null && !dynamicToken.trim().isEmpty()) {
                    // 将获取的token设置到内存中，避免重复获取
                    currentAccessToken = dynamicToken;
                    android.util.Log.d("ApiHeaderUtils", "Successfully got token from TokenManager: " + maskToken(dynamicToken));
                    return dynamicToken;
                }
            } catch (Exception e) {
                // TokenManager获取失败，记录错误
                android.util.Log.w("ApiHeaderUtils", "Failed to get token from TokenManager: " + e.getMessage());
            }
        } else {
            android.util.Log.w("ApiHeaderUtils", "ApplicationContext is null, cannot access TokenManager");
        }

        // 最后的备用方案：返回默认的全局token
        android.util.Log.d("ApiHeaderUtils", "No dynamic token found, using default global token");
        return BaseApiConstantsUtils.DEFAULT_ACCESS_TOKEN;
    }

    /**
     * 重置访问令牌
     * <p>
     * 清除内存中的访问令牌。
     * 通常在用户退出登录时调用。
     * 注意：这不会清除TokenManager中的缓存token，
     * 如需完全清除请调用TokenManager.clearToken()。
     * </p>
     *
     * @example
     * <pre>
     * // 用户退出登录时重置令牌
     * ApiHeaderUtils.resetAccessToken();
     * </pre>
     */
    public static void resetAccessToken() {
        currentAccessToken = null;
    }

    /**
     * 检查当前访问令牌是否有效
     * <p>
     * 验证当前设置的访问令牌是否有效。
     * </p>
     * 
     * @return 如果当前令牌有效则返回true，否则返回false
     */
    public static boolean isCurrentTokenValid() {
        return BaseApiConstantsUtils.isValidAccessToken(currentAccessToken);
    }

    /**
     * 检查当前访问令牌是否有效
     * <p>
     * 验证当前访问令牌是否不为空且不为空字符串。
     * </p>
     *
     * @return 如果令牌有效则返回true，否则返回false
     */
    public static boolean isAccessTokenValid() {
        return currentAccessToken != null && !currentAccessToken.trim().isEmpty();
    }

    /**
     * 获取访问令牌请求头名称
     * <p>
     * 返回访问令牌在HTTP请求头中的字段名称。
     * </p>
     *
     * @return 访问令牌请求头名称
     */
    public static String getAccessTokenHeaderName() {
        return BaseApiConstantsUtils.HEADER_ACCESS_TOKEN;
    }
}
