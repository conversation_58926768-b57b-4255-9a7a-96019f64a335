package com.android.video.network;

import android.util.Log;

import com.android.video.constants.VideoDetailApiConstantsUtils;
import com.android.video.model.response.VideoDetailResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.google.gson.Gson;

import java.io.IOException;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 视频详情API服务类 - 提供视频详情相关的网络请求功能
 * <p>
 * 此类封装了视频详情模块的所有API请求，包括：
 * <ul>
 *   <li>获取视频详情信息</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * VideoDetailApiService videoDetailApiService = new VideoDetailApiService();
 *
 * // 获取视频详情
 * videoDetailApiService.getVideoDetail("c655490792384ed887bfe20e56f30c47",
 *     new ApiCallback&lt;VideoDetailResponseModel&gt;() {
 *     {@literal @}Override
 *     public void onSuccess(VideoDetailResponseModel response) {
 *         // 处理成功响应
 *     }
 *
 *     {@literal @}Override
 *     public void onError(String error) {
 *         // 处理错误
 *     }
 * });
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VideoDetailApiService {

    private static final String TAG = "VideoDetailApiService";

    private final OkHttpClient httpClient;
    private final Gson gson;

    /**
     * 构造函数
     */
    public VideoDetailApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.httpClient = httpClient;
        this.gson = new Gson();
    }

    /**
     * API回调接口
     *
     * @param <T> 响应数据类型
     */
    public interface ApiCallback<T> {
        /**
         * 请求成功回调
         *
         * @param response 响应数据
         */
        void onSuccess(T response);

        /**
         * 请求失败回调
         *
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 获取视频详情
     * <p>
     * 根据短剧语言信息ID获取视频的详细信息，包括短剧信息、演员、导演、章节列表等。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param callback 回调接口
     */
    public void getVideoDetail(String filmLanguageInfoId, ApiCallback<VideoDetailResponseModel> callback) {
        if (callback == null) {
            Log.w(TAG, "Callback is null, request cancelled");
            return;
        }

        // 参数验证
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            callback.onError("filmLanguageInfoId is not valid");
            return;
        }

        // 验证ID格式
        if (!VideoDetailApiConstantsUtils.isValidFilmLanguageInfoId(filmLanguageInfoId)) {
            Log.w(TAG, "Invalid filmLanguageInfoId format: " + filmLanguageInfoId);
            // 不阻止请求，让服务器处理
        }

        // 构建请求URL
        String apiUrl = VideoDetailApiConstantsUtils.buildVideoDetailUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            filmLanguageInfoId
        );

        // 构建请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "发起视频详情请求: " + apiUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 发起异步请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                callback.onError(errorMsg);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful() && response.body() != null) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "视频详情响应: " + responseBody);

                        // 解析JSON响应
                        VideoDetailResponseModel responseModel = gson.fromJson(responseBody, VideoDetailResponseModel.class);

                        if (responseModel != null && responseModel.isSuccess()) {
                            callback.onSuccess(responseModel);
                        } else {
                            String errorMsg = responseModel != null ? responseModel.getMessage() : "响应数据解析失败";
                            Log.e(TAG, "API响应错误: " + errorMsg);
                            callback.onError(errorMsg);
                        }
                    } else {
                        String errorMsg = "HTTP错误: " + response.code() + " " + response.message();
                        Log.e(TAG, errorMsg);
                        callback.onError(errorMsg);
                    }
                } catch (Exception e) {
                    String errorMsg = "响应解析失败: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    callback.onError(errorMsg);
                } finally {
                    if (response.body() != null) {
                        response.body().close();
                    }
                }
            }
        });
    }

    /**
     * 取消所有请求
     * <p>
     * 取消当前所有正在进行的网络请求。
     * 通常在Activity或Fragment销毁时调用。
     * </p>
     */
    public void cancelAllRequests() {
        if (httpClient != null) {
            httpClient.dispatcher().cancelAll();
            Log.d(TAG, "已取消所有视频详情API请求");
        }
    }
}
