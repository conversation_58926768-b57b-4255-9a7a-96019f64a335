package com.android.video.utils;

import java.util.regex.Pattern;

/**
 * Phone number validation utility for US, Russia, and China phone numbers
 * <AUTHOR>
 */
public class PhoneNumberValidatorUtils {
    
    // US phone number patterns
    /**
     * Supports: (*************, ************, ************, ************, 1234567890
     */
    private static final String US_PHONE_PATTERN = 
        "^(?:\\+?1[-\\s]?)?(?:\\(?[2-9]\\d{2}\\)?[-\\s]?)?[2-9]\\d{2}[-\\s]?\\d{4}$";
    
    // Russia phone number patterns
    /**
     * Supports: +7 XXX XXX-XX-XX, 8 XXX XXX-XX-XX, 7XXXXXXXXXX, 8XXXXXXXXXX
     */
    private static final String RUSSIA_PHONE_PATTERN = 
        "^(?:\\+?7|8)[-\\s]?(?:\\(?[489]\\d{2}\\)?[-\\s]?)?\\d{3}[-\\s]?\\d{2}[-\\s]?\\d{2}$";
    
    // China phone number patterns
    /**
     * Supports: +86 1XX XXXX XXXX, 1XX XXXX XXXX, 1XXXXXXXXXX
     */
    private static final String CHINA_PHONE_PATTERN = 
        "^(?:\\+?86[-\\s]?)?1[3-9]\\d[-\\s]?\\d{4}[-\\s]?\\d{4}$";
    
    private static final Pattern US_PATTERN = Pattern.compile(US_PHONE_PATTERN);
    private static final Pattern RUSSIA_PATTERN = Pattern.compile(RUSSIA_PHONE_PATTERN);
    private static final Pattern CHINA_PATTERN = Pattern.compile(CHINA_PHONE_PATTERN);
    
    /**
     * Country codes enum
     */
    public enum CountryCode {
        // 美
        US("+1", "United States"),
        // 俄
        RUSSIA("+7", "Russia"),
        // 中
        CHINA("+86", "China");
        
        private final String code;
        private final String name;
        
        CountryCode(String code, String name) {
            this.code = code;
            this.name = name;
        }
        
        public String getCode() {
            return code;
        }
        
        public String getName() {
            return name;
        }
        
        public static CountryCode fromCode(String code) {
            for (CountryCode country : values()) {
                if (country.code.equals(code)) {
                    return country;
                }
            }
            // Default to US
            return US;
        }
    }
    
    /**
     * Validation result class
     */
    public static class ValidationResult {
        private final boolean isValid;
        private final String errorMessage;
        private final CountryCode detectedCountry;
        
        public ValidationResult(boolean isValid, String errorMessage, CountryCode detectedCountry) {
            this.isValid = isValid;
            this.errorMessage = errorMessage;
            this.detectedCountry = detectedCountry;
        }
        
        public boolean isValid() {
            return isValid;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public CountryCode getDetectedCountry() {
            return detectedCountry;
        }
    }
    
    /**
     * Validate phone number based on the selected country code
     * 
     * @param phoneNumber The phone number to validate (without country code)
     * @param countryCode The selected country code (e.g., "+1", "+7", "+86")
     * @return ValidationResult containing validation status and error message
     */
    public static ValidationResult validatePhoneNumber(String phoneNumber, String countryCode) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return new ValidationResult(false, "Phone number cannot be empty", CountryCode.fromCode(countryCode));
        }
        
        // Clean the phone number (remove spaces, dashes, parentheses)
        String cleanNumber = phoneNumber.replaceAll("[\\s\\-\\(\\)\\.]", "");
        
        CountryCode country = CountryCode.fromCode(countryCode);
        
        switch (country) {
            case US:
                return validateUSNumber(cleanNumber, phoneNumber);
            case RUSSIA:
                return validateRussiaNumber(cleanNumber, phoneNumber);
            case CHINA:
                return validateChinaNumber(cleanNumber, phoneNumber);
            default:
                return new ValidationResult(false, "Unsupported country code", country);
        }
    }

    private static ValidationResult validateUSNumber(String cleanNumber, String originalNumber) {
        if (US_PATTERN.matcher(originalNumber).matches()) {
            return new ValidationResult(true, null, CountryCode.US);
        }
        
        // Additional validation for clean number
        if (cleanNumber.length() == 10 && cleanNumber.matches("^[2-9]\\d{2}[2-9]\\d{6}$")) {
            return new ValidationResult(true, null, CountryCode.US);
        } else if (cleanNumber.length() == 11 && cleanNumber.startsWith("1") && 
                   cleanNumber.substring(1).matches("^[2-9]\\d{2}[2-9]\\d{6}$")) {
            return new ValidationResult(true, null, CountryCode.US);
        }
        
        return new ValidationResult(false, "Invalid US phone number format. Please use format: (XXX) XXX-XXXX", CountryCode.US);
    }
    
    private static ValidationResult validateRussiaNumber(String cleanNumber, String originalNumber) {
        if (RUSSIA_PATTERN.matcher(originalNumber).matches()) {
            return new ValidationResult(true, null, CountryCode.RUSSIA);
        }
        
        // Additional validation for clean number
        if ((cleanNumber.length() == 10 && cleanNumber.startsWith("8")) ||
            (cleanNumber.length() == 11 && (cleanNumber.startsWith("7") || cleanNumber.startsWith("8")))) {
            return new ValidationResult(true, null, CountryCode.RUSSIA);
        }
        
        return new ValidationResult(false, "Invalid Russian phone number format. Please use format: 8 XXX XXX-XX-XX", CountryCode.RUSSIA);
    }
    
    private static ValidationResult validateChinaNumber(String cleanNumber, String originalNumber) {
        if (CHINA_PATTERN.matcher(originalNumber).matches()) {
            return new ValidationResult(true, null, CountryCode.CHINA);
        }
        
        // Additional validation for clean number
        if (cleanNumber.length() == 11 && cleanNumber.startsWith("1") && 
            cleanNumber.charAt(1) >= '3' && cleanNumber.charAt(1) <= '9') {
            return new ValidationResult(true, null, CountryCode.CHINA);
        } else if (cleanNumber.length() == 13 && cleanNumber.startsWith("86") &&
                   cleanNumber.substring(2).matches("^1[3-9]\\d{9}$")) {
            return new ValidationResult(true, null, CountryCode.CHINA);
        }
        
        return new ValidationResult(false, "Invalid Chinese phone number format. Please use format: 1XX XXXX XXXX", CountryCode.CHINA);
    }
    
    /**
     * Format phone number for display
     * 
     * @param phoneNumber The phone number to format
     * @param countryCode The country code
     * @return Formatted phone number string
     */
    public static String formatPhoneNumber(String phoneNumber, String countryCode) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return phoneNumber;
        }
        
        String cleanNumber = phoneNumber.replaceAll("[\\s\\-\\(\\)\\.]", "");
        CountryCode country = CountryCode.fromCode(countryCode);
        
        switch (country) {
            case US:
                return formatUSNumber(cleanNumber);
            case RUSSIA:
                return formatRussiaNumber(cleanNumber);
            case CHINA:
                return formatChinaNumber(cleanNumber);
            default:
                return phoneNumber;
        }
    }
    
    private static String formatUSNumber(String cleanNumber) {
        if (cleanNumber.length() == 10) {
            return String.format("(%s) %s-%s", 
                cleanNumber.substring(0, 3),
                cleanNumber.substring(3, 6),
                cleanNumber.substring(6));
        } else if (cleanNumber.length() == 11 && cleanNumber.startsWith("1")) {
            return String.format("(%s) %s-%s", 
                cleanNumber.substring(1, 4),
                cleanNumber.substring(4, 7),
                cleanNumber.substring(7));
        }
        return cleanNumber;
    }
    
    private static String formatRussiaNumber(String cleanNumber) {
        if (cleanNumber.length() == 10 && cleanNumber.startsWith("8")) {
            return String.format("8 %s %s-%s-%s",
                cleanNumber.substring(1, 4),
                cleanNumber.substring(4, 7),
                cleanNumber.substring(7, 9),
                cleanNumber.substring(9));
        } else if (cleanNumber.length() == 11 && cleanNumber.startsWith("7")) {
            return String.format("+7 %s %s-%s-%s",
                cleanNumber.substring(1, 4),
                cleanNumber.substring(4, 7),
                cleanNumber.substring(7, 9),
                cleanNumber.substring(9));
        }
        return cleanNumber;
    }
    
    private static String formatChinaNumber(String cleanNumber) {
        if (cleanNumber.length() == 11 && cleanNumber.startsWith("1")) {
            return String.format("%s %s %s",
                cleanNumber.substring(0, 3),
                cleanNumber.substring(3, 7),
                cleanNumber.substring(7));
        } else if (cleanNumber.length() == 13 && cleanNumber.startsWith("86")) {
            return String.format("+86 %s %s %s",
                cleanNumber.substring(2, 5),
                cleanNumber.substring(5, 9),
                cleanNumber.substring(9));
        }
        return cleanNumber;
    }
}
