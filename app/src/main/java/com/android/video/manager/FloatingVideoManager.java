package com.android.video.manager;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;
import android.widget.Toast;

import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.model.VideoModel;
import com.android.video.service.FloatingVideoService;
import com.android.video.utils.FloatingVideoPermissionUtils;

/**
 * 悬浮窗视频播放管理器
 * 
 * 提供悬浮窗视频播放的统一管理接口，包括权限检查、服务启动等功能。
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class FloatingVideoManager {
    
    private static final String TAG = "FloatingVideoManager";
    private static FloatingVideoManager instance;
    
    private boolean isFloatingWindowActive = false;
    
    /**
     * 获取单例实例
     */
    public static synchronized FloatingVideoManager getInstance() {
        if (instance == null) {
            instance = new FloatingVideoManager();
        }
        return instance;
    }
    
    private FloatingVideoManager() {
        // 私有构造函数
    }
    
    /**
     * 启动悬浮窗播放
     * 
     * @param activity 当前Activity
     * @param videoModel 视频模型
     * @param sourceActivity 源Activity类型
     */
    public void startFloatingVideo(Activity activity, VideoModel videoModel, String sourceActivity) {
        if (activity == null || videoModel == null) {
            Log.e(TAG, "Activity or VideoModel is null");
            return;
        }
        
        Log.d(TAG, "Starting floating video for: " + videoModel.getTitle());
        
        // 检查权限
        FloatingVideoPermissionUtils.checkAndRequestPermission(activity, 
            new FloatingVideoPermissionUtils.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    // 权限已授予，启动悬浮窗服务
                    startFloatingVideoService(activity, videoModel, sourceActivity);
                }
                
                @Override
                public void onPermissionDenied() {
                    // 权限被拒绝，显示提示
                    showPermissionDeniedMessage(activity);
                }
            });
    }
    
    /**
     * 启动悬浮窗服务
     */
    private void startFloatingVideoService(Context context, VideoModel videoModel, String sourceActivity) {
        try {
            // 暂停所有当前播放的视频
            pauseAllCurrentVideos();

            Intent serviceIntent = new Intent(context, FloatingVideoService.class);
            serviceIntent.putExtra(FloatingVideoService.EXTRA_VIDEO_MODEL, videoModel);
            serviceIntent.putExtra(FloatingVideoService.EXTRA_SOURCE_ACTIVITY, sourceActivity);

            context.startForegroundService(serviceIntent);
            isFloatingWindowActive = true;

            Log.d(TAG, "Floating video service started");

            // 显示成功提示
            Toast.makeText(context, "小窗播放已启动", Toast.LENGTH_SHORT).show();

        } catch (Exception e) {
            Log.e(TAG, "Failed to start floating video service", e);
            Toast.makeText(context, "启动小窗播放失败", Toast.LENGTH_SHORT).show();
        }
    }
    
    /**
     * 停止悬浮窗播放
     */
    public void stopFloatingVideo(Context context) {
        try {
            Intent serviceIntent = new Intent(context, FloatingVideoService.class);
            context.stopService(serviceIntent);
            isFloatingWindowActive = false;
            
            Log.d(TAG, "Floating video service stopped");
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to stop floating video service", e);
        }
    }
    
    /**
     * 检查悬浮窗是否正在播放
     */
    public boolean isFloatingWindowActive() {
        return isFloatingWindowActive;
    }
    
    /**
     * 设置悬浮窗状态
     */
    public void setFloatingWindowActive(boolean active) {
        this.isFloatingWindowActive = active;
    }
    
    /**
     * 显示权限被拒绝的消息
     */
    private void showPermissionDeniedMessage(Context context) {
        String message = FloatingVideoPermissionUtils.getPermissionDescription();
        Toast.makeText(context, message, Toast.LENGTH_LONG).show();
    }
    
    /**
     * 检查设备是否支持悬浮窗
     */
    public boolean isFloatingWindowSupported(Context context) {
        return FloatingVideoPermissionUtils.isFloatingWindowSupported(context);
    }
    
    /**
     * 处理权限请求结果
     * 在Activity的onActivityResult中调用
     */
    public void handlePermissionResult(Context context, int requestCode) {
        FloatingVideoPermissionUtils.handlePermissionResult(context, requestCode, 
            new FloatingVideoPermissionUtils.PermissionCallback() {
                @Override
                public void onPermissionGranted() {
                    Toast.makeText(context, "权限已授予，请重新点击小窗播放按钮", Toast.LENGTH_SHORT).show();
                }
                
                @Override
                public void onPermissionDenied() {
                    showPermissionDeniedMessage(context);
                }
            });
    }
    
    /**
     * 清理资源
     * 在应用退出时调用
     */
    public void cleanup(Context context) {
        stopFloatingVideo(context);
        FloatingVideoPermissionUtils.clearPermissionCallback();
    }
    
    /**
     * 从视频播放器页面启动悬浮窗
     */
    public void startFromVideoPlayer(Activity activity, VideoModel videoModel) {
        startFloatingVideo(activity, videoModel, FloatingVideoService.SOURCE_VIDEO_PLAYER);
    }
    
    /**
     * 从Discover页面启动悬浮窗
     */
    public void startFromDiscover(Activity activity, VideoModel videoModel) {
        startFloatingVideo(activity, videoModel, FloatingVideoService.SOURCE_DISCOVER);
    }
    
    /**
     * 从下载视频播放页面启动悬浮窗
     */
    public void startFromDownloadPlayer(Activity activity, VideoModel videoModel) {
        startFloatingVideo(activity, videoModel, FloatingVideoService.SOURCE_DOWNLOAD_PLAYER);
    }
    
    /**
     * 暂停所有当前播放的视频并隐藏原视频UI
     */
    private void pauseAllCurrentVideos() {
        try {
            // 使用全局视频播放器管理器暂停所有播放
            GlobalVideoPlayerManager.getInstance().stopAllFragments();

            // 隐藏所有原视频的PlayerView，显示黑色背景
            hideAllVideoPlayerViews();

            Log.d(TAG, "All current videos paused and hidden before starting floating video");
        } catch (Exception e) {
            Log.e(TAG, "Failed to pause current videos", e);
        }
    }

    /**
     * 隐藏所有视频播放器的PlayerView，显示黑色背景
     */
    private void hideAllVideoPlayerViews() {
        try {
            // 获取当前活动的Activity
            android.app.Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                Log.w(TAG, "No current activity found, cannot hide video views");
                return;
            }

            // 查找并隐藏所有PlayerView
            hidePlayerViewsInActivity(currentActivity);

        } catch (Exception e) {
            Log.e(TAG, "Failed to hide video player views", e);
        }
    }

    /**
     * 在指定Activity中隐藏PlayerView
     */
    private void hidePlayerViewsInActivity(android.app.Activity activity) {
        try {
            // 获取Activity的根视图
            android.view.View rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                hidePlayerViewsRecursively(rootView);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide player views in activity", e);
        }
    }

    /**
     * 递归查找并隐藏PlayerView
     */
    private void hidePlayerViewsRecursively(android.view.View view) {
        try {
            if (view instanceof com.google.android.exoplayer2.ui.PlayerView) {
                // 找到PlayerView，设置为黑色背景并隐藏视频内容
                com.google.android.exoplayer2.ui.PlayerView playerView = (com.google.android.exoplayer2.ui.PlayerView) view;
                playerView.setBackgroundColor(android.graphics.Color.BLACK);
                playerView.setVisibility(android.view.View.VISIBLE); // 保持可见但显示黑色

                // 隐藏播放器内容但保留黑色背景
                if (playerView.getPlayer() != null) {
                    playerView.setPlayer(null); // 临时移除播放器，显示黑色背景
                }

                Log.d(TAG, "Hidden PlayerView and set black background");
            } else if (view instanceof android.view.ViewGroup) {
                // 如果是ViewGroup，递归检查子视图
                android.view.ViewGroup viewGroup = (android.view.ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    hidePlayerViewsRecursively(viewGroup.getChildAt(i));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding player view recursively", e);
        }
    }

    /**
     * 获取当前活动的Activity
     */
    private android.app.Activity getCurrentActivity() {
        try {
            // 这里需要根据实际情况获取当前Activity
            // 可以通过Application类或其他方式获取
            return null; // 暂时返回null，需要根据实际架构实现
        } catch (Exception e) {
            Log.e(TAG, "Failed to get current activity", e);
            return null;
        }
    }

    /**
     * 获取当前播放的视频信息
     * TODO: 如果需要在多个地方访问当前播放的视频信息，可以在这里维护状态
     */
    public VideoModel getCurrentPlayingVideo() {
        // 暂时返回null，可以根据需要实现
        return null;
    }
    
    /**
     * 检查是否有悬浮窗权限
     */
    public boolean hasOverlayPermission(Context context) {
        return FloatingVideoPermissionUtils.hasOverlayPermission(context);
    }

    /**
     * 恢复原视频播放器状态
     */
    public void restoreOriginalVideoPlayer() {
        try {
            Log.d(TAG, "Restoring original video player");

            // 恢复VideoPlayerFragment的播放器状态
            restoreVideoPlayerFragments();

            // 恢复所有视频播放器的PlayerView
            restoreAllVideoPlayerViews();

        } catch (Exception e) {
            Log.e(TAG, "Failed to restore original video player", e);
        }
    }

    /**
     * 恢复所有视频播放器的PlayerView
     */
    private void restoreAllVideoPlayerViews() {
        try {
            // 获取当前活动的Activity
            android.app.Activity currentActivity = getCurrentActivity();
            if (currentActivity == null) {
                Log.w(TAG, "No current activity found, cannot restore video views");
                return;
            }

            // 查找并恢复所有PlayerView
            restorePlayerViewsInActivity(currentActivity);

        } catch (Exception e) {
            Log.e(TAG, "Failed to restore video player views", e);
        }
    }

    /**
     * 在Activity中恢复PlayerView
     */
    private void restorePlayerViewsInActivity(android.app.Activity activity) {
        try {
            android.view.View rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                restorePlayerViewsRecursively(rootView);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error restoring player views in activity", e);
        }
    }

    /**
     * 递归查找并恢复PlayerView
     */
    private void restorePlayerViewsRecursively(android.view.View view) {
        try {
            if (view instanceof com.google.android.exoplayer2.ui.PlayerView) {
                // 找到PlayerView，恢复正常状态
                com.google.android.exoplayer2.ui.PlayerView playerView = (com.google.android.exoplayer2.ui.PlayerView) view;

                // 恢复背景色
                playerView.setBackgroundColor(android.graphics.Color.TRANSPARENT);

                Log.d(TAG, "Restored PlayerView");
            } else if (view instanceof android.view.ViewGroup) {
                // 递归查找子视图
                android.view.ViewGroup viewGroup = (android.view.ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    restorePlayerViewsRecursively(viewGroup.getChildAt(i));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error restoring player view recursively", e);
        }
    }

    /**
     * 恢复VideoPlayerFragment的播放器状态
     */
    private void restoreVideoPlayerFragments() {
        try {
            // 通过GlobalVideoPlayerManager恢复所有Fragment的播放器
            GlobalVideoPlayerManager globalManager = GlobalVideoPlayerManager.getInstance();

            // 获取所有注册的Fragment并恢复它们的PlayerView
            java.lang.reflect.Field fragmentsField = globalManager.getClass().getDeclaredField("allFragments");
            fragmentsField.setAccessible(true);
            @SuppressWarnings("unchecked")
            java.util.List<java.lang.ref.WeakReference<com.android.video.ui.fragment.VideoPlayerFragment>> allFragments =
                (java.util.List<java.lang.ref.WeakReference<com.android.video.ui.fragment.VideoPlayerFragment>>) fragmentsField.get(globalManager);

            for (java.lang.ref.WeakReference<com.android.video.ui.fragment.VideoPlayerFragment> ref : allFragments) {
                com.android.video.ui.fragment.VideoPlayerFragment fragment = ref.get();
                if (fragment != null) {
                    fragment.restorePlayerViewFromFloating();
                    Log.d(TAG, "Restored VideoPlayerFragment at position " + fragment.getFragmentPosition());
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to restore VideoPlayerFragments via reflection", e);
        }
    }
}
