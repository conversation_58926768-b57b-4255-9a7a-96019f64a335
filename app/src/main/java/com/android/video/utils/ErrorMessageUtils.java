package com.android.video.utils;

import android.content.Context;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

/**
 * 错误文案管理工具类
 * <p>
 * 统一管理应用中的错误提示文案和样式
 * </p>
 */
public class ErrorMessageUtils {
    
    // 错误类型常量
    public static final int ERROR_TYPE_NETWORK = 1;
    public static final int ERROR_TYPE_CONTENT_LOAD = 2;
    public static final int ERROR_TYPE_LOGIN = 3;
    public static final int ERROR_TYPE_SERVER = 4;
    public static final int ERROR_TYPE_UNKNOWN = 5;
    
    /**
     * 网络错误文案
     */
    public static class NetworkError {
        public static final String TITLE = "网络连接异常";
        public static final String MESSAGE = "请检查您的网络连接后重试";
        public static final String BUTTON_TEXT = "重新连接";
        public static final String DETAIL = "无法连接到服务器，请确保网络畅通";
    }
    
    /**
     * 内容加载失败文案
     */
    public static class ContentLoadError {
        public static final String TITLE = "内容加载失败";
        public static final String MESSAGE = "暂时无法加载内容，请稍后重试";
        public static final String BUTTON_TEXT = "重新加载";
        public static final String DETAIL = "服务器繁忙或内容不存在";
    }
    
    /**
     * 登录失败文案
     */
    public static class LoginError {
        public static final String TITLE = "登录失败";
        public static final String MESSAGE = "登录信息有误，请重新输入";
        public static final String BUTTON_TEXT = "重新登录";
        public static final String DETAIL = "用户名或密码错误，或账户已被锁定";
        
        // 具体登录错误
        public static final String INVALID_PHONE = "手机号格式不正确";
        public static final String INVALID_CODE = "验证码错误或已过期";
        public static final String ACCOUNT_LOCKED = "账户已被锁定，请联系客服";
        public static final String TOKEN_EXPIRED = "登录已过期，请重新登录";
    }
    
    /**
     * 服务器错误文案
     */
    public static class ServerError {
        public static final String TITLE = "服务暂时不可用";
        public static final String MESSAGE = "服务器正在维护中，请稍后重试";
        public static final String BUTTON_TEXT = "稍后重试";
        public static final String DETAIL = "系统升级中，预计恢复时间：30分钟";
    }
    
    /**
     * 显示错误提示
     */
    public static void showError(Context context, int errorType) {
        showError(context, errorType, null);
    }
    
    /**
     * 显示错误提示（带自定义消息）
     */
    public static void showError(Context context, int errorType, String customMessage) {
        if (context == null) return;
        
        String title;
        String message;
        
        switch (errorType) {
            case ERROR_TYPE_NETWORK:
                title = NetworkError.TITLE;
                message = customMessage != null ? customMessage : NetworkError.MESSAGE;
                break;
            case ERROR_TYPE_CONTENT_LOAD:
                title = ContentLoadError.TITLE;
                message = customMessage != null ? customMessage : ContentLoadError.MESSAGE;
                break;
            case ERROR_TYPE_LOGIN:
                title = LoginError.TITLE;
                message = customMessage != null ? customMessage : LoginError.MESSAGE;
                break;
            case ERROR_TYPE_SERVER:
                title = ServerError.TITLE;
                message = customMessage != null ? customMessage : ServerError.MESSAGE;
                break;
            default:
                title = "操作失败";
                message = customMessage != null ? customMessage : "发生未知错误，请重试";
                break;
        }
        
        showCustomToast(context, title, message, errorType);
    }
    
    /**
     * 显示自定义样式的Toast
     */
    private static void showCustomToast(Context context, String title, String message, int errorType) {
        try {
            // 创建自定义Toast布局
            LayoutInflater inflater = LayoutInflater.from(context);
            
            // 创建简单的错误提示布局
            android.widget.LinearLayout layout = new android.widget.LinearLayout(context);
            layout.setOrientation(android.widget.LinearLayout.VERTICAL);
            layout.setBackgroundColor(0xDD000000); // 半透明黑色背景
            layout.setPadding(48, 32, 48, 32);
            layout.setGravity(Gravity.CENTER);
            
            // 错误图标
            ImageView iconView = new ImageView(context);
            iconView.setImageResource(getErrorIcon(errorType));
            android.widget.LinearLayout.LayoutParams iconParams = 
                new android.widget.LinearLayout.LayoutParams(72, 72);
            iconParams.bottomMargin = 16;
            iconView.setLayoutParams(iconParams);
            
            // 标题
            TextView titleView = new TextView(context);
            titleView.setText(title);
            titleView.setTextColor(0xFFFFFFFF);
            titleView.setTextSize(16);
            titleView.setGravity(Gravity.CENTER);
            android.widget.LinearLayout.LayoutParams titleParams = 
                new android.widget.LinearLayout.LayoutParams(
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                    android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
                );
            titleParams.bottomMargin = 8;
            titleView.setLayoutParams(titleParams);
            
            // 消息
            TextView messageView = new TextView(context);
            messageView.setText(message);
            messageView.setTextColor(0xFFCCCCCC);
            messageView.setTextSize(14);
            messageView.setGravity(Gravity.CENTER);
            messageView.setMaxLines(3);
            
            layout.addView(iconView);
            layout.addView(titleView);
            layout.addView(messageView);
            
            // 创建并显示Toast
            Toast toast = new Toast(context);
            toast.setView(layout);
            toast.setDuration(Toast.LENGTH_LONG);
            toast.setGravity(Gravity.CENTER, 0, 0);
            toast.show();
            
        } catch (Exception e) {
            // 如果自定义Toast失败，使用系统Toast
            Toast.makeText(context, title + ": " + message, Toast.LENGTH_LONG).show();
        }
    }
    
    /**
     * 获取错误类型对应的图标
     */
    private static int getErrorIcon(int errorType) {
        switch (errorType) {
            case ERROR_TYPE_NETWORK:
                return android.R.drawable.ic_dialog_alert;
            case ERROR_TYPE_CONTENT_LOAD:
                return android.R.drawable.ic_menu_info_details;
            case ERROR_TYPE_LOGIN:
                return android.R.drawable.ic_lock_lock;
            case ERROR_TYPE_SERVER:
                return android.R.drawable.ic_dialog_info;
            default:
                return android.R.drawable.ic_dialog_alert;
        }
    }
    
    /**
     * 创建错误状态视图
     */
    public static View createErrorStateView(Context context, int errorType, View.OnClickListener retryListener) {
        android.widget.LinearLayout layout = new android.widget.LinearLayout(context);
        layout.setOrientation(android.widget.LinearLayout.VERTICAL);
        layout.setGravity(Gravity.CENTER);
        layout.setPadding(64, 64, 64, 64);
        
        // 错误图标
        ImageView iconView = new ImageView(context);
        iconView.setImageResource(getErrorIcon(errorType));
        iconView.setColorFilter(0xFF999999);
        android.widget.LinearLayout.LayoutParams iconParams = 
            new android.widget.LinearLayout.LayoutParams(120, 120);
        iconParams.bottomMargin = 24;
        iconView.setLayoutParams(iconParams);
        
        // 错误标题
        TextView titleView = new TextView(context);
        titleView.setText(getErrorTitle(errorType));
        titleView.setTextColor(0xFF333333);
        titleView.setTextSize(18);
        titleView.setGravity(Gravity.CENTER);
        android.widget.LinearLayout.LayoutParams titleParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            );
        titleParams.bottomMargin = 12;
        titleView.setLayoutParams(titleParams);
        
        // 错误描述
        TextView messageView = new TextView(context);
        messageView.setText(getErrorMessage(errorType));
        messageView.setTextColor(0xFF666666);
        messageView.setTextSize(14);
        messageView.setGravity(Gravity.CENTER);
        messageView.setMaxLines(3);
        android.widget.LinearLayout.LayoutParams messageParams = 
            new android.widget.LinearLayout.LayoutParams(
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT,
                android.widget.LinearLayout.LayoutParams.WRAP_CONTENT
            );
        messageParams.bottomMargin = 32;
        messageView.setLayoutParams(messageParams);
        
        // 重试按钮
        android.widget.Button retryButton = new android.widget.Button(context);
        retryButton.setText(getErrorButtonText(errorType));
        retryButton.setBackgroundColor(0xFF007AFF);
        retryButton.setTextColor(0xFFFFFFFF);
        retryButton.setPadding(48, 24, 48, 24);
        
        if (retryListener != null) {
            retryButton.setOnClickListener(v -> {
                UIAnimationUtils.animateButtonClick(v);
                retryListener.onClick(v);
            });
        }
        
        layout.addView(iconView);
        layout.addView(titleView);
        layout.addView(messageView);
        layout.addView(retryButton);
        
        return layout;
    }
    
    private static String getErrorTitle(int errorType) {
        switch (errorType) {
            case ERROR_TYPE_NETWORK: return NetworkError.TITLE;
            case ERROR_TYPE_CONTENT_LOAD: return ContentLoadError.TITLE;
            case ERROR_TYPE_LOGIN: return LoginError.TITLE;
            case ERROR_TYPE_SERVER: return ServerError.TITLE;
            default: return "操作失败";
        }
    }
    
    private static String getErrorMessage(int errorType) {
        switch (errorType) {
            case ERROR_TYPE_NETWORK: return NetworkError.MESSAGE;
            case ERROR_TYPE_CONTENT_LOAD: return ContentLoadError.MESSAGE;
            case ERROR_TYPE_LOGIN: return LoginError.MESSAGE;
            case ERROR_TYPE_SERVER: return ServerError.MESSAGE;
            default: return "发生未知错误，请重试";
        }
    }
    
    private static String getErrorButtonText(int errorType) {
        switch (errorType) {
            case ERROR_TYPE_NETWORK: return NetworkError.BUTTON_TEXT;
            case ERROR_TYPE_CONTENT_LOAD: return ContentLoadError.BUTTON_TEXT;
            case ERROR_TYPE_LOGIN: return LoginError.BUTTON_TEXT;
            case ERROR_TYPE_SERVER: return ServerError.BUTTON_TEXT;
            default: return "重试";
        }
    }
}
