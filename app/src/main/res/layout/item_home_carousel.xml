<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="@dimen/home_carousel_selected_height"
    android:layout_marginEnd="@dimen/home_carousel_item_spacing"
    android:clickable="true"
    android:focusable="true">

    <!-- 使用FrameLayout来实现边框效果 -->
    <FrameLayout
        android:id="@+id/fl_carousel_border"
        android:layout_width="@dimen/home_carousel_unselected_width"
        android:layout_height="@dimen/home_carousel_unselected_height"
        android:background="@drawable/home_carousel_border_selector"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:id="@+id/iv_carousel_poster"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="1dp"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/home_carousel_image_background"
            android:clipToOutline="true" />

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
