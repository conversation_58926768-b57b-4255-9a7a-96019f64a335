package com.android.video.ui.component;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.content.Context;
import android.util.AttributeSet;
import android.view.View;
import android.view.ViewGroup;
import android.view.View.MeasureSpec;
import android.widget.LinearLayout;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;

import java.util.Arrays;
import java.util.List;

/**
 * 播放速度选择组件
 * 提供播放速度选择的弹出列表UI
 * <AUTHOR>
 */
public class SpeedSelectionView extends LinearLayout {
    
    private RecyclerView recyclerView;
    private SpeedAdapter adapter;
    private List<Float> speedList;
    private float selectedSpeed = 1.0f;
    private OnSpeedSelectedListener listener;
    private boolean isVisible = false;
    
    public interface OnSpeedSelectedListener {
        void onSpeedSelected(float speed);
    }
    
    public SpeedSelectionView(Context context) {
        super(context);
        init();
    }
    
    public SpeedSelectionView(Context context, @Nullable AttributeSet attrs) {
        super(context, attrs);
        init();
    }
    
    public SpeedSelectionView(Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init();
    }
    
    private void init() {
        setOrientation(VERTICAL);
        setBackgroundResource(R.drawable.video_player_popup_bg);
        
        // 设置固定尺寸
        LayoutParams params = new LayoutParams(
            getResources().getDimensionPixelSize(R.dimen.video_player_popup_width),
            getResources().getDimensionPixelSize(R.dimen.video_player_popup_height)
        );
        setLayoutParams(params);
        
        // 初始化RecyclerView
        recyclerView = new RecyclerView(getContext());
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext()));
        // 确保RecyclerView可以接收触摸事件
        recyclerView.setClickable(true);
        recyclerView.setFocusable(true);
        addView(recyclerView);
        
        // 设置默认速度列表
        speedList = Arrays.asList(0.5f, 0.7f, 1.0f, 1.5f, 2.0f, 3.0f);
        
        // 初始化适配器
        adapter = new SpeedAdapter();
        recyclerView.setAdapter(adapter);
        
        // 初始状态为隐藏
        setVisibility(GONE);
        setAlpha(0f);
    }
    
    /**
     * 设置速度列表
     */
    public void setSpeedList(List<Float> speedList) {
        this.speedList = speedList;
        adapter.notifyDataSetChanged();
    }
    
    /**
     * 设置当前选中的速度
     */
    public void setSelectedSpeed(float speed) {
        this.selectedSpeed = speed;
        adapter.notifyDataSetChanged();
    }
    
    /**
     * 设置选择监听器
     */
    public void setOnSpeedSelectedListener(OnSpeedSelectedListener listener) {
        this.listener = listener;
    }
    
    /**
     * 显示选择列表
     */
    public void show() {
        if (isVisible) return;

        setVisibility(VISIBLE);
        setClickable(true); // 显示时允许点击

        // 缓慢出现动画
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 0f, 1f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 0.8f, 1f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 0.8f, 1f);

        alphaAnimator.setDuration(250);
        scaleXAnimator.setDuration(250);
        scaleYAnimator.setDuration(250);

        alphaAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();

        isVisible = true;

        // 添加点击外部关闭功能
        setupOutsideClickListener();
    }

    /**
     * 显示弹窗在指定视图上方
     */
    public void showAbove(View anchorView) {
        if (isVisible) return;

        // 确保布局已完成
        post(() -> {
            // 强制测量以获取准确的尺寸
            measure(MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                    MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED));

            // 计算位置
            int[] location = new int[2];
            anchorView.getLocationInWindow(location);

            // 获取父容器
            ViewGroup parent = (ViewGroup) getParent();
            if (parent != null) {
                // 设置位置在按钮上方12dp处
                int offsetY = getResources().getDimensionPixelSize(R.dimen.video_player_speed_popup_offset);

                // 计算相对于父容器的位置
                int[] parentLocation = new int[2];
                parent.getLocationInWindow(parentLocation);

                // 居中对齐按钮
                int relativeX = location[0] - parentLocation[0] - (getMeasuredWidth() - anchorView.getWidth()) / 2;
                int relativeY = location[1] - parentLocation[1] - getMeasuredHeight() - offsetY;

                setTranslationX(relativeX);
                setTranslationY(relativeY);
            }

            show();
        });
    }
    
    /**
     * 隐藏选择列表
     */
    public void hide() {
        if (!isVisible) return;

        // 缓慢消失动画
        ObjectAnimator alphaAnimator = ObjectAnimator.ofFloat(this, "alpha", 1f, 0f);
        ObjectAnimator scaleXAnimator = ObjectAnimator.ofFloat(this, "scaleX", 1f, 0.8f);
        ObjectAnimator scaleYAnimator = ObjectAnimator.ofFloat(this, "scaleY", 1f, 0.8f);

        alphaAnimator.setDuration(200);
        scaleXAnimator.setDuration(200);
        scaleYAnimator.setDuration(200);

        alphaAnimator.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                setVisibility(GONE);
                setClickable(false); // 隐藏时禁止点击，避免拦截底部按钮
                removeOutsideClickListener();
            }
        });

        alphaAnimator.start();
        scaleXAnimator.start();
        scaleYAnimator.start();

        isVisible = false;
    }

    /**
     * 设置外部点击监听器
     */
    private void setupOutsideClickListener() {
        // 创建一个透明的覆盖层来捕获外部点击
        ViewGroup parent = (ViewGroup) getParent();
        if (parent != null) {
            View overlay = new View(getContext());
            overlay.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.MATCH_PARENT));
            overlay.setBackgroundColor(android.graphics.Color.TRANSPARENT);
            overlay.setClickable(true);
            overlay.setOnClickListener(v -> hide());

            // 将覆盖层添加到父容器，但在弹窗下方
            int index = parent.indexOfChild(this);
            if (index > 0) {
                parent.addView(overlay, index);
            } else {
                parent.addView(overlay, 0);
            }

            // 保存覆盖层引用以便后续移除
            setTag(R.id.btnSpeedSelection, overlay);
        }
    }

    /**
     * 移除外部点击监听器
     */
    private void removeOutsideClickListener() {
        ViewGroup parent = (ViewGroup) getParent();
        if (parent != null) {
            View overlay = (View) getTag(R.id.btnSpeedSelection);
            if (overlay != null) {
                parent.removeView(overlay);
                setTag(R.id.btnSpeedSelection, null);
            }
        }
    }
    
    /**
     * 切换显示状态
     */
    public void toggle() {
        if (isVisible) {
            hide();
        } else {
            show();
        }
    }
    
    /**
     * 格式化速度显示文本
     */
    private String formatSpeedText(float speed) {
        if (speed == 1.0f) {
            return "正常";
        } else {
            return speed + "x";
        }
    }
    
    /**
     * 播放速度适配器
     */
    private class SpeedAdapter extends RecyclerView.Adapter<SpeedAdapter.ViewHolder> {
        
        @NonNull
        @Override
        public ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
            // 创建包含文本和分割线的容器
            LinearLayout container = new LinearLayout(getContext());
            container.setOrientation(LinearLayout.VERTICAL);
            container.setLayoutParams(new ViewGroup.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                getResources().getDimensionPixelSize(R.dimen.video_player_popup_item_height)
            ));

            TextView textView = new TextView(getContext());
            LinearLayout.LayoutParams textParams = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                0,
                1.0f
            );
            textView.setLayoutParams(textParams);
            textView.setGravity(android.view.Gravity.CENTER);
            textView.setTextSize(14);
            textView.setClickable(true);
            textView.setFocusable(true);

            // 添加分割线
            View divider = new View(getContext());
            LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen.video_player_speed_popup_divider_width),
                getResources().getDimensionPixelSize(R.dimen.video_player_speed_popup_divider_height)
            );
            dividerParams.gravity = android.view.Gravity.CENTER_HORIZONTAL;
            divider.setLayoutParams(dividerParams);
            divider.setBackgroundColor(getResources().getColor(R.color.popup_divider_color));

            container.addView(textView);
            container.addView(divider);

            return new ViewHolder(container, textView);
        }
        
        @Override
        public void onBindViewHolder(@NonNull ViewHolder holder, int position) {
            float speed = speedList.get(position);
            holder.textView.setText(formatSpeedText(speed));

            // 设置选中状态
            boolean isSelected = Math.abs(speed - selectedSpeed) < 0.01f;
            if (isSelected) {
                // 设置选中背景，确保宽度为弹窗宽度
                holder.itemView.setBackgroundResource(R.drawable.video_player_popup_selected_bg);
                holder.textView.setTextColor(getResources().getColor(R.color.video_player_popup_selected_text));
            } else {
                holder.itemView.setBackground(null);
                holder.textView.setTextColor(getResources().getColor(R.color.video_player_text_white));
            }

            // 隐藏最后一个item的分割线
            LinearLayout container = (LinearLayout) holder.itemView;
            if (container.getChildCount() > 1) {
                View divider = container.getChildAt(1); // 分割线是第二个子视图
                if (position == getItemCount() - 1) {
                    // 最后一个item，隐藏分割线
                    divider.setVisibility(View.GONE);
                } else {
                    // 其他item，显示分割线
                    divider.setVisibility(View.VISIBLE);
                }
            }

            // 设置点击事件 - 同时设置容器和文本的点击事件
            View.OnClickListener clickListener = v -> {
                selectedSpeed = speed;
                notifyDataSetChanged(); // 更新选中状态
                if (listener != null) {
                    listener.onSpeedSelected(speed);
                }
                hide();
            };

            holder.itemView.setOnClickListener(clickListener);
            holder.textView.setOnClickListener(clickListener);
        }
        
        @Override
        public int getItemCount() {
            return speedList != null ? speedList.size() : 0;
        }
        
        class ViewHolder extends RecyclerView.ViewHolder {
            TextView textView;

            ViewHolder(View itemView, TextView textView) {
                super(itemView);
                this.textView = textView;
            }
        }
    }
}
