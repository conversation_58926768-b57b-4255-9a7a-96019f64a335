<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@drawable/language_dialog_background"
    android:gravity="center_horizontal"
    android:padding="24dp">

    <!-- Title -->
    <TextView
        android:id="@+id/tv_dialog_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Select Language"
        android:textColor="#ffffffff"
        android:textSize="18sp"
        android:fontFamily="sans-serif-medium"
        android:gravity="center" />

    <!-- Language Options Container -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:orientation="vertical">

        <!-- English Option -->
        <LinearLayout
            android:id="@+id/option_english"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="English"
                android:textColor="#ffffffff"
                android:textSize="16sp"
                android:fontFamily="sans-serif" />

            <ImageView
                android:id="@+id/iv_english_check"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_check_circle"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#33ffffff"
            android:layout_marginHorizontal="16dp" />

        <!-- Russian Option -->
        <LinearLayout
            android:id="@+id/option_russian"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Russian"
                android:textColor="#ffffffff"
                android:textSize="16sp"
                android:fontFamily="sans-serif" />

            <ImageView
                android:id="@+id/iv_russian_check"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_check_circle"
                android:visibility="gone" />

        </LinearLayout>

        <!-- Divider -->
        <View
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="#33ffffff"
            android:layout_marginHorizontal="16dp" />

        <!-- Kaza Option -->
        <LinearLayout
            android:id="@+id/option_kaza"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:background="?android:attr/selectableItemBackground"
            android:clickable="true"
            android:focusable="true"
            android:paddingHorizontal="16dp">

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Kaza"
                android:textColor="#ffffffff"
                android:textSize="16sp"
                android:fontFamily="sans-serif" />

            <ImageView
                android:id="@+id/iv_kaza_check"
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:src="@drawable/ic_check_circle"
                android:visibility="gone" />

        </LinearLayout>

    </LinearLayout>

    <!-- Cancel Button -->
    <TextView
        android:id="@+id/btn_cancel"
        android:layout_width="match_parent"
        android:layout_height="44dp"
        android:layout_marginTop="24dp"
        android:background="@drawable/language_dialog_cancel_button_bg"
        android:text="Cancel"
        android:textColor="#8fffffff"
        android:textSize="16sp"
        android:fontFamily="sans-serif"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
