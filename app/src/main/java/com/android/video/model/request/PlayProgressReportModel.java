package com.android.video.model.request;

import com.google.gson.annotations.SerializedName;

/**
 * 播放进度上报请求数据模型
 * <p>
 * 用于向服务器上报用户的视频播放进度信息。
 * 包含短剧语言信息ID、章节ID、播放进度、是否完成播放和章节总时长等信息。
 * </p>
 * 
 * <p>
 * 请求格式：
 * <pre>
 * {
 *   "filmLanguageInfoId": "c655490792384ed887bfe20e56f30c47",
 *   "chapterId": "07eb21080ff708d27dcbffee8d6f633a",
 *   "progress": 150,
 *   "isFinished": 0,
 *   "duration": 3600
 * }
 * </pre>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * PlayProgressReportModel request = new PlayProgressReportModel(
 *     "c655490792384ed887bfe20e56f30c47",
 *     "07eb21080ff708d27dcbffee8d6f633a",
 *     150,
 *     0,
 *     3600
 * );
 * 
 * // 或者使用Builder模式
 * PlayProgressReportModel request = new PlayProgressReportModel.Builder()
 *     .filmLanguageInfoId("c655490792384ed887bfe20e56f30c47")
 *     .chapterId("07eb21080ff708d27dcbffee8d6f633a")
 *     .progress(150)
 *     .isFinished(false)
 *     .duration(3600)
 *     .build();
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class PlayProgressReportModel {

    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;

    /**
     * 章节ID
     */
    @SerializedName("chapterId")
    private String chapterId;

    /**
     * 播放进度（单位：秒）
     */
    @SerializedName("progress")
    private int progress;

    /**
     * 是否完成播放
     * 0=未完成, 1=完成
     */
    @SerializedName("isFinished")
    private int isFinished;

    /**
     * 当前章节总时长（单位：秒）
     */
    @SerializedName("duration")
    private int duration;

    /**
     * 默认构造函数
     */
    public PlayProgressReportModel() {
    }

    /**
     * 完整构造函数
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param chapterId 章节ID
     * @param progress 播放进度（秒）
     * @param isFinished 是否完成播放（0=未完成, 1=完成）
     * @param duration 章节总时长（秒）
     */
    public PlayProgressReportModel(String filmLanguageInfoId, String chapterId, 
                                  int progress, int isFinished, int duration) {
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.chapterId = chapterId;
        this.progress = progress;
        this.isFinished = isFinished;
        this.duration = duration;
    }

    /**
     * 便利构造函数 - 使用boolean类型的isFinished
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param chapterId 章节ID
     * @param progress 播放进度（秒）
     * @param isFinished 是否完成播放
     * @param duration 章节总时长（秒）
     */
    public PlayProgressReportModel(String filmLanguageInfoId, String chapterId, 
                                  int progress, boolean isFinished, int duration) {
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.chapterId = chapterId;
        this.progress = progress;
        this.isFinished = isFinished ? 1 : 0;
        this.duration = duration;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public int getProgress() {
        return progress;
    }

    public void setProgress(int progress) {
        this.progress = progress;
    }

    public int getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(int isFinished) {
        this.isFinished = isFinished;
    }

    /**
     * 设置是否完成播放（boolean版本）
     *
     * @param finished 是否完成播放
     */
    public void setFinished(boolean finished) {
        this.isFinished = finished ? 1 : 0;
    }

    public int getDuration() {
        return duration;
    }

    public void setDuration(int duration) {
        this.duration = duration;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否播放完成
     *
     * @return 如果播放完成返回true，否则返回false
     */
    public boolean isPlayFinished() {
        return isFinished == 1;
    }

    /**
     * 获取播放进度百分比
     *
     * @return 播放进度百分比（0.0-1.0）
     */
    public float getProgressPercentage() {
        if (duration <= 0) {
            return 0.0f;
        }
        return Math.min(1.0f, (float) progress / duration);
    }

    /**
     * 验证请求数据是否有效
     *
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return filmLanguageInfoId != null && !filmLanguageInfoId.trim().isEmpty()
                && chapterId != null && !chapterId.trim().isEmpty()
                && progress >= 0
                && duration > 0
                && (isFinished == 0 || isFinished == 1);
    }

    /**
     * 获取验证错误信息
     *
     * @return 如果数据无效，返回错误信息；否则返回null
     */
    public String getValidationError() {
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            return "filmLanguageInfoId cannot be null or empty";
        }
        if (chapterId == null || chapterId.trim().isEmpty()) {
            return "chapterId cannot be null or empty";
        }
        if (progress < 0) {
            return "progress cannot be negative";
        }
        if (duration <= 0) {
            return "duration must be positive";
        }
        if (isFinished != 0 && isFinished != 1) {
            return "isFinished must be 0 or 1";
        }
        return null;
    }

    @Override
    public String toString() {
        return "PlayProgressReportModel{" +
                "filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", chapterId='" + chapterId + '\'' +
                ", progress=" + progress +
                ", isFinished=" + isFinished +
                ", duration=" + duration +
                ", progressPercentage=" + String.format("%.1f%%", getProgressPercentage() * 100) +
                '}';
    }

    /**
     * Builder模式构建器
     */
    public static class Builder {
        private String filmLanguageInfoId;
        private String chapterId;
        private int progress;
        private int isFinished;
        private int duration;

        public Builder filmLanguageInfoId(String filmLanguageInfoId) {
            this.filmLanguageInfoId = filmLanguageInfoId;
            return this;
        }

        public Builder chapterId(String chapterId) {
            this.chapterId = chapterId;
            return this;
        }

        public Builder progress(int progress) {
            this.progress = progress;
            return this;
        }

        public Builder isFinished(int isFinished) {
            this.isFinished = isFinished;
            return this;
        }

        public Builder isFinished(boolean isFinished) {
            this.isFinished = isFinished ? 1 : 0;
            return this;
        }

        public Builder duration(int duration) {
            this.duration = duration;
            return this;
        }

        public PlayProgressReportModel build() {
            return new PlayProgressReportModel(filmLanguageInfoId, chapterId, progress, isFinished, duration);
        }
    }
}
