package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.DiscoverApiConstantsUtils;
import com.android.video.model.response.BaseResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.JsonObject;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 添加/取消喜欢API服务类
 * <p>
 * 提供添加或取消喜欢短剧的网络请求功能。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see DiscoverApiConstantsUtils
 */
public class AddLoveApiService {

    private static final String TAG = "AddLoveApiService";

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    // 单例实例
    private static volatile AddLoveApiService instance;

    /**
     * 添加/取消喜欢回调接口
     */
    public interface AddLoveCallback {
        /**
         * 操作成功
         * @param isLove 当前喜欢状态（0=取消, 1=加入）
         */
        void onSuccess(int isLove);

        /**
         * 操作失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造函数
     */
    private AddLoveApiService() {
        this.client = new OkHttpClient();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
        this.gson = new Gson();
    }

    /**
     * 获取单例实例
     * @return AddLoveApiService实例
     */
    public static AddLoveApiService getInstance() {
        if (instance == null) {
            synchronized (AddLoveApiService.class) {
                if (instance == null) {
                    instance = new AddLoveApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 添加喜欢
     * <p>
     * 将指定短剧添加到用户的喜欢列表中。
     * </p>
     *
     * @param filmId 短剧ID
     * @param callback 回调接口
     */
    public void addLove(String filmId, AddLoveCallback callback) {
        performLoveOperation(filmId, 1, callback);
    }

    /**
     * 取消喜欢
     * <p>
     * 将指定短剧从用户的喜欢列表中移除。
     * </p>
     *
     * @param filmId 短剧ID
     * @param callback 回调接口
     */
    public void removeLove(String filmId, AddLoveCallback callback) {
        performLoveOperation(filmId, 0, callback);
    }

    /**
     * 切换喜欢状态
     * <p>
     * 根据当前状态切换喜欢/取消喜欢。
     * </p>
     *
     * @param filmId 短剧ID
     * @param isLove 目标状态（0=取消, 1=加入）
     * @param callback 回调接口
     */
    public void toggleLove(String filmId, int isLove, AddLoveCallback callback) {
        performLoveOperation(filmId, isLove, callback);
    }

    /**
     * 执行喜欢操作
     * <p>
     * 统一的添加/取消喜欢操作实现。
     * </p>
     *
     * @param filmId 短剧ID
     * @param isLove 喜欢状态（0=取消, 1=加入）
     * @param callback 回调接口
     */
    private void performLoveOperation(String filmId, int isLove, AddLoveCallback callback) {
        // 参数验证
        if (filmId == null || filmId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("短剧ID不能为空");
            }
            return;
        }

        if (isLove != 0 && isLove != 1) {
            if (callback != null) {
                callback.onError("喜欢状态参数无效");
            }
            return;
        }

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + DiscoverApiConstantsUtils.API_ADD_LOVE;

        // 构建请求体
        JsonObject requestJson = new JsonObject();
        requestJson.addProperty(DiscoverApiConstantsUtils.PARAM_FILM_ID, filmId.trim());
        requestJson.addProperty(DiscoverApiConstantsUtils.PARAM_IS_LOVE, isLove);

        String requestBodyString = gson.toJson(requestJson);

        // 构建POST请求（不手动添加token，由HeaderInterceptor统一处理）
        RequestBody requestBody = RequestBody.create(
            MediaType.parse("application/json; charset=utf-8"),
            requestBodyString
        );

        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 添加/取消喜欢API请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "请求体: " + requestBodyString);
        Log.d(TAG, "==================");

        // 异步执行网络请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "添加/取消喜欢API请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 添加/取消喜欢API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseAddLoveResponse(responseBody, isLove, callback);
                        } else {
                            // 提供更详细的错误信息
                            String errorMessage = "服务器错误: " + response.code();
                            if (response.code() == 404) {
                                errorMessage += " (接口不存在)";
                            } else if (response.code() == 400) {
                                errorMessage += " (请求参数错误)";
                            } else if (response.code() == 401) {
                                errorMessage += " (认证失败，请重新登录)";
                            }

                            final String finalErrorMessage = errorMessage;
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError(finalErrorMessage);
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "添加/取消喜欢响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析添加/取消喜欢API响应
     *
     * @param responseBody 响应体
     * @param isLove 请求的喜欢状态
     * @param callback 回调接口
     */
    private void parseAddLoveResponse(String responseBody, int isLove, AddLoveCallback callback) {
        try {
            Log.d(TAG, "开始解析添加/取消喜欢响应数据");

            // 解析基础响应结构
            BaseResponseModel response = gson.fromJson(responseBody, BaseResponseModel.class);

            if (response != null && BaseApiConstantsUtils.isSuccessCode(response.getCode())) {
                Log.d(TAG, "添加/取消喜欢操作成功");

                // 回调成功结果
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(isLove);
                    }
                });
            } else {
                String message = response != null ? response.getMessage() : "未知错误";
                Log.e(TAG, "添加/取消喜欢API返回错误: " + message);

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("操作失败: " + message);
                    }
                });
            }

        } catch (Exception e) {
            Log.e(TAG, "添加/取消喜欢响应解析异常", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析异常: " + e.getMessage());
                }
            });
        }
    }
}
