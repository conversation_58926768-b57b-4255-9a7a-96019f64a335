<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black"
    android:clipChildren="false"
    android:clipToPadding="false"
    tools:context=".ui.activity.MainActivity">

    <!-- Fragment容器 -->
    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_navigation"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 底部导航栏 -->
    <com.google.android.material.bottomnavigation.BottomNavigationView
        android:id="@+id/bottom_navigation"
        style="@style/CustomBottomNavigationView"
        android:layout_width="0dp"
        android:layout_height="@dimen/bottom_nav_height"
        android:paddingTop="@dimen/layout_margin_tiny"
        android:paddingBottom="@dimen/layout_margin_tiny"
        app:itemIconTint="@color/bottom_nav_color_selector"
        app:itemTextColor="@color/bottom_nav_color_selector"
        app:itemTextAppearanceActive="@style/BottomNavigationTextStyle"
        app:itemTextAppearanceInactive="@style/BottomNavigationTextStyle"
        app:itemIconSize="@dimen/bottom_nav_icon_size"
        app:itemRippleColor="@android:color/transparent"
        app:labelVisibilityMode="labeled"
        app:menu="@menu/bottom_navigation_menu"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
