package com.android.video.utils;

import android.content.Context;
import android.content.Intent;
import android.widget.Toast;
import com.android.video.ui.activity.LoginActivity;

/**
 * 登录检查工具类
 * 用于统一处理需要登录才能执行的操作
 * 
 * <AUTHOR> Team
 */
public class LoginRequiredUtils {

    /**
     * 检查用户是否已登录，如果未登录则显示登录页面
     * 
     * @param context 上下文
     * @return true表示已登录，false表示未登录（已跳转到登录页面）
     */
    public static boolean checkLoginAndNavigate(Context context) {
        return checkLoginAndNavigate(context, null);
    }

    /**
     * 检查用户是否已登录，如果未登录则显示登录页面
     * 
     * @param context 上下文
     * @param message 未登录时显示的提示消息，为null时使用默认消息
     * @return true表示已登录，false表示未登录（已跳转到登录页面）
     */
    public static boolean checkLoginAndNavigate(Context context, String message) {
        if (context == null) {
            return false;
        }

        // 检查用户是否已登录
        if (UserSessionUtils.isUserLoggedIn(context)) {
            return true;
        }

        // 用户未登录，显示提示消息
        String toastMessage = message != null ? message : "Please login first";
        Toast.makeText(context, toastMessage, Toast.LENGTH_SHORT).show();

        // 跳转到登录页面
        Intent intent = new Intent(context, LoginActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
        context.startActivity(intent);

        return false;
    }

    /**
     * 检查用户是否已登录，如果未登录则只显示提示消息，不跳转
     * 
     * @param context 上下文
     * @return true表示已登录，false表示未登录
     */
    public static boolean checkLoginWithToast(Context context) {
        return checkLoginWithToast(context, null);
    }

    /**
     * 检查用户是否已登录，如果未登录则只显示提示消息，不跳转
     * 
     * @param context 上下文
     * @param message 未登录时显示的提示消息，为null时使用默认消息
     * @return true表示已登录，false表示未登录
     */
    public static boolean checkLoginWithToast(Context context, String message) {
        if (context == null) {
            return false;
        }

        // 检查用户是否已登录
        if (UserSessionUtils.isUserLoggedIn(context)) {
            return true;
        }

        // 用户未登录，显示提示消息
        String toastMessage = message != null ? message : "Please login first";
        Toast.makeText(context, toastMessage, Toast.LENGTH_SHORT).show();

        return false;
    }

    /**
     * 静默检查用户是否已登录（不显示任何提示）
     * 
     * @param context 上下文
     * @return true表示已登录，false表示未登录
     */
    public static boolean isLoggedIn(Context context) {
        if (context == null) {
            return false;
        }
        return UserSessionUtils.isUserLoggedIn(context);
    }
}
