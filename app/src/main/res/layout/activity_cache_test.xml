<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:padding="16dp"
    android:background="@android:color/black">

    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="缓存功能测试"
        android:textSize="24sp"
        android:textStyle="bold"
        android:textColor="@android:color/white"
        android:gravity="center"
        android:layout_marginBottom="24dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_run_tests"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="运行测试"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_blue_dark"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_clear_cache"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="清除缓存"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_red_dark"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_show_stats"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="显示统计"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_green_dark"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_check_consistency"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="检查一致性"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_orange_dark"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_marginBottom="16dp">

        <Button
            android:id="@+id/btn_test_featured_cache"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="Featured缓存"
            android:textColor="@android:color/white"
            android:background="@android:color/holo_purple"
            android:layout_marginEnd="8dp" />

        <Button
            android:id="@+id/btn_test_pagination_cache"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="分页缓存"
            android:textColor="@android:color/white"
            android:background="@android:color/darker_gray"
            android:layout_marginStart="8dp" />

    </LinearLayout>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:background="@android:color/darker_gray"
        android:padding="12dp">

        <TextView
            android:id="@+id/tv_test_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="点击按钮开始测试缓存功能"
            android:textSize="14sp"
            android:textColor="@android:color/white"
            android:fontFamily="monospace"
            android:lineSpacingExtra="2dp" />

    </ScrollView>

</LinearLayout>
