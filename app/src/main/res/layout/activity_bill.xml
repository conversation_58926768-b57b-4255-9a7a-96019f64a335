<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <!-- 顶部标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/btn_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- Bill标题 -->
            <TextView
                android:layout_width="@dimen/bill_title_width"
                android:layout_height="@dimen/bill_title_height"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Bill"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 标签页容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/bill_tabs_margin_top"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            android:paddingStart="@dimen/bill_tabs_margin_horizontal"
            android:paddingEnd="@dimen/bill_tabs_margin_horizontal">

            <!-- 标签页背景 -->
            <FrameLayout
                android:layout_width="@dimen/bill_tabs_background_width"
                android:layout_height="@dimen/bill_tabs_background_height"
                android:background="@drawable/bill_tabs_background">

                <!-- 选中状态背景 -->
                <View
                    android:id="@+id/selected_tab_background"
                    android:layout_width="@dimen/bill_selected_tab_width"
                    android:layout_height="@dimen/bill_selected_tab_height"
                    android:layout_marginStart="@dimen/bill_selected_tab_margin_start"
                    android:layout_marginTop="@dimen/bill_selected_tab_margin_top"
                    android:background="@drawable/bill_selected_tab_bg" />

                <!-- 标签页文字容器 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <!-- VIP Record -->
                    <TextView
                        android:id="@+id/tab_vip_record"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="VIP Record"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/bill_tab_text_size"
                        android:fontFamily="sans-serif"
                        android:gravity="center"
                        android:clickable="true"
                        android:focusable="true" />

                    <!-- Points purchase -->
                    <TextView
                        android:id="@+id/tab_points_purchase"
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:text="Points purchase"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/bill_tab_text_size"
                        android:fontFamily="sans-serif"
                        android:gravity="center"
                        android:clickable="true"
                        android:focusable="true" />

                    <!-- Video Record -->
                    <TextView
                            android:id="@+id/tab_video_record"
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:text="Video Record"
                            android:textColor="#ffffffff"
                            android:textSize="@dimen/bill_tab_text_size"
                            android:fontFamily="sans-serif"
                            android:gravity="center"
                            android:clickable="true"
                            android:focusable="true"/>

                </LinearLayout>

            </FrameLayout>

        </LinearLayout>

        <!-- 内容区域 -->
        <FrameLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/bill_content_margin_top">

            <!-- 加载状态显示 -->
            <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                android:id="@+id/swipe_refresh_loading"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:visibility="gone">

                <View
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:background="@android:color/transparent" />

            </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            <!-- 空状态显示 -->
            <LinearLayout
                android:id="@+id/empty_state_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_gravity="center_horizontal|bottom"
                android:layout_marginStart="@dimen/bill_empty_image_margin_horizontal"
                android:layout_marginEnd="@dimen/bill_empty_image_margin_horizontal"
                android:layout_marginBottom="@dimen/bill_empty_image_margin_bottom"
                android:visibility="gone">

                <!-- 空状态图片 -->
                <ImageView
                    android:layout_width="@dimen/bill_empty_image_width"
                    android:layout_height="@dimen/bill_empty_image_height"
                    android:layout_gravity="center_horizontal"
                    android:src="@drawable/empty"
                    android:scaleType="centerInside" />

                <!-- 空状态文字 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/bill_empty_text_margin_top"
                    android:layout_gravity="center_horizontal"
                    android:text="There are no relevant records yet..."
                    android:textColor="#80ffffff"
                    android:textSize="@dimen/bill_empty_text_size"
                    android:fontFamily="sans-serif"
                    android:gravity="center" />

            </LinearLayout>

            <!-- VIP Record 内容 -->
            <LinearLayout
                android:id="@+id/vip_record_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- VIP记录下拉刷新容器 -->
                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_vip_record"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- VIP记录列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_vip_records"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/vip_record_first_card_margin_top"
                        android:paddingStart="@dimen/bill_header_padding_horizontal"
                        android:paddingEnd="@dimen/bill_header_padding_horizontal"
                        android:clipToPadding="false" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            </LinearLayout>

            <!-- Points purchase 内容 -->
            <LinearLayout
                android:id="@+id/points_purchase_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Points purchase下拉刷新容器 -->
                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_points_purchase"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- Points purchase记录列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_points_purchase"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/points_purchase_first_card_margin_top"
                        android:paddingStart="@dimen/bill_header_padding_horizontal"
                        android:paddingEnd="@dimen/bill_header_padding_horizontal"
                        android:clipToPadding="false" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            </LinearLayout>

            <!-- Video Record 内容 -->
            <LinearLayout
                android:id="@+id/video_record_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <!-- Video Record下拉刷新容器 -->
                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_video_record"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                    <!-- Video Record记录列表 -->
                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/rv_video_record"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:layout_marginTop="@dimen/video_record_first_card_margin_top"
                        android:paddingStart="@dimen/bill_header_padding_horizontal"
                        android:paddingEnd="@dimen/bill_header_padding_horizontal"
                        android:clipToPadding="false" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

            </LinearLayout>

        </FrameLayout>

    </LinearLayout>

</ScrollView>

</FrameLayout>
