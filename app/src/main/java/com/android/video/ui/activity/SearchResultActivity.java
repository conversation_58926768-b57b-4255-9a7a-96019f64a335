package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.KeyEvent;
import android.view.View;
import android.view.inputmethod.EditorInfo;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.model.MyListVideo;
import com.android.video.model.SearchResultModel;
import com.android.video.network.HomeApiService;
import com.android.video.ui.adapter.SearchResultVideoAdapter;
import com.android.video.ui.activity.VideoDetailActivity;
import java.util.ArrayList;
import java.util.List;

/**
 * 搜索结果页面Activity
 * 根据搜索关键词显示相应的搜索结果
 */
public class SearchResultActivity extends AppCompatActivity implements SearchResultVideoAdapter.OnVideoClickListener {

    private static final String TAG = "SearchResultActivity";
    public static final String EXTRA_SEARCH_TERM = "search_term";

    // UI组件
    private ImageView ivBack;
    private EditText etSearch;
    private ImageView ivClear;
    private TextView tvResultCount;
    private RecyclerView rvSearchResults;
    private View layoutEmptyState;
    private SwipeRefreshLayout swipeRefreshLoading;

    // 适配器和数据
    private SearchResultVideoAdapter searchResultAdapter;
    private List<MyListVideo> searchResults;
    private String searchTerm;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_search_result);

        // 获取搜索关键词
        searchTerm = getIntent().getStringExtra(EXTRA_SEARCH_TERM);
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            finish();
            return;
        }

        initViews();
        initData();
        setupRecyclerView();
        setupClickListeners();
        performSearch();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        etSearch = findViewById(R.id.et_search);
        ivClear = findViewById(R.id.iv_clear);
        tvResultCount = findViewById(R.id.tv_result_count);
        rvSearchResults = findViewById(R.id.rv_search_results);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);

        // 设置搜索关键词显示
        etSearch.setText(searchTerm);
        etSearch.setSelection(searchTerm.length());

        // 显示清除按钮
        updateClearButtonVisibility();
    }

    /**
     * 初始化数据
     */
    private void initData() {
        searchResults = new ArrayList<>();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        rvSearchResults.setLayoutManager(new LinearLayoutManager(this));
        searchResultAdapter = new SearchResultVideoAdapter(searchResults, this);
        rvSearchResults.setAdapter(searchResultAdapter);
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // 清除按钮
        ivClear.setOnClickListener(v -> {
            etSearch.setText("");
            etSearch.requestFocus();
        });

        // 搜索框文本变化监听
        etSearch.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateClearButtonVisibility();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        // 搜索框回车监听
        etSearch.setOnEditorActionListener(new TextView.OnEditorActionListener() {
            @Override
            public boolean onEditorAction(TextView v, int actionId, KeyEvent event) {
                if (actionId == EditorInfo.IME_ACTION_SEARCH ||
                    (event != null && event.getKeyCode() == KeyEvent.KEYCODE_ENTER)) {
                    String newSearchTerm = etSearch.getText().toString().trim();
                    if (!newSearchTerm.isEmpty()) {
                        searchTerm = newSearchTerm;
                        performSearch();
                    }
                    return true;
                }
                return false;
            }
        });
    }

    /**
     * 执行搜索
     */
    private void performSearch() {
        // 清空之前的结果
        searchResults.clear();
        searchResultAdapter.notifyDataSetChanged();

        // 显示加载状态
        showLoadingState();

        // 调用搜索API
        HomeApiService.getInstance().searchVideos(searchTerm, 1, 10, new HomeApiService.SearchCallback() {
            @Override
            public void onSuccess(List<SearchResultModel> apiResults, int total, int pages) {
                runOnUiThread(() -> {
                    // 将API搜索结果转换为MyListVideo对象
                    searchResults.clear();
                    for (SearchResultModel apiResult : apiResults) {
                        MyListVideo video = convertToMyListVideo(apiResult);
                        searchResults.add(video);
                    }

                    // 如果有结果，显示搜索结果；否则显示空状态
                    if (searchResults.isEmpty()) {
                        showEmptyState();
                    } else {
                        showSearchResults();
                    }

                    // 更新UI
                    searchResultAdapter.notifyDataSetChanged();
                    updateResultCount();

                    Log.d(TAG, "搜索成功: " + searchResults.size() + " 条结果");
                });
            }

            @Override
            public void onError(String errorMessage) {
                runOnUiThread(() -> {
                    Log.e(TAG, "搜索失败: " + errorMessage);

                    // 如果是"History"关键词且API失败，回退到本地数据
                    if ("History".equalsIgnoreCase(searchTerm.trim())) {
                        loadHistoryContent();
                        if (searchResults.isEmpty()) {
                            showEmptyState();
                        } else {
                            showSearchResults();
                        }
                        searchResultAdapter.notifyDataSetChanged();
                        updateResultCount();
                    } else {
                        // 显示空状态
                        showEmptyState();
                        Toast.makeText(SearchResultActivity.this, "搜索失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                    }
                });
            }
        });
    }

    /**
     * 加载History内容
     */
    private void loadHistoryContent() {
        // 移除测试数据，History内容应该从真实的观看历史API获取
        // 如果需要显示History内容，应该调用MyHistoryListApiService
        Log.d(TAG, "History内容应该从真实API获取，不使用测试数据");
        // searchResults保持为空，或者调用真实的API加载数据

        MyListVideo dramaSeries = new MyListVideo("6", "Drama Series", "", true, "history");
        dramaSeries.setDescription("a captivating historical drama set in a grand palace, where love and power collide. The story follows complex characters...");
        dramaSeries.setCurrentEpisode(72);
        dramaSeries.setTotalEpisodes(80);
        searchResults.add(dramaSeries);

        // 显示搜索结果
        showSearchResults();
    }

    /**
     * 显示搜索结果
     */
    private void showSearchResults() {
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
        layoutEmptyState.setVisibility(View.GONE);
        rvSearchResults.setVisibility(View.VISIBLE);
        tvResultCount.setVisibility(View.VISIBLE);
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        rvSearchResults.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.GONE);
        swipeRefreshLoading.setVisibility(View.VISIBLE);
        swipeRefreshLoading.setRefreshing(true);
        tvResultCount.setVisibility(View.GONE);
    }


    /**
     * 显示空状态
     */
    private void showEmptyState() {
        swipeRefreshLoading.setVisibility(View.GONE);
        swipeRefreshLoading.setRefreshing(false);
        rvSearchResults.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.VISIBLE);
        tvResultCount.setVisibility(View.GONE);
    }

    /**
     * 将SearchResultModel转换为MyListVideo
     * @param searchResult API搜索结果
     * @return MyListVideo对象
     */
    private MyListVideo convertToMyListVideo(SearchResultModel searchResult) {
        MyListVideo video = new MyListVideo();

        // 设置基本信息
        video.setId(searchResult.getFilmId() != null ? searchResult.getFilmId() : "");
        video.setTitle(searchResult.getTitle() != null ? searchResult.getTitle() : "");
        video.setPosterUrl(searchResult.getPosterUrl() != null ? searchResult.getPosterUrl() : "");
        video.setCategory("search"); // 标记为搜索结果
        video.setLiked(false); // 默认未收藏

        // 设置filmLanguageInfoId用于跳转详情页
        video.setFilmLanguageInfoId(searchResult.getFilmLanguageInfoId());

        // 设置描述信息
        String description = "";
        if (searchResult.getCategoryName() != null && !searchResult.getCategoryName().isEmpty()) {
            description = "分类: " + searchResult.getCategoryName();
        }

        // 根据语言类型添加语言信息
        if (searchResult.getLanguageType() == 1) {
            description += description.isEmpty() ? "语言: 英语" : " | 语言: 英语";
        } else if (searchResult.getLanguageType() == 2) {
            description += description.isEmpty() ? "语言: 俄语" : " | 语言: 俄语";
        }

        video.setDescription(description.isEmpty() ? "暂无描述" : description);

        // 设置默认集数信息
        video.setCurrentEpisode(1);
        video.setTotalEpisodes(1);

        return video;
    }

    /**
     * 更新结果数量显示
     */
    private void updateResultCount() {
        int count = searchResults.size();
        if (count > 0) {
            tvResultCount.setText(count + " related contents");
            tvResultCount.setVisibility(View.VISIBLE);
        } else {
            tvResultCount.setVisibility(View.GONE);
        }
    }

    /**
     * 更新清除按钮可见性
     */
    private void updateClearButtonVisibility() {
        if (etSearch.getText().toString().trim().isEmpty()) {
            ivClear.setVisibility(View.GONE);
        } else {
            ivClear.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onVideoClick(MyListVideo video) {
        if (video == null) {
            return;
        }

        // 跳转到视频详情页
        navigateToVideoDetail(video);
    }

    @Override
    public void onPlayClick(MyListVideo video) {
        if (video == null) {
            return;
        }

        // 跳转到视频详情页（播放按钮和视频点击都跳转到详情页）
        navigateToVideoDetail(video);
    }

    /**
     * 跳转到视频详情页
     * @param video 视频数据
     */
    private void navigateToVideoDetail(MyListVideo video) {
        // 检查是否有filmLanguageInfoId，如果有则使用新接口
        if (video.getFilmLanguageInfoId() != null && !video.getFilmLanguageInfoId().isEmpty()) {
            Intent intent = new Intent(this, VideoDetailActivity.class);
            intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
            intent.putExtra("filmTitle", video.getTitle());
            startActivity(intent);

            Log.d(TAG, "搜索结果跳转到详情页: " + video.getTitle() +
                  ", filmLanguageInfoId: " + video.getFilmLanguageInfoId());
        } else {
            // 如果没有filmLanguageInfoId，显示提示信息
            Toast.makeText(this, "无法获取视频详情信息", Toast.LENGTH_SHORT).show();
            Log.w(TAG, "搜索结果缺少filmLanguageInfoId: " + video.getTitle());
        }
    }

    @Override
    public void onLikeClick(MyListVideo video) {
        if (video == null) {
            return;
        }

        // 切换喜欢状态
        video.setLiked(!video.isLiked());
        searchResultAdapter.notifyDataSetChanged();

        String message = video.isLiked() ? "Added to favorites" : "Removed from favorites";
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }
}
