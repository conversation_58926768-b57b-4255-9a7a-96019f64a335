package com.android.video.model.request;

import java.io.Serializable;

/**
 * 确认下载请求数据模型
 * <p>
 * 用于向服务器确认下载完成的请求参数封装。
 * 包含下载记录ID、文件校验和以及本地存储路径等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class ConfirmDownloadRequestModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 下载记录ID
     * <p>
     * 服务器生成的唯一下载记录标识符，用于关联具体的下载任务。
     * </p>
     */
    private String downloadRecordId;

    /**
     * 文件校验和
     * <p>
     * 下载文件的校验和值，用于验证文件完整性。
     * 通常为MD5、SHA1或其他哈希算法生成的字符串。
     * </p>
     */
    private String fileChecksum;

    /**
     * 客户端存储路径
     * <p>
     * 文件在客户端设备上的完整存储路径。
     * 例如：/storage/emulated/0/AppName/videos/101.mp4
     * </p>
     */
    private String localPath;

    /**
     * 默认构造函数
     */
    public ConfirmDownloadRequestModel() {
    }

    /**
     * 完整构造函数
     *
     * @param downloadRecordId 下载记录ID
     * @param fileChecksum 文件校验和
     * @param localPath 本地存储路径
     */
    public ConfirmDownloadRequestModel(String downloadRecordId, String fileChecksum, String localPath) {
        this.downloadRecordId = downloadRecordId;
        this.fileChecksum = fileChecksum;
        this.localPath = localPath;
    }

    // ========== Getter和Setter方法 ==========

    public String getDownloadRecordId() {
        return downloadRecordId;
    }

    public void setDownloadRecordId(String downloadRecordId) {
        this.downloadRecordId = downloadRecordId;
    }

    public String getFileChecksum() {
        return fileChecksum;
    }

    public void setFileChecksum(String fileChecksum) {
        this.fileChecksum = fileChecksum;
    }

    public String getLocalPath() {
        return localPath;
    }

    public void setLocalPath(String localPath) {
        this.localPath = localPath;
    }

    // ========== 验证方法 ==========

    /**
     * 验证请求数据的有效性
     *
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return isValidDownloadRecordId() && isValidFileChecksum() && isValidLocalPath();
    }

    /**
     * 获取验证错误信息
     *
     * @return 验证错误的详细描述，如果数据有效则返回null
     */
    public String getValidationError() {
        if (!isValidDownloadRecordId()) {
            return "下载记录ID不能为空";
        }
        if (!isValidFileChecksum()) {
            return "文件校验和不能为空";
        }
        if (!isValidLocalPath()) {
            return "本地存储路径不能为空";
        }
        return null;
    }

    /**
     * 验证下载记录ID是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidDownloadRecordId() {
        return downloadRecordId != null && !downloadRecordId.trim().isEmpty();
    }

    /**
     * 验证文件校验和是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidFileChecksum() {
        return fileChecksum != null && !fileChecksum.trim().isEmpty();
    }

    /**
     * 验证本地路径是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidLocalPath() {
        return localPath != null && !localPath.trim().isEmpty();
    }

    // ========== 工具方法 ==========

    /**
     * 转换为字符串表示
     *
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "ConfirmDownloadRequestModel{" +
                "downloadRecordId='" + downloadRecordId + '\'' +
                ", fileChecksum='" + fileChecksum + '\'' +
                ", localPath='" + localPath + '\'' +
                '}';
    }

    /**
     * 比较两个对象是否相等
     *
     * @param obj 要比较的对象
     * @return 如果相等返回true，否则返回false
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        ConfirmDownloadRequestModel that = (ConfirmDownloadRequestModel) obj;

        if (downloadRecordId != null ? !downloadRecordId.equals(that.downloadRecordId) : that.downloadRecordId != null)
            return false;
        if (fileChecksum != null ? !fileChecksum.equals(that.fileChecksum) : that.fileChecksum != null)
            return false;
        return localPath != null ? localPath.equals(that.localPath) : that.localPath == null;
    }

    /**
     * 获取对象的哈希码
     *
     * @return 哈希码值
     */
    @Override
    public int hashCode() {
        int result = downloadRecordId != null ? downloadRecordId.hashCode() : 0;
        result = 31 * result + (fileChecksum != null ? fileChecksum.hashCode() : 0);
        result = 31 * result + (localPath != null ? localPath.hashCode() : 0);
        return result;
    }
}
