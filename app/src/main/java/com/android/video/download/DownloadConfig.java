package com.android.video.download;

import android.content.Context;
import android.os.Environment;
import java.io.File;

/**
 * 下载配置类
 * <p>
 * 管理下载相关的配置参数，包括存储路径、并发数量、超时时间等。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadConfig {

    /**
     * 默认下载目录名称
     */
    public static final String DEFAULT_DOWNLOAD_DIR = "VideoDownloads";

    /**
     * 默认最大并发下载数
     */
    public static final int DEFAULT_MAX_CONCURRENT_DOWNLOADS = 3;

    /**
     * 默认连接超时时间（毫秒）
     */
    public static final int DEFAULT_CONNECT_TIMEOUT = 30000;

    /**
     * 默认读取超时时间（毫秒）
     */
    public static final int DEFAULT_READ_TIMEOUT = 60000;

    /**
     * 默认写入超时时间（毫秒）
     */
    public static final int DEFAULT_WRITE_TIMEOUT = 60000;

    /**
     * 默认进度更新间隔（毫秒）
     */
    public static final int DEFAULT_PROGRESS_INTERVAL = 1000;

    /**
     * 默认重试次数
     */
    public static final int DEFAULT_RETRY_COUNT = 3;

    /**
     * 默认重试间隔（毫秒）
     */
    public static final int DEFAULT_RETRY_DELAY = 2000;

    /**
     * 支持的视频文件扩展名
     */
    public static final String[] SUPPORTED_VIDEO_EXTENSIONS = {
        ".mp4", ".avi", ".mkv", ".mov", ".wmv", ".flv", ".webm", ".m4v"
    };

    private final Context context;
    private String downloadDirectory;
    private int maxConcurrentDownloads;
    private int connectTimeout;
    private int readTimeout;
    private int writeTimeout;
    private int progressUpdateInterval;
    private int retryCount;
    private int retryDelay;
    private boolean enableNotification;
    private boolean enableWifiOnly;
    private boolean enableAutoResume;

    /**
     * 构造函数
     *
     * @param context 应用上下文
     */
    public DownloadConfig(Context context) {
        this.context = context.getApplicationContext();
        initDefaultConfig();
    }

    /**
     * 初始化默认配置
     */
    private void initDefaultConfig() {
        this.downloadDirectory = getDefaultDownloadDirectory();
        this.maxConcurrentDownloads = DEFAULT_MAX_CONCURRENT_DOWNLOADS;
        this.connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        this.readTimeout = DEFAULT_READ_TIMEOUT;
        this.writeTimeout = DEFAULT_WRITE_TIMEOUT;
        this.progressUpdateInterval = DEFAULT_PROGRESS_INTERVAL;
        this.retryCount = DEFAULT_RETRY_COUNT;
        this.retryDelay = DEFAULT_RETRY_DELAY;
        this.enableNotification = true;
        this.enableWifiOnly = false;
        this.enableAutoResume = true;
    }

    /**
     * 获取默认下载目录
     *
     * @return 默认下载目录路径
     */
    private String getDefaultDownloadDirectory() {
        File externalDir = context.getExternalFilesDir(Environment.DIRECTORY_MOVIES);
        if (externalDir != null) {
            File downloadDir = new File(externalDir, DEFAULT_DOWNLOAD_DIR);
            return downloadDir.getAbsolutePath();
        } else {
            // 如果外部存储不可用，使用内部存储
            File internalDir = new File(context.getFilesDir(), DEFAULT_DOWNLOAD_DIR);
            return internalDir.getAbsolutePath();
        }
    }

    /**
     * 确保下载目录存在
     *
     * @return 如果目录创建成功或已存在返回true，否则返回false
     */
    public boolean ensureDownloadDirectoryExists() {
        File dir = new File(downloadDirectory);
        if (!dir.exists()) {
            return dir.mkdirs();
        }
        return true;
    }

    /**
     * 获取视频文件的完整路径
     *
     * @param fileName 文件名
     * @return 完整的文件路径
     */
    public String getVideoFilePath(String fileName) {
        return new File(downloadDirectory, fileName).getAbsolutePath();
    }

    /**
     * 检查文件扩展名是否支持
     *
     * @param fileName 文件名
     * @return 如果支持返回true，否则返回false
     */
    public boolean isSupportedVideoFile(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            return false;
        }
        
        String lowerFileName = fileName.toLowerCase();
        for (String extension : SUPPORTED_VIDEO_EXTENSIONS) {
            if (lowerFileName.endsWith(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 生成安全的文件名
     *
     * @param originalName 原始文件名
     * @return 安全的文件名
     */
    public String generateSafeFileName(String originalName) {
        if (originalName == null || originalName.trim().isEmpty()) {
            return "video_" + System.currentTimeMillis() + ".mp4";
        }
        
        // 移除不安全的字符
        String safeName = originalName.replaceAll("[^a-zA-Z0-9._-]", "_");
        
        // 确保有扩展名
        if (!isSupportedVideoFile(safeName)) {
            safeName += ".mp4";
        }
        
        return safeName;
    }

    // ========== Getter和Setter方法 ==========

    public Context getContext() {
        return context;
    }

    public String getDownloadDirectory() {
        return downloadDirectory;
    }

    public DownloadConfig setDownloadDirectory(String downloadDirectory) {
        this.downloadDirectory = downloadDirectory;
        return this;
    }

    public int getMaxConcurrentDownloads() {
        return maxConcurrentDownloads;
    }

    public DownloadConfig setMaxConcurrentDownloads(int maxConcurrentDownloads) {
        this.maxConcurrentDownloads = Math.max(1, Math.min(maxConcurrentDownloads, 10));
        return this;
    }

    public int getConnectTimeout() {
        return connectTimeout;
    }

    public DownloadConfig setConnectTimeout(int connectTimeout) {
        this.connectTimeout = Math.max(5000, connectTimeout);
        return this;
    }

    public int getReadTimeout() {
        return readTimeout;
    }

    public DownloadConfig setReadTimeout(int readTimeout) {
        this.readTimeout = Math.max(10000, readTimeout);
        return this;
    }

    public int getWriteTimeout() {
        return writeTimeout;
    }

    public DownloadConfig setWriteTimeout(int writeTimeout) {
        this.writeTimeout = Math.max(10000, writeTimeout);
        return this;
    }

    public int getProgressUpdateInterval() {
        return progressUpdateInterval;
    }

    public DownloadConfig setProgressUpdateInterval(int progressUpdateInterval) {
        this.progressUpdateInterval = Math.max(500, progressUpdateInterval);
        return this;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public DownloadConfig setRetryCount(int retryCount) {
        this.retryCount = Math.max(0, Math.min(retryCount, 10));
        return this;
    }

    public int getRetryDelay() {
        return retryDelay;
    }

    public DownloadConfig setRetryDelay(int retryDelay) {
        this.retryDelay = Math.max(1000, retryDelay);
        return this;
    }

    public boolean isEnableNotification() {
        return enableNotification;
    }

    public DownloadConfig setEnableNotification(boolean enableNotification) {
        this.enableNotification = enableNotification;
        return this;
    }

    public boolean isEnableWifiOnly() {
        return enableWifiOnly;
    }

    public DownloadConfig setEnableWifiOnly(boolean enableWifiOnly) {
        this.enableWifiOnly = enableWifiOnly;
        return this;
    }

    public boolean isEnableAutoResume() {
        return enableAutoResume;
    }

    public DownloadConfig setEnableAutoResume(boolean enableAutoResume) {
        this.enableAutoResume = enableAutoResume;
        return this;
    }
}
