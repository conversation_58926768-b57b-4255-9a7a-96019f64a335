package com.android.video.adapter;

import android.content.Context;
import android.util.Log;
import android.util.SparseArray;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.android.video.model.VideoModel;
import com.android.video.player.VideoPlayerManager;
import com.android.video.ui.fragment.VideoPlayerFragment;

import java.util.List;

/**
 * 视频播放ViewPager2适配器
 * 用于实现上下滑动切换视频功能，支持Fragment生命周期管理和内存优化
 * 每个Fragment使用独立的VideoPlayerManager实例以避免状态冲突
 * <AUTHOR> Team
 */
public class VideoPlayerPagerAdapter extends FragmentStateAdapter {

    private static final String TAG = "VideoPlayerPagerAdapter";

    private List<VideoModel> videoList;
    private Context context;
    private SparseArray<VideoPlayerFragment> fragmentCache;
    private int currentPosition = -1;

    public VideoPlayerPagerAdapter(@NonNull FragmentActivity fragmentActivity,
                                 List<VideoModel> videoList) {
        super(fragmentActivity);
        this.context = fragmentActivity;
        this.videoList = videoList;
        this.fragmentCache = new SparseArray<>();

        Log.d(TAG, "Adapter created with " + (videoList != null ? videoList.size() : 0) + " videos");
        Log.d(TAG, "Using FragmentStateAdapter with ViewPager2 for lifecycle management");

        // 初始化当前位置为0，这样第一个Fragment会被标记为当前Fragment
        if (videoList != null && !videoList.isEmpty()) {
            currentPosition = 0;
        }
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        Log.d(TAG, "Creating fragment for position: " + position);

        if (position < 0 || position >= getItemCount()) {
            Log.w(TAG, "Invalid position: " + position);
            return new Fragment(); // 返回空Fragment作为fallback
        }

        VideoModel video = videoList.get(position);
        VideoPlayerFragment fragment = VideoPlayerFragment.newInstance(video, position);
        // 不再设置共享的PlayerManager，让Fragment自己创建

        // 缓存Fragment引用
        fragmentCache.put(position, fragment);

        // 使用BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT后，不需要手动管理播放状态
        // Fragment的onResume/onPause会自动处理播放控制

        Log.d(TAG, "Fragment created for video: " + video.getTitle() + " at position: " + position);
        return fragment;
    }

    @Override
    public int getItemCount() {
        return videoList != null ? videoList.size() : 0;
    }

    /**
     * 获取指定位置的Fragment
     * @param position 位置
     * @return VideoPlayerFragment实例，如果不存在返回null
     */
    public VideoPlayerFragment getFragment(int position) {
        return fragmentCache.get(position);
    }

    /**
     * 更新当前播放位置
     * @param position 当前位置
     */
    public void setCurrentPosition(int position) {
        if (currentPosition != position) {
            Log.d(TAG, "Position changed from " + currentPosition + " to " + position);
            int oldPosition = currentPosition;
            currentPosition = position;

            // 使用BEHAVIOR_RESUME_ONLY_CURRENT_FRAGMENT后，ViewPager2会自动管理Fragment的生命周期
            // 不需要手动调用pausePlayback/resumePlayback
            Log.d(TAG, "Fragment lifecycle will be managed automatically by ViewPager2");
        }
    }

    /**
     * 获取当前播放位置
     * @return 当前位置
     */
    public int getCurrentPosition() {
        return currentPosition;
    }

    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList;
        fragmentCache.clear(); // 清空缓存
        notifyDataSetChanged();
        Log.d(TAG, "Video list updated with " + (newVideoList != null ? newVideoList.size() : 0) + " videos");
    }

    /**
     * 添加视频到列表
     * @param video 要添加的视频
     */
    public void addVideo(VideoModel video) {
        if (videoList != null && video != null) {
            videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
            Log.d(TAG, "Video added: " + video.getTitle());
        }
    }

    /**
     * 移除指定位置的视频
     * @param position 要移除的位置
     */
    public void removeVideo(int position) {
        if (videoList != null && position >= 0 && position < videoList.size()) {
            VideoModel removedVideo = videoList.remove(position);
            fragmentCache.remove(position);
            notifyItemRemoved(position);
            Log.d(TAG, "Video removed: " + removedVideo.getTitle());
        }
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        fragmentCache.clear();
        Log.d(TAG, "Adapter cleaned up");
    }

    /**
     * 获取视频列表
     * @return 当前视频列表
     */
    public List<VideoModel> getVideoList() {
        return videoList;
    }

    /**
     * 检查位置是否有效
     * @param position 要检查的位置
     * @return 是否有效
     */
    public boolean isValidPosition(int position) {
        return position >= 0 && position < getItemCount();
    }

    /**
     * 获取指定位置的视频模型
     * @param position 位置
     * @return VideoModel实例，如果位置无效返回null
     */
    public VideoModel getVideoAt(int position) {
        if (isValidPosition(position)) {
            return videoList.get(position);
        }
        return null;
    }

}
