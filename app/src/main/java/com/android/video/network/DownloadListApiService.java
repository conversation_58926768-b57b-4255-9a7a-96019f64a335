package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.MyListConstantsUtils;
import com.android.video.model.response.DownloadListResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 下载列表API服务类
 * <p>
 * 提供下载列表相关的网络请求功能，包括获取下载列表等。
 * 使用OkHttp进行网络请求，支持异步回调和分页。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see MyListConstantsUtils
 */
public class DownloadListApiService {

    private static final String TAG = "DownloadListApiService";

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    // 单例实例
    private static volatile DownloadListApiService instance;

    /**
     * 下载列表获取回调接口
     */
    public interface DownloadListCallback {
        /**
         * 获取下载列表成功
         * @param response 下载列表响应数据
         */
        void onSuccess(DownloadListResponseModel response);

        /**
         * 获取下载列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造方法
     */
    private DownloadListApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
        this.gson = new GsonBuilder()
                .setLenient()
                .create();
    }

    /**
     * 获取单例实例
     * @return DownloadListApiService实例
     */
    public static DownloadListApiService getInstance() {
        if (instance == null) {
            synchronized (DownloadListApiService.class) {
                if (instance == null) {
                    instance = new DownloadListApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 获取下载列表
     * <p>
     * 异步获取用户的下载列表，支持分页查询。
     * </p>
     *
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @param callback 回调接口
     */
    public void getDownloadList(int pageNum, int pageSize, DownloadListCallback callback) {
        // 验证分页参数
        int validPageNum = MyListConstantsUtils.validatePageNum(pageNum);
        int validPageSize = MyListConstantsUtils.validatePageSize(pageSize);

        // 构建API URL
        String apiUrl = MyListConstantsUtils.buildDownloadListApiUrl(EnvironmentConfigUtils.getApiBaseUrl());

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append(MyListConstantsUtils.PARAM_PAGE_NUM).append("=").append(validPageNum)
                  .append("&").append(MyListConstantsUtils.PARAM_PAGE_SIZE).append("=").append(validPageSize);

        String finalUrl = urlBuilder.toString();

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取下载列表请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "页码: " + pageNum + ", 每页数量: " + pageSize);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取下载列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 下载列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDownloadListResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "下载列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析下载列表响应数据
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDownloadListResponse(String responseBody, DownloadListCallback callback) {
        try {
            DownloadListResponseModel response = gson.fromJson(responseBody, DownloadListResponseModel.class);

            if (response != null) {
                Log.d(TAG, "下载列表解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                });
            } else {
                Log.e(TAG, "下载列表响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "下载列表JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }
}
