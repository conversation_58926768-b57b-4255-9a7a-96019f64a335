package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 瀑布流推荐第一章节信息模型
 * <p>
 * 用于映射瀑布流推荐API返回的第一章节信息数据结构。
 * 对应接口：/app/index/waterfallRecommend
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>chapterId: 章节ID</li>
 *   <li>chapterEp: 剧集数（0=预告片, 1-200=正式剧集）</li>
 *   <li>isCharge: 是否收费（0=免费, 1=付费）</li>
 *   <li>points: 当前积分数</li>
 *   <li>progress: 播放进度（单位：秒）</li>
 *   <li>lastPlayTime: 最后观看时间</li>
 *   <li>isFinished: 是否播放完成</li>
 *   <li>isUnlock: 是否解锁</li>
 *   <li>videoUrl: 视频URL</li>
 *   <li>expirationTime: 视频URL过期时间</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class WaterfallFirstChapterModel {
    
    /**
     * 章节ID
     */
    @SerializedName("chapterId")
    private String chapterId;
    
    /**
     * 剧集数
     * 0=预告片, 1-200=正式剧集
     */
    @SerializedName("chapterEp")
    private int chapterEp;
    
    /**
     * 是否收费
     * 0=免费, 1=付费
     */
    @SerializedName("isCharge")
    private int isCharge;
    
    /**
     * 当前积分数
     */
    @SerializedName("points")
    private int points;
    
    /**
     * 播放进度（单位：秒）
     */
    @SerializedName("progress")
    private Integer progress;
    
    /**
     * 最后观看时间
     */
    @SerializedName("lastPlayTime")
    private String lastPlayTime;
    
    /**
     * 是否播放完成
     */
    @SerializedName("isFinished")
    private Integer isFinished;
    
    /**
     * 是否解锁
     */
    @SerializedName("isUnlock")
    private Integer isUnlock;
    
    /**
     * 视频URL
     */
    @SerializedName("videoUrl")
    private String videoUrl;
    
    /**
     * 视频URL过期时间
     */
    @SerializedName("expirationTime")
    private String expirationTime;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public WaterfallFirstChapterModel() {
    }

    // ========== Getter和Setter方法 ==========

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public int getChapterEp() {
        return chapterEp;
    }

    public void setChapterEp(int chapterEp) {
        this.chapterEp = chapterEp;
    }

    public int getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(int isCharge) {
        this.isCharge = isCharge;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(String lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    public Integer getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(Integer isFinished) {
        this.isFinished = isFinished;
    }

    public Integer getIsUnlock() {
        return isUnlock;
    }

    public void setIsUnlock(Integer isUnlock) {
        this.isUnlock = isUnlock;
    }

    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    public String getExpirationTime() {
        return expirationTime;
    }

    public void setExpirationTime(String expirationTime) {
        this.expirationTime = expirationTime;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否为预告片
     * @return 如果是预告片则返回true
     */
    public boolean isTrailer() {
        return chapterEp == 0;
    }

    /**
     * 检查是否收费
     * @return 如果收费则返回true
     */
    public boolean isCharged() {
        return isCharge == 1;
    }

    /**
     * 检查是否播放完成
     * @return 如果播放完成则返回true
     */
    public boolean isPlayFinished() {
        return isFinished != null && isFinished == 1;
    }

    /**
     * 检查是否已解锁
     * @return 如果已解锁则返回true
     */
    public boolean isUnlocked() {
        return isUnlock != null && isUnlock == 1;
    }

    /**
     * 获取播放进度（秒）
     * @return 播放进度，如果为null则返回0
     */
    public int getProgressSeconds() {
        return progress != null ? progress : 0;
    }

    @Override
    public String toString() {
        return "WaterfallFirstChapterModel{" +
                "chapterId='" + chapterId + '\'' +
                ", chapterEp=" + chapterEp +
                ", isCharge=" + isCharge +
                ", points=" + points +
                ", progress=" + progress +
                ", lastPlayTime='" + lastPlayTime + '\'' +
                ", isFinished=" + isFinished +
                ", isUnlock=" + isUnlock +
                ", videoUrl='" + videoUrl + '\'' +
                ", expirationTime='" + expirationTime + '\'' +
                '}';
    }
}
