package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;

/**
 * 消息详情响应数据模型
 * <p>
 * 用于映射消息详情API返回的JSON数据结构。
 * 包含消息的详细信息，如消息ID、创建时间、标题、内容等。
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "messageId": "26a2524447e97cb6d209c07ab0989f6b",
 *     "createTime": "2025-07-22 00:57:42",
 *     "messageTitle": "这是第一条消息",
 *     "messageContent": "这是第一条消息，这是第一条消息，这是第一条消息"
 *   }
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class MessageDetailResponseModel {
    
    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 消息详情数据
     */
    @SerializedName("data")
    private MessageDetailData data;

    /**
     * 默认构造函数
     */
    public MessageDetailResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data 消息详情数据
     */
    public MessageDetailResponseModel(String code, String message, MessageDetailData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MessageDetailData getData() {
        return data;
    }

    public void setData(MessageDetailData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * 
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    @Override
    public String toString() {
        return "MessageDetailResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 消息详情数据
     * <p>
     * 包含消息的详细信息。
     * </p>
     */
    public static class MessageDetailData {
        
        /**
         * 消息ID
         */
        @SerializedName("messageId")
        private String messageId;
        
        /**
         * 创建时间
         * 格式：yyyy-MM-dd HH:mm:ss
         */
        @SerializedName("createTime")
        private String createTime;
        
        /**
         * 消息标题
         */
        @SerializedName("messageTitle")
        private String messageTitle;
        
        /**
         * 消息内容
         */
        @SerializedName("messageContent")
        private String messageContent;

        /**
         * 默认构造函数
         */
        public MessageDetailData() {
        }

        /**
         * 完整构造函数
         * 
         * @param messageId 消息ID
         * @param createTime 创建时间
         * @param messageTitle 消息标题
         * @param messageContent 消息内容
         */
        public MessageDetailData(String messageId, String createTime, String messageTitle, String messageContent) {
            this.messageId = messageId;
            this.createTime = createTime;
            this.messageTitle = messageTitle;
            this.messageContent = messageContent;
        }

        // ========== Getter和Setter方法 ==========

        public String getMessageId() {
            return messageId;
        }

        public void setMessageId(String messageId) {
            this.messageId = messageId;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getMessageTitle() {
            return messageTitle;
        }

        public void setMessageTitle(String messageTitle) {
            this.messageTitle = messageTitle;
        }

        public String getMessageContent() {
            return messageContent;
        }

        public void setMessageContent(String messageContent) {
            this.messageContent = messageContent;
        }

        @Override
        public String toString() {
            return "MessageDetailData{" +
                    "messageId='" + messageId + '\'' +
                    ", createTime='" + createTime + '\'' +
                    ", messageTitle='" + messageTitle + '\'' +
                    ", messageContent='" + messageContent + '\'' +
                    '}';
        }
    }
}
