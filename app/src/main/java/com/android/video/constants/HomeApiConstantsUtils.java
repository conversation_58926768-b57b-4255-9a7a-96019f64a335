package com.android.video.constants;

/**
 * 首页模块API常量类 - 定义首页相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理首页相关的API常量，
 * 包括获取banner列表、分类及短剧列表、推荐位列表等功能的API路径和参数定义。
 * </p>
 *
 * <p>
 * 包含的首页功能：
 * <ul>
 *   <li>Banner轮播图管理</li>
 *   <li>分类及短剧内容展示</li>
 *   <li>推荐位内容管理</li>
 * </ul>
 * </p>
 *
 * <p>
 * 首页模块特点：
 * <ul>
 *   <li>支持多语言内容展示</li>
 *   <li>支持分页加载</li>
 *   <li>支持按分类筛选</li>
 *   <li>支持不同位置的内容展示</li>
 * </ul>
 * </p>
 *
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取banner API URL
 * String url = HomeApiConstantsUtils.buildApiUrl(baseUrl, HomeApiConstantsUtils.API_GET_BANNERS);
 *
 * // 设置请求参数
 * requestParams.put(HomeApiConstantsUtils.PARAM_LOCATION, "1");  // 首页位置
 * requestParams.put(HomeApiConstantsUtils.PARAM_LANGUAGE_TYPE, "1");  // 英语
 * requestParams.put(HomeApiConstantsUtils.PARAM_PAGE_NUM, "1");
 * requestParams.put(HomeApiConstantsUtils.PARAM_PAGE_SIZE, "10");
 *
 * // 构建搜索API URL
 * String searchUrl = HomeApiConstantsUtils.buildSearchUrl(baseUrl, "英语", 1, 10);
 * // 或者使用GET参数方式
 * String searchApiUrl = HomeApiConstantsUtils.buildApiUrl(baseUrl, HomeApiConstantsUtils.API_SEARCH);
 * </pre>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class HomeApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 首页相关API路径常量 ==========

    /**
     * 获取banner列表API路径
     * <p>
     * 用于获取首页或分类详情页的banner轮播图列表。
     * 请求方法：POST
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_BANNERS = "/app/index/banners";

    /**
     * 获取分类及短剧列表API路径
     * <p>
     * 用于获取分类信息以及对应的短剧内容列表，支持分页查询。
     * 请求方法：GET
     * 认证要求：继承父级
     * 支持参数：categoryId（可选）、page（必填）、size（必填）
     * </p>
     */
    public static final String API_GET_CATEGORIES = "/app/index/categories";

    /**
     * 获取首页推荐位列表API路径
     * <p>
     * 用于获取首页推荐位的内容列表。
     * 请求方法：POST
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_FEATURED = "/app/index/featured";

    /**
     * 搜索短剧API路径
     * <p>
     * 用于根据关键词搜索短剧内容。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_SEARCH = "/app/index/search";

    /**
     * 获取搜索历史API路径
     * <p>
     * 用于获取用户的搜索历史记录。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_SEARCH_HISTORY = "/app/index/search/history";

    /**
     * 清除搜索历史API路径
     * <p>
     * 用于清除用户的搜索历史记录。
     * 请求方法：DELETE
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_CLEAR_SEARCH_HISTORY = "/app/index/clear/history";

    /**
     * 获取分类标签列表API路径
     * <p>
     * 用于获取所有分类标签的列表信息，包括分类ID、名称、权重、语言等。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_CATEGORY_LIST = "/app/index/categoryList";

    /**
     * 获取今日热门排行榜API路径
     * <p>
     * 用于获取今日热门短剧排行榜列表，支持分页查询。
     * 请求方法：GET
     * 认证要求：继承父级
     * 支持参数：page（必填）、size（必填）
     * </p>
     */
    public static final String API_GET_DAILY_RANK = "/app/index/dailyRank";

    /**
     * 获取即将来袭推荐位API路径
     * <p>
     * 用于获取即将来袭(Coming Soon)推荐位列表，支持分页查询。
     * 请求方法：GET
     * 认证要求：继承父级
     * 支持参数：page（必填）、size（必填）
     * </p>
     */
    public static final String API_GET_WORTH_WAITING = "/app/index/worthWaiting";

    /**
     * 获取热门搜索API路径
     * <p>
     * 用于获取热门搜索列表，显示用户搜索频率最高的短剧内容。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_TRENDING_SEARCHES = "/app/index/trending/searches";

    /**
     * 获取最受欢迎列表API路径
     * <p>
     * 用于获取最受欢迎短剧列表，返回热门短剧数据。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_MOST_POPULAR = "/app/index/mostPopular";

    /**
     * 获取猜你喜欢推荐位API路径
     * <p>
     * 用于获取猜你喜欢(Best For You)推荐位列表，基于用户偏好推荐。
     * 请求方法：GET
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_GUESS_YOU_LIKE = "/app/index/guessYouLike";

    /**
     * 订阅/取消订阅短剧API路径
     * <p>
     * 用于订阅或取消订阅指定的短剧内容。
     * 请求方法：POST
     * 认证要求：继承父级
     * 请求体格式：JSON
     * </p>
     */
    public static final String API_SUBSCRIBE = "/app/index/subscribe";

    // ========== 首页相关请求参数常量 ==========

    /**
     * 分类ID参数名
     * <p>
     * 用于指定要获取的分类ID，ALL表示所有分类。
     * 参数类型：String
     * 是否必填：否
     * 示例值：b0ae69449594450e93118e4e4ed77a78, ALL
     * </p>
     */
    public static final String PARAM_CATEGORY_ID = "categoryId";

    /**
     * 语言类型参数名
     * <p>
     * 用于指定内容的语言类型。
     * 参数类型：Integer
     * 是否必填：否
     * 取值范围：1=英语, 2=俄语，多种语言用英文逗号隔开
     * </p>
     */
    public static final String PARAM_LANGUAGE_TYPE = "languageType";

    /**
     * 页码参数名
     * <p>
     * 用于分页查询的页码参数。
     * 参数类型：Integer
     * 是否必填：否
     * 默认值：1
     * </p>
     */
    public static final String PARAM_PAGE_NUM = "pageNum";

    /**
     * 每页数量参数名
     * <p>
     * 用于分页查询的每页数据量参数。
     * 参数类型：Integer
     * 是否必填：否
     * 默认值：10
     * </p>
     */
    public static final String PARAM_PAGE_SIZE = "pageSize";

    /**
     * 位置参数名
     * <p>
     * 用于指定banner所处的位置。
     * 参数类型：Integer
     * 是否必填：否
     * 取值范围：1=首页(home), 2=分类详情
     * </p>
     */
    public static final String PARAM_LOCATION = "location";

    /**
     * 搜索关键词参数名
     * <p>
     * 用于指定搜索的关键词。
     * 参数类型：String
     * 是否必填：是
     * 示例值：英语
     * </p>
     */
    public static final String PARAM_SEARCH_WORD = "searchWord";

    /**
     * 页码参数名（用于分类短剧列表）
     * <p>
     * 用于分页查询的页码参数。
     * 参数类型：String
     * 是否必填：是
     * 默认值：1
     * </p>
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名（用于分类短剧列表）
     * <p>
     * 用于分页查询的每页数据量参数。
     * 参数类型：String
     * 是否必填：是
     * 默认值：10
     * </p>
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 短剧ID参数名（用于订阅接口）
     * <p>
     * 用于指定要订阅或取消订阅的短剧ID。
     * 参数类型：String
     * 是否必填：是
     * 示例值：7e8ef885e92f4496899cabc6906567a0
     * </p>
     */
    public static final String PARAM_FILM_ID = "filmId";

    /**
     * 订阅类型参数名（用于订阅接口）
     * <p>
     * 用于指定订阅操作的类型。
     * 参数类型：Integer
     * 是否必填：是
     * 取值范围：0=取消订阅, 1=订阅
     * </p>
     */
    public static final String PARAM_SUBSCRIBE_TYPE = "subscribeType";

    // ========== 位置常量定义 ==========

    /**
     * 首页位置常量
     * <p>
     * 用于banner位置参数，表示首页位置。
     * </p>
     */
    public static final int LOCATION_HOME = 1;

    /**
     * 分类详情位置常量
     * <p>
     * 用于banner位置参数，表示分类详情页位置。
     * </p>
     */
    public static final int LOCATION_CATEGORY_DETAIL = 2;

    // ========== 语言类型常量定义 ==========

    /**
     * 英语语言类型常量
     * <p>
     * 用于语言类型参数，表示英语内容。
     * </p>
     */
    public static final int LANGUAGE_TYPE_ENGLISH = 1;

    /**
     * 俄语语言类型常量
     * <p>
     * 用于语言类型参数，表示俄语内容。
     * </p>
     */
    public static final int LANGUAGE_TYPE_RUSSIAN = 2;

    // ========== 订阅类型常量定义 ==========

    /**
     * 取消订阅类型常量
     * <p>
     * 用于订阅类型参数，表示取消订阅操作。
     * </p>
     */
    public static final int SUBSCRIBE_TYPE_UNSUBSCRIBE = 0;

    /**
     * 订阅类型常量
     * <p>
     * 用于订阅类型参数，表示订阅操作。
     * </p>
     */
    public static final int SUBSCRIBE_TYPE_SUBSCRIBE = 1;

    // ========== 搜索响应字段常量 ==========

    /**
     * 搜索结果记录列表字段名
     * <p>
     * 用于解析搜索API响应中的记录列表。
     * </p>
     */
    public static final String FIELD_RECORDS = "records";

    /**
     * 短剧标题字段名
     * <p>
     * 用于解析搜索结果中的短剧标题。
     * </p>
     */
    public static final String FIELD_FILM_TITLE = "filmTitle";

    /**
     * 短剧ID字段名
     * <p>
     * 用于解析搜索结果中的短剧ID。
     * </p>
     */
    public static final String FIELD_FILM_ID = "filmId";

    /**
     * 短剧语言信息ID字段名
     * <p>
     * 用于解析搜索结果中的短剧语言信息ID。
     * </p>
     */
    public static final String FIELD_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    /**
     * 封面字段名
     * <p>
     * 用于解析搜索结果中的封面图片URL。
     * </p>
     */
    public static final String FIELD_COVER = "cover";

    /**
     * 分类ID字段名
     * <p>
     * 用于解析搜索结果中的分类ID。
     * </p>
     */
    public static final String FIELD_CATEGORY_ID = "categoryId";

    /**
     * 分类名称字段名
     * <p>
     * 用于解析搜索结果中的分类名称。
     * </p>
     */
    public static final String FIELD_CATEGORY_NAME = "categoryName";

    /**
     * 总记录数字段名
     * <p>
     * 用于解析搜索结果中的总记录数。
     * </p>
     */
    public static final String FIELD_TOTAL = "total";

    /**
     * 每页数量字段名
     * <p>
     * 用于解析搜索结果中的每页数量。
     * </p>
     */
    public static final String FIELD_SIZE = "size";

    /**
     * 当前页码字段名
     * <p>
     * 用于解析搜索结果中的当前页码。
     * </p>
     */
    public static final String FIELD_CURRENT = "current";

    /**
     * 总页数字段名
     * <p>
     * 用于解析搜索结果中的总页数。
     * </p>
     */
    public static final String FIELD_PAGES = "pages";

    // ========== 分类标签响应字段常量 ==========

    /**
     * 分类权重字段名
     * <p>
     * 用于解析分类标签响应中的权重值。
     * </p>
     */
    public static final String FIELD_WEIGHT = "weight";

    /**
     * 语言字段名
     * <p>
     * 用于解析分类标签响应中的语言信息。
     * </p>
     */
    public static final String FIELD_LANGUAGE = "language";

    /**
     * 删除状态字段名
     * <p>
     * 用于解析分类标签响应中的删除状态。
     * </p>
     */
    public static final String FIELD_IS_DELETE = "isDelete";

    /**
     * 创建时间字段名
     * <p>
     * 用于解析分类标签响应中的创建时间。
     * </p>
     */
    public static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 更新时间字段名
     * <p>
     * 用于解析分类标签响应中的更新时间。
     * </p>
     */
    public static final String FIELD_UPDATE_TIME = "updateTime";

    // ========== 分类短剧列表响应字段常量 ==========

    /**
     * 短剧列表字段名
     * <p>
     * 用于解析分类短剧响应中的短剧列表。
     * </p>
     */
    public static final String FIELD_FILMS = "films";

    /**
     * 搜索次数字段名
     * <p>
     * 用于解析短剧信息中的搜索次数。
     * </p>
     */
    public static final String FIELD_SEARCH_NUM = "searchNum";

    /**
     * 短剧详情字段名
     * <p>
     * 用于解析短剧信息中的详情描述。
     * </p>
     */
    public static final String FIELD_DETAILS = "details";

    /**
     * 是否喜欢字段名
     * <p>
     * 用于解析短剧信息中的喜欢状态。
     * </p>
     */
    public static final String FIELD_IS_LOVE = "isLove";

    // ========== 今日热门排行榜响应字段常量 ==========

    /**
     * 播放次数字段名
     * <p>
     * 用于解析今日热门排行榜中的播放次数。
     * </p>
     */
    public static final String FIELD_PLAY_NUM = "playNum";

    /**
     * 发布时间字段名
     * <p>
     * 用于解析今日热门排行榜中的发布时间。
     * </p>
     */
    public static final String FIELD_RELEASE_TIME = "releaseTime";

    // ========== 即将来袭推荐位响应字段常量 ==========

    /**
     * 是否订阅字段名
     * <p>
     * 用于解析即将来袭推荐位中的订阅状态。
     * 0=未订阅, 1=已订阅
     * </p>
     */
    public static final String FIELD_IS_SUBSCRIBE = "isSubscribe";

    // ========== 猜你喜欢推荐位响应字段常量 ==========

    /**
     * 短剧信息字段名
     * <p>
     * 用于解析猜你喜欢推荐位中的短剧信息。
     * </p>
     */
    public static final String FIELD_FILM_INFO = "filmInfo";

    /**
     * 匹配百分比字段名
     * <p>
     * 用于解析猜你喜欢推荐位中的匹配度分值。
     * </p>
     */
    public static final String FIELD_MATCH_PERCENTAGE = "matchPercentage";





    /**
     * 总章节数字段名
     * <p>
     * 用于解析短剧信息中的总章节数。
     * </p>
     */
    public static final String FIELD_TOTAL_CHAPTERS_NUM = "totalChaptersNum";

    // ========== 首页模块工具方法 ==========

    /**
     * 检查是否为首页相关的API路径
     * <p>
     * 判断给定的API路径是否属于首页模块。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是首页相关API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isHome = isHomeApi("/app/index/banners");        // true
     * boolean isHome = isHomeApi("/app/customer/sendSmsCode"); // false
     * </pre>
     */
    public static boolean isHomeApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }

        return apiPath.equals(API_GET_BANNERS) ||
               apiPath.equals(API_GET_CATEGORIES) ||
               apiPath.equals(API_GET_FEATURED) ||
               apiPath.equals(API_SEARCH) ||
               apiPath.equals(API_SEARCH_HISTORY) ||
               apiPath.equals(API_CLEAR_SEARCH_HISTORY) ||
               apiPath.equals(API_GET_CATEGORY_LIST) ||
               apiPath.equals(API_GET_DAILY_RANK) ||
               apiPath.equals(API_GET_WORTH_WAITING) ||
               apiPath.equals(API_GET_MOST_POPULAR) ||
               apiPath.equals(API_GET_GUESS_YOU_LIKE) ||
               apiPath.equals(API_SUBSCRIBE);
    }

    /**
     * 检查是否为banner相关API
     * <p>
     * 判断给定的API路径是否为获取banner的接口。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是banner API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isBanner = isBannerApi("/app/index/banners");     // true
     * boolean isBanner = isBannerApi("/app/index/categories");  // false
     * </pre>
     */
    public static boolean isBannerApi(String apiPath) {
        return API_GET_BANNERS.equals(apiPath);
    }

    /**
     * 检查是否为搜索相关API
     * <p>
     * 判断给定的API路径是否为搜索短剧的接口。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是搜索API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isSearch = isSearchApi("/app/index/search");      // true
     * boolean isSearch = isSearchApi("/app/index/banners");     // false
     * </pre>
     */
    public static boolean isSearchApi(String apiPath) {
        return API_SEARCH.equals(apiPath);
    }

    /**
     * 检查是否为分类标签相关API
     * <p>
     * 判断给定的API路径是否为获取分类标签列表的接口。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是分类标签API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isCategoryList = isCategoryListApi("/app/index/categoryList");  // true
     * boolean isCategoryList = isCategoryListApi("/app/index/banners");       // false
     * </pre>
     */
    public static boolean isCategoryListApi(String apiPath) {
        return API_GET_CATEGORY_LIST.equals(apiPath);
    }

    /**
     * 检查位置参数是否有效
     * <p>
     * 验证位置参数是否在有效范围内。
     * </p>
     *
     * @param location 位置参数
     * @return 如果位置参数有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidLocation(1);  // true (首页)
     * boolean valid = isValidLocation(2);  // true (分类详情)
     * boolean valid = isValidLocation(3);  // false (无效位置)
     * </pre>
     */
    public static boolean isValidLocation(int location) {
        return location == LOCATION_HOME || location == LOCATION_CATEGORY_DETAIL;
    }

    /**
     * 检查语言类型是否有效
     * <p>
     * 验证语言类型参数是否在有效范围内。
     * </p>
     *
     * @param languageType 语言类型参数
     * @return 如果语言类型有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidLanguageType(1);  // true (英语)
     * boolean valid = isValidLanguageType(2);  // true (俄语)
     * boolean valid = isValidLanguageType(3);  // false (无效语言)
     * </pre>
     */
    public static boolean isValidLanguageType(int languageType) {
        return languageType == LANGUAGE_TYPE_ENGLISH || languageType == LANGUAGE_TYPE_RUSSIAN;
    }

    /**
     * 检查是否为订阅相关API
     * <p>
     * 判断给定的API路径是否为订阅/取消订阅的接口。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是订阅API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isSubscribe = isSubscribeApi("/app/index/subscribe");  // true
     * boolean isSubscribe = isSubscribeApi("/app/index/banners");    // false
     * </pre>
     */
    public static boolean isSubscribeApi(String apiPath) {
        return API_SUBSCRIBE.equals(apiPath);
    }

    /**
     * 检查订阅类型是否有效
     * <p>
     * 验证订阅类型参数是否在有效范围内。
     * </p>
     *
     * @param subscribeType 订阅类型参数
     * @return 如果订阅类型有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidSubscribeType(0);  // true (取消订阅)
     * boolean valid = isValidSubscribeType(1);  // true (订阅)
     * boolean valid = isValidSubscribeType(2);  // false (无效类型)
     * </pre>
     */
    public static boolean isValidSubscribeType(int subscribeType) {
        return subscribeType == SUBSCRIBE_TYPE_UNSUBSCRIBE || subscribeType == SUBSCRIBE_TYPE_SUBSCRIBE;
    }

    /**
     * 获取首页API的描述信息
     * <p>
     * 根据API路径返回对应的中文描述信息，
     * 便于调试和日志记录。
     * </p>
     *
     * @param apiPath API路径
     * @return API的中文描述，未知API返回"未知首页接口"
     *
     * @example
     * <pre>
     * String desc = getHomeApiDescription("/app/index/banners");     // "获取banner列表"
     * String desc = getHomeApiDescription("/app/index/categories");  // "获取分类及短剧列表"
     * String desc = getHomeApiDescription("/unknown/api");           // "未知首页接口"
     * </pre>
     */
    public static String getHomeApiDescription(String apiPath) {
        if (apiPath == null) {
            return "UNDEFINED_ERROR";
        }

        switch (apiPath) {
            case API_GET_BANNERS:
                return "获取banner列表";
            case API_GET_CATEGORIES:
                return "获取分类及短剧列表（支持分页）";
            case API_GET_FEATURED:
                return "获取首页推荐位列表";
            case API_SEARCH:
                return "搜索短剧";
            case API_GET_CATEGORY_LIST:
                return "获取分类标签列表";
            case API_GET_DAILY_RANK:
                return "获取今日热门排行榜";
            case API_GET_WORTH_WAITING:
                return "获取即将来袭推荐位";
            case API_GET_MOST_POPULAR:
                return "获取最受欢迎列表";
            case API_GET_GUESS_YOU_LIKE:
                return "获取猜你喜欢推荐位";
            case API_GET_TRENDING_SEARCHES:
                return "获取热门搜索";
            case API_SUBSCRIBE:
                return "订阅/取消订阅短剧";
            default:
                return "UNDEFINED_HOME_ERROR";
        }
    }

    /**
     * 获取位置的描述信息
     * <p>
     * 根据位置参数返回对应的中文描述。
     * </p>
     *
     * @param location 位置参数
     * @return 位置的中文描述
     *
     * @example
     * <pre>
     * String desc = getLocationDescription(1);  // "首页"
     * String desc = getLocationDescription(2);  // "分类详情"
     * String desc = getLocationDescription(3);  // "未知位置"
     * </pre>
     */
    public static String getLocationDescription(int location) {
        switch (location) {
            case LOCATION_HOME:
                return "首页";
            case LOCATION_CATEGORY_DETAIL:
                return "分类详情";
            default:
                return "UNDEFINED_HOME_ERROR";
        }
    }

    /**
     * 获取语言类型的描述信息
     * <p>
     * 根据语言类型参数返回对应的中文描述。
     * </p>
     *
     * @param languageType 语言类型参数
     * @return 语言类型的中文描述
     *
     * @example
     * <pre>
     * String desc = getLanguageTypeDescription(1);  // "英语"
     * String desc = getLanguageTypeDescription(2);  // "俄语"
     * String desc = getLanguageTypeDescription(3);  // "未知语言"
     * </pre>
     */
    public static String getLanguageTypeDescription(int languageType) {
        switch (languageType) {
            case LANGUAGE_TYPE_ENGLISH:
                return "英语";
            case LANGUAGE_TYPE_RUSSIAN:
                return "俄语";
            default:
                return "UNDEFINED_HOME_ERROR";
        }
    }

    /**
     * 获取订阅类型的描述信息
     * <p>
     * 根据订阅类型参数返回对应的中文描述。
     * </p>
     *
     * @param subscribeType 订阅类型参数
     * @return 订阅类型的中文描述
     *
     * @example
     * <pre>
     * String desc = getSubscribeTypeDescription(0);  // "取消订阅"
     * String desc = getSubscribeTypeDescription(1);  // "订阅"
     * String desc = getSubscribeTypeDescription(2);  // "未知订阅类型"
     * </pre>
     */
    public static String getSubscribeTypeDescription(int subscribeType) {
        switch (subscribeType) {
            case SUBSCRIBE_TYPE_UNSUBSCRIBE:
                return "取消订阅";
            case SUBSCRIBE_TYPE_SUBSCRIBE:
                return "订阅";
            default:
                return "UNDEFINED_HOME_ERROR";
        }
    }

    /**
     * 验证搜索关键词是否有效
     * <p>
     * 检查搜索关键词是否符合要求。
     * </p>
     *
     * @param searchWord 搜索关键词
     * @return 如果关键词有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidSearchWord("英语");     // true
     * boolean valid = isValidSearchWord("");        // false
     * boolean valid = isValidSearchWord(null);      // false
     * </pre>
     */
    public static boolean isValidSearchWord(String searchWord) {
        return searchWord != null && !searchWord.trim().isEmpty() && searchWord.trim().length() <= 50;
    }

    /**
     * 验证短剧ID是否有效
     * <p>
     * 检查短剧ID是否符合要求。
     * </p>
     *
     * @param filmId 短剧ID
     * @return 如果短剧ID有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidFilmId("7e8ef885e92f4496899cabc6906567a0");  // true
     * boolean valid = isValidFilmId("");                                   // false
     * boolean valid = isValidFilmId(null);                                 // false
     * </pre>
     */
    public static boolean isValidFilmId(String filmId) {
        return filmId != null && !filmId.trim().isEmpty() && filmId.trim().length() >= 10;
    }

    /**
     * 构建搜索API的完整URL
     * <p>
     * 根据基础URL和搜索参数构建完整的搜索API URL。
     * </p>
     *
     * @param baseUrl 基础URL
     * @param searchWord 搜索关键词
     * @param pageNum 页码
     * @param pageSize 每页数量
     * @return 完整的搜索API URL
     *
     * @example
     * <pre>
     * String url = buildSearchUrl("https://api.example.com", "英语", 1, 10);
     * // 返回: "https://api.example.com/app/index/search?searchWord=英语&pageNum=1&pageSize=10"
     * </pre>
     */
    public static String buildSearchUrl(String baseUrl, String searchWord, int pageNum, int pageSize) {
        if (baseUrl == null || searchWord == null) {
            return "";
        }

        String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;

        return cleanBaseUrl + API_SEARCH +
               "?" + PARAM_SEARCH_WORD + "=" + searchWord +
               "&" + PARAM_PAGE_NUM + "=" + pageNum +
               "&" + PARAM_PAGE_SIZE + "=" + pageSize;
    }
}
