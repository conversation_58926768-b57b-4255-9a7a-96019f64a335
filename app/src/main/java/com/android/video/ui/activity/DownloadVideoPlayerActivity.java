package com.android.video.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import com.android.video.R;
import com.android.video.model.DownloadVideo;
import com.android.video.model.VideoModel;
import com.android.video.model.EpisodeModel;
import com.android.video.player.VideoPlayerListener;
import com.android.video.player.VideoPlayerManager;
import com.android.video.player.VideoQualitySelector;
import com.android.video.ui.component.QualitySelectionView;
import com.android.video.ui.component.SpeedSelectionView;
import com.android.video.ui.component.EpisodeSelectionView;
import com.android.video.ui.component.SubtitlePanelView;
import com.android.video.ui.fragment.VideoPlayerFragment;
import com.android.video.utils.VideoPlayerPreferences;
import com.android.video.utils.VideoResourceManager;
import com.android.video.manager.FloatingVideoManager;
import com.android.video.utils.FloatingVideoPermissionUtils;

/**
 * 下载视频播放页面Activity
 * 与VideoPlayerActivity功能相同但移除了下载、评论、分享、转发按钮，且不可滑动
 * <AUTHOR> Team
 */
public class DownloadVideoPlayerActivity extends BaseFullScreenActivity implements VideoPlayerListener {
    
    private static final String TAG = "DownloadVideoPlayerActivity";
    public static final String EXTRA_DOWNLOAD_VIDEO = "extra_download_video";
    
    // UI Components
    private FrameLayout videoContainer;
    private LinearLayout topControlBar;
    private LinearLayout sideControlButtons;
    private LinearLayout bottomInfoArea;
    
    // Control Buttons
    private ImageView btnBack;
    private ImageView btnSmallScreen;
    private ImageView btnDetail;
    private ImageView btnFullscreen;
    
    // Info Views
    private TextView tvVideoTitle;
    private TextView tvVideoDescription;
    private SeekBar progressSeekBar;
    private boolean isDescriptionExpanded = false;
    private float currentPlaybackSpeed = 1.0f;
    private VideoQualitySelector.VideoQuality currentQuality = VideoQualitySelector.VideoQuality.QUALITY_720P;
    private boolean isUserSeeking = false;
    
    // Control Views
    private LinearLayout btnEpisodeSelection;
    private TextView tvEpisodeText;
    private ImageView btnDanmaku;
    private TextView btnSpeedSelection;
    private TextView btnQualitySelection;
    
    // Data
    private DownloadVideo downloadVideo;
    private VideoModel videoModel;
    
    // Player Components
    private VideoPlayerFragment playerFragment;
    private VideoPlayerPreferences preferences;
    private VideoResourceManager resourceManager;
    
    // Selection Components
    private QualitySelectionView qualitySelectionView;
    private SpeedSelectionView speedSelectionView;
    private EpisodeSelectionView episodeSelectionView;
    private SubtitlePanelView subtitlePanelView;
    
    // State
    private boolean isFullscreen = false;
    private boolean areControlsVisibleInFullscreen = true;
    private boolean isDanmakuEnabled = true; // 默认开启弹幕
    private Handler fullscreenHandler;
    private Runnable hideControlsRunnable;

    // Progress Update
    private Handler uiHandler;
    private Runnable progressUpdateRunnable;
    private boolean shouldUpdateProgress = false;
    
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download_video_player);
        
        // 获取传递的下载视频数据
        downloadVideo = (DownloadVideo) getIntent().getSerializableExtra(EXTRA_DOWNLOAD_VIDEO);
        if (downloadVideo == null) {
            Log.e(TAG, "No download video data provided");
            finish();
            return;
        }
        
        // 转换为VideoModel以便复用现有逻辑
        convertToVideoModel();
        
        // 初始化组件
        initializeComponents();
        initializeViews();
        setupEventListeners();
        setupPlayerFragment();
        
        // 加载视频数据
        loadVideoData();

        // 初始化按钮文本
        updateSpeedButton();
        updateQualityButton();

        Log.d(TAG, "DownloadVideoPlayerActivity created for: " + downloadVideo.getTitle());
    }

    /**
     * 转换DownloadVideo为VideoModel
     */
    private void convertToVideoModel() {
        videoModel = new VideoModel(
            downloadVideo.getId(),
            downloadVideo.getTitle(),
            downloadVideo.getPosterUrl(), // 使用posterUrl而不是videoUrl作为第三个参数
            downloadVideo.getCategory()
        );
        videoModel.setSynopsis(downloadVideo.getDescription());
        videoModel.setPosterUrl(downloadVideo.getThumbnailUrl());
        videoModel.setDuration(downloadVideo.getDuration());

        // 设置剧集信息
        videoModel.setTotalEpisodes(downloadVideo.getTotalEpisodes());
        videoModel.setCurrentEpisode(1); // 默认从第一集开始

        // 创建剧集列表
        createEpisodeList();
    }

    /**
     * 获取DownloadVideo对象（供VideoPlayerFragment使用）
     */
    public DownloadVideo getDownloadVideo() {
        return downloadVideo;
    }

    /**
     * 创建剧集列表
     */
    private void createEpisodeList() {
        if (downloadVideo.getTotalEpisodes() > 1) {
            for (int i = 1; i <= downloadVideo.getTotalEpisodes(); i++) {
                EpisodeModel episode = new EpisodeModel("episode_" + downloadVideo.getId() + "_" + i,
                                                       i, "第" + i + "集");
                episode.setLive(true); // 设置为有直播图标
                if (i == 1) {
                    episode.setSelected(true); // 设置第一集为选中状态
                }
                videoModel.addEpisode(episode);
            }
        }
    }
    
    /**
     * 初始化组件
     */
    private void initializeComponents() {
        preferences = new VideoPlayerPreferences(this);
        resourceManager = VideoResourceManager.getInstance();
        if (!resourceManager.isInitialized()) {
            resourceManager.initialize(this);
        }

        // 恢复用户偏好设置
        currentPlaybackSpeed = preferences.getPlaybackSpeed();
        currentQuality = preferences.getVideoQuality();
        isDanmakuEnabled = preferences.isDanmakuEnabled();

        // 初始化Handler
        fullscreenHandler = new Handler(Looper.getMainLooper());
        hideControlsRunnable = this::hideFullscreenControls;
        uiHandler = new Handler(Looper.getMainLooper());

        // 初始化进度更新
        initializeProgressUpdate();
    }
    
    /**
     * 初始化视图
     */
    private void initializeViews() {
        // 容器
        videoContainer = findViewById(R.id.video_container);
        topControlBar = findViewById(R.id.top_control_bar);
        sideControlButtons = findViewById(R.id.side_control_buttons);
        bottomInfoArea = findViewById(R.id.bottom_info_area);
        
        // 控制按钮
        btnBack = findViewById(R.id.btn_back);
        btnSmallScreen = findViewById(R.id.btn_small_screen);
        btnDetail = findViewById(R.id.btn_detail);
        btnFullscreen = findViewById(R.id.btnFullscreen);
        
        // 信息视图
        tvVideoTitle = findViewById(R.id.tv_video_title);
        tvVideoDescription = findViewById(R.id.tv_video_description);
        progressSeekBar = findViewById(R.id.progress_seek_bar);
        
        // 控制视图
        btnEpisodeSelection = findViewById(R.id.btnEpisodeSelection);
        tvEpisodeText = findViewById(R.id.tvEpisodeText);
        btnDanmaku = findViewById(R.id.btnDanmaku);
        btnSpeedSelection = findViewById(R.id.btnSpeedSelection);
        btnQualitySelection = findViewById(R.id.btnQualitySelection);
        
        // 下载按钮已从布局中移除，无需处理
        
        // 初始化选择组件
        initializeSelectionComponents();
    }
    
    /**
     * 初始化选择组件
     */
    private void initializeSelectionComponents() {
        preferences = new VideoPlayerPreferences(this);

        // 初始化清晰度选择组件
        qualitySelectionView = new QualitySelectionView(this);
        qualitySelectionView.setSelectedQuality(currentQuality);
        qualitySelectionView.setOnQualitySelectedListener(quality -> {
            currentQuality = quality;
            if (playerFragment != null && playerFragment.getPlayerManager() != null) {
                playerFragment.getPlayerManager().setQuality(quality);
            }
            if (preferences != null) {
                preferences.saveVideoQuality(quality);
            }
            updateQualityButton();
        });

        // 初始化速度选择组件
        speedSelectionView = new SpeedSelectionView(this);
        speedSelectionView.setSelectedSpeed(currentPlaybackSpeed);
        speedSelectionView.setOnSpeedSelectedListener(speed -> {
            currentPlaybackSpeed = speed;
            if (playerFragment != null && playerFragment.getPlayerManager() != null) {
                playerFragment.getPlayerManager().setPlaybackSpeed(speed);
            }
            if (preferences != null) {
                preferences.savePlaybackSpeed(speed);
            }
            updateSpeedButton();
        });

        // 初始化选集选择组件
        episodeSelectionView = new EpisodeSelectionView(this);
        episodeSelectionView.setOnEpisodeSelectedListener(episode -> {
            if (videoModel != null) {
                Log.d(TAG, "Episode selected: " + episode.getEpisodeNumber());
                videoModel.setCurrentEpisode(episode.getEpisodeNumber());
                loadVideoData();

                // 通知Fragment重新加载视频
                reloadCurrentVideo();
            }
        });

        // 初始化字幕面板
        subtitlePanelView = new SubtitlePanelView(this);
        subtitlePanelView.setOnSubtitleSettingsChangeListener(new SubtitlePanelView.OnSubtitleSettingsChangeListener() {
            @Override
            public void onSubtitleEnabledChanged(boolean enabled) {
                // 更新弹幕状态
                isDanmakuEnabled = enabled;
                btnDanmaku.setSelected(enabled);

                // 保存弹幕设置
                if (preferences != null) {
                    preferences.saveDanmakuEnabled(enabled);
                }

                // 控制Fragment的弹幕显示
                if (playerFragment != null) {
                    playerFragment.toggleDanmaku(enabled);
                }

                // 更新按钮状态
                updateDanmakuButtonState();
            }

            @Override
            public void onLanguageChanged(String language) {
                // 字幕语言切换处理
            }

            @Override
            public void onPanelClosed() {
                // 面板关闭时的处理
            }
        });

        // 设置字幕面板的初始状态（与用户设置同步）
        subtitlePanelView.setSubtitleEnabled(isDanmakuEnabled);

        // 更新弹幕按钮状态
        updateDanmakuButtonState();

        // 将组件添加到根布局中
        addComponentsToLayout();

        // 确保弹窗组件初始状态不可点击，避免拦截底部按钮的触摸事件
        setupPopupComponentsToAvoidTouchInterception();
    }

    /**
     * 将动态创建的组件添加到布局中
     */
    private void addComponentsToLayout() {
        androidx.constraintlayout.widget.ConstraintLayout rootLayout =
            findViewById(R.id.root_layout);

        if (rootLayout == null) {
            // 如果没有找到root_layout，使用第一个ConstraintLayout
            rootLayout = (androidx.constraintlayout.widget.ConstraintLayout)
                ((android.view.ViewGroup) findViewById(android.R.id.content)).getChildAt(0);
        }

        // 参考DiscoverFragment的实现，使用FrameLayout.LayoutParams
        android.widget.FrameLayout.LayoutParams popupParams =
                new android.widget.FrameLayout.LayoutParams(
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT,
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
        // 确保弹窗不会覆盖底部按钮区域，设置在左上角
        popupParams.gravity = android.view.Gravity.TOP | android.view.Gravity.START;

        qualitySelectionView.setLayoutParams(popupParams);
        speedSelectionView.setLayoutParams(popupParams);

        // 添加剧集选择组件
        androidx.constraintlayout.widget.ConstraintLayout.LayoutParams episodeParams =
            new androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT);
        episodeParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        episodeParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        episodeParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        episodeSelectionView.setLayoutParams(episodeParams);

        // 添加字幕面板组件
        androidx.constraintlayout.widget.ConstraintLayout.LayoutParams subtitleParams =
            new androidx.constraintlayout.widget.ConstraintLayout.LayoutParams(
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.MATCH_PARENT,
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.WRAP_CONTENT);
        subtitleParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        subtitleParams.startToStart = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        subtitleParams.endToEnd = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
        subtitlePanelView.setLayoutParams(subtitleParams);

        // 设置初始状态
        qualitySelectionView.setVisibility(View.GONE);
        speedSelectionView.setVisibility(View.GONE);
        episodeSelectionView.setVisibility(View.GONE);
        subtitlePanelView.setVisibility(View.GONE);

        // 添加到布局
        rootLayout.addView(qualitySelectionView);
        rootLayout.addView(speedSelectionView);
        rootLayout.addView(episodeSelectionView);
        rootLayout.addView(subtitlePanelView);
    }

    /**
     * 设置弹窗组件避免触摸事件拦截
     */
    private void setupPopupComponentsToAvoidTouchInterception() {
        // 确保弹窗组件初始状态隐藏，但保持可点击性
        qualitySelectionView.setVisibility(View.GONE);
        speedSelectionView.setVisibility(View.GONE);
        episodeSelectionView.setVisibility(View.GONE);

        // 设置弹窗组件的触摸监听器，确保隐藏时不拦截事件
        qualitySelectionView.setOnTouchListener((v, event) -> {
            // 如果弹窗不可见，完全不拦截事件
            if (qualitySelectionView.getVisibility() != View.VISIBLE) {
                return false;
            }
            // 如果弹窗可见，只处理弹窗内部的触摸事件
            return false;
        });

        speedSelectionView.setOnTouchListener((v, event) -> {
            // 如果弹窗不可见，完全不拦截事件
            if (speedSelectionView.getVisibility() != View.VISIBLE) {
                return false;
            }
            // 如果弹窗可见，只处理弹窗内部的触摸事件
            return false;
        });

        episodeSelectionView.setOnTouchListener((v, event) -> {
            // 如果弹窗不可见，完全不拦截事件
            if (episodeSelectionView.getVisibility() != View.VISIBLE) {
                return false;
            }
            // 如果弹窗可见，只处理弹窗内部的触摸事件
            return false;
        });
    }
    
    /**
     * 设置事件监听器
     */
    private void setupEventListeners() {
        // 返回按钮
        btnBack.setOnClickListener(v -> onBackPressed());
        
        // 小屏按钮
        btnSmallScreen.setOnClickListener(v -> {
            startFloatingVideoPlay();
        });
        
        // 详情按钮
        btnDetail.setOnClickListener(v -> openVideoDetail());
        
        // 全屏按钮
        btnFullscreen.setOnClickListener(v -> toggleFullscreen());
        
        // 速度选择按钮
        btnSpeedSelection.setOnClickListener(v -> {
            speedSelectionView.showAbove(btnSpeedSelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 清晰度按钮
        btnQualitySelection.setOnClickListener(v -> {
            qualitySelectionView.showAbove(btnQualitySelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 弹幕按钮
        btnDanmaku.setOnClickListener(v -> {
            toggleDanmaku();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 剧集选择按钮
        btnEpisodeSelection.setOnClickListener(v -> {
            Log.d(TAG, "Episode selection button clicked");
            if (videoModel != null && videoModel.hasMultipleEpisodes()) {
                episodeSelectionView.setEpisodeList(videoModel.getEpisodes());
                episodeSelectionView.setSelectedEpisode(videoModel.getSelectedEpisode());
                episodeSelectionView.toggle();
                if (isFullscreen) resetFullscreenAutoHideTimer();
            } else {
                Log.d(TAG, "VideoModel is null or has no multiple episodes");
            }
        });

        // 描述文本点击展开/收起
        tvVideoDescription.setOnClickListener(v -> toggleDescriptionExpansion());
        
        // 视频标题点击跳转到视频详情页
        tvVideoTitle.setOnClickListener(v -> openVideoDetail());

        // 设置进度条监听器
        setupProgressSeekBar();
    }
    
    /**
     * 设置播放器Fragment
     */
    private void setupPlayerFragment() {
        playerFragment = new VideoPlayerFragment();
        Bundle args = new Bundle();
        args.putSerializable("video_model", videoModel);
        args.putInt("fragment_position", 0);
        playerFragment.setArguments(args);
        
        getSupportFragmentManager().beginTransaction()
                .replace(R.id.video_container, playerFragment)
                .commit();
    }

    /**
     * 加载视频数据
     */
    private void loadVideoData() {
        if (videoModel == null) return;

        // 更新UI
        tvVideoTitle.setText(videoModel.getTitle());
        tvVideoDescription.setText(videoModel.getSynopsis());

        // 更新进度条
        progressSeekBar.setProgress((int) (videoModel.getWatchProgress() * 100));

        // 更新选集按钮
        updateEpisodeButton(videoModel);

        // 更新弹幕按钮状态
        updateDanmakuButtonState();

        Log.d(TAG, "Video data loaded for UI: " + videoModel.getTitle());
    }

    /**
     * 重新加载当前视频（用于选集切换）
     */
    private void reloadCurrentVideo() {
        if (playerFragment != null) {
            Log.d(TAG, "Reloading video for episode change");

            // 重置Fragment的加载状态，强制重新加载
            playerFragment.resetLoadingState();

            // 重新加载视频数据
            playerFragment.loadVideoData();
        } else {
            Log.w(TAG, "Player fragment is null, cannot reload video");
        }
    }

    /**
     * 更新选集按钮
     */
    private void updateEpisodeButton(VideoModel video) {
        if (video != null && video.getTotalEpisodes() > 1) {
            btnEpisodeSelection.setVisibility(View.VISIBLE);
            String episodeText = "EP." + video.getCurrentEpisode() + "/EP." + video.getTotalEpisodes();

            // 使用SpannableString来设置不同颜色
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到第一个"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色 (EP.1)
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                        0, slashIndex,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色 (/EP.72)
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                        slashIndex, episodeText.length(),
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        } else {
            btnEpisodeSelection.setVisibility(View.VISIBLE);
            // 单集视频或无集数信息时显示EP.1/EP.1
            String episodeText = "EP.1/EP.1";
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                        0, slashIndex,
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色
                spannableText.setSpan(
                        new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                        slashIndex, episodeText.length(),
                        android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        }
    }

    /**
     * 更新速度按钮
     */
    private void updateSpeedButton() {
        btnSpeedSelection.setText(String.format("%.1fx", currentPlaybackSpeed));
    }

    /**
     * 更新清晰度按钮
     */
    private void updateQualityButton() {
        btnQualitySelection.setText(currentQuality.getDisplayName());
    }

    /**
     * 切换描述展开状态
     */
    private void toggleDescriptionExpansion() {
        isDescriptionExpanded = !isDescriptionExpanded;
        if (isDescriptionExpanded) {
            tvVideoDescription.setMaxLines(Integer.MAX_VALUE);
        } else {
            tvVideoDescription.setMaxLines(3);
        }
    }

    /**
     * 切换弹幕状态
     */
    private void toggleDanmaku() {
        // 切换弹幕面板显示
        if (subtitlePanelView != null) {
            subtitlePanelView.toggle();
        }
    }

    /**
     * 更新弹幕按钮状态
     */
    private void updateDanmakuButtonState() {
        if (btnDanmaku != null) {
            btnDanmaku.setSelected(isDanmakuEnabled);
            btnDanmaku.setAlpha(1.0f); // 保持按钮完全可见
        }
    }

    /**
     * 打开视频详情
     */
    private void openVideoDetail() {
        // 跳转到视频详情页面
        Intent intent = new Intent(this, VideoDetailActivity.class);
        intent.putExtra("video_model", videoModel);
        startActivity(intent);
    }

    /**
     * 切换全屏状态
     */
    private void toggleFullscreen() {
        if (isFullscreen) {
            // 退出全屏
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            btnFullscreen.setImageResource(R.drawable.play_ic_quanping);
        } else {
            // 进入全屏
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            btnFullscreen.setImageResource(R.drawable.play_ic_suoxiao);
        }
        isFullscreen = !isFullscreen;
    }

    /**
     * 隐藏系统UI
     */
    private void hideSystemUI() {
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                        | View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                        | View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_FULLSCREEN);
    }

    /**
     * 显示系统UI
     */
    private void showSystemUI() {
        getWindow().getDecorView().setSystemUiVisibility(
                View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                        | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                        | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN);
    }

    /**
     * 重置全屏自动隐藏定时器
     */
    private void resetFullscreenAutoHideTimer() {
        cancelFullscreenAutoHideTimer();
        fullscreenHandler.postDelayed(hideControlsRunnable, 3000);
    }

    /**
     * 取消全屏自动隐藏定时器
     */
    private void cancelFullscreenAutoHideTimer() {
        fullscreenHandler.removeCallbacks(hideControlsRunnable);
    }

    /**
     * 隐藏全屏控制栏
     */
    private void hideFullscreenControls() {
        if (isFullscreen && areControlsVisibleInFullscreen) {
            areControlsVisibleInFullscreen = false;
            topControlBar.setVisibility(View.GONE);
            bottomInfoArea.setVisibility(View.GONE);
            sideControlButtons.setVisibility(View.GONE);
        }
    }

    /**
     * 显示全屏控制栏
     */
//    private void showFullscreenControls() {
//        if (isFullscreen && !areControlsVisibleInFullscreen) {
//            areControlsVisibleInFullscreen = true;
//            topControlBar.setVisibility(View.VISIBLE);
//            bottomInfoArea.setVisibility(View.VISIBLE);
//            sideControlButtons.setVisibility(View.VISIBLE);
//            resetFullscreenAutoHideTimer();
//        }
//    }

    // VideoPlayerListener 接口实现
    @Override
    public void onPrepared() {
        Log.d(TAG, "Player prepared");
    }

    @Override
    public void onPlaybackStarted() {
        Log.d(TAG, "Playback started");
    }

    @Override
    public void onPlaybackPaused() {
        Log.d(TAG, "Playback paused");
    }

    @Override
    public void onPlaybackStopped() {
        Log.d(TAG, "Playback stopped");
    }

    @Override
    public void onPlaybackCompleted() {
        Log.d(TAG, "Playback completed");
    }

    @Override
    public void onPositionChanged(long position, long duration) {
        // 进度更新
    }

    @Override
    public void onBufferingUpdate(boolean isBuffering, int bufferedPercentage) {
        // 缓冲更新
    }

    @Override
    public void onError(String error) {
        Log.e(TAG, "Playback error: " + error);
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        // 播放状态变化
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {
        // 视频尺寸变化
    }

    @Override
    public void onVolumeChanged(float volume) {
        // 音量变化
    }

    @Override
    public void onPlaybackSpeedChanged(float speed) {
        // 播放速度变化
    }



    /**
     * 启动下载视频播放页面
     */
    /**
     * 初始化进度更新
     */
    private void initializeProgressUpdate() {
        progressUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (shouldUpdateProgress && playerFragment != null && playerFragment.getPlayerManager() != null) {
                    com.android.video.player.VideoPlayerManager playerManager = playerFragment.getPlayerManager();
                    long position = playerManager.getCurrentPosition();
                    long duration = playerManager.getDuration();
                    if (duration > 0) {
                        int progress = (int) ((position * 100) / duration);
                        progressSeekBar.setProgress(progress);
                    }
                }
                if (shouldUpdateProgress) {
                    uiHandler.postDelayed(this, 1000); // 每秒更新一次
                }
            }
        };
    }

    /**
     * 设置进度条监听器
     */
    private void setupProgressSeekBar() {
        progressSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && playerFragment != null && playerFragment.getPlayerManager() != null) {
                    com.android.video.player.VideoPlayerManager playerManager = playerFragment.getPlayerManager();
                    long duration = playerManager.getDuration();
                    if (duration > 0) {
                        long newPosition = (progress * duration) / 100;
                        playerManager.seekTo(newPosition);
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                // 开始拖拽时暂停进度更新
                shouldUpdateProgress = false;
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                // 停止拖拽时恢复进度更新
                shouldUpdateProgress = true;
                if (uiHandler != null && progressUpdateRunnable != null) {
                    uiHandler.post(progressUpdateRunnable);
                }
            }
        });
    }

    /**
     * 启动进度更新
     */
    private void startProgressUpdate() {
        shouldUpdateProgress = true;
        if (uiHandler != null && progressUpdateRunnable != null) {
            uiHandler.post(progressUpdateRunnable);
        }
    }

    /**
     * 停止进度更新
     */
    private void stopProgressUpdate() {
        shouldUpdateProgress = false;
        if (uiHandler != null && progressUpdateRunnable != null) {
            uiHandler.removeCallbacks(progressUpdateRunnable);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        startProgressUpdate();
    }

    @Override
    protected void onPause() {
        super.onPause();
        stopProgressUpdate();
    }



    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 处理屏幕旋转
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横屏模式
            isFullscreen = true;
            btnFullscreen.setImageResource(R.drawable.play_ic_suoxiao);
            hideUIForFullscreen();
            // 启动全屏控制元素的自动隐藏
            showFullscreenControls();
        } else {
            // 竖屏模式
            isFullscreen = false;
            btnFullscreen.setImageResource(R.drawable.play_ic_quanping);
            showUIForNormalMode();
        }
    }

    /**
     * 隐藏UI元素以实现全屏效果
     */
    private void hideUIForFullscreen() {
        // 隐藏顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.GONE);
        }

        // 隐藏侧边控制按钮
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.GONE);
        }

        // 隐藏视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.GONE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.GONE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.GONE);
        }

        // 隐藏系统UI
        hideSystemUI();
    }

    /**
     * 显示UI元素恢复正常模式
     */
    private void showUIForNormalMode() {
        // 显示顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.VISIBLE);
        }

        // 显示侧边控制按钮
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.VISIBLE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.VISIBLE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.VISIBLE);
        }

        // 显示系统UI
        showSystemUI();

        // 停止全屏控制元素的自动隐藏
        cancelFullscreenAutoHideTimer();
        areControlsVisibleInFullscreen = true;
    }

    /**
     * 显示全屏控制元素
     */
    private void showFullscreenControls() {
        if (!isFullscreen) return;

        areControlsVisibleInFullscreen = true;

        // 显示全屏按钮
        if (btnFullscreen != null) {
            btnFullscreen.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 重置自动隐藏定时器
        resetFullscreenAutoHideTimer();
    }

    public static void start(Activity context, DownloadVideo downloadVideo) {
        Intent intent = new Intent(context, DownloadVideoPlayerActivity.class);
        intent.putExtra(EXTRA_DOWNLOAD_VIDEO, downloadVideo);
        context.startActivity(intent);

        Log.d("DownloadVideoPlayerActivity", "Started with download video: " + downloadVideo.getTitle());
    }

    /**
     * 启动悬浮窗播放
     */
    private void startFloatingVideoPlay() {
        if (videoModel != null) {
            // 先隐藏当前视频播放器，显示黑色背景
            hideCurrentVideoPlayer();

            // 启动悬浮窗播放
            FloatingVideoManager.getInstance().startFromDownloadPlayer(this, videoModel);
        } else {
            Toast.makeText(this, "当前没有播放的视频", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 隐藏当前视频播放器，显示黑色背景
     */
    private void hideCurrentVideoPlayer() {
        try {
            // 查找并隐藏PlayerView
            View rootView = findViewById(android.R.id.content);
            if (rootView != null) {
                hidePlayerViewsInView(rootView);
            }

            Log.d(TAG, "Current video player hidden for floating window in DownloadVideoPlayerActivity");
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide current video player in DownloadVideoPlayerActivity", e);
        }
    }

    /**
     * 在指定视图中递归查找并隐藏PlayerView
     */
    private void hidePlayerViewsInView(View view) {
        try {
            if (view instanceof com.google.android.exoplayer2.ui.PlayerView) {
                com.google.android.exoplayer2.ui.PlayerView playerView = (com.google.android.exoplayer2.ui.PlayerView) view;
                playerView.setBackgroundColor(android.graphics.Color.BLACK);
                // 暂时移除播放器，显示黑色背景
                if (playerView.getPlayer() != null) {
                    playerView.setPlayer(null);
                }
                Log.d(TAG, "PlayerView hidden and set to black background in DownloadVideoPlayerActivity");
            } else if (view instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    hidePlayerViewsInView(viewGroup.getChildAt(i));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding player views in DownloadVideoPlayerActivity", e);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 处理悬浮窗权限请求结果
        if (requestCode == FloatingVideoPermissionUtils.REQUEST_CODE_OVERLAY_PERMISSION) {
            FloatingVideoManager.getInstance().handlePermissionResult(this, requestCode);
        }
    }

    // ==================== 内容保护功能 ====================

    /**
     * 重写父类方法，启用内容保护功能
     * 下载视频播放页面需要启用内容保护
     */
    @Override
    protected boolean shouldEnableContentProtection() {
        return true; // 下载视频播放页面启用内容保护
    }

}
