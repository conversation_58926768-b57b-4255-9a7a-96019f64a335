<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="white">#FFFFFFFF</color>

    <!-- Login Screen Colors -->
    <color name="login_text_gray">#9CA3AF</color>
    <color name="login_text_white">#FFFFFF</color>
    <color name="login_button_text_black">#000000</color>
    <color name="facebook_blue">#1877F2</color>
    <color name="facebook_blue_pressed">#1565C0</color>
    <color name="button_gray_pressed">#F0F0F0</color>
    <color name="button_black_pressed">#333333</color>

    <!-- Bottom Navigation Colors -->
    <color name="bottom_nav_background">#ff25242b</color>
    <color name="bottom_nav_selected">#FFFFFF</color>
    <color name="bottom_nav_unselected">#ff9d9fa3</color>
    <color name="bottom_nav_selected_background">#ff000000</color>

    <!-- New Login UI Colors -->
    <color name="login_privacy_text">#8fffffff</color>
    <color name="login_agreement_text">#a3ffffff</color>
    <color name="facebook_button_bg">#ff4c7ae9</color>

    <!-- Phone Login Colors -->
    <color name="phone_input_bg_normal">#14ffffff</color>
    <color name="phone_input_border_normal">#8fffffff</color>
    <color name="phone_input_bg_focused">#14b936cf</color>
    <color name="phone_input_border_focused">#ffb936cf</color>
    <color name="phone_input_shadow">#3ddf00ff</color>
    <color name="send_code_button_bg">#B936CF</color>
    <color name="phone_input_placeholder">#7affffff</color>

    <!-- Verification Code Colors -->
    <color name="verification_subtitle_text">#b8ffffff</color>
    <color name="otp_line_normal">#8fffffff</color>
    <color name="otp_line_focused">#ffffff</color>
    <color name="send_again_button_disabled">#ccffffff</color>
    <color name="send_again_button_pressed">#f0f0f0</color>

    <!-- Continue Watching Colors -->
    <color name="continue_watching_watched_color">#FFE53E3E</color>
    <color name="continue_watching_unwatched_color">#8FFFFFFF</color>

    <!-- Continue Playing Button Colors -->
    <color name="continue_playing_button_bg">#B936CF</color>
    <color name="continue_playing_button_pressed">#9A2BAD</color>
    <color name="continue_playing_button_text">#FFFFFF</color>

    <!-- Video Player Colors -->
    <color name="video_player_background">#5C000000</color> <!-- rgba(0, 0, 0, 0.36) -->
    <color name="video_player_control_bg">#33000000</color> <!-- rgba(0, 0, 0, 0.20) -->
    <color name="video_player_text_white">#FFFFFF</color>
    <color name="video_player_text_secondary">#B8FFFFFF</color> <!-- rgba(255, 255, 255, 0.72) -->
    <color name="video_player_text_tertiary">#A3FFFFFF</color> <!-- rgba(255, 255, 255, 0.64) -->
    <color name="video_player_like_red">#F12626</color>
    <color name="video_player_progress_bg">#434343</color>
    <color name="video_player_progress_watched">#FFFFFF</color>
    <color name="video_player_selection_bg">#1FFFFFFF</color> <!-- rgba(255, 255, 255, 0.12) -->
    <color name="video_player_border">#24FFFFFF</color> <!-- rgba(255, 255, 255, 0.14) -->
    <color name="video_player_popup_bg">#25242B</color> <!-- RGBA(37, 36, 43, 1) -->
    <color name="video_player_popup_selected_bg">#FFFFFF</color>
    <color name="video_player_popup_selected_text">#000000</color>
    <color name="video_player_accent">#B936CF</color> <!-- Video player accent color for progress bars and buttons -->
    <color name="video_player_error_color">#F12626</color> <!-- Error color for video player -->
    <color name="video_player_background_dark">#1A1A1A</color> <!-- Dark background for video player -->
    <color name="video_player_primary_text">#FFFFFF</color> <!-- Primary text color for video player -->

    <!-- Subtitle Panel Colors -->
    <color name="subtitle_panel_background">#25242B</color>
    <color name="subtitle_panel_text_primary">#FFFFFF</color>
    <color name="subtitle_panel_text_secondary">#CCFFFFFF</color> <!-- rgba(255, 255, 255, 0.80) -->
    <color name="subtitle_panel_language_border">#5CFFFFFF</color> <!-- rgba(255, 255, 255, 0.36) -->
    <color name="subtitle_panel_language_selected_bg">#FFFFFF</color>
    <color name="subtitle_panel_language_selected_text">#141414</color>
    <color name="subtitle_panel_language_selected_border_start">#AF1E3C</color>
    <color name="subtitle_panel_language_selected_border_end">#2403AC</color>

    <!-- Popup Divider Color -->
    <color name="popup_divider_color">#434343</color>
    <color name="video_player_secondary_text">#B8FFFFFF</color> <!-- Secondary text color for video player -->

    <!-- Episode Selection Popup Colors -->
    <color name="episode_selection_popup_bg">#25242B</color> <!-- 弹窗背景色 -->
    <color name="episode_selection_divider">#3DFFFFFF</color> <!-- 分割线颜色 rgba(255, 255, 255, 0.24) -->
    <color name="episode_unavailable_bg">#E0242424</color> <!-- 不可观看状态背景 rgba(36, 36, 36, 0.88) -->
    <color name="episode_unavailable_border">#5CFFFFFF</color> <!-- 不可观看状态边框 rgba(255, 255, 255, 0.36) -->
    <color name="episode_unavailable_text">#5CFFFFFF</color> <!-- 不可观看状态文字 rgba(255, 255, 255, 0.36) -->

    <!-- Feedback Page Colors -->
    <color name="feedback_required_star_color">#fff12626</color> <!-- 必填星号颜色 -->
    <color name="feedback_label_text_color">#ffffffff</color> <!-- 标签文本颜色 -->
    <color name="feedback_type_selected_bg">#ffffffff</color> <!-- 选中类型背景色 -->
    <color name="feedback_type_selected_text">#ff141414</color> <!-- 选中类型文本颜色 -->
    <color name="feedback_type_unselected_bg">#3dffffff</color> <!-- 未选中类型背景色 -->
    <color name="feedback_type_unselected_text">#ccffffff</color> <!-- 未选中类型文本颜色 -->
    <color name="feedback_input_bg">#14ffffff</color> <!-- 输入框背景色 -->
    <color name="feedback_input_stroke">#8fffffff</color> <!-- 输入框边框颜色 -->
    <color name="feedback_input_text">#7affffff</color> <!-- 输入框文本颜色 -->
    <color name="feedback_input_hint">#7affffff</color> <!-- 输入框提示文本颜色 -->
    <color name="feedback_char_count_color">#52ffffff</color> <!-- 字符计数颜色 -->
    <color name="feedback_button_bg">#ffb936cf</color> <!-- 提交按钮背景色 -->
    <color name="feedback_button_text">#ffffffff</color> <!-- 提交按钮文本颜色 -->

    <!-- Video Poster Popup Colors -->
    <color name="video_poster_overlay_bg">#A3000000</color> <!-- rgba(0, 0, 0, 0.64) -->

    <!-- VIP Unlock Popup Colors -->
    <color name="vip_unlock_overlay_bg">#80000000</color>
    <color name="vip_unlock_container_bg">#1A1A1A</color>
    <color name="vip_unlock_text_primary">#FFFFFF</color>
    <color name="vip_unlock_text_secondary">#B3FFFFFF</color>
    <color name="vip_unlock_button_bg">#B936CF</color>
    <color name="vip_unlock_button_text">#FFFFFF</color>
    <color name="vip_unlock_coin_text">#FED956</color>

    <!-- Recharge Popup Colors -->
    <color name="recharge_popup_bg">#000000</color>
    <color name="recharge_popup_overlay_bg">#80000000</color>
    <color name="recharge_text_primary">#FFFFFF</color>
    <color name="recharge_text_secondary">#B3FFFFFF</color>
    <color name="recharge_text_tertiary">#5CFFFFFF</color>
    <color name="recharge_coin_bonus">#96FED956</color>
    <color name="recharge_amount_unselected_bg">#25242B</color>
    <color name="recharge_amount_selected_bg">#000000</color>
    <color name="recharge_amount_selected_border">#B936CF</color>
    <color name="recharge_vip_gradient_start">#D53A5A</color>
    <color name="recharge_vip_gradient_end">#3A0EEE</color>
    <color name="recharge_vip_border">#7AB936CF</color>
    <color name="recharge_vip_text_secondary">#B8FFFFFF</color>
    <color name="recharge_vip_text_tertiary">#A3130015</color>
    <color name="recharge_agreement_text">#A3FFFFFF</color>
</resources>
