<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <stroke android:width="@dimen/points_card_selected_stroke_width" android:color="#ffb936cf" />
            <solid android:color="#ff000000" />
            <corners android:radius="@dimen/points_card_corner_radius" />
        </shape>
    </item>

    <!-- 未选中状态 -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="#ff25242b" />
            <corners android:radius="@dimen/points_card_corner_radius" />
        </shape>
    </item>

</selector>
