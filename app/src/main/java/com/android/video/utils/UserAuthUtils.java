package com.android.video.utils;

import com.android.video.model.UserModel;

/**
 * 用户认证工具类 - 处理静态认证逻辑
 * <AUTHOR>
 */
public class UserAuthUtils {

    // Super Admin 手机号
    private static final String SUPER_ADMIN_PHONE = "18407151430";

    // Super Admin 用户信息
    private static final String SUPER_ADMIN_USERNAME = "Super Admin";
    private static final String SUPER_ADMIN_UID = "18407151";
    private static final int SUPER_ADMIN_VIP_STATUS = 1;
    private static final String SUPER_ADMIN_EMAIL = "<EMAIL>";
    // 可以设置默认头像路径
    private static final String SUPER_ADMIN_AVATAR = "";
    private static final int SUPER_ADMIN_POINTS = 9999;
    
    /**
     * @deprecated 此方法已弃用，请使用后端登录接口进行认证
     * 根据手机号进行静态认证（仅用于开发环境测试）
     * @param phoneNumber 用户输入的手机号
     * @return 认证后的用户模型，如果不是特殊用户则返回普通用户信息
     */
    @Deprecated
    public static UserModel authenticateUser(String phoneNumber) {
        android.util.Log.w("UserAuthUtils", "⚠️ WARNING: Static authentication is deprecated! Use AuthApiUtils.phoneLogin() instead");
        android.util.Log.d("UserAuthUtils", "authenticateUser called with: " + phoneNumber);

        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return createGuestUser();
        }

        // 清理手机号格式（移除空格、横线、括号等）
        String cleanPhoneNumber = cleanPhoneNumber(phoneNumber);
        android.util.Log.d("UserAuthUtils", "Cleaned phone number: " + cleanPhoneNumber);
        android.util.Log.d("UserAuthUtils", "Super Admin phone: " + SUPER_ADMIN_PHONE);

        // 检查是否为Super Admin
        if (SUPER_ADMIN_PHONE.equals(cleanPhoneNumber)) {
            android.util.Log.d("UserAuthUtils", "Creating Super Admin user");
            return createSuperAdminUser(cleanPhoneNumber);
        }

        // 其他手机号创建普通用户
        android.util.Log.d("UserAuthUtils", "Creating regular user");
        return createRegularUser(cleanPhoneNumber);
    }
    
    /**
     * 创建Super Admin用户
     * @param phoneNumber 手机号
     * @return Super Admin用户模型
     */
    private static UserModel createSuperAdminUser(String phoneNumber) {
        android.util.Log.d("UserAuthUtils", "Creating Super Admin with VIP status: " + SUPER_ADMIN_VIP_STATUS);

        UserModel user = new UserModel(
            SUPER_ADMIN_USERNAME,
            SUPER_ADMIN_UID,
            phoneNumber,
            SUPER_ADMIN_VIP_STATUS,
            SUPER_ADMIN_EMAIL,
            SUPER_ADMIN_AVATAR,
            SUPER_ADMIN_POINTS,
            true
        );

        android.util.Log.d("UserAuthUtils", "Created user - VIP status: " + user.getUserVip() + ", isVip(): " + user.isVip());
        return user;
    }
    
    /**
     * 创建普通用户
     * @param phoneNumber 手机号
     * @return 普通用户模型
     */
    private static UserModel createRegularUser(String phoneNumber) {
        // 生成普通用户的UID（基于手机号后6位）
        String uid = generateRegularUserUid(phoneNumber);
        
        // 用户名：User + UID后4位
        String username = "User" + uid.substring(uid.length() - 4);
        // 默认邮箱
        String email = "user" + uid + "@videoplayer.com";

        UserModel user = new UserModel(
            username,
            uid,
            phoneNumber,
            0, // 普通用户，非VIP
            email,
            "", // 默认头像
            100, // 默认积分
            true
        );
        return user;
    }
    
    /**
     * 创建访客用户
     * @return 访客用户模型
     */
    private static UserModel createGuestUser() {
        // 使用默认构造函数创建访客用户
        return new UserModel();
    }
    
    /**
     * 清理手机号格式
     * @param phoneNumber 原始手机号
     * @return 清理后的手机号
     */
    private static String cleanPhoneNumber(String phoneNumber) {
        if (phoneNumber == null) {
            return "";
        }

        // 移除所有非数字字符
        String cleaned = phoneNumber.replaceAll("[^\\d]", "");

        // 特殊处理：如果是Super Admin手机号，直接返回
        if (SUPER_ADMIN_PHONE.equals(cleaned)) {
            return cleaned;
        }

        // 处理不同国家代码格式
        // 中国手机号：移除86前缀
        if (cleaned.startsWith("86") && cleaned.length() == 13) {
            cleaned = cleaned.substring(2);
        }
        // 美国手机号：移除1前缀
        else if (cleaned.startsWith("1") && cleaned.length() == 11) {
            cleaned = cleaned.substring(1);
        }
        // 俄罗斯手机号：移除7前缀
        else if (cleaned.startsWith("7") && cleaned.length() == 11) {
            cleaned = cleaned.substring(1);
        }

        return cleaned;
    }
    
    /**
     * 为普通用户生成UID
     * @param phoneNumber 手机号
     * @return 生成的UID
     */
    private static String generateRegularUserUid(String phoneNumber) {
        if (phoneNumber == null || phoneNumber.length() < 6) {
            // 如果手机号不足6位，生成随机UID
            return String.valueOf(System.currentTimeMillis() % 100000000);
        }

        // 使用手机号后8位作为UID，如果不足8位则补0
        String lastDigits;
        if (phoneNumber.length() >= 8) {
            lastDigits = phoneNumber.substring(phoneNumber.length() - 8);
        } else {
            lastDigits = String.format("%08d", Integer.parseInt(phoneNumber));
        }

        return lastDigits;
    }
    
    /**
     * 检查用户是否为Super Admin
     * @param phoneNumber 手机号
     * @return 是否为Super Admin
     */
    public static boolean isSuperAdmin(String phoneNumber) {
        return SUPER_ADMIN_PHONE.equals(cleanPhoneNumber(phoneNumber));
    }
    
    /**
     * 检查用户是否为VIP
     * @param user 用户模型
     * @return 是否为VIP
     */
    public static boolean isVipUser(UserModel user) {
        return user != null && user.isVip();
    }
    
    /**
     * 验证用户登录状态
     * @param user 用户模型
     * @return 是否已登录
     */
    public static boolean isUserLoggedIn(UserModel user) {
        return user != null && user.isLoggedIn();
    }
}
