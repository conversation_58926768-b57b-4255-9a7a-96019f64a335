package com.android.video.ui.activity;

import android.app.AlertDialog;
import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.appcompat.app.AppCompatActivity;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;

import com.android.video.R;
import com.android.video.model.DownloadVideo;
import com.android.video.model.Episode;
import com.android.video.model.response.DownloadListResponseModel;
import com.android.video.model.response.DownloadChapterResponseModel;
import com.android.video.model.response.DeleteChapterResponseModel;
import com.android.video.model.response.DownloadProgressResponseModel;
import com.android.video.network.DownloadListApiService;
import com.android.video.network.DownloadChapterApiService;
import com.android.video.network.DownloadProgressApiService;
import com.android.video.ui.activity.VideoDownloadDetailActivity;
import com.android.video.ui.activity.DownloadVideoPlayerActivity;
import com.android.video.ui.adapter.DownloadVideoAdapter;
import com.android.video.ui.adapter.EpisodeAdapter;
import com.google.android.material.bottomsheet.BottomSheetDialog;
import com.google.android.material.bottomsheet.BottomSheetBehavior;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * 下载页面Activity - 集成真实API数据和分页功能
 * <AUTHOR> Team
 */
public class DownloadActivity extends BaseFullScreenActivity implements DownloadVideoAdapter.OnDownloadVideoClickListener {

    private static final String TAG = "DownloadActivity";
    private static final int PAGE_SIZE = 10;

    private ImageView ivBack;
    private TextView tvTitle;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView rvDownloadedVideos;
    private LinearLayout layoutEmptyState;
    private SwipeRefreshLayout swipeRefreshLoading;

    private DownloadVideoAdapter adapter;
    private List<DownloadVideo> downloadedVideos;
    private DownloadListApiService apiService;
    private DownloadChapterApiService chapterApiService;
    private DownloadProgressApiService progressApiService;

    // 底部弹窗相关
    private EpisodeAdapter currentEpisodeAdapter;
    private List<Episode> currentEpisodes;

    // 分页相关
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_download);

        initViews();
        initData();
        setupRecyclerView();
        setupClickListeners();
        setupSwipeRefresh();

        // 加载第一页数据
        loadDownloadList(true);
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        rvDownloadedVideos = findViewById(R.id.rv_downloaded_videos);
        layoutEmptyState = findViewById(R.id.layout_empty_state);
        swipeRefreshLoading = findViewById(R.id.swipe_refresh_loading);
    }

    private void initData() {
        downloadedVideos = new ArrayList<>();
        apiService = DownloadListApiService.getInstance();
        chapterApiService = DownloadChapterApiService.getInstance();
        progressApiService = DownloadProgressApiService.getInstance();
    }

    private void setupSwipeRefresh() {
        swipeRefreshLayout.setColorSchemeColors(
            getResources().getColor(android.R.color.white, null)
        );
        swipeRefreshLayout.setProgressBackgroundColorSchemeColor(
            getResources().getColor(android.R.color.black, null)
        );

        swipeRefreshLayout.setOnRefreshListener(() -> {
            Log.d(TAG, "下拉刷新触发");
            refreshData();
        });
    }

    private void setupRecyclerView() {
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvDownloadedVideos.setLayoutManager(layoutManager);

        adapter = new DownloadVideoAdapter(downloadedVideos, this);
        rvDownloadedVideos.setAdapter(adapter);

        // 添加滚动监听器实现上拉加载更多
        rvDownloadedVideos.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0) { // 向下滚动
                    LinearLayoutManager layoutManager = (LinearLayoutManager) recyclerView.getLayoutManager();
                    if (layoutManager != null) {
                        int visibleItemCount = layoutManager.getChildCount();
                        int totalItemCount = layoutManager.getItemCount();
                        int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

                        // 当滚动到底部附近时加载更多数据
                        if (!isLoading && hasMoreData &&
                            (visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 2) {
                            Log.d(TAG, "触发上拉加载更多");
                            loadMoreData();
                        }
                    }
                }
            }
        });
    }

    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());
    }

    /**
     * 刷新数据（下拉刷新）
     */
    private void refreshData() {
        currentPage = 1;
        hasMoreData = true;
        loadDownloadList(true);
    }

    /**
     * 加载更多数据（上拉加载）
     */
    private void loadMoreData() {
        if (hasMoreData && !isLoading) {
            currentPage++;
            loadDownloadList(false);
        }
    }

    /**
     * 加载下载列表数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadDownloadList(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;

        // 显示加载状态
        if (isRefresh) {
            if (downloadedVideos.isEmpty()) {
                showLoadingState();
            } else {
                swipeRefreshLayout.setRefreshing(true);
            }
        }

        Log.d(TAG, "开始加载下载列表，页码: " + currentPage + ", 每页数量: " + PAGE_SIZE);

        apiService.getDownloadList(currentPage, PAGE_SIZE, new DownloadListApiService.DownloadListCallback() {
            @Override
            public void onSuccess(DownloadListResponseModel response) {
                isLoading = false;
                swipeRefreshLayout.setRefreshing(false);

                Log.d(TAG, "下载列表加载成功: " + response.toString());

                if (response.isSuccess() && response.getData() != null) {
                    List<DownloadVideo> newVideos = response.getData().toDownloadVideoList();

                    if (isRefresh) {
                        // 刷新时清空现有数据
                        downloadedVideos.clear();
                    }

                    // 添加新数据
                    downloadedVideos.addAll(newVideos);

                    // 更新分页状态
                    hasMoreData = response.getData().hasMorePages();

                    // 更新UI
                    adapter.notifyDataSetChanged();
                    updateUI();

                    Log.d(TAG, "数据更新完成，当前总数: " + downloadedVideos.size() +
                             ", 是否还有更多: " + hasMoreData);
                } else {
                    showError("获取数据失败: " + response.getErrorMessage());
                }
            }

            @Override
            public void onError(String errorMessage) {
                isLoading = false;
                swipeRefreshLayout.setRefreshing(false);

                Log.e(TAG, "下载列表加载失败: " + errorMessage);
                showError("加载失败: " + errorMessage);

                // 如果是第一页加载失败，显示错误状态
                if (currentPage == 1 && downloadedVideos.isEmpty()) {
                    updateUI();
                }
            }
        });
    }

    /**
     * 显示加载状态
     */
    private void showLoadingState() {
        rvDownloadedVideos.setVisibility(View.GONE);
        layoutEmptyState.setVisibility(View.GONE);
        swipeRefreshLoading.setVisibility(View.VISIBLE);
        swipeRefreshLoading.setRefreshing(true);
    }

    /**
     * 更新UI状态
     */
    private void updateUI() {
        if (downloadedVideos.isEmpty()) {
            rvDownloadedVideos.setVisibility(View.GONE);
            swipeRefreshLoading.setVisibility(View.GONE);
            swipeRefreshLoading.setRefreshing(false);
            layoutEmptyState.setVisibility(View.VISIBLE);
        } else {
            layoutEmptyState.setVisibility(View.GONE);
            swipeRefreshLoading.setVisibility(View.GONE);
            swipeRefreshLoading.setRefreshing(false);
            rvDownloadedVideos.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 显示错误信息
     */
    private void showError(String message) {
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();
    }

    /**
     * 更新剧集下载进度
     */
    private void updateEpisodesDownloadProgress(List<Episode> episodes, EpisodeAdapter adapter) {
        if (episodes == null || episodes.isEmpty()) {
            return;
        }

        for (int i = 0; i < episodes.size(); i++) {
            Episode episode = episodes.get(i);

            // 只查询正在下载的剧集进度
            if (episode.isDownloading() && episode.getDownloadRecordId() != null &&
                !episode.getDownloadRecordId().trim().isEmpty()) {

                final int position = i;
                progressApiService.getDownloadProgress(episode.getDownloadRecordId(),
                    new DownloadProgressApiService.DownloadProgressCallback() {
                        @Override
                        public void onSuccess(DownloadProgressResponseModel response) {
                            if (response.isSuccess() && response.hasData()) {
                                DownloadProgressResponseModel.DownloadProgressData data = response.getData();

                                // 更新剧集的下载状态和进度
                                episode.setDownloading(data.isDownloading());
                                episode.setDownloadProgress(data.getProgress());

                                if (data.isCompleted()) {
                                    episode.setDownloaded(true);
                                    episode.setDownloading(false);
                                    episode.setDownloadProgress(100);
                                }

                                // 更新文件大小信息
                                if (data.getTotalSize() > 0) {
                                    episode.setFileSizeBytes(data.getTotalSize());
                                }

                                // 通知适配器更新特定位置
                                adapter.notifyItemChanged(position);

                                Log.d(TAG, "更新剧集进度: " + episode.getEpisodeNumber() +
                                          ", 进度: " + data.getProgress() + "%");
                            }
                        }

                        @Override
                        public void onError(String errorMessage) {
                            Log.w(TAG, "获取剧集下载进度失败: " + episode.getEpisodeNumber() +
                                      ", 错误: " + errorMessage);
                        }
                    });
            }
        }
    }

    @Override
    public void onPlayClick(DownloadVideo video) {
        showVideoEpisodesBottomSheet(video);
    }

    @Override
    public void onDeleteClick(DownloadVideo video) {
        showDeleteConfirmDialog(video);
    }

    @Override
    public void onVideoClick(DownloadVideo video) {
        // 点击视频项时不做任何操作，或者可以显示简单的信息
        // Toast.makeText(this, "Video: " + video.getTitle(), Toast.LENGTH_SHORT).show();
    }

    private void showDeleteConfirmDialog(DownloadVideo video) {
        new AlertDialog.Builder(this)
            .setTitle("Delete Download")
            .setMessage("Are you sure you want to delete \"" + video.getTitle() + "\"?")
            .setPositiveButton("Delete", (dialog, which) -> {
                deleteVideo(video);
            })
            .setNegativeButton("Cancel", null)
            .show();
    }

    private void deleteVideo(DownloadVideo video) {
        // 检查是否有filmLanguageInfoId
        if (video.getFilmLanguageInfoId() == null || video.getFilmLanguageInfoId().trim().isEmpty()) {
            showError("无法删除：缺少短剧语言信息ID");
            return;
        }

        Log.d(TAG, "开始删除短剧，filmLanguageInfoId: " + video.getFilmLanguageInfoId());

        // 调用API删除短剧
        chapterApiService.deleteDownloadFilm(video.getFilmLanguageInfoId(),
            new DownloadChapterApiService.DeleteFilmCallback() {
                @Override
                public void onSuccess(DeleteChapterResponseModel response) {
                    Log.d(TAG, "删除短剧成功: " + response.toString());

                    if (response.isSuccess() && response.isDeleteSuccess()) {
                        // API删除成功，更新本地数据
                        int position = downloadedVideos.indexOf(video);
                        if (position != -1) {
                            // 删除本地文件
                            if (video.getFilePath() != null && !video.getFilePath().isEmpty()) {
                                File file = new File(video.getFilePath());
                                if (file.exists()) {
                                    boolean deleted = file.delete();
                                    Log.d(TAG, "Local file deleted: " + deleted + " - " + video.getFilePath());
                                }
                            }

                            adapter.removeVideo(position);
                            updateUI();

                            Toast.makeText(DownloadActivity.this,
                                "\"" + video.getTitle() + "\" deleted", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        showError("删除失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "删除短剧失败: " + errorMessage);
                    showError("删除失败: " + errorMessage);
                }
            });
    }

    private void showVideoEpisodesBottomSheet(DownloadVideo video) {
        BottomSheetDialog bottomSheetDialog = new BottomSheetDialog(this);
        View bottomSheetView = LayoutInflater.from(this).inflate(R.layout.bottom_sheet_video_episodes, null);

        // 设置弹窗高度为屏幕高度的75%
        int screenHeight = getResources().getDisplayMetrics().heightPixels;
        int bottomSheetHeight = (int) (screenHeight * 0.75);

        // 设置底部弹窗的高度
        bottomSheetView.setLayoutParams(new ViewGroup.LayoutParams(ViewGroup.LayoutParams.MATCH_PARENT, bottomSheetHeight));
        bottomSheetDialog.setContentView(bottomSheetView);

        // 设置弹窗行为
        BottomSheetBehavior<FrameLayout> behavior = bottomSheetDialog.getBehavior();
        behavior.setPeekHeight(bottomSheetHeight);
        behavior.setState(BottomSheetBehavior.STATE_EXPANDED);
        behavior.setSkipCollapsed(true);

        // 初始化视图
        ImageView ivClose = bottomSheetView.findViewById(R.id.iv_close);
        ImageView ivPoster = bottomSheetView.findViewById(R.id.iv_poster);
        TextView tvVideoTitle = bottomSheetView.findViewById(R.id.tv_video_title);
        TextView tvDownloadedStatus = bottomSheetView.findViewById(R.id.tv_downloaded_status);
        TextView tvDownloadedCount = bottomSheetView.findViewById(R.id.tv_downloaded_count);
        TextView tvTotalCount = bottomSheetView.findViewById(R.id.tv_total_count);
        TextView tvTotalFileSize = bottomSheetView.findViewById(R.id.tv_total_file_size);
        LinearLayout btnPlay = bottomSheetView.findViewById(R.id.btn_play);
        LinearLayout llEpisodeProgress = bottomSheetView.findViewById(R.id.ll_episode_progress);
        RecyclerView rvEpisodes = bottomSheetView.findViewById(R.id.rv_episodes);

        // 设置视频信息
        // 设置海报图片，与下载页面中的视频项保持一致
        if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
            Glide.with(this)
                    .load(video.getPosterUrl())
                    .placeholder(R.drawable.movie_poster)
                    .error(R.drawable.movie_poster)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(ivPoster);
        } else {
            ivPoster.setImageResource(R.drawable.movie_poster);
        }
        tvVideoTitle.setText(video.getTitle());
        tvDownloadedStatus.setText("Downloaded/Total episodes");
        tvDownloadedCount.setText("EP." + video.getDownloadedEpisodes());
        tvTotalCount.setText("EP." + video.getTotalEpisodes());
        tvTotalFileSize.setText(video.getFormattedFileSize());

        // 创建剧集数据列表
        currentEpisodes = new ArrayList<>();

        // 设置剧集列表
        LinearLayoutManager layoutManager = new LinearLayoutManager(this);
        rvEpisodes.setLayoutManager(layoutManager);

        // 创建适配器
        currentEpisodeAdapter = new EpisodeAdapter(currentEpisodes, new EpisodeAdapter.OnEpisodeClickListener() {
            @Override
            public void onPlayClick(Episode episode) {
                // 跳转到已下载视频播放页面，播放指定剧集
                Intent intent = new Intent(DownloadActivity.this, DownloadVideoPlayerActivity.class);
                intent.putExtra(DownloadVideoPlayerActivity.EXTRA_DOWNLOAD_VIDEO, video);
                // 可以添加剧集信息，但当前DownloadVideoPlayerActivity可能不支持
                startActivity(intent);
                bottomSheetDialog.dismiss();
            }

            @Override
            public void onDeleteClick(Episode episode) {
                new AlertDialog.Builder(DownloadActivity.this)
                    .setTitle("Delete Episode")
                    .setMessage("Are you sure you want to delete \"" + episode.getEpisodeNumber() + "\"?")
                    .setPositiveButton("Delete", (dialog, which) -> {
                        deleteEpisodeFromBottomSheet(episode, currentEpisodeAdapter, currentEpisodes);
                    })
                    .setNegativeButton("Cancel", null)
                    .show();
            }

            @Override
            public void onEpisodeClick(Episode episode) {
                Toast.makeText(DownloadActivity.this, "Episode info: " + episode.getEpisodeNumber(), Toast.LENGTH_SHORT).show();
            }
        });

        rvEpisodes.setAdapter(currentEpisodeAdapter);

        // 设置关闭按钮点击事件
        ivClose.setOnClickListener(v -> bottomSheetDialog.dismiss());

        // 设置播放按钮点击事件
        btnPlay.setOnClickListener(v -> {
            // 跳转到已下载视频播放页面
            Intent intent = new Intent(this, DownloadVideoPlayerActivity.class);
            intent.putExtra(DownloadVideoPlayerActivity.EXTRA_DOWNLOAD_VIDEO, video);
            startActivity(intent);
            bottomSheetDialog.dismiss();
        });

        // 设置集数进度容器点击事件（滚动到集数列表）
        llEpisodeProgress.setOnClickListener(v -> {
            // 平滑滚动到集数列表顶部
            rvEpisodes.smoothScrollToPosition(0);
        });

        bottomSheetDialog.show();

        // 加载真实的章节数据
        loadChapterDetailsForBottomSheet(video, currentEpisodes, currentEpisodeAdapter);
    }

    /**
     * 为底部弹窗加载章节详情数据
     */
    private void loadChapterDetailsForBottomSheet(DownloadVideo video, List<Episode> episodes, EpisodeAdapter adapter) {
        if (video.getFilmLanguageInfoId() == null || video.getFilmLanguageInfoId().trim().isEmpty()) {
            Log.w(TAG, "filmLanguageInfoId为空，无法加载章节详情");
            return;
        }

        Log.d(TAG, "开始加载底部弹窗章节详情，filmLanguageInfoId: " + video.getFilmLanguageInfoId());

        chapterApiService.getDownloadChapterDetails(video.getFilmLanguageInfoId(), 1, 50,
            new DownloadChapterApiService.DownloadChapterCallback() {
                @Override
                public void onSuccess(DownloadChapterResponseModel response) {
                    Log.d(TAG, "底部弹窗章节详情加载成功: " + response.toString());

                    if (response.isSuccess() && response.getData() != null) {
                        List<Episode> newEpisodes = response.getData().toEpisodeList();

                        // 清空现有数据并添加新数据
                        episodes.clear();
                        episodes.addAll(newEpisodes);

                        // 更新适配器
                        adapter.notifyDataSetChanged();

                        // 更新下载进度
                        updateEpisodesDownloadProgress(episodes, adapter);

                        Log.d(TAG, "底部弹窗数据更新完成，章节数: " + episodes.size());
                    } else {
                        Log.e(TAG, "获取章节详情失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "底部弹窗章节详情加载失败: " + errorMessage);
                }
            });
    }

    /**
     * 从底部弹窗删除章节
     */
    private void deleteEpisodeFromBottomSheet(Episode episode, EpisodeAdapter adapter, List<Episode> episodes) {
        if (episode.getDownloadRecordId() == null || episode.getDownloadRecordId().trim().isEmpty()) {
            showError("无法删除：缺少下载记录ID");
            return;
        }

        Log.d(TAG, "开始从底部弹窗删除章节，downloadRecordId: " + episode.getDownloadRecordId());

        chapterApiService.deleteDownloadChapter(episode.getDownloadRecordId(),
            new DownloadChapterApiService.DeleteChapterCallback() {
                @Override
                public void onSuccess(DeleteChapterResponseModel response) {
                    Log.d(TAG, "底部弹窗删除章节成功: " + response.toString());

                    if (response.isSuccess() && response.isDeleteSuccess()) {
                        // API删除成功，更新本地数据
                        int position = episodes.indexOf(episode);
                        if (position != -1) {
                            episodes.remove(position);
                            adapter.notifyItemRemoved(position);

                            Toast.makeText(DownloadActivity.this,
                                "\"" + episode.getEpisodeNumber() + "\" deleted", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        showError("删除失败: " + response.getErrorMessage());
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "底部弹窗删除章节失败: " + errorMessage);
                    showError("删除失败: " + errorMessage);
                }
            });
    }
}
