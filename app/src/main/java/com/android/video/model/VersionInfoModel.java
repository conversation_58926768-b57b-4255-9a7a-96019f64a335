package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import android.util.Log;

/**
 * 版本信息数据模型
 * <p>
 * 用于映射版本检查API返回的数据结构，包含版本号、发布状态、
 * 更新描述、下载地址等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VersionInfoModel {

    private static final String TAG = "VersionInfoModel";

    /**
     * 版本ID
     */
    @SerializedName("versionId")
    private String versionId;

    /**
     * 客户端类型 (1=Android, 2=iOS)
     */
    @SerializedName("clientType")
    private int clientType;

    /**
     * 版本号 (如: v1.0.3)
     */
    @SerializedName("versionCode")
    private String versionCode;

    /**
     * 发布状态 (0=待发布, 1=已发布)
     */
    @SerializedName("versionStatus")
    private int versionStatus;

    /**
     * 发布时间
     */
    @SerializedName("releaseTime")
    private String releaseTime;

    /**
     * 删除标记
     */
    @SerializedName("isDelete")
    private int isDelete;

    /**
     * 版本描述信息
     */
    @SerializedName("versionInfo")
    private String versionInfo;

    /**
     * 安装包下载路径
     */
    @SerializedName("packageUrl")
    private String packageUrl;

    /**
     * 创建时间
     */
    @SerializedName("createTime")
    private String createTime;

    /**
     * 修改时间
     */
    @SerializedName("updateTime")
    private String updateTime;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public VersionInfoModel() {
    }

    /**
     * 完整构造函数
     */
    public VersionInfoModel(String versionId, int clientType, String versionCode, 
                           int versionStatus, String releaseTime, int isDelete,
                           String versionInfo, String packageUrl, String createTime, String updateTime) {
        this.versionId = versionId;
        this.clientType = clientType;
        this.versionCode = versionCode;
        this.versionStatus = versionStatus;
        this.releaseTime = releaseTime;
        this.isDelete = isDelete;
        this.versionInfo = versionInfo;
        this.packageUrl = packageUrl;
        this.createTime = createTime;
        this.updateTime = updateTime;
    }

    // ========== Getter和Setter方法 ==========

    public String getVersionId() {
        return versionId;
    }

    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    public int getClientType() {
        return clientType;
    }

    public void setClientType(int clientType) {
        this.clientType = clientType;
    }

    public String getVersionCode() {
        return versionCode;
    }

    public void setVersionCode(String versionCode) {
        this.versionCode = versionCode;
    }

    public int getVersionStatus() {
        return versionStatus;
    }

    public void setVersionStatus(int versionStatus) {
        this.versionStatus = versionStatus;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getVersionInfo() {
        return versionInfo;
    }

    public void setVersionInfo(String versionInfo) {
        this.versionInfo = versionInfo;
    }

    public String getPackageUrl() {
        return packageUrl;
    }

    public void setPackageUrl(String packageUrl) {
        this.packageUrl = packageUrl;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    // ========== 业务逻辑方法 ==========

    /**
     * 检查版本是否已发布
     * @return 如果版本已发布返回true，否则返回false
     */
    public boolean isPublished() {
        return versionStatus == 1;
    }

    /**
     * 检查版本是否被删除
     * <p>
     * 注意：根据API文档，isDelete字段的含义可能与常规理解相反
     * 这里暂时假设 isDelete == 0 表示已删除，isDelete == 1 表示未删除
     * </p>
     * @return 如果版本被删除返回true，否则返回false
     */
    public boolean isDeleted() {
        // 根据API返回的示例数据，isDelete=1的版本应该是可用的
        // 所以这里假设 0=已删除，1=未删除
        return isDelete == 0;
    }

    /**
     * 检查版本是否可用
     * <p>
     * 版本可用的条件：已发布（不检查删除状态）
     * </p>
     * @return 如果版本可用返回true，否则返回false
     */
    public boolean isAvailable() {
        return isPublished();
    }

    /**
     * 比较版本号大小
     * <p>
     * 比较当前版本与给定版本的大小关系。
     * 支持语义化版本号比较 (如: v1.0.3 vs v1.0.2)
     * </p>
     * 
     * @param otherVersion 要比较的版本号
     * @return 如果当前版本更新返回正数，相同返回0，更旧返回负数
     */
    public int compareVersion(String otherVersion) {
        if (versionCode == null || otherVersion == null) {
            Log.w(TAG, "Version comparison failed: null version");
            return 0;
        }

        try {
            // 移除版本号前缀 (如 "v1.0.3" -> "1.0.3")
            String currentClean = versionCode.replaceFirst("^v", "");
            String otherClean = otherVersion.replaceFirst("^v", "");

            // 分割版本号
            String[] currentParts = currentClean.split("\\.");
            String[] otherParts = otherClean.split("\\.");

            // 比较每个部分
            int maxLength = Math.max(currentParts.length, otherParts.length);
            for (int i = 0; i < maxLength; i++) {
                int currentPart = i < currentParts.length ? Integer.parseInt(currentParts[i]) : 0;
                int otherPart = i < otherParts.length ? Integer.parseInt(otherParts[i]) : 0;

                if (currentPart != otherPart) {
                    return currentPart - otherPart;
                }
            }

            return 0; // 版本号相同
        } catch (NumberFormatException e) {
            Log.e(TAG, "Version comparison failed: invalid format", e);
            return 0;
        }
    }

    /**
     * 检查是否比给定版本更新
     * @param otherVersion 要比较的版本号
     * @return 如果当前版本更新返回true，否则返回false
     */
    public boolean isNewerThan(String otherVersion) {
        return compareVersion(otherVersion) > 0;
    }

    /**
     * 检查是否与给定版本相同
     * @param otherVersion 要比较的版本号
     * @return 如果版本号相同返回true，否则返回false
     */
    public boolean isSameVersion(String otherVersion) {
        return compareVersion(otherVersion) == 0;
    }

    /**
     * 获取简化的版本号
     * <p>
     * 移除版本号前缀，返回纯数字版本号
     * </p>
     * @return 简化的版本号 (如: "1.0.3")
     */
    public String getCleanVersionCode() {
        if (versionCode == null) {
            return "";
        }
        return versionCode.replaceFirst("^v", "");
    }

    /**
     * 验证数据完整性
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return versionId != null && !versionId.trim().isEmpty() &&
               versionCode != null && !versionCode.trim().isEmpty() &&
               clientType > 0;
    }

    @Override
    public String toString() {
        return "VersionInfoModel{" +
                "versionId='" + versionId + '\'' +
                ", clientType=" + clientType +
                ", versionCode='" + versionCode + '\'' +
                ", versionStatus=" + versionStatus +
                ", releaseTime='" + releaseTime + '\'' +
                ", isDelete=" + isDelete +
                ", versionInfo='" + versionInfo + '\'' +
                ", packageUrl='" + packageUrl + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
