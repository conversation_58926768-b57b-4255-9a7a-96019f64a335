<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
          xmlns:tools="http://schemas.android.com/tools">

    <!-- Network permissions -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- Location permissions -->
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />

    <!-- Camera and Storage permissions -->
    <uses-permission android:name="android.permission.CAMERA" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_MEDIA_IMAGES" />

    <!-- Floating window permissions -->
    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />

    <!-- Download permissions -->
    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE"
        tools:ignore="ScopedStorage" />
    <uses-permission android:name="android.permission.WAKE_LOCK" />
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_DATA_SYNC" />

    <!-- Package visibility permissions for Android 11+ -->
    <!-- 用于检测TikTok和VKontakte应用是否已安装 -->
    <queries>
        <!-- TikTok packages -->
        <package android:name="com.zhiliaoapp.musically" />
        <package android:name="com.ss.android.ugc.trill" />
        <package android:name="com.ss.android.ugc.aweme" />

        <!-- VKontakte packages -->
        <package android:name="com.vkontakte.android" />

        <!-- 通用查询模式 - 用于检测包含特定关键词的应用 -->
        <intent>
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent>
    </queries>
    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />

    <application
            android:name=".VideoPlayerApplication"
            android:allowBackup="true"
            android:dataExtractionRules="@xml/data_extraction_rules"
            android:fullBackupContent="@xml/backup_rules"
            android:icon="@mipmap/ic_launcher"
            android:label="@string/app_name"
            android:roundIcon="@mipmap/ic_launcher_round"
            android:supportsRtl="true"
            android:theme="@style/Theme.VideoPlayer"
            tools:targetApi="31">

        <!-- Facebook App ID -->
        <meta-data android:name="com.facebook.sdk.ApplicationId"
                   android:value="@string/facebook_app_id"/>

        <!-- Facebook Client Token -->
        <meta-data android:name="com.facebook.sdk.ClientToken"
                   android:value="@string/facebook_client_token"/>

        <!-- Facebook Login Activity -->
        <activity android:name="com.facebook.FacebookActivity"
                  android:configChanges="keyboard|keyboardHidden|screenLayout|screenSize|orientation"
                  android:label="@string/app_name" />

        <!-- Facebook Custom Tab Activity -->
        <activity android:name="com.facebook.CustomTabActivity"
                  android:exported="true">
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="@string/fb_login_protocol_scheme" />
            </intent-filter>
        </activity>
        <!-- Login Activity -->
        <activity
                android:name=".ui.activity.LoginActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">

            <!-- TikTok OAuth Callback Intent Filter -->
            <intent-filter>
                <action android:name="android.intent.action.VIEW" />
                <category android:name="android.intent.category.DEFAULT" />
                <category android:name="android.intent.category.BROWSABLE" />
                <data android:scheme="videoplayer"
                      android:host="tiktok.callback" />
            </intent-filter>
        </activity>

        <!-- Phone Login Activity -->
        <activity
                android:name=".ui.activity.PhoneLoginActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Verification Code Activity -->
        <activity
                android:name=".ui.activity.VerificationCodeActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Favorite Selection Activity -->
        <activity
                android:name=".ui.activity.FavoriteSelectionActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Main Activity - Main Entry Point -->
        <activity
                android:name=".ui.activity.MainActivity"
                android:exported="true"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:theme="@style/Theme.VideoPlayer.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN"/>
                <category android:name="android.intent.category.LAUNCHER"/>
            </intent-filter>
        </activity>

        <!-- Subscribe Activity -->
        <activity
                android:name=".ui.activity.SubscribeActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Points Activity -->
        <activity
                android:name=".ui.activity.PointsActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Bill Activity -->
        <activity
                android:name=".ui.activity.BillActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Video Detail Activity -->
        <activity
                android:name=".ui.activity.VideoDetailActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Video Preview Detail Activity -->
        <activity
                android:name=".ui.activity.VideoPreviewDetailActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Message Activity -->
        <activity
                android:name=".ui.activity.MessageActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Message Detail Activity -->
        <activity
                android:name=".ui.activity.MessageDetailActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Setting Activity -->
        <activity
                android:name=".ui.activity.SettingActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Information Activity -->
        <activity
                android:name=".ui.activity.InformationActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Download Activity -->
        <activity
                android:name=".ui.activity.DownloadActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Video Download Detail Activity -->
        <activity
                android:name=".ui.activity.VideoDownloadDetailActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Edit Profile Activity -->
        <activity
                android:name=".ui.activity.EditProfileActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Log Off Activity -->
        <activity
                android:name=".ui.activity.LogOffActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Feedback Activity -->
        <activity
                android:name=".ui.activity.FeedbackActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Search Activity -->
        <activity
                android:name=".ui.activity.SearchActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Search Result Activity -->
        <activity
                android:name=".ui.activity.SearchResultActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Video Player Activity -->
        <activity
                android:name=".ui.activity.VideoPlayerActivity"
                android:exported="false"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Download Video Player Activity -->
        <activity
                android:name=".ui.activity.DownloadVideoPlayerActivity"
                android:exported="false"
                android:configChanges="orientation|screenSize|keyboardHidden"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Most Popular Activity -->
        <activity
                android:name=".ui.activity.MostPopularActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Categories Activity -->
        <activity
                android:name=".ui.activity.CategoriesActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- Coming Soon Activity -->
        <activity
                android:name=".ui.activity.ComingSoonActivity"
                android:exported="false"
                android:screenOrientation="portrait"
                android:theme="@style/Theme.VideoPlayer.NoActionBar">
        </activity>

        <!-- FileProvider for camera -->
        <provider
                android:name="androidx.core.content.FileProvider"
                android:authorities="${applicationId}.fileprovider"
                android:exported="false"
                android:grantUriPermissions="true">
            <meta-data
                    android:name="android.support.FILE_PROVIDER_PATHS"
                    android:resource="@xml/file_paths" />
        </provider>

        <!-- 悬浮窗视频播放服务 -->
        <service
                android:name=".service.FloatingVideoService"
                android:enabled="true"
                android:exported="false"
                android:foregroundServiceType="mediaPlayback" />

        <!-- 下载服务 -->
        <service
                android:name=".service.DownloadService"
                android:enabled="true"
                android:exported="false"
                android:foregroundServiceType="dataSync" />

    </application>

</manifest>
