package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.HomeApiConstantsUtils;

/**
 * 今日热门排行榜数据模型
 * <p>
 * 用于映射今日热门排行榜API返回的短剧数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_DAILY_RANK}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmTitle: 短剧标题</li>
 *   <li>cover: 封面图片URL</li>
 *   <li>languageType: 语言类型（1=英语, 2=俄语）</li>
 *   <li>filmId: 短剧唯一标识符</li>
 *   <li>filmLanguageInfoId: 短剧语言信息ID</li>
 *   <li>releaseTime: 发布时间</li>
 *   <li>details: 短剧简介</li>
 *   <li>playNum: 播放次数</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * List&lt;DailyRankModel&gt; dailyRankList = gson.fromJson(jsonArray,
 *     new TypeToken&lt;List&lt;DailyRankModel&gt;&gt;(){}.getType());
 *
 * // 获取排行榜数据
 * for (DailyRankModel rankItem : dailyRankList) {
 *     String title = rankItem.getFilmTitle();
 *     String cover = rankItem.getCover();
 *     long playCount = rankItem.getPlayNum();
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_DAILY_RANK
 */
public class DailyRankModel {
    
    /**
     * 短剧标题
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_TITLE)
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_COVER)
    private String cover;
    
    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 短剧ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_ID)
    private String filmId;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_LANGUAGE_INFO_ID)
    private String filmLanguageInfoId;
    
    /**
     * 发布时间
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_RELEASE_TIME)
    private String releaseTime;
    
    /**
     * 短剧简介
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_DETAILS)
    private String details;
    
    /**
     * 播放次数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_PLAY_NUM)
    private Long playNum;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public DailyRankModel() {
    }

    /**
     * 完整构造函数
     */
    public DailyRankModel(String filmTitle, String cover, Integer languageType, 
                         String filmId, String filmLanguageInfoId, String releaseTime, 
                         String details, Long playNum) {
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.releaseTime = releaseTime;
        this.details = details;
        this.playNum = playNum;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public String getReleaseTime() {
        return releaseTime;
    }

    public void setReleaseTime(String releaseTime) {
        this.releaseTime = releaseTime;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Long getPlayNum() {
        return playNum;
    }

    public void setPlayNum(Long playNum) {
        this.playNum = playNum;
    }

    // ========== 工具方法 ==========

    /**
     * 获取语言类型的文本描述
     * @return 语言类型描述
     */
    public String getLanguageTypeText() {
        if (languageType == null) {
            return "未知语言";
        }
        return HomeApiConstantsUtils.getLanguageTypeDescription(languageType);
    }

    /**
     * 获取格式化的播放次数
     * @return 格式化的播放次数字符串
     */
    public String getFormattedPlayNum() {
        if (playNum == null || playNum == 0) {
            return "0";
        }
        
        if (playNum >= 1000000) {
            return String.format("%.1fM", playNum / 1000000.0);
        } else if (playNum >= 1000) {
            return String.format("%.1fK", playNum / 1000.0);
        } else {
            return String.valueOf(playNum);
        }
    }

    /**
     * 检查是否有有效的封面图片
     * @return 如果有有效封面则返回true
     */
    public boolean hasCover() {
        return cover != null && !cover.trim().isEmpty() && !cover.equals("null");
    }

    /**
     * 检查是否有有效的简介
     * @return 如果有有效简介则返回true
     */
    public boolean hasDetails() {
        return details != null && !details.trim().isEmpty() && !details.equals("null");
    }

    /**
     * 转换为VideoModel对象
     * @param ranking 排名位置
     * @return VideoModel对象
     */
    public VideoModel toVideoModel(int ranking) {
        VideoModel videoModel = new VideoModel();
        videoModel.setId(filmId);
        videoModel.setTitle(filmTitle);
        videoModel.setPosterUrl(cover);
        videoModel.setDescription(details);
        videoModel.setRanking(ranking);

        // 添加调试日志
        android.util.Log.d("DailyRankModel", "Converting to VideoModel - Title: " + filmTitle + ", Cover: " + cover);
        
        // 设置播放次数作为热度值
        if (playNum != null) {
            videoModel.setViewCount(playNum);
        }
        
        // 添加语言标签
        if (languageType != null) {
            videoModel.addTag(getLanguageTypeText());
        }
        
        // 存储原始数据用于跳转
        videoModel.addTag("film_id:" + filmId);
        if (filmLanguageInfoId != null) {
            videoModel.addTag("film_language_info_id:" + filmLanguageInfoId);
        }
        
        return videoModel;
    }

    @Override
    public String toString() {
        return "DailyRankModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", releaseTime='" + releaseTime + '\'' +
                ", details='" + details + '\'' +
                ", playNum=" + playNum +
                '}';
    }
}
