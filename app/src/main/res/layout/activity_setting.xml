<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

        <!-- 标题栏 -->
        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingStart="@dimen/bill_header_padding_horizontal"
            android:paddingEnd="@dimen/bill_header_padding_horizontal"
            android:paddingTop="@dimen/bill_header_padding_top">

            <!-- 返回按钮 -->
            <ImageView
                android:id="@+id/iv_back"
                android:layout_width="@dimen/bill_back_button_size"
                android:layout_height="@dimen/bill_back_button_size"
                android:layout_alignParentStart="true"
                android:layout_marginTop="@dimen/bill_back_button_margin_top"
                android:layout_marginStart="@dimen/bill_back_button_margin_start"
                android:src="@drawable/login_btn_back"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:clickable="true"
                android:focusable="true" />

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="@dimen/bill_title_margin_top"
                android:text="Setting"
                android:textColor="#ffffffff"
                android:textSize="@dimen/bill_title_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center" />

        </RelativeLayout>

        <!-- 设置项容器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/setting_bottom_button_margin_horizontal"
            android:layout_marginEnd="@dimen/setting_bottom_button_margin_horizontal"
            android:layout_marginTop="@dimen/setting_first_item_margin_top"
            android:orientation="vertical">

            <!-- Terms of Service + Privacy Policy 双项容器 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_double_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_double_item_bg"
                android:orientation="vertical"
                android:padding="@dimen/setting_item_padding">

                <!-- Terms of Service -->
                <RelativeLayout
                    android:id="@+id/item_terms_of_service"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/setting_text_line_height"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:text="Terms of Service"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/setting_text_size"
                        android:fontFamily="sans-serif" />

                    <ImageView
                        android:layout_width="@dimen/setting_arrow_size"
                        android:layout_height="@dimen/setting_arrow_size"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/set_ic_next" />

                </RelativeLayout>

                <!-- 分隔线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="0.5dp"
                    android:layout_marginTop="10dp"
                    android:layout_marginBottom="10dp"
                    android:background="#33ffffff" />

                <!-- Privacy Policy -->
                <RelativeLayout
                    android:id="@+id/item_privacy_policy"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/setting_text_line_height"
                        android:layout_alignParentStart="true"
                        android:layout_centerVertical="true"
                        android:text="Privacy Policy"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/setting_text_size"
                        android:fontFamily="sans-serif" />

                    <ImageView
                        android:layout_width="@dimen/setting_arrow_size"
                        android:layout_height="@dimen/setting_arrow_size"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:src="@drawable/set_ic_next" />

                </RelativeLayout>

            </LinearLayout>

            <!-- Clear Cache -->
            <RelativeLayout
                android:id="@+id/item_clear_cache"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_single_item_bg"
                android:paddingTop="@dimen/setting_item_padding"
                android:paddingBottom="@dimen/setting_item_padding"
                android:paddingStart="@dimen/setting_item_padding"
                android:paddingEnd="@dimen/setting_item_padding"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Clear Cache"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tv_cache_size"
                    android:layout_width="@dimen/setting_cache_size_width"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_toStartOf="@+id/iv_cache_arrow"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="8dp"
                    android:text="208.2M"
                    android:textColor="#8fffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif"
                    android:gravity="end" />

                <ImageView
                    android:id="@+id/iv_cache_arrow"
                    android:layout_width="@dimen/setting_arrow_size"
                    android:layout_height="@dimen/setting_arrow_size"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/set_ic_next" />

            </RelativeLayout>

            <!-- About Us -->
            <RelativeLayout
                android:id="@+id/item_about_us"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_single_item_bg"
                android:paddingTop="@dimen/setting_item_padding"
                android:paddingBottom="@dimen/setting_item_padding"
                android:paddingStart="@dimen/setting_item_padding"
                android:paddingEnd="@dimen/setting_item_padding"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="About Us"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

                <ImageView
                    android:layout_width="@dimen/setting_arrow_size"
                    android:layout_height="@dimen/setting_arrow_size"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/set_ic_next" />

            </RelativeLayout>

            <!-- Language -->
            <RelativeLayout
                android:id="@+id/item_language"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_single_item_bg"
                android:paddingTop="@dimen/setting_item_padding"
                android:paddingBottom="@dimen/setting_item_padding"
                android:paddingStart="@dimen/setting_item_padding"
                android:paddingEnd="@dimen/setting_item_padding"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="@dimen/setting_cache_text_width"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Language"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tv_current_language"
                    android:layout_width="@dimen/setting_cache_size_width"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_toStartOf="@+id/iv_language_arrow"
                    android:layout_centerVertical="true"
                    android:layout_marginEnd="8dp"
                    android:text="English"
                    android:textColor="#8fffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif"
                    android:gravity="end" />

                <ImageView
                    android:id="@+id/iv_language_arrow"
                    android:layout_width="@dimen/setting_arrow_size"
                    android:layout_height="@dimen/setting_arrow_size"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/set_ic_next" />

            </RelativeLayout>

            <!-- Feedback -->
            <RelativeLayout
                android:id="@+id/item_feedback"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_single_item_bg"
                android:paddingTop="@dimen/setting_item_padding"
                android:paddingBottom="@dimen/setting_item_padding"
                android:paddingStart="@dimen/setting_item_padding"
                android:paddingEnd="@dimen/setting_item_padding"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Feedback"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

                <ImageView
                    android:layout_width="@dimen/setting_arrow_size"
                    android:layout_height="@dimen/setting_arrow_size"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:src="@drawable/set_ic_next" />

            </RelativeLayout>

            <!-- Version -->
            <RelativeLayout
                android:id="@+id/item_version"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_item_margin_bottom"
                android:background="@drawable/setting_single_item_bg"
                android:paddingTop="@dimen/setting_item_padding"
                android:paddingBottom="@dimen/setting_item_padding"
                android:paddingStart="@dimen/setting_item_padding"
                android:paddingEnd="@dimen/setting_item_padding">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:text="Version"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

                <TextView
                    android:id="@+id/tv_version"
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/setting_text_line_height"
                    android:layout_alignParentEnd="true"
                    android:layout_centerVertical="true"
                    android:text="V1.0.0"
                    android:textColor="#8fffffff"
                    android:textSize="@dimen/setting_text_size"
                    android:fontFamily="sans-serif" />

            </RelativeLayout>

        </LinearLayout>

        <!-- 底部按钮区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/setting_bottom_buttons_margin_top"
            android:layout_marginBottom="@dimen/setting_bottom_buttons_margin_bottom"
            android:layout_marginStart="@dimen/setting_bottom_button_margin_horizontal"
            android:layout_marginEnd="@dimen/setting_bottom_button_margin_horizontal"
            android:orientation="vertical">

            <!-- Log Out 按钮 -->
            <TextView
                android:id="@+id/btn_log_out"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:layout_marginBottom="@dimen/setting_bottom_button_margin_between"
                android:background="@drawable/setting_logout_button_bg"
                android:text="Log Out"
                android:textColor="#ff000000"
                android:textSize="@dimen/setting_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

            <!-- Delete Account 按钮 -->
            <TextView
                android:id="@+id/btn_delete_account"
                android:layout_width="match_parent"
                android:layout_height="@dimen/setting_single_item_height"
                android:background="@drawable/setting_delete_button_bg"
                android:text="Delete Account"
                android:textColor="#ffffffff"
                android:textSize="@dimen/setting_text_size"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>

</FrameLayout>
