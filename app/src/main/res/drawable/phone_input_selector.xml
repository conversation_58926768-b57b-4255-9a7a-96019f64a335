<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- When the container or its child EditText has focus -->
    <item android:state_focused="true"
        android:drawable="@drawable/phone_input_focused" />

    <!-- When any child view has focus (for EditText inside container) -->
    <item android:state_focused="false" android:state_selected="true"
        android:drawable="@drawable/phone_input_focused" />

    <!-- Default state -->
    <item android:drawable="@drawable/phone_input_normal" />

</selector>
