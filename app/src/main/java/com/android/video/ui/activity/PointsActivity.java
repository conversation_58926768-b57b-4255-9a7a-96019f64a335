package com.android.video.ui.activity;

import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.LinearLayout;
import android.widget.Toast;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.graphics.Insets;
import com.android.video.R;
import com.android.video.model.PointsPackage;
import com.android.video.model.PointsSerial;
import com.android.video.model.UserModel;
import com.android.video.network.PointsApiService;
import com.android.video.utils.UserSessionUtils;
import com.android.video.utils.PaymentUtils;
import com.android.video.constants.PointsApiConstantsUtils;
import com.android.video.ui.dialog.PaymentMethodDialog;

import java.util.ArrayList;
import java.util.List;

public class PointsActivity extends BaseFullScreenActivity {

    private static final String TAG = "PointsActivity";

    private ImageView btnBack;
    private TextView tvPointsBalance;
    private LinearLayout cardPoints500;
    private LinearLayout cardPoints1000;
    private LinearLayout cardPoints2000;
    private LinearLayout cardPoints3000;
    private TextView tabObtain;
    private TextView tabExpenses;
    private View tabIndicator;
    private View tabIndicatorExpenses;
    private LinearLayout obtainRecordsContainer;
    private LinearLayout expensesRecordsContainer;
    private TextView btnRefill;

    private List<PointsPackage> pointsPackages;
    private UserModel currentUser;
    private List<PointsSerial> obtainRecords;
    private List<PointsSerial> expensesRecords;
    private boolean isObtainTabSelected = true;

    // 当前选中的积分套餐
    private PointsPackage selectedPointsPackage;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_points);

        initViews();
        setupClickListeners();
        setupTabs();
        loadUserData();
        loadPointsPackages();
        loadPointsRecords();
    }

    private void initViews() {
        btnBack = findViewById(R.id.btn_back);
        tvPointsBalance = findViewById(R.id.tv_points_balance);
        cardPoints500 = findViewById(R.id.card_points_500);
        cardPoints1000 = findViewById(R.id.card_points_1000);
        cardPoints2000 = findViewById(R.id.card_points_2000);
        cardPoints3000 = findViewById(R.id.card_points_3000);
        tabObtain = findViewById(R.id.tab_obtain);
        tabExpenses = findViewById(R.id.tab_expenses);
        tabIndicator = findViewById(R.id.tab_indicator);
        tabIndicatorExpenses = findViewById(R.id.tab_indicator_expenses);
        obtainRecordsContainer = findViewById(R.id.obtain_records_container);
        expensesRecordsContainer = findViewById(R.id.expenses_records_container);
        btnRefill = findViewById(R.id.btn_refill);

        // 初始化时隐藏所有积分卡片，等待API数据加载
        hideAllPointsCards();
    }



    private void setupClickListeners() {
        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 积分套餐卡片点击事件将在updatePointsCards()中动态设置

        // 充值按钮 - 弹出支付方式选择
        btnRefill.setOnClickListener(v -> {
            if (selectedPointsPackage != null) {
                String pointsInfo = selectedPointsPackage.getPointsDescription();
                String price = selectedPointsPackage.getFormattedPrice();
                showPurchaseDialog(pointsInfo, price);
            } else {
                Toast.makeText(this, "Please select a points package first", Toast.LENGTH_SHORT).show();
            }
        });
    }

    private void setupTabs() {
        // 标签页点击事件
        tabObtain.setOnClickListener(v -> {
            selectTab(tabObtain, true);
            isObtainTabSelected = true;
            updateRecordsDisplay();
        });

        tabExpenses.setOnClickListener(v -> {
            selectTab(tabExpenses, false);
            isObtainTabSelected = false;
            updateRecordsDisplay();
        });

        // 默认选中Obtain
        selectTab(tabObtain, true);
        isObtainTabSelected = true;

        // 积分卡片的默认选中将在API数据加载后设置
    }

    private void selectTab(TextView selectedTab, boolean isObtain) {
        // 重置所有标签颜色
        tabObtain.setTextColor(getResources().getColor(android.R.color.white, null));
        tabExpenses.setTextColor(getResources().getColor(android.R.color.white, null));

        // 设置选中标签颜色
        selectedTab.setTextColor(getResources().getColor(android.R.color.white, null));

        // 切换内容显示
        if (isObtain) {
            obtainRecordsContainer.setVisibility(android.view.View.VISIBLE);
            expensesRecordsContainer.setVisibility(android.view.View.GONE);
            // 显示Obtain指示器，隐藏Expenses指示器
            tabIndicator.setVisibility(android.view.View.VISIBLE);
            tabIndicatorExpenses.setVisibility(android.view.View.INVISIBLE);
        } else {
            obtainRecordsContainer.setVisibility(android.view.View.GONE);
            expensesRecordsContainer.setVisibility(android.view.View.VISIBLE);
            // 隐藏Obtain指示器，显示Expenses指示器
            tabIndicator.setVisibility(android.view.View.INVISIBLE);
            tabIndicatorExpenses.setVisibility(android.view.View.VISIBLE);
        }
    }

    private void selectPointsCard(LinearLayout selectedCard) {
        // 重置所有卡片状态
        cardPoints500.setSelected(false);
        cardPoints1000.setSelected(false);
        cardPoints2000.setSelected(false);
        cardPoints3000.setSelected(false);

        // 设置选中状态
        selectedCard.setSelected(true);
    }

    private void showPurchaseDialog(String pointsInfo, String price) {
        PaymentMethodDialog paymentDialog = new PaymentMethodDialog(this, new PaymentMethodDialog.OnPaymentMethodSelectedListener() {
            @Override
            public void onPaymentMethodSelected(String paymentMethod) {
                handlePaymentMethodSelection(paymentMethod, pointsInfo, price);
            }
        });
        paymentDialog.show();
    }

    private void handlePaymentMethodSelection(String paymentMethod, String pointsInfo, String price) {
        // 参数验证
        if (selectedPointsPackage == null) {
            Toast.makeText(this, "请先选择积分套餐", Toast.LENGTH_SHORT).show();
            return;
        }

        // 验证支付参数
        if (!PointsApiConstantsUtils.validatePaymentParams(
                selectedPointsPackage.getPackageId(),
                selectedPointsPackage.getPrice(),
                paymentMethod)) {
            Toast.makeText(this, "支付参数无效，请重试", Toast.LENGTH_SHORT).show();
            return;
        }

        String paymentMethodName = PointsApiConstantsUtils.getPayTypeDescription(paymentMethod);

        Log.d(TAG, "=== 开始支付流程 ===");
        Log.d(TAG, "套餐ID: " + selectedPointsPackage.getPackageId());
        Log.d(TAG, "支付金额: " + selectedPointsPackage.getPrice());
        Log.d(TAG, "支付方式: " + paymentMethod + " (" + paymentMethodName + ")");
        Log.d(TAG, "==================");

        // 显示加载状态
        showLoadingState("正在处理支付请求...");

        // 调用支付API
        PointsApiService.getInstance().purchasePointsPackage(
            selectedPointsPackage.getPackageId(),
            selectedPointsPackage.getPrice(),
            paymentMethod,
            new PointsApiService.PointsPaymentCallback() {
                @Override
                public void onSuccess(String orderNo, String paymentPageUrl) {
                    // 隐藏加载状态
                    hideLoadingState();

                    Log.d(TAG, "支付请求成功 - 订单号: " + orderNo);

                    // 调用PaymentUtils打开支付页面
                    boolean success = PaymentUtils.openPaymentPage(
                        PointsActivity.this,
                        paymentPageUrl,
                        orderNo
                    );

                    if (success) {
                        Toast.makeText(PointsActivity.this,
                            "正在跳转到支付页面...",
                            Toast.LENGTH_SHORT).show();
                    } else {
                        Toast.makeText(PointsActivity.this,
                            "无法打开支付页面，请检查是否安装浏览器应用",
                            Toast.LENGTH_LONG).show();
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    // 隐藏加载状态
                    hideLoadingState();

                    Log.e(TAG, "支付请求失败: " + errorMessage);

                    // 显示错误提示
                    Toast.makeText(PointsActivity.this,
                        "支付请求失败: " + errorMessage,
                        Toast.LENGTH_LONG).show();
                }
            }
        );
    }

    /**
     * 显示加载状态
     * <p>
     * 在支付请求期间显示加载提示，防止用户重复操作。
     * </p>
     *
     * @param message 加载提示消息
     */
    private void showLoadingState(String message) {
        // 显示加载提示
        Toast.makeText(this, message, Toast.LENGTH_SHORT).show();

        // 禁用充值按钮，防止重复点击
        if (btnRefill != null) {
            btnRefill.setEnabled(false);
            btnRefill.setText("处理中...");
        }

        Log.d(TAG, "显示加载状态: " + message);
    }

    /**
     * 隐藏加载状态
     * <p>
     * 支付请求完成后恢复UI状态。
     * </p>
     */
    private void hideLoadingState() {
        // 恢复充值按钮状态
        if (btnRefill != null) {
            btnRefill.setEnabled(true);
            btnRefill.setText("充值");
        }

        Log.d(TAG, "隐藏加载状态");
    }

    private void selectPointsCard(android.view.View selectedCard) {
        // 重置所有卡片选中状态
        cardPoints500.setSelected(false);
        cardPoints1000.setSelected(false);
        cardPoints2000.setSelected(false);
        cardPoints3000.setSelected(false);

        // 设置选中卡片状态
        selectedCard.setSelected(true);
    }

    /**
     * 加载积分套餐数据
     */
    private void loadPointsPackages() {
        PointsApiService.getInstance().getPointsPackageList(new PointsApiService.PointsPackageListCallback() {
            @Override
            public void onSuccess(List<PointsPackage> packages) {
                pointsPackages = packages;
                updatePointsCards();
                Log.d(TAG, "积分套餐加载成功，共 " + packages.size() + " 个套餐");
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "积分套餐加载失败: " + errorMessage);
                Toast.makeText(PointsActivity.this, "积分套餐加载失败: " + errorMessage, Toast.LENGTH_SHORT).show();

                // API失败时隐藏所有卡片，不显示假数据
                hideAllPointsCards();
            }
        });
    }

    /**
     * 隐藏所有积分卡片
     */
    private void hideAllPointsCards() {
        cardPoints500.setVisibility(View.GONE);
        cardPoints1000.setVisibility(View.GONE);
        cardPoints2000.setVisibility(View.GONE);
        cardPoints3000.setVisibility(View.GONE);
    }

    /**
     * 更新积分卡片显示
     */
    private void updatePointsCards() {
        if (pointsPackages == null || pointsPackages.isEmpty()) {
            return;
        }

        // 更新卡片点击事件和内容，使用API数据
        LinearLayout[] cards = {cardPoints500, cardPoints1000, cardPoints2000, cardPoints3000};

        // 根据API数据数量显示对应数量的卡片，隐藏多余的卡片
        for (int i = 0; i < cards.length; i++) {
            final LinearLayout card = cards[i];

            if (i < pointsPackages.size()) {
                // 有对应的API数据，显示并更新卡片
                final PointsPackage pointsPackage = pointsPackages.get(i);

                // 显示卡片
                card.setVisibility(View.VISIBLE);

                // 更新卡片内容
                updateCardContent(card, pointsPackage);

                // 设置点击事件
                card.setOnClickListener(v -> {
                    selectPointsCard(card);
                    selectedPointsPackage = pointsPackage; // 保存选中的套餐
                });
            } else {
                // 没有对应的API数据，隐藏卡片
                card.setVisibility(View.GONE);
            }
        }

        // 默认选中第一个可见的卡片
        if (pointsPackages.size() > 0) {
            selectPointsCard(cards[0]);
            selectedPointsPackage = pointsPackages.get(0); // 同时设置选中的套餐
        }

        // 如果API返回的套餐数量超过4个，记录日志
        if (pointsPackages.size() > cards.length) {
            Log.w(TAG, "API返回了 " + pointsPackages.size() + " 个套餐，但界面只能显示 " + cards.length + " 个");
        }
    }

    /**
     * 更新单个卡片的内容
     */
    private void updateCardContent(LinearLayout card, PointsPackage pointsPackage) {
        try {
            // 查找卡片中的TextView并更新内容
            LinearLayout textContainer = (LinearLayout) card.getChildAt(0); // 文字信息容器（现在是第一个子元素）
            LinearLayout pointsContainer = (LinearLayout) textContainer.getChildAt(0); // 积分数量和奖励容器

            // 更新主要积分数量（现在图标是第0个，积分数量是第1个）
            TextView mainPointsText = (TextView) pointsContainer.getChildAt(1);
            mainPointsText.setText(String.valueOf(pointsPackage.getPoints()));

            // 更新奖励积分（如果有）
            if (pointsPackage.hasGiftPoints() && pointsContainer.getChildCount() > 2) {
                TextView bonusText = (TextView) pointsContainer.getChildAt(2);
                bonusText.setText("+" + pointsPackage.getGiftPoints());
                bonusText.setVisibility(View.VISIBLE);
            } else if (pointsContainer.getChildCount() > 2) {
                // 如果没有奖励积分，隐藏奖励文本
                TextView bonusText = (TextView) pointsContainer.getChildAt(2);
                bonusText.setVisibility(View.GONE);
            }

            // 更新价格
            if (textContainer.getChildCount() > 1) {
                TextView priceText = (TextView) textContainer.getChildAt(1);
                priceText.setText(pointsPackage.getFormattedPrice());
            }

        } catch (Exception e) {
            Log.e(TAG, "更新卡片内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载用户数据
     */
    private void loadUserData() {
        currentUser = UserSessionUtils.getCurrentUser(this);
        updatePointsBalance();
    }

    /**
     * 更新积分余额显示
     */
    private void updatePointsBalance() {
        if (currentUser != null && currentUser.isLoggedIn()) {
            tvPointsBalance.setText(String.valueOf(currentUser.getPoints()));
        } else {
            tvPointsBalance.setText("0");
        }
    }



    /**
     * 加载积分流水记录
     */
    private void loadPointsRecords() {
        // 初始化列表
        obtainRecords = new ArrayList<>();
        expensesRecords = new ArrayList<>();

        // 加载收入记录
        loadObtainRecords();

        // 加载支出记录
        loadExpensesRecords();
    }

    /**
     * 加载收入记录
     */
    private void loadObtainRecords() {
        PointsApiService.getInstance().getPointsSerialList(1, 10, PointsApiConstantsUtils.BILL_TYPE_INCOME,
            new PointsApiService.PointsSerialListCallback() {
                @Override
                public void onSuccess(List<PointsSerial> serials, boolean hasMore) {
                    obtainRecords.clear();
                    obtainRecords.addAll(serials);
                    Log.d(TAG, "收入记录加载成功，共 " + serials.size() + " 条");

                    // 如果当前选中的是Obtain标签，更新显示
                    if (isObtainTabSelected) {
                        updateRecordsDisplay();
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "收入记录加载失败: " + errorMessage);
                    Toast.makeText(PointsActivity.this, "收入记录加载失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                }
            });
    }

    /**
     * 加载支出记录
     */
    private void loadExpensesRecords() {
        PointsApiService.getInstance().getPointsSerialList(1, 10, PointsApiConstantsUtils.BILL_TYPE_EXPENSE,
            new PointsApiService.PointsSerialListCallback() {
                @Override
                public void onSuccess(List<PointsSerial> serials, boolean hasMore) {
                    expensesRecords.clear();
                    expensesRecords.addAll(serials);
                    Log.d(TAG, "支出记录加载成功，共 " + serials.size() + " 条");

                    // 如果当前选中的是Expenses标签，更新显示
                    if (!isObtainTabSelected) {
                        updateRecordsDisplay();
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e(TAG, "支出记录加载失败: " + errorMessage);
                    Toast.makeText(PointsActivity.this, "支出记录加载失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                }
            });
    }

    /**
     * 更新记录显示
     */
    private void updateRecordsDisplay() {
        if (isObtainTabSelected) {
            // 显示收入记录
            updateObtainRecordsDisplay();
        } else {
            // 显示支出记录
            updateExpensesRecordsDisplay();
        }
    }

    /**
     * 更新收入记录显示
     */
    private void updateObtainRecordsDisplay() {
        obtainRecordsContainer.removeAllViews();

        if (obtainRecords.isEmpty()) {
            // 显示空状态图片
            ImageView emptyView = new ImageView(this);
            emptyView.setImageResource(R.drawable.empty);

            // 设置图片尺寸为286dp
            int size = (int) (286 * getResources().getDisplayMetrics().density);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(size, size);
            params.gravity = android.view.Gravity.CENTER;
            emptyView.setLayoutParams(params);

            obtainRecordsContainer.addView(emptyView);
        } else {
            // 显示收入记录
            for (PointsSerial serial : obtainRecords) {
                View recordView = createRecordView(serial);
                obtainRecordsContainer.addView(recordView);
            }
        }
    }

    /**
     * 更新支出记录显示
     */
    private void updateExpensesRecordsDisplay() {
        expensesRecordsContainer.removeAllViews();

        if (expensesRecords.isEmpty()) {
            // 显示空状态图片
            ImageView emptyView = new ImageView(this);
            emptyView.setImageResource(R.drawable.empty);

            // 设置图片尺寸为286dp
            int size = (int) (286 * getResources().getDisplayMetrics().density);
            LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(size, size);
            params.gravity = android.view.Gravity.CENTER;
            emptyView.setLayoutParams(params);

            expensesRecordsContainer.addView(emptyView);
        } else {
            // 显示支出记录
            for (PointsSerial serial : expensesRecords) {
                View recordView = createRecordView(serial);
                expensesRecordsContainer.addView(recordView);
            }
        }
    }

    /**
     * 创建记录视图 - 完全按照原有XML布局样式
     */
    private View createRecordView(PointsSerial serial) {
        // 使用LayoutInflater加载原有的记录项布局，然后只替换数据
        // 由于没有单独的记录项布局文件，我们手动创建但完全按照XML中的样式

        // 创建主容器
        LinearLayout container = new LinearLayout(this);
        container.setOrientation(LinearLayout.VERTICAL);

        // 记录项布局 - 完全按照XML中的样式
        LinearLayout recordLayout = new LinearLayout(this);
        recordLayout.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT));
        recordLayout.setOrientation(LinearLayout.HORIZONTAL);
        recordLayout.setGravity(android.view.Gravity.CENTER_VERTICAL);
        recordLayout.setPadding(
            getResources().getDimensionPixelSize(R.dimen.points_item_padding),
            getResources().getDimensionPixelSize(R.dimen.points_item_padding),
            getResources().getDimensionPixelSize(R.dimen.points_item_padding),
            getResources().getDimensionPixelSize(R.dimen.points_item_padding)
        );

        LinearLayout.LayoutParams recordParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT);
        recordParams.bottomMargin = getResources().getDimensionPixelSize(R.dimen.points_item_margin_bottom);
        recordLayout.setLayoutParams(recordParams);

        // 左侧文字容器
        LinearLayout leftContainer = new LinearLayout(this);
        leftContainer.setLayoutParams(new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.WRAP_CONTENT, 1));
        leftContainer.setOrientation(LinearLayout.VERTICAL);

        // 标题文本 - 只替换数据，样式完全按照XML
        TextView titleText = new TextView(this);
        titleText.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT));
        titleText.setText(serial.getBizDesc()); // 只替换数据
        titleText.setTextColor(0xFFFFFFFF); // #ffffff
        titleText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX,
            getResources().getDimension(R.dimen.points_item_title_text_size));
        titleText.setTypeface(android.graphics.Typeface.create("sans-serif-medium", android.graphics.Typeface.NORMAL));

        // 时间文本 - 只替换数据，样式完全按照XML
        TextView timeText = new TextView(this);
        LinearLayout.LayoutParams timeParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT);
        timeParams.topMargin = getResources().getDimensionPixelSize(R.dimen.points_item_date_margin_top);
        timeText.setLayoutParams(timeParams);
        timeText.setText(serial.getFormattedCreateTime()); // 只替换数据
        timeText.setTextColor(0xA3FFFFFF); // #a3ffffff
        timeText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX,
            getResources().getDimension(R.dimen.points_item_date_text_size));

        leftContainer.addView(titleText);
        leftContainer.addView(timeText);

        // 右侧积分容器
        LinearLayout rightContainer = new LinearLayout(this);
        rightContainer.setLayoutParams(new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT));
        rightContainer.setOrientation(LinearLayout.HORIZONTAL);
        rightContainer.setGravity(android.view.Gravity.CENTER_VERTICAL);

        // 积分数量文本 - 只替换数据，样式完全按照XML
        TextView pointsText = new TextView(this);
        pointsText.setLayoutParams(new LinearLayout.LayoutParams(
            getResources().getDimensionPixelSize(R.dimen.points_record_amount_width),
            getResources().getDimensionPixelSize(R.dimen.points_record_amount_height)));
        pointsText.setText(serial.getPointsDisplayText()); // 只替换数据
        pointsText.setTextColor(0xFFFFFFFF); // #ffffffff
        pointsText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX,
            getResources().getDimension(R.dimen.points_record_amount_text_size));
        pointsText.setGravity(android.view.Gravity.CENTER);

        // 金币图标 - 样式完全按照XML
        ImageView coinIcon = new ImageView(this);
        LinearLayout.LayoutParams coinParams = new LinearLayout.LayoutParams(
            getResources().getDimensionPixelSize(R.dimen.points_item_coin_size),
            getResources().getDimensionPixelSize(R.dimen.points_item_coin_size));
        coinParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.points_item_coin_margin);
        coinIcon.setLayoutParams(coinParams);
        coinIcon.setImageResource(R.drawable.jinbi);

        rightContainer.addView(pointsText);
        rightContainer.addView(coinIcon);

        recordLayout.addView(leftContainer);
        recordLayout.addView(rightContainer);

        // 分割线 - 样式完全按照XML
        View divider = new View(this);
        LinearLayout.LayoutParams dividerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            (int) (0.5 * getResources().getDisplayMetrics().density)); // 0.5dp
        dividerParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.points_divider_margin_horizontal);
        dividerParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.points_divider_margin_horizontal);
        divider.setLayoutParams(dividerParams);
        divider.setBackgroundColor(0x3DFFFFFF); // #3dffffff

        container.addView(recordLayout);
        container.addView(divider);

        return container;
    }

    @Override
    protected void onResume() {
        super.onResume();
        // 页面恢复时重新加载用户数据，确保积分同步
        loadUserData();
    }
}
