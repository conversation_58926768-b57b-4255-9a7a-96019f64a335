<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="@dimen/home_video_item_height"
    android:layout_marginBottom="@dimen/home_video_item_spacing_vertical"
    android:background="@drawable/home_categories_video_border"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报背景 -->
    <ImageView
        android:id="@+id/iv_video_poster"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0.5dp"
        android:scaleType="centerCrop"
        android:src="@drawable/movie_poster"
        android:background="@drawable/home_categories_video_inner_border"
        android:clipToOutline="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <!-- 渐变遮罩 -->
    <View
        android:id="@+id/view_gradient_overlay"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/home_video_item_gradient"
        app:layout_constraintTop_toTopOf="@id/iv_video_poster"
        app:layout_constraintStart_toStartOf="@id/iv_video_poster"
        app:layout_constraintEnd_toEndOf="@id/iv_video_poster"
        app:layout_constraintBottom_toBottomOf="@id/iv_video_poster" />

    <!-- 视频标题 -->
    <TextView
        android:id="@+id/tv_video_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/home_video_title_margin_start"
        android:layout_marginEnd="@dimen/home_video_title_margin_start"
        android:layout_marginBottom="@dimen/home_video_title_margin_bottom"
        android:text="喜洋洋与灰太狼"
        android:textColor="@color/white"
        android:textSize="@dimen/home_video_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
