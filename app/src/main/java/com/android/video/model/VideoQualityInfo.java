package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 视频质量信息数据模型
 * <p>
 * 用于映射API返回的视频质量信息JSON数据结构。
 * 包含分辨率类型和是否需要VIP等信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class VideoQualityInfo {
    
    /**
     * 分辨率类型
     * auto/480/720/1080
     */
    @SerializedName("type")
    private String type;
    
    /**
     * 是否需要VIP
     * 0=否, 1=是
     */
    @SerializedName("needVip")
    private Integer needVip;

    /**
     * 默认构造函数
     */
    public VideoQualityInfo() {
    }

    /**
     * 完整构造函数
     */
    public VideoQualityInfo(String type, Integer needVip) {
        this.type = type;
        this.needVip = needVip;
    }

    /**
     * 便利构造函数 - 使用boolean类型的needVip
     */
    public VideoQualityInfo(String type, boolean needVip) {
        this.type = type;
        this.needVip = needVip ? 1 : 0;
    }

    // ========== Getter和Setter方法 ==========

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getNeedVip() {
        return needVip;
    }

    public void setNeedVip(Integer needVip) {
        this.needVip = needVip;
    }

    /**
     * 设置是否需要VIP（boolean版本）
     */
    public void setNeedVip(boolean needVip) {
        this.needVip = needVip ? 1 : 0;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否需要VIP
     * 
     * @return 如果需要VIP返回true，否则返回false
     */
    public boolean isVipRequired() {
        return needVip != null && needVip == 1;
    }

    /**
     * 检查是否为自动分辨率
     * 
     * @return 如果是自动分辨率返回true，否则返回false
     */
    public boolean isAutoQuality() {
        return "auto".equals(type);
    }

    /**
     * 获取分辨率数值
     * 
     * @return 分辨率数值，如果无法解析则返回0
     */
    public int getResolutionValue() {
        if (type == null || type.trim().isEmpty()) {
            return 0;
        }
        
        if ("auto".equals(type)) {
            return -1; // 自动分辨率用-1表示
        }
        
        try {
            return Integer.parseInt(type);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    /**
     * 获取分辨率显示名称
     * 
     * @return 分辨率显示名称
     */
    public String getDisplayName() {
        if (type == null || type.trim().isEmpty()) {
            return "未知";
        }
        
        switch (type) {
            case "auto":
                return "自动";
            case "480":
                return "480P";
            case "720":
                return "720P";
            case "1080":
                return "1080P";
            default:
                return type + "P";
        }
    }

    /**
     * 获取完整的显示名称（包含VIP标识）
     * 
     * @return 完整的显示名称
     */
    public String getFullDisplayName() {
        String displayName = getDisplayName();
        if (isVipRequired()) {
            return displayName + " (VIP)";
        }
        return displayName;
    }

    /**
     * 比较分辨率高低
     * 
     * @param other 另一个质量信息
     * @return 如果当前分辨率更高返回正数，相等返回0，更低返回负数
     */
    public int compareResolution(VideoQualityInfo other) {
        if (other == null) {
            return 1;
        }
        
        int thisValue = this.getResolutionValue();
        int otherValue = other.getResolutionValue();
        
        // 自动分辨率特殊处理
        if (thisValue == -1 && otherValue == -1) {
            return 0;
        } else if (thisValue == -1) {
            return 1; // 自动分辨率优先级最高
        } else if (otherValue == -1) {
            return -1;
        }
        
        return Integer.compare(thisValue, otherValue);
    }

    /**
     * 检查分辨率类型是否有效
     * 
     * @return 如果分辨率类型有效返回true，否则返回false
     */
    public boolean isValidType() {
        return type != null && !type.trim().isEmpty() &&
               (type.equals("auto") || type.matches("\\d+"));
    }

    @Override
    public String toString() {
        return "VideoQualityInfo{" +
                "type='" + type + '\'' +
                ", needVip=" + needVip +
                ", displayName='" + getDisplayName() + '\'' +
                ", isVipRequired=" + isVipRequired() +
                ", resolutionValue=" + getResolutionValue() +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        VideoQualityInfo that = (VideoQualityInfo) o;

        if (type != null ? !type.equals(that.type) : that.type != null) return false;
        return needVip != null ? needVip.equals(that.needVip) : that.needVip == null;
    }

    @Override
    public int hashCode() {
        int result = type != null ? type.hashCode() : 0;
        result = 31 * result + (needVip != null ? needVip.hashCode() : 0);
        return result;
    }
}
