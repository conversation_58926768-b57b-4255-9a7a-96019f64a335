package com.android.video.model.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.android.video.constants.AuthApiConstantsUtils;

/**
 * 初始化设备信息请求模型
 * <p>
 * 用于APP启动时调用初始化接口的请求参数模型。
 * </p>
 * 
 * <AUTHOR> Team
 */
public class InitDeviceRequestModel {

    /**
     * 设备唯一ID（如IMEI/AndroidID）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_DEVICE_CODE)
    @Expose
    private String deviceCode;

    /**
     * 系统类型：1=iOS, 2=Android
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_OS_TYPE)
    @Expose
    private int osType;

    /**
     * 默认构造函数
     */
    public InitDeviceRequestModel() {
    }

    /**
     * 完整构造函数
     * @param deviceCode 设备唯一ID
     * @param osType 系统类型
     */
    public InitDeviceRequestModel(String deviceCode, int osType) {
        this.deviceCode = deviceCode;
        this.osType = osType;
    }

    // ========== Getter和Setter方法 ==========

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public int getOsType() {
        return osType;
    }

    public void setOsType(int osType) {
        this.osType = osType;
    }

    // ========== 便利方法 ==========

    /**
     * 验证请求参数是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        return deviceCode != null && !deviceCode.trim().isEmpty() &&
               (osType == AuthApiConstantsUtils.OS_TYPE_IOS || osType == AuthApiConstantsUtils.OS_TYPE_ANDROID);
    }

    /**
     * 获取验证失败的原因
     * @return 验证失败原因，如果验证通过则返回null
     */
    public String getValidationError() {
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            return "Device code is required";
        }
        if (osType != AuthApiConstantsUtils.OS_TYPE_IOS && osType != AuthApiConstantsUtils.OS_TYPE_ANDROID) {
            return "Invalid OS type. Must be 1 (iOS) or 2 (Android)";
        }
        return null;
    }

    /**
     * 获取操作系统类型描述
     * @return 操作系统类型描述
     */
    public String getOsTypeDescription() {
        switch (osType) {
            case AuthApiConstantsUtils.OS_TYPE_IOS:
                return "iOS";
            case AuthApiConstantsUtils.OS_TYPE_ANDROID:
                return "Android";
            default:
                return "Unknown";
        }
    }

    @Override
    public String toString() {
        return "InitDeviceRequestModel{" +
                "deviceCode='" + deviceCode + '\'' +
                ", osType=" + osType + " (" + getOsTypeDescription() + ")" +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        InitDeviceRequestModel that = (InitDeviceRequestModel) obj;
        
        if (osType != that.osType) return false;
        return deviceCode != null ? deviceCode.equals(that.deviceCode) : that.deviceCode == null;
    }

    @Override
    public int hashCode() {
        int result = deviceCode != null ? deviceCode.hashCode() : 0;
        result = 31 * result + osType;
        return result;
    }
}
