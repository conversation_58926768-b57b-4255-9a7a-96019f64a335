package com.android.video.model.request;

import java.io.Serializable;

/**
 * 积分解锁章节请求数据模型
 * <p>
 * 用于向服务器发送积分解锁章节请求的参数封装。
 * 包含章节ID和短剧语言信息ID等信息。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class UnlockChapterRequestModel implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 章节ID
     * <p>
     * 需要解锁的章节的唯一标识符。
     * 例如：dda0c118ea34677f173dbca87f81d6e1
     * </p>
     */
    private String chapterId;

    /**
     * 短剧语言信息ID
     * <p>
     * 短剧的语言版本信息ID，用于标识具体的短剧版本。
     * 例如：c655490792384ed887bfe20e56f30c47
     * </p>
     */
    private String filmLanguageInfoId;

    /**
     * 默认构造函数
     */
    public UnlockChapterRequestModel() {
    }

    /**
     * 完整构造函数
     *
     * @param chapterId 章节ID
     * @param filmLanguageInfoId 短剧语言信息ID
     */
    public UnlockChapterRequestModel(String chapterId, String filmLanguageInfoId) {
        this.chapterId = chapterId;
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    // ========== Getter和Setter方法 ==========

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    // ========== 验证方法 ==========

    /**
     * 验证请求数据的有效性
     *
     * @return 如果数据有效返回true，否则返回false
     */
    public boolean isValid() {
        return isValidChapterId() && isValidFilmLanguageInfoId();
    }

    /**
     * 获取验证错误信息
     *
     * @return 验证错误的详细描述，如果数据有效则返回null
     */
    public String getValidationError() {
        if (!isValidChapterId()) {
            return "章节ID不能为空";
        }
        if (!isValidFilmLanguageInfoId()) {
            return "短剧语言信息ID不能为空";
        }
        return null;
    }

    /**
     * 验证章节ID是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidChapterId() {
        return chapterId != null && !chapterId.trim().isEmpty();
    }

    /**
     * 验证短剧语言信息ID是否有效
     *
     * @return 如果有效返回true，否则返回false
     */
    private boolean isValidFilmLanguageInfoId() {
        return filmLanguageInfoId != null && !filmLanguageInfoId.trim().isEmpty();
    }

    // ========== 业务方法 ==========

    /**
     * 获取清理后的章节ID
     * <p>
     * 返回去除前后空格的章节ID。
     * </p>
     *
     * @return 清理后的章节ID
     */
    public String getCleanChapterId() {
        return chapterId != null ? chapterId.trim() : null;
    }

    /**
     * 获取清理后的短剧语言信息ID
     * <p>
     * 返回去除前后空格的短剧语言信息ID。
     * </p>
     *
     * @return 清理后的短剧语言信息ID
     */
    public String getCleanFilmLanguageInfoId() {
        return filmLanguageInfoId != null ? filmLanguageInfoId.trim() : null;
    }

    /**
     * 检查是否为有效的UUID格式
     * <p>
     * 简单检查ID是否符合基本的UUID格式要求（32位十六进制字符）。
     * </p>
     *
     * @param id 要检查的ID
     * @return 如果格式有效返回true，否则返回false
     */
    private boolean isValidUuidFormat(String id) {
        if (id == null || id.trim().isEmpty()) {
            return false;
        }
        
        String cleanId = id.trim();
        // 检查长度是否为32位（不包含连字符的UUID）
        if (cleanId.length() != 32) {
            return false;
        }
        
        // 检查是否只包含十六进制字符
        return cleanId.matches("[0-9a-fA-F]{32}");
    }

    /**
     * 验证章节ID格式
     *
     * @return 如果格式有效返回true，否则返回false
     */
    public boolean isValidChapterIdFormat() {
        return isValidUuidFormat(chapterId);
    }

    /**
     * 验证短剧语言信息ID格式
     *
     * @return 如果格式有效返回true，否则返回false
     */
    public boolean isValidFilmLanguageInfoIdFormat() {
        return isValidUuidFormat(filmLanguageInfoId);
    }

    // ========== 工具方法 ==========

    /**
     * 转换为字符串表示
     *
     * @return 对象的字符串表示
     */
    @Override
    public String toString() {
        return "UnlockChapterRequestModel{" +
                "chapterId='" + chapterId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                '}';
    }

    /**
     * 比较两个对象是否相等
     *
     * @param obj 要比较的对象
     * @return 如果相等返回true，否则返回false
     */
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        UnlockChapterRequestModel that = (UnlockChapterRequestModel) obj;

        if (chapterId != null ? !chapterId.equals(that.chapterId) : that.chapterId != null)
            return false;
        return filmLanguageInfoId != null ? filmLanguageInfoId.equals(that.filmLanguageInfoId) : that.filmLanguageInfoId == null;
    }

    /**
     * 获取对象的哈希码
     *
     * @return 哈希码值
     */
    @Override
    public int hashCode() {
        int result = chapterId != null ? chapterId.hashCode() : 0;
        result = 31 * result + (filmLanguageInfoId != null ? filmLanguageInfoId.hashCode() : 0);
        return result;
    }

    /**
     * 创建请求模型的构建器
     *
     * @return 构建器实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 构建器类
     * <p>
     * 提供链式调用方式来构建UnlockChapterRequestModel实例。
     * </p>
     */
    public static class Builder {
        private String chapterId;
        private String filmLanguageInfoId;

        /**
         * 设置章节ID
         *
         * @param chapterId 章节ID
         * @return 构建器实例
         */
        public Builder chapterId(String chapterId) {
            this.chapterId = chapterId;
            return this;
        }

        /**
         * 设置短剧语言信息ID
         *
         * @param filmLanguageInfoId 短剧语言信息ID
         * @return 构建器实例
         */
        public Builder filmLanguageInfoId(String filmLanguageInfoId) {
            this.filmLanguageInfoId = filmLanguageInfoId;
            return this;
        }

        /**
         * 构建UnlockChapterRequestModel实例
         *
         * @return UnlockChapterRequestModel实例
         */
        public UnlockChapterRequestModel build() {
            return new UnlockChapterRequestModel(chapterId, filmLanguageInfoId);
        }
    }
}
