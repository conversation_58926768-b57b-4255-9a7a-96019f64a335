package com.android.video.model.response;

import com.android.video.model.BannerModel;
import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Banner列表响应数据模型
 * <p>
 * 用于映射Banner列表API返回的JSON数据结构。
 * 包含响应状态码、消息和Banner数据列表。
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": [
 *     {
 *       "bannerId": "e6b48afc12bf4aaeac4519e570efebc2",
 *       "bannerName": "banner008",
 *       "location": 1,
 *       "languageType": 1,
 *       "categoryId": "all",
 *       "bannerCover": "https://www.com",
 *       "filmId": "7e8ef885e92f4496899cabc6906567a0",
 *       "promotionalUrl": "https://www.com"
 *     }
 *   ]
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class BannerListResponseModel {
    
    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * Banner数据列表
     */
    @SerializedName("data")
    private List<BannerModel> data;

    /**
     * 默认构造函数
     */
    public BannerListResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data Banner数据列表
     */
    public BannerListResponseModel(String code, String message, List<BannerModel> data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<BannerModel> getData() {
        return data;
    }

    public void setData(List<BannerModel> data) {
        this.data = data;
    }

    // ========== 便利方法 ==========

    /**
     * 检查响应是否成功
     * 
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取Banner数量
     * 
     * @return Banner数量，如果data为null则返回0
     */
    public int getBannerCount() {
        return data != null ? data.size() : 0;
    }

    /**
     * 检查是否有Banner数据
     * 
     * @return 如果有Banner数据返回true，否则返回false
     */
    public boolean hasBanners() {
        return data != null && !data.isEmpty();
    }

    /**
     * 获取首页Banner列表
     * 
     * @return 首页Banner列表
     */
    public List<BannerModel> getHomeBanners() {
        if (data == null) return null;
        
        return data.stream()
                .filter(BannerModel::isHomeBanner)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取分类详情Banner列表
     * 
     * @return 分类详情Banner列表
     */
    public List<BannerModel> getCategoryBanners() {
        if (data == null) return null;
        
        return data.stream()
                .filter(BannerModel::isCategoryBanner)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据语言类型过滤Banner
     * 
     * @param languageType 语言类型 (1=英语, 2=俄语)
     * @return 指定语言的Banner列表
     */
    public List<BannerModel> getBannersByLanguage(int languageType) {
        if (data == null) return null;
        
        return data.stream()
                .filter(banner -> banner.getLanguageType() != null && banner.getLanguageType() == languageType)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 根据分类ID过滤Banner
     * 
     * @param categoryId 分类ID
     * @return 指定分类的Banner列表
     */
    public List<BannerModel> getBannersByCategory(String categoryId) {
        if (data == null || categoryId == null) return null;
        
        return data.stream()
                .filter(banner -> categoryId.equals(banner.getCategoryId()))
                .collect(java.util.stream.Collectors.toList());
    }

    @Override
    public String toString() {
        return "BannerListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
