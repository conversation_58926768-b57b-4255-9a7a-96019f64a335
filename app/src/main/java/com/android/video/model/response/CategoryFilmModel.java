package com.android.video.model.response;

import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.model.VideoModel;
import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 分类短剧数据模型
 * <p>
 * 用于解析分类短剧列表API返回的短剧数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_CATEGORIES}
 * </p>
 *
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmTitle: 短剧标题</li>
 *   <li>categoryId: 分类ID</li>
 *   <li>categoryName: 分类名称</li>
 *   <li>cover: 封面图片URL</li>
 *   <li>languageType: 支持语言类型（1=英语, 2=俄语）</li>
 *   <li>filmId: 短剧唯一标识符</li>
 *   <li>filmLanguageInfoId: 短剧语言信息ID</li>
 *   <li>searchNum: 搜索次数</li>
 *   <li>details: 短剧详情描述</li>
 *   <li>isLove: 是否喜欢</li>
 *   <li>playNum: 播放次数</li>
 *   <li>totalChaptersNum: 总章节数</li>
 * </ul>
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_CATEGORIES
 */
public class CategoryFilmModel {

    /**
     * 短剧标题
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_TITLE)
    @Expose
    private String filmTitle;

    /**
     * 分类ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_ID)
    @Expose
    private String categoryId;

    /**
     * 分类名称
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_NAME)
    @Expose
    private String categoryName;

    /**
     * 封面图片URL
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_COVER)
    @Expose
    private String cover;

    /**
     * 支持语言类型
     * <p>
     * 1=英语, 2=俄语
     * </p>
     */
    @SerializedName(HomeApiConstantsUtils.PARAM_LANGUAGE_TYPE)
    @Expose
    private int languageType;

    /**
     * 短剧ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_ID)
    @Expose
    private String filmId;

    /**
     * 短剧语言信息ID
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_LANGUAGE_INFO_ID)
    @Expose
    private String filmLanguageInfoId;

    /**
     * 搜索次数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_SEARCH_NUM)
    @Expose
    private Integer searchNum;

    /**
     * 短剧详情描述
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_DETAILS)
    @Expose
    private String details;

    /**
     * 是否喜欢
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_IS_LOVE)
    @Expose
    private Boolean isLove;

    /**
     * 播放次数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_PLAY_NUM)
    @Expose
    private Integer playNum;

    /**
     * 总章节数
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_TOTAL_CHAPTERS_NUM)
    @Expose
    private Integer totalChaptersNum;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public CategoryFilmModel() {
    }

    /**
     * 基础构造函数
     *
     * @param filmTitle 短剧标题
     * @param categoryId 分类ID
     * @param categoryName 分类名称
     * @param cover 封面URL
     * @param languageType 语言类型
     * @param filmId 短剧ID
     */
    public CategoryFilmModel(String filmTitle, String categoryId, String categoryName,
                           String cover, int languageType, String filmId) {
        this.filmTitle = filmTitle;
        this.categoryId = categoryId;
        this.categoryName = categoryName;
        this.cover = cover;
        this.languageType = languageType;
        this.filmId = filmId;
    }

    // ========== Getter和Setter方法 ==========

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public int getLanguageType() {
        return languageType;
    }

    public void setLanguageType(int languageType) {
        this.languageType = languageType;
    }

    public String getFilmId() {
        return filmId;
    }

    public void setFilmId(String filmId) {
        this.filmId = filmId;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public Integer getSearchNum() {
        return searchNum;
    }

    public void setSearchNum(Integer searchNum) {
        this.searchNum = searchNum;
    }

    public String getDetails() {
        return details;
    }

    public void setDetails(String details) {
        this.details = details;
    }

    public Boolean getIsLove() {
        return isLove;
    }

    public void setIsLove(Boolean isLove) {
        this.isLove = isLove;
    }

    public Integer getPlayNum() {
        return playNum;
    }

    public void setPlayNum(Integer playNum) {
        this.playNum = playNum;
    }

    public Integer getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(Integer totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    // ========== 工具方法 ==========

    /**
     * 检查短剧是否有封面图片
     *
     * @return 如果有封面图片则返回true，否则返回false
     */
    public boolean hasCover() {
        return cover != null && !cover.trim().isEmpty();
    }

    /**
     * 检查短剧是否被喜欢
     *
     * @return 如果被喜欢则返回true，否则返回false
     */
    public boolean isLoved() {
        return isLove != null && isLove;
    }

    /**
     * 获取语言类型描述
     *
     * @return 语言类型的中文描述
     */
    public String getLanguageTypeDescription() {
        switch (languageType) {
            case HomeApiConstantsUtils.LANGUAGE_TYPE_ENGLISH:
                return "英语";
            case HomeApiConstantsUtils.LANGUAGE_TYPE_RUSSIAN:
                return "俄语";
            default:
                return "未知语言";
        }
    }

    /**
     * 检查短剧信息是否完整
     *
     * @return 如果基本信息完整则返回true，否则返回false
     */
    public boolean isValid() {
        return filmTitle != null && !filmTitle.trim().isEmpty() &&
               filmId != null && !filmId.trim().isEmpty() &&
               categoryId != null && !categoryId.trim().isEmpty();
    }

    /**
     * 转换为VideoModel对象
     *
     * @return VideoModel对象
     */
    public VideoModel toVideoModel() {
        if (!isValid()) {
            return null;
        }

        VideoModel videoModel = new VideoModel();
        videoModel.setId(filmId);
        videoModel.setTitle(filmTitle);
        videoModel.setPosterUrl(cover);
        videoModel.setDescription(details);
        videoModel.setCategory(categoryName != null ? categoryName : "");

        // 设置播放次数作为热度值
        if (playNum != null) {
            videoModel.setViewCount(playNum.longValue());
        }

        // 设置总章节数
        if (totalChaptersNum != null) {
            videoModel.setTotalEpisodes(totalChaptersNum);
        }

        // 设置喜欢状态
        if (isLove != null) {
            videoModel.setLiked(isLove);
        }

        // 设置其他默认值
        videoModel.setRating(0.0f);
        videoModel.setDuration("");
        videoModel.setSubscribed(false);
        videoModel.setFilmLanguageInfoId(filmLanguageInfoId != null ? filmLanguageInfoId : "");

        return videoModel;
    }

    @Override
    public String toString() {
        return "CategoryFilmModel{" +
                "filmTitle='" + filmTitle + '\'' +
                ", categoryId='" + categoryId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmId='" + filmId + '\'' +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", searchNum=" + searchNum +
                ", details='" + details + '\'' +
                ", isLove=" + isLove +
                ", playNum=" + playNum +
                ", totalChaptersNum=" + totalChaptersNum +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        CategoryFilmModel that = (CategoryFilmModel) o;

        return filmId != null ? filmId.equals(that.filmId) : that.filmId == null;
    }

    @Override
    public int hashCode() {
        return filmId != null ? filmId.hashCode() : 0;
    }
}
