package com.android.video.network;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.constants.VersionApiConstantsUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.model.VersionInfoModel;

import com.android.video.model.response.ApiResponseModel;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

import java.io.IOException;

/**
 * 版本检查API服务类
 * <p>
 * 负责调用版本检查相关的API接口，获取最新版本信息。
 * 使用项目统一的网络框架和错误处理机制。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VersionCheckApiService {

    private static final String TAG = "VersionCheckApiService";

    private final OkHttpClient httpClient;
    private final Gson gson;
    private final Handler mainHandler;

    /**
     * API回调接口
     */
    public interface ApiCallback<T> {
        /**
         * 请求成功回调
         * @param data 响应数据
         */
        void onSuccess(T data);

        /**
         * 请求失败回调
         * @param error 错误信息
         */
        void onError(String error);
    }

    /**
     * 构造函数
     * @param context 上下文
     */
    public VersionCheckApiService(Context context) {
        this.httpClient = ApiClientUtils.getHttpClient();
        this.gson = new Gson();
        this.mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 获取版本信息
     * <p>
     * 调用版本检查API获取最新版本信息。
     * 该方法是异步的，结果通过回调返回。
     * </p>
     * 
     * @param callback 结果回调接口
     */
    public void getVersionInfo(ApiCallback<VersionInfoModel> callback) {
        getVersionInfo(VersionApiConstantsUtils.getCurrentClientType(), callback);
    }

    /**
     * 获取指定客户端类型的版本信息
     * <p>
     * 调用版本检查API获取指定客户端类型的最新版本信息。
     * 该方法是异步的，结果通过回调返回。
     * </p>
     * 
     * @param clientType 客户端类型 (1=Android, 2=iOS)
     * @param callback 结果回调接口
     */
    public void getVersionInfo(int clientType, ApiCallback<VersionInfoModel> callback) {
        if (callback == null) {
            Log.e(TAG, "Callback is null");
            return;
        }

        // 构建请求URL
        String apiUrl = VersionApiConstantsUtils.buildVersionCheckUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            clientType
        );

        Log.d(TAG, "发起版本检查请求: " + apiUrl);

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        // 发起异步请求
        httpClient.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                String errorMsg = "版本检查网络请求失败: " + e.getMessage();
                Log.e(TAG, errorMsg, e);
                
                // 在主线程回调错误
                mainHandler.post(() -> callback.onError(errorMsg));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (!response.isSuccessful()) {
                        String errorMsg = "版本检查请求失败，HTTP状态码: " + response.code();
                        Log.e(TAG, errorMsg);
                        mainHandler.post(() -> callback.onError(errorMsg));
                        return;
                    }

                    String responseBody = response.body().string();
                    Log.d(TAG, "版本检查响应: " + responseBody);

                    // 解析响应
                    parseVersionResponse(responseBody, callback);

                } catch (Exception e) {
                    String errorMsg = "版本检查响应处理异常: " + e.getMessage();
                    Log.e(TAG, errorMsg, e);
                    mainHandler.post(() -> callback.onError(errorMsg));
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * 解析版本检查响应
     * @param responseBody 响应体JSON字符串
     * @param callback 结果回调接口
     */
    private void parseVersionResponse(String responseBody, ApiCallback<VersionInfoModel> callback) {
        try {
            // 解析为通用API响应格式
            ApiResponseModel<VersionInfoModel> apiResponse = gson.fromJson(
                responseBody,
                com.google.gson.reflect.TypeToken.get(
                    new com.google.gson.reflect.TypeToken<ApiResponseModel<VersionInfoModel>>(){}.getType()
                ).getType()
            );

            if (apiResponse == null) {
                mainHandler.post(() -> callback.onError("版本检查响应解析失败：响应为空"));
                return;
            }

            // 检查响应码
            if (!"200".equals(apiResponse.getCode())) {
                String errorMsg = "版本检查失败: " + apiResponse.getMessage();
                Log.w(TAG, errorMsg);
                mainHandler.post(() -> callback.onError(errorMsg));
                return;
            }

            // 获取版本信息数据
            VersionInfoModel versionInfo = apiResponse.getData();
            if (versionInfo == null) {
                mainHandler.post(() -> callback.onError("版本检查响应数据为空"));
                return;
            }

            // 验证版本信息完整性
            if (!versionInfo.isValid()) {
                Log.w(TAG, "版本信息数据不完整: " + versionInfo.toString());
                mainHandler.post(() -> callback.onError("版本信息数据不完整"));
                return;
            }

            Log.d(TAG, "版本检查成功: " + versionInfo.toString());
            
            // 在主线程回调成功结果
            mainHandler.post(() -> callback.onSuccess(versionInfo));

        } catch (JsonSyntaxException e) {
            String errorMsg = "版本检查响应JSON解析失败: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            mainHandler.post(() -> callback.onError(errorMsg));
        } catch (Exception e) {
            String errorMsg = "版本检查响应处理异常: " + e.getMessage();
            Log.e(TAG, errorMsg, e);
            mainHandler.post(() -> callback.onError(errorMsg));
        }
    }

    /**
     * 检查网络连接状态
     * @param context 上下文
     * @return 如果网络可用返回true，否则返回false
     */
    private boolean isNetworkAvailable(Context context) {
        return com.android.video.network.NetworkUtils.isNetworkAvailable(context);
    }

    /**
     * 同步获取版本信息（仅用于测试）
     * <p>
     * 注意：此方法会阻塞当前线程，不应在主线程调用。
     * 仅用于测试目的，生产环境请使用异步方法。
     * </p>
     * 
     * @param clientType 客户端类型
     * @return 版本信息，失败时返回null
     */
    public VersionInfoModel getVersionInfoSync(int clientType) {
        try {
            String apiUrl = VersionApiConstantsUtils.buildVersionCheckUrl(
                EnvironmentConfigUtils.getApiBaseUrl(),
                clientType
            );

            Request request = new Request.Builder()
                .url(apiUrl)
                .get()
                .build();

            Response response = httpClient.newCall(request).execute();
            
            if (!response.isSuccessful()) {
                Log.e(TAG, "同步版本检查失败，HTTP状态码: " + response.code());
                return null;
            }

            String responseBody = response.body().string();
            response.close();

            ApiResponseModel<VersionInfoModel> apiResponse = gson.fromJson(
                responseBody,
                com.google.gson.reflect.TypeToken.get(
                    new com.google.gson.reflect.TypeToken<ApiResponseModel<VersionInfoModel>>(){}.getType()
                ).getType()
            );

            if (apiResponse != null && "200".equals(apiResponse.getCode())) {
                return apiResponse.getData();
            }

            return null;

        } catch (Exception e) {
            Log.e(TAG, "同步版本检查异常", e);
            return null;
        }
    }

    /**
     * 取消所有进行中的请求
     * <p>
     * 取消当前服务实例发起的所有网络请求。
     * 通常在Activity或Fragment销毁时调用。
     * </p>
     */
    public void cancelAllRequests() {
        if (httpClient != null) {
            httpClient.dispatcher().cancelAll();
            Log.d(TAG, "已取消所有版本检查请求");
        }
    }
}
