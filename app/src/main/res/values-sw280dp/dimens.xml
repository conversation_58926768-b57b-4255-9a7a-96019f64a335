<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- Extra Small phone specific dimensions (280dp width, scale factor: 0.65) -->

    <!-- Message Notification Dot - Extra Small phone (280dp, scale factor: 0.65) -->
    <dimen name="message_unread_dot_size">5dp</dimen> <!-- 7 × 0.65 -->

    <!-- Redemption Code Dialog - Extra Small phone (280dp, 20dp margin each side) -->
    <dimen name="redeem_dialog_width">240dp</dimen>
    <dimen name="redeem_dialog_height">200dp</dimen>
    <dimen name="redeem_dialog_corner_radius">10dp</dimen>
    <dimen name="redeem_dialog_title_text_size">15sp</dimen>
    <dimen name="redeem_dialog_title_line_height">17dp</dimen>
    <dimen name="redeem_dialog_description_text_size">12sp</dimen>
    <dimen name="redeem_dialog_input_width">208dp</dimen>
    <dimen name="redeem_dialog_input_height">40dp</dimen>
    <dimen name="redeem_dialog_input_corner_radius">8dp</dimen>
    <dimen name="redeem_dialog_input_stroke_width">1dp</dimen>
    <dimen name="redeem_dialog_button_width">208dp</dimen>
    <dimen name="redeem_dialog_button_height">34dp</dimen>
    <dimen name="redeem_dialog_button_corner_radius">17dp</dimen>
    <dimen name="redeem_dialog_button_text_size">14sp</dimen>
    <dimen name="redeem_dialog_button_line_height">18dp</dimen>

    <!-- VIP Background Image - Extra Small phone (280dp, scale factor: 0.75) -->
    <dimen name="vip_bg_width">280dp</dimen>
    <dimen name="vip_bg_height">148dp</dimen>
    <dimen name="vip_bg_margin_top">23dp</dimen>

    <!-- Discount Badge - Extra Small phone (280dp, scale factor: 0.75) -->
    <dimen name="discount_badge_width">66dp</dimen>
    <dimen name="discount_badge_height">15dp</dimen>
    <dimen name="discount_badge_text_size">10sp</dimen>
    <dimen name="discount_badge_padding_horizontal">6dp</dimen>

    <!-- VIP Card Border Frame - Extra Small phone (scale factor: 0.75) -->
    <dimen name="vip_card_border_width">259dp</dimen>
    <dimen name="vip_card_border_height">102dp</dimen>
    <dimen name="vip_card_border_corner_radius">14dp</dimen>
    <dimen name="vip_card_border_stroke_width">1dp</dimen>
    <dimen name="vip_card_border_overflow">3dp</dimen>

    <!-- Subscription Card - Extra Small phone (scale factor: 0.75) -->
    <dimen name="subscribe_card_width">253dp</dimen>
    <dimen name="subscribe_card_price_main_text_size">18sp</dimen>
    <dimen name="subscribe_card_price_unit_text_size">12sp</dimen>

    <!-- Points Page - Extra Small phone (scale factor: 0.75) -->
    <dimen name="points_header_height">66dp</dimen>
    <dimen name="points_header_padding_horizontal">12dp</dimen>
    <dimen name="points_header_padding_top">51dp</dimen>
    <dimen name="points_back_button_size">18dp</dimen>
    <dimen name="points_title_text_size">14sp</dimen>
    <dimen name="points_balance_margin_top">24dp</dimen>
    <dimen name="points_balance_margin_bottom">24dp</dimen>
    <dimen name="points_coin_icon_size">24dp</dimen>
    <dimen name="points_coin_margin_end">6dp</dimen>
    <dimen name="points_balance_text_size">36sp</dimen>
    <dimen name="points_grid_margin_horizontal">12dp</dimen>
    <dimen name="points_grid_margin_bottom">18dp</dimen>
    <dimen name="points_card_width">125dp</dimen>
    <dimen name="points_card_height">60dp</dimen>
    <dimen name="points_card_margin">4dp</dimen>
    <dimen name="points_card_padding">9dp</dimen>
    <dimen name="points_card_corner_radius">6dp</dimen>
    <dimen name="points_card_selected_stroke_width">1.5dp</dimen>
    <dimen name="points_card_coin_size">12dp</dimen>
    <dimen name="points_card_coin_margin">3dp</dimen>
    <dimen name="points_card_main_text_size">12sp</dimen>
    <dimen name="points_card_bonus_text_size">12sp</dimen>
    <dimen name="points_card_bonus_margin">3dp</dimen>
    <dimen name="points_card_price_text_size">9sp</dimen>
    <dimen name="points_card_price_margin_top">3dp</dimen>
    <dimen name="points_tabs_margin_horizontal">12dp</dimen>
    <dimen name="points_tabs_margin_bottom">12dp</dimen>
    <dimen name="points_tab_text_size">12sp</dimen>
    <dimen name="points_tab_margin_end">18dp</dimen>
    <dimen name="points_tab_indicator_width">59dp</dimen>
    <dimen name="points_tab_indicator_height">2dp</dimen>
    <dimen name="points_tab_indicator_corner_radius">1dp</dimen>
    <dimen name="points_tab_obtain_indicator_margin">0dp</dimen>
    <dimen name="points_tab_expenses_indicator_margin">59dp</dimen>
    <dimen name="points_list_margin_horizontal">12dp</dimen>
    <dimen name="points_item_padding_vertical">12dp</dimen>
    <dimen name="points_item_title_text_size">11sp</dimen> <!-- 16 × 0.7 -->
    <dimen name="points_item_date_text_size">10sp</dimen> <!-- 14 × 0.7 -->
    <dimen name="points_record_padding">11dp</dimen> <!-- 16 × 0.7 -->
    <dimen name="points_record_subtitle_margin_top">2dp</dimen> <!-- 3 × 0.7 -->
    <dimen name="points_record_time_margin_top">2dp</dimen> <!-- 3 × 0.7 -->
    <dimen name="points_item_subtitle_text_size">9sp</dimen>
    <dimen name="points_item_subtitle_margin_top">3dp</dimen>
    <dimen name="points_item_time_text_size">8sp</dimen>
    <dimen name="points_item_time_margin_top">2dp</dimen>
    <dimen name="points_item_amount_text_size">11sp</dimen>
    <dimen name="points_item_coin_size">12dp</dimen>
    <dimen name="points_item_coin_margin">3dp</dimen>
    <dimen name="points_divider_stroke_width">0.4dp</dimen>
    <dimen name="points_tips_margin_horizontal">12dp</dimen>
    <dimen name="points_tips_margin_top">18dp</dimen>
    <dimen name="points_tips_title_text_size">12sp</dimen>
    <dimen name="points_tips_title_margin_bottom">9dp</dimen>
    <dimen name="points_tips_subtitle_text_size">11sp</dimen>
    <dimen name="points_tips_content_text_size">9sp</dimen>
    <dimen name="points_tips_item_margin_bottom">6dp</dimen>
    <dimen name="points_tips_bottom_margin">18dp</dimen>
    <dimen name="points_refill_button_padding">12dp</dimen>
    <dimen name="points_refill_button_height">36dp</dimen>
    <dimen name="points_refill_button_text_size">12sp</dimen>
    <dimen name="points_refill_button_corner_radius">18dp</dimen>

    <!-- 积分记录金额样式 -->
    <dimen name="points_record_amount_width">21dp</dimen> <!-- 30 × 0.7 -->
    <dimen name="points_record_amount_height">15dp</dimen> <!-- 21 × 0.7 -->
    <dimen name="points_record_amount_text_size">10sp</dimen> <!-- 16 × 0.7 -->

    <!-- Expenses记录特定样式 -->
    <dimen name="expenses_record_title_width">125dp</dimen> <!-- 179 × 0.7 -->
    <dimen name="expenses_record_title_height">13dp</dimen> <!-- 19 × 0.7 -->
    <dimen name="expenses_record_title_text_size">11sp</dimen> <!-- 16 × 0.7 -->
    <dimen name="expenses_record_subtitle_width">78dp</dimen> <!-- 111 × 0.7 -->
    <dimen name="expenses_record_subtitle_height">12dp</dimen> <!-- 17 × 0.7 -->
    <dimen name="expenses_record_subtitle_text_size">10sp</dimen> <!-- 14 × 0.7 -->
    <dimen name="expenses_record_time_width">95dp</dimen> <!-- 135 × 0.7 -->
    <dimen name="expenses_record_time_height">12dp</dimen> <!-- 17 × 0.7 -->
    <dimen name="expenses_record_time_text_size">10sp</dimen> <!-- 14 × 0.7 -->
    <dimen name="expenses_record_margin_start">11dp</dimen> <!-- 16 × 0.7 -->

    <!-- VIP Privileges样式 -->
    <dimen name="vip_privileges_width">240dp</dimen> <!-- 343 × 0.7 -->
    <dimen name="vip_privileges_height">151dp</dimen> <!-- 216 × 0.7 -->
    <dimen name="vip_privileges_text_size">10sp</dimen> <!-- 14 × 0.7 -->

    <!-- Bill Page - sw280dp (缩放因子: 0.75) -->
    <!-- Header -->
    <dimen name="bill_header_padding_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_header_padding_top">44dp</dimen> <!-- 58 × 0.75 -->
    <dimen name="bill_back_button_size">27dp</dimen> <!-- 36 × 0.75 -->
    <dimen name="bill_back_button_margin_top">0dp</dimen>
    <dimen name="bill_back_button_margin_start">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="bill_title_width">20dp</dimen> <!-- 27 × 0.75 -->
    <dimen name="bill_title_height">16dp</dimen> <!-- 21 × 0.75 -->
    <dimen name="bill_title_margin_top">-2dp</dimen> <!-- -3 × 0.75 -->
    <dimen name="bill_title_text_size">14sp</dimen> <!-- 18 × 0.75 -->

    <!-- Tabs -->
    <dimen name="bill_tabs_margin_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_tabs_margin_top">30dp</dimen> <!-- 40 × 0.75 -->
    <dimen name="bill_tabs_background_width">248dp</dimen> <!-- 280-32=248dp -->
    <dimen name="bill_tabs_background_height">30dp</dimen> <!-- 40 × 0.75 -->
    <dimen name="bill_tabs_background_corner_radius">15dp</dimen> <!-- 20 × 0.75 -->
    <dimen name="bill_selected_tab_width">81dp</dimen> <!-- (248-6)/3=81dp -->
    <dimen name="bill_selected_tab_height">26dp</dimen> <!-- 34 × 0.75 -->
    <dimen name="bill_selected_tab_margin_start">2dp</dimen> <!-- 3 × 0.75 -->
    <dimen name="bill_selected_tab_margin_top">2dp</dimen> <!-- 3 × 0.75 -->
    <dimen name="bill_selected_tab_corner_radius">13dp</dimen> <!-- 17 × 0.75 -->
    <dimen name="bill_tab_text_size">11sp</dimen> <!-- 14 × 0.75 -->

    <!-- Content -->
    <dimen name="bill_content_margin_top">18dp</dimen> <!-- 24 × 0.75 -->

    <!-- Empty State -->
    <dimen name="bill_empty_image_width">215dp</dimen> <!-- 286 × 0.75 -->
    <dimen name="bill_empty_image_height">194dp</dimen> <!-- 258 × 0.75 -->
    <dimen name="bill_empty_image_margin_horizontal">33dp</dimen> <!-- 44.5 × 0.75 -->
    <dimen name="bill_empty_image_margin_bottom">162dp</dimen> <!-- 216 × 0.75 -->
    <dimen name="bill_empty_text_margin_top">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="bill_empty_text_size">11sp</dimen> <!-- 14 × 0.75 -->

    <!-- VIP Record Card - 统一基准值 -->
    <dimen name="vip_record_first_card_margin_top">22dp</dimen> <!-- 统一基准值 -->

    <!-- Points Purchase Card - 统一基准值 -->
    <dimen name="points_purchase_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Video Record Card - 统一基准值 -->
    <dimen name="video_record_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Message Page - 统一基准值 -->
    <dimen name="message_first_card_margin_top">24dp</dimen> <!-- 统一基准值 -->

    <!-- Message Detail Page - 适配280dp屏幕 (280/375 ≈ 0.75) -->
    <dimen name="message_detail_card_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="message_detail_card_min_height">300dp</dimen> <!-- 400 × 0.75 -->
    <dimen name="message_detail_card_margin_top">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="message_detail_divider_width">239dp</dimen> <!-- 319 × 0.75 -->
    <dimen name="message_detail_divider_height">0.5dp</dimen> <!-- 保持不变 -->
    <dimen name="message_detail_divider_margin_vertical">8dp</dimen> <!-- 10 × 0.75 -->

    <!-- Setting Page - 适配280dp屏幕 (280/375 ≈ 0.75) -->
    <dimen name="setting_first_item_margin_start">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_first_item_margin_top">24dp</dimen> <!-- 32 × 0.75 -->
    <dimen name="setting_double_item_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="setting_double_item_height">71dp</dimen> <!-- 94 × 0.75 -->
    <dimen name="setting_single_item_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="setting_single_item_height">38dp</dimen> <!-- 51 × 0.75 -->
    <dimen name="setting_item_margin_bottom">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_item_padding">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_text_size">14sp</dimen> <!-- 16 × 0.88 -->
    <dimen name="setting_text_line_height">14dp</dimen> <!-- 19 × 0.75 -->
    <dimen name="setting_arrow_size">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_terms_text_width">91dp</dimen> <!-- 121 × 0.75 -->
    <dimen name="setting_cache_text_width">65dp</dimen> <!-- 86 × 0.75 -->
    <dimen name="setting_cache_size_width">41dp</dimen> <!-- 55 × 0.75 -->
    <dimen name="setting_bottom_buttons_margin_top">24dp</dimen> <!-- 32 × 0.75 -->
    <dimen name="setting_bottom_buttons_margin_bottom">24dp</dimen> <!-- 32 × 0.75 -->
    <dimen name="setting_bottom_button_margin_horizontal">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_bottom_button_margin_between">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="setting_bottom_button_corner_radius">16dp</dimen> <!-- 21 × 0.75 -->

    <!-- User info section - 适配280dp屏幕 (280/375 ≈ 0.75) -->
    <dimen name="profile_avatar_size">51dp</dimen> <!-- 68 × 0.75 -->
    <dimen name="profile_avatar_margin_start">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="profile_avatar_margin_top">34dp</dimen> <!-- 45 × 0.75 -->

    <!-- Edit Profile Page - 适配280dp屏幕 (280/375 ≈ 0.75) -->
    <dimen name="edit_profile_card_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="edit_profile_card_height">180dp</dimen> <!-- 240 × 0.75 -->
    <dimen name="edit_profile_card_margin_top">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="edit_profile_avatar_size">36dp</dimen> <!-- 48 × 0.75 -->
    <dimen name="edit_profile_arrow_size">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="edit_profile_divider_width">233dp</dimen> <!-- 311 × 0.75 -->
    <dimen name="edit_profile_input_width">233dp</dimen> <!-- 311 × 0.75 -->
    <dimen name="edit_profile_input_height">36dp</dimen> <!-- 48 × 0.75 -->
    <dimen name="edit_profile_ip_location_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="edit_profile_ip_location_height">38dp</dimen> <!-- 50 × 0.75 -->
    <dimen name="edit_profile_save_button_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="edit_profile_save_button_height">32dp</dimen> <!-- 42 × 0.75 -->
    <dimen name="edit_profile_text_size">14sp</dimen> <!-- 16 × 0.88 -->
    <dimen name="edit_profile_text_line_height">14dp</dimen> <!-- 19 × 0.75 -->
    <dimen name="edit_profile_china_text_width">45dp</dimen> <!-- 60 × 0.75 -->
    <dimen name="edit_profile_save_text_width">28dp</dimen> <!-- 37 × 0.75 -->
    <dimen name="edit_profile_item_margin_bottom">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="edit_profile_item_padding">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="edit_profile_save_button_margin_bottom">29dp</dimen> <!-- 38 × 0.75 -->
    <dimen name="edit_profile_popup_width">90dp</dimen> <!-- 120 × 0.75 -->
    <dimen name="edit_profile_popup_item_height">24dp</dimen> <!-- 32 × 0.75 -->
    <dimen name="edit_profile_popup_padding">6dp</dimen> <!-- 8 × 0.75 -->
    <dimen name="edit_profile_popup_text_size">12sp</dimen> <!-- 14 × 0.86 -->
    <dimen name="edit_profile_popup_corner_radius">6dp</dimen> <!-- 8 × 0.75 -->

    <!-- Log Off Page - 适配280dp屏幕 (280/375 ≈ 0.75) -->
    <dimen name="log_off_warning_icon_size">45dp</dimen> <!-- 60 × 0.75 -->
    <dimen name="log_off_warning_icon_margin_top">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="log_off_description_width">246dp</dimen> <!-- 328 × 0.75 -->
    <dimen name="log_off_description_height">45dp</dimen> <!-- 60 × 0.75 -->
    <dimen name="log_off_description_margin_top">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="log_off_card_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="log_off_card_height">240dp</dimen> <!-- 320 × 0.75 -->
    <dimen name="log_off_card_margin_top">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="log_off_card_padding">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="log_off_card_item_line_height">20dp</dimen> <!-- 26 × 0.75 -->
    <dimen name="log_off_checkbox_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="log_off_checkbox_height">45dp</dimen> <!-- 60 × 0.75 -->
    <dimen name="log_off_checkbox_margin_top">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="log_off_checkbox_icon_size">15dp</dimen> <!-- 20 × 0.75 -->
    <dimen name="log_off_checkbox_icon_margin_end">6dp</dimen> <!-- 8 × 0.75 -->
    <dimen name="log_off_checkbox_text_margin_start">21dp</dimen> <!-- 28 × 0.75 -->
    <dimen name="log_off_checkbox_text_size">12sp</dimen> <!-- 13 × 0.92 -->
    <dimen name="log_off_checkbox_text_line_spacing">5dp</dimen> <!-- 7 × 0.75 -->
    <dimen name="log_off_delete_button_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="log_off_delete_button_height">32dp</dimen> <!-- 42 × 0.75 -->
    <dimen name="log_off_delete_button_margin_top">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="log_off_delete_button_margin_bottom">29dp</dimen> <!-- 38 × 0.75 -->
    <dimen name="log_off_delete_text_width">86dp</dimen> <!-- 115 × 0.75 -->
    <dimen name="log_off_delete_text_height">14dp</dimen> <!-- 19 × 0.75 -->
    <dimen name="log_off_item_margin_horizontal">12dp</dimen> <!-- 16 × 0.75 -->

    <!-- Feedback Page - 适配280dp屏幕 (280/375 = 0.75) -->
    <dimen name="feedback_required_star_width">5dp</dimen> <!-- 7 × 0.75 -->
    <dimen name="feedback_required_star_height">14dp</dimen> <!-- 19 × 0.75 -->
    <dimen name="feedback_label_width">81dp</dimen> <!-- 108 × 0.75 -->
    <dimen name="feedback_label_height">14dp</dimen> <!-- 19 × 0.75 -->
    <dimen name="feedback_type_selected_width">85dp</dimen> <!-- 113 × 0.75 -->
    <dimen name="feedback_type_selected_height">30dp</dimen> <!-- 40 × 0.75 -->
    <dimen name="feedback_type_unselected_width">164dp</dimen> <!-- 218 × 0.75 -->
    <dimen name="feedback_type_unselected_height">30dp</dimen> <!-- 40 × 0.75 -->
    <dimen name="feedback_type_corner_radius">11dp</dimen> <!-- 14 × 0.75 -->
    <dimen name="feedback_input_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="feedback_input_height">140dp</dimen> <!-- 186 × 0.75 -->
    <dimen name="feedback_input_corner_radius">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="feedback_input_padding">11dp</dimen> <!-- 15 × 0.75 -->
    <dimen name="feedback_image_size">80dp</dimen> <!-- 107 × 0.75 -->
    <dimen name="feedback_image_corner_radius">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="feedback_image_icon_size">21dp</dimen> <!-- 28 × 0.75 -->
    <dimen name="feedback_button_width">257dp</dimen> <!-- 343 × 0.75 -->
    <dimen name="feedback_button_height">32dp</dimen> <!-- 42 × 0.75 -->
    <dimen name="feedback_button_corner_radius">16dp</dimen> <!-- 21 × 0.75 -->
    <dimen name="feedback_item_margin_horizontal">12dp</dimen> <!-- 16 × 0.75 -->
    <dimen name="feedback_item_margin_vertical">18dp</dimen> <!-- 24 × 0.75 -->
    <dimen name="feedback_type_margin_end">6dp</dimen> <!-- 8 × 0.75 -->

    <!-- My List History Layout Dimensions (280dp屏幕适配，缩放因子: 0.747，基准375dp) -->
    <dimen name="my_list_history_item_height">135dp</dimen> <!-- 180 × 0.747 = 135dp -->
    <dimen name="my_list_history_item_margin_start">6dp</dimen> <!-- 8 × 0.747 = 6dp -->
    <dimen name="my_list_history_item_margin_end">6dp</dimen> <!-- 8 × 0.747 = 6dp -->
    <dimen name="my_list_history_item_margin_bottom">3dp</dimen> <!-- 4 × 0.747 = 3dp -->
    <dimen name="my_list_history_poster_width">90dp</dimen> <!-- 120 × 0.747 = 90dp -->
    <dimen name="my_list_history_poster_height">120dp</dimen> <!-- 160 × 0.747 = 120dp -->
    <dimen name="my_list_history_poster_corner_radius">11dp</dimen> <!-- 15 × 0.747 = 11dp -->
    <dimen name="my_list_history_poster_margin_end">12dp</dimen> <!-- 16 × 0.747 = 12dp -->
    <dimen name="my_list_history_title_width">135dp</dimen> <!-- 180 × 0.747 = 135dp -->
    <dimen name="my_list_history_title_height">21dp</dimen> <!-- 28 × 0.747 = 21dp -->
    <dimen name="my_list_history_title_text_size">16sp</dimen> <!-- 22 × 0.747 = 16sp -->
    <dimen name="my_list_history_episode_text_size">15sp</dimen> <!-- 20 × 0.747 = 15sp -->
    <dimen name="my_list_history_episode_margin_top">12dp</dimen> <!-- 16 × 0.747 = 12dp -->
    <dimen name="my_list_history_play_button_width">75dp</dimen> <!-- 100 × 0.747 = 75dp -->
    <dimen name="my_list_history_play_button_height">37dp</dimen> <!-- 50 × 0.747 = 37dp -->
    <dimen name="my_list_history_play_button_corner_radius">19dp</dimen> <!-- 25 × 0.747 = 19dp -->
    <dimen name="my_list_history_play_button_margin_end">9dp</dimen> <!-- 12 × 0.747 = 9dp -->
    <dimen name="my_list_history_play_icon_size">18dp</dimen> <!-- 24 × 0.747 = 18dp -->
    <dimen name="my_list_history_play_text_size">13sp</dimen> <!-- 18 × 0.747 = 13sp -->

    <!-- Interest Tab Card Dimensions - sw280dp (缩放因子: 0.75，基准360dp) -->
    <!-- Card Layout -->
    <dimen name="interest_card_margin_horizontal">9dp</dimen> <!-- 12 × 0.75 = 9dp -->
    <dimen name="interest_card_margin_bottom">14dp</dimen> <!-- 18 × 0.75 = 14dp -->

    <!-- Poster - 放大尺寸 -->
    <dimen name="interest_poster_width">90dp</dimen> <!-- 120 × 0.75 = 90dp -->
    <dimen name="interest_poster_height">120dp</dimen> <!-- 160 × 0.75 = 120dp -->

    <!-- Content Area -->
    <dimen name="interest_content_margin_start">11dp</dimen> <!-- 14 × 0.75 = 11dp -->

    <!-- Title -->
    <dimen name="interest_title_width">150dp</dimen> <!-- 200 × 0.75 = 150dp -->
    <dimen name="interest_title_height">17dp</dimen> <!-- 22 × 0.75 = 17dp -->
    <dimen name="interest_title_text_size">12sp</dimen> <!-- 16 × 0.75 = 12sp -->

    <!-- Status Tag - 调整位置和大小 -->
    <dimen name="interest_status_tag_width">66dp</dimen> <!-- 88 × 0.75 = 66dp -->
    <dimen name="interest_status_tag_height">17dp</dimen> <!-- 22 × 0.75 = 17dp -->
    <dimen name="interest_status_tag_margin_top">5dp</dimen> <!-- 6 × 0.75 = 5dp -->
    <dimen name="interest_status_text_width">57dp</dimen> <!-- 76 × 0.75 = 57dp -->
    <dimen name="interest_status_text_height">11dp</dimen> <!-- 14 × 0.75 = 11dp -->
    <dimen name="interest_status_text_size">9sp</dimen> <!-- 12 × 0.75 = 9sp -->

    <!-- Description - 优化文字大小 -->
    <dimen name="interest_description_width">150dp</dimen> <!-- 200 × 0.75 = 150dp -->
    <dimen name="interest_description_height">27dp</dimen> <!-- 36 × 0.75 = 27dp -->
    <dimen name="interest_description_margin_top">6dp</dimen> <!-- 8 × 0.75 = 6dp -->
    <dimen name="interest_description_text_size">9sp</dimen> <!-- 12 × 0.75 = 9sp -->

    <!-- Time Info -->
    <dimen name="interest_time_container_margin_top">9dp</dimen> <!-- 12 × 0.75 = 9dp -->
    <dimen name="interest_time_icon_size">11dp</dimen> <!-- 14 × 0.75 = 11dp -->
    <dimen name="interest_time_text_width">60dp</dimen> <!-- 80 × 0.75 = 60dp -->
    <dimen name="interest_time_text_height">14dp</dimen> <!-- 18 × 0.75 = 14dp -->
    <dimen name="interest_time_text_margin_start">3dp</dimen> <!-- 4 × 0.75 = 3dp -->
    <dimen name="interest_time_text_size">9sp</dimen> <!-- 12 × 0.75 = 9sp -->

    <!-- Cancel Button -->
    <dimen name="interest_cancel_button_width">150dp</dimen> <!-- 200 × 0.75 = 150dp -->
    <dimen name="interest_cancel_button_height">21dp</dimen> <!-- 28 × 0.75 = 21dp -->
    <dimen name="interest_cancel_button_margin_top">9dp</dimen> <!-- 12 × 0.75 = 9dp -->
    <dimen name="interest_cancel_icon_size">11dp</dimen> <!-- 14 × 0.75 = 11dp -->
    <dimen name="interest_cancel_text_width">60dp</dimen> <!-- 80 × 0.75 = 60dp -->
    <dimen name="interest_cancel_text_height">11dp</dimen> <!-- 14 × 0.75 = 11dp -->
    <dimen name="interest_cancel_text_margin_start">3dp</dimen> <!-- 4 × 0.75 = 3dp -->
    <dimen name="interest_cancel_text_size">9sp</dimen> <!-- 12 × 0.75 = 9sp -->

    <!-- Download Page Dimensions - 280dp screen (scale factor: 0.75) -->
    <!-- Header -->
    <dimen name="download_header_height">66dp</dimen>
    <dimen name="download_header_padding_start">12dp</dimen>
    <dimen name="download_header_padding_end">12dp</dimen>
    <dimen name="download_header_padding_top">33dp</dimen>
    <dimen name="download_back_button_size">27dp</dimen>
    <dimen name="download_header_title_text_size">14sp</dimen>

    <!-- List -->
    <dimen name="download_list_margin_top">11dp</dimen>
    <dimen name="download_list_padding_horizontal">12dp</dimen>
    <dimen name="download_list_padding_bottom">15dp</dimen>

    <!-- Download Item -->
    <dimen name="download_item_height">105dp</dimen>
    <dimen name="download_item_margin_bottom">15dp</dimen>

    <!-- Delete Icon -->
    <dimen name="download_delete_icon_size">17dp</dimen>
    <dimen name="download_delete_margin_end">12dp</dimen>

    <!-- Poster -->
    <dimen name="download_poster_width">69dp</dimen>
    <dimen name="download_poster_height">92dp</dimen>
    <dimen name="download_poster_margin_top">13dp</dimen>

    <!-- Content Area -->
    <dimen name="download_content_margin_start">11dp</dimen>
    <dimen name="download_content_margin_top">13dp</dimen>
    <dimen name="download_content_margin_end">11dp</dimen>

    <!-- Title -->
    <dimen name="download_title_width">135dp</dimen>
    <dimen name="download_title_height">18dp</dimen>
    <dimen name="download_title_text_size">15sp</dimen>

    <!-- Status -->
    <dimen name="download_status_width">90dp</dimen>
    <dimen name="download_status_height">13dp</dimen>
    <dimen name="download_status_margin_top">4dp</dimen>
    <dimen name="download_status_text_size">11sp</dimen>

    <!-- Progress -->
    <dimen name="download_progress_margin_top">8dp</dimen>
    <dimen name="download_count_width">17dp</dimen>
    <dimen name="download_count_height">18dp</dimen>
    <dimen name="download_count_text_size">15sp</dimen>
    <dimen name="download_arrow_icon_size">15dp</dimen>
    <dimen name="download_arrow_margin_start">4dp</dimen>

    <!-- Play Button -->
    <dimen name="download_play_button_width">64dp</dimen>
    <dimen name="download_play_button_height">30dp</dimen>
    <dimen name="download_play_button_margin_end">12dp</dimen>
    <dimen name="download_play_icon_size">15dp</dimen>
    <dimen name="download_play_icon_margin_end">4dp</dimen>
    <dimen name="download_play_text_width">23dp</dimen>
    <dimen name="download_play_text_height">14dp</dimen>
    <dimen name="download_play_text_size">12sp</dimen>

    <!-- Empty State -->
    <dimen name="download_empty_image_size">90dp</dimen>
    <dimen name="download_empty_text_margin_top">12dp</dimen>
    <dimen name="download_empty_text_size">11sp</dimen>

    <!-- Download Page Title -->
    <dimen name="download_page_title_text_size">17sp</dimen>

    <!-- Video Download Detail Page - 280dp screen (scale factor: 0.75) -->
    <!-- Video Info Card -->
    <dimen name="download_detail_card_height">105dp</dimen>
    <dimen name="download_detail_card_margin_horizontal">12dp</dimen>
    <dimen name="download_detail_card_margin_top">18dp</dimen>

    <!-- Poster -->
    <dimen name="download_detail_poster_width">69dp</dimen>
    <dimen name="download_detail_poster_height">92dp</dimen>

    <!-- Content Area -->
    <dimen name="download_detail_content_margin_start">11dp</dimen>
    <dimen name="download_detail_content_margin_end">11dp</dimen>

    <!-- Title -->
    <dimen name="download_detail_title_text_size">15sp</dimen>

    <!-- Status -->
    <dimen name="download_detail_status_margin_top">4dp</dimen>
    <dimen name="download_detail_status_text_size">11sp</dimen>

    <!-- Progress -->
    <dimen name="download_detail_progress_margin_top">8dp</dimen>
    <dimen name="download_detail_count_text_size">15sp</dimen>

    <!-- File Size -->
    <dimen name="download_detail_file_size_margin_top">6dp</dimen>
    <dimen name="download_detail_file_size_text_size">11sp</dimen>

    <!-- Play Button -->
    <dimen name="download_detail_play_button_width">60dp</dimen>
    <dimen name="download_detail_play_button_height">30dp</dimen>
    <dimen name="download_detail_play_icon_size">15dp</dimen>
    <dimen name="download_detail_play_icon_margin_end">4dp</dimen>
    <dimen name="download_detail_play_text_size">12sp</dimen>

    <!-- Episodes List -->
    <dimen name="download_detail_episodes_margin_top">24dp</dimen>
    <dimen name="download_detail_episodes_padding_horizontal">12dp</dimen>
    <dimen name="download_detail_episodes_padding_bottom">15dp</dimen>

    <!-- Episode Item -->
    <dimen name="episode_item_height">42dp</dimen>
    <dimen name="episode_item_margin_bottom">12dp</dimen>

    <!-- Episode Number -->
    <dimen name="episode_number_width">45dp</dimen>
    <dimen name="episode_number_height">18dp</dimen>
    <dimen name="episode_number_margin_start">12dp</dimen>
    <dimen name="episode_number_text_size">14sp</dimen>

    <!-- Episode File Size -->
    <dimen name="episode_file_size_width">45dp</dimen>
    <dimen name="episode_file_size_height">18dp</dimen>
    <dimen name="episode_file_size_margin_start">18dp</dimen>
    <dimen name="episode_file_size_margin_top">3dp</dimen>
    <dimen name="episode_file_size_text_size">11sp</dimen>

    <!-- Episode Buttons -->
    <dimen name="episode_play_button_size">24dp</dimen>
    <dimen name="episode_play_button_margin_end">12dp</dimen>
    <dimen name="episode_delete_button_size">24dp</dimen>
    <dimen name="episode_delete_button_margin_end">12dp</dimen>

    <!-- Episode Divider -->
    <dimen name="episode_divider_height">0.5dp</dimen>
    <dimen name="episode_divider_margin_top">6dp</dimen>

    <!-- Bottom Sheet - 280dp screen -->
    <dimen name="bottom_sheet_padding_top">12dp</dimen>
    <dimen name="bottom_sheet_padding_horizontal">12dp</dimen>
    <dimen name="bottom_sheet_handle_width">30dp</dimen>
    <dimen name="bottom_sheet_handle_height">3dp</dimen>
    <dimen name="bottom_sheet_handle_margin_bottom">12dp</dimen>
    <dimen name="bottom_sheet_close_button_size">14dp</dimen>
    <dimen name="bottom_sheet_divider_height">5dp</dimen>
    <dimen name="bottom_sheet_divider_margin_top">18dp</dimen>
    <dimen name="bottom_sheet_divider_margin_bottom">12dp</dimen>
    <dimen name="bottom_sheet_episodes_max_height">300dp</dimen>

    <!-- Search Page Dimensions Scaled for 280dp (0.75x) -->

    <!-- Back button -->
    <dimen name="search_back_button_size">18dp</dimen>
    <dimen name="search_back_button_margin_start">12dp</dimen>
    <dimen name="search_back_button_margin_top">41dp</dimen>

    <!-- Search input -->
    <dimen name="search_input_width">230dp</dimen>
    <dimen name="search_input_height">29dp</dimen>
    <dimen name="search_input_margin_start">39dp</dimen>
    <dimen name="search_input_margin_top">35dp</dimen>
    <dimen name="search_input_corner_radius">9dp</dimen>
    <dimen name="search_input_padding_start">9dp</dimen>
    <dimen name="search_input_padding_vertical">6dp</dimen>

    <!-- Search icon in input -->
    <dimen name="search_input_icon_size">12dp</dimen>
    <dimen name="search_input_icon_margin_start">9dp</dimen>

    <!-- Search text -->
    <dimen name="search_text_size">12sp</dimen>
    <dimen name="search_text_margin_start">74dp</dimen>
    <dimen name="search_text_margin_top">42dp</dimen>

    <!-- Recent searches section -->
    <dimen name="search_recent_title_margin_start">12dp</dimen>
    <dimen name="search_recent_title_margin_top">78dp</dimen>
    <dimen name="search_recent_title_size">14sp</dimen>

    <!-- Clear recent searches icon -->
    <dimen name="search_clear_icon_size">14dp</dimen>
    <dimen name="search_clear_icon_margin_end">12dp</dimen>
    <dimen name="search_clear_icon_margin_top">79dp</dimen>

    <!-- Recent search tags -->
    <dimen name="search_recent_tag_height">24dp</dimen>
    <dimen name="search_recent_tag_padding_horizontal">9dp</dimen>
    <dimen name="search_recent_tag_padding_vertical">5dp</dimen>
    <dimen name="search_recent_tag_margin">3dp</dimen>
    <dimen name="search_recent_tag_text_size">11sp</dimen> <!-- 14 × 0.75 -->
    <dimen name="search_recent_tag_corner_radius">12dp</dimen> <!-- 16 × 0.75 -->

    <!-- Trending searches section -->
    <dimen name="search_trending_title_size">14sp</dimen>
    <dimen name="search_trending_title_margin_start">12dp</dimen>
    <dimen name="search_trending_title_margin_top">9dp</dimen>

    <!-- Trending search cards -->
    <dimen name="search_trending_card_width">257dp</dimen>
    <dimen name="search_trending_card_height">72dp</dimen>
    <dimen name="search_trending_card_margin_horizontal">12dp</dimen>
    <dimen name="search_trending_card_margin_vertical">5dp</dimen>
    <dimen name="search_trending_card_corner_radius">9dp</dimen>
    <dimen name="search_trending_card_padding">11dp</dimen>

    <!-- Rank background -->
    <dimen name="search_rank_bg_size">21dp</dimen>
    <dimen name="search_rank_bg_corner_radius">5dp</dimen>
    <dimen name="search_rank_text_size">12sp</dimen>

    <!-- Poster in trending card -->
    <dimen name="search_poster_size">51dp</dimen>
    <dimen name="search_poster_corner_radius">6dp</dimen>
    <dimen name="search_poster_margin_start">8dp</dimen>

    <!-- Content in trending card -->
    <dimen name="search_content_margin_start">8dp</dimen>
    <dimen name="search_title_text_size">11sp</dimen>
    <dimen name="search_count_text_size">9sp</dimen>
    <dimen name="search_count_icon_size">12dp</dimen>
    <dimen name="search_count_icon_margin_end">3dp</dimen>
    <dimen name="search_count_margin_top">17dp</dimen>

    <!-- Search Result Screen - Extra Small phone (280dp, scale factor: 0.68) -->

    <!-- Header Section -->
    <dimen name="search_result_header_height">60dp</dimen>
    <dimen name="search_result_header_padding_horizontal">11dp</dimen>
    <dimen name="search_result_header_padding_top">30dp</dimen>

    <!-- Back Button -->
    <dimen name="search_result_back_button_size">16dp</dimen>

    <!-- Search Box -->
    <dimen name="search_result_search_box_height">24dp</dimen>
    <dimen name="search_result_search_box_margin_start">11dp</dimen>
    <dimen name="search_result_search_box_margin_end">11dp</dimen>
    <dimen name="search_result_search_box_padding_horizontal">8dp</dimen>

    <!-- Search Icon -->
    <dimen name="search_result_search_icon_size">11dp</dimen>
    <dimen name="search_result_search_icon_margin_end">5dp</dimen>

    <!-- Search Text -->
    <dimen name="search_result_search_text_size">10sp</dimen>

    <!-- Clear Button -->
    <dimen name="search_result_clear_button_size">16dp</dimen>

    <!-- Content Section -->
    <dimen name="search_result_content_padding_horizontal">11dp</dimen>

    <!-- Result Count -->
    <dimen name="search_result_count_padding_vertical">8dp</dimen>
    <dimen name="search_result_count_text_size">8sp</dimen>

    <!-- Video Item -->
    <dimen name="search_result_item_height">54dp</dimen>
    <dimen name="search_result_item_margin_bottom">12dp</dimen>
    <dimen name="search_result_item_padding_horizontal">0dp</dimen>
    <dimen name="search_result_item_padding_vertical">0dp</dimen>

    <!-- Poster -->
    <dimen name="search_result_poster_width">80dp</dimen>
    <dimen name="search_result_poster_height">54dp</dimen>
    <dimen name="search_result_poster_margin_end">8dp</dimen>

    <!-- Title -->
    <dimen name="search_result_title_height">14dp</dimen>
    <dimen name="search_result_title_margin_end">5dp</dimen>
    <dimen name="search_result_title_text_size">11sp</dimen>

    <!-- Episode Info -->
    <dimen name="search_result_episode_margin_top">5dp</dimen>
    <dimen name="search_result_episode_text_size">8sp</dimen>

    <!-- Play Button -->
    <dimen name="search_result_play_button_size">22dp</dimen>
    <dimen name="search_result_play_button_margin_end">11dp</dimen>

    <!-- Like Button -->
    <dimen name="search_result_like_button_size">16dp</dimen>

    <!-- Empty State -->
    <dimen name="empty_state_padding_horizontal">22dp</dimen>
    <dimen name="empty_state_container_width">195dp</dimen>
    <dimen name="empty_state_container_height">195dp</dimen>
    <dimen name="empty_state_image_width">195dp</dimen>
    <dimen name="empty_state_image_height">195dp</dimen>
    <dimen name="empty_state_text_margin_top">11dp</dimen>
    <dimen name="empty_state_text_size">10sp</dimen>

    <!-- Information Page Dimensions - Extra Small phone (280dp, scale factor: 0.75) -->

    <!-- Content margins -->
    <dimen name="information_content_margin_horizontal">12dp</dimen>
    <dimen name="information_content_margin_top">18dp</dimen>
    <dimen name="information_content_margin_bottom">18dp</dimen>

    <!-- Text dimensions -->
    <dimen name="information_content_text_size">14sp</dimen>
    <dimen name="information_content_line_spacing">3dp</dimen>

    <!-- Loading state -->
    <dimen name="information_loading_margin_top">75dp</dimen>

    <!-- Payment Method Dialog - Extra Small phone (280dp, scale factor 0.8) -->
    <dimen name="payment_dialog_width">256dp</dimen> <!-- 320 × 0.8 -->
    <dimen name="payment_dialog_padding">19dp</dimen> <!-- 24 × 0.8 -->
    <dimen name="payment_dialog_title_text_size">14sp</dimen> <!-- 18 × 0.8 -->
    <dimen name="payment_dialog_option_height">38dp</dimen> <!-- 48 × 0.8 -->
    <dimen name="payment_dialog_option_padding_horizontal">13dp</dimen> <!-- 16 × 0.8 -->
    <dimen name="payment_dialog_option_text_size">13sp</dimen> <!-- 16 × 0.8 -->
    <dimen name="payment_dialog_icon_size">19dp</dimen> <!-- 24 × 0.8 -->
    <dimen name="payment_dialog_icon_margin_end">10dp</dimen> <!-- 12 × 0.8 -->
    <dimen name="payment_dialog_options_margin_top">16dp</dimen> <!-- 20 × 0.8 -->
    <dimen name="payment_dialog_divider_height">0.4dp</dimen> <!-- 0.5 × 0.8 -->
    <dimen name="payment_dialog_divider_margin_horizontal">13dp</dimen> <!-- 16 × 0.8 -->

</resources>
