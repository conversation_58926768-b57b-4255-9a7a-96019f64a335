package com.android.video.example;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.widget.Toast;

import com.android.video.ui.dialog.UpdateDialog;

/**
 * 更新弹窗使用示例
 * 展示如何在应用中使用UpdateDialog
 */
public class UpdateDialogExample {

    /**
     * 显示更新弹窗的示例方法
     * @param context 上下文
     * @param newVersionName 新版本号
     * @param downloadUrl 下载链接（可选）
     */
    public static void showUpdateDialog(Context context, String newVersionName, String downloadUrl) {
        UpdateDialog.showUpdateDialog(context, newVersionName, new UpdateDialog.OnUpdateDialogListener() {
            @Override
            public void onUpdateClicked() {
                // 用户点击了更新按钮
                handleUpdateClick(context, downloadUrl);
            }

            @Override
            public void onCancelClicked() {
                // 用户点击了取消按钮（强制更新模式下不会调用）
                Toast.makeText(context, "Update cancelled", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onDialogDismissed() {
                // 弹窗被关闭
                Toast.makeText(context, "Update dialog dismissed", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 处理更新按钮点击事件
     * @param context 上下文
     * @param downloadUrl 下载链接
     */
    private static void handleUpdateClick(Context context, String downloadUrl) {
        if (downloadUrl != null && !downloadUrl.isEmpty()) {
            // 如果有下载链接，打开浏览器下载
            try {
                Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse(downloadUrl));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                Toast.makeText(context, "Opening download page...", Toast.LENGTH_SHORT).show();
            } catch (Exception e) {
                Toast.makeText(context, "Failed to open download page", Toast.LENGTH_SHORT).show();
                e.printStackTrace();
            }
        } else {
            // 如果没有下载链接，可以跳转到应用商店
            openAppStore(context);
        }
    }

    /**
     * 打开应用商店
     * @param context 上下文
     */
    private static void openAppStore(Context context) {
        try {
            // 尝试打开Google Play Store
            String packageName = context.getPackageName();
            Intent intent = new Intent(Intent.ACTION_VIEW, Uri.parse("market://details?id=" + packageName));
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            context.startActivity(intent);
        } catch (Exception e) {
            // 如果无法打开应用商店，打开网页版
            try {
                String packageName = context.getPackageName();
                Intent intent = new Intent(Intent.ACTION_VIEW, 
                    Uri.parse("https://play.google.com/store/apps/details?id=" + packageName));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
                Toast.makeText(context, "Opening Play Store...", Toast.LENGTH_SHORT).show();
            } catch (Exception ex) {
                Toast.makeText(context, "Failed to open app store", Toast.LENGTH_SHORT).show();
                ex.printStackTrace();
            }
        }
    }

    /**
     * 检查版本更新的示例方法
     * 在实际应用中，这里应该调用服务器API检查版本
     * @param context 上下文
     * @param currentVersion 当前版本号
     */
    public static void checkForUpdates(Context context, String currentVersion) {
        // 模拟版本检查逻辑
        // 在实际应用中，这里应该是网络请求
        
        // 模拟服务器返回的最新版本信息
        String latestVersion = "1.2.0";
        String downloadUrl = "https://example.com/download/app-v1.2.0.apk";
        
        // 比较版本号（这里简化处理，实际应用中需要更复杂的版本比较逻辑）
        if (!currentVersion.equals(latestVersion)) {
            // 有新版本，显示更新弹窗
            showUpdateDialog(context, latestVersion, downloadUrl);
        } else {
            Toast.makeText(context, "You are using the latest version", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 在Activity中使用的示例
     * 可以在Activity的onCreate或onResume中调用
     */
    public static class ActivityUsageExample {
        
        public void checkUpdateInActivity(Context context) {
            // 获取当前版本号
            String currentVersion = getCurrentVersion(context);
            
            // 检查更新
            checkForUpdates(context, currentVersion);
        }
        
        /**
         * 获取当前应用版本号
         * @param context 上下文
         * @return 版本号字符串
         */
        private String getCurrentVersion(Context context) {
            try {
                return context.getPackageManager()
                    .getPackageInfo(context.getPackageName(), 0)
                    .versionName;
            } catch (Exception e) {
                e.printStackTrace();
                return "1.0.0";
            }
        }
    }
}
