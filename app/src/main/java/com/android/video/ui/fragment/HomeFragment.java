package com.android.video.ui.fragment;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import android.widget.LinearLayout;
import android.graphics.Typeface;
import android.util.TypedValue;
import androidx.core.content.ContextCompat;
import java.util.Objects;
import java.util.Collections;
import java.util.Comparator;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.adapter.HomeCarouselAdapter;
import com.android.video.adapter.HomeVideoGridAdapter;
import com.android.video.adapter.HomeContinueWatchingAdapter;
import com.android.video.adapter.TagAdapter;
import com.android.video.adapter.ComingSoonAdapter;
import com.android.video.adapter.BestForYouAdapter;
import com.android.video.adapter.FeaturedSectionAdapter;
import com.android.video.adapter.TodaysHotAdapter;
import com.android.video.adapter.PopularSeriesAdapter;
import com.android.video.model.VideoModel;
import com.android.video.model.TagModel;
import com.android.video.model.ActorModel;
import com.android.video.model.DirectorModel;
import com.android.video.model.EpisodeModel;
import com.android.video.ui.activity.*;
import com.android.video.utils.TestValidationUtils;
import com.android.video.network.HomeApiService;
import com.android.video.network.MyHistoryListApiService;
import com.android.video.model.BannerModel;
import com.android.video.model.FeaturedModel;
import com.android.video.model.MyHistoryDataModel;
import com.android.video.model.MyHistoryItemModel;
import com.android.video.model.response.MyHistoryListResponseModel;
import com.android.video.model.DailyRankModel;
import com.android.video.model.response.DailyRankDataModel;
import com.android.video.model.WorthWaitingModel;
import com.android.video.model.response.WorthWaitingDataModel;
import com.android.video.model.GuessYouLikeModel;
import com.android.video.model.FeaturedFilmModel;
import com.android.video.model.MostPopularModel;
import com.android.video.model.TodayHotReleaseModel;
import com.android.video.model.response.CategoryModel;
import com.android.video.model.response.CategoryListResponseModel;
import com.android.video.model.response.CategoryWithFilmsModel;
import com.android.video.model.response.CategoryFilmModel;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.bumptech.glide.Glide;
import com.android.video.ui.component.ImageLoadingAnimationManager;
import com.android.video.cache.DataCacheManager;
import com.android.video.cache.ImprovedDataCacheManager;
import com.android.video.cache.SmartCacheManager;
import com.android.video.utils.OptimizedImageLoader;
import com.android.video.manager.FragmentCacheManager;
import com.android.video.base.BaseMultiLanguageFragment;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 首页Fragment - 显示视频推荐内容
 * 继承BaseFullScreenFragment实现全屏显示和状态栏处理
 * <AUTHOR>
 */
public class HomeFragment extends BaseMultiLanguageFragment {

    private static final String TAG = "HomeFragment";

    // Fragment缓存管理器
    private FragmentCacheManager fragmentCacheManager;
    private String fragmentKey = "HomeFragment";

    // 下拉刷新组件
    private SwipeRefreshLayout swipeRefreshLayout;

    // Banner区域组件
    private ImageView ivBannerPoster;
    private ImageView ivHomeLogo;
    private ImageView ivSearchIcon;
    private RecyclerView rvCarousel;
    private ImageView ivPlayButton;

    // Categories区域组件
    private TextView tvCategoriesTitle;
    private TextView tvCategoriesSeeAll;
    private ImageView ivCategoriesNext;
    private RecyclerView rvCategoryTags;
    private RecyclerView rvVideoGrid;

    // Continue Watching区域组件
    private TextView tvContinueWatchingTitle;
    private TextView tvContinueWatchingSeeAll;
    private ImageView ivContinueWatchingNext;
    private RecyclerView rvContinueWatching;

    // Coming Soon区域组件
    private TextView tvComingSoonTitle;
    private TextView tvComingSoonSeeAll;
    private ImageView ivComingSoonNext;
    private RecyclerView rvComingSoon;

    // Best For You区域组件
    private TextView tvBestForYouTitle;
    private RecyclerView rvBestForYou;

    // 动态推荐位容器
    private LinearLayout llDynamicFeaturedContainer;

    // Today's Hot区域组件
    private TextView tvTodaysHotTitle;
    private TextView tvTodaysHotSeeAll;
    private ImageView ivTodaysHotNext;
    private RecyclerView rvTodaysHot;

    // Popular Series区域组件
    private TextView tvPopularSeriesTitle;
    private TextView tvPopularSeriesSeeAll;
    private ImageView ivPopularSeriesNext;
    private RecyclerView rvPopularSeries;

    // 适配器
    private HomeCarouselAdapter carouselAdapter;
    private TagAdapter categoryTagAdapter;
    private HomeVideoGridAdapter videoGridAdapter;
    private HomeContinueWatchingAdapter continueWatchingAdapter;

    // 独立的视频数据源
    private List<VideoModel> featuredCategoriesVideos;  // Featured API的Categories数据
    private List<VideoModel> categoryFilmsVideos;       // 分类短剧API数据
    private String currentVideoDataSource = "featured"; // 当前显示的数据源：featured 或 category
    private ComingSoonAdapter comingSoonAdapter;
    private BestForYouAdapter bestForYouAdapter;
    // 动态推荐位适配器列表
    private List<FeaturedSectionAdapter> dynamicFeaturedAdapters = new ArrayList<>();
    private TodaysHotAdapter todaysHotAdapter;
    private PopularSeriesAdapter popularSeriesAdapter;

    // API服务
    private HomeApiService homeApiService;
    private MyHistoryListApiService myHistoryListApiService;

    // 分类名称到ID的映射
    private Map<String, String> categoryNameToIdMap = new HashMap<>();

    // 当前选择的分类ID（null表示ALL选项）
    private String currentSelectedCategoryId = null;

    public HomeFragment() {
        // Required empty public constructor
    }

    public static HomeFragment newInstance() {
        return new HomeFragment();
    }

    @Override
    public void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Log.d(TAG, "HomeFragment onCreate called");

        // 初始化Fragment缓存管理器
        fragmentCacheManager = FragmentCacheManager.getInstance();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_home, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        // 初始化API服务
        homeApiService = HomeApiService.getInstance();
        myHistoryListApiService = MyHistoryListApiService.getInstance();

        // 初始化独立的视频数据源
        if (featuredCategoriesVideos == null) {
            featuredCategoriesVideos = new ArrayList<>();
        }
        if (categoryFilmsVideos == null) {
            categoryFilmsVideos = new ArrayList<>();
        }

        initViews(view);
        setupAdapters();
        setupClickListeners();

        // 初始化Continue Watching区域显示
        initializeContinueWatchingSection();

        // 使用FragmentCacheManager智能数据加载策略
        if (fragmentCacheManager.isFirstLoad(fragmentKey)) {
            Log.d(TAG, "首次进入Home页面，强制刷新数据");
            loadRealData(); // 首次进入直接加载，不使用缓存
        } else {
            Log.d(TAG, "非首次进入，优先使用缓存数据");
            // 先尝试从缓存恢复数据
            boolean cacheRestored = restoreDataFromCache();
            if (cacheRestored) {
                Log.d(TAG, "成功从缓存恢复数据");
                // 确保所有UI组件正确显示
                ensureAllUIComponentsDisplay();
            } else if (fragmentCacheManager.shouldReloadData(fragmentKey)) {
                Log.d(TAG, "缓存恢复失败且需要重新加载数据");
                loadRealDataWithCache();
            } else {
                Log.d(TAG, "缓存恢复失败但不需要重新加载，显示空状态");
                ensureContinueWatchingDisplay();
            }
        }
    }

    /**
     * 初始化视图组件
     */
    private void initViews(View view) {
        // 下拉刷新组件
        swipeRefreshLayout = view.findViewById(R.id.swipe_refresh_layout);

        // Banner区域组件
        ivBannerPoster = view.findViewById(R.id.iv_banner_poster);
        ivHomeLogo = view.findViewById(R.id.iv_home_logo);
        ivSearchIcon = view.findViewById(R.id.iv_search_icon);
        rvCarousel = view.findViewById(R.id.rv_carousel);
        ivPlayButton = view.findViewById(R.id.iv_play_button);

        // Categories区域组件
        tvCategoriesTitle = view.findViewById(R.id.tv_categories_title);
        tvCategoriesSeeAll = view.findViewById(R.id.tv_categories_see_all);
        ivCategoriesNext = view.findViewById(R.id.iv_categories_next);
        rvCategoryTags = view.findViewById(R.id.rv_category_tags);
        rvVideoGrid = view.findViewById(R.id.rv_video_grid);

        // Continue Watching区域组件
        tvContinueWatchingTitle = view.findViewById(R.id.tv_continue_watching_title);
        tvContinueWatchingSeeAll = view.findViewById(R.id.tv_continue_watching_see_all);
        ivContinueWatchingNext = view.findViewById(R.id.iv_continue_watching_next);
        rvContinueWatching = view.findViewById(R.id.rv_continue_watching);

        // Coming Soon区域组件
        tvComingSoonTitle = view.findViewById(R.id.tv_coming_soon_title);
        tvComingSoonSeeAll = view.findViewById(R.id.tv_coming_soon_see_all);
        ivComingSoonNext = view.findViewById(R.id.iv_coming_soon_next);
        rvComingSoon = view.findViewById(R.id.rv_coming_soon);

        // Best For You区域组件
        tvBestForYouTitle = view.findViewById(R.id.tv_best_for_you_title);
        rvBestForYou = view.findViewById(R.id.rv_best_for_you);

        // 动态推荐位容器
        llDynamicFeaturedContainer = view.findViewById(R.id.ll_dynamic_featured_container);

        // Today's Hot区域组件
        tvTodaysHotTitle = view.findViewById(R.id.tv_todays_hot_title);
        tvTodaysHotSeeAll = view.findViewById(R.id.tv_todays_hot_see_all);
        ivTodaysHotNext = view.findViewById(R.id.iv_todays_hot_next);
        rvTodaysHot = view.findViewById(R.id.rv_todays_hot);

        // Popular Series区域组件
        tvPopularSeriesTitle = view.findViewById(R.id.tv_popular_series_title);
        tvPopularSeriesSeeAll = view.findViewById(R.id.tv_popular_series_see_all);
        ivPopularSeriesNext = view.findViewById(R.id.iv_popular_series_next);
        rvPopularSeries = view.findViewById(R.id.rv_popular_series);
    }

    /**
     * 设置适配器和布局管理器
     */
    private void setupAdapters() {
        try {
            // 轮播图适配器
            carouselAdapter = new HomeCarouselAdapter();
            rvCarousel.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvCarousel.setAdapter(carouselAdapter);

            // 分类标签适配器
            categoryTagAdapter = new TagAdapter();
            rvCategoryTags.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvCategoryTags.setAdapter(categoryTagAdapter);

            // 视频网格适配器 (自适应网格)
            videoGridAdapter = new HomeVideoGridAdapter();
            setupAdaptiveGridLayout();
            rvVideoGrid.setAdapter(videoGridAdapter);

            // 为主要的RecyclerView添加滑动增强功能
            setupScrollEnhancements();

            // 继续观看适配器
            continueWatchingAdapter = new HomeContinueWatchingAdapter();
            rvContinueWatching.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvContinueWatching.setAdapter(continueWatchingAdapter);

            // Coming Soon适配器
            comingSoonAdapter = new ComingSoonAdapter();
            rvComingSoon.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvComingSoon.setAdapter(comingSoonAdapter);

            // Best For You适配器
            bestForYouAdapter = new BestForYouAdapter();
            rvBestForYou.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvBestForYou.setAdapter(bestForYouAdapter);

            // 初始化动态推荐位适配器列表
            dynamicFeaturedAdapters = new ArrayList<>();

            // Today's Hot适配器
            todaysHotAdapter = new TodaysHotAdapter();
            rvTodaysHot.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
            rvTodaysHot.setAdapter(todaysHotAdapter);

            // Popular Series适配器 (垂直布局)
            popularSeriesAdapter = new PopularSeriesAdapter();
            rvPopularSeries.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.VERTICAL, false));
            rvPopularSeries.setAdapter(popularSeriesAdapter);

            Log.d(TAG, "All adapters initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Error setting up adapters", e);
        }
    }

    /**
     * 设置滑动增强功能
     * <p>
     * 需求：滑动惯性衰减系数 0.9，快速滑动 3 屏显示进度条
     * </p>
     */
    private void setupScrollEnhancements() {
        try {
            // 创建进度条（用于快速滑动时显示）
            android.widget.ProgressBar scrollProgressBar = new android.widget.ProgressBar(
                getContext(), null, android.R.attr.progressBarStyleHorizontal);
            scrollProgressBar.setVisibility(android.view.View.GONE);

            // 为主要的RecyclerView添加滑动增强
            if (rvVideoGrid != null) {
                com.android.video.utils.ScrollEnhancementUtils.enhanceRecyclerViewScrolling(
                    rvVideoGrid, scrollProgressBar);
            }

            // 为Popular Series RecyclerView添加滑动增强
            if (rvPopularSeries != null) {
                com.android.video.utils.ScrollEnhancementUtils.enhanceRecyclerViewScrolling(
                    rvPopularSeries, scrollProgressBar);
            }

            Log.d(TAG, "✅ 滑动增强功能设置完成");
        } catch (Exception e) {
            Log.e(TAG, "设置滑动增强功能失败: " + e.getMessage());
        }
    }

    /**
     * 设置点击事件监听器
     */
    private void setupClickListeners() {
        // 下拉刷新监听器
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setOnRefreshListener(() -> {
                Log.d(TAG, "触发下拉刷新");
                refreshAllData();
            });

            // 设置刷新动画颜色
            swipeRefreshLayout.setColorSchemeResources(
                android.R.color.holo_blue_bright,
                android.R.color.holo_green_light,
                android.R.color.holo_orange_light,
                android.R.color.holo_red_light
            );
        }

        // 搜索图标点击事件
        if (ivSearchIcon != null) {
            ivSearchIcon.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), SearchActivity.class);
                startActivity(intent);
            });
        }

        // Banner海报点击事件 - 跳转到视频详情页
        if (ivBannerPoster != null) {
            ivBannerPoster.setOnClickListener(v -> {
                // 获取当前选中的轮播图视频
                VideoModel selectedVideo = carouselAdapter.getSelectedVideo();
                if (selectedVideo != null) {
                    navigateToVideoDetailPage(selectedVideo);
                } else {
                    Toast.makeText(getContext(), "没有选中的视频", Toast.LENGTH_SHORT).show();
                }
            });
        }

        // 播放按钮点击事件
        if (ivPlayButton != null) {
            ivPlayButton.setOnClickListener(v -> {
                // 获取当前选中的轮播图视频
                VideoModel selectedVideo = carouselAdapter.getSelectedVideo();
                if (selectedVideo != null) {
                    navigateToVideoPlayer(selectedVideo);
                } else {
                    Toast.makeText(getContext(), "没有选中的视频", Toast.LENGTH_SHORT).show();
                }
            });
        }

        // Categories See All点击事件
        if (tvCategoriesSeeAll != null) {
            tvCategoriesSeeAll.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), com.android.video.ui.activity.CategoriesActivity.class);
                startActivity(intent);
            });
        }

        // Categories箭头按钮点击事件
        if (ivCategoriesNext != null) {
            ivCategoriesNext.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), com.android.video.ui.activity.CategoriesActivity.class);
                startActivity(intent);
            });
        }

        // Continue Watching See All点击事件
        if (tvContinueWatchingSeeAll != null) {
            tvContinueWatchingSeeAll.setOnClickListener(v -> {
                // 检查登录状态
                if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                    return;
                }

                // 跳转到我的片单-观看历史页面
                if (getActivity() instanceof MainActivity) {
                    MainActivity mainActivity = (MainActivity) getActivity();
                    // 切换到MyList标签页并显示观看历史
                    mainActivity.switchToMyListWithHistory();
                }
            });
        }

        // Continue Watching箭头按钮点击事件
        if (ivContinueWatchingNext != null) {
            ivContinueWatchingNext.setOnClickListener(v -> {
                // 检查登录状态
                if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                    return;
                }

                // 跳转到我的片单-观看历史页面
                if (getActivity() instanceof MainActivity) {
                    MainActivity mainActivity = (MainActivity) getActivity();
                    // 切换到MyList标签页并显示观看历史
                    mainActivity.switchToMyListWithHistory();
                }
            });
        }

        // Coming Soon See All点击事件
        if (tvComingSoonSeeAll != null) {
            tvComingSoonSeeAll.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), com.android.video.ui.activity.ComingSoonActivity.class);
                startActivity(intent);
            });
        }

        // Coming Soon箭头按钮点击事件
        if (ivComingSoonNext != null) {
            ivComingSoonNext.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), com.android.video.ui.activity.ComingSoonActivity.class);
                startActivity(intent);
            });
        }



        // Today's Hot See All点击事件
        if (tvTodaysHotSeeAll != null) {
            tvTodaysHotSeeAll.setOnClickListener(v ->
                Toast.makeText(getContext(), "查看所有Today's Hot", Toast.LENGTH_SHORT).show());
        }

        // Popular Series See All点击事件
        if (tvPopularSeriesSeeAll != null) {
            tvPopularSeriesSeeAll.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), MostPopularActivity.class);
                startActivity(intent);
            });
        }

        // Popular Series箭头按钮点击事件
        if (ivPopularSeriesNext != null) {
            ivPopularSeriesNext.setOnClickListener(v -> {
                Intent intent = new Intent(getContext(), MostPopularActivity.class);
                startActivity(intent);
            });
        }

        // 轮播图点击事件 - 更新背景海报和选中状态
        carouselAdapter.setOnCarouselClickListener((video, position) -> {
            // 更新背景海报为点击的视频封面
            updateBackgroundPoster(video);

            Log.d(TAG, "轮播图点击: " + video.getDisplayTitle() + ", 位置: " + position);
            // 注意：轮播图的选中状态更新在适配器内部的updateSelectedPosition方法中处理
        });

        // 分类标签点击事件
        categoryTagAdapter.setOnTagClickListener((tag, position) -> {
            Log.d(TAG, "分类标签点击: " + tag.getDisplayName());

            // 获取对应的分类ID
            String categoryId;
            if ("ALL".equals(tag.getName())) {
                categoryId = null; // ALL表示查询所有分类
            } else {
                categoryId = categoryNameToIdMap.get(tag.getName());
            }

            // 更新当前选择的分类ID
            currentSelectedCategoryId = categoryId;

            Log.d(TAG, "映射的分类ID: " + categoryId);
            Log.d(TAG, "当前选择的分类ID: " + currentSelectedCategoryId);
            Log.d(TAG, "当前映射表: " + categoryNameToIdMap.toString());

            // 显示加载状态
            if (swipeRefreshLayout != null) {
                swipeRefreshLayout.setRefreshing(true);
            }

            // 重新加载分类短剧数据
            loadCategoriesWithFilmsData(categoryId);

            Toast.makeText(getContext(),
                "已切换到: " + tag.getDisplayName(),
                Toast.LENGTH_SHORT).show();
        });

        // 视频网格点击事件
        if (videoGridAdapter != null) {
            videoGridAdapter.setOnVideoClickListener((video, position) -> {
                navigateToVideoDetail(video);
            });
        }

        // 继续观看点击事件
        if (continueWatchingAdapter != null) {
            continueWatchingAdapter.setOnContinueWatchingClickListener(new HomeContinueWatchingAdapter.OnContinueWatchingClickListener() {
                @Override
                public void onContinueWatchingClick(VideoModel video, int position) {
                    // Continue Watching应该直接跳转到播放页面，而不是详情页面
                    navigateToContinueWatching(video);
                }

                @Override
                public void onContinueWatchingLongClick(VideoModel video, int position) {
                    Toast.makeText(getContext(),
                        "长按: " + video.getDisplayTitle() + " (进度: " + String.format("%.1f%%", video.getWatchProgress() * 100) + ")",
                        Toast.LENGTH_LONG).show();
                }
            });
        }

        // Coming Soon适配器点击事件 - 跳转到视频预告详情页
        if (comingSoonAdapter != null) {
            comingSoonAdapter.setOnVideoClickListener((video, position) -> {
                navigateToVideoPreviewDetail(video);
            });
        }

        if (comingSoonAdapter != null) {
            comingSoonAdapter.setOnSubscriptionChangeListener((video, position, isSubscribed) -> {
                String message = isSubscribed ? "已订阅: " : "取消订阅: ";
                Toast.makeText(getContext(),
                    message + video.getDisplayTitle(),
                    Toast.LENGTH_SHORT).show();
            });
        }

        // Best For You适配器点击事件
        if (bestForYouAdapter != null) {
            bestForYouAdapter.setOnVideoClickListener((video, position) -> {
                navigateToVideoDetail(video);
            });
        }

        // Today's Hot适配器点击事件
        if (todaysHotAdapter != null) {
            todaysHotAdapter.setOnVideoClickListener((video, position) -> {
                navigateToVideoDetail(video);
            });
        }

        // Popular Series适配器点击事件
        if (popularSeriesAdapter != null) {
            popularSeriesAdapter.setOnVideoClickListener((video, position) -> {
                navigateToVideoDetail(video);
            });
        }
    }

    /**
     * 初始化Continue Watching区域
     * 确保区域在Fragment创建时就显示，避免首次进入时看不到
     */
    private void initializeContinueWatchingSection() {
        // 首次进入时显示Continue Watching区域（空状态）
        showContinueWatchingEmptyState();
        Log.d(TAG, "初始化Continue Watching区域，显示空状态等待数据加载");
    }

    /**
     * 加载真实数据（带缓存支持）
     */
    private void loadRealDataWithCache() {
        Log.d(TAG, "开始加载数据（带缓存支持）");

        // 先尝试从缓存恢复数据
        if (restoreDataFromCache()) {
            Log.d(TAG, "成功从缓存恢复数据，同时在后台调用接口进行数据比对");
            fragmentCacheManager.updateCacheState(fragmentKey, true);
            fragmentCacheManager.updateDataLoaded(fragmentKey, true);

            // 在后台调用接口进行数据比对
            loadRealDataInBackground();
            return;
        }

        // 缓存中没有数据，进行网络请求
        Log.d(TAG, "缓存中无数据，开始网络请求");
        loadRealData();
    }

    /**
     * 在后台加载数据并与缓存比对
     */
    private void loadRealDataInBackground() {
        Log.d(TAG, "开始后台数据加载和比对");

        // 在后台线程执行，避免影响UI
        new Thread(() -> {
            try {
                // 获取当前缓存的数据作为比对基准
                List<VideoModel> cachedCarouselData = getCachedCarouselData();
                List<VideoModel> cachedVideoGridData = getCachedVideoGridData();

                // 后台调用接口获取最新数据
                loadDataForComparison(cachedCarouselData, cachedVideoGridData);

            } catch (Exception e) {
                Log.e(TAG, "后台数据比对过程中发生错误: " + e.getMessage());
            }
        }).start();
    }

    /**
     * 加载真实数据
     */
    private void loadRealData() {
        // 加载真实Banner数据
        loadBannerData();

        // 加载真实Featured推荐位数据
        loadFeaturedData();

        // 加载真实分类标签数据
        loadCategoryListData();

        // 加载真实分类短剧数据（默认加载所有分类）
        loadCategoriesWithFilmsData();

        // 加载继续观看历史记录数据
        loadContinueWatchingData();

        // 加载今日热门数据
        loadTodaysHotData();

        // 加载即将来袭数据
        loadComingSoonData();

        // 加载最受欢迎数据 (Popular Series)
        loadPopularSeriesData();
    }

    /**
     * 加载Banner数据
     */
    private void loadBannerData() {
        Log.d(TAG, "开始加载Banner数据 - 检查缓存状态");

        homeApiService.getBanners(HomeApiConstantsUtils.LOCATION_HOME, new HomeApiService.BannerCallback() {
            @Override
            public void onSuccess(List<BannerModel> banners) {
                Log.d(TAG, "Banner数据加载成功，数量: " + banners.size());

                // 将BannerModel转换为VideoModel
                List<VideoModel> carouselList = convertBannersToVideoModels(banners);

                // 更新轮播图适配器
                if (carouselAdapter != null) {
                    carouselAdapter.updateCarouselList(carouselList);

                    // 更新背景海报（显示第一个Banner的封面）
                    if (!carouselList.isEmpty() && ivBannerPoster != null) {
                        VideoModel firstVideo = carouselList.get(0);
                        String posterUrl = firstVideo.getPosterUrl();

                        if (posterUrl != null && !posterUrl.isEmpty()) {
                            // 使用优化的图片加载器智能加载Banner封面图片
                            OptimizedImageLoader.loadImageSmart(
                                getContext(),
                                posterUrl,
                                ivBannerPoster,
                                R.drawable.movie_poster
                            );

                            Log.d(TAG, "更新背景海报: " + firstVideo.getTitle() + ", URL: " + posterUrl);
                        } else {
                            Log.w(TAG, "Banner封面URL为空，使用默认图片");
                            ivBannerPoster.setImageResource(R.drawable.movie_poster);
                        }
                    }

                    // 保存数据到缓存并更新Fragment缓存状态
                    saveDataToCache();
                    fragmentCacheManager.updateDataLoaded(fragmentKey, true);
                    fragmentCacheManager.updateCacheState(fragmentKey, true);
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Banner数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                loadCarouselTestData();

                if (getContext() != null) {
                    Toast.makeText(getContext(), "Banner加载失败，使用测试数据", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 将BannerModel列表转换为VideoModel列表
     *
     * @param banners Banner列表
     * @return VideoModel列表
     */
    private List<VideoModel> convertBannersToVideoModels(List<BannerModel> banners) {
        List<VideoModel> videoModels = new ArrayList<>();

        for (BannerModel banner : banners) {
            if (banner != null) {
                // 创建VideoModel
                VideoModel videoModel = new VideoModel(
                    banner.getFilmId() != null ? banner.getFilmId() : banner.getBannerId(),
                    banner.getBannerName() != null ? banner.getBannerName() : "未知视频",
                    banner.getBannerCover() != null ? banner.getBannerCover() : "",
                    "Banner"
                );

                // 检查Banner数据是否有filmLanguageInfoId字段
                Log.d(TAG, "Banner数据 - filmId: " + banner.getFilmId() + ", filmLanguageInfoId: " + banner.getFilmLanguageInfoId());

                // 设置filmLanguageInfoId（如果API提供了这个字段）
                if (banner.getFilmLanguageInfoId() != null && !banner.getFilmLanguageInfoId().isEmpty()) {
                    videoModel.setFilmLanguageInfoId(banner.getFilmLanguageInfoId());
                    Log.d(TAG, "Banner设置filmLanguageInfoId: " + banner.getFilmLanguageInfoId());
                } else {
                    Log.w(TAG, "Banner缺少filmLanguageInfoId，将无法使用详情接口");
                    // 添加标识，让VideoDetailActivity知道这是Banner数据
                    videoModel.addTag("banner_id:" + banner.getFilmId());
                }

                // 设置额外信息
                videoModel.setDescription("来自Banner推荐");

                // 添加语言标签
                if (banner.getLanguageType() != null) {
                    videoModel.addTag(banner.getLanguageTypeText());
                }

                // 存储原始Banner数据（用于点击跳转）
                videoModel.addTag("banner_id:" + banner.getBannerId());
                if (banner.getFilmId() != null) {
                    videoModel.addTag("film_id:" + banner.getFilmId());
                }

                videoModels.add(videoModel);

                Log.d(TAG, "转换Banner: " + banner.getBannerName() + " -> " + videoModel.getTitle());
            }
        }

        return videoModels;
    }

    /**
     * 更新背景海报
     *
     * @param video 视频数据
     */
    private void updateBackgroundPoster(VideoModel video) {
        if (video == null || ivBannerPoster == null) {
            return;
        }

        String posterUrl = video.getPosterUrl();

        if (posterUrl != null && !posterUrl.isEmpty()) {
            // 使用优化的图片加载器智能加载视频封面图片
            OptimizedImageLoader.loadImageSmart(
                getContext(),
                posterUrl,
                ivBannerPoster,
                R.drawable.movie_poster
            );

            Log.d(TAG, "更新背景海报: " + video.getTitle() + ", URL: " + posterUrl);
        } else {
            Log.w(TAG, "视频封面URL为空，使用默认图片");
            ivBannerPoster.setImageResource(R.drawable.movie_poster);
        }
    }

    /**
     * 加载Featured推荐位数据
     */
    private void loadFeaturedData() {
        Log.d(TAG, "开始加载Featured推荐位数据");

        homeApiService.getFeatured(new HomeApiService.FeaturedCallback() {
            @Override
            public void onSuccess(List<FeaturedModel> featuredList) {
                Log.d(TAG, "Featured数据加载成功，推荐位数量: " + featuredList.size());

                // 处理各个推荐位的数据
                processFeaturedData(featuredList);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Featured数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                loadFeaturedTestData();

                if (getContext() != null) {
                    Toast.makeText(getContext(), "推荐位加载失败，使用测试数据", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 加载分类标签列表数据
     */
    private void loadCategoryListData() {
        Log.d(TAG, "开始加载分类标签列表数据");

        homeApiService.getCategoryList(new HomeApiService.CategoryListCallback() {
            @Override
            public void onSuccess(List<CategoryModel> categories) {
                Log.d(TAG, "分类标签列表数据加载成功，数量: " + categories.size());

                // 将CategoryModel数据转换为TagModel
                List<TagModel> categoryTags = convertCategoriesToTagModels(categories);

                // 更新分类标签适配器
                if (categoryTagAdapter != null) {
                    categoryTagAdapter.updateTagList(categoryTags);
                    Log.d(TAG, "✅ 更新分类标签: " + categoryTags.size() + "个标签");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "分类标签列表数据加载失败: " + errorMessage);

                // 构建详细的错误信息用于调试
                String detailedError = "分类标签API调用失败\n" +
                                     "API地址: " + EnvironmentConfigUtils.getApiBaseUrl() + HomeApiConstantsUtils.API_GET_CATEGORY_LIST + "\n" +
                                     "错误信息: " + errorMessage;
                Log.e(TAG, detailedError);

                // 加载失败时使用测试数据作为后备
                loadCategoryTagsTestData();

                if (getContext() != null) {
                    // 在调试模式下显示详细错误信息
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "分类标签加载失败: " + errorMessage
                        : "分类标签加载失败，使用测试数据";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    /**
     * 将CategoryModel数据转换为TagModel列表
     *
     * @param categories 分类标签列表
     * @return TagModel列表
     */
    private List<TagModel> convertCategoriesToTagModels(List<CategoryModel> categories) {
        List<TagModel> tagModels = new ArrayList<>();

        // 清空之前的映射
        categoryNameToIdMap.clear();

        // 添加默认的"ALL"标签
        tagModels.add(new TagModel("ALL", "category", true)); // 默认选中ALL
        categoryNameToIdMap.put("ALL", null); // ALL对应null，表示查询所有分类

        for (CategoryModel category : categories) {
            if (category != null && category.getCategoryName() != null && category.isActive()) {
                // 只显示正常状态的分类（isDelete = 1）
                String tagName = category.getCategoryName();
                String categoryId = category.getCategoryId();

                // 创建TagModel
                TagModel tagModel = new TagModel(tagName, "category");
                tagModels.add(tagModel);

                // 保存分类名称到ID的映射
                categoryNameToIdMap.put(tagName, categoryId);

                Log.d(TAG, "转换分类标签: " + category.getCategoryName() +
                          " (ID: " + category.getCategoryId() +
                          ", Weight: " + category.getWeight() +
                          ", Language: " + category.getLanguage() + ") -> " +
                          tagModel.getDisplayName());
            }
        }

        Log.d(TAG, "分类标签转换完成，共 " + tagModels.size() + " 个标签（包含ALL）");
        Log.d(TAG, "分类映射: " + categoryNameToIdMap.toString());
        return tagModels;
    }

    /**
     * 从分类短剧数据中更新分类标签
     *
     * @param categories 分类短剧列表
     */
    private void updateCategoryTagsFromCategories(List<CategoryWithFilmsModel> categories) {
        List<TagModel> tagModels = new ArrayList<>();

        // 清空之前的映射
        categoryNameToIdMap.clear();

        // 添加默认的"ALL"标签
        tagModels.add(new TagModel("ALL", "category", true)); // 默认选中ALL
        categoryNameToIdMap.put("ALL", null); // ALL对应null，表示查询所有分类

        for (CategoryWithFilmsModel category : categories) {
            // 显示所有有效的分类标签，不管是否有短剧内容
            if (category != null && category.getCategoryName() != null && category.isValid()) {

                String tagName = category.getCategoryName();
                String categoryId = category.getCategoryId();

                // 创建TagModel
                TagModel tagModel = new TagModel(tagName, "category");
                tagModels.add(tagModel);

                // 保存分类名称到ID的映射
                categoryNameToIdMap.put(tagName, categoryId);

                Log.d(TAG, "提取分类标签: " + category.getCategoryName() +
                          " (ID: " + category.getCategoryId() +
                          ", 短剧数: " + category.getFilmCount() +
                          ", 有短剧: " + category.hasFilms() + ")");
            } else {
                // 记录跳过的无效分类
                if (category != null) {
                    Log.d(TAG, "跳过无效分类标签: " + category.getCategoryName() +
                              " (有效: " + category.isValid() + ")");
                }
            }
        }

        // 更新分类标签适配器
        if (categoryTagAdapter != null) {
            categoryTagAdapter.updateTagList(tagModels);
            Log.d(TAG, "✅ 更新分类标签: " + tagModels.size() + "个标签（包含ALL）");
        }

        Log.d(TAG, "分类映射: " + categoryNameToIdMap.toString());
    }

    /**
     * 刷新所有数据
     */
    private void refreshAllData() {
        Log.d(TAG, "开始刷新所有数据");

        // 显示刷新动画
        if (swipeRefreshLayout != null) {
            swipeRefreshLayout.setRefreshing(true);
        }

        // 重置Fragment缓存状态，强制重新加载
        fragmentCacheManager.resetFragmentState(fragmentKey);

        // 清除相关缓存数据
        clearCacheData();

        // 重新加载所有数据
        loadRealData();
    }

    /**
     * 清除缓存数据（使用改进的缓存管理器）
     */
    private void clearCacheData() {
        if (getContext() == null) {
            return;
        }

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());

            // 清理传统缓存键
            cacheManager.clearCachedData("home_carousel_data");
            cacheManager.clearCachedData("home_category_tags");
            cacheManager.clearCachedData("home_continue_watching");
            // 注意：home_video_grid已被Featured精细化缓存替代

            // 清理Featured相关的所有缓存（使用命名空间清理）
            cacheManager.clearCacheNamespace("home_featured");

            Log.d(TAG, "清除首页所有缓存数据完成");
        } catch (Exception e) {
            Log.e(TAG, "清除首页缓存数据失败", e);
        }
    }

    /**
     * 停止刷新动画
     */
    private void stopRefreshing() {
        if (swipeRefreshLayout != null && swipeRefreshLayout.isRefreshing()) {
            swipeRefreshLayout.setRefreshing(false);
            Log.d(TAG, "停止刷新动画");
        }
    }

    /**
     * 加载分类短剧列表数据
     */
    private void loadCategoriesWithFilmsData() {
        loadCategoriesWithFilmsData(null); // 默认加载所有分类
    }

    /**
     * 加载分类短剧列表数据（支持分类过滤）
     *
     * @param categoryId 分类ID，为null时查询所有分类
     */
    private void loadCategoriesWithFilmsData(String categoryId) {
        Log.d(TAG, "开始加载分类短剧列表数据 - 分类ID: " + (categoryId != null ? categoryId : "ALL"));

        homeApiService.getCategoriesWithFilms(categoryId, 1, 20, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                Log.d(TAG, "分类短剧列表数据加载成功");

                if (response.hasData()) {
                    List<CategoryWithFilmsModel> categories = response.getCategories();
                    Log.d(TAG, "接收到 " + categories.size() + " 个分类数据");

                    // 打印每个分类的详细信息
                    for (int i = 0; i < categories.size(); i++) {
                        CategoryWithFilmsModel category = categories.get(i);
                        Log.d(TAG, "分类 " + i + ": " + category.getCategoryName() +
                                  " (ID: " + category.getCategoryId() +
                                  ", 短剧数: " + category.getFilmCount() + ")");
                    }

                    // 更新视频内容（不再更新分类标签）
                    List<VideoModel> videoList = convertCategoriesWithFilmsToVideoModels(categories, categoryId);
                    Log.d(TAG, "转换得到 " + videoList.size() + " 个视频");

                    // 更新视频网格适配器（使用新的数据隔离逻辑）
                    updateVideoGridWithCategoryData(videoList, categoryId);
                } else {
                    Log.w(TAG, "分类短剧列表响应无数据");
                    if (categoryId == null) {
                        // 只有在加载所有分类时才使用测试数据
                        loadVideoGridTestData();
                    }
                }

                // 停止刷新动画
                stopRefreshing();
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "分类短剧列表数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                loadVideoGridTestData();

                if (getContext() != null) {
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "分类短剧加载失败: " + errorMessage
                        : "分类短剧加载失败，使用测试数据";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }

                // 停止刷新动画
                stopRefreshing();
            }
        });
    }

    /**
     * 将分类短剧数据转换为VideoModel列表
     *
     * @param categories 分类短剧列表
     * @param categoryId 分类ID，为null时表示ALL选项
     * @return VideoModel列表
     */
    private List<VideoModel> convertCategoriesWithFilmsToVideoModels(List<CategoryWithFilmsModel> categories, String categoryId) {
        List<VideoModel> videoList = new ArrayList<>();

        for (CategoryWithFilmsModel category : categories) {
            // 检查分类是否有效且有短剧数据
            if (category != null && category.isValid() && category.hasFilms()) {
                List<CategoryFilmModel> films = category.getFilms();
                Log.d(TAG, "处理分类: " + category.getCategoryName() + ", 短剧数量: " + films.size());

                for (CategoryFilmModel film : films) {
                    if (film != null && film.isValid()) {
                        // 创建VideoModel
                        VideoModel video = new VideoModel(
                            film.getFilmId(),
                            film.getFilmTitle(),
                            film.getCover(),
                            category.getCategoryName()
                        );

                        // 设置filmLanguageInfoId（重要：用于调用详情接口）
                        if (film.getFilmLanguageInfoId() != null) {
                            video.setFilmLanguageInfoId(film.getFilmLanguageInfoId());
                        }

                        // 设置额外信息
                        if (film.getDetails() != null) {
                            video.setSynopsis(film.getDetails());
                        }

                        // 设置播放次数
                        if (film.getPlayNum() != null) {
                            video.setViewCount(film.getPlayNum());
                        }

                        // 设置章节信息
                        if (film.getTotalChaptersNum() != null) {
                            video.setTotalEpisodes(film.getTotalChaptersNum());
                        }

                        // 设置喜欢状态
                        if (film.getIsLove() != null) {
                            video.setLiked(film.getIsLove());
                        }

                        videoList.add(video);

                        Log.d(TAG, "转换短剧: " + film.getFilmTitle() +
                                  " (分类: " + category.getCategoryName() +
                                  ", 语言: " + film.getLanguageTypeDescription() + ")");

                        // 如果是ALL选项且已经达到4个视频，停止添加
                        if (categoryId == null && videoList.size() >= 4) {
                            Log.d(TAG, "ALL选项限制：已达到4个视频上限，停止添加");
                            break;
                        }
                    } else {
                        Log.d(TAG, "跳过无效短剧: " + (film != null ? film.getFilmTitle() : "null"));
                    }
                }

                // 如果是ALL选项且已经达到4个视频，停止处理其他分类
                if (categoryId == null && videoList.size() >= 4) {
                    Log.d(TAG, "ALL选项限制：已达到4个视频上限，停止处理其他分类");
                    break;
                }
            } else {
                // 记录跳过的分类
                if (category != null) {
                    Log.d(TAG, "跳过分类: " + category.getCategoryName() +
                              " (有效: " + category.isValid() +
                              ", 有短剧: " + category.hasFilms() +
                              ", 短剧数量: " + category.getFilmCount() + ")");
                } else {
                    Log.d(TAG, "跳过null分类");
                }
            }
        }

        Log.d(TAG, "分类短剧转换完成，共 " + videoList.size() + " 个视频");
        return videoList;
    }

    /**
     * 获取缓存的轮播图数据
     */
    private List<VideoModel> getCachedCarouselData() {
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());
            return cacheManager.getCachedDataAtomic("home_carousel_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "获取缓存轮播图数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 获取缓存的视频网格数据
     */
    private List<VideoModel> getCachedVideoGridData() {
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());
            return cacheManager.getCachedDataAtomic("home_video_grid_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){}.getType());
        } catch (Exception e) {
            Log.e(TAG, "获取缓存视频网格数据失败: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 后台加载数据进行比对
     */
    private void loadDataForComparison(List<VideoModel> cachedCarouselData, List<VideoModel> cachedVideoGridData) {
        Log.d(TAG, "开始后台数据比对，缓存轮播图数量: " + (cachedCarouselData != null ? cachedCarouselData.size() : 0) +
                   ", 缓存视频网格数量: " + (cachedVideoGridData != null ? cachedVideoGridData.size() : 0));

        // 后台调用Banner接口
        homeApiService.getBanners(HomeApiConstantsUtils.LOCATION_HOME, new HomeApiService.BannerCallback() {
            @Override
            public void onSuccess(List<BannerModel> banners) {
                List<VideoModel> newCarouselData = convertBannersToVideoModels(banners);

                // 比对轮播图数据
                if (!isDataEqual(cachedCarouselData, newCarouselData)) {
                    Log.d(TAG, "轮播图数据有更新，刷新UI");
                    updateCarouselDataInUI(newCarouselData);
                } else {
                    Log.d(TAG, "轮播图数据无变化");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "后台轮播图数据获取失败: " + errorMessage);
            }
        });

        // 后台调用分类短剧接口，使用当前选择的分类ID
        homeApiService.getCategoriesWithFilms(currentSelectedCategoryId, 1, 20, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                if (response.hasData()) {
                    List<CategoryWithFilmsModel> categories = response.getCategories();
                    List<VideoModel> newVideoGridData = convertCategoriesWithFilmsToVideoModels(categories, currentSelectedCategoryId);

                    // 比对视频网格数据
                    if (!isDataEqual(cachedVideoGridData, newVideoGridData)) {
                        Log.d(TAG, "视频网格数据有更新，刷新UI（分类ID: " + currentSelectedCategoryId + "）");
                        updateVideoGridDataInUI(newVideoGridData);
                    } else {
                        Log.d(TAG, "视频网格数据无变化（分类ID: " + currentSelectedCategoryId + "）");
                    }
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "后台视频网格数据获取失败（分类ID: " + currentSelectedCategoryId + "）: " + errorMessage);
            }
        });
    }

    /**
     * 比对两个VideoModel列表是否相等
     */
    private boolean isDataEqual(List<VideoModel> cachedData, List<VideoModel> newData) {
        if (cachedData == null && newData == null) return true;
        if (cachedData == null || newData == null) return false;
        if (cachedData.size() != newData.size()) return false;

        for (int i = 0; i < cachedData.size(); i++) {
            VideoModel cached = cachedData.get(i);
            VideoModel newVideo = newData.get(i);

            if (!isVideoEqual(cached, newVideo)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 比对两个VideoModel是否相等
     */
    private boolean isVideoEqual(VideoModel video1, VideoModel video2) {
        if (video1 == null && video2 == null) return true;
        if (video1 == null || video2 == null) return false;

        return Objects.equals(video1.getId(), video2.getId()) &&
               Objects.equals(video1.getDisplayTitle(), video2.getDisplayTitle()) &&
               Objects.equals(video1.getPosterUrl(), video2.getPosterUrl()) &&
               Objects.equals(video1.getCategory(), video2.getCategory());
    }

    /**
     * 在UI线程更新轮播图数据
     */
    private void updateCarouselDataInUI(List<VideoModel> newCarouselData) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                try {
                    if (carouselAdapter != null) {
                        carouselAdapter.updateCarouselList(newCarouselData);

                        // 更新大图显示
                        if (!newCarouselData.isEmpty()) {
                            updateBackgroundPoster(newCarouselData.get(0));
                        }

                        // 更新缓存
                        saveCarouselDataToCache(newCarouselData);

                        Log.d(TAG, "轮播图数据已更新到UI");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "更新轮播图UI时发生错误: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 在UI线程更新视频网格数据
     */
    private void updateVideoGridDataInUI(List<VideoModel> newVideoGridData) {
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                try {
                    if (videoGridAdapter != null) {
                        videoGridAdapter.updateVideoList(newVideoGridData);

                        // 更新缓存
                        saveVideoGridDataToCache(newVideoGridData);

                        Log.d(TAG, "视频网格数据已更新到UI");
                    }
                } catch (Exception e) {
                    Log.e(TAG, "更新视频网格UI时发生错误: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 保存轮播图数据到缓存
     */
    private void saveCarouselDataToCache(List<VideoModel> carouselData) {
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());
            cacheManager.cacheDataAtomic("home_carousel_data", carouselData);
            Log.d(TAG, "轮播图数据已保存到缓存");
        } catch (Exception e) {
            Log.e(TAG, "保存轮播图数据到缓存失败: " + e.getMessage());
        }
    }

    /**
     * 保存视频网格数据到缓存
     */
    private void saveVideoGridDataToCache(List<VideoModel> videoGridData) {
        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());
            cacheManager.cacheDataAtomic("home_video_grid_data", videoGridData);
            Log.d(TAG, "视频网格数据已保存到缓存");
        } catch (Exception e) {
            Log.e(TAG, "保存视频网格数据到缓存失败: " + e.getMessage());
        }
    }

    /**
     * 将Banner数据转换为TagModel列表（保留作为后备方法）
     *
     * @param banners Banner列表
     * @return TagModel列表
     */
    private List<TagModel> convertBannersToTagModels(List<BannerModel> banners) {
        List<TagModel> tagModels = new ArrayList<>();

        // 添加默认的"ALL"标签
        tagModels.add(new TagModel("ALL", "category", true)); // 默认选中ALL

        for (BannerModel banner : banners) {
            if (banner != null && banner.getBannerName() != null) {
                // 使用Banner名称作为分类标签
                String tagName = banner.getBannerName();

                // 如果categoryId不是"all"，可以使用更具体的分类名称
                if (banner.getCategoryId() != null && !banner.isAllCategoryBanner()) {
                    // 可以根据categoryId获取更具体的分类名称
                    // 这里暂时使用bannerName
                }

                TagModel tagModel = new TagModel(tagName, "category");

                // 注意：TagModel没有addTag方法，Banner相关信息可以通过其他方式存储
                // 如果需要存储Banner信息，可以扩展TagModel类或使用其他方式

                tagModels.add(tagModel);

                Log.d(TAG, "转换分类标签: " + banner.getBannerName() + " -> " + tagModel.getDisplayName());
            }
        }

        return tagModels;
    }

    /**
     * 处理Featured推荐位数据
     * 更新主推荐区域，并在Today's Hot后动态创建推荐位
     *
     * @param featuredList 推荐位列表
     */
    private void processFeaturedData(List<FeaturedModel> featuredList) {
        Log.d(TAG, "开始处理Featured推荐位数据，推荐位数量: " + featuredList.size());

        // 首先缓存原始Featured响应
        cacheFeaturedRawData(featuredList);

        // 找到主要的推荐位数据
        FeaturedModel mainRecommendation = null;

        // 按推荐位类型分别处理和缓存
        for (FeaturedModel featured : featuredList) {
            if (featured == null || !featured.hasAnyData()) {
                continue;
            }

            String featuredName = featured.getFeaturedName();
            String dataType = featured.getDataType();
            int dataCount = featured.getDataCount();

            Log.d(TAG, "处理推荐位: " + featuredName + ", 数据类型: " + dataType + ", 数据数量: " + dataCount);

            // 优先选择有内容的推荐位作为主推荐
            if (mainRecommendation == null || dataCount > mainRecommendation.getDataCount()) {
                mainRecommendation = featured;
            }

            // 根据推荐位类型处理不同的数据结构
            if (featured.isCategoriesFeatured() && featured.hasCategories()) {
                // Categories -> 处理分类数据
                List<VideoModel> categoryVideos = featured.toVideoModelList();
                updateVideoGridWithVideoData(categoryVideos);
                Log.d(TAG, "✅ 更新主推荐区域(Categories): " + categoryVideos.size() + "个视频");
            } else if (featured.isContinueWatchingFeatured() && featured.hasPlayHistory()) {
                // Continue Watching -> 处理播放历史数据
                List<VideoModel> historyVideos = featured.toVideoModelList();
                Log.d(TAG, "✅ 处理Continue Watching推荐位: " + historyVideos.size() + "个视频");
            } else if (featured.isBestForYouFeatured() && featured.hasGuessYouLike()) {
                // Best For You -> 处理猜你喜欢数据
                List<VideoModel> guessVideos = featured.toVideoModelList();
                Log.d(TAG, "✅ 处理Best For You推荐位: " + guessVideos.size() + "个视频");
            } else if (featured.isTodayHotFeatured() && featured.hasTodayHotRelease()) {
                // Today's Hot -> 处理今日热映数据
                List<VideoModel> hotVideos = featured.toVideoModelList();
                Log.d(TAG, "✅ 处理Today's Hot推荐位: " + hotVideos.size() + "个视频");
            } else if (featured.isMostPopularFeatured() && featured.hasMostPopular()) {
                // Most Popular -> 处理最受欢迎数据
                List<VideoModel> popularVideos = featured.toVideoModelList();
                Log.d(TAG, "✅ 处理Most Popular推荐位: " + popularVideos.size() + "个视频");
            } else if (featured.isComingSoonFeatured() && featured.hasWorthWaiting()) {
                // Coming Soon -> 处理即将来袭数据
                List<VideoModel> comingVideos = featured.toVideoModelList();
                Log.d(TAG, "✅ 处理Coming Soon推荐位: " + comingVideos.size() + "个视频");
            } else if (featured.hasFilms()) {
                // 普通推荐位 -> 处理影片数据
                List<FeaturedFilmModel> films = featured.getFilms();
                cacheFeaturedFilms("home_featured_" + featuredName.toLowerCase().replace(" ", "_") + "_films", films);
                Log.d(TAG, "✅ 缓存普通推荐位: " + featuredName + ", " + films.size() + "个视频");
            }
        }

        // 如果没有Categories推荐位，使用内容最多的推荐位更新主推荐区域
        if (mainRecommendation != null) {
            boolean hasCategoriesData = featuredList.stream().anyMatch(featured ->
                featured.isCategoriesFeatured() && featured.hasCategories());
            if (!hasCategoriesData) {
                List<VideoModel> mainVideos = mainRecommendation.toVideoModelList();
                updateVideoGridWithVideoData(mainVideos);
                Log.d(TAG, "✅ 使用主推荐位更新推荐区域: " + mainRecommendation.getFeaturedName() +
                           ", " + mainVideos.size() + "个视频");
            }
        }

        // 动态创建推荐位
        createDynamicFeaturedSections(featuredList);

        // 其他区域使用测试数据
        loadOtherTestData();
    }

    /**
     * 动态创建推荐位区域
     * 在Today's Hot区域后面添加API返回的推荐位
     *
     * @param featuredList 推荐位列表
     */
    private void createDynamicFeaturedSections(List<FeaturedModel> featuredList) {
        if (llDynamicFeaturedContainer == null || featuredList == null) {
            Log.w(TAG, "动态推荐位容器为空或推荐位列表为空");
            return;
        }

        // 确保dynamicFeaturedAdapters不为null
        if (dynamicFeaturedAdapters == null) {
            dynamicFeaturedAdapters = new ArrayList<>();
            Log.d(TAG, "dynamicFeaturedAdapters was null, initialized new ArrayList");
        }

        // 清空之前的动态推荐位
        llDynamicFeaturedContainer.removeAllViews();
        dynamicFeaturedAdapters.clear();

        Log.d(TAG, "开始创建动态推荐位，数量: " + featuredList.size());

        for (FeaturedModel featured : featuredList) {
            if (featured == null || !featured.hasAnyData()) {
                continue;
            }

            String featuredName = featured.getFeaturedName();

            // 跳过Categories推荐位（已在主推荐区域显示）
            if (featured.isCategoriesFeatured()) {
                Log.d(TAG, "跳过Categories推荐位: " + featuredName);
                continue;
            }

            // 转换为VideoModel列表
            List<VideoModel> videoList = featured.toVideoModelList();
            if (videoList.isEmpty()) {
                Log.d(TAG, "跳过空推荐位: " + featuredName);
                continue;
            }

            Log.d(TAG, "创建动态推荐位: " + featuredName + ", 视频数量: " + videoList.size());

            // 创建推荐位区域
            createFeaturedSectionWithVideoData(featuredName, videoList);
        }

        Log.d(TAG, "✅ 动态推荐位创建完成，共创建: " + dynamicFeaturedAdapters.size() + "个推荐位");
    }

    /**
     * 创建单个推荐位区域
     *
     * @param featuredName 推荐位名称
     * @param films 影片列表
     */
    private void createFeaturedSection(String featuredName, List<FeaturedFilmModel> films) {
        if (getContext() == null) {
            Log.w(TAG, "Context为空，无法创建推荐位");
            return;
        }

        // 创建推荐位容器
        LinearLayout sectionContainer = new LinearLayout(getContext());
        sectionContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        containerParams.topMargin = getResources().getDimensionPixelSize(R.dimen.home_module_section_margin_top);
        sectionContainer.setLayoutParams(containerParams);

        // 创建标题行
        View titleView = LayoutInflater.from(getContext()).inflate(R.layout.layout_featured_section_title, sectionContainer, false);
        TextView tvTitle = titleView.findViewById(R.id.tv_featured_title);
        tvTitle.setText(featuredName);
        sectionContainer.addView(titleView);

        // 创建RecyclerView
        RecyclerView recyclerView = new RecyclerView(getContext());
        LinearLayout.LayoutParams rvParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
        );
        rvParams.topMargin = getResources().getDimensionPixelSize(R.dimen.home_module_list_margin_top);
        rvParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.home_video_item_spacing_horizontal);
        recyclerView.setLayoutParams(rvParams);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setClipToPadding(false);
        recyclerView.setPadding(0, 0, getResources().getDimensionPixelSize(R.dimen.home_video_item_spacing_horizontal), 0);

        // 创建适配器
        FeaturedSectionAdapter adapter = new FeaturedSectionAdapter(featuredName);
        List<VideoModel> videoList = convertFeaturedFilmsToVideoModels(films);
        adapter.updateVideoList(videoList);

        // 设置点击事件
        adapter.setOnVideoClickListener((video, position, sectionName) -> {
            navigateToVideoDetail(video);
        });

        recyclerView.setAdapter(adapter);
        sectionContainer.addView(recyclerView);

        // 添加到容器
        llDynamicFeaturedContainer.addView(sectionContainer);
        dynamicFeaturedAdapters.add(adapter);

        Log.d(TAG, "✅ 创建推荐位: " + featuredName + ", 视频数量: " + videoList.size());
    }

    /**
     * 创建推荐位区域（使用VideoModel数据）
     */
    private void createFeaturedSectionWithVideoData(String featuredName, List<VideoModel> videoList) {
        if (getContext() == null || llDynamicFeaturedContainer == null) {
            Log.w(TAG, "Context或容器为空，无法创建推荐位");
            return;
        }

        // 创建推荐位容器
        LinearLayout sectionContainer = new LinearLayout(getContext());
        sectionContainer.setOrientation(LinearLayout.VERTICAL);
        LinearLayout.LayoutParams containerParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        sectionContainer.setLayoutParams(containerParams);

        // 创建标题
        TextView titleView = new TextView(getContext());
        titleView.setText(featuredName);
        titleView.setTextSize(TypedValue.COMPLEX_UNIT_SP, 18);
        titleView.setTextColor(ContextCompat.getColor(getContext(), R.color.white));
        titleView.setTypeface(null, Typeface.BOLD);
        LinearLayout.LayoutParams titleParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.WRAP_CONTENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        titleParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.home_video_item_spacing_horizontal);
        titleParams.topMargin = getResources().getDimensionPixelSize(R.dimen.home_module_title_margin_top);
        titleView.setLayoutParams(titleParams);
        sectionContainer.addView(titleView);

        // 创建RecyclerView
        RecyclerView recyclerView = new RecyclerView(getContext());
        LinearLayout.LayoutParams rvParams = new LinearLayout.LayoutParams(
            LinearLayout.LayoutParams.MATCH_PARENT,
            LinearLayout.LayoutParams.WRAP_CONTENT
        );
        rvParams.topMargin = getResources().getDimensionPixelSize(R.dimen.home_module_list_margin_top);
        rvParams.leftMargin = getResources().getDimensionPixelSize(R.dimen.home_video_item_spacing_horizontal);
        recyclerView.setLayoutParams(rvParams);
        recyclerView.setLayoutManager(new LinearLayoutManager(getContext(), LinearLayoutManager.HORIZONTAL, false));
        recyclerView.setNestedScrollingEnabled(false);
        recyclerView.setClipToPadding(false);
        recyclerView.setPadding(0, 0, getResources().getDimensionPixelSize(R.dimen.home_video_item_spacing_horizontal), 0);

        // 创建适配器
        FeaturedSectionAdapter adapter = new FeaturedSectionAdapter(featuredName);
        adapter.updateVideoList(videoList);

        // 设置点击事件
        adapter.setOnVideoClickListener((video, position, sectionName) -> {
            navigateToVideoDetail(video);
        });

        recyclerView.setAdapter(adapter);
        sectionContainer.addView(recyclerView);

        // 添加到容器
        llDynamicFeaturedContainer.addView(sectionContainer);
        dynamicFeaturedAdapters.add(adapter);

        Log.d(TAG, "✅ 创建推荐位: " + featuredName + ", 视频数量: " + videoList.size());
    }

    /**
     * 更新视频网格数据（Featured数据源）
     */
    private void updateVideoGridWithFeaturedData(List<FeaturedFilmModel> films) {
        // 更新Featured数据源
        featuredCategoriesVideos = convertFeaturedFilmsToVideoModels(films);

        // 设置当前数据源为Featured
        currentVideoDataSource = "featured";

        // 统一更新显示
        updateVideoGridDisplay();

        Log.d(TAG, "更新Featured视频网格数据，数量: " + featuredCategoriesVideos.size());
    }

    /**
     * 更新视频网格数据（VideoModel数据源）
     */
    private void updateVideoGridWithVideoData(List<VideoModel> videoList) {
        if (videoList != null && !videoList.isEmpty()) {
            // 更新Featured数据源
            featuredCategoriesVideos = new ArrayList<>(videoList);

            // 只有在用户没有主动选择分类时，才切换到Featured数据源
            // 如果用户已经选择了分类（包括ALL选项），保持当前的分类数据显示
            if (!"category".equals(currentVideoDataSource)) {
                currentVideoDataSource = "featured";
                // 统一更新显示
                updateVideoGridDisplay();
                Log.d(TAG, "更新VideoModel视频网格数据，数量: " + featuredCategoriesVideos.size());
            } else {
                Log.d(TAG, "用户已选择分类，保持分类数据显示，Featured数据已缓存但不切换显示");
            }
        } else {
            Log.w(TAG, "VideoModel数据为空，无法更新视频网格");
        }
    }

    /**
     * 更新视频网格数据（分类短剧数据源）
     */
    private void updateVideoGridWithCategoryData(List<VideoModel> videoList, String categoryId) {
        if (videoList != null && !videoList.isEmpty()) {
            // 更新分类短剧数据源
            categoryFilmsVideos = new ArrayList<>(videoList);

            // 始终使用分类数据源，因为这是用户通过分类标签触发的数据加载
            currentVideoDataSource = "category";

            // 统一更新显示
            updateVideoGridDisplay();

            Log.d(TAG, "更新分类短剧视频网格数据，数量: " + videoList.size() + " (分类ID: " + categoryId + ")");
        } else {
            // 清空分类数据
            categoryFilmsVideos = new ArrayList<>();

            if (categoryId == null) {
                // 加载所有分类时没有数据，使用测试数据
                loadVideoGridTestData();
            } else {
                // 特定分类没有内容
                if (getContext() != null) {
                    Toast.makeText(getContext(), "该分类暂无短剧内容", Toast.LENGTH_SHORT).show();
                }

                // 如果当前显示的是分类数据，切换回Featured数据
                if ("category".equals(currentVideoDataSource)) {
                    currentVideoDataSource = "featured";
                    updateVideoGridDisplay();
                }
            }

            Log.w(TAG, "分类短剧数据为空 (分类ID: " + categoryId + ")");
        }
    }

    /**
     * 加载其他区域的测试数据
     * 除了主推荐区域外，其他区域使用测试数据
     */
    private void loadOtherTestData() {
        Log.d(TAG, "加载其他区域的测试数据");

        // 加载Coming Soon动态数据
        loadComingSoonData();

        // 加载Best For You测试数据
        loadBestForYouData();

        // 加载Today's Hot动态数据
        loadTodaysHotData();

        Log.d(TAG, "✅ 其他区域测试数据加载完成");
    }

    /**
     * 获取当前应该显示的视频数据
     */
    private List<VideoModel> getCurrentVideoData() {
        switch (currentVideoDataSource) {
            case "featured":
                return featuredCategoriesVideos != null ? featuredCategoriesVideos : new ArrayList<>();
            case "category":
                return categoryFilmsVideos != null ? categoryFilmsVideos : new ArrayList<>();
            default:
                // 默认优先显示Featured数据
                if (featuredCategoriesVideos != null && !featuredCategoriesVideos.isEmpty()) {
                    return featuredCategoriesVideos;
                } else if (categoryFilmsVideos != null && !categoryFilmsVideos.isEmpty()) {
                    return categoryFilmsVideos;
                } else {
                    return new ArrayList<>();
                }
        }
    }

    /**
     * 统一的视频网格显示更新方法
     */
    private void updateVideoGridDisplay() {
        List<VideoModel> displayData = getCurrentVideoData();
        if (videoGridAdapter != null) {
            videoGridAdapter.updateVideoList(displayData);
            Log.d(TAG, "✅ 更新视频网格显示: " + displayData.size() + "个视频 (数据源: " + currentVideoDataSource + ")");
        }
    }

    /**
     * 将FeaturedFilmModel列表转换为VideoModel列表
     */
    private List<VideoModel> convertFeaturedFilmsToVideoModels(List<FeaturedFilmModel> films) {
        List<VideoModel> videoModels = new ArrayList<>();

        for (FeaturedFilmModel film : films) {
            if (film != null) {
                VideoModel videoModel = new VideoModel(
                    film.getFilmId() != null ? film.getFilmId() : "unknown",
                    film.getFilmTitle() != null ? film.getFilmTitle() : "未知视频",
                    film.getCover() != null ? film.getCover() : "",
                    film.getCategoryName() != null ? film.getCategoryName() : "推荐"
                );

                // 设置filmLanguageInfoId（重要：用于调用详情接口）
                if (film.getFilmLanguageInfoId() != null) {
                    videoModel.setFilmLanguageInfoId(film.getFilmLanguageInfoId());
                }

                // 设置额外信息
                videoModel.setDescription(film.getDetails() != null ? film.getDetails() : "");
                videoModel.setLiked(film.isLoved());

                if (film.getPlayNum() != null) {
                    videoModel.setViewCount(film.getPlayNum());
                }

                if (film.getTotalChaptersNum() != null) {
                    videoModel.setTotalEpisodes(film.getTotalChaptersNum());
                }

                // 添加语言标签
                if (film.getLanguageType() != null) {
                    videoModel.addTag(film.getLanguageTypeText());
                }

                // 存储原始数据用于跳转
                videoModel.addTag("film_id:" + film.getFilmId());
                if (film.getFilmLanguageInfoId() != null) {
                    videoModel.addTag("film_language_info_id:" + film.getFilmLanguageInfoId());
                }

                videoModels.add(videoModel);
            }
        }

        return videoModels;
    }

    /**
     * 加载Featured测试数据（后备方案）
     */
    private void loadFeaturedTestData() {
        loadComingSoonData(); // 使用动态数据
        loadBestForYouData(); // 使用测试数据

        loadTodaysHotData(); // 使用动态数据
    }

    /**
     * 加载测试数据
     */
    private void loadTestData() {
        loadCarouselTestData();
        loadCategoryTagsTestData();
        loadVideoGridTestData();
        loadContinueWatchingData();
        loadComingSoonData(); // 使用动态数据
        loadBestForYouData(); // 使用测试数据

        loadTodaysHotData(); // 使用动态数据
        loadPopularSeriesData(); // 使用动态数据 (Most Popular接口)
    }

    /**
     * 创建包含完整数据的VideoModel
     * 参考VideoDetailActivity.createTestVideoData方法的实现
     */
    private VideoModel createDetailedVideoModel(String id, String title, String category,
                                               String synopsis, int totalEpisodes) {
        VideoModel video = new VideoModel();
        video.setId(id);
        video.setTitle(title);
        video.setCategory(category);
        video.setSynopsis(synopsis);
        video.setViewCount(generateRandomViewCount());
        video.setLiked(false);
        video.setTotalEpisodes(totalEpisodes);
        video.setCurrentEpisode(1);

        // 根据类别添加相应的标签
        addTagsByCategory(video, category);

        // 添加演员信息
        addActorsToVideo(video, category);

        // 添加导演信息
        addDirectorsToVideo(video, category);

        // 添加剧集信息（如果是多集内容）
        if (totalEpisodes > 1) {
            addEpisodesToVideo(video, totalEpisodes);
        }

        return video;
    }

    /**
     * 生成随机观看次数
     */
    private long generateRandomViewCount() {
        return (long) (Math.random() * 5000000) + 100000; // 10万到500万之间
    }

    /**
     * 根据类别添加标签
     */
    private void addTagsByCategory(VideoModel video, String category) {
        switch (category) {
            case "动画":
                video.addTag("动画");
                video.addTag("家庭");
                video.addTag("喜剧");
                break;
            case "电影":
                video.addTag("电影");
                video.addTag("动作");
                video.addTag("冒险");
                break;
            case "电视剧":
                video.addTag("电视剧");
                video.addTag("剧情");
                video.addTag("爱情");
                break;
            case "科幻":
                video.addTag("科幻");
                video.addTag("未来");
                video.addTag("技术");
                break;
            default:
                video.addTag("热门");
                video.addTag("推荐");
                break;
        }
    }

    /**
     * 为视频添加演员信息
     */
    private void addActorsToVideo(VideoModel video, String category) {
        // 根据类别添加不同的演员
        if ("动画".equals(category)) {
            video.addActor(new ActorModel("actor_001", "配音演员A", "", "主角配音", ""));
            video.addActor(new ActorModel("actor_002", "配音演员B", "", "配角配音", ""));
            video.addActor(new ActorModel("actor_003", "配音演员C", "", "特别出演", ""));
        } else {
            video.addActor(new ActorModel("actor_101", "演员甲", "", "主演", ""));
            video.addActor(new ActorModel("actor_102", "演员乙", "", "主演", ""));
            video.addActor(new ActorModel("actor_103", "演员丙", "", "配角", ""));
            video.addActor(new ActorModel("actor_104", "演员丁", "", "特别出演", ""));
        }
    }

    /**
     * 为视频添加导演信息
     */
    private void addDirectorsToVideo(VideoModel video, String category) {
        if ("动画".equals(category)) {
            video.addDirector(new DirectorModel("director_001", "动画导演A", "", "", "中国"));
            video.addDirector(new DirectorModel("director_002", "动画导演B", "", "", "中国"));
        } else {
            video.addDirector(new DirectorModel("director_101", "知名导演", "", "", "中国"));
        }
    }

    /**
     * 为视频添加剧集信息
     */
    private void addEpisodesToVideo(VideoModel video, int totalEpisodes) {
        for (int i = 1; i <= totalEpisodes; i++) {
            EpisodeModel episode = new EpisodeModel("episode_" + video.getId() + "_" + i,
                                                   i, "第" + i + "集");
            // 所有集数都标记为有直播图标
            episode.setLive(true);
            video.addEpisode(episode);
        }
    }

    /**
     * 加载轮播图测试数据
     */
    private void loadCarouselTestData() {
        List<VideoModel> carouselList = new ArrayList<>();

        carouselList.add(createDetailedVideoModel("carousel_001", "喜洋洋与灰太狼之筐出胜利", "动画",
            "喜羊羊与灰太狼系列的最新篇章，讲述了羊羊们在篮球场上与灰太狼展开激烈对决的故事。充满了友谊、团队合作和永不放弃的精神。", 52));

        carouselList.add(createDetailedVideoModel("carousel_002", "熊出没之重返地球", "动画",
            "熊大熊二和光头强意外来到神秘星球，在这里他们遇到了前所未有的挑战和奇遇。一场关于友谊与勇气的太空冒险即将展开。", 1));

        carouselList.add(createDetailedVideoModel("carousel_003", "超级飞侠环球大冒险", "动画",
            "乐迪和他的超级飞侠伙伴们继续环游世界，为世界各地的小朋友们送包裹，解决各种问题，传递友爱与帮助的正能量。", 104));

        // 确保适配器不为null
        if (carouselAdapter != null) {
            carouselAdapter.updateCarouselList(carouselList);
        } else {
            Log.w(TAG, "carouselAdapter is null, cannot update carousel data");
        }

        // 验证轮播图数据
        validateVideoListData(carouselList, "轮播图");
    }

    /**
     * 加载分类标签测试数据（最多4个标签）
     */
    private void loadCategoryTagsTestData() {
        List<TagModel> categoryTags = new ArrayList<>();
        categoryTags.add(new TagModel("ALL", "category", true)); // 默认选中ALL
        categoryTags.add(new TagModel("动画", "category"));
        categoryTags.add(new TagModel("电影", "category"));
        categoryTags.add(new TagModel("电视剧", "category"));

        if (categoryTagAdapter != null) {
            categoryTagAdapter.updateTagList(categoryTags);
        }
    }

    /**
     * 加载视频网格测试数据 (2x2网格，共4个视频)
     */
    private void loadVideoGridTestData() {
        List<VideoModel> videoList = new ArrayList<>();

        videoList.add(createDetailedVideoModel("grid_001", "海绵宝宝", "动画",
            "住在海底比奇堡的海绵宝宝和他的好朋友们的日常生活，充满了欢声笑语和无尽的想象力。", 26));

        videoList.add(createDetailedVideoModel("grid_002", "哆啦A梦", "动画",
            "来自未来的机器猫哆啦A梦和大雄的温馨日常，用神奇的道具帮助大雄解决各种问题。", 78));

        videoList.add(createDetailedVideoModel("grid_003", "猫和老鼠", "动画",
            "经典的猫鼠追逐游戏，汤姆猫和杰瑞鼠之间永恒的追逐与友谊，带来无数欢乐时光。", 164));

        videoList.add(createDetailedVideoModel("grid_004", "小猪佩奇", "动画",
            "小猪佩奇和她的家人朋友们的日常生活，教会孩子们关于友谊、家庭和成长的重要道理。", 52));

        // 确保适配器不为null
        if (videoGridAdapter != null) {
            videoGridAdapter.updateVideoList(videoList);
        } else {
            Log.w(TAG, "videoGridAdapter is null, cannot update video grid test data");
        }

        // 验证网格视频数据
        validateVideoListData(videoList, "视频网格");
    }

    /**
     * 加载继续观看数据（从历史记录API）
     */
    private void loadContinueWatchingData() {
        Log.d(TAG, "开始加载Continue Watching历史记录数据");

        myHistoryListApiService.getMyPlayHistoryList(1, 10, new MyHistoryListApiService.MyHistoryListCallback() {
            @Override
            public void onSuccess(MyHistoryListResponseModel response) {
                Log.d(TAG, "Continue Watching历史记录数据加载成功");

                if (response != null && response.isSuccess() && response.getData() != null) {
                    MyHistoryDataModel historyData = response.getData();
                    List<MyHistoryItemModel> historyItems = historyData.getRecords();

                    if (historyItems != null && !historyItems.isEmpty()) {
                        // 转换为VideoModel列表
                        List<VideoModel> continueWatchingList = new ArrayList<>();
                        for (MyHistoryItemModel item : historyItems) {
                            // 只显示未完成观看的视频
                            if (!item.isCompleted()) {
                                VideoModel video = item.toVideoModel();
                                continueWatchingList.add(video);
                            }
                        }

                        Log.d(TAG, "Continue Watching数据转换完成，未完成观看数量: " + continueWatchingList.size());

                        // 更新适配器和UI显示
                        updateContinueWatchingUI(continueWatchingList);
                    } else {
                        Log.d(TAG, "Continue Watching历史记录为空，显示空状态");
                        showContinueWatchingEmptyState();
                    }
                } else {
                    Log.e(TAG, "Continue Watching历史记录响应数据无效");
                    showContinueWatchingEmptyState();
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Continue Watching历史记录数据加载失败: " + errorMessage);
                // 加载失败时显示空状态，不显示假数据
                showContinueWatchingEmptyState();

                // 可选：显示错误提示
                if (getContext() != null) {
                    Toast.makeText(getContext(), "观看历史加载失败", Toast.LENGTH_SHORT).show();
                }
            }
        });
    }

    /**
     * 更新Continue Watching UI显示
     * @param continueWatchingList 继续观看列表
     */
    private void updateContinueWatchingUI(List<VideoModel> continueWatchingList) {
        if (continueWatchingList != null && !continueWatchingList.isEmpty()) {
            // 有数据时显示Continue Watching区域
            showContinueWatchingSection();

            // 更新适配器
            if (continueWatchingAdapter != null) {
                continueWatchingAdapter.updateContinueWatchingList(continueWatchingList);
                Log.d(TAG, "Continue Watching UI更新完成，显示 " + continueWatchingList.size() + " 个视频");
            } else {
                Log.w(TAG, "continueWatchingAdapter is null, cannot update continue watching data");
            }
        } else {
            // 没有数据时显示空状态
            showContinueWatchingEmptyState();
        }
    }

    /**
     * 显示Continue Watching区域
     */
    private void showContinueWatchingSection() {
        if (getView() != null) {
            View continueWatchingSection = getView().findViewById(R.id.ll_continue_watching_area);
            if (continueWatchingSection != null) {
                continueWatchingSection.setVisibility(View.VISIBLE);
                Log.d(TAG, "显示Continue Watching区域");
            } else {
                Log.w(TAG, "Continue Watching区域未找到 (ll_continue_watching_area)");
            }
        }
    }

    /**
     * 显示Continue Watching空状态
     */
    private void showContinueWatchingEmptyState() {
        // 始终显示Continue Watching区域
        showContinueWatchingSection();

        // 清空适配器数据，显示空状态
        if (continueWatchingAdapter != null) {
            continueWatchingAdapter.updateContinueWatchingList(new ArrayList<>());
            Log.d(TAG, "Continue Watching显示空状态");
        }
    }

    /**
     * 确保Continue Watching区域正确显示
     * 用于从其他页面返回时检查和恢复显示状态
     */
    private void ensureContinueWatchingDisplay() {
        if (continueWatchingAdapter != null) {
            int itemCount = continueWatchingAdapter.getItemCount();
            if (itemCount > 0) {
                // 有数据，确保区域显示
                showContinueWatchingSection();
                Log.d(TAG, "确保Continue Watching区域显示，当前有 " + itemCount + " 项数据");
            } else {
                // 没有数据，显示空状态
                showContinueWatchingEmptyState();
                Log.d(TAG, "Continue Watching无数据，显示空状态");
            }
        } else {
            // 适配器为空，显示空状态
            showContinueWatchingEmptyState();
            Log.d(TAG, "Continue Watching适配器为空，显示空状态");
        }
    }

    /**
     * 确保所有UI组件正确显示
     * 用于缓存恢复后检查和修复UI状态
     */
    private void ensureAllUIComponentsDisplay() {
        Log.d(TAG, "开始检查所有UI组件显示状态");

        // 1. 确保Continue Watching区域正确显示
        ensureContinueWatchingDisplay();

        // 2. 确保轮播图大图正确显示
        if (carouselAdapter != null && carouselAdapter.getItemCount() > 0) {
            List<VideoModel> carouselList = carouselAdapter.getCarouselList();
            if (carouselList != null && !carouselList.isEmpty()) {
                VideoModel firstVideo = carouselList.get(0);
                updateBackgroundPoster(firstVideo);
                Log.d(TAG, "确保轮播图大图显示: " + firstVideo.getDisplayTitle());
            }
        }

        // 3. 确保视频网格正确显示
        if (videoGridAdapter != null) {
            int gridItemCount = videoGridAdapter.getItemCount();
            Log.d(TAG, "视频网格当前显示: " + gridItemCount + " 项数据");

            // 如果网格没有数据，尝试刷新显示
            if (gridItemCount == 0) {
                updateVideoGridDisplay();
                Log.d(TAG, "视频网格无数据，尝试刷新显示");
            }
        }

        // 4. 确保其他适配器数据正确显示
        logAdapterStates();

        Log.d(TAG, "所有UI组件显示状态检查完成");
    }

    /**
     * 记录所有适配器的状态，用于调试
     */
    private void logAdapterStates() {
        Log.d(TAG, "=== 适配器状态检查 ===");
        Log.d(TAG, "轮播图适配器: " + (carouselAdapter != null ? carouselAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "分类标签适配器: " + (categoryTagAdapter != null ? categoryTagAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "视频网格适配器: " + (videoGridAdapter != null ? videoGridAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "Continue Watching适配器: " + (continueWatchingAdapter != null ? continueWatchingAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "Today's Hot适配器: " + (todaysHotAdapter != null ? todaysHotAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "Coming Soon适配器: " + (comingSoonAdapter != null ? comingSoonAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "Popular Series适配器: " + (popularSeriesAdapter != null ? popularSeriesAdapter.getItemCount() + " 项" : "null"));
        Log.d(TAG, "动态Featured适配器数量: " + (dynamicFeaturedAdapters != null ? dynamicFeaturedAdapters.size() : 0));
        Log.d(TAG, "========================");
    }

    /**
     * 隐藏Continue Watching区域（保留此方法用于特殊情况）
     */
    private void hideContinueWatchingSection() {
        if (getView() != null) {
            View continueWatchingSection = getView().findViewById(R.id.ll_continue_watching_area);
            if (continueWatchingSection != null) {
                continueWatchingSection.setVisibility(View.GONE);
                Log.d(TAG, "隐藏Continue Watching区域");
            } else {
                Log.w(TAG, "Continue Watching区域未找到 (ll_continue_watching_area)");
            }
        }

        // 清空适配器数据
        if (continueWatchingAdapter != null) {
            continueWatchingAdapter.updateContinueWatchingList(new ArrayList<>());
        }
    }

    /**
     * 加载Coming Soon数据
     */
    private void loadComingSoonData() {
        Log.d(TAG, "开始加载Coming Soon数据");

        homeApiService.getWorthWaiting(1, 10, new HomeApiService.WorthWaitingCallback() {
            @Override
            public void onSuccess(WorthWaitingDataModel worthWaitingData) {
                Log.d(TAG, "Coming Soon数据加载成功，数量: " + worthWaitingData.getRecordCount());

                // 将WorthWaitingDataModel转换为VideoModel列表
                List<VideoModel> comingSoonList = worthWaitingData.toVideoModelList();

                // 更新Coming Soon适配器
                if (comingSoonAdapter != null) {
                    comingSoonAdapter.updateVideoList(comingSoonList);
                    Log.d(TAG, "✅ 更新Coming Soon: " + comingSoonList.size() + "个视频");
                } else {
                    Log.w(TAG, "comingSoonAdapter is null, cannot update video list");
                }

                // 缓存数据（包括空数据）
                cacheComingSoonData(comingSoonList);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Coming Soon数据加载失败: " + errorMessage);

                // 清空Coming Soon列表，不再使用测试数据
                if (comingSoonAdapter != null) {
                    comingSoonAdapter.updateVideoList(new ArrayList<>());
                    Log.d(TAG, "Coming Soon数据加载失败，清空列表显示");
                }

                if (getContext() != null) {
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "Coming Soon加载失败: " + errorMessage
                        : "Coming Soon数据加载失败";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    /**
     * 加载Coming Soon测试数据（已废弃 - 不再使用静态数据）
     * @deprecated 现在完全使用动态数据，此方法仅保留用于调试目的
     */
    @Deprecated
    private void loadComingSoonTestData() {
        List<VideoModel> comingSoonList = new ArrayList<>();

        VideoModel video1 = createDetailedVideoModel("cs1", "哪吒之魔童降世2", "电影",
            "哪吒的全新冒险即将开始，这一次他将面临更大的挑战，守护自己珍视的一切。", 1);
        video1.setSubscribed(false);
        comingSoonList.add(video1);

        VideoModel video2 = createDetailedVideoModel("cs2", "姜子牙2", "电影",
            "姜子牙的故事还未结束，新的神话传说即将展开，带来更加震撼的视觉盛宴。", 1);
        video2.setSubscribed(true);
        comingSoonList.add(video2);

        VideoModel video3 = createDetailedVideoModel("cs3", "大圣归来2", "电影",
            "齐天大圣孙悟空的新冒险，这一次他将面临前所未有的挑战和考验。", 1);
        video3.setSubscribed(false);
        comingSoonList.add(video3);

        VideoModel video4 = createDetailedVideoModel("cs4", "白蛇3", "电影",
            "白素贞和许仙的爱情故事继续，新的传说即将在这个美丽的神话世界中展开。", 1);
        video4.setSubscribed(false);
        comingSoonList.add(video4);

        VideoModel video5 = createDetailedVideoModel("cs5", "罗小黑战记2", "电影",
            "小黑猫的新冒险，在这个充满魔法和奇迹的世界里，友谊和勇气将指引前进的道路。", 1);
        video5.setSubscribed(true);
        comingSoonList.add(video5);

        if (comingSoonAdapter != null) {
            comingSoonAdapter.updateVideoList(comingSoonList);
        } else {
            Log.w(TAG, "comingSoonAdapter is null, cannot update video list");
        }
    }

    /**
     * 加载Popular Series数据 (Most Popular接口)
     */
    private void loadPopularSeriesData() {
        Log.d(TAG, "开始加载Popular Series数据");

        homeApiService.getMostPopular(1, 10, new HomeApiService.DailyRankCallback() {
            @Override
            public void onSuccess(DailyRankDataModel mostPopularData) {
                Log.d(TAG, "Popular Series数据加载成功，数量: " + mostPopularData.getRecordCount());

                // 将DailyRankDataModel转换为VideoModel列表
                List<VideoModel> popularSeriesList = mostPopularData.toVideoModelList();

                // 更新Popular Series适配器
                if (popularSeriesAdapter != null) {
                    popularSeriesAdapter.updateVideoList(popularSeriesList);
                    Log.d(TAG, "✅ 更新Popular Series: " + popularSeriesList.size() + "个视频");
                } else {
                    Log.w(TAG, "popularSeriesAdapter is null, cannot update video list");
                }

                // 缓存数据（包括空数据）
                cachePopularSeriesData(popularSeriesList);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Popular Series数据加载失败: " + errorMessage);

                // 清空Popular Series列表，不再使用测试数据
                if (popularSeriesAdapter != null) {
                    popularSeriesAdapter.updateVideoList(new ArrayList<>());
                    Log.d(TAG, "Popular Series数据加载失败，清空列表显示");
                }

                if (getContext() != null) {
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "Popular Series加载失败: " + errorMessage
                        : "Popular Series数据加载失败";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    /**
     * 加载Best For You数据
     */
    private void loadBestForYouData() {
        Log.d(TAG, "开始加载Best For You数据");

        homeApiService.getGuessYouLike(new HomeApiService.GuessYouLikeCallback() {
            @Override
            public void onSuccess(List<GuessYouLikeModel> guessYouLikeList) {
                Log.d(TAG, "Best For You数据加载成功，数量: " + guessYouLikeList.size());

                // 根据匹配度从高到低排序
                Collections.sort(guessYouLikeList, new Comparator<GuessYouLikeModel>() {
                    @Override
                    public int compare(GuessYouLikeModel o1, GuessYouLikeModel o2) {
                        Integer match1 = o1.getMatchPercentage();
                        Integer match2 = o2.getMatchPercentage();

                        // 处理null值，null值排在最后
                        if (match1 == null && match2 == null) return 0;
                        if (match1 == null) return 1;
                        if (match2 == null) return -1;

                        // 从高到低排序
                        return match2.compareTo(match1);
                    }
                });

                Log.d(TAG, "Best For You数据已按匹配度排序");

                // 将排序后的GuessYouLikeModel列表转换为VideoModel列表
                List<VideoModel> bestForYouList = new ArrayList<>();
                for (GuessYouLikeModel guessYouLikeModel : guessYouLikeList) {
                    VideoModel videoModel = guessYouLikeModel.toVideoModel();
                    if (videoModel != null) {
                        bestForYouList.add(videoModel);
                        Log.d(TAG, "转换视频: " + videoModel.getDisplayTitle() +
                                  ", 匹配度: " + guessYouLikeModel.getMatchPercentage() + "%");
                    }
                }

                // 更新Best For You适配器
                if (bestForYouAdapter != null) {
                    bestForYouAdapter.updateVideoList(bestForYouList);
                    Log.d(TAG, "✅ 更新Best For You: " + bestForYouList.size() + "个视频");
                } else {
                    Log.w(TAG, "bestForYouAdapter is null, cannot update video list");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Best For You数据加载失败: " + errorMessage);

                // 加载失败时使用测试数据作为后备
                loadBestForYouTestData();

                if (getContext() != null) {
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "Best For You加载失败: " + errorMessage
                        : "Best For You加载失败，使用测试数据";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    /**
     * 为Best For You列表设置匹配百分比
     * 基于播放次数和排名计算匹配度
     */
    private void setBestForYouMatchPercentages(List<VideoModel> videoList) {
        if (videoList == null || videoList.isEmpty()) {
            return;
        }

        // 找到最大播放次数用于计算相对匹配度
        long maxViewCount = 0;
        for (VideoModel video : videoList) {
            if (video.getViewCount() > maxViewCount) {
                maxViewCount = video.getViewCount();
            }
        }

        // 为每个视频设置匹配百分比
        for (int i = 0; i < videoList.size(); i++) {
            VideoModel video = videoList.get(i);

            // 基于播放次数计算基础匹配度 (60-90%)
            int baseMatch = maxViewCount > 0 ?
                (int) (60 + (video.getViewCount() * 30.0 / maxViewCount)) : 75;

            // 基于排名调整匹配度 (排名越靠前，匹配度越高)
            int rankingBonus = Math.max(0, 10 - i * 2);

            // 最终匹配度 (70-95%)
            int finalMatch = Math.min(95, Math.max(70, baseMatch + rankingBonus));

            video.setMatchPercentage(finalMatch);
            Log.d(TAG, "设置匹配度: " + video.getTitle() + " = " + finalMatch + "%");
        }
    }

    /**
     * 加载Best For You测试数据
     */
    private void loadBestForYouTestData() {
        List<VideoModel> bestForYouList = new ArrayList<>();

        VideoModel video1 = new VideoModel("bfy1", "喜洋洋与灰太狼", "", "动画");
        video1.setMatchPercentage(95);
        bestForYouList.add(video1);

        VideoModel video2 = new VideoModel("bfy2", "喜洋洋与灰太狼", "", "动画");
        video2.setMatchPercentage(88);
        bestForYouList.add(video2);

        VideoModel video3 = new VideoModel("bfy3", "喜洋洋与灰太狼", "", "动画");
        video3.setMatchPercentage(92);
        bestForYouList.add(video3);

        VideoModel video4 = new VideoModel("bfy4", "喜洋洋与灰太狼", "", "动画");
        video4.setMatchPercentage(85);
        bestForYouList.add(video4);

        if (bestForYouAdapter != null) {
            bestForYouAdapter.updateVideoList(bestForYouList);
        } else {
            Log.w(TAG, "bestForYouAdapter is null, cannot update video list");
        }
    }



    /**
     * 加载Today's Hot数据
     */
    private void loadTodaysHotData() {
        Log.d(TAG, "开始加载Today's Hot数据");

        homeApiService.getDailyRank(1, 10, new HomeApiService.DailyRankCallback() {
            @Override
            public void onSuccess(DailyRankDataModel dailyRankData) {
                Log.d(TAG, "Today's Hot数据加载成功，数量: " + dailyRankData.getRecordCount());

                // 将DailyRankDataModel转换为VideoModel列表
                List<VideoModel> todaysHotList = dailyRankData.toVideoModelList();

                // 更新Today's Hot适配器
                if (todaysHotAdapter != null) {
                    todaysHotAdapter.updateVideoList(todaysHotList);
                    Log.d(TAG, "✅ 更新Today's Hot: " + todaysHotList.size() + "个视频");
                } else {
                    Log.w(TAG, "todaysHotAdapter is null, cannot update video list");
                }

                // 缓存数据（包括空数据）
                cacheTodaysHotData(todaysHotList);
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Today's Hot数据加载失败: " + errorMessage);

                // 清空Today's Hot列表，不再使用测试数据
                if (todaysHotAdapter != null) {
                    todaysHotAdapter.updateVideoList(new ArrayList<>());
                    Log.d(TAG, "Today's Hot数据加载失败，清空列表显示");
                }

                if (getContext() != null) {
                    String toastMessage = EnvironmentConfigUtils.isDebugMode()
                        ? "Today's Hot加载失败: " + errorMessage
                        : "Today's Hot数据加载失败";
                    Toast.makeText(getContext(), toastMessage, Toast.LENGTH_LONG).show();
                }
            }
        });
    }

    /**
     * 加载Today's Hot测试数据（已废弃 - 不再使用静态数据）
     * @deprecated 现在完全使用动态数据，此方法仅保留用于调试目的
     */
    @Deprecated
    private void loadTodaysHotTestData() {
        List<VideoModel> todaysHotList = new ArrayList<>();

        VideoModel video1 = new VideoModel("th1", "喜洋洋与灰太狼", "", "动画");
        video1.setRanking(1);
        todaysHotList.add(video1);

        VideoModel video2 = new VideoModel("th2", "喜洋洋与灰太狼", "", "动画");
        video2.setRanking(2);
        todaysHotList.add(video2);

        VideoModel video3 = new VideoModel("th3", "喜洋洋与灰太狼", "", "动画");
        video3.setRanking(3);
        todaysHotList.add(video3);

        VideoModel video4 = new VideoModel("th4", "喜洋洋与灰太狼", "", "动画");
        video4.setRanking(4);
        todaysHotList.add(video4);

        VideoModel video5 = new VideoModel("th5", "喜洋洋与灰太狼", "", "动画");
        video5.setRanking(5);
        todaysHotList.add(video5);

        if (todaysHotAdapter != null) {
            todaysHotAdapter.updateVideoList(todaysHotList);
        } else {
            Log.w(TAG, "todaysHotAdapter is null, cannot update video list");
        }
    }

    /**
     * 加载Popular Series测试数据
     */
    private void loadPopularSeriesTestData() {
        List<VideoModel> popularSeriesList = new ArrayList<>();

        VideoModel video1 = new VideoModel("ps1", "喜洋洋与灰太狼", "", "动画");
        video1.setViewCount(1200000); // 1.2M
        popularSeriesList.add(video1);

        VideoModel video2 = new VideoModel("ps2", "喜洋洋与灰太狼", "", "动画");
        video2.setViewCount(850000); // 850K
        popularSeriesList.add(video2);

        VideoModel video3 = new VideoModel("ps3", "喜洋洋与灰太狼", "", "动画");
        video3.setViewCount(2500000); // 2.5M
        popularSeriesList.add(video3);

        VideoModel video4 = new VideoModel("ps4", "喜洋洋与灰太狼", "", "动画");
        video4.setViewCount(1800000); // 1.8M
        popularSeriesList.add(video4);

        popularSeriesAdapter.updateVideoList(popularSeriesList);
    }

    /**
     * 刷新首页数据
     */
    public void refreshData() {
        loadTestData();
    }

    /**
     * 处理WindowInsets，确保内容正确显示
     */
    @Override
    protected void onApplyWindowInsets(androidx.core.view.WindowInsetsCompat insets) {
        super.onApplyWindowInsets(insets);
        // 首页使用全屏布局，内容自然延伸到状态栏区域
        // 具体的内容定位由布局中的margin和约束来控制
    }

    /**
     * 处理导航栏相关的布局调整
     */
    @Override
    protected void onApplyNavigationBarInsets(androidx.core.view.WindowInsetsCompat insets,
                                            int navigationBarHeight, boolean hasNavigationBar,
                                            boolean isGestureNavigation) {
        super.onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

        // 根据导航栏情况调整底部内容
        if (hasNavigationBar && !isGestureNavigation) {
            // 按钮导航栏需要为底部内容添加内边距，避免被遮挡
            // 这里可以调整RecyclerView或其他底部元素的padding
            adjustBottomContentForNavigationBar(navigationBarHeight);
        } else if (hasNavigationBar && isGestureNavigation) {
            // 手势导航栏通常是悬浮的，只需要很小的调整
            adjustBottomContentForGestureNavigation(navigationBarHeight);
        }

        // 调试信息（可选）
        if (getActivity() != null) {
            android.util.Log.d("HomeFragment", "导航栏适配 - 高度: " + navigationBarHeight +
                "px, 存在: " + hasNavigationBar + ", 手势导航: " + isGestureNavigation);
        }
    }

    /**
     * 为按钮导航栏调整底部内容
     */
    private void adjustBottomContentForNavigationBar(int navigationBarHeight) {
        // 示例：为主要内容容器添加底部内边距
        // 实际实现中，您可能需要调整特定的View或RecyclerView
        View rootView = getView();
        if (rootView != null) {
            // 这里可以调整具体的UI元素，例如最底部的RecyclerView
            // 为Popular Series RecyclerView添加底部内边距
            if (rvPopularSeries != null) {
                rvPopularSeries.setPadding(
                    rvPopularSeries.getPaddingLeft(),
                    rvPopularSeries.getPaddingTop(),
                    rvPopularSeries.getPaddingRight(),
                    navigationBarHeight
                );
            }
        }
    }

    /**
     * 为手势导航调整底部内容
     */
    private void adjustBottomContentForGestureNavigation(int navigationBarHeight) {
        // 手势导航通常只需要很小的调整，避免手势冲突
        int smallPadding = Math.min(navigationBarHeight / 3, 16); // 最多16dp

        View rootView = getView();
        if (rootView != null) {
            // 这里可以添加很小的底部内边距
            // 为Popular Series RecyclerView添加小的底部内边距
            if (rvPopularSeries != null) {
                rvPopularSeries.setPadding(
                    rvPopularSeries.getPaddingLeft(),
                    rvPopularSeries.getPaddingTop(),
                    rvPopularSeries.getPaddingRight(),
                    smallPadding
                );
            }
        }
    }

    /**
     * 设置自适应网格布局
     */
    private void setupAdaptiveGridLayout() {
        // 获取屏幕宽度
        int screenWidth = getResources().getDisplayMetrics().widthPixels;

        // 8dp边距转换为像素（减少padding让图片看起来更大）
        int marginPx = (int) (8 * getResources().getDisplayMetrics().density);

        // 可用宽度 = 屏幕宽度 - 左右边距
        int availableWidth = screenWidth - (marginPx * 2);

        // 最小item宽度 (120dp转换为像素)
        int minItemWidth = (int) (120 * getResources().getDisplayMetrics().density);

        // 计算列数
        int spanCount = Math.max(2, availableWidth / minItemWidth);

        // 创建GridLayoutManager
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), spanCount);
        rvVideoGrid.setLayoutManager(gridLayoutManager);

        // 设置8dp左右边距（减少padding让图片看起来更大）
        rvVideoGrid.setPadding(marginPx, 0, marginPx, 0);
        rvVideoGrid.setClipToPadding(false);

        // 添加item间距装饰器
        rvVideoGrid.addItemDecoration(new GridSpacingItemDecoration(spanCount,
            (int) (8 * getResources().getDisplayMetrics().density), true));
    }

    /**
     * 网格间距装饰器
     */
    private static class GridSpacingItemDecoration extends RecyclerView.ItemDecoration {
        private int spanCount;
        private int spacing;
        private boolean includeEdge;

        public GridSpacingItemDecoration(int spanCount, int spacing, boolean includeEdge) {
            this.spanCount = spanCount;
            this.spacing = spacing;
            this.includeEdge = includeEdge;
        }

        @Override
        public void getItemOffsets(android.graphics.Rect outRect, android.view.View view,
                                   RecyclerView parent, RecyclerView.State state) {
            int position = parent.getChildAdapterPosition(view);
            int column = position % spanCount;

            if (includeEdge) {
                outRect.left = spacing - column * spacing / spanCount;
                outRect.right = (column + 1) * spacing / spanCount;

                if (position < spanCount) {
                    outRect.top = spacing;
                }
                outRect.bottom = spacing;
            } else {
                outRect.left = column * spacing / spanCount;
                outRect.right = spacing - (column + 1) * spacing / spanCount;
                if (position >= spanCount) {
                    outRect.top = spacing;
                }
            }
        }
    }

    /**
     * 跳转到视频播放页面
     * @param video 视频数据
     */
    private void navigateToVideoDetail(VideoModel video) {
        if (video == null || getContext() == null) {
            return;
        }

        // 新的导航流程：主页视频海报 → 视频播放页面
        navigateToVideoPlayer(video);
    }

    /**
     * 从Continue Watching跳转到视频播放页面
     * 特殊处理：需要恢复观看进度
     * @param video 视频数据
     */
    private void navigateToContinueWatching(VideoModel video) {
        if (video == null || getContext() == null) {
            Log.w(TAG, "Cannot navigate to continue watching: video or context is null");
            return;
        }

        // 检查登录状态
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
            return;
        }

        Log.d(TAG, "Continue Watching点击: " + video.getDisplayTitle() +
                   ", 当前进度: " + video.getCurrentEpisode() + "/" + video.getTotalEpisodes());

        try {
            // 如果有filmLanguageInfoId，优先跳转到详情页面让用户选择剧集
            if (video.hasFilmLanguageInfoId()) {
                navigateToVideoDetailPageForContinueWatching(video);
            } else {
                // 否则直接跳转到播放页面
                navigateToVideoPlayerForContinueWatching(video);
            }
        } catch (Exception e) {
            Log.e(TAG, "Error navigating to continue watching", e);
            showToast("跳转失败，请重试");
        }
    }

    /**
     * 跳转到视频播放页面
     * @param video 视频数据
     */
    private void navigateToVideoPlayer(VideoModel video) {
        if (video == null) {
            Log.e(TAG, "Cannot navigate to video player: video is null");
            showToast("视频信息无效");
            return;
        }

        if (getContext() == null) {
            Log.e(TAG, "Cannot navigate to video player: context is null");
            return;
        }

        try {
            // 创建视频列表，将当前视频作为第一个
            List<VideoModel> videoList = new ArrayList<>();
            videoList.add(video);

            // 添加其他相关视频到列表中
            addRelatedVideosToList(videoList, video);

            // 验证视频列表
            if (videoList.isEmpty()) {
                Log.e(TAG, "Video list is empty after adding related videos");
                showToast("无法加载视频列表");
                return;
            }

            // 启动VideoPlayerActivity
            VideoPlayerActivity.start(getContext(), videoList, 0);
            Log.d(TAG, "Successfully navigated to video player with " + videoList.size() + " videos");

        } catch (Exception e) {
            Log.e(TAG, "Error navigating to video player", e);
            showToast("跳转失败，请重试");
        }
    }

    /**
     * 添加相关视频到播放列表
     * @param videoList 视频列表
     * @param currentVideo 当前视频
     */
    private void addRelatedVideosToList(List<VideoModel> videoList, VideoModel currentVideo) {
        // 从各个适配器中获取相关视频

        // 添加轮播图中的其他视频
        if (carouselAdapter != null) {
            List<VideoModel> carouselVideos = carouselAdapter.getCarouselList();
            for (VideoModel video : carouselVideos) {
                if (!video.getId().equals(currentVideo.getId()) && !videoList.contains(video)) {
                    videoList.add(video);
                }
            }
        }

        // 添加视频网格中的其他视频
        if (videoGridAdapter != null) {
            List<VideoModel> gridVideos = videoGridAdapter.getVideoList();
            for (VideoModel video : gridVideos) {
                if (!video.getId().equals(currentVideo.getId()) && !videoList.contains(video)) {
                    videoList.add(video);
                }
            }
        }

        // 添加Best For You中的视频
        if (bestForYouAdapter != null) {
            List<VideoModel> bestForYouVideos = bestForYouAdapter.getVideoList();
            for (VideoModel video : bestForYouVideos) {
                if (!video.getId().equals(currentVideo.getId()) && !videoList.contains(video)) {
                    videoList.add(video);
                }
            }
        }

        // 添加动态推荐位中的视频
        for (FeaturedSectionAdapter adapter : dynamicFeaturedAdapters) {
            if (adapter != null) {
                List<VideoModel> featuredVideos = adapter.getVideoList();
                for (VideoModel video : featuredVideos) {
                    if (!video.getId().equals(currentVideo.getId()) && !videoList.contains(video)) {
                        videoList.add(video);
                    }
                }
            }
        }

        // 限制列表大小，避免过多视频
        if (videoList.size() > 10) {
            // 创建新的ArrayList以确保序列化兼容性
            List<VideoModel> limitedList = new ArrayList<>(videoList.subList(0, 10));
            videoList.clear();
            videoList.addAll(limitedList);
        }
    }

    /**
     * 跳转到视频详情页面（用于Banner海报点击）
     * @param video 视频数据
     */
    private void navigateToVideoDetailPage(VideoModel video) {
        if (video == null || getContext() == null) {
            return;
        }

        Intent intent = new Intent(getContext(), VideoDetailActivity.class);

        // 优先使用filmLanguageInfoId调用新接口
        if (video.hasFilmLanguageInfoId()) {
            intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
            intent.putExtra("filmTitle", video.getTitle());
            Log.d(TAG, "Banner使用filmLanguageInfoId跳转详情页: " + video.getFilmLanguageInfoId());
        } else {
            // 回退到旧模式
            intent.putExtra("video_model", video);
            Log.d(TAG, "Banner回退到video_model模式跳转详情页: " + video.getTitle());
        }

        startActivity(intent);
    }

    /**
     * 跳转到视频预告详情页面（用于Coming Soon模块点击）
     * @param video 视频数据
     */
    private void navigateToVideoPreviewDetail(VideoModel video) {
        if (video == null || getContext() == null) {
            return;
        }

        Intent intent = new Intent(getContext(), VideoPreviewDetailActivity.class);
        intent.putExtra("video_model", video);
        startActivity(intent);
    }

    /**
     * 从Continue Watching跳转到视频详情页面
     * @param video 视频数据
     */
    private void navigateToVideoDetailPageForContinueWatching(VideoModel video) {
        if (video == null || getContext() == null) {
            return;
        }

        Intent intent = new Intent(getContext(), VideoDetailActivity.class);

        // 使用filmLanguageInfoId调用新接口
        intent.putExtra("filmLanguageInfoId", video.getFilmLanguageInfoId());
        intent.putExtra("filmTitle", video.getTitle());

        // 传递Continue Watching标识，让详情页面知道这是从Continue Watching跳转的
        intent.putExtra("fromContinueWatching", true);
        intent.putExtra("continueWatchingEpisode", video.getCurrentEpisode());

        Log.d(TAG, "Continue Watching跳转到详情页: " + video.getFilmLanguageInfoId() +
                   ", 当前剧集: " + video.getCurrentEpisode());

        startActivity(intent);
    }

    /**
     * 从Continue Watching直接跳转到视频播放页面
     * @param video 视频数据
     */
    private void navigateToVideoPlayerForContinueWatching(VideoModel video) {
        if (video == null || getContext() == null) {
            return;
        }

        try {
            // 创建视频列表，将当前视频作为第一个
            List<VideoModel> videoList = new ArrayList<>();
            videoList.add(video);

            // 添加其他相关视频到列表中
            addRelatedVideosToList(videoList, video);

            // 验证视频列表
            if (videoList.isEmpty()) {
                Log.e(TAG, "Video list is empty after adding related videos");
                showToast("无法加载视频列表");
                return;
            }

            // 启动VideoPlayerActivity，传递Continue Watching标识
            VideoPlayerActivity.start(getContext(), videoList, 0);
            Log.d(TAG, "Continue Watching直接跳转到播放器: " + video.getDisplayTitle() +
                       ", 当前剧集: " + video.getCurrentEpisode());

        } catch (Exception e) {
            Log.e(TAG, "Error navigating to video player for continue watching", e);
            showToast("跳转失败，请重试");
        }
    }

    /**
     * 验证视频列表数据的完整性
     */
    private void validateVideoListData(List<VideoModel> videoList, String listName) {
        if (videoList == null || videoList.isEmpty()) {
            android.util.Log.w("HomeFragment", listName + "数据为空");
            return;
        }

        int validCount = 0;
        int totalCount = videoList.size();

        for (VideoModel video : videoList) {
            TestValidationUtils.TestResult result = TestValidationUtils.validateVideoModelData(video);
            if (result.success) {
                validCount++;
            } else {
                android.util.Log.w("HomeFragment", listName + "数据验证失败 - " +
                    video.getTitle() + ": " + result.message);
            }
        }

        double validRate = (validCount * 100.0) / totalCount;
        android.util.Log.i("HomeFragment", listName + "数据验证完成 - 总数: " + totalCount +
            ", 有效: " + validCount + ", 有效率: " + String.format("%.1f%%", validRate));
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "HomeFragment onResume called");

        // 优化的数据刷新策略：首次加载强制刷新，后续返回使用缓存
        if (fragmentCacheManager.isFirstLoad(fragmentKey)) {
            Log.d(TAG, "首次Resume，强制刷新数据");
            loadRealData(); // 首次进入直接加载，不使用缓存
        } else {
            Log.d(TAG, "从其他页面返回，使用缓存数据");
            // 确保UI显示缓存的数据，不进行网络请求
            boolean cacheRestored = restoreDataFromCache();
            if (cacheRestored) {
                Log.d(TAG, "成功从缓存恢复数据，UI已更新");
                // 确保所有UI组件正确显示
                ensureAllUIComponentsDisplay();
            } else {
                // 缓存恢复失败，但不是首次加载，可能需要重新加载
                Log.w(TAG, "缓存恢复失败，但不是首次加载，检查是否需要重新加载");
                if (fragmentCacheManager.shouldReloadData(fragmentKey)) {
                    Log.d(TAG, "缓存过期或无效，重新加载数据");
                    loadRealDataWithCache();
                } else {
                    // 即使缓存恢复失败，也要确保UI组件正确显示
                    ensureAllUIComponentsDisplay();
                }
            }
        }
    }

    /**
     * 检查是否需要刷新数据
     */
    private boolean shouldRefreshData() {
        // 使用FragmentCacheManager检查是否需要重新加载
        if (fragmentCacheManager.shouldReloadData(fragmentKey)) {
            return true;
        }

        // 如果轮播图数据为空，需要刷新
        if (carouselAdapter == null || carouselAdapter.getItemCount() == 0) {
            return true;
        }

        // 如果分类标签数据为空，需要刷新
        if (categoryTagAdapter == null || categoryTagAdapter.getItemCount() == 0) {
            return true;
        }

        // 其他情况下使用缓存数据
        return false;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // 清理资源
        carouselAdapter = null;
        categoryTagAdapter = null;
        videoGridAdapter = null;
        continueWatchingAdapter = null;
        comingSoonAdapter = null;
        bestForYouAdapter = null;
        // 清理动态推荐位适配器
        if (dynamicFeaturedAdapters != null) {
            dynamicFeaturedAdapters.clear();
            dynamicFeaturedAdapters = null;
        }
        todaysHotAdapter = null;
        popularSeriesAdapter = null;
    }

    /**
     * 从缓存恢复数据（使用改进的缓存管理器）
     */
    private boolean restoreDataFromCache() {
        if (getContext() == null) {
            return false;
        }

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());

            Log.d(TAG, "开始从改进缓存恢复首页数据");

            // 恢复轮播图数据
            List<VideoModel> cachedCarousel = cacheManager.getCachedDataAtomic("home_carousel_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){}.getType());
            if (cachedCarousel != null && !cachedCarousel.isEmpty() && carouselAdapter != null) {
                carouselAdapter.updateCarouselList(cachedCarousel);

                // 重要：恢复轮播图大图显示
                VideoModel firstVideo = cachedCarousel.get(0);
                updateBackgroundPoster(firstVideo);

                Log.d(TAG, "恢复轮播图缓存数据: " + cachedCarousel.size() + " 项，已更新大图");
            } else {
                Log.d(TAG, "轮播图缓存数据为空或适配器为null");
            }

            // 恢复分类标签数据
            List<TagModel> cachedTags = cacheManager.getCachedDataAtomic("home_category_tags",
                new com.google.gson.reflect.TypeToken<List<TagModel>>(){}.getType());
            if (cachedTags != null && !cachedTags.isEmpty() && categoryTagAdapter != null) {
                categoryTagAdapter.updateTagList(cachedTags);
                Log.d(TAG, "恢复分类标签缓存数据: " + cachedTags.size() + " 项");
            } else {
                Log.d(TAG, "分类标签缓存数据为空或适配器为null");
            }

            // 恢复Featured推荐位数据（包含视频网格数据）
            boolean hasFeaturedData = restoreFeaturedCacheData(cacheManager);
            if (hasFeaturedData) {
                Log.d(TAG, "Featured推荐位数据恢复成功，视频网格已更新");
            } else {
                Log.d(TAG, "Featured推荐位数据恢复失败或无数据");
            }

            // 恢复继续观看数据
            List<MyHistoryItemModel> cachedHistory = cacheManager.getCachedDataAtomic("home_continue_watching",
                new com.google.gson.reflect.TypeToken<List<MyHistoryItemModel>>(){}.getType());
            if (cachedHistory != null && continueWatchingAdapter != null) {
                if (!cachedHistory.isEmpty()) {
                    continueWatchingAdapter.updateHistoryList(cachedHistory);
                    showContinueWatchingSection();
                    Log.d(TAG, "恢复继续观看缓存数据: " + cachedHistory.size() + " 项");
                } else {
                    // 缓存中是空数据，显示空状态
                    showContinueWatchingEmptyState();
                    Log.d(TAG, "恢复继续观看缓存数据: 空状态");
                }
            } else if (cachedHistory == null) {
                // 没有缓存数据，首次进入时显示空状态，等待API加载
                showContinueWatchingEmptyState();
                Log.d(TAG, "没有继续观看缓存数据，显示空状态等待API加载");
            }

            // 恢复Today's Hot数据（包括空数据）
            SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(getContext());
            List<VideoModel> cachedTodaysHot = smartCacheManager.getCachedDataSmart("home_todays_hot_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){});
            if (cachedTodaysHot != null && todaysHotAdapter != null) {
                todaysHotAdapter.updateVideoList(cachedTodaysHot);
                Log.d(TAG, "恢复Today's Hot缓存数据: " + cachedTodaysHot.size() + " 项");
            } else {
                Log.d(TAG, "Today's Hot缓存数据为空或适配器为null");
            }

            // 恢复Coming Soon数据（包括空数据）
            List<VideoModel> cachedComingSoon = smartCacheManager.getCachedDataSmart("home_coming_soon_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){});
            if (cachedComingSoon != null && comingSoonAdapter != null) {
                comingSoonAdapter.updateVideoList(cachedComingSoon);
                Log.d(TAG, "恢复Coming Soon缓存数据: " + cachedComingSoon.size() + " 项");
            } else {
                Log.d(TAG, "Coming Soon缓存数据为空或适配器为null");
            }

            // 恢复Popular Series数据（包括空数据）
            List<VideoModel> cachedPopularSeries = smartCacheManager.getCachedDataSmart("home_popular_series_data",
                new com.google.gson.reflect.TypeToken<List<VideoModel>>(){});
            if (cachedPopularSeries != null && popularSeriesAdapter != null) {
                popularSeriesAdapter.updateVideoList(cachedPopularSeries);
                Log.d(TAG, "恢复Popular Series缓存数据: " + cachedPopularSeries.size() + " 项");
            } else {
                Log.d(TAG, "Popular Series缓存数据为空或适配器为null");
            }

            // 检查是否有足够的缓存数据（包括所有区域的数据）
            boolean hasCarouselCache = (cachedCarousel != null && !cachedCarousel.isEmpty());
            boolean hasTagsCache = (cachedTags != null && !cachedTags.isEmpty());
            boolean hasContinueWatchingCache = (cachedHistory != null); // 包括空数据
            boolean hasTodaysHotCache = (cachedTodaysHot != null);
            boolean hasComingSoonCache = (cachedComingSoon != null);
            boolean hasPopularSeriesCache = (cachedPopularSeries != null);

            boolean hasValidCache = hasCarouselCache || hasTagsCache || hasFeaturedData ||
                                  hasContinueWatchingCache || hasTodaysHotCache ||
                                  hasComingSoonCache || hasPopularSeriesCache;

            Log.d(TAG, "缓存数据检查结果:");
            Log.d(TAG, "  轮播图: " + hasCarouselCache);
            Log.d(TAG, "  分类标签: " + hasTagsCache);
            Log.d(TAG, "  Featured: " + hasFeaturedData);
            Log.d(TAG, "  Continue Watching: " + hasContinueWatchingCache);
            Log.d(TAG, "  Today's Hot: " + hasTodaysHotCache);
            Log.d(TAG, "  Coming Soon: " + hasComingSoonCache);
            Log.d(TAG, "  Popular Series: " + hasPopularSeriesCache);

            if (hasValidCache) {
                fragmentCacheManager.updateCacheState(fragmentKey, true);
                Log.d(TAG, "成功从改进缓存恢复数据");
                return true;
            } else {
                Log.d(TAG, "缓存中没有有效数据");
            }

        } catch (Exception e) {
            Log.e(TAG, "从改进缓存恢复数据失败", e);
        }

        return false;
    }

    /**
     * 保存数据到缓存（使用改进的缓存管理器）
     */
    private void saveDataToCache() {
        if (getContext() == null) {
            return;
        }

        try {
            ImprovedDataCacheManager cacheManager = ImprovedDataCacheManager.getInstance(getContext());

            Log.d(TAG, "开始保存首页数据到改进缓存");

            boolean allSaved = true;

            // 保存轮播图数据
            if (carouselAdapter != null && carouselAdapter.getItemCount() > 0) {
                List<VideoModel> carouselData = carouselAdapter.getCarouselList();
                if (carouselData != null && !carouselData.isEmpty()) {
                    boolean saved = cacheManager.cacheDataAtomic("home_carousel_data", carouselData);
                    if (saved) {
                        Log.d(TAG, "保存轮播图数据到缓存成功: " + carouselData.size() + " 项");
                    } else {
                        Log.e(TAG, "保存轮播图数据到缓存失败");
                        allSaved = false;
                    }
                }
            }

            // 保存分类标签数据
            if (categoryTagAdapter != null && categoryTagAdapter.getItemCount() > 0) {
                List<TagModel> tagData = categoryTagAdapter.getTagList();
                if (tagData != null && !tagData.isEmpty()) {
                    boolean saved = cacheManager.cacheDataAtomic("home_category_tags", tagData);
                    if (saved) {
                        Log.d(TAG, "保存分类标签数据到缓存成功: " + tagData.size() + " 项");
                    } else {
                        Log.e(TAG, "保存分类标签数据到缓存失败");
                        allSaved = false;
                    }
                }
            }

            // 注意：视频网格数据现在通过Featured精细化缓存管理，不再在此处保存

            // 保存继续观看数据
            if (continueWatchingAdapter != null && continueWatchingAdapter.getItemCount() > 0) {
                List<MyHistoryItemModel> historyData = continueWatchingAdapter.getHistoryList();
                if (historyData != null && !historyData.isEmpty()) {
                    boolean saved = cacheManager.cacheDataAtomic("home_continue_watching", historyData);
                    if (saved) {
                        Log.d(TAG, "保存继续观看数据到缓存成功: " + historyData.size() + " 项");
                    } else {
                        Log.e(TAG, "保存继续观看数据到缓存失败");
                        allSaved = false;
                    }
                }
            }

            // 更新Fragment缓存管理器状态
            if (allSaved) {
                fragmentCacheManager.updateCacheState(fragmentKey, true);
                Log.d(TAG, "所有首页数据保存到改进缓存完成");
            } else {
                Log.w(TAG, "部分首页数据保存失败，不更新缓存状态");
            }

        } catch (Exception e) {
            Log.e(TAG, "保存数据到改进缓存失败", e);
        }
    }

    /**
     * 缓存原始Featured响应数据
     *
     * @param featuredList Featured推荐位列表
     */
    private void cacheFeaturedRawData(List<FeaturedModel> featuredList) {
        if (getContext() == null || featuredList == null) {
            Log.w(TAG, "无法缓存Featured原始数据：Context或数据为空");
            return;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            boolean saved = cacheManager.cacheDataSmart("home_featured_raw_response", featuredList);
            if (saved) {
                Log.d(TAG, "智能缓存Featured原始响应成功: " + featuredList.size() + "个推荐位");
            } else {
                Log.e(TAG, "智能缓存Featured原始响应失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "智能缓存Featured原始响应时发生异常", e);
        }
    }

    /**
     * 恢复Featured推荐位缓存数据
     *
     * @param cacheManager 缓存管理器实例
     * @return 如果成功恢复任何Featured数据则返回true
     */
    private boolean restoreFeaturedCacheData(ImprovedDataCacheManager cacheManager) {
        boolean hasData = false;

        try {
            Log.d(TAG, "开始恢复Featured推荐位缓存数据");

            SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(getContext());

            // 恢复Categories推荐位数据（主推荐区域）
            List<FeaturedFilmModel> categoriesFilms = smartCacheManager.getCachedDataSmart(
                "home_featured_categories_films",
                new com.google.gson.reflect.TypeToken<List<FeaturedFilmModel>>(){});

            if (categoriesFilms != null && !categoriesFilms.isEmpty()) {
                updateVideoGridWithFeaturedData(categoriesFilms);
                Log.d(TAG, "智能恢复Categories推荐位缓存数据: " + categoriesFilms.size() + "个影片");
                hasData = true;
            }

            // 恢复原始Featured响应数据并创建动态推荐位
            List<FeaturedModel> rawFeatured = smartCacheManager.getCachedDataSmart(
                "home_featured_raw_response",
                new com.google.gson.reflect.TypeToken<List<FeaturedModel>>(){});

            if (rawFeatured != null && !rawFeatured.isEmpty()) {
                createDynamicFeaturedSections(rawFeatured);
                Log.d(TAG, "智能恢复Featured原始响应数据并创建动态推荐位: " + rawFeatured.size() + "个推荐位");
                hasData = true;
            }

            // 如果没有Categories数据但有原始数据，尝试从原始数据中恢复主推荐区域
            if (categoriesFilms == null || categoriesFilms.isEmpty()) {
                if (rawFeatured != null && !rawFeatured.isEmpty()) {
                    for (FeaturedModel featured : rawFeatured) {
                        if (featured != null && featured.isCategoriesFeatured() && featured.hasFilms()) {
                            updateVideoGridWithFeaturedData(featured.getFilms());
                            Log.d(TAG, "从原始数据恢复Categories推荐位: " + featured.getFilms().size() + "个影片");
                            hasData = true;
                            break;
                        }
                    }
                }
            }

            if (hasData) {
                Log.d(TAG, "Featured推荐位缓存数据恢复成功");
            } else {
                Log.d(TAG, "没有找到有效的Featured推荐位缓存数据");
            }

        } catch (Exception e) {
            Log.e(TAG, "恢复Featured推荐位缓存数据时发生异常", e);
        }

        return hasData;
    }

    /**
     * 缓存特定推荐位的影片数据
     *
     * @param cacheKey 缓存键
     * @param films 影片列表
     */
    private void cacheFeaturedFilms(String cacheKey, List<FeaturedFilmModel> films) {
        if (getContext() == null || cacheKey == null || films == null) {
            Log.w(TAG, "无法缓存Featured影片数据：参数为空");
            return;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            boolean saved = cacheManager.cacheDataSmart(cacheKey, films);
            if (saved) {
                Log.d(TAG, "智能缓存Featured影片数据成功 [" + cacheKey + "]: " + films.size() + "个影片");
            } else {
                Log.e(TAG, "智能缓存Featured影片数据失败 [" + cacheKey + "]");
            }
        } catch (Exception e) {
            Log.e(TAG, "智能缓存Featured影片数据时发生异常 [" + cacheKey + "]", e);
        }
    }

    /**
     * 显示Toast消息
     */
    private void showToast(String message) {
        if (getContext() != null) {
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 缓存Today's Hot数据（包括空数据）
     */
    private void cacheTodaysHotData(List<VideoModel> todaysHotList) {
        if (getContext() == null) {
            Log.w(TAG, "无法缓存Today's Hot数据：Context为空");
            return;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            boolean saved = cacheManager.cacheDataSmart("home_todays_hot_data", todaysHotList);
            if (saved) {
                Log.d(TAG, "智能缓存Today's Hot数据成功: " + (todaysHotList != null ? todaysHotList.size() : 0) + "个视频");
            } else {
                Log.e(TAG, "智能缓存Today's Hot数据失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "智能缓存Today's Hot数据时发生异常", e);
        }
    }

    /**
     * 缓存Coming Soon数据（包括空数据）
     */
    private void cacheComingSoonData(List<VideoModel> comingSoonList) {
        if (getContext() == null) {
            Log.w(TAG, "无法缓存Coming Soon数据：Context为空");
            return;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            boolean saved = cacheManager.cacheDataSmart("home_coming_soon_data", comingSoonList);
            if (saved) {
                Log.d(TAG, "智能缓存Coming Soon数据成功: " + (comingSoonList != null ? comingSoonList.size() : 0) + "个视频");
            } else {
                Log.e(TAG, "智能缓存Coming Soon数据失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "智能缓存Coming Soon数据时发生异常", e);
        }
    }

    /**
     * 缓存Popular Series数据（包括空数据）
     */
    private void cachePopularSeriesData(List<VideoModel> popularSeriesList) {
        if (getContext() == null) {
            Log.w(TAG, "无法缓存Popular Series数据：Context为空");
            return;
        }

        try {
            SmartCacheManager cacheManager = SmartCacheManager.getInstance(getContext());
            boolean saved = cacheManager.cacheDataSmart("home_popular_series_data", popularSeriesList);
            if (saved) {
                Log.d(TAG, "智能缓存Popular Series数据成功: " + (popularSeriesList != null ? popularSeriesList.size() : 0) + "个视频");
            } else {
                Log.e(TAG, "智能缓存Popular Series数据失败");
            }
        } catch (Exception e) {
            Log.e(TAG, "智能缓存Popular Series数据时发生异常", e);
        }
    }
}
