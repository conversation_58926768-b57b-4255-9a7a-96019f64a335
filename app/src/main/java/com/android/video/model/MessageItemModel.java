package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 单个消息项数据模型
 * <p>
 * 用于映射API返回的单个消息项JSON数据结构。
 * 包含消息的所有基本信息，如消息ID、类型、内容、已读状态等。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class MessageItemModel {
    
    /**
     * 消息推送ID
     */
    @SerializedName("sendMessageId")
    private String sendMessageId;
    
    /**
     * 消息类型
     * 1=系统消息, 2=订阅消息
     */
    @SerializedName("messageType")
    private int messageType;
    
    /**
     * 用户ID
     */
    @SerializedName("customerId")
    private String customerId;
    
    /**
     * 消息ID
     */
    @SerializedName("messageId")
    private String messageId;
    
    /**
     * 推送时间
     */
    @SerializedName("createTime")
    private String createTime;
    
    /**
     * 是否已读
     * 0=未读, 1=已读
     */
    @SerializedName("isRead")
    private int isRead;
    
    /**
     * 已读时间
     */
    @SerializedName("readTime")
    private String readTime;
    
    /**
     * 是否删除
     */
    @SerializedName("isDelete")
    private int isDelete;
    
    /**
     * 短剧标题
     */
    @SerializedName("filmTitle")
    private String filmTitle;
    
    /**
     * 封面图片URL
     */
    @SerializedName("cover")
    private String cover;
    
    /**
     * 语言类型
     * 1=英语, 2=俄语
     */
    @SerializedName("languageType")
    private Integer languageType;
    
    /**
     * 短剧语言信息ID
     */
    @SerializedName("filmLanguageInfoId")
    private String filmLanguageInfoId;
    
    /**
     * 总章节数EP
     */
    @SerializedName("totalChaptersNum")
    private Integer totalChaptersNum;
    
    /**
     * 消息标题
     */
    @SerializedName("messageTitle")
    private String messageTitle;

    /**
     * 默认构造函数
     */
    public MessageItemModel() {
    }

    /**
     * 完整构造函数
     */
    public MessageItemModel(String sendMessageId, int messageType, String customerId, 
                           String messageId, String createTime, int isRead, String readTime,
                           int isDelete, String filmTitle, String cover, Integer languageType,
                           String filmLanguageInfoId, Integer totalChaptersNum, String messageTitle) {
        this.sendMessageId = sendMessageId;
        this.messageType = messageType;
        this.customerId = customerId;
        this.messageId = messageId;
        this.createTime = createTime;
        this.isRead = isRead;
        this.readTime = readTime;
        this.isDelete = isDelete;
        this.filmTitle = filmTitle;
        this.cover = cover;
        this.languageType = languageType;
        this.filmLanguageInfoId = filmLanguageInfoId;
        this.totalChaptersNum = totalChaptersNum;
        this.messageTitle = messageTitle;
    }

    // ========== Getter和Setter方法 ==========

    public String getSendMessageId() {
        return sendMessageId;
    }

    public void setSendMessageId(String sendMessageId) {
        this.sendMessageId = sendMessageId;
    }

    public int getMessageType() {
        return messageType;
    }

    public void setMessageType(int messageType) {
        this.messageType = messageType;
    }

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
    }

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public int getIsRead() {
        return isRead;
    }

    public void setIsRead(int isRead) {
        this.isRead = isRead;
    }

    public String getReadTime() {
        return readTime;
    }

    public void setReadTime(String readTime) {
        this.readTime = readTime;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getFilmTitle() {
        return filmTitle;
    }

    public void setFilmTitle(String filmTitle) {
        this.filmTitle = filmTitle;
    }

    public String getCover() {
        return cover;
    }

    public void setCover(String cover) {
        this.cover = cover;
    }

    public Integer getLanguageType() {
        return languageType;
    }

    public void setLanguageType(Integer languageType) {
        this.languageType = languageType;
    }

    public String getFilmLanguageInfoId() {
        return filmLanguageInfoId;
    }

    public void setFilmLanguageInfoId(String filmLanguageInfoId) {
        this.filmLanguageInfoId = filmLanguageInfoId;
    }

    public Integer getTotalChaptersNum() {
        return totalChaptersNum;
    }

    public void setTotalChaptersNum(Integer totalChaptersNum) {
        this.totalChaptersNum = totalChaptersNum;
    }

    public String getMessageTitle() {
        return messageTitle;
    }

    public void setMessageTitle(String messageTitle) {
        this.messageTitle = messageTitle;
    }

    /**
     * 检查是否为已读状态
     * 
     * @return 如果已读返回true，否则返回false
     */
    public boolean isRead() {
        return isRead == 1;
    }

    /**
     * 检查是否为系统消息
     * 
     * @return 如果是系统消息返回true，否则返回false
     */
    public boolean isSystemMessage() {
        return messageType == 1;
    }

    /**
     * 检查是否为订阅消息
     * 
     * @return 如果是订阅消息返回true，否则返回false
     */
    public boolean isSubscriptionMessage() {
        return messageType == 2;
    }

    /**
     * 检查是否有视频信息
     * 
     * @return 如果有视频信息返回true，否则返回false
     */
    public boolean hasVideoInfo() {
        return filmTitle != null && !filmTitle.trim().isEmpty();
    }

    @Override
    public String toString() {
        return "MessageItemModel{" +
                "sendMessageId='" + sendMessageId + '\'' +
                ", messageType=" + messageType +
                ", customerId='" + customerId + '\'' +
                ", messageId='" + messageId + '\'' +
                ", createTime='" + createTime + '\'' +
                ", isRead=" + isRead +
                ", readTime='" + readTime + '\'' +
                ", isDelete=" + isDelete +
                ", filmTitle='" + filmTitle + '\'' +
                ", cover='" + cover + '\'' +
                ", languageType=" + languageType +
                ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                ", totalChaptersNum=" + totalChaptersNum +
                ", messageTitle='" + messageTitle + '\'' +
                '}';
    }
}
