package com.android.video.ui.transformer;

import android.view.View;
import androidx.annotation.NonNull;
import androidx.viewpager2.widget.ViewPager2;
import com.android.video.adapter.CarouselAdapter;

/**
 * 轮播图页面变换器
 * 实现选中项放大、未选中项缩小的效果
 * <AUTHOR>
 */
public class CarouselPageTransformer implements ViewPager2.PageTransformer {

    private static final float MIN_SCALE = 0.8f; // 未选中项的最小缩放比例
    private static final float MAX_SCALE = 1.0f; // 选中项的最大缩放比例

    @Override
    public void transformPage(@NonNull View page, float position) {
        // position参数说明：
        // -1: 完全在左侧
        // 0: 完全在屏幕中央
        // 1: 完全在右侧

        // 由于我们设置了padding，侧边的图片会部分可见
        // 我们只需要调整透明度和更新选中状态

        if (position < -1 || position > 1) {
            // 完全不可见的页面
            page.setAlpha(0.5f);
        } else {
            // 计算透明度，中间的图片完全不透明，侧边的图片稍微透明
            float alpha = Math.max(0.6f, 1.0f - Math.abs(position) * 0.4f);
            page.setAlpha(alpha);
        }

        // 更新ViewHolder的选中状态
        updateViewHolderState(page, position);
    }

    /**
     * 更新ViewHolder的选中状态
     * @param page 页面视图
     * @param position 位置
     */
    private void updateViewHolderState(View page, float position) {
        // 判断是否为选中状态（position接近0表示在中央）
        // 由于我们要同时显示3张图片，选中状态的判断更严格
        boolean isSelected = Math.abs(position) < 0.2f;

        // 通过tag获取ViewHolder并更新状态
        Object tag = page.getTag();
        if (tag instanceof CarouselAdapter.CarouselViewHolder) {
            CarouselAdapter.CarouselViewHolder holder = (CarouselAdapter.CarouselViewHolder) tag;
            holder.setSelected(isSelected);
        }
    }
}
