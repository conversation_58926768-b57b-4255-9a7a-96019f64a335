package com.android.video.ui.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import java.util.List;

/**
 * 最近搜索标签适配器
 */
public class RecentSearchAdapter extends RecyclerView.Adapter<RecentSearchAdapter.RecentSearchViewHolder> {
    
    private List<String> recentSearches;
    private OnRecentSearchClickListener clickListener;
    
    public interface OnRecentSearchClickListener {
        void onRecentSearchClick(String searchTerm);
    }
    
    public RecentSearchAdapter(List<String> recentSearches, OnRecentSearchClickListener clickListener) {
        this.recentSearches = recentSearches;
        this.clickListener = clickListener;
    }
    
    @NonNull
    @Override
    public RecentSearchViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_recent_search_tag, parent, false);
        return new RecentSearchViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull RecentSearchViewHolder holder, int position) {
        String searchTerm = recentSearches.get(position);
        holder.bind(searchTerm);
    }
    
    @Override
    public int getItemCount() {
        return recentSearches.size();
    }
    
    class RecentSearchViewHolder extends RecyclerView.ViewHolder {
        private TextView tvSearchTerm;
        
        public RecentSearchViewHolder(@NonNull View itemView) {
            super(itemView);
            tvSearchTerm = itemView.findViewById(R.id.tv_search_term);
            
            itemView.setOnClickListener(v -> {
                if (clickListener != null) {
                    int position = getAdapterPosition();
                    if (position != RecyclerView.NO_POSITION) {
                        clickListener.onRecentSearchClick(recentSearches.get(position));
                    }
                }
            });
        }
        
        public void bind(String searchTerm) {
            tvSearchTerm.setText(searchTerm);
        }
    }
}
