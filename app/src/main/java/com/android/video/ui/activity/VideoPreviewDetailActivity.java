package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;
import androidx.core.view.WindowInsetsCompat;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.databinding.ActivityVideoPreviewDetailBinding;
import com.android.video.model.VideoModel;
import com.android.video.model.ActorModel;
import com.android.video.model.DirectorModel;
import com.android.video.model.TagModel;
import com.android.video.adapter.SynopsisTagAdapter;
import com.android.video.adapter.ActorAdapter;
import com.android.video.adapter.DirectorAdapter;
import com.android.video.adapter.RecommendVideoAdapter;
import com.android.video.decoration.GridSpacingItemDecoration;
import com.android.video.utils.TestValidationUtils;
import com.android.video.utils.NavigationBarUtils;
import android.graphics.Rect;
import android.view.ViewGroup;
import android.widget.Button;
import java.util.ArrayList;
import java.util.List;

/**
 * 视频预告详情页Activity - 显示视频预告的详细信息
 * 包含视频海报、基本信息、演员、导演、推荐视频等功能
 * 与视频详情页的区别：
 * 1. 观看热度改为预告上线时间
 * 2. 收藏按钮改为订阅提醒按钮
 * 3. 没有Episodes模块
 * <AUTHOR>
 */
public class VideoPreviewDetailActivity extends BaseFullScreenActivity {

    private ActivityVideoPreviewDetailBinding binding;
    private VideoModel videoModel;
    private Button continuePlayingButton;
    private boolean isNotifySubscribed = false; // 订阅提醒状态
    
    // 适配器
    private SynopsisTagAdapter synopsisTagAdapter;
    private ActorAdapter actorAdapter;
    private DirectorAdapter directorAdapter;
    private RecommendVideoAdapter recommendVideoAdapter;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        
        binding = ActivityVideoPreviewDetailBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        
        // 隐藏ActionBar以获得全屏体验
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }
        
        // 接收传递的视频数据
        receiveVideoData();
        
        // 初始化UI组件
        initViews();
        
        // 设置适配器
        setupAdapters();
        
        // 设置点击事件
        setupClickListeners();
        
        // 加载视频数据
        loadVideoData();

        // 验证视频数据
        validateVideoData();
    }

    /**
     * 接收Intent传递的视频数据
     */
    private void receiveVideoData() {
        Intent intent = getIntent();
        if (intent != null && intent.hasExtra("video_model")) {
            videoModel = (VideoModel) intent.getSerializableExtra("video_model");
        }
        
        // 如果没有传递数据，创建测试数据
        if (videoModel == null) {
            videoModel = createTestVideoData();
        }
    }

    /**
     * 初始化UI组件
     */
    private void initViews() {
        // 设置状态栏适配
        setupStatusBarAdaptation();

        // 初始化RecyclerView
        setupRecyclerViews();

        // 初始化Continue Playing按钮
        initContinuePlayingButton();
    }

    /**
     * 设置状态栏适配
     */
    private void setupStatusBarAdaptation() {
        // 状态栏适配将在布局文件中通过Guideline处理
        // 这里可以添加额外的状态栏处理逻辑
    }

    /**
     * 初始化Continue Playing按钮
     */
    private void initContinuePlayingButton() {
        continuePlayingButton = binding.buttonContinuePlaying;
        // 按钮的导航栏适配将在onApplyNavigationBarInsets中处理
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerViews() {
        // Synopsis标签RecyclerView
        binding.recyclerViewTags.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        
        // 演员RecyclerView
        binding.recyclerViewActors.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        
        // 导演RecyclerView
        binding.recyclerViewDirectors.setLayoutManager(
            new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        
        // 推荐视频RecyclerView (2x2网格)
        binding.recyclerViewRecommendVideos.setLayoutManager(
            new GridLayoutManager(this, 2));

        // 添加网格间距装饰器 - 12dp间距
        int spacingInPx = (int) (12 * getResources().getDisplayMetrics().density);
        binding.recyclerViewRecommendVideos.addItemDecoration(
            new GridSpacingItemDecoration(2, spacingInPx, false));
    }

    /**
     * 设置适配器
     */
    private void setupAdapters() {
        // Synopsis标签适配器
        synopsisTagAdapter = new SynopsisTagAdapter(new ArrayList<>());
        binding.recyclerViewTags.setAdapter(synopsisTagAdapter);

        // 演员适配器
        actorAdapter = new ActorAdapter(this, new ArrayList<>());
        binding.recyclerViewActors.setAdapter(actorAdapter);

        // 导演适配器
        directorAdapter = new DirectorAdapter(this, new ArrayList<>());
        binding.recyclerViewDirectors.setAdapter(directorAdapter);

        // 推荐视频适配器
        recommendVideoAdapter = new RecommendVideoAdapter(new ArrayList<>());
        recommendVideoAdapter.setOnVideoClickListener(this::onRecommendVideoClick);
        binding.recyclerViewRecommendVideos.setAdapter(recommendVideoAdapter);
    }

    /**
     * 设置点击事件
     */
    private void setupClickListeners() {
        // 返回按钮
        binding.buttonBack.setOnClickListener(v -> finish());

        // 分享按钮
        binding.buttonShare.setOnClickListener(v -> shareVideo());

        // 订阅提醒按钮
        binding.buttonNotifyScrollable.setOnClickListener(v -> toggleNotifySubscription());

        // 概要展开/收起
        binding.textSynopsisContent.setOnClickListener(v -> toggleSynopsisExpanded());

        // Continue Playing按钮
        binding.buttonContinuePlaying.setOnClickListener(v -> onContinuePlayingClick());
    }

    /**
     * 加载视频数据
     */
    private void loadVideoData() {
        if (videoModel == null) return;
        
        // 设置基本信息
        loadBasicInfo();
        
        // 设置标签
        loadTags();
        
        // 设置演员信息
        loadActors();
        
        // 设置导演信息
        loadDirectors();
        
        // 设置推荐视频
        loadRecommendVideos();
    }

    /**
     * 设置基本信息
     */
    private void loadBasicInfo() {
        // 设置海报
        binding.imageVideoPreviewPosterScrollable.setImageResource(R.drawable.movie_poster);
        binding.imageSmallPosterScrollable.setImageResource(R.drawable.movie_poster);

        // 设置标题
        binding.textVideoTitleScrollable.setText(videoModel.getTitle());

        // 设置预告上线时间
        binding.textReleaseTimeScrollable.setText("2025-06-18");

        // 设置订阅提醒状态
        updateNotifyButton();

        // 设置概要内容
        binding.textSynopsisContent.setText(videoModel.getSynopsis());
        updateSynopsisDisplay();
    }

    /**
     * 加载标签
     */
    private void loadTags() {
        // 将字符串标签转换为TagModel列表
        List<TagModel> tagModels = new ArrayList<>();
        for (String tag : videoModel.getTags()) {
            tagModels.add(new TagModel(tag, "video_tag"));
        }
        synopsisTagAdapter.updateTagList(tagModels);
    }

    /**
     * 加载演员信息
     */
    private void loadActors() {
        actorAdapter.updateActors(videoModel.getActors());
    }

    /**
     * 加载导演信息
     */
    private void loadDirectors() {
        directorAdapter.updateDirectors(videoModel.getDirectors());
    }

    /**
     * 加载推荐视频
     */
    private void loadRecommendVideos() {
        // 这里应该从服务器获取推荐视频，暂时使用测试数据
        List<VideoModel> recommendVideos = createTestRecommendVideos();
        recommendVideoAdapter.updateVideoList(recommendVideos);
    }

    /**
     * 分享视频
     */
    private void shareVideo() {
        Intent shareIntent = new Intent(Intent.ACTION_SEND);
        shareIntent.setType("text/plain");
        shareIntent.putExtra(Intent.EXTRA_TEXT, "分享视频预告: " + videoModel.getTitle());
        startActivity(Intent.createChooser(shareIntent, "分享到"));
    }

    /**
     * 切换订阅提醒状态
     */
    private void toggleNotifySubscription() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
            return;
        }

        isNotifySubscribed = !isNotifySubscribed;
        updateNotifyButton();
    }

    /**
     * 更新订阅提醒按钮状态
     */
    private void updateNotifyButton() {
        if (isNotifySubscribed) {
            // 已订阅状态：显示movie_ic_notify2图标
            binding.imageNotifyIconScrollable.setImageResource(R.drawable.movie_ic_notify2);
            binding.textNotifyTextScrollable.setText("Notify me");
        } else {
            // 未订阅状态：显示movie_ic_notify1图标
            binding.imageNotifyIconScrollable.setImageResource(R.drawable.movie_ic_notify1);
            binding.textNotifyTextScrollable.setText("Notify me");
        }
    }

    /**
     * 切换概要展开状态
     */
    private void toggleSynopsisExpanded() {
        videoModel.toggleSynopsisExpanded();
        updateSynopsisDisplay();
    }

    /**
     * 更新概要显示
     */
    private void updateSynopsisDisplay() {
        if (videoModel.isSynopsisExpanded()) {
            binding.textSynopsisContent.setMaxLines(Integer.MAX_VALUE);
        } else {
            binding.textSynopsisContent.setMaxLines(3);
        }
    }

    /**
     * 推荐视频点击事件
     */
    private void onRecommendVideoClick(VideoModel video, int position) {
        // 跳转到新的视频预告详情页
        Intent intent = new Intent(this, VideoPreviewDetailActivity.class);
        intent.putExtra("video_model", video);
        startActivity(intent);
    }

    /**
     * 创建测试视频数据
     */
    private VideoModel createTestVideoData() {
        VideoModel video = new VideoModel();
        video.setId("test_preview_001");
        video.setTitle("测试预告视频标题");
        video.setSynopsis("这是一个测试预告视频的概要内容。这里包含了视频的详细描述，包括剧情简介、主要角色介绍等信息。用户可以点击展开查看完整内容。");
        video.setViewCount(1250000);
        video.setLiked(false);

        // 添加标签
        video.addTag("动作");
        video.addTag("冒险");
        video.addTag("科幻");

        // 添加演员
        video.addActor(new ActorModel("actor_001", "演员A", "", "主角", ""));
        video.addActor(new ActorModel("actor_002", "演员B", "", "配角", ""));
        video.addActor(new ActorModel("actor_003", "演员C", "", "配角", ""));

        // 添加导演
        video.addDirector(new DirectorModel("director_001", "导演A", "", "", "美国"));

        return video;
    }

    /**
     * 创建测试推荐视频数据
     */
    private List<VideoModel> createTestRecommendVideos() {
        List<VideoModel> videos = new ArrayList<>();
        for (int i = 1; i <= 4; i++) {
            VideoModel video = new VideoModel();
            video.setId("recommend_preview_" + i);
            video.setTitle("推荐预告视频 " + i);
            videos.add(video);
        }
        return videos;
    }

    @Override
    protected void onApplyNavigationBarInsets(WindowInsetsCompat insets, int navigationBarHeight,
                                            boolean hasNavigationBar, boolean isGestureNavigation) {
        super.onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

        // 根据导航栏情况调整底部内容
        if (hasNavigationBar && !isGestureNavigation) {
            // 按钮导航栏需要为底部内容添加内边距
            binding.nestedScrollView.setPadding(0, 0, 0, navigationBarHeight);
        } else if (hasNavigationBar && isGestureNavigation) {
            // 手势导航栏只需要小的内边距
            binding.nestedScrollView.setPadding(0, 0, 0, navigationBarHeight / 3);
        }

        // 处理Continue Playing按钮的导航栏适配
        adaptContinuePlayingButtonForNavigationBar(hasNavigationBar, isGestureNavigation, navigationBarHeight);
    }

    /**
     * 验证视频数据完整性
     */
    private void validateVideoData() {
        if (videoModel == null) {
            android.util.Log.e("VideoPreviewDetailActivity", "VideoModel为null，无法验证数据");
            return;
        }

        TestValidationUtils.TestResult result = TestValidationUtils.validateVideoModelData(videoModel);

        if (result.success) {
            android.util.Log.i("VideoPreviewDetailActivity", "视频预告数据验证通过 - " + videoModel.getTitle());
        } else {
            android.util.Log.w("VideoPreviewDetailActivity", "视频预告数据验证失败 - " + result.message);
            android.util.Log.w("VideoPreviewDetailActivity", "详情: " + result.details);
        }

        // 验证UI渲染状态
        validateUIRendering();
    }

    /**
     * 验证UI渲染状态
     */
    private void validateUIRendering() {
        // 延迟执行，确保UI已渲染
        binding.getRoot().post(() -> {
            StringBuilder renderingStatus = new StringBuilder();
            renderingStatus.append("UI渲染状态检查:\n");

            // 检查概要内容
            String synopsisText = binding.textSynopsisContent.getText().toString();
            renderingStatus.append("概要内容: ").append(synopsisText.isEmpty() ? "空" : "已渲染").append("\n");

            // 检查演员列表
            int actorCount = actorAdapter.getItemCount();
            renderingStatus.append("演员数量: ").append(actorCount).append("\n");

            // 检查导演列表
            int directorCount = directorAdapter.getItemCount();
            renderingStatus.append("导演数量: ").append(directorCount).append("\n");

            // 检查标签列表
            int tagCount = synopsisTagAdapter.getItemCount();
            renderingStatus.append("标签数量: ").append(tagCount).append("\n");

            android.util.Log.i("VideoPreviewDetailActivity", renderingStatus.toString());
        });
    }

    /**
     * Continue Playing按钮点击事件
     */
    private void onContinuePlayingClick() {
        if (videoModel == null) {
            android.util.Log.w("VideoPreviewDetailActivity", "VideoModel is null, cannot start playing");
            return;
        }

        // 跳转到VideoPlayerActivity进行播放
        navigateToVideoPlayer();

        android.util.Log.d("VideoPreviewDetailActivity", "Continue Playing clicked - navigating to VideoPlayerActivity");
    }

    /**
     * 跳转到视频播放页面
     */
    private void navigateToVideoPlayer() {
        // 创建视频列表，将当前视频作为第一个
        List<VideoModel> videoList = new ArrayList<>();
        videoList.add(videoModel);

        // 添加推荐视频到播放列表
        if (recommendVideoAdapter != null) {
            List<VideoModel> recommendVideos = recommendVideoAdapter.getVideoList();
            for (VideoModel video : recommendVideos) {
                if (!video.getId().equals(videoModel.getId())) {
                    videoList.add(video);
                }
            }
        }

        // 启动VideoPlayerActivity
        Intent intent = new Intent(this, VideoPlayerActivity.class);
        intent.putExtra(VideoPlayerActivity.EXTRA_VIDEO_LIST, (ArrayList<VideoModel>) videoList);
        intent.putExtra(VideoPlayerActivity.EXTRA_CURRENT_INDEX, 0);

        startActivity(intent);
    }

    /**
     * 适配Continue Playing按钮的导航栏位置
     */
    private void adaptContinuePlayingButtonForNavigationBar(boolean hasNavigationBar,
                                                          boolean isGestureNavigation,
                                                          int navigationBarHeight) {
        if (continuePlayingButton == null) return;

        // 获取导航栏信息
        NavigationBarUtils.NavigationBarInfo navInfo = NavigationBarUtils.getNavigationBarInfo(this);

        // 获取按钮的布局参数
        ViewGroup.LayoutParams layoutParams = continuePlayingButton.getLayoutParams();
        if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) layoutParams;

            // 获取基础margin（来自dimens.xml）
            int baseMargin = getResources().getDimensionPixelSize(R.dimen.continue_playing_button_margin_bottom);

            if (navInfo.exists) {
                if (!isGestureNavigation) {
                    // 按钮导航：基础margin + 完整导航栏高度
                    marginParams.bottomMargin = baseMargin + navInfo.height;
                    android.util.Log.d("VideoPreviewDetailActivity", "按钮导航适配 - bottomMargin: " + marginParams.bottomMargin + "px");
                } else {
                    // 手势导航：基础margin + 导航栏高度的一半，最小48px
                    int gestureNavMargin = Math.max(navInfo.height / 2, 48);
                    marginParams.bottomMargin = baseMargin + gestureNavMargin;
                    android.util.Log.d("VideoPreviewDetailActivity", "手势导航适配 - bottomMargin: " + marginParams.bottomMargin + "px");
                }
            } else {
                // 无导航栏：只使用基础margin
                marginParams.bottomMargin = baseMargin;
                android.util.Log.d("VideoPreviewDetailActivity", "无导航栏 - bottomMargin: " + marginParams.bottomMargin + "px");
            }

            continuePlayingButton.setLayoutParams(marginParams);
        }
    }
}
