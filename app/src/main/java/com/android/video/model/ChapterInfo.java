package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 章节信息数据模型
 * <p>
 * 用于映射API返回的章节信息JSON数据结构。
 * 包含章节的ID、集数、收费状态、积分值、播放进度等信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class ChapterInfo {
    
    /**
     * 章节ID
     */
    @SerializedName("chapterId")
    private String chapterId;
    
    /**
     * 剧集数
     */
    @SerializedName("chapterEp")
    private Integer chapterEp;
    
    /**
     * 是否收费
     * 0=免费, 1=付费
     */
    @SerializedName("isCharge")
    private Integer isCharge;
    
    /**
     * 当前剧集积分值
     */
    @SerializedName("points")
    private Integer points;
    
    /**
     * 播放进度
     * 单位：秒
     */
    @SerializedName("progress")
    private Integer progress;
    
    /**
     * 最后播放时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @SerializedName("lastPlayTime")
    private String lastPlayTime;
    
    /**
     * 是否播放完成
     * 0=未完成, 1=已完成
     */
    @SerializedName("isFinished")
    private Integer isFinished;
    
    /**
     * 是否解锁
     * 0=未解锁, 1=已解锁
     */
    @SerializedName("isUnlock")
    private Integer isUnlock;

    /**
     * 默认构造函数
     */
    public ChapterInfo() {
    }

    /**
     * 完整构造函数
     */
    public ChapterInfo(String chapterId, Integer chapterEp, Integer isCharge, Integer points,
                      Integer progress, String lastPlayTime, Integer isFinished, Integer isUnlock) {
        this.chapterId = chapterId;
        this.chapterEp = chapterEp;
        this.isCharge = isCharge;
        this.points = points;
        this.progress = progress;
        this.lastPlayTime = lastPlayTime;
        this.isFinished = isFinished;
        this.isUnlock = isUnlock;
    }

    // ========== Getter和Setter方法 ==========

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Integer getChapterEp() {
        return chapterEp;
    }

    public void setChapterEp(Integer chapterEp) {
        this.chapterEp = chapterEp;
    }

    public Integer getIsCharge() {
        return isCharge;
    }

    public void setIsCharge(Integer isCharge) {
        this.isCharge = isCharge;
    }

    public Integer getPoints() {
        return points;
    }

    public void setPoints(Integer points) {
        this.points = points;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(String lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    public Integer getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(Integer isFinished) {
        this.isFinished = isFinished;
    }

    public Integer getIsUnlock() {
        return isUnlock;
    }

    public void setIsUnlock(Integer isUnlock) {
        this.isUnlock = isUnlock;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否为付费章节
     * 
     * @return 如果是付费章节返回true，否则返回false
     */
    public boolean isCharged() {
        return isCharge != null && isCharge == 1;
    }

    /**
     * 检查是否播放完成
     * 
     * @return 如果播放完成返回true，否则返回false
     */
    public boolean isPlayFinished() {
        return isFinished != null && isFinished == 1;
    }

    /**
     * 检查是否已解锁
     * 
     * @return 如果已解锁返回true，否则返回false
     */
    public boolean isUnlocked() {
        return isUnlock != null && isUnlock == 1;
    }

    /**
     * 获取章节标题
     * 
     * @return 章节标题 (如: "第1集")
     */
    public String getChapterTitle() {
        return "第" + (chapterEp != null ? chapterEp : 0) + "集";
    }

    /**
     * 获取格式化的播放进度
     * 
     * @return 格式化的播放进度字符串 (如: "2:30")
     */
    public String getFormattedProgress() {
        if (progress == null || progress <= 0) return "0:00";
        
        int minutes = progress / 60;
        int seconds = progress % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * 获取章节状态文本
     * 
     * @return 章节状态文本
     */
    public String getStatusText() {
        if (!isUnlocked()) {
            return "未解锁";
        } else if (isPlayFinished()) {
            return "已完成";
        } else if (progress != null && progress > 0) {
            return "进行中";
        } else {
            return "未开始";
        }
    }

    @Override
    public String toString() {
        return "ChapterInfo{" +
                "chapterId='" + chapterId + '\'' +
                ", chapterEp=" + chapterEp +
                ", isCharge=" + isCharge +
                ", points=" + points +
                ", progress=" + progress +
                ", lastPlayTime='" + lastPlayTime + '\'' +
                ", isFinished=" + isFinished +
                ", isUnlock=" + isUnlock +
                '}';
    }
}
