package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import java.util.Map;
import java.util.HashMap;

/**
 * 翻译数据模型类
 * 用于管理多语言翻译数据，支持英语、俄语、Kaza语三种语言
 * 支持从CSV文件和JSON格式加载数据
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class TranslationDataModel {
    
    @SerializedName("english")
    private Map<String, String> english;
    
    @SerializedName("russian")
    private Map<String, String> russian;
    
    @SerializedName("kaza")
    private Map<String, String> kaza;
    
    /**
     * 默认构造函数
     */
    public TranslationDataModel() {
    }
    
    /**
     * 构造函数
     * 
     * @param english 英语翻译数据
     * @param russian 俄语翻译数据
     * @param kaza Kaza语翻译数据
     */
    public TranslationDataModel(Map<String, String> english, Map<String, String> russian, Map<String, String> kaza) {
        this.english = english;
        this.russian = russian;
        this.kaza = kaza;
    }
    
    /**
     * 获取英语翻译数据
     * 
     * @return 英语翻译Map
     */
    public Map<String, String> getEnglish() {
        return english;
    }
    
    /**
     * 设置英语翻译数据
     * 
     * @param english 英语翻译Map
     */
    public void setEnglish(Map<String, String> english) {
        this.english = english;
    }
    
    /**
     * 获取俄语翻译数据
     * 
     * @return 俄语翻译Map
     */
    public Map<String, String> getRussian() {
        return russian;
    }
    
    /**
     * 设置俄语翻译数据
     * 
     * @param russian 俄语翻译Map
     */
    public void setRussian(Map<String, String> russian) {
        this.russian = russian;
    }
    
    /**
     * 获取Kaza语翻译数据
     * 
     * @return Kaza语翻译Map
     */
    public Map<String, String> getKaza() {
        return kaza;
    }
    
    /**
     * 设置Kaza语翻译数据
     * 
     * @param kaza Kaza语翻译Map
     */
    public void setKaza(Map<String, String> kaza) {
        this.kaza = kaza;
    }
    
    /**
     * 根据语言类型获取对应的翻译数据
     * 
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     * @return 对应语言的翻译Map，如果类型无效则返回英语
     */
    public Map<String, String> getTranslationByLanguageType(int languageType) {
        switch (languageType) {
            case 1:
                return english;
            case 2:
                return russian;
            case 3:
                return kaza;
            default:
                return english; // 默认返回英语
        }
    }
    
    /**
     * 检查是否包含指定语言的翻译数据
     * 
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     * @return 如果包含对应语言数据则返回true
     */
    public boolean hasTranslationForLanguage(int languageType) {
        Map<String, String> translations = getTranslationByLanguageType(languageType);
        return translations != null && !translations.isEmpty();
    }
    
    /**
     * 获取指定语言和键的翻译文本
     * 
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     * @param key 翻译键
     * @return 翻译文本，如果不存在则返回键本身
     */
    public String getTranslation(int languageType, String key) {
        if (key == null || key.trim().isEmpty()) {
            return key;
        }
        
        Map<String, String> translations = getTranslationByLanguageType(languageType);
        if (translations != null && translations.containsKey(key)) {
            return translations.get(key);
        }
        
        // 如果当前语言没有翻译，尝试使用英语作为后备
        if (languageType != 1 && english != null && english.containsKey(key)) {
            return english.get(key);
        }
        
        // 如果都没有，返回键本身
        return key;
    }
    
    @Override
    public String toString() {
        return "TranslationDataModel{" +
                "english=" + (english != null ? english.size() + " entries" : "null") +
                ", russian=" + (russian != null ? russian.size() + " entries" : "null") +
                ", kaza=" + (kaza != null ? kaza.size() + " entries" : "null") +
                '}';
    }
}
