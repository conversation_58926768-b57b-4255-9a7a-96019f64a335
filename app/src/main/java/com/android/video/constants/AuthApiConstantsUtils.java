package com.android.video.constants;

/**
 * 认证模块API常量类 - 定义认证相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理用户认证相关的API常量，
 * 包括手机号登录、第三方登录（Facebook、TikTok）、验证码发送等功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的认证功能：
 * <ul>
 *   <li>手机验证码发送和验证</li>
 *   <li>手机号登录</li>
 *   <li>Facebook第三方登录</li>
 *   <li>TikTok第三方登录</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建发送验证码API URL
 * String url = AuthApiConstantsUtils.buildApiUrl(baseUrl, AuthApiConstantsUtils.API_SEND_SMS_CODE);
 * 
 * // 使用参数常量
 * requestParams.put(AuthApiConstantsUtils.PARAM_PHONE_NUMBER, phoneNumber);
 * requestParams.put(AuthApiConstantsUtils.PARAM_DEVICE_ID, deviceId);
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class AuthApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 认证相关API路径常量 ==========

    /**
     * 发送手机验证码API路径
     * <p>
     * 用于向用户手机号发送登录验证码的接口。
     * 请求方法：POST
     * </p>
     */
    public static final String API_SEND_SMS_CODE = "/app/customer/sendSmsCode";

    /**
     * 统一登录API路径
     * <p>
     * 用于统一登录接口，支持手机号、VKontakte、TikTok登录。
     * 请求方法：POST
     * </p>
     */
    public static final String API_LOGIN = "/app/customer/login";

    /**
     * @deprecated 使用 {@link #API_LOGIN} 替代
     * 手机号登录API路径（已弃用）
     * <p>
     * 用于通过手机号和验证码进行用户登录的接口。
     * 请求方法：POST
     * </p>
     */
    @Deprecated
    public static final String API_PHONE_LOGIN = "/app/customer/login/phone";

    /**
     * Facebook登录回调API路径
     * <p>
     * 用于处理Facebook第三方登录回调的接口。
     * 请求方法：POST
     * </p>
     */
    public static final String API_FACEBOOK_LOGIN = "/app/customer/login/facebook/callback";

    /**
     * TikTok登录回调API路径
     * <p>
     * 用于处理TikTok第三方登录回调的接口。
     * 请求方法：POST
     * </p>
     */
    public static final String API_TIKTOK_LOGIN = "/app/customer/login/tiktok/callback";

    /**
     * 初始化设备信息API路径
     * <p>
     * APP启动时调用的初始化接口，用于初始化设备信息。
     * 请求方法：POST
     * </p>
     */
    public static final String API_INIT_DEVICE = "/app/customer/init";

    /**
     * 获取用户信息API路径
     * <p>
     * 用于获取当前登录用户的详细信息，包括用户基本信息、VIP状态、积分等。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * </p>
     */
    public static final String API_GET_USER_INFO = "/app/customer/info";

    // ========== 认证相关请求参数常量 ==========

    /**
     * 手机号参数名
     * <p>
     * 用于发送验证码和手机号登录接口的手机号参数。
     * 参数类型：String
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_PHONE_NUMBER = "phoneNumber";

    /**
     * 验证码参数名
     * <p>
     * 用于手机号登录接口的验证码参数。
     * 参数类型：String
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_VERIFICATION_CODE = "verificationCode";

    /**
     * 授权码参数名
     * <p>
     * 用于第三方登录（Facebook、TikTok）接口的授权码参数。
     * 参数类型：String
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_CODE = "code";

    /**
     * 登录类型参数名
     * <p>
     * 用于统一登录接口的登录类型参数。
     * 参数类型：Integer
     * 取值：1=手机号登录, 2=VKontakte登录, 3=TikTok登录
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_LOGIN_TYPE = "loginType";

    /**
     * VKontakte授权码参数名
     * <p>
     * 用于VKontakte登录的授权码参数。
     * 参数类型：String
     * 是否必填：VKontakte登录时必填
     * </p>
     */
    public static final String PARAM_VKONTAKTE_CODE = "vKontakteCode";

    /**
     * TikTok授权码参数名
     * <p>
     * 用于TikTok登录的授权码参数。
     * 参数类型：String
     * 是否必填：TikTok登录时必填
     * </p>
     */
    public static final String PARAM_TIKTOK_CODE = "tiktokCode";

    /**
     * 设备唯一ID参数名
     * <p>
     * 用于登录和初始化接口的设备唯一标识参数。
     * 参数类型：String
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_DEVICE_CODE = "deviceCode";

    /**
     * 操作系统类型参数名
     * <p>
     * 用于登录和初始化接口的系统类型参数。
     * 参数类型：Integer
     * 取值：1=iOS, 2=Android
     * 是否必填：是
     * </p>
     */
    public static final String PARAM_OS_TYPE = "osType";

    // 注意：PARAM_DEVICE_ID 已在基类 BaseApiConstantsUtils 中定义，无需重复定义

    // ========== 登录类型常量 ==========

    /**
     * 手机号登录类型
     */
    public static final int LOGIN_TYPE_PHONE = 1;

    /**
     * VKontakte登录类型
     */
    public static final int LOGIN_TYPE_VKONTAKTE = 2;

    /**
     * TikTok登录类型
     */
    public static final int LOGIN_TYPE_TIKTOK = 3;

    // ========== 操作系统类型常量 ==========

    /**
     * iOS系统类型
     */
    public static final int OS_TYPE_IOS = 1;

    /**
     * Android系统类型
     */
    public static final int OS_TYPE_ANDROID = 2;

    // ========== 认证模块工具方法 ==========

    /**
     * 检查是否为认证相关的API路径
     * <p>
     * 判断给定的API路径是否属于认证模块。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是认证相关API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isAuth = isAuthApi("/app/customer/sendSmsCode");  // true
     * boolean isAuth = isAuthApi("/app/index/banners");         // false
     * </pre>
     */
    public static boolean isAuthApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }
        
        return apiPath.equals(API_SEND_SMS_CODE) ||
               apiPath.equals(API_LOGIN) ||
               apiPath.equals(API_INIT_DEVICE) ||
               apiPath.equals(API_PHONE_LOGIN) ||  // 保留弃用的API路径以兼容
               apiPath.equals(API_FACEBOOK_LOGIN) ||
               apiPath.equals(API_TIKTOK_LOGIN) ||
               apiPath.equals(API_GET_USER_INFO);
    }

    /**
     * 检查是否为第三方登录API
     * <p>
     * 判断给定的API路径是否为第三方登录接口。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是第三方登录API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isThirdParty = isThirdPartyLoginApi("/app/customer/login/facebook/callback");  // true
     * boolean isThirdParty = isThirdPartyLoginApi("/app/customer/login/phone");              // false
     * </pre>
     */
    public static boolean isThirdPartyLoginApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }
        
        return apiPath.equals(API_FACEBOOK_LOGIN) ||
               apiPath.equals(API_TIKTOK_LOGIN);
    }

    /**
     * 获取认证API的描述信息
     * <p>
     * 根据API路径返回对应的中文描述信息，
     * 便于调试和日志记录。
     * </p>
     * 
     * @param apiPath API路径
     * @return API的中文描述，未知API返回"未知认证接口"
     * 
     * @example
     * <pre>
     * String desc = getAuthApiDescription("/app/customer/sendSmsCode");  // "发送手机验证码"
     * String desc = getAuthApiDescription("/unknown/api");               // "未知认证接口"
     * </pre>
     */
    public static String getAuthApiDescription(String apiPath) {
        if (apiPath == null) {
            return "UNDEFINED_AUTH_ERROR";
        }
        
        switch (apiPath) {
            case API_SEND_SMS_CODE:
                return "发送手机验证码";
            case API_PHONE_LOGIN:
                return "手机号登录";
            case API_FACEBOOK_LOGIN:
                return "Facebook登录回调";
            case API_TIKTOK_LOGIN:
                return "TikTok登录回调";
            default:
                return "UNDEFINED_AUTH_ERROR";
        }
    }
}
