package com.android.video.ui.activity;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextWatcher;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.TextView;
import android.widget.Toast;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AlertDialog;
import androidx.appcompat.app.AppCompatActivity;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.android.flexbox.JustifyContent;
import com.android.video.R;
import com.android.video.ui.adapter.FeedbackTypeAdapter;
import com.android.video.ui.adapter.FeedbackImageAdapter;
import com.android.video.model.FeedbackType;
import com.android.video.network.ProfileApiService;
import com.android.video.network.CommonApiService;
import com.android.video.utils.UriUtils;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 反馈页面
 */
public class FeedbackActivity extends BaseFullScreenActivity {

    private ImageView ivBack;
    private RecyclerView rvFeedbackTypes;
    private EditText etProblemDescription;
    private TextView tvCharCount;
    private RecyclerView rvUploadImages;
    private LinearLayout layoutCountryCode;
    private TextView tvCountryCode;
    private EditText etContactPhone;
    private TextView btnSubmitFeedback;

    private FeedbackTypeAdapter feedbackTypeAdapter;
    private FeedbackImageAdapter feedbackImageAdapter;
    private List<FeedbackType> feedbackTypes;
    private List<String> uploadedImages;

    private static final int MAX_DESCRIPTION_LENGTH = 1000;
    private static final int MAX_IMAGE_COUNT = 3;

    // 权限请求码
    private static final int PERMISSION_REQUEST_CODE = 100;

    // ActivityResultLauncher for camera and gallery
    private ActivityResultLauncher<Intent> cameraLauncher;
    private ActivityResultLauncher<Intent> galleryLauncher;

    // 临时文件URI
    private Uri tempImageUri;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_feedback);

        initViews();
        initData();
        setupActivityResultLaunchers();
        setupClickListeners();
        setupTextWatcher();
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        rvFeedbackTypes = findViewById(R.id.rv_feedback_types);
        etProblemDescription = findViewById(R.id.et_problem_description);
        tvCharCount = findViewById(R.id.tv_char_count);
        rvUploadImages = findViewById(R.id.rv_upload_images);
        layoutCountryCode = findViewById(R.id.layout_country_code);
        tvCountryCode = findViewById(R.id.tv_country_code);
        etContactPhone = findViewById(R.id.et_contact_phone);
        btnSubmitFeedback = findViewById(R.id.btn_submit_feedback);
    }

    private void initData() {
        // 初始化反馈类型数据 - 从API获取
        feedbackTypes = new ArrayList<>();

        // 设置反馈类型适配器 - 使用FlexboxLayoutManager支持自适应换行
        feedbackTypeAdapter = new FeedbackTypeAdapter(feedbackTypes, this::onFeedbackTypeSelected);

        // 创建FlexboxLayoutManager
        FlexboxLayoutManager flexboxLayoutManager = new FlexboxLayoutManager(this);
        flexboxLayoutManager.setFlexDirection(FlexDirection.ROW); // 水平排列
        flexboxLayoutManager.setFlexWrap(FlexWrap.WRAP); // 支持换行
        flexboxLayoutManager.setJustifyContent(JustifyContent.FLEX_START); // 左对齐

        rvFeedbackTypes.setLayoutManager(flexboxLayoutManager);
        rvFeedbackTypes.setAdapter(feedbackTypeAdapter);

        // 初始化上传图片数据
        uploadedImages = new ArrayList<>();
        feedbackImageAdapter = new FeedbackImageAdapter(uploadedImages, this::onImageClick);
        rvUploadImages.setLayoutManager(new LinearLayoutManager(this, LinearLayoutManager.HORIZONTAL, false));
        rvUploadImages.setAdapter(feedbackImageAdapter);

        // 初始化字符计数
        updateCharCount();

        // 初始化提交按钮状态
        updateSubmitButtonState();

        // 从API加载反馈类型
        loadFeedbackTypesFromApi();
    }

    /**
     * 从API加载反馈类型
     */
    private void loadFeedbackTypesFromApi() {
        ProfileApiService.getInstance().getFeedbackTypes(new ProfileApiService.FeedbackTypeCallback() {
            @Override
            public void onSuccess(List<FeedbackType> apiTypes) {
                // 更新反馈类型数据
                feedbackTypes.clear();
                feedbackTypes.addAll(apiTypes);
                feedbackTypeAdapter.notifyDataSetChanged();

                // 更新提交按钮状态
                updateSubmitButtonState();

//                Toast.makeText(FeedbackActivity.this, "反馈类型加载成功", Toast.LENGTH_SHORT).show();
            }

            @Override
            public void onError(String errorMessage) {
                // API加载失败，使用默认数据
                loadDefaultFeedbackTypes();
                Toast.makeText(FeedbackActivity.this, "使用默认反馈类型: " + errorMessage, Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 加载默认反馈类型（API失败时的备用方案）
     */
    private void loadDefaultFeedbackTypes() {
        feedbackTypes.clear();
        feedbackTypes.add(new FeedbackType("dysfunction", "dysfunction", true)); // 默认选中
        feedbackTypes.add(new FeedbackType("Product recommendations", "Product recommendations", false));
        feedbackTypes.add(new FeedbackType("Interface optimization", "Interface optimization", false));
        feedbackTypes.add(new FeedbackType("Other", "Other", false));

        feedbackTypeAdapter.notifyDataSetChanged();
        updateSubmitButtonState();
    }

    private void setupActivityResultLaunchers() {
        // 相机拍照结果处理
        cameraLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 拍照成功，验证并添加图片到列表
                        if (tempImageUri != null) {
                            if (UriUtils.isValidUri(this, tempImageUri)) {
                                uploadedImages.add(tempImageUri.toString());
                                feedbackImageAdapter.notifyDataSetChanged();
                                updateSubmitButtonState();
                                Toast.makeText(this, "图片已添加", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(this, "图片文件无效，请重新拍照", Toast.LENGTH_SHORT).show();
                            }
                        }
                    } else {
                        Toast.makeText(this, "拍照取消", Toast.LENGTH_SHORT).show();
                    }
                }
        );

        // 相册选择结果处理
        galleryLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Uri selectedImageUri = result.getData().getData();
                        if (selectedImageUri != null) {
                            if (UriUtils.isValidUri(this, selectedImageUri)) {
                                uploadedImages.add(selectedImageUri.toString());
                                feedbackImageAdapter.notifyDataSetChanged();
                                updateSubmitButtonState();
                                Toast.makeText(this, "图片已添加", Toast.LENGTH_SHORT).show();
                            } else {
                                Toast.makeText(this, "选择的图片文件无效，请重新选择", Toast.LENGTH_SHORT).show();
                            }
                        } else {
                            Toast.makeText(this, "未选择有效的图片", Toast.LENGTH_SHORT).show();
                        }
                    } else {
                        Toast.makeText(this, "图片选择取消", Toast.LENGTH_SHORT).show();
                    }
                }
        );
    }

    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // 国家代码选择
        layoutCountryCode.setOnClickListener(v -> showCountryCodePopup());

        // 提交反馈按钮
        btnSubmitFeedback.setOnClickListener(v -> submitFeedback());
    }

    private void setupTextWatcher() {
        etProblemDescription.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateCharCount();
                updateSubmitButtonState();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });

        etContactPhone.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateSubmitButtonState();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    private void onFeedbackTypeSelected(FeedbackType selectedType) {
        // 更新选中状态
        for (FeedbackType type : feedbackTypes) {
            type.setSelected(type.getId().equals(selectedType.getId()));
        }
        feedbackTypeAdapter.notifyDataSetChanged();
        updateSubmitButtonState();
    }

    private void onImageClick(int position) {
        if (position == uploadedImages.size() && uploadedImages.size() < MAX_IMAGE_COUNT) {
            // 点击添加图片
            selectImage();
        } else if (position < uploadedImages.size()) {
            // 点击已上传的图片，可以预览或删除
            showImageOptions(position);
        }
    }

    private void selectImage() {
        // 检查权限
        if (!hasPermissions()) {
            requestPermissions();
            return;
        }

        // 显示图片选择对话框
        String[] options = {"拍摄", "从相册选取"};

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择图片")
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            // 拍摄
                            openCamera();
                            break;
                        case 1:
                            // 从相册选取
                            openGallery();
                            break;
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void showImageOptions(int position) {
        String[] options = {"预览", "删除"};

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("图片操作")
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            // 预览图片
                            previewImage(position);
                            break;
                        case 1:
                            // 删除图片
                            deleteImage(position);
                            break;
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void updateCharCount() {
        int currentLength = etProblemDescription.getText().toString().length();
        tvCharCount.setText(currentLength + "/" + MAX_DESCRIPTION_LENGTH);
    }

    private void updateSubmitButtonState() {
        boolean hasSelectedType = feedbackTypes.stream().anyMatch(FeedbackType::isSelected);
        boolean hasDescription = !etProblemDescription.getText().toString().trim().isEmpty();
        boolean hasImages = !uploadedImages.isEmpty();
        boolean hasPhone = !etContactPhone.getText().toString().trim().isEmpty();

        boolean canSubmit = hasSelectedType && hasDescription && hasImages && hasPhone;

        btnSubmitFeedback.setEnabled(canSubmit);
        btnSubmitFeedback.setAlpha(canSubmit ? 1.0f : 0.5f);
    }

    private void submitFeedback() {
        if (validateInput()) {
            // 禁用提交按钮，显示加载状态
            btnSubmitFeedback.setEnabled(false);
            btnSubmitFeedback.setText("提交中...");

            // 如果有图片，先上传图片，然后提交反馈
            if (!uploadedImages.isEmpty()) {
                uploadImagesAndSubmit();
            } else {
                // 没有图片，直接提交反馈
                submitFeedbackToServer(null);
            }
        }
    }

    /**
     * 上传图片并提交反馈
     */
    private void uploadImagesAndSubmit() {
        List<String> imageUrls = new ArrayList<>();
        List<File> tempFiles = new ArrayList<>(); // 记录临时文件，用于后续清理
        AtomicInteger uploadCount = new AtomicInteger(0);
        int totalImages = uploadedImages.size();

        CommonApiService commonApiService = CommonApiService.getInstance();

        for (String imagePath : uploadedImages) {
            // 使用UriUtils将Uri字符串转换为File对象
            File imageFile = UriUtils.uriStringToFile(this, imagePath, "feedback_image");

            if (imageFile == null || !imageFile.exists()) {
                Log.e("FeedbackActivity", "图片文件转换失败或不存在: " + imagePath);
                runOnUiThread(() -> {
                    // 恢复提交按钮状态
                    btnSubmitFeedback.setEnabled(true);
                    btnSubmitFeedback.setText("Submit Feedback");
                    Toast.makeText(FeedbackActivity.this, "图片文件不存在或无效，请重新选择图片", Toast.LENGTH_LONG).show();
                });
                return;
            }

            // 记录临时文件
            if (imageFile.getName().contains("temp_") || imageFile.getName().contains("feedback_image")) {
                tempFiles.add(imageFile);
            }

            commonApiService.uploadFile(imageFile, new CommonApiService.FileUploadCallback() {
                @Override
                public void onSuccess(String fileUrl) {
                    synchronized (imageUrls) {
                        imageUrls.add(fileUrl);
                        int completed = uploadCount.incrementAndGet();

                        Log.d("FeedbackActivity", "图片上传成功 (" + completed + "/" + totalImages + "): " + fileUrl);

                        if (completed == totalImages) {
                            // 所有图片上传完成，提交反馈
                            submitFeedbackToServer(imageUrls);
                            // 清理临时文件
                            cleanupTempFiles(tempFiles);
                        }
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e("FeedbackActivity", "图片上传失败: " + errorMessage);
                    runOnUiThread(() -> {
                        // 恢复提交按钮状态
                        btnSubmitFeedback.setEnabled(true);
                        btnSubmitFeedback.setText("Submit Feedback");
                        Toast.makeText(FeedbackActivity.this, "图片上传失败: " + errorMessage, Toast.LENGTH_LONG).show();
                        // 清理临时文件
                        cleanupTempFiles(tempFiles);
                    });
                }
            });
        }
    }

    /**
     * 提交反馈到服务器
     * @param imageUrls 上传成功的图片URL列表，可为null
     */
    private void submitFeedbackToServer(List<String> imageUrls) {
        // 获取选中的反馈类型
        FeedbackType selectedType = null;
        for (FeedbackType type : feedbackTypes) {
            if (type.isSelected()) {
                selectedType = type;
                break;
            }
        }

        if (selectedType == null) {
            runOnUiThread(() -> {
                btnSubmitFeedback.setEnabled(true);
                btnSubmitFeedback.setText("Submit Feedback");
                Toast.makeText(this, "请选择反馈类型", Toast.LENGTH_SHORT).show();
            });
            return;
        }

        // 获取反馈内容
        String description = etProblemDescription.getText().toString().trim();

        // 获取联系方式
        String countryCode = tvCountryCode.getText().toString().trim();
        String phoneNumber = etContactPhone.getText().toString().trim();
        String contactInfo = "";
        if (!phoneNumber.isEmpty()) {
            contactInfo = countryCode + phoneNumber;
        }

        // 构建图片URL字符串
        String picturesString = "";
        if (imageUrls != null && !imageUrls.isEmpty()) {
            StringBuilder sb = new StringBuilder();
            for (String url : imageUrls) {
                sb.append(url).append(",");
            }
            picturesString = sb.toString();
        }

        // 调用API提交反馈
        ProfileApiService.getInstance().submitFeedback(
            selectedType.getFeedbackTypeId(),
            description,
            picturesString,
            contactInfo,
            new ProfileApiService.SubmitFeedbackCallback() {
                @Override
                public void onSuccess() {
                    runOnUiThread(() -> {
                        Toast.makeText(FeedbackActivity.this, "反馈提交成功", Toast.LENGTH_SHORT).show();

                        // 返回个人中心
                        Intent intent = new Intent(FeedbackActivity.this, MainActivity.class);
                        intent.setFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP);
                        intent.putExtra("selected_tab", "profile");
                        startActivity(intent);
                        finish();
                    });
                }

                @Override
                public void onError(String errorMessage) {
                    runOnUiThread(() -> {
                        // 恢复提交按钮状态
                        btnSubmitFeedback.setEnabled(true);
                        btnSubmitFeedback.setText("Submit Feedback");
                        Toast.makeText(FeedbackActivity.this, "反馈提交失败: " + errorMessage, Toast.LENGTH_LONG).show();
                    });
                }
            }
        );
    }

    private boolean validateInput() {
        // 验证反馈类型
        boolean hasSelectedType = feedbackTypes.stream().anyMatch(FeedbackType::isSelected);
        if (!hasSelectedType) {
            Toast.makeText(this, "请选择反馈类型", Toast.LENGTH_SHORT).show();
            return false;
        }

        // 验证问题描述
        String description = etProblemDescription.getText().toString().trim();
        if (description.isEmpty()) {
            Toast.makeText(this, "请填写问题描述", Toast.LENGTH_SHORT).show();
            return false;
        }

        // 验证图片
        if (uploadedImages.isEmpty()) {
            Toast.makeText(this, "请上传至少一张图片", Toast.LENGTH_SHORT).show();
            return false;
        }

        // 验证联系电话
        String phone = etContactPhone.getText().toString().trim();
        if (phone.isEmpty()) {
            Toast.makeText(this, "请填写联系电话", Toast.LENGTH_SHORT).show();
            return false;
        }

        return true;
    }

    /**
     * 显示国家代码选择弹窗
     */
    private void showCountryCodePopup() {
        // 创建下拉菜单
        View popupView = LayoutInflater.from(this).inflate(R.layout.popup_country_code, null);

        PopupWindow popupWindow = new PopupWindow(popupView,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                true);

        // 设置背景，使点击外部可以关闭
        popupWindow.setBackgroundDrawable(getResources().getDrawable(android.R.color.transparent));
        popupWindow.setOutsideTouchable(true);

        // 设置点击事件
        TextView tvChina = popupView.findViewById(R.id.tv_country_china);
        TextView tvUsa = popupView.findViewById(R.id.tv_country_usa);
        TextView tvUk = popupView.findViewById(R.id.tv_country_uk);
        TextView tvJapan = popupView.findViewById(R.id.tv_country_japan);
        TextView tvKorea = popupView.findViewById(R.id.tv_country_korea);
        TextView tvOther = popupView.findViewById(R.id.tv_country_other);

        View.OnClickListener clickListener = v -> {
            TextView selectedView = (TextView) v;
            tvCountryCode.setText(selectedView.getText().toString());
            popupWindow.dismiss();
        };

        tvChina.setOnClickListener(clickListener);
        tvUsa.setOnClickListener(clickListener);
        tvUk.setOnClickListener(clickListener);
        tvJapan.setOnClickListener(clickListener);
        tvKorea.setOnClickListener(clickListener);
        tvOther.setOnClickListener(clickListener);

        // 在国家代码布局下方显示
        popupWindow.showAsDropDown(layoutCountryCode, 0, 0);
    }

    /**
     * 预览图片
     */
    private void previewImage(int position) {
        // TODO: 实现图片预览功能
        Toast.makeText(this, "图片预览功能待实现", Toast.LENGTH_SHORT).show();
    }

    /**
     * 删除图片
     */
    private void deleteImage(int position) {
        if (position < uploadedImages.size()) {
            uploadedImages.remove(position);
            feedbackImageAdapter.notifyDataSetChanged();
            updateSubmitButtonState();
            Toast.makeText(this, "图片已删除", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 检查是否有必要的权限
     */
    private boolean hasPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 请求权限
     */
    private void requestPermissions() {
        String[] permissions = {
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
        };

        ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
    }

    /**
     * 打开相机
     */
    private void openCamera() {
        try {
            Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

            // 创建临时文件
            File tempFile = new File(getExternalFilesDir(null), "temp_feedback_" + System.currentTimeMillis() + ".jpg");
            tempImageUri = FileProvider.getUriForFile(this,
                    getApplicationContext().getPackageName() + ".fileprovider", tempFile);

            cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, tempImageUri);
            cameraLauncher.launch(cameraIntent);

        } catch (Exception e) {
            Toast.makeText(this, "无法打开相机", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 打开相册
     */
    private void openGallery() {
        Intent galleryIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        galleryLauncher.launch(galleryIntent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }

            if (allPermissionsGranted) {
                Toast.makeText(this, "权限已获取，请重新点击添加图片", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "需要相机和存储权限才能添加图片", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 清理临时文件
     * @param tempFiles 临时文件列表
     */
    private void cleanupTempFiles(List<File> tempFiles) {
        if (tempFiles == null || tempFiles.isEmpty()) {
            return;
        }

        for (File tempFile : tempFiles) {
            UriUtils.cleanupTempFile(tempFile);
        }
    }
}
