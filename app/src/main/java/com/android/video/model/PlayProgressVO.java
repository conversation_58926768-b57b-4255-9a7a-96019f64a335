package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 播放进度信息数据模型
 * <p>
 * 用于映射API返回的播放进度信息JSON数据结构。
 * 包含最后一次播放记录的详细信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class PlayProgressVO {
    
    /**
     * 最后一次播放记录ID
     */
    @SerializedName("playProgressId")
    private String playProgressId;
    
    /**
     * 章节ID
     */
    @SerializedName("chapterId")
    private String chapterId;
    
    /**
     * 播放进度
     * 单位：秒
     */
    @SerializedName("progress")
    private Integer progress;
    
    /**
     * 最后一次播放时间
     * 格式：yyyy-MM-dd HH:mm:ss
     */
    @SerializedName("lastPlayTime")
    private String lastPlayTime;
    
    /**
     * 是否播放完成
     * 0=未完成, 1=已完成
     */
    @SerializedName("isFinished")
    private Integer isFinished;
    
    /**
     * 章节集数
     */
    @SerializedName("chapterEp")
    private Integer chapterEp;

    /**
     * 默认构造函数
     */
    public PlayProgressVO() {
    }

    /**
     * 完整构造函数
     */
    public PlayProgressVO(String playProgressId, String chapterId, Integer progress,
                         String lastPlayTime, Integer isFinished, Integer chapterEp) {
        this.playProgressId = playProgressId;
        this.chapterId = chapterId;
        this.progress = progress;
        this.lastPlayTime = lastPlayTime;
        this.isFinished = isFinished;
        this.chapterEp = chapterEp;
    }

    // ========== Getter和Setter方法 ==========

    public String getPlayProgressId() {
        return playProgressId;
    }

    public void setPlayProgressId(String playProgressId) {
        this.playProgressId = playProgressId;
    }

    public String getChapterId() {
        return chapterId;
    }

    public void setChapterId(String chapterId) {
        this.chapterId = chapterId;
    }

    public Integer getProgress() {
        return progress;
    }

    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    public String getLastPlayTime() {
        return lastPlayTime;
    }

    public void setLastPlayTime(String lastPlayTime) {
        this.lastPlayTime = lastPlayTime;
    }

    public Integer getIsFinished() {
        return isFinished;
    }

    public void setIsFinished(Integer isFinished) {
        this.isFinished = isFinished;
    }

    public Integer getChapterEp() {
        return chapterEp;
    }

    public void setChapterEp(Integer chapterEp) {
        this.chapterEp = chapterEp;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否播放完成
     * 
     * @return 如果播放完成返回true，否则返回false
     */
    public boolean isPlayFinished() {
        return isFinished != null && isFinished == 1;
    }

    /**
     * 获取格式化的播放进度
     * 
     * @return 格式化的播放进度字符串 (如: "2:30")
     */
    public String getFormattedProgress() {
        if (progress == null || progress <= 0) return "0:00";
        
        int minutes = progress / 60;
        int seconds = progress % 60;
        return String.format("%d:%02d", minutes, seconds);
    }

    /**
     * 获取播放进度百分比
     * 
     * @param totalDuration 总时长（秒）
     * @return 播放进度百分比 (0-100)
     */
    public float getProgressPercentage(int totalDuration) {
        if (progress == null || progress <= 0 || totalDuration <= 0) return 0.0f;
        return Math.min(100.0f, (progress * 100.0f) / totalDuration);
    }

    @Override
    public String toString() {
        return "PlayProgressVO{" +
                "playProgressId='" + playProgressId + '\'' +
                ", chapterId='" + chapterId + '\'' +
                ", progress=" + progress +
                ", lastPlayTime='" + lastPlayTime + '\'' +
                ", isFinished=" + isFinished +
                ", chapterEp=" + chapterEp +
                '}';
    }
}
