package com.android.video.model.response;

import com.google.gson.annotations.SerializedName;
import com.android.video.model.MyHistoryDataModel;

/**
 * 我的播放历史列表API响应模型
 * <AUTHOR> Team
 */
public class MyHistoryListResponseModel {
    
    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 响应数据
     */
    @SerializedName("data")
    private MyHistoryDataModel data;

    public MyHistoryListResponseModel() {
    }

    public MyHistoryListResponseModel(String code, String message, MyHistoryDataModel data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // Getters and Setters
    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MyHistoryDataModel getData() {
        return data;
    }

    public void setData(MyHistoryDataModel data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * @return 是否成功
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 检查是否有数据
     * @return 是否有数据
     */
    public boolean hasData() {
        return data != null && !data.isEmpty();
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null && !message.trim().isEmpty() ? message : "Unknown error";
    }

    @Override
    public String toString() {
        return "MyHistoryListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }
}
