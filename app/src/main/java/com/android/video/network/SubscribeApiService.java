package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.HomeApiConstantsUtils;
import com.android.video.utils.ApiHeaderUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.Map;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 订阅API服务类
 * <p>
 * 提供短剧订阅和取消订阅的网络请求功能。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils
 */
public class SubscribeApiService {

    private static final String TAG = "SubscribeApiService";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient client;
    private final Handler mainHandler;

    // 单例实例
    private static volatile SubscribeApiService instance;

    /**
     * 订阅操作回调接口
     */
    public interface SubscribeCallback {
        /**
         * 订阅操作成功
         * @param isSubscribed 订阅状态：true=已订阅，false=已取消订阅
         * @param message 服务器返回的消息
         */
        void onSuccess(boolean isSubscribed, String message);

        /**
         * 订阅操作失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造函数
     */
    private SubscribeApiService() {
        // 使用统一的HTTP客户端，确保包含HeaderInterceptor
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
            Log.d(TAG, "Using ApiClientUtils HTTP client with HeaderInterceptor");
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，创建包含HeaderInterceptor的客户端
            Log.w(TAG, "ApiClientUtils not initialized, creating client with HeaderInterceptor");
            httpClient = createClientWithHeaderInterceptor();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
    }

    /**
     * 创建包含HeaderInterceptor的HTTP客户端
     * 用于ApiClientUtils未初始化时的降级处理
     */
    private OkHttpClient createClientWithHeaderInterceptor() {
        return new OkHttpClient.Builder()
                .addInterceptor(chain -> {
                    Request originalRequest = chain.request();
                    Request.Builder requestBuilder = originalRequest.newBuilder()
                            .header("Content-Type", "application/json")
                            .header("User-Agent", "VideoPlayer/1.0");

                    // 自动添加访问令牌
                    String token = ApiHeaderUtils.getCurrentAccessToken();
                    if (token != null && !token.trim().isEmpty()) {
                        requestBuilder.header("X-Access-Token", token);
                        Log.d(TAG, "Added token to subscribe request: " + maskToken(token));
                    } else {
                        Log.w(TAG, "No token available for subscribe request: " + originalRequest.url());
                    }

                    return chain.proceed(requestBuilder.build());
                })
                .build();
    }

    /**
     * 脱敏处理token用于日志输出
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }

    /**
     * 获取单例实例
     * @return SubscribeApiService实例
     */
    public static SubscribeApiService getInstance() {
        if (instance == null) {
            synchronized (SubscribeApiService.class) {
                if (instance == null) {
                    instance = new SubscribeApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 订阅短剧
     * <p>
     * 异步订阅指定的短剧内容。
     * </p>
     *
     * @param filmId 短剧ID
     * @param callback 回调接口
     */
    public void subscribe(String filmId, SubscribeCallback callback) {
        performSubscribeOperation(filmId, HomeApiConstantsUtils.SUBSCRIBE_TYPE_SUBSCRIBE, callback);
    }

    /**
     * 取消订阅短剧
     * <p>
     * 异步取消订阅指定的短剧内容。
     * </p>
     *
     * @param filmId 短剧ID
     * @param callback 回调接口
     */
    public void unsubscribe(String filmId, SubscribeCallback callback) {
        performSubscribeOperation(filmId, HomeApiConstantsUtils.SUBSCRIBE_TYPE_UNSUBSCRIBE, callback);
    }

    /**
     * 执行订阅操作
     * <p>
     * 统一的订阅/取消订阅操作实现。
     * </p>
     *
     * @param filmId 短剧ID
     * @param subscribeType 订阅类型：0=取消订阅，1=订阅
     * @param callback 回调接口
     */
    private void performSubscribeOperation(String filmId, int subscribeType, SubscribeCallback callback) {
        // 参数验证
        if (!HomeApiConstantsUtils.isValidFilmId(filmId)) {
            if (callback != null) {
                callback.onError("短剧ID无效");
            }
            return;
        }

        if (!HomeApiConstantsUtils.isValidSubscribeType(subscribeType)) {
            if (callback != null) {
                callback.onError("订阅类型无效");
            }
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            HomeApiConstantsUtils.API_SUBSCRIBE
        );

        // 构建POST请求体
        JSONObject requestJson = new JSONObject();
        try {
            requestJson.put(HomeApiConstantsUtils.PARAM_FILM_ID, filmId);
            requestJson.put(HomeApiConstantsUtils.PARAM_SUBSCRIBE_TYPE, subscribeType);
        } catch (JSONException e) {
            Log.e(TAG, "构建订阅请求JSON失败", e);
            if (callback != null) {
                callback.onError("请求参数构建失败");
            }
            return;
        }

        RequestBody requestBody = RequestBody.create(requestJson.toString(), JSON);

        // 构建POST请求（token由HeaderInterceptor自动添加）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        String operationDesc = HomeApiConstantsUtils.getSubscribeTypeDescription(subscribeType);
        Log.d(TAG, "=== " + operationDesc + "请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "请求体: " + requestJson.toString());
        Log.d(TAG, "短剧ID: " + filmId);
        Log.d(TAG, "操作类型: " + operationDesc);
        Log.d(TAG, "==================");

        // 异步执行网络请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, operationDesc + "请求失败", e);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("网络请求失败: " + e.getMessage());
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "=== " + operationDesc + "响应 ===");
                    Log.d(TAG, "HTTP状态码: " + response.code());
                    Log.d(TAG, "响应体长度: " + responseBody.length());
                    Log.d(TAG, "响应内容: " + responseBody);
                    Log.d(TAG, "==================");

                    if (response.isSuccessful()) {
                        // 解析响应数据
                        parseSubscribeResponse(responseBody, subscribeType, callback);
                    } else {
                        String errorMessage = "服务器错误: " + response.code();
                        if (response.code() == 401) {
                            errorMessage += " (认证失败，请重新登录)";
                        }

                        final String finalErrorMessage = errorMessage;
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError(finalErrorMessage);
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, operationDesc + "响应处理失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应处理失败: " + e.getMessage());
                        }
                    });
                }
            }
        });
    }

    /**
     * 解析订阅操作响应
     * <p>
     * 解析服务器返回的订阅操作结果。
     * </p>
     *
     * @param responseBody 响应体内容
     * @param subscribeType 订阅类型
     * @param callback 回调接口
     */
    private void parseSubscribeResponse(String responseBody, int subscribeType, SubscribeCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "订阅API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "订阅操作失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取响应消息
            String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "操作成功");
            boolean isSubscribed = (subscribeType == HomeApiConstantsUtils.SUBSCRIBE_TYPE_SUBSCRIBE);

            String operationDesc = HomeApiConstantsUtils.getSubscribeTypeDescription(subscribeType);
            Log.d(TAG, operationDesc + "成功: " + message);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(isSubscribed, message);
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "解析订阅响应失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }
}
