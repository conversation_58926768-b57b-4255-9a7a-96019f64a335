<?xml version="1.0" encoding="utf-8"?>
<layer-list xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 渐变边框 -->
    <item>
        <shape android:shape="rectangle">
            <gradient
                android:startColor="@color/subtitle_panel_language_selected_border_start"
                android:endColor="@color/subtitle_panel_language_selected_border_end"
                android:angle="0" />
            <corners android:radius="@dimen/subtitle_panel_language_corner_radius" />
        </shape>
    </item>
    
    <!-- 内部背景 -->
    <item 
        android:left="1dp"
        android:top="1dp"
        android:right="1dp"
        android:bottom="1dp">
        <shape android:shape="rectangle">
            <solid android:color="@color/subtitle_panel_language_selected_bg" />
            <corners android:radius="@dimen/subtitle_panel_language_corner_radius" />
        </shape>
    </item>
    
</layer-list>
