package com.android.video.model.response;

import com.android.video.model.MessageItemModel;
import com.google.gson.annotations.SerializedName;

import java.util.List;

/**
 * 消息列表响应数据模型
 * <p>
 * 用于映射消息列表API返回的JSON数据结构。
 * 包含分页信息和消息记录列表。
 * </p>
 * 
 * <p>
 * API响应格式：
 * <pre>
 * {
 *   "code": "200",
 *   "message": "success",
 *   "data": {
 *     "records": [...],
 *     "total": 2,
 *     "size": 10,
 *     "current": 1,
 *     "pages": 1,
 *     "orders": [],
 *     "optimizeCountSql": true,
 *     "hitCount": false,
 *     "countId": null,
 *     "maxLimit": null,
 *     "searchCount": true
 *   }
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class MessageListResponseModel {
    
    /**
     * 响应码
     * "200" 表示成功
     */
    @SerializedName("code")
    private String code;
    
    /**
     * 响应消息
     * 通常为 "success" 或错误信息
     */
    @SerializedName("message")
    private String message;
    
    /**
     * 消息列表数据
     */
    @SerializedName("data")
    private MessageListData data;

    /**
     * 默认构造函数
     */
    public MessageListResponseModel() {
    }

    /**
     * 完整构造函数
     * 
     * @param code 响应码
     * @param message 响应消息
     * @param data 消息列表数据
     */
    public MessageListResponseModel(String code, String message, MessageListData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public MessageListData getData() {
        return data;
    }

    public void setData(MessageListData data) {
        this.data = data;
    }

    /**
     * 检查响应是否成功
     * 
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    @Override
    public String toString() {
        return "MessageListResponseModel{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * 消息列表数据
     * <p>
     * 包含分页信息和消息记录列表。
     * </p>
     */
    public static class MessageListData {
        
        /**
         * 消息记录列表
         */
        @SerializedName("records")
        private List<MessageItemModel> records;
        
        /**
         * 总记录数
         */
        @SerializedName("total")
        private int total;
        
        /**
         * 每页数量
         */
        @SerializedName("size")
        private int size;
        
        /**
         * 当前页码
         */
        @SerializedName("current")
        private int current;
        
        /**
         * 总页数
         */
        @SerializedName("pages")
        private int pages;
        
        /**
         * 排序信息
         */
        @SerializedName("orders")
        private List<Object> orders;
        
        /**
         * 是否优化count SQL
         */
        @SerializedName("optimizeCountSql")
        private boolean optimizeCountSql;
        
        /**
         * 是否命中计数
         */
        @SerializedName("hitCount")
        private boolean hitCount;
        
        /**
         * 计数ID
         */
        @SerializedName("countId")
        private String countId;
        
        /**
         * 最大限制
         */
        @SerializedName("maxLimit")
        private Integer maxLimit;
        
        /**
         * 是否搜索计数
         */
        @SerializedName("searchCount")
        private boolean searchCount;

        /**
         * 默认构造函数
         */
        public MessageListData() {
        }

        // ========== Getter和Setter方法 ==========

        public List<MessageItemModel> getRecords() {
            return records;
        }

        public void setRecords(List<MessageItemModel> records) {
            this.records = records;
        }

        public int getTotal() {
            return total;
        }

        public void setTotal(int total) {
            this.total = total;
        }

        public int getSize() {
            return size;
        }

        public void setSize(int size) {
            this.size = size;
        }

        public int getCurrent() {
            return current;
        }

        public void setCurrent(int current) {
            this.current = current;
        }

        public int getPages() {
            return pages;
        }

        public void setPages(int pages) {
            this.pages = pages;
        }

        public List<Object> getOrders() {
            return orders;
        }

        public void setOrders(List<Object> orders) {
            this.orders = orders;
        }

        public boolean isOptimizeCountSql() {
            return optimizeCountSql;
        }

        public void setOptimizeCountSql(boolean optimizeCountSql) {
            this.optimizeCountSql = optimizeCountSql;
        }

        public boolean isHitCount() {
            return hitCount;
        }

        public void setHitCount(boolean hitCount) {
            this.hitCount = hitCount;
        }

        public String getCountId() {
            return countId;
        }

        public void setCountId(String countId) {
            this.countId = countId;
        }

        public Integer getMaxLimit() {
            return maxLimit;
        }

        public void setMaxLimit(Integer maxLimit) {
            this.maxLimit = maxLimit;
        }

        public boolean isSearchCount() {
            return searchCount;
        }

        public void setSearchCount(boolean searchCount) {
            this.searchCount = searchCount;
        }

        @Override
        public String toString() {
            return "MessageListData{" +
                    "records=" + records +
                    ", total=" + total +
                    ", size=" + size +
                    ", current=" + current +
                    ", pages=" + pages +
                    ", orders=" + orders +
                    ", optimizeCountSql=" + optimizeCountSql +
                    ", hitCount=" + hitCount +
                    ", countId='" + countId + '\'' +
                    ", maxLimit=" + maxLimit +
                    ", searchCount=" + searchCount +
                    '}';
        }
    }
}
