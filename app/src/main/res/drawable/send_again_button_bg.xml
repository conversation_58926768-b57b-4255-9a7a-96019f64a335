<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">

    <!-- Pressed state -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="@color/send_again_button_pressed" />
            <corners android:radius="21dp" />
        </shape>
    </item>

    <!-- Normal state -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/white" />
            <corners android:radius="21dp" />
        </shape>
    </item>

</selector>
