package com.android.video.download;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.concurrent.TimeUnit;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 文件下载器
 * <p>
 * 负责实际的文件下载操作，支持断点续传、进度回调、错误处理等功能。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class FileDownloader {

    private static final String TAG = "FileDownloader";
    private static final int BUFFER_SIZE = 8192; // 8KB缓冲区

    private final DownloadConfig config;
    private final OkHttpClient httpClient;
    private final Handler mainHandler;
    private volatile boolean isCancelled = false;
    private volatile boolean isPaused = false;
    private Call currentCall;

    /**
     * 构造函数
     *
     * @param config 下载配置
     */
    public FileDownloader(DownloadConfig config) {
        this.config = config;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.httpClient = createHttpClient();
    }

    /**
     * 创建HTTP客户端
     *
     * @return OkHttpClient实例
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient.Builder()
                .connectTimeout(config.getConnectTimeout(), TimeUnit.MILLISECONDS)
                .readTimeout(config.getReadTimeout(), TimeUnit.MILLISECONDS)
                .writeTimeout(config.getWriteTimeout(), TimeUnit.MILLISECONDS)
                .retryOnConnectionFailure(true)
                .build();
    }

    /**
     * 开始下载
     *
     * @param downloadInfo 下载信息
     * @param listener 下载监听器
     */
    public void startDownload(DownloadInfo downloadInfo, DownloadListener listener) {
        if (downloadInfo == null || listener == null) {
            Log.e(TAG, "DownloadInfo or DownloadListener is null");
            return;
        }

        // 重置状态
        isCancelled = false;
        isPaused = false;

        // 确保下载目录存在
        if (!config.ensureDownloadDirectoryExists()) {
            String error = "无法创建下载目录";
            Log.e(TAG, error);
            notifyDownloadFailed(downloadInfo, listener, error);
            return;
        }

        // 检查文件是否已存在且完整
        File targetFile = new File(downloadInfo.getFilePath());
        if (targetFile.exists() && targetFile.length() > 0) {
            // 文件已存在，检查是否需要重新下载
            if (downloadInfo.getTotalSize() > 0 && targetFile.length() == downloadInfo.getTotalSize()) {
                // 文件完整，直接标记为完成
                downloadInfo.setStatus(DownloadInfo.DownloadStatus.COMPLETED);
                downloadInfo.setDownloadedSize(targetFile.length());
                downloadInfo.updateProgress(targetFile.length(), targetFile.length());
                notifyDownloadComplete(downloadInfo, listener);
                return;
            }
        }

        // 开始下载
        downloadInfo.setStatus(DownloadInfo.DownloadStatus.DOWNLOADING);
        notifyDownloadStart(downloadInfo, listener);

        performDownload(downloadInfo, listener);
    }

    /**
     * 执行下载
     *
     * @param downloadInfo 下载信息
     * @param listener 下载监听器
     */
    private void performDownload(DownloadInfo downloadInfo, DownloadListener listener) {
        File tempFile = new File(downloadInfo.getTempFilePath());
        final long resumePosition;

        // 检查是否支持断点续传
        if (tempFile.exists() && tempFile.length() > 0) {
            resumePosition = tempFile.length();
            downloadInfo.setDownloadedSize(resumePosition);
            Log.d(TAG, "Resume download from position: " + resumePosition);
        } else {
            resumePosition = 0;
        }

        // 构建请求
        Request.Builder requestBuilder = new Request.Builder()
                .url(downloadInfo.getUrl())
                .get();

        // 添加Range头支持断点续传
        if (resumePosition > 0) {
            requestBuilder.addHeader("Range", "bytes=" + resumePosition + "-");
        }

        Request request = requestBuilder.build();
        currentCall = httpClient.newCall(request);

        currentCall.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                if (isCancelled) {
                    notifyDownloadCancelled(downloadInfo, listener);
                } else {
                    String error = "网络请求失败: " + e.getMessage();
                    Log.e(TAG, error, e);
                    notifyDownloadFailed(downloadInfo, listener, error);
                }
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                if (!response.isSuccessful() && response.code() != 206) {
                    String error = "服务器响应错误: " + response.code();
                    Log.e(TAG, error);
                    notifyDownloadFailed(downloadInfo, listener, error);
                    return;
                }

                try {
                    downloadFile(response, downloadInfo, listener, resumePosition);
                } catch (Exception e) {
                    if (!isCancelled) {
                        String error = "下载文件失败: " + e.getMessage();
                        Log.e(TAG, error, e);
                        notifyDownloadFailed(downloadInfo, listener, error);
                    }
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * 下载文件
     *
     * @param response HTTP响应
     * @param downloadInfo 下载信息
     * @param listener 下载监听器
     * @param resumePosition 断点续传位置
     * @throws IOException IO异常
     */
    private void downloadFile(Response response, DownloadInfo downloadInfo, 
                             DownloadListener listener, long resumePosition) throws IOException {
        
        InputStream inputStream = response.body().byteStream();
        File tempFile = new File(downloadInfo.getTempFilePath());
        
        // 获取文件总大小
        long contentLength = response.body().contentLength();
        long totalSize = resumePosition + contentLength;
        
        if (downloadInfo.getTotalSize() == 0) {
            downloadInfo.setTotalSize(totalSize);
        }

        FileOutputStream outputStream = null;
        try {
            // 以追加模式打开文件输出流
            outputStream = new FileOutputStream(tempFile, resumePosition > 0);
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            long totalBytesRead = resumePosition;
            long lastProgressUpdate = System.currentTimeMillis();

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                // 检查是否被取消或暂停
                if (isCancelled) {
                    break;
                }
                
                if (isPaused) {
                    // 暂停下载
                    downloadInfo.setStatus(DownloadInfo.DownloadStatus.PAUSED);
                    notifyDownloadPause(downloadInfo, listener);
                    return;
                }

                outputStream.write(buffer, 0, bytesRead);
                totalBytesRead += bytesRead;
                
                // 更新进度
                downloadInfo.updateProgress(totalBytesRead, totalSize);
                
                // 定期通知进度更新
                long currentTime = System.currentTimeMillis();
                if (currentTime - lastProgressUpdate >= config.getProgressUpdateInterval()) {
                    notifyDownloadProgress(downloadInfo, listener);
                    lastProgressUpdate = currentTime;
                }
            }

            outputStream.flush();

            if (isCancelled) {
                notifyDownloadCancelled(downloadInfo, listener);
                return;
            }

            // 下载完成，移动临时文件到目标位置
            File targetFile = new File(downloadInfo.getFilePath());
            if (tempFile.renameTo(targetFile)) {
                downloadInfo.setStatus(DownloadInfo.DownloadStatus.COMPLETED);
                notifyDownloadComplete(downloadInfo, listener);
                Log.d(TAG, "Download completed: " + downloadInfo.getFileName());
            } else {
                String error = "无法移动临时文件到目标位置";
                Log.e(TAG, error);
                notifyDownloadFailed(downloadInfo, listener, error);
            }

        } finally {
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    Log.e(TAG, "Error closing output stream", e);
                }
            }
        }
    }

    /**
     * 暂停下载
     */
    public void pauseDownload() {
        isPaused = true;
        if (currentCall != null && !currentCall.isCanceled()) {
            currentCall.cancel();
        }
    }

    /**
     * 取消下载
     */
    public void cancelDownload() {
        isCancelled = true;
        if (currentCall != null && !currentCall.isCanceled()) {
            currentCall.cancel();
        }
    }

    /**
     * 检查是否被取消
     *
     * @return 如果被取消返回true，否则返回false
     */
    public boolean isCancelled() {
        return isCancelled;
    }

    /**
     * 检查是否被暂停
     *
     * @return 如果被暂停返回true，否则返回false
     */
    public boolean isPaused() {
        return isPaused;
    }

    // ========== 通知方法 ==========

    private void notifyDownloadStart(DownloadInfo downloadInfo, DownloadListener listener) {
        mainHandler.post(() -> listener.onDownloadStart(downloadInfo));
    }

    private void notifyDownloadProgress(DownloadInfo downloadInfo, DownloadListener listener) {
        mainHandler.post(() -> listener.onDownloadProgress(
                downloadInfo, 
                downloadInfo.getDownloadedSize(), 
                downloadInfo.getTotalSize(), 
                downloadInfo.getProgress()
        ));
    }

    private void notifyDownloadPause(DownloadInfo downloadInfo, DownloadListener listener) {
        mainHandler.post(() -> listener.onDownloadPause(downloadInfo));
    }

    private void notifyDownloadComplete(DownloadInfo downloadInfo, DownloadListener listener) {
        mainHandler.post(() -> listener.onDownloadComplete(downloadInfo));
    }

    private void notifyDownloadFailed(DownloadInfo downloadInfo, DownloadListener listener, String error) {
        downloadInfo.setStatus(DownloadInfo.DownloadStatus.FAILED);
        downloadInfo.setErrorMessage(error);
        mainHandler.post(() -> listener.onDownloadFailed(downloadInfo, error));
    }

    private void notifyDownloadCancelled(DownloadInfo downloadInfo, DownloadListener listener) {
        downloadInfo.setStatus(DownloadInfo.DownloadStatus.CANCELLED);
        mainHandler.post(() -> listener.onDownloadCancelled(downloadInfo));
    }
}
