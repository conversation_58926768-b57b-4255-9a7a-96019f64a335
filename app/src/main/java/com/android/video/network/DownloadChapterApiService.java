package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.MyListConstantsUtils;
import com.android.video.model.response.DownloadChapterResponseModel;
import com.android.video.model.response.DeleteChapterResponseModel;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * 下载章节详情API服务类
 * <p>
 * 提供下载章节详情相关的网络请求功能，包括获取特定短剧的章节下载详情等。
 * 使用OkHttp进行网络请求，支持异步回调和分页。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see MyListConstantsUtils
 */
public class DownloadChapterApiService {

    private static final String TAG = "DownloadChapterApiService";
    private static final String API_DOWNLOAD_CHAPTER_DETAILS = "/app/myList/downloadChapterDetails";

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;
    private final Gson gson;

    // 单例实例
    private static volatile DownloadChapterApiService instance;

    /**
     * 下载章节详情获取回调接口
     */
    public interface DownloadChapterCallback {
        /**
         * 获取下载章节详情成功
         * @param response 下载章节详情响应数据
         */
        void onSuccess(DownloadChapterResponseModel response);

        /**
         * 获取下载章节详情失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 删除章节回调接口
     */
    public interface DeleteChapterCallback {
        /**
         * 删除章节成功
         * @param response 删除章节响应数据
         */
        void onSuccess(DeleteChapterResponseModel response);

        /**
         * 删除章节失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 删除短剧回调接口
     */
    public interface DeleteFilmCallback {
        /**
         * 删除短剧成功
         * @param response 删除短剧响应数据
         */
        void onSuccess(DeleteChapterResponseModel response);

        /**
         * 删除短剧失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 私有构造方法
     */
    private DownloadChapterApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
        this.gson = new GsonBuilder()
                .setLenient()
                .create();
    }

    /**
     * 获取单例实例
     * @return DownloadChapterApiService实例
     */
    public static DownloadChapterApiService getInstance() {
        if (instance == null) {
            synchronized (DownloadChapterApiService.class) {
                if (instance == null) {
                    instance = new DownloadChapterApiService();
                }
            }
        }
        return instance;
    }

    /**
     * 获取下载章节详情
     * <p>
     * 异步获取特定短剧的下载章节详情，支持分页查询。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param pageNum 页码，从1开始
     * @param pageSize 每页数量
     * @param callback 回调接口
     */
    public void getDownloadChapterDetails(String filmLanguageInfoId, int pageNum, int pageSize,
                                        DownloadChapterCallback callback) {
        // 验证参数
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("filmLanguageInfoId不能为空");
            }
            return;
        }

        // 验证分页参数
        int validPageNum = MyListConstantsUtils.validatePageNum(pageNum);
        int validPageSize = MyListConstantsUtils.validatePageSize(pageSize);

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + API_DOWNLOAD_CHAPTER_DETAILS;

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append(MyListConstantsUtils.PARAM_PAGE_SIZE).append("=").append(validPageSize)
                  .append("&").append(MyListConstantsUtils.PARAM_PAGE_NUM).append("=").append(validPageNum)
                  .append("&").append("filmLanguageInfoId").append("=").append(filmLanguageInfoId.trim());

        String finalUrl = urlBuilder.toString();

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取下载章节详情请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "filmLanguageInfoId: " + filmLanguageInfoId);
        Log.d(TAG, "页码: " + validPageNum + ", 每页数量: " + validPageSize);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取下载章节详情请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 下载章节详情API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDownloadChapterResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "下载章节详情响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析下载章节详情响应数据
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDownloadChapterResponse(String responseBody, DownloadChapterCallback callback) {
        try {
            DownloadChapterResponseModel response = gson.fromJson(responseBody, DownloadChapterResponseModel.class);

            if (response != null) {
                Log.d(TAG, "下载章节详情解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                });
            } else {
                Log.e(TAG, "下载章节详情响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "下载章节详情JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 删除下载章节
     * <p>
     * 异步删除指定的下载章节记录。
     * </p>
     *
     * @param downloadRecordId 下载记录ID
     * @param callback 回调接口
     */
    public void deleteDownloadChapter(String downloadRecordId, DeleteChapterCallback callback) {
        // 验证参数
        if (downloadRecordId == null || downloadRecordId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("downloadRecordId不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + MyListConstantsUtils.API_DELETE_DOWNLOAD_CHAPTER;

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append(MyListConstantsUtils.PARAM_DOWNLOAD_RECORD_ID).append("=").append(downloadRecordId.trim());

        String finalUrl = urlBuilder.toString();

        // 构建DELETE请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .delete();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 删除下载章节请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: DELETE");
        Log.d(TAG, "downloadRecordId: " + downloadRecordId);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "删除下载章节请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 删除章节API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDeleteChapterResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "删除章节响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析删除章节响应数据
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDeleteChapterResponse(String responseBody, DeleteChapterCallback callback) {
        try {
            DeleteChapterResponseModel response = gson.fromJson(responseBody, DeleteChapterResponseModel.class);

            if (response != null) {
                Log.d(TAG, "删除章节解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                });
            } else {
                Log.e(TAG, "删除章节响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "删除章节JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 删除下载短剧
     * <p>
     * 异步删除指定的下载短剧及其所有章节。
     * </p>
     *
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param callback 回调接口
     */
    public void deleteDownloadFilm(String filmLanguageInfoId, DeleteFilmCallback callback) {
        // 验证参数
        if (filmLanguageInfoId == null || filmLanguageInfoId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("filmLanguageInfoId不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = EnvironmentConfigUtils.getApiBaseUrl() + MyListConstantsUtils.API_DELETE_DOWNLOAD_FILM;

        // 添加查询参数
        StringBuilder urlBuilder = new StringBuilder(apiUrl);
        urlBuilder.append("?")
                  .append(MyListConstantsUtils.PARAM_FILM_LANGUAGE_INFO_ID).append("=").append(filmLanguageInfoId.trim());

        String finalUrl = urlBuilder.toString();

        // 构建DELETE请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(finalUrl)
            .delete();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 删除下载短剧请求 ===");
        Log.d(TAG, "请求URL: " + finalUrl);
        Log.d(TAG, "请求方法: DELETE");
        Log.d(TAG, "filmLanguageInfoId: " + filmLanguageInfoId);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "删除下载短剧请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 删除短剧API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseDeleteFilmResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "删除短剧响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析删除短剧响应数据
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseDeleteFilmResponse(String responseBody, DeleteFilmCallback callback) {
        try {
            DeleteChapterResponseModel response = gson.fromJson(responseBody, DeleteChapterResponseModel.class);

            if (response != null) {
                Log.d(TAG, "删除短剧解析成功: " + response.toString());

                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess(response);
                    }
                });
            } else {
                Log.e(TAG, "删除短剧响应解析为null");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("数据解析失败");
                    }
                });
            }
        } catch (Exception e) {
            Log.e(TAG, "删除短剧JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }
}
