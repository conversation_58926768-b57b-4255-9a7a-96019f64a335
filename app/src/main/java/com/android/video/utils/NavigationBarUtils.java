package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.view.View;
import androidx.core.graphics.Insets;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

/**
 * 导航栏工具类 - 提供导航栏检测和适配相关的实用方法
 * 支持检测导航栏高度、类型（手势/按钮）以及提供适配建议
 * 
 * <AUTHOR> Team
 */
public class NavigationBarUtils {

    /**
     * 导航栏信息数据类
     */
    public static class NavigationBarInfo {
        public final int height;
        public final boolean exists;
        public final boolean isGestureNavigation;
        public final NavigationType type;
        
        public NavigationBarInfo(int height, boolean exists, boolean isGestureNavigation, NavigationType type) {
            this.height = height;
            this.exists = exists;
            this.isGestureNavigation = isGestureNavigation;
            this.type = type;
        }
        
        @Override
        public String toString() {
            return "NavigationBarInfo{" +
                    "height=" + height +
                    ", exists=" + exists +
                    ", isGestureNavigation=" + isGestureNavigation +
                    ", type=" + type +
                    '}';
        }
    }
    
    /**
     * 导航栏类型枚举
     */
    public enum NavigationType {
        NONE("无导航栏"),
        GESTURE("手势导航"),
        BUTTON("按钮导航"),
        UNKNOWN("未知类型");
        
        private final String description;
        
        NavigationType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取导航栏完整信息
     * @param activity Activity实例
     * @return NavigationBarInfo对象，包含导航栏的所有信息
     */
    public static NavigationBarInfo getNavigationBarInfo(Activity activity) {
        if (activity == null) {
            return new NavigationBarInfo(0, false, false, NavigationType.NONE);
        }
        
        View rootView = activity.findViewById(android.R.id.content);
        if (rootView == null) {
            return new NavigationBarInfo(0, false, false, NavigationType.NONE);
        }
        
        WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
        if (insets == null) {
            return new NavigationBarInfo(0, false, false, NavigationType.NONE);
        }
        
        Insets navBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars());
        Insets tappable = insets.getInsets(WindowInsetsCompat.Type.tappableElement());
        
        int height = navBars.bottom;
        boolean exists = height > 0;
        boolean isGesture = exists && height > tappable.bottom;
        
        NavigationType type;
        if (!exists) {
            type = NavigationType.NONE;
        } else if (isGesture) {
            type = NavigationType.GESTURE;
        } else {
            type = NavigationType.BUTTON;
        }
        
        return new NavigationBarInfo(height, exists, isGesture, type);
    }

    /**
     * 检测是否需要为导航栏调整布局
     * @param activity Activity实例
     * @return true表示需要调整，false表示不需要
     */
    public static boolean shouldAdjustForNavigationBar(Activity activity) {
        NavigationBarInfo info = getNavigationBarInfo(activity);
        // 只有在存在导航栏且为按钮导航时才需要调整
        // 手势导航通常是悬浮的，不需要特殊处理
        return info.exists && !info.isGestureNavigation;
    }

    /**
     * 获取建议的底部内边距
     * @param activity Activity实例
     * @return 建议的底部内边距值（像素）
     */
    public static int getRecommendedBottomPadding(Activity activity) {
        NavigationBarInfo info = getNavigationBarInfo(activity);
        
        if (!info.exists) {
            return 0; // 无导航栏，不需要内边距
        }
        
        if (info.isGestureNavigation) {
            // 手势导航通常是悬浮的，只需要很小的内边距避免手势冲突
            return Math.min(info.height / 3, 16); // 最多16dp
        } else {
            // 按钮导航需要完整的导航栏高度作为内边距
            return info.height;
        }
    }

    /**
     * 调试导航栏配置（仅在DEBUG模式下使用）
     * @param activity Activity实例
     */
    public static void debugNavigationBarConfig(Activity activity) {
        NavigationBarInfo info = getNavigationBarInfo(activity);
        
        android.util.Log.d("NavigationBarUtils", "=== 导航栏配置调试信息 ===");
        android.util.Log.d("NavigationBarUtils", "导航栏信息: " + info.toString());
        android.util.Log.d("NavigationBarUtils", "是否需要调整布局: " + shouldAdjustForNavigationBar(activity));
        android.util.Log.d("NavigationBarUtils", "建议底部内边距: " + getRecommendedBottomPadding(activity) + "px");
        
        if (activity != null) {
            View rootView = activity.findViewById(android.R.id.content);
            if (rootView != null) {
                WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(rootView);
                if (insets != null) {
                    Insets navBars = insets.getInsets(WindowInsetsCompat.Type.navigationBars());
                    Insets tappable = insets.getInsets(WindowInsetsCompat.Type.tappableElement());
                    Insets systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars());
                    
                    android.util.Log.d("NavigationBarUtils", "navigationBars insets: " + navBars.toString());
                    android.util.Log.d("NavigationBarUtils", "tappableElement insets: " + tappable.toString());
                    android.util.Log.d("NavigationBarUtils", "systemBars insets: " + systemBars.toString());
                }
            }
        }
        
        android.util.Log.d("NavigationBarUtils", "=== 调试信息结束 ===");
    }

    /**
     * 导航栏适配验证结果
     */
    public static class NavigationBarValidationResult {
        public final boolean isValid;
        public final String message;
        public final NavigationBarInfo info;
        
        public NavigationBarValidationResult(boolean isValid, String message, NavigationBarInfo info) {
            this.isValid = isValid;
            this.message = message;
            this.info = info;
        }
        
        @Override
        public String toString() {
            return "NavigationBarValidationResult{" +
                    "isValid=" + isValid +
                    ", message='" + message + '\'' +
                    ", info=" + info +
                    '}';
        }
    }

    /**
     * 验证导航栏适配是否正确
     * @param activity Activity实例
     * @return 验证结果
     */
    public static NavigationBarValidationResult validateNavigationBarAdaptation(Activity activity) {
        NavigationBarInfo info = getNavigationBarInfo(activity);

        if (!info.exists) {
            return new NavigationBarValidationResult(true, "设备无导航栏，无需适配", info);
        }

        if (info.isGestureNavigation) {
            return new NavigationBarValidationResult(true,
                "手势导航模式，导航栏为悬浮式，通常无需特殊适配", info);
        }

        // 按钮导航需要检查是否有适当的适配
        // 对于MainActivity，需要检查底部导航栏是否已经正确应用了内边距
        if (activity instanceof com.android.video.ui.activity.MainActivity) {
            // 检查MainActivity的底部导航栏是否已经应用了正确的margin
            return validateMainActivityBottomNavigation((com.android.video.ui.activity.MainActivity) activity, info);
        }

        // 对于其他Activity，使用原有逻辑
        boolean needsAdjustment = shouldAdjustForNavigationBar(activity);
        if (needsAdjustment) {
            return new NavigationBarValidationResult(false,
                "检测到按钮导航栏，建议为底部内容添加 " + info.height + "px 的内边距", info);
        }

        return new NavigationBarValidationResult(true, "导航栏适配正确", info);
    }

    /**
     * 验证MainActivity的底部导航栏适配
     * @param mainActivity MainActivity实例
     * @param info 导航栏信息
     * @return 验证结果
     */
    private static NavigationBarValidationResult validateMainActivityBottomNavigation(
            com.android.video.ui.activity.MainActivity mainActivity, NavigationBarInfo info) {
        try {
            // 通过反射获取bottomNavigationView
            java.lang.reflect.Field field = mainActivity.getClass().getDeclaredField("bottomNavigationView");
            field.setAccessible(true);
            com.google.android.material.bottomnavigation.BottomNavigationView bottomNav =
                (com.google.android.material.bottomnavigation.BottomNavigationView) field.get(mainActivity);

            if (bottomNav != null) {
                android.view.ViewGroup.LayoutParams layoutParams = bottomNav.getLayoutParams();
                if (layoutParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
                    androidx.constraintlayout.widget.ConstraintLayout.LayoutParams constraintParams =
                        (androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) layoutParams;

                    int currentMargin = constraintParams.bottomMargin;
                    int expectedMargin = info.height;

                    // 检查margin是否正确设置（允许一定的误差）
                    if (Math.abs(currentMargin - expectedMargin) <= 10) {
                        return new NavigationBarValidationResult(true,
                            "按钮导航栏适配正确，当前底部内边距: " + currentMargin + "px，期望: " + expectedMargin + "px", info);
                    } else {
                        return new NavigationBarValidationResult(false,
                            "按钮导航栏适配不正确，当前底部内边距: " + currentMargin + "px，期望: " + expectedMargin + "px", info);
                    }
                }
            }
        } catch (Exception e) {
            android.util.Log.e("NavigationBarUtils", "验证MainActivity底部导航栏适配时出错", e);
        }

        // 如果无法检查，返回需要适配的提示
        return new NavigationBarValidationResult(false,
            "无法验证MainActivity底部导航栏适配状态，建议检查底部内边距是否为 " + info.height + "px", info);
    }
}
