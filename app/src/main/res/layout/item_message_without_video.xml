<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="@dimen/message_card_without_video_height"
    android:layout_gravity="center_horizontal"
    android:layout_marginBottom="@dimen/message_card_margin_bottom"
    android:background="@drawable/message_card_bg"
    android:orientation="vertical"
    android:padding="@dimen/message_card_padding">

    <!-- 上半部分：通知图标、标题和日期 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 通知图标容器 -->
        <RelativeLayout
            android:layout_width="@dimen/message_notification_icon_size"
            android:layout_height="@dimen/message_notification_icon_size"
            android:layout_marginEnd="@dimen/message_notification_icon_margin_end">

            <!-- 通知图标 -->
            <ImageView
                android:id="@+id/iv_notification_icon"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/message_notification_icon_bg"
                android:src="@drawable/mess_ic_notice"
                android:scaleType="center" />

            <!-- 未读红点 -->
            <View
                android:id="@+id/message_unread_dot"
                android:layout_width="@dimen/message_unread_dot_size"
                android:layout_height="@dimen/message_unread_dot_size"
                android:layout_alignParentEnd="true"
                android:layout_alignParentTop="true"
                android:layout_marginEnd="2dp"
                android:layout_marginTop="2dp"
                android:background="@drawable/message_notification_dot"
                android:visibility="gone" />

        </RelativeLayout>

        <!-- 标题和日期 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 标题 -->
            <TextView
                android:id="@+id/tv_title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Message Notification"
                android:textColor="#ffffffff"
                android:textSize="@dimen/message_title_text_size"
                android:fontFamily="sans-serif" />

            <!-- 日期时间 -->
            <TextView
                android:id="@+id/tv_date_time"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                android:text="06/18/2025 10:03AM"
                android:textColor="#80ffffff"
                android:textSize="@dimen/message_date_text_size"
                android:fontFamily="sans-serif" />

        </LinearLayout>

    </LinearLayout>

    <!-- 下半部分：内容描述和Go按钮 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="12dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <!-- 内容描述 -->
        <TextView
            android:id="@+id/tv_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:text="This is a message title"
            android:textColor="#ffffffff"
            android:textSize="@dimen/message_content_text_size"
            android:fontFamily="sans-serif"
            android:gravity="top" />



    </LinearLayout>

</LinearLayout>
