package com.android.video.model;

import com.google.gson.annotations.SerializedName;

/**
 * 导演信息数据模型
 * <p>
 * 用于映射API返回的导演信息JSON数据结构。
 * 包含导演的姓名和头像信息。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class DirectorInfo {
    
    /**
     * 导演名称
     */
    @SerializedName("directorName")
    private String directorName;
    
    /**
     * 导演头像URL
     */
    @SerializedName("directorImg")
    private String directorImg;

    /**
     * 默认构造函数
     */
    public DirectorInfo() {
    }

    /**
     * 完整构造函数
     * 
     * @param directorName 导演名称
     * @param directorImg 导演头像URL
     */
    public DirectorInfo(String directorName, String directorImg) {
        this.directorName = directorName;
        this.directorImg = directorImg;
    }

    // ========== Getter和Setter方法 ==========

    public String getDirectorName() {
        return directorName;
    }

    public void setDirectorName(String directorName) {
        this.directorName = directorName;
    }

    public String getDirectorImg() {
        return directorImg;
    }

    public void setDirectorImg(String directorImg) {
        this.directorImg = directorImg;
    }

    @Override
    public String toString() {
        return "DirectorInfo{" +
                "directorName='" + directorName + '\'' +
                ", directorImg='" + directorImg + '\'' +
                '}';
    }
}
