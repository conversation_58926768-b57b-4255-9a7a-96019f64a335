package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.VipApiConstantsUtils;
import com.android.video.model.response.VipConfigListResponseModel;
import com.android.video.model.response.RedeemCodeResponseModel;
import com.android.video.model.VipPurchaseResponse;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.json.JSONObject;

/**
 * VIP配置API服务类
 * <p>
 * 提供VIP配置相关的网络请求服务，包括获取VIP配置列表等功能。
 * 使用OkHttp进行网络请求，Gson进行JSON解析，Handler进行主线程回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VipConfigApiService {

    private static final String TAG = "VipConfigApiService";

    /**
     * OkHttp客户端实例
     */
    private final OkHttpClient client;

    /**
     * Gson实例，用于JSON解析
     */
    private final Gson gson;

    /**
     * 主线程Handler，用于回调
     */
    private final Handler mainHandler;

    /**
     * 线程池，用于执行网络请求
     */
    private final ExecutorService executorService;

    /**
     * VIP配置列表回调接口
     */
    public interface VipConfigListCallback {
        /**
         * 请求成功回调
         *
         * @param response VIP配置列表响应数据
         */
        void onSuccess(VipConfigListResponseModel response);

        /**
         * 请求失败回调
         *
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * VIP兑换码兑换回调接口
     */
    public interface RedeemCodeCallback {
        /**
         * 兑换成功回调
         *
         * @param response 兑换响应数据
         */
        void onSuccess(RedeemCodeResponseModel response);

        /**
         * 兑换失败回调
         *
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * VIP购买回调接口
     */
    public interface VipPurchaseCallback {
        /**
         * 购买成功回调
         *
         * @param response VIP购买响应数据
         */
        void onSuccess(VipPurchaseResponse response);

        /**
         * 购买失败回调
         *
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 构造函数
     */
    public VipConfigApiService() {
        // 使用统一的HTTP客户端，确保包含HeaderInterceptor
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
            Log.d(TAG, "Using ApiClientUtils HTTP client with HeaderInterceptor");
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，创建包含HeaderInterceptor的客户端
            Log.w(TAG, "ApiClientUtils not initialized, creating client with HeaderInterceptor");
            httpClient = createClientWithHeaderInterceptor();
        }
        this.client = httpClient;
        this.gson = new GsonBuilder()
                .setLenient()
                .create();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executorService = Executors.newCachedThreadPool();
    }

    /**
     * 创建包含HeaderInterceptor的HTTP客户端
     * 用于ApiClientUtils未初始化时的降级处理
     */
    private OkHttpClient createClientWithHeaderInterceptor() {
        return new OkHttpClient.Builder()
                .addInterceptor(chain -> {
                    Request originalRequest = chain.request();
                    Request.Builder requestBuilder = originalRequest.newBuilder()
                            .header("Content-Type", "application/json")
                            .header("User-Agent", "VideoPlayer/1.0");

                    // 自动添加访问令牌
                    String token = ApiHeaderUtils.getCurrentAccessToken();
                    if (token != null && !token.trim().isEmpty()) {
                        requestBuilder.header("X-Access-Token", token);
                        Log.d(TAG, "Added token to VIP request: " + maskToken(token));
                    } else {
                        Log.w(TAG, "No token available for VIP request: " + originalRequest.url());
                    }

                    return chain.proceed(requestBuilder.build());
                })
                .build();
    }

    /**
     * 脱敏处理token用于日志输出
     */
    private String maskToken(String token) {
        if (token == null || token.length() <= 8) {
            return "***";
        }
        return token.substring(0, 4) + "***" + token.substring(token.length() - 4);
    }

    /**
     * 获取VIP配置列表
     * <p>
     * 异步获取VIP配置列表，包含所有可用的VIP套餐配置信息。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getVipConfigList(VipConfigListCallback callback) {
        if (callback == null) {
            Log.w(TAG, "getVipConfigList: callback is null");
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            VipApiConstantsUtils.API_GET_VIP_CONFIG_LIST
        );

        Log.d(TAG, "getVipConfigList: requesting URL: " + apiUrl);

        // 构建请求（token由HeaderInterceptor自动添加）
        Request.Builder requestBuilder = new Request.Builder()
                .url(apiUrl)
                .get();

        Request request = requestBuilder.build();

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "getVipConfigList: network request failed", e);
                String errorMessage = "Network request failed: " + e.getMessage();
                
                // 在主线程回调
                mainHandler.post(() -> callback.onError(errorMessage));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (!response.isSuccessful()) {
                        String errorMessage = "HTTP error: " + response.code() + " " + response.message();
                        Log.e(TAG, "getVipConfigList: " + errorMessage);
                        
                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                        return;
                    }

                    String responseBody = response.body().string();
                    Log.d(TAG, "getVipConfigList: response body: " + responseBody);

                    // 解析JSON响应
                    VipConfigListResponseModel responseModel = gson.fromJson(responseBody, VipConfigListResponseModel.class);

                    if (responseModel == null) {
                        String errorMessage = "Failed to parse response data";
                        Log.e(TAG, "getVipConfigList: " + errorMessage);
                        
                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                        return;
                    }

                    // 检查响应码
                    if (!BaseApiConstantsUtils.isSuccessCode(responseModel.getCode())) {
                        String errorMessage = "API error: " + responseModel.getCode() + " " + responseModel.getMessage();
                        Log.e(TAG, "getVipConfigList: " + errorMessage);
                        
                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                        return;
                    }

                    Log.d(TAG, "getVipConfigList: success, got " + responseModel.getConfigCount() + " configs");

                    // 在主线程回调成功结果
                    mainHandler.post(() -> callback.onSuccess(responseModel));

                } catch (Exception e) {
                    Log.e(TAG, "getVipConfigList: error processing response", e);
                    String errorMessage = "Error processing response: " + e.getMessage();
                    
                    // 在主线程回调
                    mainHandler.post(() -> callback.onError(errorMessage));
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * VIP兑换码兑换
     * <p>
     * 异步兑换VIP兑换码，获取VIP会员权益。
     * </p>
     *
     * @param code 兑换码
     * @param callback 回调接口
     */
    public void redeemCode(String code, RedeemCodeCallback callback) {
        if (callback == null) {
            Log.w(TAG, "redeemCode: callback is null");
            return;
        }

        if (code == null || code.trim().isEmpty()) {
            Log.w(TAG, "redeemCode: code is null or empty");
            callback.onError("Redemption code cannot be empty");
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            VipApiConstantsUtils.API_REDEEM_CODE_EXCHANGE
        );

        // 添加查询参数
        apiUrl += "?" + VipApiConstantsUtils.PARAM_CODE + "=" + code.trim();

        Log.d(TAG, "redeemCode: requesting URL: " + apiUrl);

        // 构建GET请求（token由HeaderInterceptor自动添加）
        Request.Builder requestBuilder = new Request.Builder()
                .url(apiUrl)
                .get();

        Request request = requestBuilder.build();

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "redeemCode: network request failed", e);
                String errorMessage = "Network request failed: " + e.getMessage();

                // 在主线程回调
                mainHandler.post(() -> callback.onError(errorMessage));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (!response.isSuccessful()) {
                        String errorMessage = "HTTP error: " + response.code() + " " + response.message();
                        Log.e(TAG, "redeemCode: " + errorMessage);

                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                        return;
                    }

                    String responseBody = response.body().string();
                    Log.d(TAG, "redeemCode: response body: " + responseBody);

                    // 解析JSON响应
                    RedeemCodeResponseModel responseModel = gson.fromJson(responseBody, RedeemCodeResponseModel.class);

                    if (responseModel == null) {
                        String errorMessage = "Failed to parse response data";
                        Log.e(TAG, "redeemCode: " + errorMessage);

                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                        return;
                    }

                    Log.d(TAG, "redeemCode: response parsed, code: " + responseModel.getCode() + ", message: " + responseModel.getMessage());

                    // 在主线程回调结果（无论成功还是失败都通过onSuccess返回，让调用方根据响应码判断）
                    mainHandler.post(() -> callback.onSuccess(responseModel));

                } catch (Exception e) {
                    Log.e(TAG, "redeemCode: error processing response", e);
                    String errorMessage = "Error processing response: " + e.getMessage();

                    // 在主线程回调
                    mainHandler.post(() -> callback.onError(errorMessage));
                } finally {
                    response.close();
                }
            }
        });
    }

    /**
     * VIP购买
     * <p>
     * 异步调用VIP购买API，获取支付参数。
     * </p>
     *
     * @param vipId VIP配置ID
     * @param payAmount 支付金额
     * @param payType 支付类型（google, apple等）
     * @param callback 回调接口
     */
    public void purchaseVip(String vipId, double payAmount, String payType, VipPurchaseCallback callback) {
        if (callback == null) {
            Log.w(TAG, "purchaseVip: callback is null");
            return;
        }

        if (vipId == null || vipId.trim().isEmpty()) {
            Log.w(TAG, "purchaseVip: vipId is null or empty");
            callback.onError("VIP ID cannot be empty");
            return;
        }

        if (payAmount <= 0) {
            Log.w(TAG, "purchaseVip: invalid payAmount: " + payAmount);
            callback.onError("Invalid payment amount");
            return;
        }

        if (payType == null || payType.trim().isEmpty()) {
            Log.w(TAG, "purchaseVip: payType is null or empty");
            callback.onError("Payment type cannot be empty");
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            VipApiConstantsUtils.API_VIP_BUY
        );

        Log.d(TAG, "purchaseVip: requesting URL: " + apiUrl);
        Log.d(TAG, "purchaseVip: vipId=" + vipId + ", payAmount=" + payAmount + ", payType=" + payType);

        try {
            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put(VipApiConstantsUtils.PARAM_VIP_ID, vipId);
            requestBody.put(VipApiConstantsUtils.PARAM_PAY_AMOUNT, payAmount);
            requestBody.put(VipApiConstantsUtils.PARAM_SYSTEM_TYPE, VipApiConstantsUtils.SYSTEM_TYPE_ANDROID);
            requestBody.put(VipApiConstantsUtils.PARAM_PAY_TYPE, payType);

            RequestBody body = RequestBody.create(
                MediaType.parse("application/json; charset=utf-8"),
                requestBody.toString()
            );

            // 构建POST请求（token由HeaderInterceptor自动添加）
            Request.Builder requestBuilder = new Request.Builder()
                    .url(apiUrl)
                    .post(body);

            Request request = requestBuilder.build();

            // 异步执行请求
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "purchaseVip: network request failed", e);
                    String errorMessage = "Network request failed: " + e.getMessage();

                    // 在主线程回调
                    mainHandler.post(() -> callback.onError(errorMessage));
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        if (!response.isSuccessful()) {
                            String errorMessage = "HTTP error: " + response.code() + " " + response.message();
                            Log.e(TAG, "purchaseVip: " + errorMessage);

                            // 在主线程回调
                            mainHandler.post(() -> callback.onError(errorMessage));
                            return;
                        }

                        String responseBody = response.body().string();
                        Log.d(TAG, "purchaseVip: response received: " + responseBody);

                        // 解析响应
                        VipPurchaseResponse purchaseResponse = gson.fromJson(responseBody, VipPurchaseResponse.class);

                        // 在主线程回调
                        mainHandler.post(() -> {
                            if (purchaseResponse.isSuccess()) {
                                Log.d(TAG, "purchaseVip: success - orderNo: " + purchaseResponse.getOrderNo());
                                callback.onSuccess(purchaseResponse);
                            } else {
                                String errorMessage = purchaseResponse.getErrorMessage();
                                Log.e(TAG, "purchaseVip: API error: " + errorMessage);
                                callback.onError(errorMessage);
                            }
                        });

                    } catch (Exception e) {
                        Log.e(TAG, "purchaseVip: error parsing response", e);
                        String errorMessage = "Error parsing response: " + e.getMessage();

                        // 在主线程回调
                        mainHandler.post(() -> callback.onError(errorMessage));
                    }
                }
            });

        } catch (Exception e) {
            Log.e(TAG, "purchaseVip: error creating request", e);
            callback.onError("Error creating request: " + e.getMessage());
        }
    }

    /**
     * 取消所有正在进行的请求
     */
    public void cancelAllRequests() {
        try {
            client.dispatcher().cancelAll();
            Log.d(TAG, "cancelAllRequests: all requests cancelled");
        } catch (Exception e) {
            Log.e(TAG, "cancelAllRequests: error cancelling requests", e);
        }
    }

    /**
     * 关闭服务，释放资源
     */
    public void shutdown() {
        try {
            cancelAllRequests();
            executorService.shutdown();
            Log.d(TAG, "shutdown: service shutdown completed");
        } catch (Exception e) {
            Log.e(TAG, "shutdown: error during shutdown", e);
        }
    }
}
