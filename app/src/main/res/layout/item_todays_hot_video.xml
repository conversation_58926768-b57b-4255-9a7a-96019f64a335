<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/home_video_item_spacing_horizontal"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_container"
        android:layout_width="@dimen/home_continue_watching_item_width"
        android:layout_height="@dimen/home_continue_watching_item_height"
        android:background="@drawable/todays_hot_item_border"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 海报背景 -->
        <ImageView
            android:id="@+id/iv_video_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/home_continue_watching_item_border_width"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/todays_hot_inner_border"
            android:clipToOutline="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />



        <!-- 排名标签 -->
        <TextView
            android:id="@+id/tv_ranking"
            android:layout_width="@dimen/home_ranking_width"
            android:layout_height="@dimen/home_ranking_height"
            android:layout_marginStart="@dimen/home_ranking_margin_start"
            android:layout_marginTop="@dimen/home_ranking_margin_top"
            android:background="@drawable/home_ranking_background_new"
            android:text="1"
            android:textColor="@color/white"
            android:textSize="@dimen/home_ranking_text_size"
            android:fontFamily="sans-serif-black"
            android:gravity="center"
            app:layout_constraintStart_toStartOf="@id/iv_video_poster"
            app:layout_constraintTop_toTopOf="@id/iv_video_poster" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 视频标题 -->
    <TextView
        android:id="@+id/tv_video_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/home_continue_watching_title_margin_top"
        android:text="喜洋洋与灰太狼"
        android:textColor="@color/white"
        android:textSize="@dimen/home_video_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/cl_poster_container"
        app:layout_constraintStart_toStartOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="@id/cl_poster_container" />

</androidx.constraintlayout.widget.ConstraintLayout>
