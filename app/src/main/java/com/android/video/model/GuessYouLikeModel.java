package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.HomeApiConstantsUtils;

/**
 * 猜你喜欢推荐位数据模型
 * <p>
 * 用于映射猜你喜欢推荐位API返回的数据结构。
 * 对应接口：{@link HomeApiConstantsUtils#API_GET_GUESS_YOU_LIKE}
 * </p>
 * 
 * <p>
 * 数据结构说明：
 * <ul>
 *   <li>filmInfo: 短剧信息对象</li>
 *   <li>matchPercentage: 匹配度分值</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 解析API响应
 * List&lt;GuessYouLikeModel&gt; guessYouLikeList = gson.fromJson(jsonArray,
 *     new TypeToken&lt;List&lt;GuessYouLikeModel&gt;&gt;(){}.getType());
 *
 * // 获取猜你喜欢数据
 * for (GuessYouLikeModel item : guessYouLikeList) {
 *     FilmInfoModel filmInfo = item.getFilmInfo();
 *     int matchPercentage = item.getMatchPercentage();
 * }
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see HomeApiConstantsUtils#API_GET_GUESS_YOU_LIKE
 * @see FilmInfoModel
 */
public class GuessYouLikeModel {
    
    /**
     * 短剧信息
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_FILM_INFO)
    private FilmInfoModel filmInfo;
    
    /**
     * 匹配度分值
     */
    @SerializedName(HomeApiConstantsUtils.FIELD_MATCH_PERCENTAGE)
    private Integer matchPercentage;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public GuessYouLikeModel() {
    }

    /**
     * 完整构造函数
     */
    public GuessYouLikeModel(FilmInfoModel filmInfo, Integer matchPercentage) {
        this.filmInfo = filmInfo;
        this.matchPercentage = matchPercentage;
    }

    // ========== Getter和Setter方法 ==========

    public FilmInfoModel getFilmInfo() {
        return filmInfo;
    }

    public void setFilmInfo(FilmInfoModel filmInfo) {
        this.filmInfo = filmInfo;
    }

    public Integer getMatchPercentage() {
        return matchPercentage;
    }

    public void setMatchPercentage(Integer matchPercentage) {
        this.matchPercentage = matchPercentage;
    }

    // ========== 工具方法 ==========

    /**
     * 检查是否有有效的短剧信息
     * @return 如果有有效短剧信息则返回true
     */
    public boolean hasValidFilmInfo() {
        return filmInfo != null && filmInfo.getFilmId() != null && !filmInfo.getFilmId().trim().isEmpty();
    }

    /**
     * 获取匹配度百分比
     * @return 匹配度百分比，如果为null则返回0
     */
    public int getMatchPercentageValue() {
        return matchPercentage != null ? matchPercentage : 0;
    }

    /**
     * 转换为VideoModel对象
     * @return VideoModel对象
     */
    public VideoModel toVideoModel() {
        if (!hasValidFilmInfo()) {
            return null;
        }

        VideoModel videoModel = new VideoModel();
        videoModel.setId(filmInfo.getFilmId());
        videoModel.setTitle(filmInfo.getFilmTitle());
        videoModel.setPosterUrl(filmInfo.getCover());
        videoModel.setDescription(filmInfo.getDetails());
        videoModel.setMatchPercentage(getMatchPercentageValue());
        videoModel.setLiked(filmInfo.isLoved());
        
        // 设置播放次数作为热度值
        if (filmInfo.getPlayNum() != null) {
            videoModel.setViewCount(filmInfo.getPlayNum());
        }
        
        // 设置总章节数
        if (filmInfo.getTotalChaptersNum() != null) {
            videoModel.setTotalEpisodes(filmInfo.getTotalChaptersNum());
        }
        
        // 添加分类标签
        if (filmInfo.getCategoryName() != null && !filmInfo.getCategoryName().trim().isEmpty()) {
            String[] categories = filmInfo.getCategoryName().split(",");
            for (String category : categories) {
                String trimmedCategory = category.trim();
                if (!trimmedCategory.isEmpty()) {
                    videoModel.addTag(trimmedCategory);
                }
            }
        }
        
        // 添加语言标签
        if (filmInfo.getLanguageType() != null) {
            videoModel.addTag(filmInfo.getLanguageTypeText());
        }
        
        // 存储原始数据用于跳转
        videoModel.addTag("film_id:" + filmInfo.getFilmId());
        if (filmInfo.getFilmLanguageInfoId() != null) {
            videoModel.addTag("film_language_info_id:" + filmInfo.getFilmLanguageInfoId());
        }
        if (filmInfo.getCategoryId() != null) {
            videoModel.addTag("category_id:" + filmInfo.getCategoryId());
        }
        
        return videoModel;
    }

    @Override
    public String toString() {
        return "GuessYouLikeModel{" +
                "filmInfo=" + filmInfo +
                ", matchPercentage=" + matchPercentage +
                '}';
    }

    /**
     * 短剧信息内部类
     */
    public static class FilmInfoModel {
        
        /**
         * 短剧标题
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_FILM_TITLE)
        private String filmTitle;
        
        /**
         * 分类ID
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_ID)
        private String categoryId;
        
        /**
         * 分类名称
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_CATEGORY_NAME)
        private String categoryName;
        
        /**
         * 封面图片URL
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_COVER)
        private String cover;
        
        /**
         * 语言类型
         * 1=英语, 2=俄语
         */
        @SerializedName("languageType")
        private Integer languageType;
        
        /**
         * 短剧ID
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_FILM_ID)
        private String filmId;
        
        /**
         * 短剧语言信息ID
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_FILM_LANGUAGE_INFO_ID)
        private String filmLanguageInfoId;
        
        /**
         * 搜索次数
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_SEARCH_NUM)
        private Long searchNum;
        
        /**
         * 短剧简介
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_DETAILS)
        private String details;
        
        /**
         * 是否喜欢
         * 0=未加入, 1=已加入
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_IS_LOVE)
        private Integer isLove;
        
        /**
         * 播放次数
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_PLAY_NUM)
        private Long playNum;
        
        /**
         * 总章节数
         */
        @SerializedName(HomeApiConstantsUtils.FIELD_TOTAL_CHAPTERS_NUM)
        private Integer totalChaptersNum;

        // ========== 构造函数 ==========

        public FilmInfoModel() {
        }

        // ========== Getter和Setter方法 ==========

        public String getFilmTitle() {
            return filmTitle;
        }

        public void setFilmTitle(String filmTitle) {
            this.filmTitle = filmTitle;
        }

        public String getCategoryId() {
            return categoryId;
        }

        public void setCategoryId(String categoryId) {
            this.categoryId = categoryId;
        }

        public String getCategoryName() {
            return categoryName;
        }

        public void setCategoryName(String categoryName) {
            this.categoryName = categoryName;
        }

        public String getCover() {
            return cover;
        }

        public void setCover(String cover) {
            this.cover = cover;
        }

        public Integer getLanguageType() {
            return languageType;
        }

        public void setLanguageType(Integer languageType) {
            this.languageType = languageType;
        }

        public String getFilmId() {
            return filmId;
        }

        public void setFilmId(String filmId) {
            this.filmId = filmId;
        }

        public String getFilmLanguageInfoId() {
            return filmLanguageInfoId;
        }

        public void setFilmLanguageInfoId(String filmLanguageInfoId) {
            this.filmLanguageInfoId = filmLanguageInfoId;
        }

        public Long getSearchNum() {
            return searchNum;
        }

        public void setSearchNum(Long searchNum) {
            this.searchNum = searchNum;
        }

        public String getDetails() {
            return details;
        }

        public void setDetails(String details) {
            this.details = details;
        }

        public Integer getIsLove() {
            return isLove;
        }

        public void setIsLove(Integer isLove) {
            this.isLove = isLove;
        }

        public Long getPlayNum() {
            return playNum;
        }

        public void setPlayNum(Long playNum) {
            this.playNum = playNum;
        }

        public Integer getTotalChaptersNum() {
            return totalChaptersNum;
        }

        public void setTotalChaptersNum(Integer totalChaptersNum) {
            this.totalChaptersNum = totalChaptersNum;
        }

        // ========== 工具方法 ==========

        /**
         * 获取语言类型的文本描述
         * @return 语言类型描述
         */
        public String getLanguageTypeText() {
            if (languageType == null) {
                return "未知语言";
            }
            return HomeApiConstantsUtils.getLanguageTypeDescription(languageType);
        }

        /**
         * 检查是否已喜欢
         * @return 如果已喜欢则返回true
         */
        public boolean isLoved() {
            return isLove != null && isLove == 1;
        }

        /**
         * 检查是否有有效的封面图片
         * @return 如果有有效封面则返回true
         */
        public boolean hasCover() {
            return cover != null && !cover.trim().isEmpty() && !cover.equals("null");
        }

        @Override
        public String toString() {
            return "FilmInfoModel{" +
                    "filmTitle='" + filmTitle + '\'' +
                    ", categoryId='" + categoryId + '\'' +
                    ", categoryName='" + categoryName + '\'' +
                    ", cover='" + cover + '\'' +
                    ", languageType=" + languageType +
                    ", filmId='" + filmId + '\'' +
                    ", filmLanguageInfoId='" + filmLanguageInfoId + '\'' +
                    ", searchNum=" + searchNum +
                    ", details='" + details + '\'' +
                    ", isLove=" + isLove +
                    ", playNum=" + playNum +
                    ", totalChaptersNum=" + totalChaptersNum +
                    '}';
        }
    }
}
