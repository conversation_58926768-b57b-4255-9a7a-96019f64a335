package com.android.video.service;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.app.Service;
import android.content.Context;
import android.content.Intent;
import android.graphics.PixelFormat;
import android.os.Build;
import android.os.IBinder;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.View;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;

import androidx.annotation.Nullable;
import androidx.core.app.NotificationCompat;

import com.android.video.R;
import com.android.video.manager.FloatingVideoManager;
import com.android.video.model.VideoModel;
import com.android.video.player.VideoPlayerManager;
import com.android.video.player.VideoPlayerListener;
import com.android.video.ui.activity.MainActivity;
import com.google.android.exoplayer2.ui.PlayerView;

/**
 * 悬浮窗视频播放服务
 * 
 * 提供全局悬浮窗视频播放功能，支持拖拽和基本播放控制。
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class FloatingVideoService extends Service implements VideoPlayerListener {
    
    private static final String TAG = "FloatingVideoService";
    private static final String CHANNEL_ID = "floating_video_channel";
    private static final int NOTIFICATION_ID = 1001;
    
    // Intent extras
    public static final String EXTRA_VIDEO_MODEL = "extra_video_model";
    public static final String EXTRA_SOURCE_ACTIVITY = "extra_source_activity";
    
    // 源Activity类型
    public static final String SOURCE_VIDEO_PLAYER = "video_player";
    public static final String SOURCE_DISCOVER = "discover";
    public static final String SOURCE_DOWNLOAD_PLAYER = "download_player";
    
    private WindowManager windowManager;
    private View floatingView;
    private WindowManager.LayoutParams layoutParams;
    
    // UI组件
    private PlayerView playerView;
    private ProgressBar loadingIndicator;
    private ImageView playPauseOverlay;
    private ImageView btnPlayPause;
    private ImageView btnReturnApp;
    private ImageView btnClose;
    private View dragHandle;
    private LinearLayout controlButtons;
    
    // 播放器相关
    private VideoPlayerManager playerManager;
    private VideoModel videoModel;
    private String sourceActivity;
    
    // 拖拽相关
    private int initialX, initialY;
    private float initialTouchX, initialTouchY;
    private boolean isDragging = false;

    // 缩放相关
    private ScaleGestureDetector scaleGestureDetector;
    private float currentScale = 1.0f;
    private int minWindowWidth, minWindowHeight;
    private int maxWindowWidth, maxWindowHeight;
    private int originalWindowWidth, originalWindowHeight;
    
    @Nullable
    @Override
    public IBinder onBind(Intent intent) {
        return null;
    }
    
    @Override
    public void onCreate() {
        super.onCreate();
        Log.d(TAG, "FloatingVideoService created");
        
        windowManager = (WindowManager) getSystemService(WINDOW_SERVICE);
        createNotificationChannel();
        initializeFloatingWindow();
    }
    
    @Override
    public int onStartCommand(Intent intent, int flags, int startId) {
        Log.d(TAG, "FloatingVideoService started");
        
        if (intent != null) {
            videoModel = (VideoModel) intent.getSerializableExtra(EXTRA_VIDEO_MODEL);
            sourceActivity = intent.getStringExtra(EXTRA_SOURCE_ACTIVITY);
            
            if (videoModel != null) {
                Log.d(TAG, "Starting floating video for: " + videoModel.getTitle());
                startForeground(NOTIFICATION_ID, createNotification());
                showFloatingWindow();
                initializePlayer();
                loadVideo();
                // 显示控制按钮3秒
                showControlButtonsTemporarily();
            } else {
                Log.e(TAG, "No video model provided, stopping service");
                stopSelf();
            }
        }
        
        return START_NOT_STICKY;
    }
    
    @Override
    public void onDestroy() {
        Log.d(TAG, "FloatingVideoService destroyed");

        hideFloatingWindow();
        releasePlayer();

        // 恢复原视频播放状态
        restoreOriginalVideoPlayer();

        // 更新悬浮窗状态
        FloatingVideoManager.getInstance().setFloatingWindowActive(false);

        super.onDestroy();
    }

    /**
     * 恢复原视频播放器状态
     */
    private void restoreOriginalVideoPlayer() {
        try {
            Log.d(TAG, "Attempting to restore original video player");

            // 通知FloatingVideoManager恢复原视频
            FloatingVideoManager.getInstance().restoreOriginalVideoPlayer();

        } catch (Exception e) {
            Log.e(TAG, "Error restoring original video player", e);
        }
    }

    /**
     * 创建通知渠道
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                "悬浮窗视频播放",
                NotificationManager.IMPORTANCE_LOW
            );
            channel.setDescription("悬浮窗视频播放服务");
            channel.setShowBadge(false);
            
            NotificationManager manager = getSystemService(NotificationManager.class);
            if (manager != null) {
                manager.createNotificationChannel(channel);
            }
        }
    }
    
    /**
     * 创建前台服务通知
     */
    private Notification createNotification() {
        Intent intent = new Intent(this, MainActivity.class);
        PendingIntent pendingIntent = PendingIntent.getActivity(
            this, 0, intent, 
            Build.VERSION.SDK_INT >= Build.VERSION_CODES.M ? 
                PendingIntent.FLAG_IMMUTABLE : PendingIntent.FLAG_UPDATE_CURRENT
        );
        
        String title = videoModel != null ? videoModel.getTitle() : "视频播放中";
        
        return new NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("小窗播放")
            .setContentText(title)
            .setSmallIcon(R.drawable.play_ic_play)
            .setContentIntent(pendingIntent)
            .setOngoing(true)
            .setPriority(NotificationCompat.PRIORITY_LOW)
            .build();
    }
    
    /**
     * 初始化悬浮窗
     */
    private void initializeFloatingWindow() {
        // 创建悬浮窗布局
        LayoutInflater inflater = LayoutInflater.from(this);
        floatingView = inflater.inflate(R.layout.floating_video_window, null);
        
        // 初始化UI组件
        initializeViews();
        
        // 设置窗口参数
        setupWindowParams();
        
        // 设置拖拽监听器
        setupDragListener();
        
        // 设置控制按钮监听器
        setupControlListeners();
    }
    
    /**
     * 初始化UI组件
     */
    private void initializeViews() {
        playerView = floatingView.findViewById(R.id.player_view);
        loadingIndicator = floatingView.findViewById(R.id.loading_indicator);
        playPauseOverlay = floatingView.findViewById(R.id.play_pause_overlay);
        btnPlayPause = floatingView.findViewById(R.id.btn_play_pause);
        btnReturnApp = floatingView.findViewById(R.id.btn_return_app);
        btnClose = floatingView.findViewById(R.id.btn_close);
        dragHandle = floatingView.findViewById(R.id.drag_handle);
        controlButtons = floatingView.findViewById(R.id.control_buttons);
        
        // 隐藏ExoPlayer默认控制器
        if (playerView != null) {
            playerView.setUseController(false);
        }
    }

    /**
     * 设置窗口参数
     */
    private void setupWindowParams() {
        int windowType;
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            windowType = WindowManager.LayoutParams.TYPE_APPLICATION_OVERLAY;
        } else {
            windowType = WindowManager.LayoutParams.TYPE_PHONE;
        }

        // 计算小窗尺寸 (dp转px)
        float density = getResources().getDisplayMetrics().density;
        originalWindowWidth = (int) (280 * density);
        originalWindowHeight = (int) (180 * density);

        // 设置缩放范围
        minWindowWidth = (int) (160 * density);   // 最小160dp
        minWindowHeight = (int) (100 * density);  // 最小100dp
        maxWindowWidth = (int) (480 * density);   // 最大480dp
        maxWindowHeight = (int) (320 * density);  // 最大320dp

        layoutParams = new WindowManager.LayoutParams(
            originalWindowWidth,
            originalWindowHeight,
            windowType,
            WindowManager.LayoutParams.FLAG_NOT_FOCUSABLE |
            WindowManager.LayoutParams.FLAG_LAYOUT_IN_SCREEN |
            WindowManager.LayoutParams.FLAG_NOT_TOUCH_MODAL,
            PixelFormat.TRANSLUCENT
        );

        layoutParams.gravity = Gravity.TOP | Gravity.START;
        layoutParams.x = 100;
        layoutParams.y = 200;

        // 初始化缩放手势检测器
        initializeScaleGestureDetector();
    }

    /**
     * 初始化缩放手势检测器
     */
    private void initializeScaleGestureDetector() {
        scaleGestureDetector = new ScaleGestureDetector(this, new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float scaleFactor = detector.getScaleFactor();
                currentScale *= scaleFactor;

                // 限制缩放范围
                currentScale = Math.max(0.5f, Math.min(currentScale, 2.0f));

                // 计算新的窗口尺寸
                int newWidth = (int) (originalWindowWidth * currentScale);
                int newHeight = (int) (originalWindowHeight * currentScale);

                // 限制窗口尺寸范围
                newWidth = Math.max(minWindowWidth, Math.min(newWidth, maxWindowWidth));
                newHeight = Math.max(minWindowHeight, Math.min(newHeight, maxWindowHeight));

                // 更新窗口尺寸
                layoutParams.width = newWidth;
                layoutParams.height = newHeight;

                // 确保窗口不会超出屏幕边界
                android.util.DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
                int screenWidth = displayMetrics.widthPixels;
                int screenHeight = displayMetrics.heightPixels;

                layoutParams.x = Math.max(0, Math.min(layoutParams.x, screenWidth - newWidth));
                layoutParams.y = Math.max(0, Math.min(layoutParams.y, screenHeight - newHeight));

                // 更新窗口布局
                if (windowManager != null && floatingView != null) {
                    windowManager.updateViewLayout(floatingView, layoutParams);
                }

                Log.d(TAG, "Window scaled to: " + newWidth + "x" + newHeight + ", scale: " + currentScale);
                return true;
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                Log.d(TAG, "Scale gesture begin");
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                Log.d(TAG, "Scale gesture end, final scale: " + currentScale);
            }
        });
    }

    /**
     * 设置拖拽监听器
     */
    private void setupDragListener() {
        // 为整个悬浮窗设置拖拽监听器，但排除控制按钮区域
        floatingView.setOnTouchListener(new View.OnTouchListener() {
            @Override
            public boolean onTouch(View v, MotionEvent event) {
                // 首先处理缩放手势
                if (scaleGestureDetector != null) {
                    scaleGestureDetector.onTouchEvent(event);

                    // 如果正在进行缩放手势，不处理拖拽
                    if (scaleGestureDetector.isInProgress()) {
                        return true;
                    }
                }

                // 检查触摸点是否在控制按钮区域内
                if (controlButtons != null && controlButtons.getVisibility() == View.VISIBLE) {
                    int[] location = new int[2];
                    controlButtons.getLocationOnScreen(location);
                    float touchX = event.getRawX();
                    float touchY = event.getRawY();

                    if (touchX >= location[0] && touchX <= location[0] + controlButtons.getWidth() &&
                        touchY >= location[1] && touchY <= location[1] + controlButtons.getHeight()) {
                        // 触摸在控制按钮区域内，不处理拖拽
                        return false;
                    }
                }

                switch (event.getAction() & MotionEvent.ACTION_MASK) {
                    case MotionEvent.ACTION_DOWN:
                        initialX = layoutParams.x;
                        initialY = layoutParams.y;
                        initialTouchX = event.getRawX();
                        initialTouchY = event.getRawY();
                        isDragging = false;
                        return true;

                    case MotionEvent.ACTION_MOVE:
                        // 只有在单指触摸时才处理拖拽
                        if (event.getPointerCount() == 1) {
                            float deltaX = event.getRawX() - initialTouchX;
                            float deltaY = event.getRawY() - initialTouchY;

                            // 检测是否开始拖拽 (增加阈值避免误触)
                            if (!isDragging && (Math.abs(deltaX) > 15 || Math.abs(deltaY) > 15)) {
                                isDragging = true;
                            }

                            if (isDragging) {
                                layoutParams.x = initialX + (int) deltaX;
                                layoutParams.y = initialY + (int) deltaY;

                                // 限制拖拽范围在屏幕内
                                if (windowManager != null && floatingView != null) {
                                    android.util.DisplayMetrics displayMetrics = getResources().getDisplayMetrics();
                                    int screenWidth = displayMetrics.widthPixels;
                                    int screenHeight = displayMetrics.heightPixels;

                                    layoutParams.x = Math.max(0, Math.min(layoutParams.x, screenWidth - layoutParams.width));
                                    layoutParams.y = Math.max(0, Math.min(layoutParams.y, screenHeight - layoutParams.height));

                                    windowManager.updateViewLayout(floatingView, layoutParams);
                                }
                            }
                        }
                        return true;

                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_POINTER_UP:
                        if (!isDragging && event.getPointerCount() == 1) {
                            // 单击事件 - 显示/隐藏控制按钮
                            toggleControlButtons();
                        }
                        isDragging = false;
                        return true;
                }
                return false;
            }
        });
    }

    /**
     * 设置控制按钮监听器
     */
    private void setupControlListeners() {
        // 播放/暂停按钮
        if (btnPlayPause != null) {
            btnPlayPause.setOnClickListener(v -> {
                Log.d(TAG, "Play/Pause button clicked");
                togglePlayPause();
                // 点击后保持控制按钮显示3秒
                showControlButtonsTemporarily();
            });
        }

        // 返回应用按钮
        if (btnReturnApp != null) {
            btnReturnApp.setOnClickListener(v -> {
                Log.d(TAG, "Return to app button clicked");
                returnToApp();
            });
        }

        // 关闭按钮
        if (btnClose != null) {
            btnClose.setOnClickListener(v -> {
                Log.d(TAG, "Close button clicked");
                closeFloatingWindow();
            });
        }

        // 为播放/暂停覆盖层也设置点击事件
        if (playPauseOverlay != null) {
            playPauseOverlay.setOnClickListener(v -> {
                Log.d(TAG, "Play/Pause overlay clicked");
                togglePlayPause();
                showControlButtonsTemporarily();
            });
        }
    }

    /**
     * 显示悬浮窗
     */
    private void showFloatingWindow() {
        try {
            if (windowManager != null && floatingView != null) {
                windowManager.addView(floatingView, layoutParams);
                Log.d(TAG, "Floating window shown");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to show floating window", e);
        }
    }

    /**
     * 隐藏悬浮窗
     */
    private void hideFloatingWindow() {
        try {
            if (windowManager != null && floatingView != null) {
                windowManager.removeView(floatingView);
                Log.d(TAG, "Floating window hidden");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide floating window", e);
        }
    }

    /**
     * 初始化播放器
     */
    private void initializePlayer() {
        if (playerManager == null) {
            playerManager = new VideoPlayerManager(this);
            playerManager.setPlayerView(playerView);
            playerManager.setPlayerListener(this);
            Log.d(TAG, "Player initialized");
        }
    }

    /**
     * 加载视频
     */
    private void loadVideo() {
        if (playerManager != null && videoModel != null) {
            showLoading();

            // 这里应该根据实际的视频URL获取逻辑来设置
            // 暂时使用测试URL
            String videoUrl = getVideoUrl();
            if (videoUrl != null) {
                playerManager.setVideoSource(videoUrl);
                Log.d(TAG, "Loading video: " + videoUrl);
            } else {
                Log.e(TAG, "No video URL available");
                hideLoading();
            }
        }
    }

    /**
     * 获取视频URL
     */
    private String getVideoUrl() {
        if (videoModel != null) {
            // 尝试获取视频的实际URL
            String videoUrl = videoModel.getVideoUrl();
            if (videoUrl != null && !videoUrl.isEmpty()) {
                Log.d(TAG, "Using video URL from model: " + videoUrl);
                return videoUrl;
            }

            // 检查videoUri
            if (videoModel.getVideoUri() != null) {
                String uriString = videoModel.getVideoUri().toString();
                if (!uriString.isEmpty()) {
                    Log.d(TAG, "Using video URI from model: " + uriString);
                    return uriString;
                }
            }

            // 尝试获取流媒体URL
            try {
                // 使用反射或其他方法获取流媒体URL
                if (videoModel.getClass().getMethod("getStreamUrl") != null) {
                    String streamUrl = (String) videoModel.getClass().getMethod("getStreamUrl").invoke(videoModel);
                    if (streamUrl != null && !streamUrl.isEmpty()) {
                        Log.d(TAG, "Using stream URL from model: " + streamUrl);
                        return streamUrl;
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Failed to get stream URL via reflection", e);
            }

            // 尝试获取其他可能的URL字段
            try {
                // 检查是否有其他URL字段
                String[] urlFields = {"url", "playUrl", "videoPath", "filePath", "downloadUrl", "localPath"};
                for (String fieldName : urlFields) {
                    try {
                        java.lang.reflect.Field field = videoModel.getClass().getDeclaredField(fieldName);
                        field.setAccessible(true);
                        String url = (String) field.get(videoModel);
                        if (url != null && !url.isEmpty()) {
                            Log.d(TAG, "Using URL from field " + fieldName + ": " + url);
                            return url;
                        }
                    } catch (Exception ignored) {
                        // 字段不存在，继续尝试下一个
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Failed to get URL via reflection", e);
            }

            // 检查是否有剧集信息，尝试获取当前剧集的URL
            try {
                if (videoModel.getEpisodes() != null && !videoModel.getEpisodes().isEmpty()) {
                    int currentEpisode = videoModel.getCurrentEpisode();
                    for (Object episode : videoModel.getEpisodes()) {
                        // 使用反射获取剧集的播放URL
                        try {
                            java.lang.reflect.Method getEpisodeNumber = episode.getClass().getMethod("getEpisodeNumber");
                            int episodeNumber = (Integer) getEpisodeNumber.invoke(episode);
                            if (episodeNumber == currentEpisode) {
                                // 尝试获取这个剧集的URL
                                String[] episodeUrlFields = {"url", "playUrl", "videoUrl", "streamUrl"};
                                for (String fieldName : episodeUrlFields) {
                                    try {
                                        java.lang.reflect.Field field = episode.getClass().getDeclaredField(fieldName);
                                        field.setAccessible(true);
                                        String url = (String) field.get(episode);
                                        if (url != null && !url.isEmpty()) {
                                            Log.d(TAG, "Using episode URL from field " + fieldName + ": " + url);
                                            return url;
                                        }
                                    } catch (Exception ignored) {
                                        // 字段不存在，继续尝试下一个
                                    }
                                }
                            }
                        } catch (Exception ignored) {
                            // 无法获取剧集信息，继续
                        }
                    }
                }
            } catch (Exception e) {
                Log.w(TAG, "Failed to get episode URL", e);
            }

            // 如果都没有，尝试通过filmLanguageInfoId获取播放URL
            if (videoModel.hasFilmLanguageInfoId()) {
                Log.d(TAG, "Attempting to get video URL via filmLanguageInfoId: " + videoModel.getFilmLanguageInfoId());
                // 这里可以调用API获取实际的播放URL
                // 暂时先使用测试URL，但记录filmLanguageInfoId以便后续改进
                Log.w(TAG, "TODO: Implement API call to get video URL for filmLanguageInfoId: " + videoModel.getFilmLanguageInfoId());
            }

            // 如果都没有，记录详细信息并使用测试URL
            Log.w(TAG, "No video URL found in VideoModel: " + videoModel.getTitle() +
                  ", ID: " + videoModel.getId() +
                  ", filmLanguageInfoId: " + videoModel.getFilmLanguageInfoId() +
                  ", using test URL. VideoModel class: " + videoModel.getClass().getName());
        }

        // 返回一个更稳定的测试URL
        String testUrl = "https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4";
        Log.d(TAG, "Using test URL: " + testUrl);
        return testUrl;
    }

    /**
     * 释放播放器
     */
    private void releasePlayer() {
        if (playerManager != null) {
            playerManager.release();
            playerManager = null;
            Log.d(TAG, "Player released");
        }
    }

    /**
     * 切换播放/暂停
     */
    private void togglePlayPause() {
        if (playerManager != null) {
            if (playerManager.isPlaying()) {
                playerManager.pause();
            } else {
                playerManager.play();
            }
        }
    }

    /**
     * 切换控制按钮显示/隐藏
     */
    private void toggleControlButtons() {
        if (controlButtons != null) {
            if (controlButtons.getVisibility() == View.VISIBLE) {
                controlButtons.setVisibility(View.GONE);
            } else {
                controlButtons.setVisibility(View.VISIBLE);
                // 3秒后自动隐藏
                controlButtons.postDelayed(() -> {
                    if (controlButtons != null) {
                        controlButtons.setVisibility(View.GONE);
                    }
                }, 3000);
            }
        }
    }

    /**
     * 临时显示控制按钮
     */
    private void showControlButtonsTemporarily() {
        if (controlButtons != null) {
            controlButtons.setVisibility(View.VISIBLE);
            // 3秒后自动隐藏
            controlButtons.postDelayed(() -> {
                if (controlButtons != null) {
                    controlButtons.setVisibility(View.GONE);
                }
            }, 3000);
        }
    }

    /**
     * 返回应用
     */
    private void returnToApp() {
        Intent intent = new Intent(this, MainActivity.class);
        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);

        // 根据源Activity添加额外信息
        if (sourceActivity != null) {
            intent.putExtra("source_activity", sourceActivity);
            if (videoModel != null) {
                intent.putExtra("video_model", videoModel);
            }
        }

        startActivity(intent);

        // 可选：关闭悬浮窗或保持播放
        // closeFloatingWindow();
    }

    /**
     * 关闭悬浮窗
     */
    private void closeFloatingWindow() {
        stopSelf();
    }

    /**
     * 显示加载指示器
     */
    private void showLoading() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 隐藏加载指示器
     */
    private void hideLoading() {
        if (loadingIndicator != null) {
            loadingIndicator.setVisibility(View.GONE);
        }
    }

    /**
     * 更新播放按钮状态
     */
    private void updatePlayButtonState(boolean isPlaying) {
        int iconRes = isPlaying ? R.drawable.play_ic_pause : R.drawable.play_ic_play;

        if (btnPlayPause != null) {
            btnPlayPause.setImageResource(iconRes);
        }

        if (playPauseOverlay != null) {
            playPauseOverlay.setImageResource(iconRes);
        }
    }

    // VideoPlayerListener接口实现
    @Override
    public void onPrepared() {
        Log.d(TAG, "Video prepared");
        hideLoading();
        updatePlayButtonState(false);
    }

    @Override
    public void onPlaybackStarted() {
        Log.d(TAG, "Playback started");
        updatePlayButtonState(true);
        if (playPauseOverlay != null) {
            playPauseOverlay.setVisibility(View.GONE);
        }
    }

    @Override
    public void onPlaybackPaused() {
        Log.d(TAG, "Playback paused");
        updatePlayButtonState(false);
        if (playPauseOverlay != null) {
            playPauseOverlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onPlaybackStopped() {
        Log.d(TAG, "Playback stopped");
        updatePlayButtonState(false);
        if (playPauseOverlay != null) {
            playPauseOverlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onError(String errorMessage) {
        Log.e(TAG, "Playback error: " + errorMessage);
        hideLoading();
        // 可以显示错误提示或关闭悬浮窗
    }

    @Override
    public void onVolumeChanged(float volume) {
        Log.d(TAG, "Volume changed: " + volume);
        // 悬浮窗中暂时不处理音量变化
    }

    @Override
    public void onPlaybackCompleted() {
        Log.d(TAG, "Playback completed");
        updatePlayButtonState(false);
        if (playPauseOverlay != null) {
            playPauseOverlay.setVisibility(View.VISIBLE);
        }
    }

    @Override
    public void onPositionChanged(long position, long duration) {
        // 悬浮窗中暂时不显示进度信息
    }

    @Override
    public void onBufferingUpdate(boolean isBuffering, int bufferedPercentage) {
        if (isBuffering) {
            showLoading();
        } else {
            hideLoading();
        }
    }

    @Override
    public void onPlaybackSpeedChanged(float speed) {
        Log.d(TAG, "Playback speed changed: " + speed);
        // 悬浮窗中暂时不处理播放速度变化
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {
        Log.d(TAG, "Video size changed: " + width + "x" + height);
        // 悬浮窗中暂时不处理视频尺寸变化
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        Log.d(TAG, "Player state changed: playWhenReady=" + playWhenReady + ", state=" + playbackState);
        // 悬浮窗中暂时不处理播放器状态变化
    }

    public void onBuffering() {
        Log.d(TAG, "Buffering");
        showLoading();
    }

    public void onBufferingEnd() {
        Log.d(TAG, "Buffering end");
        hideLoading();
    }

    public void onProgressUpdate(long currentPosition, long duration) {
        // 悬浮窗中暂时不显示进度条
    }
}
