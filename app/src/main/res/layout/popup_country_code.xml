<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/edit_profile_popup_width"
    android:layout_height="wrap_content"
    android:background="@drawable/edit_profile_country_popup_bg"
    android:orientation="vertical"
    android:padding="@dimen/edit_profile_popup_padding">

    <TextView
        android:id="@+id/tv_country_china"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+86"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_country_usa"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+1"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_country_uk"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+44"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_country_japan"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+81"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_country_korea"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+82"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

    <TextView
        android:id="@+id/tv_country_other"
        android:layout_width="match_parent"
        android:layout_height="@dimen/edit_profile_popup_item_height"
        android:text="+10"
        android:textColor="#ffffffff"
        android:textSize="@dimen/edit_profile_popup_text_size"
        android:fontFamily="sans-serif"
        android:gravity="center_vertical|start"
        android:paddingStart="@dimen/edit_profile_popup_padding"
        android:paddingEnd="@dimen/edit_profile_popup_padding"
        android:background="?android:attr/selectableItemBackground"
        android:clickable="true"
        android:focusable="true" />

</LinearLayout>
