<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FF000000"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:fillViewport="true">

<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false"
    android:clipToPadding="false">

    <!-- 顶部渐变背景阴影 -->
    <View
        android:id="@+id/view_gradient_background"
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:background="@drawable/most_popular_gradient_background"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 返回按钮 -->
    <ImageView
        android:id="@+id/iv_back"
        android:layout_width="@dimen/login_back_button_size"
        android:layout_height="@dimen/login_back_button_size"
        android:layout_marginStart="@dimen/login_back_button_margin_start"
        android:layout_marginTop="@dimen/login_back_button_margin_top"
        android:src="@drawable/login_btn_back"
        android:background="?android:attr/selectableItemBackgroundBorderless"
        android:clickable="true"
        android:focusable="true"
        android:contentDescription="返回"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- 页面标题 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="Categories"
        android:textColor="@color/white"
        android:textSize="@dimen/download_page_title_text_size"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintTop_toTopOf="@id/iv_back"
        app:layout_constraintBottom_toBottomOf="@id/iv_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 分类标签组 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_category_tags"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="22dp"
        android:layout_marginStart="@dimen/home_categories_margin_start"
        android:orientation="horizontal"
        android:clipToPadding="false"
        android:paddingEnd="@dimen/home_categories_margin_start"
        app:layout_constraintTop_toBottomOf="@id/iv_back"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- 海报轮播图 -->
    <androidx.viewpager2.widget.ViewPager2
        android:id="@+id/vp_carousel"
        android:layout_width="match_parent"
        android:layout_height="360dp"
        android:layout_marginTop="14dp"
        android:paddingStart="50dp"
        android:paddingEnd="50dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintTop_toBottomOf="@id/rv_category_tags"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- List标题 -->
    <TextView
        android:id="@+id/tv_list_title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginStart="@dimen/home_categories_margin_start"
        android:layout_marginBottom="18dp"
        android:text="List"
        android:textColor="@color/white"
        android:textSize="@dimen/home_categories_text_size"
        android:fontFamily="sans-serif-medium"
        app:layout_constraintTop_toBottomOf="@id/vp_carousel"
        app:layout_constraintStart_toStartOf="parent" />

    <!-- List列表 -->
    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/rv_list"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="18dp"
        android:clipToPadding="false"
        android:paddingBottom="16dp"
        android:nestedScrollingEnabled="false"
        app:layout_constraintTop_toBottomOf="@id/tv_list_title"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>

</androidx.core.widget.NestedScrollView>
