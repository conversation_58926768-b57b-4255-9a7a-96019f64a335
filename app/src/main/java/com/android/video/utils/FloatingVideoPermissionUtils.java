package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;
import androidx.annotation.RequiresApi;

/**
 * 悬浮窗权限管理工具类
 * 
 * 提供悬浮窗权限的检查、请求和管理功能。
 * 支持不同Android版本的权限处理。
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class FloatingVideoPermissionUtils {
    
    private static final String TAG = "FloatingVideoPermissionUtils";
    
    /**
     * 权限请求码
     */
    public static final int REQUEST_CODE_OVERLAY_PERMISSION = 1001;
    
    /**
     * 检查是否有悬浮窗权限
     * 
     * @param context 上下文
     * @return 是否有权限
     */
    public static boolean hasOverlayPermission(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            return Settings.canDrawOverlays(context);
        }
        // Android 6.0以下默认有权限
        return true;
    }
    
    /**
     * 请求悬浮窗权限
     * 
     * @param activity Activity实例
     */
    @RequiresApi(api = Build.VERSION_CODES.M)
    public static void requestOverlayPermission(Activity activity) {
        if (hasOverlayPermission(activity)) {
            Log.d(TAG, "Overlay permission already granted");
            return;
        }
        
        try {
            Intent intent = new Intent(Settings.ACTION_MANAGE_OVERLAY_PERMISSION);
            intent.setData(Uri.parse("package:" + activity.getPackageName()));
            activity.startActivityForResult(intent, REQUEST_CODE_OVERLAY_PERMISSION);
            Log.d(TAG, "Requesting overlay permission");
        } catch (Exception e) {
            Log.e(TAG, "Failed to request overlay permission", e);
            // 如果无法打开设置页面，尝试打开应用设置页面
            try {
                Intent intent = new Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS);
                intent.setData(Uri.parse("package:" + activity.getPackageName()));
                activity.startActivityForResult(intent, REQUEST_CODE_OVERLAY_PERMISSION);
            } catch (Exception ex) {
                Log.e(TAG, "Failed to open app settings", ex);
            }
        }
    }
    
    /**
     * 处理权限请求结果
     * 
     * @param context 上下文
     * @param requestCode 请求码
     * @param callback 回调接口
     */
    public static void handlePermissionResult(Context context, int requestCode, 
                                            PermissionCallback callback) {
        if (requestCode == REQUEST_CODE_OVERLAY_PERMISSION) {
            boolean hasPermission = hasOverlayPermission(context);
            Log.d(TAG, "Permission result: " + hasPermission);
            
            if (callback != null) {
                if (hasPermission) {
                    callback.onPermissionGranted();
                } else {
                    callback.onPermissionDenied();
                }
            }
        }
    }
    
    /**
     * 检查并请求权限（如果需要）
     * 
     * @param activity Activity实例
     * @param callback 回调接口
     */
    public static void checkAndRequestPermission(Activity activity, PermissionCallback callback) {
        if (hasOverlayPermission(activity)) {
            Log.d(TAG, "Permission already granted");
            if (callback != null) {
                callback.onPermissionGranted();
            }
        } else {
            Log.d(TAG, "Permission not granted, requesting...");
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
                // 保存回调以便在权限结果中使用
                PermissionCallbackHolder.setCallback(callback);
                requestOverlayPermission(activity);
            } else {
                // Android 6.0以下默认有权限
                if (callback != null) {
                    callback.onPermissionGranted();
                }
            }
        }
    }
    
    /**
     * 权限回调接口
     */
    public interface PermissionCallback {
        /**
         * 权限授予
         */
        void onPermissionGranted();
        
        /**
         * 权限拒绝
         */
        void onPermissionDenied();
    }
    
    /**
     * 权限回调持有者
     * 用于在Activity结果回调中获取回调接口
     */
    private static class PermissionCallbackHolder {
        private static PermissionCallback callback;
        
        public static void setCallback(PermissionCallback cb) {
            callback = cb;
        }
        
        public static PermissionCallback getCallback() {
            return callback;
        }
        
        public static void clearCallback() {
            callback = null;
        }
    }
    
    /**
     * 获取权限说明文本
     * 
     * @return 权限说明
     */
    public static String getPermissionDescription() {
        return "为了实现小窗播放功能，需要允许应用在其他应用上层显示内容。" +
               "请在设置中开启\"显示在其他应用的上层\"权限。";
    }
    
    /**
     * 检查设备是否支持悬浮窗功能
     * 
     * @param context 上下文
     * @return 是否支持
     */
    public static boolean isFloatingWindowSupported(Context context) {
        // 大部分Android设备都支持悬浮窗，但某些定制ROM可能有限制
        return Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT;
    }
    
    /**
     * 清理权限回调
     * 在Activity销毁时调用以避免内存泄漏
     */
    public static void clearPermissionCallback() {
        PermissionCallbackHolder.clearCallback();
    }
}
