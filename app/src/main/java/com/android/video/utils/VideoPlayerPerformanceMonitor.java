package com.android.video.utils;

import android.content.Context;
import android.os.Debug;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 视频播放器性能监控工具类
 * 用于监控内存使用、CPU使用率、播放性能等指标
 * <AUTHOR> Team
 */
public class VideoPlayerPerformanceMonitor {

    private static final String TAG = "VideoPlayerPerformanceMonitor";
    private static VideoPlayerPerformanceMonitor instance;
    
    private Context context;
    private Handler monitorHandler;
    private boolean isMonitoring = false;
    private long monitoringStartTime;
    
    // 性能数据
    private List<MemorySnapshot> memorySnapshots;
    private List<PerformanceMetric> performanceMetrics;
    private Map<String, Long> operationTimings;
    private Map<String, Integer> operationCounts;
    
    // 监控配置
    private static final long MONITOR_INTERVAL = 1000; // 1秒
    private static final int MAX_SNAPSHOTS = 300; // 最多保存5分钟的数据

    private VideoPlayerPerformanceMonitor(Context context) {
        this.context = context.getApplicationContext();
        this.monitorHandler = new Handler(Looper.getMainLooper());
        this.memorySnapshots = new ArrayList<>();
        this.performanceMetrics = new ArrayList<>();
        this.operationTimings = new ConcurrentHashMap<>();
        this.operationCounts = new ConcurrentHashMap<>();
    }

    public static synchronized VideoPlayerPerformanceMonitor getInstance(Context context) {
        if (instance == null) {
            instance = new VideoPlayerPerformanceMonitor(context);
        }
        return instance;
    }

    /**
     * 开始性能监控
     */
    public void startMonitoring() {
        if (isMonitoring) {
            Log.w(TAG, "Performance monitoring already started");
            return;
        }

        isMonitoring = true;
        monitoringStartTime = System.currentTimeMillis();
        
        // 清空之前的数据
        memorySnapshots.clear();
        performanceMetrics.clear();
        operationTimings.clear();
        operationCounts.clear();
        
        // 开始定期监控
        scheduleNextMonitoring();
        
        Log.i(TAG, "Performance monitoring started");
    }

    /**
     * 停止性能监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            Log.w(TAG, "Performance monitoring not started");
            return;
        }

        isMonitoring = false;
        monitorHandler.removeCallbacksAndMessages(null);
        
        Log.i(TAG, "Performance monitoring stopped");
    }

    /**
     * 安排下次监控
     */
    private void scheduleNextMonitoring() {
        if (!isMonitoring) return;
        
        monitorHandler.postDelayed(() -> {
            collectPerformanceData();
            scheduleNextMonitoring();
        }, MONITOR_INTERVAL);
    }

    /**
     * 收集性能数据
     */
    private void collectPerformanceData() {
        try {
            // 收集内存数据
            collectMemoryData();
            
            // 收集性能指标
            collectPerformanceMetrics();
            
            // 限制数据量
            limitDataSize();
            
        } catch (Exception e) {
            Log.e(TAG, "Failed to collect performance data", e);
        }
    }

    /**
     * 收集内存数据
     */
    private void collectMemoryData() {
        Runtime runtime = Runtime.getRuntime();
        Debug.MemoryInfo memoryInfo = new Debug.MemoryInfo();
        Debug.getMemoryInfo(memoryInfo);
        
        MemorySnapshot snapshot = new MemorySnapshot();
        snapshot.timestamp = System.currentTimeMillis();
        snapshot.totalMemory = runtime.totalMemory();
        snapshot.freeMemory = runtime.freeMemory();
        snapshot.usedMemory = snapshot.totalMemory - snapshot.freeMemory;
        snapshot.maxMemory = runtime.maxMemory();
        snapshot.nativeHeap = memoryInfo.nativePss * 1024; // Convert KB to bytes
        snapshot.dalvikHeap = memoryInfo.dalvikPss * 1024;
        
        memorySnapshots.add(snapshot);
    }

    /**
     * 收集性能指标
     */
    private void collectPerformanceMetrics() {
        PerformanceMetric metric = new PerformanceMetric();
        metric.timestamp = System.currentTimeMillis();
        metric.operationCounts = new HashMap<>(operationCounts);
        metric.averageOperationTimes = calculateAverageOperationTimes();
        
        performanceMetrics.add(metric);
    }

    /**
     * 计算平均操作时间
     */
    private Map<String, Double> calculateAverageOperationTimes() {
        Map<String, Double> averages = new HashMap<>();
        
        for (Map.Entry<String, Long> entry : operationTimings.entrySet()) {
            String operation = entry.getKey();
            Long totalTime = entry.getValue();
            Integer count = operationCounts.get(operation);
            
            if (count != null && count > 0) {
                averages.put(operation, (double) totalTime / count);
            }
        }
        
        return averages;
    }

    /**
     * 限制数据大小
     */
    private void limitDataSize() {
        // 限制内存快照数量
        while (memorySnapshots.size() > MAX_SNAPSHOTS) {
            memorySnapshots.remove(0);
        }
        
        // 限制性能指标数量
        while (performanceMetrics.size() > MAX_SNAPSHOTS) {
            performanceMetrics.remove(0);
        }
    }

    /**
     * 记录操作开始时间
     */
    public void startOperation(String operationName) {
        long startTime = System.currentTimeMillis();
        operationTimings.put(operationName + "_start", startTime);
    }

    /**
     * 记录操作结束时间
     */
    public void endOperation(String operationName) {
        long endTime = System.currentTimeMillis();
        Long startTime = operationTimings.get(operationName + "_start");
        
        if (startTime != null) {
            long duration = endTime - startTime;
            
            // 累加总时间
            operationTimings.merge(operationName, duration, Long::sum);
            
            // 增加计数
            operationCounts.merge(operationName, 1, Integer::sum);
            
            // 移除开始时间记录
            operationTimings.remove(operationName + "_start");
            
            Log.d(TAG, "Operation " + operationName + " took " + duration + "ms");
        }
    }

    /**
     * 记录事件
     */
    public void recordEvent(String eventName) {
        operationCounts.merge(eventName, 1, Integer::sum);
        Log.d(TAG, "Event recorded: " + eventName);
    }

    /**
     * 获取性能报告
     */
    public PerformanceReport getPerformanceReport() {
        PerformanceReport report = new PerformanceReport();
        report.monitoringDuration = System.currentTimeMillis() - monitoringStartTime;
        report.memorySnapshots = new ArrayList<>(memorySnapshots);
        report.performanceMetrics = new ArrayList<>(performanceMetrics);
        report.operationSummary = generateOperationSummary();
        report.memoryAnalysis = analyzeMemoryUsage();
        report.recommendations = generateRecommendations();
        
        return report;
    }

    /**
     * 生成操作摘要
     */
    private Map<String, OperationSummary> generateOperationSummary() {
        Map<String, OperationSummary> summary = new HashMap<>();
        
        for (Map.Entry<String, Integer> entry : operationCounts.entrySet()) {
            String operation = entry.getKey();
            Integer count = entry.getValue();
            Long totalTime = operationTimings.get(operation);
            
            if (totalTime != null && count > 0) {
                OperationSummary opSummary = new OperationSummary();
                opSummary.operationName = operation;
                opSummary.totalCount = count;
                opSummary.totalTime = totalTime;
                opSummary.averageTime = (double) totalTime / count;
                opSummary.frequency = (double) count / ((System.currentTimeMillis() - monitoringStartTime) / 1000.0);
                
                summary.put(operation, opSummary);
            }
        }
        
        return summary;
    }

    /**
     * 分析内存使用
     */
    private MemoryAnalysis analyzeMemoryUsage() {
        if (memorySnapshots.isEmpty()) {
            return new MemoryAnalysis();
        }

        MemoryAnalysis analysis = new MemoryAnalysis();
        
        // 计算内存统计
        long minUsed = Long.MAX_VALUE;
        long maxUsed = Long.MIN_VALUE;
        long totalUsed = 0;
        
        for (MemorySnapshot snapshot : memorySnapshots) {
            minUsed = Math.min(minUsed, snapshot.usedMemory);
            maxUsed = Math.max(maxUsed, snapshot.usedMemory);
            totalUsed += snapshot.usedMemory;
        }
        
        analysis.minMemoryUsed = minUsed;
        analysis.maxMemoryUsed = maxUsed;
        analysis.averageMemoryUsed = totalUsed / memorySnapshots.size();
        analysis.memoryGrowth = maxUsed - minUsed;
        
        // 检测内存泄漏
        if (memorySnapshots.size() >= 10) {
            MemorySnapshot first = memorySnapshots.get(0);
            MemorySnapshot last = memorySnapshots.get(memorySnapshots.size() - 1);
            analysis.potentialMemoryLeak = (last.usedMemory - first.usedMemory) > (50 * 1024 * 1024); // 50MB
        }
        
        return analysis;
    }

    /**
     * 生成优化建议
     */
    private List<String> generateRecommendations() {
        List<String> recommendations = new ArrayList<>();
        
        MemoryAnalysis memoryAnalysis = analyzeMemoryUsage();
        
        // 内存相关建议
        if (memoryAnalysis.potentialMemoryLeak) {
            recommendations.add("检测到潜在内存泄漏，建议检查资源释放逻辑");
        }
        
        if (memoryAnalysis.maxMemoryUsed > 200 * 1024 * 1024) { // 200MB
            recommendations.add("内存使用量较高，建议优化内存管理");
        }
        
        // 性能相关建议
        Map<String, OperationSummary> operationSummary = generateOperationSummary();
        for (OperationSummary summary : operationSummary.values()) {
            if (summary.averageTime > 1000) { // 1秒
                recommendations.add("操作 '" + summary.operationName + "' 平均耗时较长 (" + 
                    String.format("%.1f", summary.averageTime) + "ms)，建议优化");
            }
        }
        
        return recommendations;
    }

    /**
     * 内存快照类
     */
    public static class MemorySnapshot {
        public long timestamp;
        public long totalMemory;
        public long freeMemory;
        public long usedMemory;
        public long maxMemory;
        public long nativeHeap;
        public long dalvikHeap;
    }

    /**
     * 性能指标类
     */
    public static class PerformanceMetric {
        public long timestamp;
        public Map<String, Integer> operationCounts;
        public Map<String, Double> averageOperationTimes;
    }

    /**
     * 操作摘要类
     */
    public static class OperationSummary {
        public String operationName;
        public int totalCount;
        public long totalTime;
        public double averageTime;
        public double frequency; // 每秒频率
    }

    /**
     * 内存分析类
     */
    public static class MemoryAnalysis {
        public long minMemoryUsed;
        public long maxMemoryUsed;
        public long averageMemoryUsed;
        public long memoryGrowth;
        public boolean potentialMemoryLeak;
    }

    /**
     * 性能报告类
     */
    public static class PerformanceReport {
        public long monitoringDuration;
        public List<MemorySnapshot> memorySnapshots;
        public List<PerformanceMetric> performanceMetrics;
        public Map<String, OperationSummary> operationSummary;
        public MemoryAnalysis memoryAnalysis;
        public List<String> recommendations;
        
        public String generateSummary() {
            StringBuilder summary = new StringBuilder();
            summary.append("=== 视频播放器性能报告 ===\n");
            summary.append("监控时长: ").append(monitoringDuration / 1000).append("秒\n");
            summary.append("内存使用: ").append(formatBytes(memoryAnalysis.averageMemoryUsed)).append(" (平均)\n");
            summary.append("内存增长: ").append(formatBytes(memoryAnalysis.memoryGrowth)).append("\n");
            summary.append("潜在内存泄漏: ").append(memoryAnalysis.potentialMemoryLeak ? "是" : "否").append("\n");
            
            if (!operationSummary.isEmpty()) {
                summary.append("\n操作性能:\n");
                for (OperationSummary op : operationSummary.values()) {
                    summary.append("  ").append(op.operationName)
                           .append(": ").append(String.format("%.1f", op.averageTime))
                           .append("ms (").append(op.totalCount).append("次)\n");
                }
            }
            
            if (!recommendations.isEmpty()) {
                summary.append("\n优化建议:\n");
                for (String recommendation : recommendations) {
                    summary.append("  - ").append(recommendation).append("\n");
                }
            }
            
            return summary.toString();
        }
        
        private String formatBytes(long bytes) {
            if (bytes >= 1024 * 1024 * 1024) {
                return String.format("%.1f GB", bytes / (1024.0 * 1024.0 * 1024.0));
            } else if (bytes >= 1024 * 1024) {
                return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
            } else if (bytes >= 1024) {
                return String.format("%.1f KB", bytes / 1024.0);
            } else {
                return bytes + " B";
            }
        }
    }
}
