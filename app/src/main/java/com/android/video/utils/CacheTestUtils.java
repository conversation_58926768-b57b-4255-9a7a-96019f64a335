package com.android.video.utils;

import android.content.Context;
import android.util.Log;

import com.android.video.cache.DataCacheManager;
import com.android.video.network.ApiClientUtils;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * 缓存测试工具类
 * 用于测试和验证缓存系统的功能
 * <AUTHOR> Team
 */
public class CacheTestUtils {
    
    private static final String TAG = "CacheTestUtils";
    
    /**
     * 测试数据缓存功能
     */
    public static void testDataCache(Context context) {
        Log.d(TAG, "Starting data cache test");
        
        DataCacheManager cacheManager = DataCacheManager.getInstance(context);
        
        // 测试字符串缓存
        String testKey = "test_string_key";
        String testValue = "Hello, Cache World!";
        
        cacheManager.cacheData(testKey, testValue);
        String cachedValue = cacheManager.getCachedData(testKey, String.class);
        
        if (testValue.equals(cachedValue)) {
            Log.d(TAG, "✓ String cache test passed");
        } else {
            Log.e(TAG, "✗ String cache test failed");
        }
        
        // 测试对象缓存
        TestObject testObject = new TestObject("Test Name", 123);
        String objectKey = "test_object_key";
        
        cacheManager.cacheData(objectKey, testObject);
        TestObject cachedObject = cacheManager.getCachedData(objectKey, TestObject.class);
        
        if (cachedObject != null && testObject.name.equals(cachedObject.name) && testObject.id == cachedObject.id) {
            Log.d(TAG, "✓ Object cache test passed");
        } else {
            Log.e(TAG, "✗ Object cache test failed");
        }
        
        // 测试列表缓存
        List<String> testList = new ArrayList<>();
        testList.add("Item 1");
        testList.add("Item 2");
        testList.add("Item 3");
        
        String listKey = "test_list_key";
        cacheManager.cacheData(listKey, testList);
        
        // 使用TypeToken获取泛型列表
        com.google.gson.reflect.TypeToken<List<String>> typeToken = 
            new com.google.gson.reflect.TypeToken<List<String>>(){};
        List<String> cachedList = cacheManager.getCachedData(listKey, typeToken.getType());
        
        if (cachedList != null && cachedList.size() == testList.size()) {
            Log.d(TAG, "✓ List cache test passed");
        } else {
            Log.e(TAG, "✗ List cache test failed");
        }
        
        // 测试缓存过期
        String expireKey = "test_expire_key";
        String expireValue = "This will expire";
        
        // 设置1秒过期时间
        cacheManager.cacheData(expireKey, expireValue, 1000);
        
        // 立即检查
        String immediateValue = cacheManager.getCachedData(expireKey, String.class);
        if (expireValue.equals(immediateValue)) {
            Log.d(TAG, "✓ Immediate cache retrieval test passed");
        } else {
            Log.e(TAG, "✗ Immediate cache retrieval test failed");
        }
        
        // 等待过期后检查
        new Thread(() -> {
            try {
                Thread.sleep(1500); // 等待1.5秒
                String expiredValue = cacheManager.getCachedData(expireKey, String.class);
                if (expiredValue == null) {
                    Log.d(TAG, "✓ Cache expiration test passed");
                } else {
                    Log.e(TAG, "✗ Cache expiration test failed");
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Cache expiration test interrupted", e);
            }
        }).start();
        
        Log.d(TAG, "Data cache test completed");
    }
    
    /**
     * 测试API缓存功能
     */
    public static void testApiCache(Context context) {
        Log.d(TAG, "Starting API cache test");

        // 测试缓存大小
        long cacheSize = ApiClientUtils.getCacheSize();
        Log.d(TAG, "Current cache size: " + cacheSize + " bytes");

        // 测试缓存目录
        File cacheDir = ApiClientUtils.getCacheDirectory();
        if (cacheDir != null) {
            Log.d(TAG, "Cache directory: " + cacheDir.getAbsolutePath());
            Log.d(TAG, "Cache directory exists: " + cacheDir.exists());

            // 列出缓存文件
            File[] cacheFiles = cacheDir.listFiles();
            if (cacheFiles != null) {
                Log.d(TAG, "Cache files count: " + cacheFiles.length);
                for (File file : cacheFiles) {
                    Log.d(TAG, "Cache file: " + file.getName() + " (" + file.length() + " bytes)");
                }
            }
        }

        // 测试实际的HTTP请求缓存
        testHttpRequestCache(context);

        Log.d(TAG, "API cache test completed");
    }

    /**
     * 测试HTTP请求缓存
     */
    private static void testHttpRequestCache(Context context) {
        new Thread(() -> {
            try {
                okhttp3.OkHttpClient client = ApiClientUtils.getHttpClient();
                if (client == null) {
                    Log.e(TAG, "HTTP client is null");
                    return;
                }

                String testUrl = "https://httpbin.org/delay/1"; // 测试URL
                okhttp3.Request request = new okhttp3.Request.Builder()
                        .url(testUrl)
                        .build();

                Log.d(TAG, "Making first request to test caching...");
                long startTime1 = System.currentTimeMillis();
                okhttp3.Response response1 = client.newCall(request).execute();
                long endTime1 = System.currentTimeMillis();

                Log.d(TAG, "First request completed in " + (endTime1 - startTime1) + "ms");
                Log.d(TAG, "Cache response: " + (response1.cacheResponse() != null));
                Log.d(TAG, "Network response: " + (response1.networkResponse() != null));
                response1.close();

                // 等待一秒后发起第二次请求
                Thread.sleep(1000);

                Log.d(TAG, "Making second request to test caching...");
                long startTime2 = System.currentTimeMillis();
                okhttp3.Response response2 = client.newCall(request).execute();
                long endTime2 = System.currentTimeMillis();

                Log.d(TAG, "Second request completed in " + (endTime2 - startTime2) + "ms");
                Log.d(TAG, "Cache response: " + (response2.cacheResponse() != null));
                Log.d(TAG, "Network response: " + (response2.networkResponse() != null));

                if (response2.cacheResponse() != null) {
                    Log.d(TAG, "✓ Cache test PASSED - Second request used cache");
                } else {
                    Log.d(TAG, "✗ Cache test FAILED - Second request did not use cache");
                }

                response2.close();

            } catch (Exception e) {
                Log.e(TAG, "HTTP cache test failed", e);
            }
        }).start();
    }
    
    /**
     * 获取缓存统计信息
     */
    public static void printCacheStats(Context context) throws IOException {
        Log.d(TAG, "=== Cache Statistics ===");
        
        DataCacheManager cacheManager = DataCacheManager.getInstance(context);
        DataCacheManager.CacheStats stats = cacheManager.getCacheStats();
        
        Log.d(TAG, "Memory cache size: " + stats.memoryCacheSize);
        Log.d(TAG, "Disk cache size: " + stats.diskCacheSize);
        
        // 获取API客户端信息
        String clientInfo = ApiClientUtils.getClientInfo();
        Log.d(TAG, "API Client Info:\n" + clientInfo);
        
        Log.d(TAG, "========================");
    }
    
    /**
     * 清理所有测试缓存
     */
    public static void cleanupTestCache(Context context) {
        Log.d(TAG, "Cleaning up test cache");
        
        DataCacheManager cacheManager = DataCacheManager.getInstance(context);
        
        // 清除测试用的缓存键
        cacheManager.clearCachedData("test_string_key");
        cacheManager.clearCachedData("test_object_key");
        cacheManager.clearCachedData("test_list_key");
        cacheManager.clearCachedData("test_expire_key");
        
        Log.d(TAG, "Test cache cleanup completed");
    }
    
    /**
     * 运行完整的缓存测试套件
     */
    public static void runFullCacheTest(Context context) throws IOException {
        Log.d(TAG, "========== Starting Full Cache Test ==========");
        
        // 打印初始状态
        printCacheStats(context);
        
        // 运行数据缓存测试
        testDataCache(context);
        
        // 运行API缓存测试
        testApiCache(context);
        
        // 等待一段时间让异步操作完成
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Log.e(TAG, "Test interrupted", e);
        }
        
        // 打印最终状态
        printCacheStats(context);
        
        // 清理测试数据
        cleanupTestCache(context);
        
        Log.d(TAG, "========== Full Cache Test Completed ==========");
    }
    
    /**
     * 测试用的简单对象类
     */
    public static class TestObject {
        public String name;
        public int id;
        
        public TestObject() {
            // 默认构造函数（Gson需要）
        }
        
        public TestObject(String name, int id) {
            this.name = name;
            this.id = id;
        }
        
        @Override
        public String toString() {
            return "TestObject{name='" + name + "', id=" + id + "}";
        }
    }
    
    /**
     * 测试图片加载动画
     */
    public static void testImageLoadingAnimation(Context context) {
        Log.d(TAG, "Image loading animation test - check UI for visual verification");
        // 这个测试需要在UI中进行视觉验证
        // 可以在HomeFragment中观察图片加载动画效果
    }
    
    /**
     * 性能测试 - 测试缓存读写性能
     */
    public static void performanceTest(Context context) {
        Log.d(TAG, "Starting cache performance test");
        
        DataCacheManager cacheManager = DataCacheManager.getInstance(context);
        
        // 写入性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            cacheManager.cacheData("perf_test_" + i, "Test data " + i);
        }
        long writeTime = System.currentTimeMillis() - startTime;
        Log.d(TAG, "Write 100 items took: " + writeTime + "ms");
        
        // 读取性能测试
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 100; i++) {
            String data = cacheManager.getCachedData("perf_test_" + i, String.class);
        }
        long readTime = System.currentTimeMillis() - startTime;
        Log.d(TAG, "Read 100 items took: " + readTime + "ms");
        
        // 清理性能测试数据
        for (int i = 0; i < 100; i++) {
            cacheManager.clearCachedData("perf_test_" + i);
        }
        
        Log.d(TAG, "Cache performance test completed");
    }
}
