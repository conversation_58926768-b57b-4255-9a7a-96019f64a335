<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="@dimen/home_video_item_spacing_horizontal"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报容器 -->
    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_poster_container"
        android:layout_width="@dimen/home_best_for_you_item_width"
        android:layout_height="@dimen/home_best_for_you_item_height"
        android:background="@drawable/home_best_for_you_border_new"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 海报背景 -->
        <ImageView
            android:id="@+id/iv_video_poster"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="@dimen/home_best_for_you_item_border_width"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster"
            android:background="@drawable/home_best_for_you_inner_border"
            android:clipToOutline="true"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />



        <!-- 合适率标签 - 位于矩形内部右上角 -->
        <TextView
            android:id="@+id/tv_match_percentage"
            android:layout_width="@dimen/home_match_percentage_width"
            android:layout_height="@dimen/home_match_percentage_height"
            android:layout_marginTop="@dimen/home_best_for_you_match_margin_top"
            android:layout_marginEnd="@dimen/home_best_for_you_match_margin_end"
            android:background="@drawable/home_match_percentage_background"
            android:text="95%"
            android:textColor="@color/white"
            android:textSize="@dimen/home_match_percentage_text_size"
            android:fontFamily="sans-serif-bold"
            android:gravity="center"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <!-- 视频标题 - 位于矩形外部下方 -->
    <TextView
        android:id="@+id/tv_video_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/home_best_for_you_title_margin_top"
        android:text="喜洋洋与灰太狼"
        android:textColor="@color/white"
        android:textSize="@dimen/home_video_title_text_size"
        android:fontFamily="sans-serif-medium"
        android:maxLines="2"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/cl_poster_container"
        app:layout_constraintStart_toStartOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="@id/cl_poster_container" />

    <!-- 合适原因标签 - 位于标题下方 -->
    <TextView
        android:id="@+id/tv_best_for_you_label"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/home_best_for_you_reason_margin_top"
        android:text="认为你喜欢"
        android:textColor="#CCFFFFFF"
        android:textSize="@dimen/home_match_percentage_text_size"
        android:fontFamily="sans-serif-light"
        android:maxLines="1"
        android:ellipsize="end"
        app:layout_constraintTop_toBottomOf="@id/tv_video_title"
        app:layout_constraintStart_toStartOf="@id/cl_poster_container"
        app:layout_constraintEnd_toEndOf="@id/cl_poster_container" />

</androidx.constraintlayout.widget.ConstraintLayout>
