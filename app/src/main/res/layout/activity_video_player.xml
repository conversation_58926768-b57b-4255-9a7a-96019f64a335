<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.activity.VideoPlayerActivity">

    <!-- ViewPager2 for video switching with boundary control -->
    <com.android.video.ui.component.BoundaryControlledViewPager2
        android:id="@+id/viewPagerVideos"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Top Control Bar -->
    <LinearLayout
        android:id="@+id/topControlBar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/login_back_button_margin_start"
        android:paddingEnd="@dimen/video_player_common_margin"
        android:paddingTop="@dimen/login_back_button_margin_top"
        android:paddingBottom="@dimen/video_player_common_margin"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btnBack"
            android:layout_width="@dimen/login_back_button_size"
            android:layout_height="@dimen/login_back_button_size"
            android:src="@drawable/login_btn_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/video_player_divider_height"
            android:layout_weight="1" />

        <!-- Small Screen Play Button -->
        <ImageView
            android:id="@+id/btnSmallScreen"
            android:layout_width="@dimen/video_player_new_download_button_size"
            android:layout_height="@dimen/video_player_new_download_button_size"
            android:background="@drawable/video_player_control_button_bg"
            android:scaleType="center"
            android:layout_marginEnd="@dimen/video_player_control_button_spacing"
            android:src="@drawable/play_ic_xiaoping"
            android:contentDescription="@string/video_player_small_screen" />

        <!-- Download Button -->
        <ImageView
            android:id="@+id/btnDownload"
            android:layout_width="@dimen/video_player_new_download_button_size"
            android:layout_height="@dimen/video_player_new_download_button_size"
            android:src="@drawable/play_ic_download"
            android:background="@drawable/video_player_control_button_bg"
            android:scaleType="center"
            android:contentDescription="@string/video_player_download" />

    </LinearLayout>

    <!-- Side Control Buttons -->
    <LinearLayout
        android:id="@+id/sideControlButtons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginEnd="@dimen/video_player_side_button_margin_end"
        android:layout_marginBottom="@dimen/video_player_like_button_margin_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottomInfoArea">

        <!-- Like Button -->
        <LinearLayout
            android:layout_width="@dimen/video_player_side_button_container_width"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="@dimen/video_player_side_button_vertical_spacing">

            <ImageView
                android:id="@+id/btnLike"
                style="@style/VideoPlayerSideButton"
                android:src="@drawable/play_ic_like"
                android:contentDescription="收藏" />

            <TextView
                android:id="@+id/tvLikeCount"
                style="@style/VideoPlayerSideButtonText"
                android:layout_marginTop="@dimen/video_player_side_button_spacing"
                android:text="1.2万"
                android:gravity="center"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- Share Button -->
        <LinearLayout
            android:layout_width="@dimen/video_player_side_button_container_width"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginBottom="@dimen/video_player_side_button_vertical_spacing">

            <ImageView
                android:id="@+id/btnShare"
                style="@style/VideoPlayerSideButton"
                android:src="@drawable/play_ic_share"
                android:contentDescription="分享" />

            <TextView
                android:id="@+id/tvShareCount"
                style="@style/VideoPlayerSideButtonText"
                android:layout_marginTop="@dimen/video_player_side_button_spacing"
                android:text="Share"
                android:gravity="center"
                tools:ignore="HardcodedText" />

        </LinearLayout>

        <!-- Comment Button -->
        <LinearLayout
            android:layout_width="@dimen/video_player_side_button_container_width"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center">

            <ImageView
                android:id="@+id/btnComment"
                style="@style/VideoPlayerSideButton"
                android:src="@drawable/play_ic_comment"
                android:contentDescription="评论" />

            <TextView
                android:id="@+id/tvCommentCount"
                style="@style/VideoPlayerSideButtonText"
                android:layout_marginTop="@dimen/video_player_side_button_spacing"
                android:text="Comment"
                android:gravity="center"
                tools:ignore="HardcodedText" />

        </LinearLayout>

    </LinearLayout>

    <!-- Bottom Info Area -->
    <LinearLayout
        android:id="@+id/bottomInfoArea"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginStart="@dimen/video_player_common_margin"
        android:layout_marginEnd="@dimen/video_player_common_margin"
        android:layout_marginBottom="@dimen/video_player_common_margin"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Video Title -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="start|center_vertical">

            <TextView
                android:id="@+id/tvVideoTitle"
                style="@style/VideoPlayerTitleText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="视频标题"
                android:maxLines="1"
                android:ellipsize="end"
                tools:ignore="HardcodedText" />

            <ImageView
                android:id="@+id/btnDetail"
                android:layout_width="@dimen/video_player_title_icon_size"
                android:layout_height="@dimen/video_player_title_icon_size"
                android:layout_marginStart="@dimen/video_player_common_margin_small"
                android:src="@drawable/play_ic_detail"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/video_player_episode" />

        </LinearLayout>

        <!-- Video Description -->
        <TextView
            android:id="@+id/tvVideoDescription"
            style="@style/VideoPlayerDescriptionText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/video_player_common_margin_medium"
            android:text="视频描述内容"
            android:maxLines="1"
            android:ellipsize="end"
            android:clickable="true"
            android:focusable="true"
            tools:ignore="HardcodedText" />

        <!-- Progress SeekBar with increased touch area -->
        <SeekBar
            android:id="@+id/progressSeekBar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/video_player_progress_height"
            android:layout_marginTop="@dimen/video_player_progress_margin_top"
            android:progressDrawable="@drawable/video_player_seekbar_bg"
            android:thumb="@drawable/seek_thumb_normal"
            android:progress="30"
            android:max="100"
            android:splitTrack="false"
            android:thumbOffset="@dimen/video_player_thumb_offset" />

        <!-- Bottom Controls -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/video_player_bottom_controls_margin_top"
            android:layout_marginBottom="@dimen/video_player_common_margin">

            <!-- Episode Selection Button -->
            <LinearLayout
                android:id="@+id/btnEpisodeSelection"
                android:layout_width="@dimen/video_player_episode_selection_width"
                android:layout_height="@dimen/video_player_episode_button_height"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/video_player_episode_button_bg"
                android:paddingStart="@dimen/video_player_episode_padding_start"
                android:paddingEnd="@dimen/video_player_episode_padding_end"
                android:clickable="true"
                android:focusable="true"
                tools:ignore="RtlSymmetry">

                <TextView
                    android:id="@+id/tvEpisodeText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="EP.1/EP.10"
                    android:textSize="12sp"
                    android:textColor="@color/video_player_text_tertiary"
                    android:fontFamily="sans-serif"
                    tools:ignore="HardcodedText" />

                <ImageView
                    android:layout_width="@dimen/video_player_episode_icon_size"
                    android:layout_height="@dimen/video_player_episode_icon_size"
                    android:src="@drawable/play_ic_ep"
                    android:contentDescription="@string/video_player_episode" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="@dimen/video_player_divider_height"
                android:layout_weight="1" />

            <!-- Small Control Buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Danmaku Toggle Button -->
                <ImageView
                    android:id="@+id/btnDanmaku"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:src="@drawable/play_ic_zimu"
                    android:contentDescription="@string/video_player_danmaku" />

                <!-- Speed Selection Button -->
                <TextView
                    android:id="@+id/btnSpeedSelection"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:text="1.0x"
                    android:textColor="@color/video_player_text_white"
                    android:textSize="12sp"
                    android:gravity="center"
                    tools:ignore="HardcodedText" />

                <!-- Quality Selection Button -->
                <TextView
                    android:id="@+id/btnQualitySelection"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:text="720P"
                    android:textColor="@color/video_player_text_white"
                    android:textSize="12sp"
                    android:gravity="center"
                    tools:ignore="HardcodedText" />

                <!-- Fullscreen Button -->
                <ImageView
                    android:id="@+id/btnFullscreen"
                    android:layout_width="@dimen/video_player_fullscreen_button_width"
                    android:layout_height="@dimen/video_player_fullscreen_button_height"
                    android:background="@drawable/video_player_small_button_bg"
                    android:scaleType="center"
                    android:src="@drawable/play_ic_quanping"
                    android:contentDescription="@string/video_player_fullscreen" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- Progress Touch Area -->
    <View
        android:id="@+id/progressTouchArea"
        android:layout_width="match_parent"
        android:layout_height="@dimen/video_player_progress_touch_area_height"
        android:background="@android:color/transparent"
        app:layout_constraintTop_toTopOf="@+id/progressSeekBar"
        app:layout_constraintBottom_toBottomOf="@+id/progressSeekBar"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:layout_marginTop="@dimen/video_player_progress_touch_area_margin_negative"
        android:layout_marginBottom="@dimen/video_player_progress_touch_area_margin_negative" />

    <!-- Video Poster Popup Overlay -->
    <FrameLayout
        android:id="@+id/posterPopupOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/video_poster_overlay_bg"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Poster Container -->
        <LinearLayout
            android:id="@+id/posterContainer"
            android:layout_width="@dimen/poster_popup_width"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/poster_popup_margin_top"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- Movie Poster -->
            <ImageView
                android:id="@+id/moviePosterImage"
                android:layout_width="@dimen/poster_popup_width"
                android:layout_height="@dimen/poster_popup_height"
                android:src="@drawable/movie_poster"
                android:scaleType="centerCrop"
                android:background="@drawable/video_poster_popup_bg"
                android:clipToOutline="true" />

            <!-- Close Button -->
            <ImageView
                android:id="@+id/btnClosePoster"
                android:layout_width="@dimen/poster_popup_close_button_size"
                android:layout_height="@dimen/poster_popup_close_button_size"
                android:layout_marginTop="@dimen/poster_popup_close_button_margin_top"
                android:src="@drawable/ic_close1"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="关闭海报" />

        </LinearLayout>

    </FrameLayout>

    <!-- VIP Unlock Popup Overlay -->
    <FrameLayout
        android:id="@+id/vipUnlockPopupOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/video_poster_overlay_bg"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- VIP Unlock Container -->
        <LinearLayout
            android:id="@+id/vipUnlockContainer"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="@dimen/vip_unlock_popup_margin_top"
            android:orientation="vertical"
            android:gravity="center_horizontal">

            <!-- VIP Message Text -->
            <TextView
                android:id="@+id/tvVipMessage"
                android:layout_width="@dimen/vip_unlock_message_width"
                android:layout_height="wrap_content"
                android:text="This episode is a VIP chapter,\nplease unlock it first"
                android:textSize="@dimen/vip_unlock_message_text_size"
                android:textColor="@android:color/white"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:lineSpacingExtra="@dimen/vip_unlock_message_line_spacing" />

            <!-- Unlock Now Button -->
            <LinearLayout
                android:id="@+id/btnUnlockNow"
                android:layout_width="@dimen/vip_unlock_button_width"
                android:layout_height="@dimen/vip_unlock_button_height"
                android:layout_marginTop="@dimen/vip_unlock_button_margin_top"
                android:background="@drawable/vip_unlock_button_bg"
                android:orientation="horizontal"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Unlock now"
                    android:textSize="@dimen/vip_unlock_button_text_size"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif" />

                <ImageView
                    android:layout_width="@dimen/vip_unlock_coin_icon_size"
                    android:layout_height="@dimen/vip_unlock_coin_icon_size"
                    android:layout_marginStart="@dimen/vip_unlock_coin_icon_margin_start"
                    android:src="@drawable/jinbi" />

                <TextView
                    android:id="@+id/tvCoinCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/vip_unlock_coin_text_margin_start"
                    android:text="3000"
                    android:textSize="@dimen/vip_unlock_coin_text_size"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- Watch AD Button -->
            <LinearLayout
                android:id="@+id/btnWatchAd"
                android:layout_width="@dimen/vip_unlock_button_width"
                android:layout_height="@dimen/vip_unlock_button_height"
                android:layout_marginTop="@dimen/vip_unlock_button_spacing"
                android:background="@drawable/vip_watch_ad_button_bg"
                android:orientation="horizontal"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Watch AD to unlock"
                    android:textSize="@dimen/vip_unlock_button_text_size"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- Open VIP Button -->
            <LinearLayout
                android:id="@+id/btnOpenVip"
                android:layout_width="@dimen/vip_unlock_button_width"
                android:layout_height="@dimen/vip_unlock_button_height"
                android:layout_marginTop="@dimen/vip_unlock_button_spacing"
                android:background="@drawable/vip_open_vip_button_bg"
                android:orientation="horizontal"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Open VIP for free viewing"
                    android:textSize="@dimen/vip_unlock_button_text_size"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- Close Button -->
            <ImageView
                android:id="@+id/btnCloseVipPopup"
                android:layout_width="@dimen/vip_unlock_close_button_size"
                android:layout_height="@dimen/vip_unlock_close_button_size"
                android:layout_marginTop="@dimen/vip_unlock_close_button_margin_top"
                android:src="@drawable/ic_close1"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="关闭VIP弹窗" />

        </LinearLayout>

    </FrameLayout>

    <!-- Watch AD Popup Overlay -->
    <FrameLayout
        android:id="@+id/watchAdPopupOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/video_poster_overlay_bg"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Watch AD Container -->
        <LinearLayout
            android:id="@+id/watchAdContainer"
            android:layout_width="@dimen/watch_ad_popup_width"
            android:layout_height="@dimen/watch_ad_popup_height"
            android:layout_gravity="center"
            android:background="@drawable/vip_watch_ad_button_bg"
            android:orientation="vertical"
            android:gravity="center"
            android:paddingTop="@dimen/watch_ad_popup_padding_vertical"
            android:paddingBottom="@dimen/watch_ad_popup_padding_vertical">

            <!-- Watch AD Text -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Watch AD to unlock"
                android:textSize="@dimen/watch_ad_popup_text_size"
                android:textColor="#FFFFFF"
                android:fontFamily="sans-serif" />

            <!-- Coin Info Container -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/watch_ad_popup_text_margin_top"
                android:orientation="horizontal"
                android:gravity="center">

                <!-- Coin Icon -->
                <ImageView
                    android:layout_width="@dimen/watch_ad_popup_coin_icon_size"
                    android:layout_height="@dimen/watch_ad_popup_coin_icon_size"
                    android:src="@drawable/jinbi" />

                <!-- Coin Count -->
                <TextView
                    android:id="@+id/tvWatchAdCoinCount"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/watch_ad_popup_coin_margin_start"
                    android:text="3000"
                    android:textSize="@dimen/watch_ad_popup_coin_text_size"
                    android:textColor="#FFFFFF"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

        </LinearLayout>

    </FrameLayout>

    <!-- Recharge Popup Overlay -->
    <FrameLayout
        android:id="@+id/rechargePopupOverlay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/recharge_popup_overlay_bg"
        android:visibility="gone"
        android:clickable="true"
        android:focusable="true"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Recharge Container -->
        <FrameLayout
            android:id="@+id/rechargeContainer"
            android:layout_width="match_parent"
            android:layout_height="@dimen/recharge_popup_height"
            android:layout_gravity="bottom"
            android:background="@drawable/recharge_popup_bg">

            <!-- Background Image (底层) -->
            <ImageView
                android:id="@+id/rechargeBgImage"
                android:layout_width="match_parent"
                android:layout_height="@dimen/recharge_bg_image_height"
                android:layout_gravity="top"
                android:src="@drawable/movie_bg"
                android:scaleType="centerCrop" />

            <!-- Content Container (上层) -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical">

                <!-- Close Button -->
                <ImageView
                    android:id="@+id/btnCloseRechargePopup"
                    android:layout_width="@dimen/recharge_close_button_size"
                    android:layout_height="@dimen/recharge_close_button_size"
                    android:layout_marginTop="@dimen/video_player_popup_margin"
                    android:layout_marginEnd="@dimen/video_player_popup_margin"
                    android:layout_gravity="end"
                    android:src="@drawable/ic_close"
                    android:background="?android:attr/selectableItemBackgroundBorderless"
                    android:contentDescription="关闭充值弹窗" />

                <!-- Content Area -->
                <ScrollView
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_weight="1"
                    android:fillViewport="true">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!-- My Points Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_my_points_margin_top"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/recharge_my_points_margin_start"
                        android:text="My points"
                        android:textSize="@dimen/recharge_my_points_text_size"
                        android:textColor="@color/recharge_text_primary"
                        android:fontFamily="sans-serif" />

                    <ImageView
                        android:layout_width="@dimen/recharge_header_jinbi_icon_size"
                        android:layout_height="@dimen/recharge_header_jinbi_icon_size"
                        android:layout_marginStart="@dimen/recharge_jinbi_icon_margin_start"
                        android:src="@drawable/jinbi" />

                    <TextView
                        android:id="@+id/tvRechargeCurrentCoins"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/recharge_amount_coin_margin_start"
                        android:text="3000"
                        android:textSize="@dimen/recharge_header_coin_count_text_size"
                        android:textColor="@color/recharge_text_primary"
                        android:fontFamily="sans-serif-medium" />

                </LinearLayout>

                <!-- Recharge Amount Cards -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_amount_cards_margin_top"
                    android:layout_marginStart="@dimen/recharge_amount_card_margin_start"
                    android:layout_marginEnd="@dimen/recharge_amount_card_margin_start"
                    android:orientation="horizontal">

                    <!-- First Row -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <!-- 500 Coins Card -->
                        <LinearLayout
                            android:id="@+id/rechargeCard500"
                            android:layout_width="@dimen/recharge_amount_card_width"
                            android:layout_height="@dimen/recharge_amount_card_height"
                            android:background="@drawable/recharge_amount_card_selector"
                            android:orientation="vertical"
                            android:clickable="true"
                            android:focusable="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_jinbi_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <ImageView
                                    android:layout_width="@dimen/recharge_amount_jinbi_icon_size"
                                    android:layout_height="@dimen/recharge_amount_jinbi_icon_size"
                                    android:src="@drawable/jinbi" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_coin_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_coin_text_size"
                                    android:textColor="@color/recharge_text_primary"
                                    android:fontFamily="sans-serif" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_bonus_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_bonus_text_size"
                                    android:textColor="@color/recharge_coin_bonus"
                                    android:fontFamily="sans-serif" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_price_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_amount_price_text_size"
                                android:textColor="@color/recharge_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                        <!-- 1000 Coins Card -->
                        <LinearLayout
                            android:id="@+id/rechargeCard1000"
                            android:layout_width="@dimen/recharge_amount_card_width"
                            android:layout_height="@dimen/recharge_amount_card_height"
                            android:layout_marginStart="@dimen/recharge_amount_card_margin_between"
                            android:background="@drawable/recharge_amount_card_selector"
                            android:orientation="vertical"
                            android:clickable="true"
                            android:focusable="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_jinbi_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <ImageView
                                    android:layout_width="@dimen/recharge_amount_jinbi_icon_size"
                                    android:layout_height="@dimen/recharge_amount_jinbi_icon_size"
                                    android:src="@drawable/jinbi" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_coin_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_coin_text_size"
                                    android:textColor="@color/recharge_text_primary"
                                    android:fontFamily="sans-serif" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_bonus_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_bonus_text_size"
                                    android:textColor="@color/recharge_coin_bonus"
                                    android:fontFamily="sans-serif" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_price_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_amount_price_text_size"
                                android:textColor="@color/recharge_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- Second Row of Recharge Cards -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_amount_card_margin_between"
                    android:layout_marginStart="@dimen/recharge_amount_card_margin_start"
                    android:layout_marginEnd="@dimen/recharge_amount_card_margin_start"
                    android:orientation="horizontal">

                    <!-- Second Row -->
                    <LinearLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:orientation="horizontal">

                        <!-- 2000 Coins Card -->
                        <LinearLayout
                            android:id="@+id/rechargeCard2000"
                            android:layout_width="@dimen/recharge_amount_card_width"
                            android:layout_height="@dimen/recharge_amount_card_height"
                            android:background="@drawable/recharge_amount_card_selector"
                            android:orientation="vertical"
                            android:clickable="true"
                            android:focusable="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_jinbi_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <ImageView
                                    android:layout_width="@dimen/recharge_amount_jinbi_icon_size"
                                    android:layout_height="@dimen/recharge_amount_jinbi_icon_size"
                                    android:src="@drawable/jinbi" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_coin_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_coin_text_size"
                                    android:textColor="@color/recharge_text_primary"
                                    android:fontFamily="sans-serif" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_bonus_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_bonus_text_size"
                                    android:textColor="@color/recharge_coin_bonus"
                                    android:fontFamily="sans-serif" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_price_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_amount_price_text_size"
                                android:textColor="@color/recharge_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                        <!-- 3000 Coins Card -->
                        <LinearLayout
                            android:id="@+id/rechargeCard3000"
                            android:layout_width="@dimen/recharge_amount_card_width"
                            android:layout_height="@dimen/recharge_amount_card_height"
                            android:layout_marginStart="@dimen/recharge_amount_card_margin_between"
                            android:background="@drawable/recharge_amount_card_selector"
                            android:orientation="vertical"
                            android:clickable="true"
                            android:focusable="true">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_jinbi_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <ImageView
                                    android:layout_width="@dimen/recharge_amount_jinbi_icon_size"
                                    android:layout_height="@dimen/recharge_amount_jinbi_icon_size"
                                    android:src="@drawable/jinbi" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_coin_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_coin_text_size"
                                    android:textColor="@color/recharge_text_primary"
                                    android:fontFamily="sans-serif" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:layout_marginStart="@dimen/recharge_amount_bonus_margin_start"
                                    android:text=""
                                    android:textSize="@dimen/recharge_amount_bonus_text_size"
                                    android:textColor="@color/recharge_coin_bonus"
                                    android:fontFamily="sans-serif" />

                            </LinearLayout>

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_amount_price_margin_top"
                                android:layout_marginStart="@dimen/recharge_amount_jinbi_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_amount_price_text_size"
                                android:textColor="@color/recharge_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- Crown Section -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_crown_margin_top"
                    android:orientation="vertical"
                    android:gravity="center_horizontal">

                    <ImageView
                        android:layout_width="@dimen/recharge_crown_width"
                        android:layout_height="@dimen/recharge_crown_height"
                        android:src="@drawable/huangguan" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/recharge_crown_text_margin_top"
                        android:text="Subscribe for free to see all ?"
                        android:textSize="@dimen/recharge_crown_text_size"
                        android:textColor="@color/recharge_text_primary"
                        android:fontFamily="sans-serif" />

                    <ImageView
                        android:layout_width="@dimen/recharge_yellow_router_width"
                        android:layout_height="@dimen/recharge_yellow_router_height"
                        android:layout_marginTop="@dimen/recharge_yellow_router_margin_top"
                        android:src="@drawable/yellow_router" />

                </LinearLayout>

                <!-- VIP Cards Section -->
                <LinearLayout
                    android:id="@+id/rechargeVipCardsContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_vip_cards_margin_top"
                    android:layout_marginStart="@dimen/video_player_popup_margin"
                    android:layout_marginEnd="@dimen/video_player_popup_margin"
                    android:orientation="vertical">

                    <!-- Weekly Pass Pro Card -->
                    <LinearLayout
                        android:id="@+id/rechargeWeeklyCard"
                        android:layout_width="@dimen/recharge_vip_card_width"
                        android:layout_height="@dimen/recharge_vip_card_height"
                        android:layout_gravity="center_horizontal"
                        android:background="@drawable/recharge_vip_card_bg"
                        android:orientation="horizontal"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- Content -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingStart="@dimen/recharge_vip_title_margin_start"
                            android:paddingTop="@dimen/recharge_vip_title_margin_top">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_title_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-medium" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_vip_subtitle_margin_top"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_subtitle_text_size"
                                android:textColor="@color/recharge_vip_text_secondary"
                                android:fontFamily="sans-serif" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_vip_bonus_margin_top"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_bonus_text_size"
                                android:textColor="@color/recharge_vip_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                        <!-- Price Container -->
                        <LinearLayout
                            android:layout_width="@dimen/recharge_vip_price_container_width"
                            android:layout_height="@dimen/recharge_vip_price_container_height"
                            android:layout_marginTop="@dimen/recharge_vip_price_container_margin_top"
                            android:layout_marginEnd="@dimen/recharge_vip_price_container_margin_end"
                            android:background="@drawable/recharge_vip_price_container_bg"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_price_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-medium" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/recharge_vip_period_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_period_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-light" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- Monthly Pass Pro Card -->
                    <LinearLayout
                        android:id="@+id/rechargeMonthlyCard"
                        android:layout_width="@dimen/recharge_vip_card_width"
                        android:layout_height="@dimen/recharge_vip_card_height"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="@dimen/recharge_vip_card_margin_between"
                        android:background="@drawable/recharge_vip_card_bg"
                        android:orientation="horizontal"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- Content -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="match_parent"
                            android:layout_weight="1"
                            android:orientation="vertical"
                            android:paddingStart="@dimen/recharge_vip_title_margin_start"
                            android:paddingTop="@dimen/recharge_vip_title_margin_top">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_title_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-medium" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_vip_subtitle_margin_top"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_subtitle_text_size"
                                android:textColor="@color/recharge_vip_text_secondary"
                                android:fontFamily="sans-serif" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="@dimen/recharge_vip_bonus_margin_top"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_bonus_text_size"
                                android:textColor="@color/recharge_vip_text_tertiary"
                                android:fontFamily="sans-serif" />

                        </LinearLayout>

                        <!-- Price Container -->
                        <LinearLayout
                            android:layout_width="@dimen/recharge_vip_price_container_width"
                            android:layout_height="@dimen/recharge_vip_price_container_height"
                            android:layout_marginTop="@dimen/recharge_vip_price_container_margin_top"
                            android:layout_marginEnd="@dimen/recharge_vip_price_container_margin_end"
                            android:background="@drawable/recharge_vip_price_container_bg"
                            android:orientation="horizontal"
                            android:gravity="center">

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_price_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-medium" />

                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginStart="@dimen/recharge_vip_period_margin_start"
                                android:text=""
                                android:textSize="@dimen/recharge_vip_period_text_size"
                                android:textColor="@color/recharge_text_primary"
                                android:fontFamily="sans-serif-light" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- Loading UI -->
                <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
                    android:id="@+id/swipe_refresh_recharge_loading"
                    android:layout_width="match_parent"
                    android:layout_height="200dp"
                    android:layout_marginTop="@dimen/recharge_vip_cards_margin_top"
                    android:visibility="gone">

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@android:color/transparent" />

                </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

                <!-- Agreement Text -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/recharge_agreement_margin_top"
                    android:layout_marginStart="@dimen/video_player_popup_margin"
                    android:layout_marginEnd="@dimen/video_player_popup_margin"
                    android:orientation="horizontal"
                    android:gravity="center_horizontal">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Recharge representative agrees to our "
                        android:textSize="@dimen/recharge_agreement_text_size"
                        android:textColor="@color/recharge_agreement_text"
                        android:fontFamily="sans-serif-light" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Recharge Agreement"
                        android:textSize="@dimen/recharge_agreement_text_size"
                        android:textColor="@color/recharge_text_primary"
                        android:fontFamily="sans-serif" />

                </LinearLayout>

                    </LinearLayout>

                </ScrollView>

            </LinearLayout>

        </FrameLayout>

    </FrameLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
