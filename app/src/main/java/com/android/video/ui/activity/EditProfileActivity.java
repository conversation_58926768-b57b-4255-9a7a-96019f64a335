package com.android.video.ui.activity;

import android.Manifest;
import android.content.Intent;
import android.content.pm.PackageManager;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.provider.MediaStore;
import android.text.Editable;
import android.text.TextWatcher;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.PopupWindow;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import androidx.appcompat.app.AlertDialog;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.content.FileProvider;

import com.android.video.R;
import com.android.video.model.UserModel;
import com.android.video.utils.UserSessionUtils;
import com.android.video.utils.ImageCompressUtils;
import com.android.video.service.LocationService;
import com.android.video.service.IpLocationService;
import com.android.video.network.ProfileApiService;
import com.android.video.network.CommonApiService;
import com.android.video.utils.ApiHeaderUtils;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import jp.wasabeef.glide.transformations.CropCircleTransformation;

import java.io.File;
import java.io.IOException;

public class EditProfileActivity extends BaseFullScreenActivity {

    private ImageView ivBack;
    private RelativeLayout itemModifyAvatar;
    private ImageView ivAvatar;
    private EditText etNickname;
    private LinearLayout layoutCountryCode;
    private TextView tvCountryCode;
    private EditText etPhone;
    private TextView tvIpLocation;
    private TextView btnSave;

    private UserModel currentUser;

    // 权限请求码
    private static final int PERMISSION_REQUEST_CODE = 100;
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 101;

    // ActivityResultLauncher for camera and gallery
    private ActivityResultLauncher<Intent> cameraLauncher;
    private ActivityResultLauncher<Intent> galleryLauncher;

    // 临时文件URI
    private Uri tempImageUri;
    // 拍照时创建的临时文件
    private File tempCameraFile;

    // 位置服务
    private LocationService locationService;
    private IpLocationService ipLocationService;

    // API服务
    private ProfileApiService profileApiService;
    private CommonApiService commonApiService;

    // 当前选择的头像文件
    private File selectedAvatarFile;
    private String uploadedAvatarUrl;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_edit_profile);

        initViews();
        initLocationServices();
        initApiServices();
        setupActivityResultLaunchers();
        setupClickListeners();
        loadUserData();
    }

    @Override
    protected void onResume() {
        super.onResume();

        // 每次进入页面都刷新位置信息
        android.util.Log.d("EditProfile", "onResume - 刷新位置信息");
        loadLocationInfo();
    }

    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        itemModifyAvatar = findViewById(R.id.item_modify_avatar);
        ivAvatar = findViewById(R.id.iv_avatar);
        etNickname = findViewById(R.id.et_nickname);
        layoutCountryCode = findViewById(R.id.layout_country_code);
        tvCountryCode = findViewById(R.id.tv_country_code);
        etPhone = findViewById(R.id.et_phone);
        tvIpLocation = findViewById(R.id.tv_ip_location);
        btnSave = findViewById(R.id.btn_save);

        // 设置手机号输入框焦点监听，控制容器背景
        setupPhoneFocusListener();
    }

    private void initLocationServices() {
        locationService = new LocationService(this);
        ipLocationService = IpLocationService.getInstance();

        // 立即设置一个默认值，确保UI有内容显示
        if (tvIpLocation != null) {
            tvIpLocation.setText("China");
            android.util.Log.d("EditProfile", "设置默认位置: China");
        } else {
            android.util.Log.e("EditProfile", "tvIpLocation为null，无法设置默认位置");
        }
    }

    private void initApiServices() {
        profileApiService = ProfileApiService.getInstance();
        commonApiService = CommonApiService.getInstance();
    }

    private void setupPhoneFocusListener() {
        // 获取Contact information的容器RelativeLayout
        RelativeLayout contactContainer = (RelativeLayout) findViewById(R.id.layout_country_code).getParent();

        // 监听手机号输入框的焦点变化
        etPhone.setOnFocusChangeListener((v, hasFocus) -> {
            if (contactContainer != null) {
                // 根据焦点状态设置容器的选中状态，触发selector
                contactContainer.setSelected(hasFocus);
            }
        });
    }

    private void setupActivityResultLaunchers() {
        // 相机拍照结果处理
        cameraLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        // 拍照成功，设置头像
                        if (tempImageUri != null && tempCameraFile != null) {
                            ivAvatar.setImageURI(tempImageUri);
                            // 使用拍照保存的实际文件
                            try {
                                // 验证原始文件是否存在且有内容
                                if (tempCameraFile.exists() && tempCameraFile.length() > 0) {
                                    android.util.Log.d("EditProfile", "拍照成功，原始文件路径: " + tempCameraFile.getAbsolutePath());
                                    android.util.Log.d("EditProfile", "原始文件大小: " + tempCameraFile.length() + " bytes");

                                    // 压缩图片
                                    compressAndSetAvatar(tempCameraFile);
                                } else {
                                    android.util.Log.e("EditProfile", "拍照文件不存在或为空");
                                    Toast.makeText(this, "拍照失败，请重试", Toast.LENGTH_SHORT).show();
                                    selectedAvatarFile = null;
                                }
                            } catch (Exception e) {
                                android.util.Log.e("EditProfile", "获取拍照文件失败", e);
                                Toast.makeText(this, "拍照文件处理失败", Toast.LENGTH_SHORT).show();
                                selectedAvatarFile = null;
                            }
                        }
                    }
                }
        );

        // 相册选择结果处理
        galleryLauncher = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK && result.getData() != null) {
                        Uri selectedImageUri = result.getData().getData();
                        if (selectedImageUri != null) {
                            ivAvatar.setImageURI(selectedImageUri);
                            // 将URI转换为文件用于上传
                            try {
                                File originalFile = copyUriToFile(selectedImageUri);
                                android.util.Log.d("EditProfile", "相册选择成功，原始文件路径: " + originalFile.getAbsolutePath());
                                android.util.Log.d("EditProfile", "原始文件大小: " + originalFile.length() + " bytes");

                                // 压缩图片
                                compressAndSetAvatar(originalFile);
                            } catch (Exception e) {
                                android.util.Log.e("EditProfile", "处理相册图片失败", e);
                                Toast.makeText(this, "图片处理失败", Toast.LENGTH_SHORT).show();
                            }
                        }
                    }
                }
        );
    }

    private void setupClickListeners() {
        // 返回按钮
        ivBack.setOnClickListener(v -> finish());

        // 修改头像
        itemModifyAvatar.setOnClickListener(v -> {
            handleModifyAvatar();
        });

        // 国家代码选择
        layoutCountryCode.setOnClickListener(v -> {
            handleCountryCodeSelection();
        });

        // 保存按钮
        btnSave.setOnClickListener(v -> {
            handleSave();
        });

        // IP位置点击事件
        tvIpLocation.setOnClickListener(v -> {
            handleLocationClick();
        });

        // 昵称输入监听，控制保存按钮状态
        etNickname.addTextChangedListener(new TextWatcher() {
            @Override
            public void beforeTextChanged(CharSequence s, int start, int count, int after) {}

            @Override
            public void onTextChanged(CharSequence s, int start, int before, int count) {
                updateSaveButtonState();
            }

            @Override
            public void afterTextChanged(Editable s) {}
        });
    }

    private void loadUserData() {
        // 获取当前用户信息
        currentUser = UserSessionUtils.getCurrentUser(this);

        if (currentUser != null) {
            // 设置用户名（作为昵称）
            if (currentUser.getUsername() != null) {
                etNickname.setText(currentUser.getUsername());
            }

            // 设置手机号
            if (currentUser.getPhoneNumber() != null) {
                etPhone.setText(currentUser.getPhoneNumber());
            }

            // 设置头像（如果有的话）
            loadUserAvatar();

            // 设置用户token到ApiHeaderUtils（如果用户有token的话）
            // 注意：这里需要检查UserModel是否有getUserToken方法
            // 如果没有，我们需要从其他地方获取token
            android.util.Log.d("EditProfile", "当前用户: " + currentUser.getUsername());
        }

        // 动态获取位置信息
        loadLocationInfo();

        // 初始化保存按钮状态
        updateSaveButtonState();
    }

    /**
     * 加载用户头像
     */
    private void loadUserAvatar() {
        if (currentUser == null || ivAvatar == null) {
            return;
        }

        // 检查用户是否有头像URL
        if (currentUser.getAvatar() != null && !currentUser.getAvatar().trim().isEmpty()) {
            // 使用Glide加载网络头像，应用圆形变换
            Glide.with(this)
                    .load(currentUser.getAvatar())
                    .placeholder(R.drawable.profile_norentouxiang)
                    .error(R.drawable.profile_norentouxiang)
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .transform(new CropCircleTransformation())
                    .into(ivAvatar);

            android.util.Log.d("EditProfile", "加载用户头像: " + currentUser.getAvatar());
        } else {
            // 没有头像URL，使用默认头像
            ivAvatar.setImageResource(R.drawable.profile_norentouxiang);
            android.util.Log.d("EditProfile", "使用默认头像");
        }
    }

    /**
     * 加载位置信息
     */
    private void loadLocationInfo() {
        android.util.Log.d("EditProfile", "loadLocationInfo方法被调用");

        if (tvIpLocation == null) {
            android.util.Log.e("EditProfile", "tvIpLocation为null，无法显示位置信息");
            return;
        }

        // 设置加载状态
        tvIpLocation.setText("Loading...");
        android.util.Log.d("EditProfile", "开始获取位置信息，当前显示: " + tvIpLocation.getText());

        // 添加超时处理，如果5秒内没有响应，显示默认值
        Handler timeoutHandler = new Handler();
        timeoutHandler.postDelayed(() -> {
            if (tvIpLocation != null && "Loading...".equals(tvIpLocation.getText().toString())) {
                android.util.Log.w("EditProfile", "位置获取超时，使用默认值");
                tvIpLocation.setText("China");
            }
        }, 5000); // 5秒超时

        // 首先尝试获取IP位置（不需要权限）
        ipLocationService.getIpLocation(new IpLocationService.IpLocationCallback() {
            @Override
            public void onIpLocationSuccess(IpLocationService.IpLocationInfo ipLocationInfo) {
                if (tvIpLocation != null) {
                    // 取消超时处理
                    timeoutHandler.removeCallbacksAndMessages(null);

                    // 确保显示英文国家名称
                    String locationText = ipLocationInfo.getCountryDisplay();
                    tvIpLocation.setText(locationText);
                    android.util.Log.d("EditProfile", "IP位置获取成功: " + locationText);
                    android.util.Log.d("EditProfile", "完整位置信息: " + ipLocationInfo.toString());

                    // IP位置获取成功后，尝试获取GPS位置（如果有权限）
                    tryGetGpsLocation(locationText);
                }
            }

            @Override
            public void onIpLocationError(String errorMessage) {
                if (tvIpLocation != null) {
                    // 取消超时处理
                    timeoutHandler.removeCallbacksAndMessages(null);

                    android.util.Log.e("EditProfile", "IP位置获取失败: " + errorMessage);
                    tvIpLocation.setText("China"); // 使用默认值而不是Unknown

                    // IP位置获取失败，仍然尝试GPS位置
                    tryGetGpsLocation("China");
                }
            }
        });
    }

    /**
     * 尝试获取GPS位置
     */
    private void tryGetGpsLocation(String fallbackLocation) {
        if (locationService.hasLocationPermission()) {
            // 有位置权限，获取GPS位置
            locationService.getCurrentLocation(new LocationService.LocationCallback() {
                @Override
                public void onLocationSuccess(LocationService.LocationInfo locationInfo) {
                    String gpsLocation = locationInfo.getFormattedLocation();
                    tvIpLocation.setText(gpsLocation + " (GPS)");
                    android.util.Log.d("EditProfile", "GPS位置获取成功: " + gpsLocation);
                }

                @Override
                public void onLocationError(String errorMessage) {
                    android.util.Log.e("EditProfile", "GPS位置获取失败: " + errorMessage);
                    // GPS失败，保持IP位置或fallback
                    // tvIpLocation已经设置了IP位置，不需要再次设置
                }
            });
        } else {
            // 没有位置权限，询问用户是否要获取更精确的位置
            // 这里可以选择性地请求权限，或者保持IP位置
            android.util.Log.d("EditProfile", "没有位置权限，使用IP位置: " + fallbackLocation);
        }
    }

    /**
     * 处理位置点击事件
     */
    private void handleLocationClick() {
        if (!locationService.hasLocationPermission()) {
            // 没有位置权限，询问用户是否要获取更精确的位置
            AlertDialog.Builder builder = new AlertDialog.Builder(this);
            builder.setTitle("获取精确位置")
                    .setMessage("当前显示的是基于IP的大概位置。是否允许获取您的精确位置？")
                    .setPositiveButton("允许", (dialog, which) -> {
                        requestLocationPermission();
                    })
                    .setNegativeButton("取消", null)
                    .show();
        } else {
            // 已有权限，刷新位置
            refreshLocation();
        }
    }

    /**
     * 请求位置权限
     */
    private void requestLocationPermission() {
        String[] permissions = {
            android.Manifest.permission.ACCESS_FINE_LOCATION,
            android.Manifest.permission.ACCESS_COARSE_LOCATION
        };

        ActivityCompat.requestPermissions(this, permissions, LOCATION_PERMISSION_REQUEST_CODE);
    }

    /**
     * 刷新位置信息
     */
    private void refreshLocation() {
        tvIpLocation.setText("Updating...");

        locationService.getCurrentLocation(new LocationService.LocationCallback() {
            @Override
            public void onLocationSuccess(LocationService.LocationInfo locationInfo) {
                String gpsLocation = locationInfo.getFormattedLocation();
                tvIpLocation.setText(gpsLocation + " (GPS)");
                Toast.makeText(EditProfileActivity.this, "位置已更新", Toast.LENGTH_SHORT).show();
                android.util.Log.d("EditProfile", "位置刷新成功: " + gpsLocation);
            }

            @Override
            public void onLocationError(String errorMessage) {
                android.util.Log.e("EditProfile", "位置刷新失败: " + errorMessage);
                Toast.makeText(EditProfileActivity.this, "位置更新失败: " + errorMessage, Toast.LENGTH_SHORT).show();

                // 刷新失败，重新加载IP位置
                loadLocationInfo();
            }
        });
    }

    private void handleModifyAvatar() {
        // 检查权限
        if (!hasPermissions()) {
            requestPermissions();
            return;
        }

        // 显示头像选择对话框
        String[] options = {"拍摄", "从相册选取"};

        AlertDialog.Builder builder = new AlertDialog.Builder(this);
        builder.setTitle("选择头像")
                .setItems(options, (dialog, which) -> {
                    switch (which) {
                        case 0:
                            // 拍摄
                            openCamera();
                            break;
                        case 1:
                            // 从相册选取
                            openGallery();
                            break;
                    }
                })
                .setNegativeButton("取消", null)
                .show();
    }

    private void handleCountryCodeSelection() {
        // 创建下拉菜单
        View popupView = LayoutInflater.from(this).inflate(R.layout.popup_country_code, null);

        PopupWindow popupWindow = new PopupWindow(popupView,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT,
                true);

        // 设置背景，使点击外部可以关闭
        popupWindow.setBackgroundDrawable(getResources().getDrawable(android.R.color.transparent));
        popupWindow.setOutsideTouchable(true);

        // 设置点击事件
        TextView tvChina = popupView.findViewById(R.id.tv_country_china);
        TextView tvUsa = popupView.findViewById(R.id.tv_country_usa);
        TextView tvUk = popupView.findViewById(R.id.tv_country_uk);
        TextView tvJapan = popupView.findViewById(R.id.tv_country_japan);
        TextView tvKorea = popupView.findViewById(R.id.tv_country_korea);
        TextView tvOther = popupView.findViewById(R.id.tv_country_other);

        View.OnClickListener clickListener = v -> {
            TextView selectedView = (TextView) v;
            tvCountryCode.setText(selectedView.getText().toString());
            popupWindow.dismiss();
        };

        tvChina.setOnClickListener(clickListener);
        tvUsa.setOnClickListener(clickListener);
        tvUk.setOnClickListener(clickListener);
        tvJapan.setOnClickListener(clickListener);
        tvKorea.setOnClickListener(clickListener);
        tvOther.setOnClickListener(clickListener);

        // 在国家代码布局下方显示
        popupWindow.showAsDropDown(layoutCountryCode, 0, 0);
    }

    private void handleSave() {
        String nickname = etNickname.getText().toString().trim();
        String phone = etPhone.getText().toString().trim();
        String countryCode = tvCountryCode.getText().toString();

        // 验证输入
        if (nickname.isEmpty()) {
            Toast.makeText(this, "Please enter a nickname", Toast.LENGTH_SHORT).show();
            return;
        }

        if (phone.isEmpty()) {
            Toast.makeText(this, "Please enter a phone number", Toast.LENGTH_SHORT).show();
            return;
        }

        // 禁用保存按钮，显示加载状态
        btnSave.setEnabled(false);
        btnSave.setText("Saving...");

        // 如果有选择新头像，先上传头像
        if (selectedAvatarFile != null && selectedAvatarFile.exists()) {
            uploadAvatarAndUpdateProfile(nickname, phone);
        } else {
            // 没有新头像，直接更新个人信息
            updateProfile(nickname, phone, uploadedAvatarUrl);
        }
    }

    /**
     * 上传头像并更新个人信息
     */
    private void uploadAvatarAndUpdateProfile(String nickname, String phone) {
        android.util.Log.d("EditProfile", "开始上传头像: " + selectedAvatarFile.getAbsolutePath());

        commonApiService.uploadFile(selectedAvatarFile, new CommonApiService.FileUploadCallback() {
            @Override
            public void onSuccess(String fileUrl) {
                android.util.Log.d("EditProfile", "头像上传成功: " + fileUrl);
                uploadedAvatarUrl = fileUrl;
                // 头像上传成功，继续更新个人信息
                updateProfile(nickname, phone, fileUrl);
            }

            @Override
            public void onError(String errorMessage) {
                android.util.Log.e("EditProfile", "头像上传失败: " + errorMessage);
                // 头像上传失败，询问用户是否继续
                runOnUiThread(() -> {
                    new AlertDialog.Builder(EditProfileActivity.this)
                        .setTitle("头像上传失败")
                        .setMessage("头像上传失败：" + errorMessage + "\n\n是否继续保存其他信息？")
                        .setPositiveButton("继续保存", (dialog, which) -> {
                            updateProfile(nickname, phone, uploadedAvatarUrl);
                        })
                        .setNegativeButton("取消", (dialog, which) -> {
                            resetSaveButton();
                        })
                        .show();
                });
            }
        });
    }

    /**
     * 更新个人信息
     */
    private void updateProfile(String nickname, String phone, String headImg) {
        android.util.Log.d("EditProfile", "开始更新个人信息: " + nickname + ", " + phone + ", " + headImg);

        profileApiService.updateMyInformation(nickname, phone, headImg, new ProfileApiService.UpdateProfileCallback() {
            @Override
            public void onSuccess() {
                android.util.Log.d("EditProfile", "个人信息更新成功");
                runOnUiThread(() -> {
                    // 更新本地用户信息
                    if (currentUser != null) {
                        currentUser.setUsername(nickname);
                        currentUser.setPhoneNumber(phone);
                        if (headImg != null && !headImg.isEmpty()) {
                            // 这里可以设置头像URL，如果UserModel支持的话
                        }

                        // 保存到本地存储
                        UserSessionUtils.saveUserSession(EditProfileActivity.this, currentUser);
                    }

                    Toast.makeText(EditProfileActivity.this, "Profile updated successfully", Toast.LENGTH_SHORT).show();

                    // 返回上一页
                    finish();
                });
            }

            @Override
            public void onError(String errorMessage) {
                android.util.Log.e("EditProfile", "个人信息更新失败: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(EditProfileActivity.this, "更新失败: " + errorMessage, Toast.LENGTH_LONG).show();
                    resetSaveButton();
                });
            }
        });
    }

    /**
     * 重置保存按钮状态
     */
    private void resetSaveButton() {
        btnSave.setEnabled(true);
        btnSave.setText("Save");
    }

    /**
     * 更新保存按钮状态
     */
    private void updateSaveButtonState() {
        String nickname = etNickname.getText().toString().trim();

        if (nickname.isEmpty()) {
            // 名称为空，按钮置灰
            btnSave.setEnabled(false);
            btnSave.setAlpha(0.5f);
            btnSave.setBackgroundResource(R.drawable.edit_profile_save_button_disabled_bg);
        } else {
            // 名称不为空，按钮可用
            btnSave.setEnabled(true);
            btnSave.setAlpha(1.0f);
            btnSave.setBackgroundResource(R.drawable.edit_profile_save_button_bg);
        }
    }

    /**
     * 检查是否有必要的权限
     */
    private boolean hasPermissions() {
        return ContextCompat.checkSelfPermission(this, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED &&
               ContextCompat.checkSelfPermission(this, Manifest.permission.READ_EXTERNAL_STORAGE) == PackageManager.PERMISSION_GRANTED;
    }

    /**
     * 请求权限
     */
    private void requestPermissions() {
        String[] permissions = {
                Manifest.permission.CAMERA,
                Manifest.permission.READ_EXTERNAL_STORAGE,
                Manifest.permission.WRITE_EXTERNAL_STORAGE
        };

        ActivityCompat.requestPermissions(this, permissions, PERMISSION_REQUEST_CODE);
    }

    /**
     * 打开相机
     */
    private void openCamera() {
        try {
            Intent cameraIntent = new Intent(MediaStore.ACTION_IMAGE_CAPTURE);

            // 创建临时文件，使用时间戳确保文件名唯一
            String fileName = "temp_avatar_" + System.currentTimeMillis() + ".jpg";
            tempCameraFile = new File(getExternalFilesDir(null), fileName);

            // 确保父目录存在
            File parentDir = tempCameraFile.getParentFile();
            if (parentDir != null && !parentDir.exists()) {
                parentDir.mkdirs();
            }

            tempImageUri = FileProvider.getUriForFile(this,
                    getApplicationContext().getPackageName() + ".fileprovider", tempCameraFile);

            cameraIntent.putExtra(MediaStore.EXTRA_OUTPUT, tempImageUri);

            android.util.Log.d("EditProfile", "准备拍照，临时文件路径: " + tempCameraFile.getAbsolutePath());
            cameraLauncher.launch(cameraIntent);

        } catch (Exception e) {
            android.util.Log.e("EditProfile", "打开相机失败", e);
            Toast.makeText(this, "无法打开相机: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 打开相册
     */
    private void openGallery() {
        Intent galleryIntent = new Intent(Intent.ACTION_PICK, MediaStore.Images.Media.EXTERNAL_CONTENT_URI);
        galleryLauncher.launch(galleryIntent);
    }

    @Override
    public void onRequestPermissionsResult(int requestCode, String[] permissions, int[] grantResults) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults);

        if (requestCode == PERMISSION_REQUEST_CODE) {
            boolean allPermissionsGranted = true;
            for (int result : grantResults) {
                if (result != PackageManager.PERMISSION_GRANTED) {
                    allPermissionsGranted = false;
                    break;
                }
            }

            if (allPermissionsGranted) {
                Toast.makeText(this, "权限已获取，请重新点击头像", Toast.LENGTH_SHORT).show();
            } else {
                Toast.makeText(this, "需要相机和存储权限才能修改头像", Toast.LENGTH_SHORT).show();
            }
        } else if (requestCode == LOCATION_PERMISSION_REQUEST_CODE) {
            boolean locationPermissionGranted = false;
            for (int result : grantResults) {
                if (result == PackageManager.PERMISSION_GRANTED) {
                    locationPermissionGranted = true;
                    break;
                }
            }

            if (locationPermissionGranted) {
                // 位置权限已授予，获取精确位置
                Toast.makeText(this, "正在获取精确位置...", Toast.LENGTH_SHORT).show();
                refreshLocation();
            } else {
                // 位置权限被拒绝
                Toast.makeText(this, "位置权限被拒绝，将继续使用IP位置", Toast.LENGTH_SHORT).show();
            }
        }
    }

    /**
     * 将URI转换为文件
     */
    private File copyUriToFile(Uri uri) throws IOException {
        File tempFile = new File(getExternalFilesDir(null), "selected_avatar_" + System.currentTimeMillis() + ".jpg");

        try (java.io.InputStream inputStream = getContentResolver().openInputStream(uri);
             java.io.FileOutputStream outputStream = new java.io.FileOutputStream(tempFile)) {

            if (inputStream == null) {
                throw new IOException("无法打开输入流");
            }

            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        }

        return tempFile;
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 释放位置服务资源
        if (locationService != null) {
            locationService.release();
        }

        if (ipLocationService != null) {
            ipLocationService.release();
        }

        // 清理临时文件
        if (selectedAvatarFile != null && selectedAvatarFile.exists()) {
            selectedAvatarFile.delete();
        }
    }

    /**
     * 压缩并设置头像
     */
    private void compressAndSetAvatar(File originalFile) {
        // 在后台线程中进行压缩
        new Thread(() -> {
            try {
                // 创建压缩后的文件
                String compressedFileName = "compressed_avatar_" + System.currentTimeMillis() + ".jpg";
                File compressedFile = new File(getExternalFilesDir(null), compressedFileName);

                android.util.Log.d("EditProfile", "开始压缩图片...");

                // 压缩图片（限制为1MB）
                boolean success = ImageCompressUtils.compressImage(originalFile, compressedFile, 1024 * 1024);

                // 在主线程中更新UI
                runOnUiThread(() -> {
                    if (success && compressedFile.exists()) {
                        selectedAvatarFile = compressedFile;
                        android.util.Log.d("EditProfile", "图片压缩成功!");
                        android.util.Log.d("EditProfile", "压缩前大小: " + originalFile.length() + " bytes");
                        android.util.Log.d("EditProfile", "压缩后大小: " + compressedFile.length() + " bytes");
                        android.util.Log.d("EditProfile", "压缩后文件路径: " + compressedFile.getAbsolutePath());

                        Toast.makeText(this, "头像已选择并压缩，保存时将上传", Toast.LENGTH_SHORT).show();
                    } else {
                        android.util.Log.e("EditProfile", "图片压缩失败");
                        Toast.makeText(this, "图片压缩失败，请重试", Toast.LENGTH_SHORT).show();
                        selectedAvatarFile = null;
                    }
                });

            } catch (Exception e) {
                android.util.Log.e("EditProfile", "图片压缩过程中发生错误", e);
                runOnUiThread(() -> {
                    Toast.makeText(this, "图片处理失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
                    selectedAvatarFile = null;
                });
            }
        }).start();
    }
}
