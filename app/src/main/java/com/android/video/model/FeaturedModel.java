package com.android.video.model;

import com.android.video.model.response.CategoryFilmModel;
import com.google.gson.annotations.SerializedName;
import com.android.video.model.response.CategoryWithFilmsModel;
import java.util.List;

/**
 * 推荐位数据模型
 * <p>
 * 用于映射推荐位API返回的推荐区块JSON数据结构。
 * 支持多种推荐位类型：普通推荐位、分类推荐位、播放历史、猜你喜欢、今日热映、最受欢迎、即将来袭等。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class FeaturedModel {

    /**
     * 推荐位ID
     */
    @SerializedName("featuredId")
    private String featuredId;

    /**
     * 推荐位名称
     * 如：Categories、Continue Watching、Coming Soon、Best For You、Today's Hot、Most Popular等
     */
    @SerializedName("featuredName")
    private String featuredName;

    /**
     * 普通推荐位影片列表
     * 用于普通推荐位类型
     */
    @SerializedName("films")
    private List<FeaturedFilmModel> films;

    /**
     * 分类列表
     * 用于分类类型推荐位（Categories）
     */
    @SerializedName("categories")
    private List<CategoryWithFilmsModel> categories;

    /**
     * 播放历史列表
     * 用于播放历史类型推荐位（Continue Watching）
     */
    @SerializedName("playHistoryList")
    private List<MyHistoryItemModel> playHistoryList;

    /**
     * 猜你喜欢列表
     * 用于猜你喜欢类型推荐位（Best For You）
     */
    @SerializedName("guessYouLikeList")
    private List<GuessYouLikeModel> guessYouLikeList;

    /**
     * 今日热映列表
     * 用于今日热映类型推荐位（Today's Hot）
     */
    @SerializedName("todayHotReleaseList")
    private List<TodayHotReleaseModel> todayHotReleaseList;

    /**
     * 最受欢迎列表
     * 用于最受欢迎类型推荐位（Most Popular）
     */
    @SerializedName("mostPopularList")
    private List<MostPopularModel> mostPopularList;

    /**
     * 即将来袭列表
     * 用于即将来袭类型推荐位（Coming Soon）
     */
    @SerializedName("worthWaitingList")
    private List<WorthWaitingModel> worthWaitingList;

    /**
     * 默认构造函数
     */
    public FeaturedModel() {
    }

    /**
     * 普通推荐位构造函数
     *
     * @param featuredId 推荐位ID
     * @param featuredName 推荐位名称
     * @param films 影片列表
     */
    public FeaturedModel(String featuredId, String featuredName, List<FeaturedFilmModel> films) {
        this.featuredId = featuredId;
        this.featuredName = featuredName;
        this.films = films;
    }

    /**
     * 完整构造函数
     *
     * @param featuredId 推荐位ID
     * @param featuredName 推荐位名称
     * @param films 普通推荐位影片列表
     * @param categories 分类列表
     * @param playHistoryList 播放历史列表
     * @param guessYouLikeList 猜你喜欢列表
     * @param todayHotReleaseList 今日热映列表
     * @param mostPopularList 最受欢迎列表
     * @param worthWaitingList 即将来袭列表
     */
    public FeaturedModel(String featuredId, String featuredName, List<FeaturedFilmModel> films,
                        List<CategoryWithFilmsModel> categories, List<MyHistoryItemModel> playHistoryList,
                        List<GuessYouLikeModel> guessYouLikeList, List<TodayHotReleaseModel> todayHotReleaseList,
                        List<MostPopularModel> mostPopularList, List<WorthWaitingModel> worthWaitingList) {
        this.featuredId = featuredId;
        this.featuredName = featuredName;
        this.films = films;
        this.categories = categories;
        this.playHistoryList = playHistoryList;
        this.guessYouLikeList = guessYouLikeList;
        this.todayHotReleaseList = todayHotReleaseList;
        this.mostPopularList = mostPopularList;
        this.worthWaitingList = worthWaitingList;
    }

    // ========== Getter和Setter方法 ==========

    public String getFeaturedId() {
        return featuredId;
    }

    public void setFeaturedId(String featuredId) {
        this.featuredId = featuredId;
    }

    public String getFeaturedName() {
        return featuredName;
    }

    public void setFeaturedName(String featuredName) {
        this.featuredName = featuredName;
    }

    public List<FeaturedFilmModel> getFilms() {
        return films;
    }

    public void setFilms(List<FeaturedFilmModel> films) {
        this.films = films;
    }

    public List<CategoryWithFilmsModel> getCategories() {
        return categories;
    }

    public void setCategories(List<CategoryWithFilmsModel> categories) {
        this.categories = categories;
    }

    public List<MyHistoryItemModel> getPlayHistoryList() {
        return playHistoryList;
    }

    public void setPlayHistoryList(List<MyHistoryItemModel> playHistoryList) {
        this.playHistoryList = playHistoryList;
    }

    public List<GuessYouLikeModel> getGuessYouLikeList() {
        return guessYouLikeList;
    }

    public void setGuessYouLikeList(List<GuessYouLikeModel> guessYouLikeList) {
        this.guessYouLikeList = guessYouLikeList;
    }

    public List<TodayHotReleaseModel> getTodayHotReleaseList() {
        return todayHotReleaseList;
    }

    public void setTodayHotReleaseList(List<TodayHotReleaseModel> todayHotReleaseList) {
        this.todayHotReleaseList = todayHotReleaseList;
    }

    public List<MostPopularModel> getMostPopularList() {
        return mostPopularList;
    }

    public void setMostPopularList(List<MostPopularModel> mostPopularList) {
        this.mostPopularList = mostPopularList;
    }

    public List<WorthWaitingModel> getWorthWaitingList() {
        return worthWaitingList;
    }

    public void setWorthWaitingList(List<WorthWaitingModel> worthWaitingList) {
        this.worthWaitingList = worthWaitingList;
    }

    // ========== 便利方法 ==========

    /**
     * 检查是否有影片
     *
     * @return 如果有影片返回true，否则返回false
     */
    public boolean hasFilms() {
        return films != null && !films.isEmpty();
    }

    /**
     * 获取影片数量
     *
     * @return 影片数量
     */
    public int getFilmCount() {
        return films != null ? films.size() : 0;
    }

    /**
     * 检查是否有分类数据
     *
     * @return 如果有分类数据返回true，否则返回false
     */
    public boolean hasCategories() {
        return categories != null && !categories.isEmpty();
    }

    /**
     * 检查是否有播放历史数据
     *
     * @return 如果有播放历史数据返回true，否则返回false
     */
    public boolean hasPlayHistory() {
        return playHistoryList != null && !playHistoryList.isEmpty();
    }

    /**
     * 检查是否有猜你喜欢数据
     *
     * @return 如果有猜你喜欢数据返回true，否则返回false
     */
    public boolean hasGuessYouLike() {
        return guessYouLikeList != null && !guessYouLikeList.isEmpty();
    }

    /**
     * 检查是否有今日热映数据
     *
     * @return 如果有今日热映数据返回true，否则返回false
     */
    public boolean hasTodayHotRelease() {
        return todayHotReleaseList != null && !todayHotReleaseList.isEmpty();
    }

    /**
     * 检查是否有最受欢迎数据
     *
     * @return 如果有最受欢迎数据返回true，否则返回false
     */
    public boolean hasMostPopular() {
        return mostPopularList != null && !mostPopularList.isEmpty();
    }

    /**
     * 检查是否有即将来袭数据
     *
     * @return 如果有即将来袭数据返回true，否则返回false
     */
    public boolean hasWorthWaiting() {
        return worthWaitingList != null && !worthWaitingList.isEmpty();
    }

    /**
     * 检查推荐位是否有任何数据
     *
     * @return 如果有任何数据返回true，否则返回false
     */
    public boolean hasAnyData() {
        return hasFilms() || hasCategories() || hasPlayHistory() ||
               hasGuessYouLike() || hasTodayHotRelease() || hasMostPopular() || hasWorthWaiting();
    }

    /**
     * 检查是否为Categories推荐位
     *
     * @return 如果是Categories推荐位返回true，否则返回false
     */
    public boolean isCategoriesFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("categories");
    }

    /**
     * 检查是否为Continue Watching推荐位
     *
     * @return 如果是Continue Watching推荐位返回true，否则返回false
     */
    public boolean isContinueWatchingFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("continue watching");
    }

    /**
     * 检查是否为Coming Soon推荐位
     *
     * @return 如果是Coming Soon推荐位返回true，否则返回false
     */
    public boolean isComingSoonFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("coming soon");
    }

    /**
     * 检查是否为Best For You推荐位
     *
     * @return 如果是Best For You推荐位返回true，否则返回false
     */
    public boolean isBestForYouFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("best for you");
    }

    /**
     * 检查是否为Today's Hot推荐位
     *
     * @return 如果是Today's Hot推荐位返回true，否则返回false
     */
    public boolean isTodayHotFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("today's hot");
    }

    /**
     * 检查是否为Most Popular推荐位
     *
     * @return 如果是Most Popular推荐位返回true，否则返回false
     */
    public boolean isMostPopularFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("most popular");
    }

    /**
     * 检查是否为Popular series推荐位（向后兼容）
     *
     * @return 如果是Popular series推荐位返回true，否则返回false
     */
    public boolean isPopularSeriesFeatured() {
        return featuredName != null && featuredName.toLowerCase().contains("popular series");
    }

    /**
     * 根据语言类型过滤影片
     *
     * @param languageType 语言类型 (1=英语, 2=俄语)
     * @return 指定语言的影片列表
     */
    public List<FeaturedFilmModel> getFilmsByLanguage(int languageType) {
        if (films == null) return null;

        return films.stream()
                .filter(film -> film.getLanguageType() != null && film.getLanguageType() == languageType)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取有封面的影片列表
     *
     * @return 有封面的影片列表
     */
    public List<FeaturedFilmModel> getFilmsWithCover() {
        if (films == null) return null;

        return films.stream()
                .filter(FeaturedFilmModel::hasCover)
                .collect(java.util.stream.Collectors.toList());
    }

    /**
     * 获取推荐位的数据类型
     *
     * @return 推荐位数据类型描述
     */
    public String getDataType() {
        if (hasFilms()) return "films";
        if (hasCategories()) return "categories";
        if (hasPlayHistory()) return "playHistory";
        if (hasGuessYouLike()) return "guessYouLike";
        if (hasTodayHotRelease()) return "todayHotRelease";
        if (hasMostPopular()) return "mostPopular";
        if (hasWorthWaiting()) return "worthWaiting";
        return "empty";
    }

    /**
     * 获取推荐位的数据数量
     *
     * @return 数据数量
     */
    public int getDataCount() {
        if (hasFilms()) return films.size();
        if (hasCategories()) return categories.size();
        if (hasPlayHistory()) return playHistoryList.size();
        if (hasGuessYouLike()) return guessYouLikeList.size();
        if (hasTodayHotRelease()) return todayHotReleaseList.size();
        if (hasMostPopular()) return mostPopularList.size();
        if (hasWorthWaiting()) return worthWaitingList.size();
        return 0;
    }

    /**
     * 将推荐位数据转换为VideoModel列表
     *
     * @return VideoModel列表
     */
    public List<VideoModel> toVideoModelList() {
        List<VideoModel> videoList = new java.util.ArrayList<>();

        // 处理普通影片列表
        if (hasFilms()) {
            for (FeaturedFilmModel film : films) {
                VideoModel video = film.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        // 处理分类数据
        if (hasCategories()) {
            for (CategoryWithFilmsModel category : categories) {
                if (category.hasFilms()) {
                    for (CategoryFilmModel film : category.getFilms()) {
                        VideoModel video = film.toVideoModel();
                        if (video != null) {
                            videoList.add(video);
                        }
                    }
                }
            }
        }

        // 处理播放历史
        if (hasPlayHistory()) {
            for (MyHistoryItemModel history : playHistoryList) {
                VideoModel video = history.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        // 处理猜你喜欢
        if (hasGuessYouLike()) {
            for (GuessYouLikeModel guess : guessYouLikeList) {
                VideoModel video = guess.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        // 处理今日热映
        if (hasTodayHotRelease()) {
            for (TodayHotReleaseModel hot : todayHotReleaseList) {
                VideoModel video = hot.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        // 处理最受欢迎
        if (hasMostPopular()) {
            for (MostPopularModel popular : mostPopularList) {
                VideoModel video = popular.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        // 处理即将来袭
        if (hasWorthWaiting()) {
            for (WorthWaitingModel worth : worthWaitingList) {
                VideoModel video = worth.toVideoModel();
                if (video != null) {
                    videoList.add(video);
                }
            }
        }

        return videoList;
    }

    @Override
    public String toString() {
        return "FeaturedModel{" +
                "featuredId='" + featuredId + '\'' +
                ", featuredName='" + featuredName + '\'' +
                ", films=" + (films != null ? films.size() + " films" : "null") +
                '}';
    }
}
