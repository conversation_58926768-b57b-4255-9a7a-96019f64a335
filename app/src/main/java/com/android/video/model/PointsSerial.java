package com.android.video.model;

/**
 * 积分流水模型
 */
public class PointsSerial {
    private String id;
    private String userId;
    private String userName;
    private int billType;
    private int points;
    private String bizType;
    private String bizDesc;
    private int isDelete;
    private String createTime;
    private String updateTime;
    private int prePointsBalance;
    private int postPointsBalance;

    /**
     * 构造方法 - 用于API响应数据
     */
    public PointsSerial(String id, String userId, String userName, int billType, int points,
                       String bizType, String bizDesc, int isDelete, String createTime,
                       String updateTime, int prePointsBalance, int postPointsBalance) {
        this.id = id;
        this.userId = userId;
        this.userName = userName;
        this.billType = billType;
        this.points = points;
        this.bizType = bizType;
        this.bizDesc = bizDesc;
        this.isDelete = isDelete;
        this.createTime = createTime;
        this.updateTime = updateTime;
        this.prePointsBalance = prePointsBalance;
        this.postPointsBalance = postPointsBalance;
    }

    /**
     * 默认构造方法
     */
    public PointsSerial() {
    }

    // Getter和Setter方法

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getUserName() {
        return userName;
    }

    public void setUserName(String userName) {
        this.userName = userName;
    }

    public int getBillType() {
        return billType;
    }

    public void setBillType(int billType) {
        this.billType = billType;
    }

    public int getPoints() {
        return points;
    }

    public void setPoints(int points) {
        this.points = points;
    }

    public String getBizType() {
        return bizType;
    }

    public void setBizType(String bizType) {
        this.bizType = bizType;
    }

    public String getBizDesc() {
        return bizDesc;
    }

    public void setBizDesc(String bizDesc) {
        this.bizDesc = bizDesc;
    }

    public int getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(int isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public int getPrePointsBalance() {
        return prePointsBalance;
    }

    public void setPrePointsBalance(int prePointsBalance) {
        this.prePointsBalance = prePointsBalance;
    }

    public int getPostPointsBalance() {
        return postPointsBalance;
    }

    public void setPostPointsBalance(int postPointsBalance) {
        this.postPointsBalance = postPointsBalance;
    }

    // 业务方法

    /**
     * 检查是否为收入类型
     */
    public boolean isIncome() {
        return billType == 1;
    }

    /**
     * 检查是否为支出类型
     */
    public boolean isExpense() {
        return billType == 2;
    }

    /**
     * 获取积分变动的显示文本
     */
    public String getPointsDisplayText() {
        if (isIncome()) {
            return "+" + points;
        } else {
            return "-" + Math.abs(points);
        }
    }

    /**
     * 获取流水类型描述
     */
    public String getBillTypeDescription() {
        switch (billType) {
            case 1:
                return "收入";
            case 2:
                return "支出";
            default:
                return "未知";
        }
    }

    /**
     * 获取格式化的创建时间（AM/PM格式）
     */
    public String getFormattedCreateTime() {
        if (createTime == null || createTime.isEmpty()) {
            return createTime;
        }

        try {
            // API返回格式：2025-07-24 22:23:07
            java.text.SimpleDateFormat inputFormat = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault());
            // UI显示格式：06/18/2025 10:03AM - 强制使用英文AM/PM
            java.text.SimpleDateFormat outputFormat = new java.text.SimpleDateFormat("MM/dd/yyyy hh:mma", java.util.Locale.ENGLISH);

            java.util.Date date = inputFormat.parse(createTime);
            if (date != null) {
                return outputFormat.format(date);
            }
        } catch (Exception e) {
            // 解析失败时返回截取的字符串（兼容旧格式）
            if (createTime.length() >= 16) {
                return createTime.substring(0, 16);
            }
        }

        return createTime;
    }

    /**
     * 检查流水是否有效
     */
    public boolean isValid() {
        return isDelete == 1;
    }

    @Override
    public String toString() {
        return "PointsSerial{" +
                "id='" + id + '\'' +
                ", userId='" + userId + '\'' +
                ", userName='" + userName + '\'' +
                ", billType=" + billType +
                ", points=" + points +
                ", bizType='" + bizType + '\'' +
                ", bizDesc='" + bizDesc + '\'' +
                ", isDelete=" + isDelete +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                ", prePointsBalance=" + prePointsBalance +
                ", postPointsBalance=" + postPointsBalance +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;

        PointsSerial that = (PointsSerial) o;

        return id != null ? id.equals(that.id) : that.id == null;
    }

    @Override
    public int hashCode() {
        return id != null ? id.hashCode() : 0;
    }
}
