package com.android.video.config;

import com.android.video.BuildConfig;

/**
 * 环境配置管理工具类 - 处理不同环境的配置切换
 * <AUTHOR> Team
 */
public class EnvironmentConfigUtils {

    /**
     * 环境类型枚举
     */
    public enum Environment {
        DEV("dev"),
        PROD("prod"),
        TEST("test");

        private final String value;

        Environment(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static Environment fromString(String value) {
            for (Environment env : Environment.values()) {
                if (env.value.equalsIgnoreCase(value)) {
                    return env;
                }
            }
            return DEV; // 默认返回开发环境
        }
    }

    /**
     * 获取当前环境
     * @return 当前环境枚举
     */
    public static Environment getCurrentEnvironment() {
        return Environment.fromString(BuildConfig.ENVIRONMENT);
    }

    /**
     * 判断是否为开发环境
     * @return 是否为开发环境
     */
    public static boolean isDevelopment() {
        return getCurrentEnvironment() == Environment.DEV;
    }

    /**
     * 判断是否为生产环境
     * @return 是否为生产环境
     */
    public static boolean isProduction() {
        return getCurrentEnvironment() == Environment.PROD;
    }

    /**
     * 判断是否为测试环境
     * @return 是否为测试环境
     */
    public static boolean isTest() {
        return getCurrentEnvironment() == Environment.TEST;
    }

    /**
     * 判断是否启用API模式
     * @return 是否启用API模式
     */
    public static boolean isApiModeEnabled() {
        return BuildConfig.ENABLE_API_MODE;
    }

    /**
     * 获取API基础URL
     * @return API基础URL
     */
    public static String getApiBaseUrl() {
        return BuildConfig.API_BASE_URL;
    }

    /**
     * 获取当前环境名称
     * @return 环境名称字符串
     */
    public static String getEnvironmentName() {
        return getCurrentEnvironment().getValue();
    }

    /**
     * 判断是否为调试模式
     * @return 是否为调试模式
     */
    public static boolean isDebugMode() {
        return BuildConfig.DEBUG;
    }

    /**
     * 获取环境配置信息（用于调试）
     * @return 环境配置信息字符串
     */
    public static String getEnvironmentInfo() {
        StringBuilder info = new StringBuilder();
        info.append("Environment: ").append(getEnvironmentName()).append("\n");
        info.append("API Mode: ").append(isApiModeEnabled() ? "Enabled" : "Disabled").append("\n");
        info.append("API Base URL: ").append(getApiBaseUrl()).append("\n");
        info.append("Debug Mode: ").append(isDebugMode() ? "Enabled" : "Disabled");
        return info.toString();
    }
}
