package com.android.video.constants;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.model.response.DownloadListResponseModel;
import com.android.video.model.response.DownloadChapterResponseModel;
import com.android.video.model.response.DeleteChapterResponseModel;
import com.android.video.network.ApiClientUtils;
import com.android.video.utils.ApiHeaderUtils;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 下载模块API常量工具类
 * <p>
 * 提供下载相关的API常量定义和调用方法。
 * 继承自BaseApiConstantsUtils，遵循统一的API规范。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class DownloadApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== API路径常量 ==========

    /**
     * 获取下载列表API路径
     */
    public static final String API_GET_DOWNLOAD_LIST = "/app/download/list";

    /**
     * 获取下载章节详情API路径
     */
    public static final String API_GET_DOWNLOAD_CHAPTER_DETAILS = "/app/download/chapter/details";

    /**
     * 删除下载短剧API路径
     */
    public static final String API_DELETE_DOWNLOAD_FILM = "/app/download/film/delete";

    /**
     * 删除下载章节API路径
     */
    public static final String API_DELETE_DOWNLOAD_CHAPTER = "/app/download/chapter/delete";

    /**
     * 确认下载API路径
     */
    public static final String API_CONFIRM_DOWNLOAD = "/app/player/confirmDownload";

    // ========== 参数常量 ==========

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 短剧语言信息ID参数名
     */
    public static final String PARAM_FILM_LANGUAGE_INFO_ID = "filmLanguageInfoId";

    /**
     * 下载记录ID参数名
     */
    public static final String PARAM_DOWNLOAD_RECORD_ID = "downloadRecordId";

    /**
     * 文件校验和参数名
     */
    public static final String PARAM_FILE_CHECKSUM = "fileChecksum";

    /**
     * 本地路径参数名
     */
    public static final String PARAM_LOCAL_PATH = "localPath";

    // ========== 下载API调用方法 ==========

    private static final String TAG = "DownloadApiConstantsUtils";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");
    private static OkHttpClient client;
    private static Handler mainHandler;
    private static ExecutorService executor;
    private static Gson gson;

    /**
     * 下载列表获取回调接口
     */
    public interface DownloadListCallback {
        /**
         * 获取下载列表成功
         * @param response 下载列表响应模型
         */
        void onSuccess(DownloadListResponseModel response);

        /**
         * 获取下载列表失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 下载章节详情获取回调接口
     */
    public interface DownloadChapterCallback {
        /**
         * 获取下载章节详情成功
         * @param response 下载章节详情响应模型
         */
        void onSuccess(DownloadChapterResponseModel response);

        /**
         * 获取下载章节详情失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 删除短剧回调接口
     */
    public interface DeleteFilmCallback {
        /**
         * 删除短剧成功
         * @param response 删除响应模型
         */
        void onSuccess(DeleteChapterResponseModel response);

        /**
         * 删除短剧失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 删除章节回调接口
     */
    public interface DeleteChapterCallback {
        /**
         * 删除章节成功
         * @param response 删除响应模型
         */
        void onSuccess(DeleteChapterResponseModel response);

        /**
         * 删除章节失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 初始化网络组件
     */
    private static void initNetworkComponents() {
        if (client == null) {
            try {
                client = ApiClientUtils.getHttpClient();
            } catch (IllegalStateException e) {
                Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
                client = new OkHttpClient();
            }
            mainHandler = new Handler(Looper.getMainLooper());
            executor = Executors.newCachedThreadPool();
            gson = new GsonBuilder()
                    .setLenient()
                    .create();
        }
    }

    /**
     * 获取下载列表
     * <p>
     * 异步获取用户的下载列表，支持分页查询。
     * </p>
     *
     * @param page 页码，从1开始
     * @param size 每页数量
     * @param callback 回调接口
     */
    public static void getDownloadList(int page, int size, DownloadListCallback callback) {
        // 初始化网络组件
        initNetworkComponents();

        // 参数验证
        if (page < 1) page = 1;
        if (size < 1 || size > 100) size = 10;

        // 构建API URL - 使用全局配置
        String apiUrl = buildApiUrl(EnvironmentConfigUtils.getApiBaseUrl(), API_GET_DOWNLOAD_LIST) +
                "?" + PARAM_PAGE + "=" + page +
                "&" + PARAM_SIZE + "=" + size;

        // 获取请求头
        Map<String, String> headers = ApiHeaderUtils.getDefaultHeaders();

        // 构建GET请求
        Request.Builder requestBuilder = new Request.Builder()
                .url(apiUrl)
                .get();

        // 添加请求头
        for (Map.Entry<String, String> header : headers.entrySet()) {
            requestBuilder.addHeader(header.getKey(), header.getValue());
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "发送下载列表请求: " + apiUrl);

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "下载列表请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 下载列表API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 使用Gson解析响应数据
                            DownloadListResponseModel responseModel = gson.fromJson(responseBody, DownloadListResponseModel.class);
                            if (responseModel != null) {
                                mainHandler.post(() -> {
                                    if (callback != null) {
                                        callback.onSuccess(responseModel);
                                    }
                                });
                            } else {
                                mainHandler.post(() -> {
                                    if (callback != null) {
                                        callback.onError("响应数据解析失败");
                                    }
                                });
                            }
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "下载列表响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }
}
