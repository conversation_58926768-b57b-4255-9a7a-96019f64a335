<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:background="@android:color/transparent">

    <!-- Episode content -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/episode_item_height"
        android:layout_marginBottom="@dimen/episode_item_margin_bottom"
        android:background="@android:color/transparent">

    <!-- Episode info container -->
    <LinearLayout
        android:id="@+id/episode_info_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/episode_number_margin_start"
        android:orientation="vertical">

        <!-- Episode number -->
        <TextView
            android:id="@+id/tv_episode_number"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="EP.1"
            android:textColor="#ffffffff"
            android:textSize="@dimen/episode_number_text_size"
            android:fontFamily="sans-serif" />

        <!-- File size -->
        <TextView
            android:id="@+id/tv_file_size"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/episode_file_size_margin_top"
            android:text="24MB"
            android:textColor="#8fffffff"
            android:textSize="@dimen/episode_file_size_text_size"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- Play button -->
    <ImageView
        android:id="@+id/iv_play"
        android:layout_width="@dimen/episode_play_button_size"
        android:layout_height="@dimen/episode_play_button_size"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/episode_play_button_margin_end"
        android:src="@drawable/list_ic_play"
        android:background="@drawable/episode_play_button_bg"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp"
        android:scaleType="center" />

    <!-- Delete button -->
    <ImageView
        android:id="@+id/iv_delete"
        android:layout_width="@dimen/episode_delete_button_size"
        android:layout_height="@dimen/episode_delete_button_size"
        android:layout_toStartOf="@id/iv_play"
        android:layout_centerVertical="true"
        android:layout_marginEnd="@dimen/episode_delete_button_margin_end"
        android:src="@drawable/dw_ic_delete"
        android:background="@drawable/episode_delete_button_bg"
        android:clickable="true"
        android:focusable="true"
        android:padding="8dp"
        android:scaleType="center" />

    </RelativeLayout>

    <!-- Divider -->
    <View
        android:layout_width="match_parent"
        android:layout_height="@dimen/episode_divider_height"
        android:layout_marginTop="@dimen/episode_divider_margin_top"
        android:background="@drawable/episode_divider_bg" />

</LinearLayout>
