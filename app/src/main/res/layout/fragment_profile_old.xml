<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:app="http://schemas.android.com/apk/res-auto"
            xmlns:tools="http://schemas.android.com/tools"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/proflie_bg"
            tools:context=".ui.fragment.ProfileFragment">

    <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

        <!-- 状态栏区域 -->
        <View
                android:id="@+id/status_bar_background"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_status_bar_height"
                android:layout_marginStart="@dimen/profile_status_bar_margin_start"
                android:layout_marginTop="@dimen/profile_status_bar_margin_top"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 状态栏时间 -->
        <TextView
                android:id="@+id/tv_status_time"
                android:layout_width="@dimen/profile_status_time_width"
                android:layout_height="@dimen/profile_status_time_height"
                android:layout_marginStart="@dimen/profile_status_bar_margin_start"
                android:layout_marginTop="@dimen/profile_status_bar_margin_top"
                android:text="9:41"
                android:textColor="#ff000000"
                android:textSize="@dimen/profile_status_time_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 移动信号图标 -->
        <ImageView
                android:id="@+id/iv_mobile_signal"
                android:layout_width="@dimen/profile_status_mobile_signal_width"
                android:layout_height="@dimen/profile_status_mobile_signal_height"
                android:layout_marginTop="@dimen/profile_status_icon_margin_top"
                android:layout_marginEnd="@dimen/profile_status_mobile_signal_margin_end"
                android:background="@drawable/profile_status_mobile_signal"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- WiFi图标 -->
        <ImageView
                android:id="@+id/iv_wifi"
                android:layout_width="@dimen/profile_status_wifi_width"
                android:layout_height="@dimen/profile_status_wifi_height"
                android:layout_marginTop="@dimen/profile_status_icon_margin_top"
                android:layout_marginEnd="@dimen/profile_status_wifi_margin_end"
                android:background="@drawable/profile_status_wifi"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 电池图标 -->
        <ImageView
                android:id="@+id/iv_battery"
                android:layout_width="@dimen/profile_status_battery_width"
                android:layout_height="@dimen/profile_status_battery_height"
                android:layout_marginTop="@dimen/profile_status_battery_margin_top"
                android:layout_marginEnd="@dimen/profile_status_bar_margin_start"
                android:background="@drawable/profile_status_battery"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 用户头像 -->
        <ImageView
                android:id="@+id/iv_avatar"
                android:layout_width="@dimen/profile_avatar_size"
                android:layout_height="@dimen/profile_avatar_size"
                android:layout_marginStart="@dimen/profile_avatar_margin_start"
                android:layout_marginTop="@dimen/profile_avatar_margin_top"
                android:src="@drawable/profile_norentouxiang"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 用户名 -->
        <TextView
                android:id="@+id/tv_username"
                android:layout_width="@dimen/profile_username_width"
                android:layout_height="@dimen/profile_username_height"
                android:layout_marginStart="@dimen/profile_username_margin_start"
                android:layout_marginTop="@dimen/profile_username_margin_top"
                android:text="Guest"
                android:textColor="#ffffffff"
                android:textSize="@dimen/profile_username_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- UID -->
        <TextView
                android:id="@+id/tv_uid"
                android:layout_width="@dimen/profile_uid_width"
                android:layout_height="@dimen/profile_uid_height"
                android:layout_marginStart="@dimen/profile_uid_margin_start"
                android:layout_marginTop="@dimen/profile_uid_margin_top"
                android:text="UID:43310879"
                android:textColor="#b8ffffff"
                android:textSize="@dimen/profile_uid_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 复制按钮 -->
        <ImageView
                android:id="@+id/iv_copy"
                android:layout_width="@dimen/profile_copy_icon_size"
                android:layout_height="@dimen/profile_copy_icon_size"
                android:layout_marginStart="@dimen/profile_copy_margin_start"
                android:layout_marginTop="@dimen/profile_copy_margin_top"
                android:src="@drawable/profile_ic_copy"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- 登录/Edit按钮 -->
        <TextView
                android:id="@+id/btn_login"
                android:layout_width="@dimen/profile_login_button_width"
                android:layout_height="@dimen/profile_login_button_height"
                android:layout_marginTop="@dimen/profile_login_button_margin_top"
                android:layout_marginEnd="@dimen/profile_login_button_margin_end"
                android:background="@drawable/profile_edit_button_bg"
                android:text="Log in"
                android:textColor="#ffffffff"
                android:textSize="@dimen/profile_login_button_text_size"
                android:fontFamily="sans-serif-light"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        <!-- VIP卡片 -->
        <FrameLayout
                android:id="@+id/card_vip"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_vip_card_height"
                android:layout_marginStart="@dimen/profile_vip_card_margin_horizontal"
                android:layout_marginTop="@dimen/profile_vip_card_margin_top"
                android:layout_marginEnd="@dimen/profile_vip_card_margin_horizontal"
                android:background="@drawable/profile_card"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

            <!-- VIP卡片内容布局 -->
            <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent">

                <!-- VIP卡片类型图片 "VIP accepted" -->
                <ImageView
                        android:id="@+id/iv_vip_card_type"
                        android:layout_width="@dimen/profile_vip_card_type_width"
                        android:layout_height="@dimen/profile_vip_card_type_height"
                        android:layout_marginStart="@dimen/profile_vip_card_content_margin"
                        android:layout_marginTop="@dimen/profile_vip_card_type_margin_top"
                        android:scaleType="centerInside"
                        android:src="@drawable/profile_card_type"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                <!-- Edit按钮 -->
                <TextView
                        android:id="@+id/btn_edit"
                        android:layout_width="@dimen/profile_edit_button_width"
                        android:layout_height="@dimen/profile_edit_button_height"
                        android:layout_marginTop="@dimen/profile_vip_card_content_margin"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:background="@drawable/profile_edit_button_bg"
                        android:text="@string/profile_edit"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_edit_text_size"
                        android:fontFamily="sans-serif-light"
                        android:gravity="center"
                        android:clickable="true"
                        android:focusable="true"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                <!-- 星星图标 -->
                <ImageView
                        android:id="@+id/iv_star"
                        android:layout_width="@dimen/profile_vip_star_size"
                        android:layout_height="@dimen/profile_vip_star_size"
                        android:layout_marginStart="@dimen/profile_vip_card_content_margin"
                        android:layout_marginBottom="@dimen/profile_vip_star_margin_bottom"
                        android:src="@drawable/proflie_ic_stargou"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                <!-- "Unlimited access to all series" 文字 -->
                <TextView
                        android:id="@+id/tv_unlimited_access"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/profile_vip_text_margin_start"
                        android:text="Unlimited access to all series"
                        android:textColor="#e0fffaf0"
                        android:textSize="@dimen/profile_vip_text_size"
                        app:layout_constraintBottom_toBottomOf="@id/iv_star"
                        app:layout_constraintStart_toEndOf="@id/iv_star"
                        app:layout_constraintTop_toTopOf="@id/iv_star" />

                <!-- 第二个星星图标 -->
                <ImageView
                        android:id="@+id/iv_star_second"
                        android:layout_width="@dimen/profile_vip_star_size"
                        android:layout_height="@dimen/profile_vip_star_size"
                        android:layout_marginStart="@dimen/profile_vip_card_content_margin"
                        android:layout_marginBottom="@dimen/profile_vip_star_second_margin_bottom"
                        android:src="@drawable/proflie_ic_stargou"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintStart_toStartOf="parent" />

                <!-- Watch videos and enjoy 1080P -->
                <TextView
                        android:id="@+id/tv_watch_1080p"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="@dimen/profile_vip_text_margin_start"
                        android:text="@string/profile_watch_videos_1080p"
                        android:textColor="#e0fffaf0"
                        android:textSize="@dimen/profile_vip_text_size"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="@id/iv_star_second"
                        app:layout_constraintStart_toEndOf="@id/iv_star_second"
                        app:layout_constraintTop_toTopOf="@id/iv_star_second" />

                <!-- GO按钮 -->
                <TextView
                        android:id="@+id/btn_go_vip"
                        android:layout_width="@dimen/profile_vip_go_button_width"
                        android:layout_height="@dimen/profile_vip_go_button_height"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:layout_marginBottom="@dimen/profile_vip_star_second_margin_bottom"
                        android:background="@drawable/profile_vip_go_button_bg"
                        android:text="@string/profile_go"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_vip_go_button_text_size"
                        android:fontFamily="sans-serif-medium"
                        android:textStyle="italic"
                        android:gravity="center"
                        android:clickable="true"
                        android:focusable="true"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

                <!-- Automatic renewal 文本 -->
                <TextView
                        android:id="@+id/tv_auto_renewal"
                        android:layout_width="@dimen/profile_vip_renewal_text_width"
                        android:layout_height="@dimen/profile_vip_renewal_text_height"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:layout_marginBottom="@dimen/profile_vip_star_margin_bottom"
                        android:text="@string/profile_automatic_renewal"
                        android:textColor="#b8ffffff"
                        android:textSize="@dimen/profile_auto_renewal_text_size"
                        android:gravity="end"
                        android:visibility="gone"
                        app:layout_constraintBottom_toTopOf="@id/tv_renewal_period"
                        app:layout_constraintEnd_toEndOf="parent" />

                <!-- Renewal by Week 文本 -->
                <TextView
                        android:id="@+id/tv_renewal_period"
                        android:layout_width="@dimen/profile_vip_renewal_period_width"
                        android:layout_height="@dimen/profile_vip_renewal_period_height"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:layout_marginBottom="@dimen/profile_vip_star_second_margin_bottom"
                        android:text="@string/profile_renewal_by_week"
                        android:textColor="#b8ffffff"
                        android:textSize="@dimen/profile_renewal_period_text_size"
                        android:gravity="start"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

                <!-- 续费状态图标 (关闭状态) -->
                <ImageView
                        android:id="@+id/iv_renewal_status_off"
                        android:layout_width="@dimen/profile_vip_renewal_icon_size"
                        android:layout_height="@dimen/profile_vip_renewal_icon_size"
                        android:layout_marginTop="@dimen/profile_vip_card_content_margin"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:src="@drawable/proflie_guan"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                <!-- 续费状态图标 (开启状态) -->
                <ImageView
                        android:id="@+id/iv_renewal_status_on"
                        android:layout_width="@dimen/profile_vip_renewal_icon_size"
                        android:layout_height="@dimen/profile_vip_renewal_icon_size"
                        android:layout_marginTop="@dimen/profile_vip_card_content_margin"
                        android:layout_marginEnd="@dimen/profile_vip_card_content_margin"
                        android:src="@drawable/proflie_kai"
                        android:visibility="gone"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                <!-- 到期日期 -->
                <TextView
                        android:id="@+id/tv_expiration_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="@dimen/profile_expiration_date_margin_end"
                        android:layout_marginBottom="@dimen/profile_expiration_date_margin_bottom"
                        android:text="@string/profile_expiration_date"
                        android:textColor="#80ffffff"
                        android:textSize="@dimen/profile_expiration_date_text_size"
                        android:visibility="gone"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </FrameLayout>

        <!-- 积分区域 -->
        <LinearLayout
                android:id="@+id/layout_points"
                android:layout_width="0dp"
                android:layout_height="@dimen/profile_points_card_height"
                android:layout_marginStart="@dimen/profile_vip_card_margin_horizontal"
                android:layout_marginTop="@dimen/profile_section_spacing"
                android:layout_marginEnd="@dimen/profile_vip_card_margin_horizontal"
                android:background="@drawable/profile_points_bg"
                android:orientation="horizontal"
                android:padding="@dimen/profile_points_card_padding"
                android:gravity="center_vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/card_vip">

            <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="My point"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_points_text_size" />

                <LinearLayout
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/profile_points_icon_margin_top"
                        android:orientation="horizontal"
                        android:gravity="center_vertical">

                    <ImageView
                            android:layout_width="@dimen/profile_points_jinbi_size"
                            android:layout_height="@dimen/profile_points_jinbi_size"
                            android:src="@drawable/jinbi" />

                    <TextView
                            android:id="@+id/tv_points"
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/profile_points_text_line_height"
                            android:layout_marginStart="@dimen/profile_points_value_margin_start"
                            android:text="0"
                            android:textColor="#ffffffff"
                            android:textSize="@dimen/profile_points_value_size"
                            android:fontFamily="sans-serif-medium"
                            android:gravity="center_vertical" />

                </LinearLayout>

            </LinearLayout>

            <TextView
                    android:id="@+id/btn_refill"
                    android:layout_width="@dimen/profile_points_refill_button_width"
                    android:layout_height="@dimen/profile_points_refill_button_height"
                    android:background="@drawable/profile_refill_button_bg"
                    android:text="Refill"
                    android:textColor="#ffb65c16"
                    android:textSize="@dimen/profile_points_refill_text_size"
                    android:fontFamily="sans-serif-medium"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true" />

        </LinearLayout>

        <!-- 菜单项 -->
        <LinearLayout
                android:id="@+id/layout_menu"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/profile_menu_margin_horizontal"
                android:layout_marginTop="@dimen/profile_menu_margin_vertical"
                android:layout_marginEnd="@dimen/profile_menu_margin_horizontal"
                android:background="@drawable/profile_menu_bg"
                android:orientation="vertical"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_points">

            <!-- Language -->
            <LinearLayout
                    android:id="@+id/menu_language"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="@dimen/profile_menu_item_padding">

                <ImageView
                        android:layout_width="@dimen/profile_menu_icon_size"
                        android:layout_height="@dimen/profile_menu_icon_size"
                        android:src="@drawable/profile_ic_language" />

                <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/profile_menu_text_margin_start"
                        android:layout_weight="1"
                        android:text="Language"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_menu_text_size" />

                <ImageView
                        android:layout_width="@dimen/profile_menu_arrow_size"
                        android:layout_height="@dimen/profile_menu_arrow_size"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/profile_ic_next" />

            </LinearLayout>

            <!-- Divider -->
            <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/profile_menu_divider_height"
                    android:layout_marginStart="@dimen/profile_menu_divider_margin_start"
                    android:layout_marginEnd="@dimen/profile_menu_divider_margin_start"
                    android:background="#33ffffff" />

            <!-- Message -->
            <LinearLayout
                    android:id="@+id/menu_message"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="@dimen/profile_menu_item_padding">

                <ImageView
                        android:layout_width="@dimen/profile_menu_icon_size"
                        android:layout_height="@dimen/profile_menu_icon_size"
                        android:src="@drawable/profile_ic_message" />

                <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/profile_menu_text_margin_start"
                        android:layout_weight="1"
                        android:text="Message"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_menu_text_size" />

                <ImageView
                        android:layout_width="@dimen/profile_menu_arrow_size"
                        android:layout_height="@dimen/profile_menu_arrow_size"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/profile_ic_next" />

            </LinearLayout>

            <!-- Divider -->
            <View
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/profile_menu_divider_height"
                    android:layout_marginStart="@dimen/profile_menu_divider_margin_start"
                    android:layout_marginEnd="@dimen/profile_menu_divider_margin_start"
                    android:background="#33ffffff" />

            <!-- Bill -->
            <LinearLayout
                    android:id="@+id/menu_bill"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:orientation="horizontal"
                    android:padding="@dimen/profile_menu_item_padding">

                <ImageView
                        android:layout_width="@dimen/profile_menu_icon_size"
                        android:layout_height="@dimen/profile_menu_icon_size"
                        android:src="@drawable/profile_ic_bill" />

                <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="@dimen/profile_menu_text_margin_start"
                        android:layout_weight="1"
                        android:text="Bill"
                        android:textColor="#ffffffff"
                        android:textSize="@dimen/profile_menu_text_size" />

                <ImageView
                        android:layout_width="@dimen/profile_menu_arrow_size"
                        android:layout_height="@dimen/profile_menu_arrow_size"
                        android:layout_gravity="center_vertical"
                        android:src="@drawable/profile_ic_next" />

            </LinearLayout>

        </LinearLayout>

        <!-- Setting -->
        <LinearLayout
                android:id="@+id/menu_setting"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/profile_menu_margin_horizontal"
                android:layout_marginTop="@dimen/profile_menu_margin_vertical"
                android:layout_marginEnd="@dimen/profile_menu_margin_horizontal"
                android:layout_marginBottom="@dimen/profile_menu_bottom_margin"
                android:background="@drawable/profile_menu_bg"
                android:clickable="true"
                android:focusable="true"
                android:orientation="horizontal"
                android:padding="@dimen/profile_menu_item_padding"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/layout_menu">

            <ImageView
                    android:layout_width="@dimen/profile_menu_icon_size"
                    android:layout_height="@dimen/profile_menu_icon_size"
                    android:src="@drawable/profile_ic_setting" />

            <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/profile_menu_text_margin_start"
                    android:layout_weight="1"
                    android:text="Setting"
                    android:textColor="#ffffffff"
                    android:textSize="@dimen/profile_menu_text_size" />

            <ImageView
                    android:layout_width="@dimen/profile_menu_arrow_size"
                    android:layout_height="@dimen/profile_menu_arrow_size"
                    android:layout_gravity="center_vertical"
                    android:src="@drawable/profile_ic_next" />

        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</ScrollView>
