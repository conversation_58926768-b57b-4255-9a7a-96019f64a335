<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff000000">

    <!-- Layout Guidelines for responsive design -->
    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideline_header_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintGuide_begin="@dimen/download_header_height" />

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="0dp"
        android:layout_height="160dp"
        android:background="@drawable/top_shadow_gradient"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent" />

    <!-- Header with back button and title -->
    <RelativeLayout
        android:id="@+id/header_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:paddingStart="@dimen/bill_header_padding_horizontal"
        android:paddingEnd="@dimen/bill_header_padding_horizontal"
        android:paddingTop="@dimen/bill_header_padding_top"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@id/guideline_header_bottom">

        <!-- Back button -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/bill_back_button_size"
            android:layout_height="@dimen/bill_back_button_size"
            android:layout_alignParentStart="true"
            android:layout_marginTop="@dimen/bill_back_button_margin_top"
            android:layout_marginStart="@dimen/bill_back_button_margin_start"
            android:src="@drawable/login_btn_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

        <!-- Title -->
        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/bill_title_margin_top"
            android:text="downloaded"
            android:textColor="#ffffffff"
            android:textSize="@dimen/download_page_title_text_size"
            android:fontFamily="sans-serif"
            android:gravity="center" />

    </RelativeLayout>

    <!-- SwipeRefreshLayout for pull-to-refresh -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@id/guideline_header_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <!-- Downloaded videos list -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_downloaded_videos"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/download_list_margin_top"
            android:paddingStart="@dimen/download_list_padding_horizontal"
            android:paddingEnd="@dimen/download_list_padding_horizontal"
            android:paddingBottom="@dimen/download_list_padding_bottom"
            android:clipToPadding="false" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- Empty state (initially hidden) - Centered using ConstraintLayout -->
    <LinearLayout
        android:id="@+id/layout_empty_state"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:gravity="center"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/guideline_header_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <ImageView
            android:layout_width="@dimen/download_empty_image_size"
            android:layout_height="@dimen/download_empty_image_size"
            android:src="@drawable/empty"
            android:alpha="0.5" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/download_empty_text_margin_top"
            android:text="No downloaded videos"
            android:textColor="#80ffffff"
            android:textSize="@dimen/download_empty_text_size"
            android:fontFamily="sans-serif" />

    </LinearLayout>

    <!-- Loading state (initially hidden) - Centered using ConstraintLayout -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_loading"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:visibility="gone"
        app:layout_constraintTop_toBottomOf="@id/guideline_header_bottom"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent">

        <View
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@android:color/transparent" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
