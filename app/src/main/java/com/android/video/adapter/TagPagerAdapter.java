package com.android.video.adapter;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.TagModel;
import com.android.video.utils.TagLengthGroupingUtils;
import com.google.android.flexbox.FlexDirection;
import com.google.android.flexbox.FlexWrap;
import com.google.android.flexbox.FlexboxLayoutManager;
import com.google.android.flexbox.JustifyContent;
import java.util.ArrayList;
import java.util.List;

/**
 * ViewPager2分页适配器 - 管理标签分页显示
 * <AUTHOR>
 */
public class TagPagerAdapter extends RecyclerView.Adapter<TagPagerAdapter.PageViewHolder> {
    
    private static final int TAGS_PER_PAGE = 12;
    private static final int TOTAL_PAGES = 3;

    private List<TagModel> allTags;
    private List<FavoriteTagAdapter> tagAdapters;
    private OnTagSelectionChangeListener onTagSelectionChangeListener;
    private Context context;
    
    /**
     * 标签选择变化监听接口
     */
    public interface OnTagSelectionChangeListener {
        void onTagSelectionChanged(TagModel tag, boolean isSelected);
    }
    
    /**
     * 构造函数
     */
    public TagPagerAdapter() {
        this.allTags = new ArrayList<>();
        this.tagAdapters = new ArrayList<>();
        initializeTagAdapters();
    }
    
    /**
     * 构造函数
     * @param allTags 所有标签列表
     */
    public TagPagerAdapter(List<TagModel> allTags) {
        this.allTags = allTags != null ? allTags : new ArrayList<>();
        this.tagAdapters = new ArrayList<>();
        initializeTagAdapters();
        distributeTagsToPages();
    }
    
    /**
     * 设置标签选择变化监听器
     * @param listener 监听器
     */
    public void setOnTagSelectionChangeListener(OnTagSelectionChangeListener listener) {
        this.onTagSelectionChangeListener = listener;
    }
    
    /**
     * 更新所有标签数据
     * @param newTags 新的标签列表
     */
    public void updateAllTags(List<TagModel> newTags) {
        this.allTags = newTags != null ? newTags : new ArrayList<>();
        distributeTagsToPages();
        notifyDataSetChanged();
    }
    
    /**
     * 获取所有选中的标签
     * @return 选中的标签列表
     */
    public List<TagModel> getAllSelectedTags() {
        List<TagModel> selectedTags = new ArrayList<>();
        for (TagModel tag : allTags) {
            if (tag.isSelected()) {
                selectedTags.add(tag);
            }
        }
        return selectedTags;
    }
    
    /**
     * 获取选中标签的名称集合
     * @return 选中标签名称集合
     */
    public List<String> getSelectedTagNames() {
        List<String> selectedNames = new ArrayList<>();
        for (TagModel tag : allTags) {
            if (tag.isSelected()) {
                selectedNames.add(tag.getName());
            }
        }
        return selectedNames;
    }

    /**
     * 获取选中的标签对象集合
     * @return 选中标签对象集合
     */
    public List<TagModel> getSelectedTags() {
        List<TagModel> selectedTags = new ArrayList<>();
        for (TagModel tag : allTags) {
            if (tag.isSelected()) {
                selectedTags.add(tag);
            }
        }
        return selectedTags;
    }
    
    /**
     * 初始化FavoriteTagAdapter实例
     */
    private void initializeTagAdapters() {
        tagAdapters.clear();
        for (int i = 0; i < TOTAL_PAGES; i++) {
            FavoriteTagAdapter adapter = new FavoriteTagAdapter();
            adapter.setOnMultiTagSelectionListener((tag, position, isSelected) -> {
                if (onTagSelectionChangeListener != null) {
                    onTagSelectionChangeListener.onTagSelectionChanged(tag, isSelected);
                }
            });
            tagAdapters.add(adapter);
        }
    }
    
    /**
     * 将标签分发到各个页面（应用智能分组）
     */
    private void distributeTagsToPages() {
        // 应用智能分组，按长度重新排列标签
        List<TagModel> groupedTags = applySmartGrouping(allTags);

        for (int pageIndex = 0; pageIndex < TOTAL_PAGES && pageIndex < tagAdapters.size(); pageIndex++) {
            int startIndex = pageIndex * TAGS_PER_PAGE;
            int endIndex = Math.min(startIndex + TAGS_PER_PAGE, groupedTags.size());

            List<TagModel> pageTagList = new ArrayList<>();
            if (startIndex < groupedTags.size()) {
                pageTagList = groupedTags.subList(startIndex, endIndex);
            }

            tagAdapters.get(pageIndex).updateTagList(pageTagList);
        }
    }

    /**
     * 应用智能分组逻辑
     * @param originalTags 原始标签列表
     * @return 分组后的标签列表
     */
    private List<TagModel> applySmartGrouping(List<TagModel> originalTags) {
        if (originalTags == null || originalTags.isEmpty()) {
            return new ArrayList<>();
        }

        // 如果Context可用，使用智能分组；否则使用简单的字符数分组
        if (context != null) {
            return TagLengthGroupingUtils.smartGroupTags(context, originalTags, 15f);
        } else {
            // 降级方案：按字符数进行简单分组
            List<TagLengthGroupingUtils.TagGroup> groups =
                TagLengthGroupingUtils.groupTagsByLength(null, originalTags, 15f, false);
            return TagLengthGroupingUtils.flattenGroupedTags(groups);
        }
    }
    
    @NonNull
    @Override
    public PageViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        // 保存Context用于智能分组
        if (context == null) {
            context = parent.getContext();
        }

        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_tag_page, parent, false);
        return new PageViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PageViewHolder holder, int position) {
        if (position < tagAdapters.size()) {
            holder.bind(tagAdapters.get(position));
        }
    }
    
    @Override
    public int getItemCount() {
        return TOTAL_PAGES;
    }
    
    /**
     * PageViewHolder内部类
     */
    public static class PageViewHolder extends RecyclerView.ViewHolder {
        
        private RecyclerView rvTags;
        
        public PageViewHolder(@NonNull View itemView) {
            super(itemView);
            rvTags = itemView.findViewById(R.id.rv_tags);
            setupRecyclerView();
        }
        
        /**
         * 设置RecyclerView
         */
        private void setupRecyclerView() {
            // 创建FlexboxLayoutManager
            FlexboxLayoutManager layoutManager = new FlexboxLayoutManager(itemView.getContext());
            layoutManager.setFlexDirection(FlexDirection.ROW);
            layoutManager.setFlexWrap(FlexWrap.WRAP);
            layoutManager.setJustifyContent(JustifyContent.CENTER);
            
            rvTags.setLayoutManager(layoutManager);
        }
        
        /**
         * 绑定FavoriteTagAdapter
         * @param tagAdapter 标签适配器
         */
        public void bind(FavoriteTagAdapter tagAdapter) {
            rvTags.setAdapter(tagAdapter);
        }
    }
}
