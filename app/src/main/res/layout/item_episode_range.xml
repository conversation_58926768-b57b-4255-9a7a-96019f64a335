<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_marginEnd="16dp"
    android:orientation="vertical"
    android:gravity="center"
    android:clickable="true"
    android:focusable="true"
    android:background="?android:attr/selectableItemBackground">

    <!-- 区间文本 -->
    <TextView
        android:id="@+id/text_range"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1-30"
        android:textColor="#FFFFFF"
        android:textSize="@dimen/video_detail_episodes_range_text_size"
        android:textStyle="bold"
        android:paddingHorizontal="8dp"
        android:paddingVertical="4dp" />

    <!-- 下划线 -->
    <View
        android:id="@+id/view_range_underline"
        android:layout_width="match_parent"
        android:layout_height="3dp"
        android:layout_marginTop="7dp"
        android:background="@drawable/episode_underline_background"
        android:visibility="gone" />

</LinearLayout>
