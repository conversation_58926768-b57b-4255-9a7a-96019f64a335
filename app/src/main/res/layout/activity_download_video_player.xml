<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/root_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black"
    tools:context=".ui.activity.DownloadVideoPlayerActivity">

    <!-- Video Container (no ViewPager2, just a single container) -->
    <FrameLayout
        android:id="@+id/video_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Top Control Bar -->
    <LinearLayout
        android:id="@+id/top_control_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:paddingStart="@dimen/login_back_button_margin_start"
        android:paddingEnd="@dimen/video_player_common_margin"
        android:paddingTop="@dimen/login_back_button_margin_top"
        android:paddingBottom="@dimen/video_player_common_margin"
        android:gravity="center_vertical"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Back Button -->
        <ImageView
            android:id="@+id/btn_back"
            android:layout_width="@dimen/login_back_button_size"
            android:layout_height="@dimen/login_back_button_size"
            android:src="@drawable/login_btn_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:contentDescription="返回" />

        <View
            android:layout_width="0dp"
            android:layout_height="@dimen/video_player_divider_height"
            android:layout_weight="1" />

        <!-- Small Screen Play Button -->
        <ImageView
            android:id="@+id/btn_small_screen"
            android:layout_width="@dimen/video_player_new_download_button_size"
            android:layout_height="@dimen/video_player_new_download_button_size"
            android:src="@drawable/play_ic_xiaoping"
            android:background="@drawable/video_player_control_button_bg"
            android:scaleType="center"
            android:contentDescription="小屏播放"
            android:layout_marginEnd="@dimen/video_player_control_button_spacing" />



    </LinearLayout>

    <!-- Side Control Buttons (removed like, share, comment buttons) -->
    <LinearLayout
        android:id="@+id/side_control_buttons"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginEnd="@dimen/video_player_side_button_margin_end"
        android:layout_marginBottom="@dimen/video_player_like_button_margin_bottom"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/bottom_info_area">

        <!-- No like, share, comment buttons for downloaded video player -->

    </LinearLayout>

    <!-- Bottom Info Area -->
    <LinearLayout
        android:id="@+id/bottom_info_area"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingStart="@dimen/video_player_common_margin"
        android:paddingEnd="@dimen/video_player_side_button_margin_end"
        android:paddingBottom="@dimen/video_player_bottom_margin"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- Video Title with Detail Icon -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="@dimen/video_player_new_title_margin_top">

            <TextView
                android:id="@+id/tv_video_title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="Video Title"
                android:textColor="@color/video_player_text_white"
                android:textSize="@dimen/video_player_title_text_size"
                android:textStyle="bold"
                android:maxLines="2"
                android:ellipsize="end" />

            <ImageView
                android:id="@+id/btn_detail"
                android:layout_width="@dimen/video_player_title_icon_size"
                android:layout_height="@dimen/video_player_title_icon_size"
                android:layout_marginStart="@dimen/video_player_common_margin_small"
                android:src="@drawable/play_ic_detail"
                android:background="?android:attr/selectableItemBackgroundBorderless"
                android:contentDescription="@string/video_player_episode" />

        </LinearLayout>

        <!-- Video Description -->
        <TextView
            android:id="@+id/tv_video_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="Video description goes here..."
            android:textColor="@color/video_player_text_secondary"
            android:textSize="@dimen/video_player_description_text_size"
            android:maxLines="3"
            android:ellipsize="end"
            android:layout_marginBottom="@dimen/video_player_new_description_margin_top" />

        <!-- Progress SeekBar with increased touch area -->
        <SeekBar
            android:id="@+id/progress_seek_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/video_player_progress_height"
            android:layout_marginTop="@dimen/video_player_progress_margin_top"
            android:progressDrawable="@drawable/video_player_seekbar_bg"
            android:thumb="@drawable/seek_thumb_normal"
            android:progress="30"
            android:max="100"
            android:splitTrack="false"
            android:thumbOffset="@dimen/video_player_thumb_offset" />

        <!-- Bottom Control Bar -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/video_player_new_bottom_controls_margin_top">

            <!-- Episode Selection Button -->
            <LinearLayout
                android:id="@+id/btnEpisodeSelection"
                android:layout_width="@dimen/video_player_episode_selection_width"
                android:layout_height="@dimen/video_player_episode_button_height"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:background="@drawable/video_player_episode_button_bg"
                android:paddingStart="@dimen/video_player_episode_padding_start"
                android:paddingEnd="@dimen/video_player_episode_padding_end"
                android:clickable="true"
                android:focusable="true">

                <TextView
                    android:id="@+id/tvEpisodeText"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="EP.1/EP.10"
                    android:textSize="12sp"
                    android:textColor="@color/video_player_text_white"
                    android:fontFamily="sans-serif" />

                <ImageView
                    android:layout_width="@dimen/video_player_episode_icon_size"
                    android:layout_height="@dimen/video_player_episode_icon_size"
                    android:src="@drawable/play_ic_ep"
                    android:contentDescription="@string/video_player_episode" />

            </LinearLayout>

            <View
                android:layout_width="0dp"
                android:layout_height="@dimen/video_player_divider_height"
                android:layout_weight="1" />

            <!-- Small Control Buttons -->
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Danmaku Toggle Button -->
                <ImageView
                    android:id="@+id/btnDanmaku"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:src="@drawable/play_ic_zimu"
                    android:contentDescription="@string/video_player_danmaku"
                    android:clickable="true"
                    android:focusable="true" />

                <!-- Speed Selection Button -->
                <TextView
                    android:id="@+id/btnSpeedSelection"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:text="1.0x"
                    android:textColor="@color/video_player_text_white"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true" />

                <!-- Quality Selection Button -->
                <TextView
                    android:id="@+id/btnQualitySelection"
                    style="@style/VideoPlayerSmallControlButton"
                    android:layout_marginEnd="@dimen/video_player_control_margin_end"
                    android:text="720P"
                    android:textColor="@color/video_player_text_white"
                    android:textSize="12sp"
                    android:gravity="center"
                    android:clickable="true"
                    android:focusable="true" />

                <!-- Fullscreen Button -->
                <ImageView
                    android:id="@+id/btnFullscreen"
                android:layout_width="@dimen/video_player_fullscreen_button_width"
                android:layout_height="@dimen/video_player_fullscreen_button_height"
                android:background="@drawable/video_player_small_button_bg"
                android:scaleType="center"
                android:src="@drawable/play_ic_quanping"
                android:contentDescription="@string/video_player_fullscreen"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

        </LinearLayout>

    </LinearLayout>



</androidx.constraintlayout.widget.ConstraintLayout>
