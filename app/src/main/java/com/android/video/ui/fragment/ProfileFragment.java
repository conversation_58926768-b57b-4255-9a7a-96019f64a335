package com.android.video.ui.fragment;

import android.content.ClipData;
import android.content.ClipboardManager;
import android.content.Context;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.*;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import com.android.video.R;
import com.android.video.ui.activity.SubscribeActivity;
import com.android.video.ui.activity.PointsActivity;
import com.android.video.ui.activity.MessageActivity;
import com.android.video.ui.activity.SettingActivity;
import com.android.video.ui.activity.EditProfileActivity;
import com.android.video.model.UserModel;
import com.android.video.model.UserInfo;
import com.android.video.network.ProfileApiService;
import com.android.video.utils.UserInfoConverter;
import com.android.video.utils.UserSessionUtils;
import com.android.video.utils.LocalizationUtils;
import com.android.video.utils.GlobalTextUpdater;
import com.android.video.utils.TranslationTestUtils;
import com.android.video.base.BaseMultiLanguageFragment;
import com.android.video.ui.activity.LoginActivity;
import android.content.Intent;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import jp.wasabeef.glide.transformations.CropCircleTransformation;

import static java.security.AccessController.getContext;

/**
 * 个人资料Fragment - 显示用户信息和设置选项
 * 支持多语言界面切换（英语、俄语、Kaza语）
 * <AUTHOR>
 */
public class ProfileFragment extends BaseMultiLanguageFragment {

    // UI组件
    private TextView tvUsername;
    private TextView tvUid;
    private TextView btnLogin;
    private TextView btnGoVip;
    private TextView btnRefill;
    private ImageView ivCopy;
    private ImageView ivAvatar;
    private TextView tvPoints;
    private LinearLayout layoutPoints;

    // VIP卡片相关UI组件
    private ImageView ivVipCardType;
    private TextView btnEdit;
    private TextView tvAutoRenewal;
    private TextView tvRenewalPeriod;
    private TextView tvUnlimitedAccess;
    private TextView tvWatch1080p;
    private TextView tvExpirationDate;
    private ImageView ivRenewalStatusOff;
    private ImageView ivRenewalStatusOn;
    private ImageView ivStarFirst;
    private ImageView ivStarSecond;

    // 用户信息
    private UserModel currentUser;
    private View messageNotificationDot;

    // 续费状态
    private boolean isAutoRenewalEnabled = true; // 默认开启状态

    public ProfileFragment() {
        // Required empty public constructor
    }

    public static ProfileFragment newInstance() {
        return new ProfileFragment();
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        return inflater.inflate(R.layout.fragment_profile, container, false);
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        initViews(view);
        loadUserInfo();
    }

    @Override
    public void onResume() {
        super.onResume();
        // 每次页面显示时刷新用户信息
        loadUserInfo();

        // 每次进入页面时更新消息提示圆点状态
        if (messageNotificationDot != null) {
            updateMessageNotificationDot(messageNotificationDot);
        }
    }

    /**
     * 公共方法：刷新用户信息
     * 可以被其他Activity调用来强制刷新用户信息
     */
    public void refreshUserInfo() {
        loadUserInfo();
    }

    private void initViews(View view) {
        // 初始化基础UI组件
        tvUsername = view.findViewById(R.id.tv_username);
        tvUid = view.findViewById(R.id.tv_uid);
        ivCopy = view.findViewById(R.id.iv_copy);
        ivAvatar = view.findViewById(R.id.iv_avatar);
        btnLogin = view.findViewById(R.id.btn_login);
        btnGoVip = view.findViewById(R.id.btn_go_vip);
        btnRefill = view.findViewById(R.id.btn_refill);
        tvPoints = view.findViewById(R.id.tv_points);
        layoutPoints = view.findViewById(R.id.layout_points);

        // 初始化VIP卡片相关UI组件
        ivVipCardType = view.findViewById(R.id.iv_vip_card_type);
        btnEdit = view.findViewById(R.id.btn_edit);
        tvAutoRenewal = view.findViewById(R.id.tv_auto_renewal);
        tvRenewalPeriod = view.findViewById(R.id.tv_renewal_period);
        tvUnlimitedAccess = view.findViewById(R.id.tv_unlimited_access);
        tvWatch1080p = view.findViewById(R.id.tv_watch_1080p);
        tvExpirationDate = view.findViewById(R.id.tv_expiration_date);
        ivRenewalStatusOff = view.findViewById(R.id.iv_renewal_status_off);
        ivRenewalStatusOn = view.findViewById(R.id.iv_renewal_status_on);
        ivStarFirst = view.findViewById(R.id.iv_star);
        ivStarSecond = view.findViewById(R.id.iv_star_second);

        // 设置点击事件
        ivCopy.setOnClickListener(v -> copyUidToClipboard());
        ivAvatar.setOnClickListener(v -> handleAvatarClick());
        btnLogin.setOnClickListener(v -> handleLogin());
        btnGoVip.setOnClickListener(v -> handleGoVip());
        btnRefill.setOnClickListener(v -> handleRefill());
        layoutPoints.setOnClickListener(v -> handlePointsClick());

        // VIP卡片相关点击事件
        if (btnEdit != null) {
            btnEdit.setOnClickListener(v -> handleEdit());
        }

        // 续费开关点击事件
        if (ivRenewalStatusOff != null) {
            ivRenewalStatusOff.setOnClickListener(v -> toggleRenewalStatus());
        }
        if (ivRenewalStatusOn != null) {
            ivRenewalStatusOn.setOnClickListener(v -> toggleRenewalStatus());
        }

        // 菜单项点击事件
        setupMenuClickListeners(view);
    }

    private void copyUidToClipboard() {
        if (currentUser != null && currentUser.isLoggedIn() && !currentUser.getUid().isEmpty()) {
            ClipboardManager clipboard = (ClipboardManager) requireContext().getSystemService(Context.CLIPBOARD_SERVICE);
            ClipData clip = ClipData.newPlainText("UID", currentUser.getUid());
            clipboard.setPrimaryClip(clip);
            Toast.makeText(getContext(), "UID copied to clipboard", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "Please login first", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 加载用户信息
     */
    private void loadUserInfo() {
        if (getContext() != null) {
            // 首先从本地获取用户信息
            currentUser = UserSessionUtils.getCurrentUser(getContext());
            updateUI();

            // 如果用户已登录，从API获取最新的用户信息
            if (currentUser != null && currentUser.isLoggedIn()) {
                loadUserInfoFromApi();
            }
        }
    }

    /**
     * 从API加载用户信息
     */
    private void loadUserInfoFromApi() {
        loadUserInfoFromApi(true); // 默认先尝试GET方法
    }

    /**
     * 从API加载用户信息（指定HTTP方法）
     */
    private void loadUserInfoFromApi(boolean useGetMethod) {
        ProfileApiService.getInstance().getUserInfo(new ProfileApiService.UserInfoCallback() {
            @Override
            public void onSuccess(UserInfo userInfo) {
                if (getContext() != null && isAdded()) {
                    // 转换为UserModel并保存到本地
                    UserModel updatedUser = UserInfoConverter.convertToUserModel(userInfo);
                    UserSessionUtils.saveUserSession(getContext(), updatedUser);

                    // 保存UserInfo信息（包含messageReadStatus）
                    UserSessionUtils.saveUserInfo(getContext(), userInfo);

                    // 更新访问令牌
                    UserInfoConverter.updateAccessTokenFromUserInfo(userInfo);

                    // 更新当前用户信息并刷新UI
                    currentUser = updatedUser;
                    updateUI();

                    // 更新消息红点状态
                    if (messageNotificationDot != null) {
                        updateMessageNotificationDot(messageNotificationDot);
                    }

                    // 记录日志
                    android.util.Log.d("ProfileFragment", "用户信息更新成功: " +
                        UserInfoConverter.createUserModelSummary(updatedUser));
                }
            }

            @Override
            public void onError(String errorMessage) {
                if (getContext() != null && isAdded()) {
                    android.util.Log.e("ProfileFragment", "获取用户信息失败: " + errorMessage);

                    // 如果是405错误且当前使用GET方法，尝试POST方法
                    if (errorMessage.contains("405") && useGetMethod) {
                        android.util.Log.d("ProfileFragment", "GET方法失败，尝试POST方法");
                        loadUserInfoFromApi(false); // 尝试POST方法
                        return;
                    }

                    Toast.makeText(getContext(), "获取用户信息失败: " + errorMessage, Toast.LENGTH_SHORT).show();
                }
            }
        }, useGetMethod);
    }

    /**
     * 更新UI显示
     */
    private void updateUI() {
        if (currentUser == null) {
            return;
        }

        // 更新用户名显示
        if (tvUsername != null) {
            tvUsername.setText(currentUser.getDisplayName());
        }

        // 更新UID显示
        if (tvUid != null) {
            tvUid.setText(currentUser.getDisplayUid());
        }

        // 更新积分显示
        if (tvPoints != null) {
            tvPoints.setText(String.valueOf(currentUser.getPoints()));
        }

        // 更新头像显示
        updateAvatarDisplay();

        // 根据登录状态和VIP状态更新按钮显示
        updateButtonsVisibility();

        // 更新VIP到期时间显示
        updateVipExpirationDisplay();
    }

    /**
     * 更新头像显示
     */
    private void updateAvatarDisplay() {
        if (currentUser == null || ivAvatar == null) {
            return;
        }

        // 检查用户是否有头像URL
        if (currentUser.getAvatar() != null && !currentUser.getAvatar().trim().isEmpty()) {
            // 使用Glide加载网络头像，应用圆形变换
            if (getContext() != null) {
                Glide.with(getContext())
                        .load(currentUser.getAvatar())
                        .placeholder(R.drawable.profile_norentouxiang)
                        .error(R.drawable.profile_norentouxiang)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .transform(new CropCircleTransformation())
                        .into(ivAvatar);
            }
        } else {
            // 没有头像URL，使用默认头像
            ivAvatar.setImageResource(R.drawable.profile_norentouxiang);
        }
    }

    /**
     * 根据用户状态更新按钮显示
     */
    private void updateButtonsVisibility() {
        if (currentUser == null) {
            return;
        }

        if (currentUser.isLoggedIn()) {
            // 登录后统一显示Log out按钮
            if (btnLogin != null) {
                btnLogin.setText("Log out");
                btnLogin.setBackgroundResource(R.drawable.profile_edit_button_bg);
                btnLogin.setTextColor(0xffffffff);
                btnLogin.setVisibility(View.VISIBLE);
            }

            // 根据VIP状态显示不同的UI
            if (currentUser.isVip()) {
                showVipUI();
            } else {
                showNonVipUI();
            }
        } else {
            // 未登录状态 - 显示未登录UI
            showGuestUI();
        }
    }

    /**
     * 显示VIP用户UI
     */
    private void showVipUI() {
        // 设置VIP卡片背景
        FrameLayout cardVip = getView().findViewById(R.id.card_vip);
        if (cardVip != null) {
            cardVip.setBackgroundResource(R.drawable.profile_card);
        }

        // 隐藏GO按钮，Refill按钮保持可见
        if (btnGoVip != null) {
            btnGoVip.setVisibility(View.GONE);
        }
        // Refill按钮始终可见，不需要特殊处理

        // 切换到VIP卡片类型图片
        if (ivVipCardType != null) {
            ivVipCardType.setImageResource(R.drawable.profile_card_type_use);
            // 调整为VIP卡片类型图片的专用尺寸
            adjustVipCardTypeSize(true);
        }

        // 隐藏VIP卡片上的Edit按钮（Edit按钮在用户名旁边）
        if (btnEdit != null) {
            btnEdit.setVisibility(View.GONE);
        }
        if (tvAutoRenewal != null) {
            tvAutoRenewal.setVisibility(View.VISIBLE);
        }
        if (tvRenewalPeriod != null) {
            tvRenewalPeriod.setVisibility(View.VISIBLE);
        }
        if (tvUnlimitedAccess != null) {
            tvUnlimitedAccess.setVisibility(View.VISIBLE);
        }
        if (tvWatch1080p != null) {
            tvWatch1080p.setVisibility(View.VISIBLE);
        }
        if (tvExpirationDate != null) {
            tvExpirationDate.setVisibility(View.VISIBLE);
            // 设置VIP到期日期 - 使用真实数据
            updateVipExpirationDisplay();
        }
        // 根据续费状态显示对应的开关
        updateRenewalStatusDisplay();
        if (ivStarFirst != null) {
            ivStarFirst.setVisibility(View.VISIBLE);
        }
        if (ivStarSecond != null) {
            ivStarSecond.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 显示非VIP用户UI
     */
    private void showNonVipUI() {
        // 设置非VIP卡片背景
        FrameLayout cardVip = getView().findViewById(R.id.card_vip);
        if (cardVip != null) {
            cardVip.setBackgroundResource(R.drawable.card_bg);
        }

        // 设置非VIP卡片类型图片
        if (ivVipCardType != null) {
            ivVipCardType.setImageResource(R.drawable.profile_card_type);
            // 调整为非VIP卡片类型图片的默认尺寸
            adjustVipCardTypeSize(false);
        }

        // 显示GO按钮，Refill按钮保持可见
        if (btnGoVip != null) {
            btnGoVip.setVisibility(View.VISIBLE);
        }
        // Refill按钮始终可见，不需要隐藏

        // 隐藏VIP专属元素，保持非VIP原有显示
        hideVipOnlyElements();
    }

    /**
     * 显示访客用户UI
     */
    private void showGuestUI() {
        // 设置访客用户卡片背景
        FrameLayout cardVip = getView().findViewById(R.id.card_vip);
        if (cardVip != null) {
            cardVip.setBackgroundResource(R.drawable.card_bg);
        }

        if (btnLogin != null) {
            btnLogin.setText("Log in");
            btnLogin.setBackgroundResource(R.drawable.profile_login_button_bg_guest);
            btnLogin.setTextColor(0xffb936cf);
            btnLogin.setVisibility(View.VISIBLE);
        }
        if (btnGoVip != null) {
            btnGoVip.setVisibility(View.VISIBLE);
        }
        // Refill按钮始终可见，不需要隐藏

        // 隐藏VIP相关元素
        hideVipElements();
    }

    /**
     * 隐藏VIP专属UI元素（保留基本卡片内容）
     */
    private void hideVipOnlyElements() {
        if (btnEdit != null) {
            btnEdit.setVisibility(View.GONE);
        }
        if (tvAutoRenewal != null) {
            tvAutoRenewal.setVisibility(View.GONE);
        }
        if (tvRenewalPeriod != null) {
            tvRenewalPeriod.setVisibility(View.GONE);
        }
        if (tvWatch1080p != null) {
            tvWatch1080p.setVisibility(View.GONE);
        }
        if (tvExpirationDate != null) {
            tvExpirationDate.setVisibility(View.GONE);
        }
        if (ivRenewalStatusOn != null) {
            ivRenewalStatusOn.setVisibility(View.GONE);
        }
        if (ivRenewalStatusOff != null) {
            ivRenewalStatusOff.setVisibility(View.GONE);
        }
        if (ivStarSecond != null) {
            ivStarSecond.setVisibility(View.GONE);
        }
    }

    /**
     * 隐藏VIP专属UI元素（用于访客用户），但保留基本的吸引元素
     */
    private void hideVipElements() {
        // 切换回非VIP卡片类型图片
        if (ivVipCardType != null) {
            ivVipCardType.setImageResource(R.drawable.profile_card_type);
            // 调整为非VIP卡片类型图片的默认尺寸
            adjustVipCardTypeSize(false);
        }

        // 访客用户应该能看到"Unlimited access to"文本和星星图标来了解VIP好处
        if (tvUnlimitedAccess != null) {
            tvUnlimitedAccess.setVisibility(View.VISIBLE);
        }
        if (ivStarFirst != null) {
            ivStarFirst.setVisibility(View.VISIBLE);
        }

        // 隐藏VIP专属元素（续费相关等）
        hideVipOnlyElements();
    }

    private void handleLogin() {
        if (currentUser != null && currentUser.isLoggedIn()) {
            // 已登录，执行登出
            handleLogout();
        } else {
            // 未登录，跳转到登录页面
            navigateToLogin();
        }
    }

    /**
     * 处理登录成功后的操作
     * <p>
     * 当用户登录成功后，立即从API获取最新的用户信息并更新UI。
     * 这个方法可以被登录页面调用。
     * </p>
     */
    public void onLoginSuccess() {
        if (getContext() != null) {
            // 立即从API获取用户信息
            loadUserInfoFromApi();
        }
    }

    /**
     * 处理用户登出
     */
    private void handleLogout() {
        if (getContext() != null) {
            // 清除本地用户会话
            UserSessionUtils.performLogout(getContext());

            // 清除TokenManager中的token缓存（重要：确保完全清除token）
            com.android.video.manager.TokenManager tokenManager =
                com.android.video.manager.TokenManager.getInstance(getContext());
            tokenManager.clearToken();

            android.util.Log.d("ProfileFragment", "用户退出登录，已清除所有token缓存");

            Toast.makeText(getContext(), "Logged out successfully", Toast.LENGTH_SHORT).show();
            loadUserInfo(); // 刷新UI
        }
    }

    /**
     * 导航到登录页面
     */
    private void navigateToLogin() {
        if (getContext() != null) {
            Intent intent = new Intent(getContext(), LoginActivity.class);
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TASK);
            startActivity(intent);

            // 结束当前Activity（如果在Activity中）
            if (getActivity() != null) {
                getActivity().finish();
            }
        }
    }

    private void handleGoVip() {
        if (currentUser != null && currentUser.isLoggedIn()) {
            if (currentUser.isVip()) {
                Toast.makeText(getContext(), "You are already a VIP member!", Toast.LENGTH_SHORT).show();
            } else {
                // 跳转到充值页面
                Intent intent = new Intent(getActivity(), SubscribeActivity.class);
                startActivity(intent);
            }
        } else {
            Toast.makeText(getContext(), "Please login first to upgrade to VIP", Toast.LENGTH_SHORT).show();
        }
    }

    private void handleRefill() {
        // Refill按钮也跳转到积分页面
        handlePointsClick();
    }

    private void handlePointsClick() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
            return;
        }

        // 跳转到积分页面
        Intent intent = new Intent(getActivity(), PointsActivity.class);
        startActivity(intent);
    }

    /**
     * 处理头像点击
     */
    private void handleAvatarClick() {
        if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
            return;
        }

        // 跳转到编辑个人信息页面
        Intent intent = new Intent(getContext(), EditProfileActivity.class);
        startActivity(intent);
    }

    /**
     * 处理Edit按钮点击（VIP卡片上的Edit按钮）
     */
    private void handleEdit() {
        if (currentUser != null && currentUser.isLoggedIn() && currentUser.isVip()) {
            Toast.makeText(getContext(), "Edit VIP settings - Coming Soon!", Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "VIP access required", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 切换续费状态开关
     */
    private void toggleRenewalStatus() {
        if (currentUser != null && currentUser.isLoggedIn() && currentUser.isVip()) {
            // 切换状态
            isAutoRenewalEnabled = !isAutoRenewalEnabled;

            // 更新开关显示状态，带动画效果
            updateRenewalStatusWithAnimation();

            // 显示状态提示
            String message = isAutoRenewalEnabled ? "Auto renewal enabled" : "Auto renewal disabled";
            Toast.makeText(getContext(), message, Toast.LENGTH_SHORT).show();
        } else {
            Toast.makeText(getContext(), "VIP access required", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 更新续费状态显示（无动画）
     */
    private void updateRenewalStatusDisplay() {
        if (ivRenewalStatusOff == null || ivRenewalStatusOn == null) return;

        if (isAutoRenewalEnabled) {
            ivRenewalStatusOn.setVisibility(View.VISIBLE);
            ivRenewalStatusOff.setVisibility(View.GONE);
        } else {
            ivRenewalStatusOn.setVisibility(View.GONE);
            ivRenewalStatusOff.setVisibility(View.VISIBLE);
        }
    }

    /**
     * 更新VIP到期时间显示
     */
    private void updateVipExpirationDisplay() {
        if (tvExpirationDate == null || currentUser == null) {
            android.util.Log.w("ProfileFragment", "无法更新VIP到期时间: tvExpirationDate或currentUser为null");
            return;
        }

        android.util.Log.d("ProfileFragment", "更新VIP到期时间显示 - 用户VIP状态: " + currentUser.isVip() +
            ", VIP天数: " + currentUser.getVipDays() +
            ", VIP到期日期: " + currentUser.getVipExpireDate());

        if (currentUser.isVip()) {
            // 显示真实的VIP到期时间
            String expirationText = currentUser.getFormattedVipExpireDate();
            tvExpirationDate.setText(expirationText);

            // 如果VIP即将到期，可以改变文字颜色提醒用户
            if (currentUser.isVipExpiringSoon()) {
                tvExpirationDate.setTextColor(0xFFFF6B6B); // 红色警告
                android.util.Log.d("ProfileFragment", "VIP即将到期，显示警告颜色");
            } else {
                tvExpirationDate.setTextColor(0xFFFFFFFF); // 白色正常
            }

            android.util.Log.d("ProfileFragment", "VIP到期时间显示: " + expirationText);
        } else {
            // 非VIP用户显示默认文本
            tvExpirationDate.setText("Not VIP");
            tvExpirationDate.setTextColor(0xFFFFFFFF);
            android.util.Log.d("ProfileFragment", "非VIP用户，显示默认文本");
        }
    }

    /**
     * 带动画效果更新续费状态显示
     */
    private void updateRenewalStatusWithAnimation() {
        if (ivRenewalStatusOff == null || ivRenewalStatusOn == null) return;

        ImageView currentView = isAutoRenewalEnabled ? ivRenewalStatusOff : ivRenewalStatusOn;
        ImageView targetView = isAutoRenewalEnabled ? ivRenewalStatusOn : ivRenewalStatusOff;

        // 淡出当前开关
        currentView.animate()
                .alpha(0f)
                .setDuration(150)
                .withEndAction(() -> {
                    currentView.setVisibility(View.GONE);

                    // 淡入目标开关
                    targetView.setVisibility(View.VISIBLE);
                    targetView.setAlpha(0f);
                    targetView.animate()
                            .alpha(1f)
                            .setDuration(150)
                            .start();
                })
                .start();
    }

    private void setupMenuClickListeners(View view) {
        // Language
        LinearLayout menuLanguage = view.findViewById(R.id.menu_language);
        menuLanguage.setOnClickListener(v -> {
            showLanguageSelectionDialog();
        });

        // Message
        LinearLayout menuMessage = view.findViewById(R.id.menu_message);
        messageNotificationDot = view.findViewById(R.id.message_notification_dot);

        // 检查是否有新消息并显示红色圆点
        updateMessageNotificationDot(messageNotificationDot);

        menuMessage.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }

            Intent intent = new Intent(getContext(), MessageActivity.class);
            startActivity(intent);

            // 点击后隐藏红色圆点（表示已查看消息）
            messageNotificationDot.setVisibility(View.GONE);

            // 记录用户已查看消息
            markMessagesAsRead();
        });

        // Bill
        LinearLayout menuBill = view.findViewById(R.id.menu_bill);
        menuBill.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }

            Intent intent = new Intent(getActivity(), com.android.video.ui.activity.BillActivity.class);
            startActivity(intent);
        });

        // Setting
        LinearLayout menuSetting = view.findViewById(R.id.menu_setting);
        menuSetting.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(getContext())) {
                return;
            }

            Intent intent = new Intent(getContext(), SettingActivity.class);
            startActivity(intent);
        });
    }

    /**
     * 调整VIP卡片类型图片的尺寸和位置
     * @param isVipUse true表示使用VIP专用图片尺寸，false表示使用默认图片尺寸
     */
    private void adjustVipCardTypeSize(boolean isVipUse) {
        if (ivVipCardType != null) {
            ViewGroup.LayoutParams layoutParams = ivVipCardType.getLayoutParams();
            if (layoutParams != null && layoutParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams constraintParams =
                    (androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) layoutParams;

                if (isVipUse) {
                    // 使用VIP专用图片的尺寸
                    constraintParams.width = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_use_width);
                    constraintParams.height = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_use_height);
                    constraintParams.topMargin = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_use_margin_top);
                } else {
                    // 使用默认图片的尺寸
                    constraintParams.width = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_width);
                    constraintParams.height = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_height);
                    constraintParams.topMargin = getResources().getDimensionPixelSize(R.dimen.profile_vip_card_type_margin_top);
                }
                ivVipCardType.setLayoutParams(constraintParams);
            }
        }
    }

    /**
     * 更新消息通知圆点的显示状态
     */
    private void updateMessageNotificationDot(View notificationDot) {
        boolean hasNewMessages = false;

        // 优先使用API返回的messageReadStatus字段
        if (currentUser != null && currentUser.isLoggedIn()) {
            // 从本地获取最新的用户信息
            UserInfo userInfo = UserSessionUtils.getCurrentUserInfo(getContext());
            if (userInfo != null) {
                // 使用API返回的messageReadStatus字段
                // 1 = 有未读消息，0 = 没有未读消息
                hasNewMessages = (userInfo.getMessageReadStatus() == 1);
            } else {
                // 如果没有UserInfo，回退到原来的逻辑
                hasNewMessages = checkForNewMessages();
            }
        } else {
            // 未登录用户不显示消息红点
            hasNewMessages = false;
        }

        if (hasNewMessages) {
            notificationDot.setVisibility(View.VISIBLE);
        } else {
            notificationDot.setVisibility(View.GONE);
        }
    }

    /**
     * 检查是否有新消息
     */
    private boolean checkForNewMessages() {
        if (getContext() == null) return false;

        SharedPreferences prefs = getContext().getSharedPreferences("message_prefs", Context.MODE_PRIVATE);
        String currentUserPhone = getCurrentUserPhone();

        // 获取上次查看消息的时间
        long lastReadTime = prefs.getLong("last_read_time_" + currentUserPhone, 0);

        // 模拟新消息时间（这里可以替换为实际的服务器时间或本地消息时间）
        long latestMessageTime = System.currentTimeMillis() - (24 * 60 * 60 * 1000); // 24小时前的消息

        // 如果有比上次查看时间更新的消息，显示红色圆点
        return latestMessageTime > lastReadTime;
    }

    /**
     * 标记消息为已读
     */
    private void markMessagesAsRead() {
        if (getContext() == null) return;

        SharedPreferences prefs = getContext().getSharedPreferences("message_prefs", Context.MODE_PRIVATE);
        String currentUserPhone = getCurrentUserPhone();

        // 记录当前时间为最后查看时间
        prefs.edit()
             .putLong("last_read_time_" + currentUserPhone, System.currentTimeMillis())
             .apply();
    }

    /**
     * 获取当前用户手机号
     */
    private String getCurrentUserPhone() {
        if (currentUser != null && currentUser.getPhoneNumber() != null) {
            return currentUser.getPhoneNumber();
        }
        return "guest"; // 默认值，用于未登录用户
    }

    /**
     * 显示语言选择对话框
     */
    private void showLanguageSelectionDialog() {
        if (getContext() == null) return;

        // 添加翻译数据验证和测试
        testTranslationData();

        // 运行完整的翻译测试
        TranslationTestUtils.testTranslation(getContext());

        // 验证文本映射覆盖率
        if (getView() != null) {
            TranslationTestUtils.validateTextMappingCoverage(getView());
        }

        com.android.video.ui.dialog.LanguageSelectionDialog dialog =
            new com.android.video.ui.dialog.LanguageSelectionDialog(getContext());

        dialog.setOnLanguageSelectedListener(languageType -> {
            changeLanguage(languageType);
        });

        dialog.show();
    }

    /**
     * 测试翻译数据是否正确加载
     */
    private void testTranslationData() {
        try {
            Log.d("ProfileFragment", "=== 翻译数据测试 ===");

            if (localizationManager == null) {
                Log.e("ProfileFragment", "LocalizationManager为null");
                return;
            }

            int currentLang = localizationManager.getCurrentLanguageType();
            Log.d("ProfileFragment", "当前语言类型: " + currentLang);

            // 测试几个常用翻译
            String[] testKeys = {"home", "profile", "settings", "language"};
            for (String key : testKeys) {
                String translation = localizationManager.getString(key);
                Log.d("ProfileFragment", "翻译测试 - " + key + ": " + translation);
            }

            // 测试不同语言的翻译
            for (int lang = 1; lang <= 3; lang++) {
                String homeText = localizationManager.getStringForLanguage("home", lang);
                Log.d("ProfileFragment", "语言" + lang + " - home: " + homeText);
            }

            Log.d("ProfileFragment", "=== 翻译数据测试完成 ===");

        } catch (Exception e) {
            Log.e("ProfileFragment", "翻译数据测试失败", e);
        }
    }

    /**
     * 手动更新界面文本
     */
    private void manualUpdateTexts() {
        try {
            Log.d("ProfileFragment", "开始手动更新界面文本");

            if (getView() == null || getContext() == null) {
                Log.w("ProfileFragment", "View或Context为null，无法更新文本");
                return;
            }

            // 使用GlobalTextUpdater更新所有文本
            GlobalTextUpdater.updateViewTexts(getView(), getContext());

            // 手动更新一些关键文本
            updateSpecificTexts();

            Log.d("ProfileFragment", "手动文本更新完成");

        } catch (Exception e) {
            Log.e("ProfileFragment", "手动更新文本失败", e);
        }
    }

    /**
     * 立即测试翻译功能
     */
    private void testImmediateTranslation(int languageType) {
        try {
            Log.d("ProfileFragment", "=== 立即翻译测试开始 ===");

            // 直接设置语言并测试
            if (localizationManager != null) {
                localizationManager.setLanguage(languageType);
                UserSessionUtils.saveLanguageType(getContext(), languageType);

                // 测试翻译
                String languageText = LocalizationUtils.getString(getContext(), "language");
                String messageText = LocalizationUtils.getString(getContext(), "message");
                String billText = LocalizationUtils.getString(getContext(), "bill");
                String settingText = LocalizationUtils.getString(getContext(), "setting");

                Log.d("ProfileFragment", "语言类型 " + languageType + " 翻译结果:");
                Log.d("ProfileFragment", "  Language: " + languageText);
                Log.d("ProfileFragment", "  Message: " + messageText);
                Log.d("ProfileFragment", "  Bill: " + billText);
                Log.d("ProfileFragment", "  Setting: " + settingText);

                // 立即更新界面
                if (getView() != null) {
                    GlobalTextUpdater.updateViewTexts(getView(), getContext());
                }

                // 显示测试结果
                Toast.makeText(getContext(),
                    "Test: Language=" + languageText + ", Message=" + messageText,
                    Toast.LENGTH_LONG).show();

                // 手动更新几个关键文本作为测试
                testDirectTextUpdate();
            }

            Log.d("ProfileFragment", "=== 立即翻译测试完成 ===");

        } catch (Exception e) {
            Log.e("ProfileFragment", "立即翻译测试失败", e);
        }
    }

    /**
     * 直接更新文本测试
     */
    private void testDirectTextUpdate() {
        try {
            if (getView() == null || getContext() == null) return;

            // 查找并直接更新一些文本
            View rootView = getView();
            updateTextViewsDirectly(rootView);

        } catch (Exception e) {
            Log.e("ProfileFragment", "直接文本更新失败", e);
        }
    }

    /**
     * 递归查找并更新TextView
     */
    private void updateTextViewsDirectly(View view) {
        if (view instanceof TextView) {
            TextView textView = (TextView) view;
            String currentText = textView.getText().toString().trim();

            // 直接测试几个常见文本的翻译
            String newText = getTranslationForText(currentText);
            if (!newText.equals(currentText)) {
                textView.setText(newText);
                Log.d("ProfileFragment", "直接更新: " + currentText + " -> " + newText);
            }

        } else if (view instanceof ViewGroup) {
            ViewGroup viewGroup = (ViewGroup) view;
            for (int i = 0; i < viewGroup.getChildCount(); i++) {
                updateTextViewsDirectly(viewGroup.getChildAt(i));
            }
        }
    }

    /**
     * 获取文本的翻译
     */
    private String getTranslationForText(String text) {
        if (getContext() == null) return text;

        switch (text) {
            case "Language":
                return LocalizationUtils.getString(getContext(), "language");
            case "Message":
                return LocalizationUtils.getString(getContext(), "message");
            case "Bill":
                return LocalizationUtils.getString(getContext(), "bill");
            case "Setting":
                return LocalizationUtils.getString(getContext(), "setting");
            case "Guest":
                return LocalizationUtils.getString(getContext(), "guest");
            case "Log in":
                return LocalizationUtils.getString(getContext(), "log_in");
            case "My points":
                return LocalizationUtils.getString(getContext(), "my_points");
            case "Unlimited access to all series":
                return LocalizationUtils.getString(getContext(), "unlimited_access_to_all_series");
            default:
                return text;
        }
    }

    /**
     * 更新特定的文本元素
     */
    private void updateSpecificTexts() {
        try {
            Log.d("ProfileFragment", "开始更新特定文本元素");

            // 测试几个翻译
            String languageText = LocalizationUtils.getString(getContext(), "language");
            String messageText = LocalizationUtils.getString(getContext(), "message");
            String billText = LocalizationUtils.getString(getContext(), "bill");
            String settingText = LocalizationUtils.getString(getContext(), "setting");

            Log.d("ProfileFragment", "翻译结果 - Language: " + languageText);
            Log.d("ProfileFragment", "翻译结果 - Message: " + messageText);
            Log.d("ProfileFragment", "翻译结果 - Bill: " + billText);
            Log.d("ProfileFragment", "翻译结果 - Setting: " + settingText);

        } catch (Exception e) {
            Log.e("ProfileFragment", "更新特定文本失败", e);
        }
    }







    /**
     * 切换语言
     */
    private void changeLanguage(int languageType) {
        if (getContext() == null) return;

        // 显示加载提示
        Toast.makeText(getContext(), "Changing language...", Toast.LENGTH_SHORT).show();

        // 添加调试日志
        Log.d("ProfileFragment", "开始切换语言到类型: " + languageType);

        // 立即进行简单测试
        testImmediateTranslation(languageType);



        // 调用API切换语言
        com.android.video.network.ProfileApiService.getInstance().changeLanguage(
            languageType,
            new com.android.video.network.ProfileApiService.LanguageChangeCallback() {
                @Override
                public void onSuccess() {
                    Log.d("ProfileFragment", "API语言切换成功: " + languageType);
                    if (getContext() != null && isAdded()) {
                        // 保存到本地
                        UserSessionUtils.saveLanguageType(getContext(), languageType);

                        // 使用新的重启机制（更彻底的解决方案）
                        if (localizationManager != null) {
                            localizationManager.setLanguageAndRestart(languageType, getActivity());
                        } else {
                            // 备用方案：手动更新
                            UserSessionUtils.saveLanguageType(getContext(), languageType);
                            manualUpdateTexts();
                        }

                        // 显示成功提示（使用本地化文本）
                        String successMessage = LocalizationUtils.getString(getContext(), "language_changed_successfully");
                        if (successMessage.equals("language_changed_successfully")) {
                            // 如果没有找到翻译，使用默认文本
                            successMessage = "Language changed successfully";
                        }
                        Toast.makeText(getContext(), successMessage, Toast.LENGTH_SHORT).show();
                    }
                }

                @Override
                public void onError(String errorMessage) {
                    Log.e("ProfileFragment", "API语言切换失败: " + errorMessage);
                    if (getContext() != null && isAdded()) {
                        // 显示错误提示
                        Toast.makeText(getContext(), "Failed to change language: " + errorMessage, Toast.LENGTH_LONG).show();


                    }
                }
            }
        );
    }



    @Override
    protected void updateAllTexts() {
        super.updateAllTexts();

        // 更新用户名显示
        if (tvUsername != null) {
            if (currentUser != null && currentUser.getUsername() != null) {
                tvUsername.setText(currentUser.getUsername());
            } else {
                String guestText = LocalizationUtils.getString(getContext(), "guest");
                tvUsername.setText(guestText);
            }
        }

        // 更新登录/登出按钮文本
        if (btnLogin != null) {
            if (currentUser != null) {
                String logoutText = LocalizationUtils.getString(getContext(), "log_out");
                btnLogin.setText(logoutText);
            } else {
                String loginText = LocalizationUtils.getString(getContext(), "log_in");
                btnLogin.setText(loginText);
            }
        }
    }
}
