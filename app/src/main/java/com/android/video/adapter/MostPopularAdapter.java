package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import java.util.ArrayList;
import java.util.List;

/**
 * Most Popular页面专用RecyclerView适配器
 * 与首页PopularSeriesAdapter的区别：视频标题字号为16sp
 * <AUTHOR>
 */
public class MostPopularAdapter extends RecyclerView.Adapter<MostPopularAdapter.MostPopularViewHolder> {
    
    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    
    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }
    
    /**
     * 构造函数
     */
    public MostPopularAdapter() {
        this.videoList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public MostPopularAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }
    
    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }
    
    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList != null ? newVideoList : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 添加视频到列表
     * @param video 视频数据
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }
    
    /**
     * 移除指定位置的视频
     * @param position 位置
     */
    public void removeVideo(int position) {
        if (position >= 0 && position < videoList.size()) {
            videoList.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }
    
    @NonNull
    @Override
    public MostPopularViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_most_popular_video, parent, false);
        return new MostPopularViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull MostPopularViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }
    
    @Override
    public int getItemCount() {
        return videoList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class MostPopularViewHolder extends RecyclerView.ViewHolder {
        
        private ConstraintLayout clPosterContainer;
        private ImageView ivVideoPoster;
        private View viewGradientOverlay;
        private ConstraintLayout clInfoContainer;
        private TextView tvVideoTitle;
        private TextView tvVideoDescription;
        private ConstraintLayout clPlayCount;
        private ImageView ivPlayCountIcon;
        private TextView tvPlayCount;

        public MostPopularViewHolder(@NonNull View itemView) {
            super(itemView);
            clPosterContainer = itemView.findViewById(R.id.cl_poster_container);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            viewGradientOverlay = itemView.findViewById(R.id.view_gradient_overlay);
            clInfoContainer = itemView.findViewById(R.id.cl_info_container);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            tvVideoDescription = itemView.findViewById(R.id.tv_video_description);
            clPlayCount = itemView.findViewById(R.id.cl_play_count);
            ivPlayCountIcon = itemView.findViewById(R.id.iv_play_count_icon);
            tvPlayCount = itemView.findViewById(R.id.tv_play_count);
        }
        
        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }
            
            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            android.util.Log.d("MostPopularAdapter", "Loading poster for: " + video.getTitle() + ", URL: " + posterUrl);

            if (posterUrl != null && !posterUrl.isEmpty() && !posterUrl.equals("https://www.baidu.com")) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .diskCacheStrategy(DiskCacheStrategy.ALL)
                    .centerCrop()
                    .into(ivVideoPoster);
                android.util.Log.d("MostPopularAdapter", "Loading image from URL: " + posterUrl);
            } else {
                // 如果没有URL或URL是测试数据，使用默认图片
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
                android.util.Log.d("MostPopularAdapter", "Using default poster for: " + video.getTitle());
            }
            
            // 设置视频标题 - 使用16sp字号
            tvVideoTitle.setText(video.getDisplayTitle());

            // 设置视频介绍
            if (video.getDescription() != null && !video.getDescription().isEmpty()) {
                tvVideoDescription.setText(video.getDescription());
                tvVideoDescription.setVisibility(View.VISIBLE);
            } else {
                tvVideoDescription.setText("精彩的视频内容，值得观看");
                tvVideoDescription.setVisibility(View.VISIBLE);
            }

            // 设置播放次数
            if (video.getViewCount() > 0) {
                tvPlayCount.setText(video.getFormattedViewCount() + " Views");
                clPlayCount.setVisibility(View.VISIBLE);
            } else {
                clPlayCount.setVisibility(View.GONE);
            }
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                } else {
                    // 默认Toast提示
                    String viewCountText = video.getViewCount() > 0 ? 
                            " (" + video.getFormattedViewCount() + " Views)" : "";
                    Toast.makeText(itemView.getContext(), 
                            "Most Popular视频点击: " + video.getDisplayTitle() + viewCountText, 
                            Toast.LENGTH_SHORT).show();
                }
            });
            
            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                String viewCountText = video.getViewCount() > 0 ? 
                        "\n播放次数: " + video.getFormattedViewCount() + " Views" : "";
                Toast.makeText(itemView.getContext(), 
                        "长按: " + video.getDisplayTitle() + 
                        "\n分类: " + video.getCategory() + viewCountText, 
                        Toast.LENGTH_SHORT).show();
                return true;
            });
        }
    }
}
