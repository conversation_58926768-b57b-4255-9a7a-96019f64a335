package com.android.video.constants;

/**
 * 公共API常量类 - 定义公共API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理公共API常量，
 * 包括文件上传等通用功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的公共功能：
 * <ul>
 *   <li>文件上传管理</li>
 *   <li>通用资源管理</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 公共API模块特点：
 * <ul>
 *   <li>可能需要用户登录认证</li>
 *   <li>支持多种文件类型上传</li>
 *   <li>提供统一的文件管理服务</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建文件上传API URL
 * String url = CommonApiConstantsUtils.buildApiUrl(baseUrl, CommonApiConstantsUtils.API_UPLOAD_FILE);
 * 
 * // 使用请求头
 * Map&lt;String, String&gt; headers = ApiHeaderUtils.getDefaultHeaders();
 * 
 * // 解析响应数据
 * String fileUrl = response.getString(CommonApiConstantsUtils.FIELD_FILE_URL);
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class CommonApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 公共API路径常量 ==========

    /**
     * 文件上传API路径
     * <p>
     * 用于上传文件（如头像图片）到服务器。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * 内容类型：multipart/form-data
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>file: 文件数据 (binary, 可选)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "https://short-play-cdn.hb.kz-ast.bizmrg.com/short_play/80a69bdd-c2cc-4758-8c6a-ca323e17af6b_1.jpg"
     * }
     * </pre>
     * </p>
     */
    public static final String API_UPLOAD_FILE = "/app/common/file/upload";

    // ========== 公共API响应字段常量 ==========

    /**
     * 文件URL字段名
     * <p>
     * 文件上传成功后返回的文件URL，直接在data字段中。
     * 字段类型：String
     * 示例值：https://short-play-cdn.hb.kz-ast.bizmrg.com/short_play/80a69bdd-c2cc-4758-8c6a-ca323e17af6b_1.jpg
     * </p>
     */
    public static final String FIELD_FILE_URL = "data";

    // ========== 请求参数常量 ==========

    /**
     * 文件参数名
     * <p>
     * 用于文件上传API的文件参数名。
     * 参数类型：File (binary)
     * </p>
     */
    public static final String PARAM_FILE = "file";

    // ========== 公共API模块工具方法 ==========

    /**
     * 检查是否为公共API相关的API路径
     * <p>
     * 判断给定的API路径是否属于公共API模块。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是公共API相关API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isCommon = isCommonApi("/app/common/file/upload");     // true
     * boolean isCommon = isCommonApi("/app/customer/info");          // false
     * </pre>
     */
    public static boolean isCommonApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }

        return apiPath.equals(API_UPLOAD_FILE);
    }

    /**
     * 验证文件URL格式
     * <p>
     * 检查给定的URL是否为有效的文件URL格式。
     * </p>
     *
     * @param fileUrl 文件URL
     * @return 如果URL格式有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidFileUrl("https://example.com/file.jpg");  // true
     * boolean valid = isValidFileUrl("invalid-url");                   // false
     * </pre>
     */
    public static boolean isValidFileUrl(String fileUrl) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return false;
        }

        String url = fileUrl.trim();
        return url.startsWith("http://") || url.startsWith("https://");
    }

    /**
     * 获取文件扩展名
     * <p>
     * 从文件URL中提取文件扩展名。
     * </p>
     *
     * @param fileUrl 文件URL
     * @return 文件扩展名，如果无法提取则返回空字符串
     *
     * @example
     * <pre>
     * String ext = getFileExtension("https://example.com/file.jpg");  // "jpg"
     * String ext = getFileExtension("https://example.com/file");      // ""
     * </pre>
     */
    public static String getFileExtension(String fileUrl) {
        if (fileUrl == null || fileUrl.trim().isEmpty()) {
            return "";
        }

        String url = fileUrl.trim();
        int lastDotIndex = url.lastIndexOf('.');
        int lastSlashIndex = url.lastIndexOf('/');

        // 确保点在最后一个斜杠之后
        if (lastDotIndex > lastSlashIndex && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1);
        }

        return "";
    }

    /**
     * 检查是否为图片文件URL
     * <p>
     * 根据文件扩展名判断是否为图片文件。
     * </p>
     *
     * @param fileUrl 文件URL
     * @return 如果是图片文件则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isImage = isImageFile("https://example.com/avatar.jpg");  // true
     * boolean isImage = isImageFile("https://example.com/doc.pdf");     // false
     * </pre>
     */
    public static boolean isImageFile(String fileUrl) {
        String extension = getFileExtension(fileUrl).toLowerCase();
        return extension.equals("jpg") || extension.equals("jpeg") || 
               extension.equals("png") || extension.equals("gif") || 
               extension.equals("bmp") || extension.equals("webp");
    }
}
