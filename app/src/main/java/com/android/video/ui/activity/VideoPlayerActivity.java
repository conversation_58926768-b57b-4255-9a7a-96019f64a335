package com.android.video.ui.activity;

import android.app.Activity;
import android.content.Intent;
import android.content.pm.ActivityInfo;
import android.content.res.Configuration;
import android.net.Uri;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.MotionEvent;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.ProgressBar;
import android.widget.SeekBar;
import android.widget.TextView;
import android.widget.Toast;

import androidx.core.view.WindowInsetsCompat;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import androidx.viewpager2.widget.ViewPager2;

import com.android.video.R;
import com.android.video.adapter.VideoPlayerPagerAdapter;
import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.model.TestVideoModel;
import com.android.video.model.VideoModel;
import com.android.video.model.EpisodeModel;
import com.android.video.model.PointsPackage;
import com.android.video.model.VipConfigModel;
import com.android.video.model.UserModel;
import com.android.video.model.response.VipConfigListResponseModel;
import com.android.video.player.VideoPlayerController;
import com.android.video.player.VideoPlayerListener;
import com.android.video.player.VideoPlayerManager;
import com.android.video.player.VideoQualitySelector;
import com.android.video.ui.component.QualitySelectionView;
import com.android.video.ui.component.SpeedSelectionView;
import com.android.video.ui.component.EpisodeSelectionView;
import com.android.video.ui.component.SubtitlePanelView;
import com.android.video.ui.component.BoundaryControlledViewPager2;
import com.android.video.ui.fragment.VideoPlayerFragment;
import com.android.video.utils.VideoPlayerPreferences;
import com.android.video.utils.VideoResourceManager;
import com.android.video.utils.UserSessionUtils;
import com.android.video.network.PointsApiService;
import com.android.video.monitor.ScreenRecordingMonitor;
import com.android.video.network.VipConfigApiService;
import com.android.video.ui.dialog.PaymentMethodDialog;
import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.manager.FloatingVideoManager;
import com.android.video.utils.FloatingVideoPermissionUtils;
import com.android.video.network.NetworkOptimizationManager;
import com.android.video.network.NetworkUtils;
import com.android.video.ui.component.PoorNetworkWarningView;

import java.util.ArrayList;
import java.util.List;

/**
 * 视频播放页面Activity
 * 实现类似抖音快手的短视频播放功能
 * <AUTHOR>
 */
public class VideoPlayerActivity extends BaseFullScreenActivity implements VideoPlayerListener {

    private static final String TAG = "VideoPlayerActivity";
    public static final String EXTRA_VIDEO_LIST = "extra_video_list";
    public static final String EXTRA_CURRENT_INDEX = "extra_current_index";
    private static final String EXTRA_VIDEO_MODEL = "extra_video_model";
    public static final String EXTRA_SELECTED_EPISODE = "extra_selected_episode";

    // UI Components
    private BoundaryControlledViewPager2 viewPagerVideos;
    private LinearLayout topControlBar;
    private LinearLayout sideControlButtons;
    private LinearLayout bottomInfoArea;

    // Control Buttons
    private ImageView btnBack;
    private ImageView btnSmallScreen;
    private ImageView btnDownload;
    private ImageView btnLike;
    private ImageView btnShare;
    private ImageView btnComment;
    private ImageView btnDetail;
    private ImageView btnFullscreen;

    // Info Views
    private TextView tvVideoTitle;
    private TextView tvVideoDescription;
    private TextView tvLikeCount;
    private TextView tvShareCount;
    private TextView tvCommentCount;
    private SeekBar progressSeekBar;
    private boolean isDescriptionExpanded = false;
    private float currentPlaybackSpeed = 1.0f;
    private VideoQualitySelector.VideoQuality currentQuality = VideoQualitySelector.VideoQuality.QUALITY_720P;
    private boolean isUserSeeking = false;

    // Control Views
    private LinearLayout btnEpisodeSelection;
    private TextView tvEpisodeText;
    private ImageView btnDanmaku;
    private TextView btnSpeedSelection;
    private TextView btnQualitySelection;

    // Data
    private List<VideoModel> videoList;
    private int currentVideoIndex;
    private VideoModel currentVideo;
    private VideoPlayerPagerAdapter pagerAdapter;

    // Player Management - 移除Activity级别的播放器，只使用Fragment级别的播放器
    private Handler uiHandler;
    private boolean isFullscreen = false;
    private boolean isControlsVisible = true;
    private boolean areControlsVisibleInFullscreen = true;
    private Runnable hideControlsRunnable;
    private Runnable hideFullscreenControlsRunnable;

    // Player State
    private boolean isDanmakuEnabled = true;

    // Poster Popup Views
    private FrameLayout posterPopupOverlay;
    private LinearLayout posterContainer;
    private ImageView moviePosterImage;
    private ImageView btnClosePoster;

    // VIP Unlock Popup Views
    private FrameLayout vipUnlockPopupOverlay;
    private LinearLayout vipUnlockContainer;
    private TextView tvVipMessage;
    private LinearLayout btnUnlockNow;
    private LinearLayout btnWatchAd;
    private LinearLayout btnOpenVip;
    private TextView tvCoinCount;
    private ImageView btnCloseVipPopup;

    // Watch AD Popup Views
    private FrameLayout watchAdPopupOverlay;
    private LinearLayout watchAdContainer;
    private TextView tvWatchAdCoinCount;

    // Recharge Popup Views
    private FrameLayout rechargePopupOverlay;
    private FrameLayout rechargeContainer;
    private ImageView btnCloseRechargePopup;
    private TextView tvRechargeCurrentCoins;
    private LinearLayout rechargeCard500;
    private LinearLayout rechargeCard1000;
    private LinearLayout rechargeCard2000;
    private LinearLayout rechargeCard3000;
    private LinearLayout rechargeWeeklyCard;
    private LinearLayout rechargeMonthlyCard;
    private LinearLayout rechargeVipCardsContainer;
    private SwipeRefreshLayout swipeRefreshRechargeLoading;
    private int selectedRechargeAmount = 0;
    private boolean isWeeklySelected = false;
    private boolean isMonthlySelected = false;

    // Data members for real data integration
    private List<PointsPackage> pointsPackages;
    private List<VipConfigModel> vipConfigs;
    private UserModel currentUser;
    private PointsPackage selectedPointsPackage;
    private VipConfigModel selectedVipConfig;
    private LinearLayout selectedRechargeCard;

    // API services
    private PointsApiService pointsApiService;
    private VipConfigApiService vipConfigApiService;

    // Loading state management
    private int loadingTasksCount = 0;
    private final int TOTAL_LOADING_TASKS = 2; // Points packages + VIP configs

    // Advanced Control Components
    private QualitySelectionView qualitySelectionView;
    private SpeedSelectionView speedSelectionView;
    private EpisodeSelectionView episodeSelectionView;
    private SubtitlePanelView subtitlePanelView;
    private VideoPlayerPreferences preferences;

    // Progress Update
    private Runnable progressUpdateRunnable;
    private boolean shouldUpdateProgress = false;

    // Network Optimization
    private NetworkOptimizationManager networkOptimizationManager;
    private PoorNetworkWarningView poorNetworkWarningView;
    private int currentPreloadCount = 5; // 默认预加载数量

    // Content Protection
    private ScreenRecordingMonitor recordingMonitor;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_video_player);

        initializeData();
        initializeViews();
        // 移除initializePlayer() - 不再需要Activity级别的播放器
        initializeAdvancedComponents();
        initializeNetworkOptimization();
        initializeContentProtection(); // 初始化内容保护功能
        setupViewPager();
        setupClickListeners();
        setupAutoHideControls();
        loadUserPreferences();

        // 初始化进度更新任务
        initializeProgressUpdate();

        // Load initial video
        if (currentVideo != null) {
            loadVideoData(currentVideo);
        }
    }

    /**
     * 初始化数据
     */
    private void initializeData() {
        Intent intent = getIntent();

        try {
            // 获取视频列表和当前索引
            if (intent.hasExtra(EXTRA_VIDEO_LIST)) {
                Object videoListObj = intent.getSerializableExtra(EXTRA_VIDEO_LIST);
                if (videoListObj instanceof List<?>) {
                    videoList = (List<VideoModel>) videoListObj;
                    currentVideoIndex = intent.getIntExtra(EXTRA_CURRENT_INDEX, 0);
                    Log.d(TAG, "Received video list with " + videoList.size() + " videos, current index: " + currentVideoIndex);
                } else {
                    Log.e(TAG, "Invalid video list data type");
                    createTestVideoData();
                }
            } else if (intent.hasExtra(EXTRA_VIDEO_MODEL)) {
                // 单个视频模式
                Object videoObj = intent.getSerializableExtra(EXTRA_VIDEO_MODEL);
                if (videoObj instanceof VideoModel) {
                    VideoModel video = (VideoModel) videoObj;
                    videoList = new ArrayList<>();
                    videoList.add(video);
                    currentVideoIndex = 0;
                    Log.d(TAG, "Received single video: " + video.getTitle());
                } else {
                    Log.e(TAG, "Invalid video model data type");
                    createTestVideoData();
                }
            } else {
                // 创建默认测试数据
                Log.d(TAG, "No video data provided, creating test data");
                createTestVideoData();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error initializing video data", e);
            createTestVideoData();
        }

        // 验证数据完整性
        if (!validateVideoData()) {
            Log.e(TAG, "Video data validation failed, creating fallback data");
            createTestVideoData();
        }

        if (videoList != null && !videoList.isEmpty() &&
            currentVideoIndex >= 0 && currentVideoIndex < videoList.size()) {
            currentVideo = videoList.get(currentVideoIndex);

            // 处理选中的剧集
            if (intent.hasExtra(EXTRA_SELECTED_EPISODE)) {
                int selectedEpisode = intent.getIntExtra(EXTRA_SELECTED_EPISODE, 0);
                if (currentVideo.hasMultipleEpisodes()) {
                    currentVideo.setSelectedEpisodeIndex(selectedEpisode);
                    Log.d(TAG, "Set selected episode to: " + selectedEpisode);
                }
            }
        }

        uiHandler = new Handler(Looper.getMainLooper());
        Log.d(TAG, "Data initialization completed");
    }

    /**
     * 验证视频数据的完整性
     */
    private boolean validateVideoData() {
        if (videoList == null || videoList.isEmpty()) {
            Log.w(TAG, "Video list is null or empty");
            return false;
        }

        if (currentVideoIndex < 0 || currentVideoIndex >= videoList.size()) {
            Log.w(TAG, "Invalid current video index: " + currentVideoIndex + ", list size: " + videoList.size());
            currentVideoIndex = 0; // 重置为第一个视频
        }

        // 验证视频模型的基本字段
        for (int i = 0; i < videoList.size(); i++) {
            VideoModel video = videoList.get(i);
            if (video == null) {
                Log.w(TAG, "Video at index " + i + " is null");
                return false;
            }

            if (video.getId() == null || video.getId().isEmpty()) {
                Log.w(TAG, "Video at index " + i + " has invalid ID");
                video.setId("video_" + i); // 设置默认ID
            }

            if (video.getTitle() == null || video.getTitle().isEmpty()) {
                Log.w(TAG, "Video at index " + i + " has invalid title");
                video.setTitle("Video " + (i + 1)); // 设置默认标题
            }
        }

        return true;
    }

    /**
     * 创建测试视频数据
     */
    private void createTestVideoData() {
        videoList = new ArrayList<>();

        // 初始化VideoResourceManager
        VideoResourceManager resourceManager = VideoResourceManager.getInstance();
        if (!resourceManager.isInitialized()) {
            resourceManager.initialize(this);
        }

        // 获取测试视频并转换为VideoModel
        List<com.android.video.model.TestVideoModel> testVideos = resourceManager.getValidTestVideos();

        if (!testVideos.isEmpty()) {
            for (com.android.video.model.TestVideoModel testVideo : testVideos) {
                VideoModel videoModel = convertTestVideoToVideoModel(testVideo);
                videoList.add(videoModel);
            }
        } else {
            // 如果没有测试视频，创建默认视频
            createFallbackVideoData();
        }

        currentVideoIndex = 0;
        Log.d(TAG, "Created test video data with " + videoList.size() + " videos");
    }

    /**
     * 将TestVideoModel转换为VideoModel
     */
    private VideoModel convertTestVideoToVideoModel(com.android.video.model.TestVideoModel testVideo) {
        VideoModel videoModel = new VideoModel();
        videoModel.setId(testVideo.getId());
        videoModel.setTitle(testVideo.getTitle());
        videoModel.setDescription(testVideo.getDescription());
        videoModel.setDuration(testVideo.getDurationText());
        videoModel.setRating(testVideo.getRating());
        videoModel.setViewCount(testVideo.getViewCount());
        videoModel.setLiked(testVideo.isLiked());
        videoModel.setCategory(testVideo.getCategory());
        videoModel.setTags(new ArrayList<>(testVideo.getTags()));

        // 添加测试集数数据
        if (testVideo.getCategory().contains("电影")) {
            // 电影类型设置为单集
            videoModel.setTotalEpisodes(1);
            videoModel.setCurrentEpisode(1);
        } else {
            // 其他类型设置为多集
            videoModel.setTotalEpisodes(72);
            videoModel.setCurrentEpisode(1);
        }

        // 转换视频URI信息
        if (testVideo.getVideoUri() != null) {
            videoModel.setVideoUri(testVideo.getVideoUri());
        } else if (testVideo.getResourceId() > 0) {
            // 如果有资源ID，构建URI
            Uri uri = Uri.parse("android.resource://" + getPackageName() + "/" + testVideo.getResourceId());
            videoModel.setVideoUri(uri);
        }

        return videoModel;
    }

    /**
     * 创建备用视频数据
     */
    private void createFallbackVideoData() {
        VideoModel testVideo = new VideoModel();
        testVideo.setId("fallback_video_1");
        testVideo.setTitle("默认测试视频");
        testVideo.setDescription("这是一个默认的测试视频，用于演示视频播放器的功能。");
        testVideo.setLiked(false);
        testVideo.setViewCount(12000);
        testVideo.setCategory("测试视频");
        testVideo.setRating(4.5f);
        testVideo.setDuration("02:00");
        testVideo.setTotalEpisodes(24);
        testVideo.setCurrentEpisode(1);

        videoList.add(testVideo);
        Log.d(TAG, "Created fallback video data");
    }

    /**
     * 初始化视图
     */
    private void initializeViews() {
        // ViewPager2
        viewPagerVideos = findViewById(R.id.viewPagerVideos);

        // Control Areas
        topControlBar = findViewById(R.id.topControlBar);
        sideControlButtons = findViewById(R.id.sideControlButtons);
        bottomInfoArea = findViewById(R.id.bottomInfoArea);

        // Top Control Buttons
        btnBack = findViewById(R.id.btnBack);
        btnSmallScreen = findViewById(R.id.btnSmallScreen);
        btnDownload = findViewById(R.id.btnDownload);

        // Side Control Buttons
        btnLike = findViewById(R.id.btnLike);
        btnShare = findViewById(R.id.btnShare);
        btnComment = findViewById(R.id.btnComment);

        // Info Views
        tvVideoTitle = findViewById(R.id.tvVideoTitle);
        tvVideoDescription = findViewById(R.id.tvVideoDescription);
        tvLikeCount = findViewById(R.id.tvLikeCount);
        tvShareCount = findViewById(R.id.tvShareCount);
        tvCommentCount = findViewById(R.id.tvCommentCount);
        progressSeekBar = findViewById(R.id.progressSeekBar);
        setupProgressSeekBar();
        btnDetail = findViewById(R.id.btnDetail);

        // Bottom Control Views
        btnEpisodeSelection = findViewById(R.id.btnEpisodeSelection);
        tvEpisodeText = findViewById(R.id.tvEpisodeText);
        btnDanmaku = findViewById(R.id.btnDanmaku);
        btnSpeedSelection = findViewById(R.id.btnSpeedSelection);
        btnQualitySelection = findViewById(R.id.btnQualitySelection);
        btnFullscreen = findViewById(R.id.btnFullscreen);

        // Progress Touch Area
        View progressTouchArea = findViewById(R.id.progressTouchArea);
        setupProgressTouchArea(progressTouchArea);

        // Poster Popup Views
        posterPopupOverlay = findViewById(R.id.posterPopupOverlay);
        posterContainer = findViewById(R.id.posterContainer);
        moviePosterImage = findViewById(R.id.moviePosterImage);
        btnClosePoster = findViewById(R.id.btnClosePoster);

        // VIP Unlock Popup Views
        vipUnlockPopupOverlay = findViewById(R.id.vipUnlockPopupOverlay);
        vipUnlockContainer = findViewById(R.id.vipUnlockContainer);
        tvVipMessage = findViewById(R.id.tvVipMessage);
        btnUnlockNow = findViewById(R.id.btnUnlockNow);
        btnWatchAd = findViewById(R.id.btnWatchAd);
        btnOpenVip = findViewById(R.id.btnOpenVip);
        tvCoinCount = findViewById(R.id.tvCoinCount);
        btnCloseVipPopup = findViewById(R.id.btnCloseVipPopup);

        // Watch AD Popup Views
        watchAdPopupOverlay = findViewById(R.id.watchAdPopupOverlay);
        watchAdContainer = findViewById(R.id.watchAdContainer);
        tvWatchAdCoinCount = findViewById(R.id.tvWatchAdCoinCount);

        // Recharge Popup Views
        rechargePopupOverlay = findViewById(R.id.rechargePopupOverlay);
        rechargeContainer = findViewById(R.id.rechargeContainer);
        btnCloseRechargePopup = findViewById(R.id.btnCloseRechargePopup);
        tvRechargeCurrentCoins = findViewById(R.id.tvRechargeCurrentCoins);
        rechargeCard500 = findViewById(R.id.rechargeCard500);
        rechargeCard1000 = findViewById(R.id.rechargeCard1000);
        rechargeCard2000 = findViewById(R.id.rechargeCard2000);
        rechargeCard3000 = findViewById(R.id.rechargeCard3000);
        rechargeWeeklyCard = findViewById(R.id.rechargeWeeklyCard);
        rechargeMonthlyCard = findViewById(R.id.rechargeMonthlyCard);
        rechargeVipCardsContainer = findViewById(R.id.rechargeVipCardsContainer);
        swipeRefreshRechargeLoading = findViewById(R.id.swipe_refresh_recharge_loading);

        // Set default values
        btnSpeedSelection.setText("1.0x");
        btnQualitySelection.setText("720P");

        // Initialize API services
        pointsApiService = PointsApiService.getInstance();
        vipConfigApiService = new VipConfigApiService();
    }

    // 移除initializePlayer方法 - Activity不再需要自己的播放器实例

    /**
     * 初始化高级控制组件
     */
    private void initializeAdvancedComponents() {
        preferences = new VideoPlayerPreferences(this);

        // 初始化清晰度选择组件
        qualitySelectionView = new QualitySelectionView(this);
        // 设置可用的清晰度选项
        qualitySelectionView.setSelectedQuality(currentQuality);
        qualitySelectionView.setOnQualitySelectedListener(quality -> {
            currentQuality = quality;
            // 通知当前Fragment更改清晰度
            notifyCurrentFragmentQualityChange(quality);
            // 更新按钮文本
            updateQualityButton();
        });

        // 初始化速度选择组件
        speedSelectionView = new SpeedSelectionView(this);
        speedSelectionView.setSelectedSpeed(currentPlaybackSpeed);
        speedSelectionView.setOnSpeedSelectedListener(speed -> {
            currentPlaybackSpeed = speed;
            // 通知当前Fragment更改播放速度
            notifyCurrentFragmentSpeedChange(speed);
            preferences.savePlaybackSpeed(speed);
            // 更新按钮文本
            updateSpeedButton();
        });

        // 初始化选集选择组件
        episodeSelectionView = new EpisodeSelectionView(this);
        episodeSelectionView.setOnEpisodeSelectedListener(episode -> {
            if (currentVideo != null) {
                Log.d(TAG, "Episode selected: " + episode.getEpisodeNumber());
                currentVideo.setCurrentEpisode(episode.getEpisodeNumber());
                loadVideoData(currentVideo);

                // 通知当前Fragment重新加载视频
                reloadCurrentVideo();
            }
        });
        episodeSelectionView.setOnVipUnlockRequestListener(() -> {
            showVipUnlockPopup();
        });

        // 初始化字幕面板
        subtitlePanelView = new SubtitlePanelView(this);
        subtitlePanelView.setOnSubtitleSettingsChangeListener(new SubtitlePanelView.OnSubtitleSettingsChangeListener() {
            @Override
            public void onSubtitleEnabledChanged(boolean enabled) {
                isDanmakuEnabled = enabled;
                updateDanmakuButtonState();
                if (preferences != null) {
                    preferences.saveDanmakuEnabled(enabled);
                }

                // 控制当前Fragment的弹幕显示
                VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                if (currentFragment != null) {
                    currentFragment.toggleDanmaku(enabled);
                }

                // 更新按钮状态
                updateDanmakuButtonState();
            }

            @Override
            public void onLanguageChanged(String language) {
                // 字幕语言切换，移除提示信息
            }

            @Override
            public void onPanelClosed() {
                // 面板关闭时的处理
            }
        });

        // 设置字幕面板的初始状态（与用户设置同步）
        subtitlePanelView.setSubtitleEnabled(isDanmakuEnabled);

        // 更新弹幕按钮状态
        updateDanmakuButtonState();

        // 将组件添加到主布局
        ViewGroup rootLayout = findViewById(android.R.id.content);

        // 为弹窗组件设置布局参数
        android.widget.FrameLayout.LayoutParams popupParams =
                new android.widget.FrameLayout.LayoutParams(
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT,
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
        popupParams.gravity = android.view.Gravity.TOP | android.view.Gravity.START;

        qualitySelectionView.setLayoutParams(popupParams);
        speedSelectionView.setLayoutParams(popupParams);

        // 选集弹窗使用底部定位，与弹幕弹窗一致
        android.widget.FrameLayout.LayoutParams episodeParams =
                new android.widget.FrameLayout.LayoutParams(
                        android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
        episodeParams.gravity = android.view.Gravity.BOTTOM;
        episodeSelectionView.setLayoutParams(episodeParams);

        // 字幕面板设置底部布局参数
        android.widget.FrameLayout.LayoutParams subtitleParams =
                new android.widget.FrameLayout.LayoutParams(
                        android.widget.FrameLayout.LayoutParams.MATCH_PARENT,
                        android.widget.FrameLayout.LayoutParams.WRAP_CONTENT);
        subtitleParams.gravity = android.view.Gravity.BOTTOM;
        subtitlePanelView.setLayoutParams(subtitleParams);

        rootLayout.addView(qualitySelectionView);
        rootLayout.addView(speedSelectionView);
        rootLayout.addView(episodeSelectionView);
        rootLayout.addView(subtitlePanelView);

        // 设置组件位置（右下角）
        positionSelectionViews();
    }

    /**
     * 设置选择组件的位置
     */
    private void positionSelectionViews() {
        // 清晰度选择组件位置
        ViewGroup.MarginLayoutParams qualityParams =
            (ViewGroup.MarginLayoutParams) qualitySelectionView.getLayoutParams();
        qualityParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.video_player_side_button_margin_end);
        qualityParams.bottomMargin = 200; // 距离底部200dp
        qualitySelectionView.setLayoutParams(qualityParams);

        // 速度选择组件位置
        ViewGroup.MarginLayoutParams speedParams =
            (ViewGroup.MarginLayoutParams) speedSelectionView.getLayoutParams();
        speedParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.video_player_side_button_margin_end) + 70;
        speedParams.bottomMargin = 200;
        speedSelectionView.setLayoutParams(speedParams);

        // 选集选择组件位置（底部）- 与弹幕弹窗保持一致
        ViewGroup.MarginLayoutParams episodeMarginParams =
            (ViewGroup.MarginLayoutParams) episodeSelectionView.getLayoutParams();
        if (episodeMarginParams != null) {
            episodeMarginParams.bottomMargin = 0;
            episodeMarginParams.leftMargin = 0;
            episodeMarginParams.rightMargin = 0;
            episodeSelectionView.setLayoutParams(episodeMarginParams);
        }

        // 字幕面板使用gravity定位，不需要设置margin
    }

    /**
     * 加载用户偏好设置
     */
    private void loadUserPreferences() {
        // 加载播放速度
        currentPlaybackSpeed = preferences.getPlaybackSpeed();
        updateSpeedButton();

        // 加载清晰度设置
        currentQuality = preferences.getVideoQuality();
        updateQualityButton();

        // 加载弹幕设置
        isDanmakuEnabled = preferences.isDanmakuEnabled();

        // 注意：音量和其他播放器设置将在Fragment级别处理
    }

    /**
     * 初始化网络优化功能
     */
    private void initializeNetworkOptimization() {
        // 初始化网络优化管理器
        networkOptimizationManager = NetworkOptimizationManager.getInstance(this);

        // 设置网络优化监听器
        networkOptimizationManager.setNetworkOptimizationListener(new NetworkOptimizationManager.NetworkOptimizationListener() {
            @Override
            public void onNetworkQualityChanged(NetworkUtils.NetworkQuality quality) {
                Log.d(TAG, "Network quality changed to: " + quality);
                handleNetworkQualityChange(quality);
            }

            @Override
            public void onPoorNetworkDetected() {
                Log.d(TAG, "Poor network detected");
                showPoorNetworkWarning();
            }

            @Override
            public void onPreloadCountChanged(int count) {
                Log.d(TAG, "Preload count changed to: " + count);
                currentPreloadCount = count;
                updateVideoPreloadStrategy();
            }
        });

        // 初始化弱网络警告视图
        poorNetworkWarningView = new PoorNetworkWarningView(this);

        // 将警告视图添加到Activity的根布局
        ViewGroup rootLayout = findViewById(android.R.id.content);
        if (rootLayout != null) {
            poorNetworkWarningView.attachToParent(rootLayout);
        }

        // 获取初始预加载数量
        currentPreloadCount = networkOptimizationManager.getRecommendedPreloadCount();

        // 开始网络监控
        networkOptimizationManager.startNetworkMonitoring();

        // 检查是否需要显示弱网络警告
        if (networkOptimizationManager.shouldShowPoorNetworkWarning("video_player")) {
            showPoorNetworkWarning();
        }

        Log.d(TAG, "Network optimization initialized with preload count: " + currentPreloadCount);
    }

    /**
     * 处理网络质量变化
     */
    private void handleNetworkQualityChange(NetworkUtils.NetworkQuality quality) {
        runOnUiThread(() -> {
            switch (quality) {
                case EXCELLENT:
                    Log.d(TAG, "Network quality excellent - enabling full features");
                    break;
                case GOOD:
                    Log.d(TAG, "Network quality good - normal operation");
                    break;
                case FAIR:
                    Log.d(TAG, "Network quality fair - reducing preload");
                    break;
                case POOR:
                case NONE:
                    Log.d(TAG, "Network quality poor/none - minimal preload");
                    showPoorNetworkWarning();
                    break;
            }
        });
    }

    /**
     * 显示弱网络警告
     */
    private void showPoorNetworkWarning() {
        if (poorNetworkWarningView != null) {
            runOnUiThread(() -> {
                poorNetworkWarningView.showWarning();
                Log.d(TAG, "Poor network warning displayed");
            });
        }
    }

    /**
     * 更新视频预加载策略
     */
    private void updateVideoPreloadStrategy() {
        if (viewPagerVideos != null) {
            // 根据网络状况调整预加载策略
            Log.d(TAG, "Updating video preload strategy with count: " + currentPreloadCount);

            // VideoPlayerActivity通常只播放单个视频，所以预加载策略相对简单
            if (currentPreloadCount == 0) {
                // 弱网络环境，不预加载
                viewPagerVideos.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);
            } else {
                // 正常网络环境，使用默认设置
                viewPagerVideos.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT);
            }
        }
    }

    /**
     * 设置ViewPager
     */
    private void setupViewPager() {
        pagerAdapter = new VideoPlayerPagerAdapter(this, videoList);
        viewPagerVideos.setAdapter(pagerAdapter);
        viewPagerVideos.setCurrentItem(currentVideoIndex, false);

        // 禁用滑动功能 - VideoPlayerActivity只播放单个视频
        viewPagerVideos.setUserInputEnabled(false);

        // 禁用边界控制，因为不需要滑动
        viewPagerVideos.setBoundaryControlEnabled(false);

        // 设置预加载页面数量为0，避免多个Fragment同时播放
        viewPagerVideos.setOffscreenPageLimit(ViewPager2.OFFSCREEN_PAGE_LIMIT_DEFAULT); // 使用默认值

        // 设置页面变化监听（虽然禁用了滑动，但保留监听以防程序化切换）
        viewPagerVideos.registerOnPageChangeCallback(new ViewPager2.OnPageChangeCallback() {
            @Override
            public void onPageScrollStateChanged(int state) {
                super.onPageScrollStateChanged(state);
                handlePageScrollStateChanged(state);
            }

            @Override
            public void onPageScrolled(int position, float positionOffset, int positionOffsetPixels) {
                super.onPageScrolled(position, positionOffset, positionOffsetPixels);
                handlePageScrolled(position, positionOffset);
            }

            @Override
            public void onPageSelected(int position) {
                super.onPageSelected(position);
                handlePageSelected(position);
            }
        });
    }

    /**
     * 处理页面滚动状态变化
     */
    private void handlePageScrollStateChanged(int state) {
        switch (state) {
            case ViewPager2.SCROLL_STATE_IDLE:
                // 滚动结束，可以开始播放
                Log.d(TAG, "ViewPager scroll idle");
                break;
            case ViewPager2.SCROLL_STATE_DRAGGING:
                // 开始拖拽，暂停当前播放
                Log.d(TAG, "ViewPager scroll dragging");
                break;
            case ViewPager2.SCROLL_STATE_SETTLING:
                // 自动滚动中
                Log.d(TAG, "ViewPager scroll settling");
                break;
        }
    }

    /**
     * 处理页面滚动
     */
    private void handlePageScrolled(int position, float positionOffset) {
        // 这里可以添加滚动过程中的处理逻辑
        // 例如：根据滚动进度调整UI元素的透明度等
    }

    /**
     * 处理页面选中
     */
    private void handlePageSelected(int position) {
        Log.d(TAG, "Page selected: " + position);

        if (position < 0 || position >= videoList.size()) {
            Log.w(TAG, "Invalid position selected: " + position);
            return;
        }

        // 暂停之前的视频
        if (currentVideoIndex != position && pagerAdapter != null) {
            VideoPlayerFragment previousFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (previousFragment != null && previousFragment.getPlayerManager() != null) {
                previousFragment.getPlayerManager().pause();
                Log.d(TAG, "Paused previous video at position " + currentVideoIndex);
            }
        }

        // 更新当前视频信息
        currentVideoIndex = position;
        currentVideo = videoList.get(position);
        pagerAdapter.setCurrentPosition(position);

        // 更新UI显示
        loadVideoData(currentVideo);

        // 开始播放当前视频
        VideoPlayerFragment currentFragment = pagerAdapter.getFragment(position);
        if (currentFragment != null && currentFragment.getPlayerManager() != null &&
            currentFragment.isPlayerPrepared()) {
            currentFragment.getPlayerManager().play();
            Log.d(TAG, "Started current video at position " + position);
        }

        // 预加载相邻视频
        preloadAdjacentVideos(position);
    }



    /**
     * 设置进度条拖拽功能 - 简化版本，避免控制栏冲突
     */
    private void setupProgressSeekBar() {
        progressSeekBar.setOnSeekBarChangeListener(new SeekBar.OnSeekBarChangeListener() {
            @Override
            public void onProgressChanged(SeekBar seekBar, int progress, boolean fromUser) {
                if (fromUser && pagerAdapter != null) {
                    VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
                    if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                        VideoPlayerManager playerManager = currentFragment.getPlayerManager();
                        long duration = playerManager.getDuration();
                        if (duration > 0) {
                            long newPosition = (progress * duration) / 100;
                            playerManager.seekTo(newPosition);
                        }
                    }
                }
            }

            @Override
            public void onStartTrackingTouch(SeekBar seekBar) {
                isUserSeeking = true;
                // 切换到拖拽状态的样式
                seekBar.setThumb(getDrawable(R.drawable.seek_thumb_dragging));
                Log.d(TAG, "User started seeking");
                // 简化：只取消自动隐藏，不强制显示控制栏
                cancelHideControls();
            }

            @Override
            public void onStopTrackingTouch(SeekBar seekBar) {
                isUserSeeking = false;
                // 恢复正常状态的样式
                seekBar.setThumb(getDrawable(R.drawable.seek_thumb_normal));
                Log.d(TAG, "User stopped seeking");
                // 重置全屏自动隐藏定时器
                if (isFullscreen) {
                    resetFullscreenAutoHideTimer();
                }
                // 简化：只在播放时才重新安排隐藏
                if (isCurrentFragmentPlaying()) {
                    scheduleHideControls();
                }
            }
        });

        // 增加进度条的触摸区域，提高拖拽灵敏度
        progressSeekBar.setOnTouchListener((v, event) -> {
            // 扩大触摸区域
            if (event.getAction() == MotionEvent.ACTION_DOWN) {
                // 确保进度条能够接收到触摸事件
                v.getParent().requestDisallowInterceptTouchEvent(true);
            }
            return false; // 让SeekBar正常处理触摸事件
        });
    }

    /**
     * 设置进度条触摸区域
     */
    private void setupProgressTouchArea(View progressTouchArea) {
        if (progressTouchArea == null) return;

        // 获取系统的最小拖动距离
        final int touchSlop = android.view.ViewConfiguration.get(this).getScaledTouchSlop();
        final float[] startX = new float[1];
        final float[] startY = new float[1];
        final boolean[] isDragging = new boolean[1];

        progressTouchArea.setOnTouchListener((v, event) -> {
            // 将触摸事件转发给进度条
            if (progressSeekBar != null) {
                // 计算相对于进度条的触摸位置
                float touchX = event.getX();
                float touchY = event.getY();
                float touchAreaWidth = v.getWidth();
                float progress = touchX / touchAreaWidth;

                switch (event.getAction()) {
                    case android.view.MotionEvent.ACTION_DOWN:
                        startX[0] = touchX;
                        startY[0] = touchY;
                        isDragging[0] = false;
                        return true;

                    case android.view.MotionEvent.ACTION_MOVE:
                        float deltaX = Math.abs(touchX - startX[0]);
                        float deltaY = Math.abs(touchY - startY[0]);

                        // 只有当移动距离超过最小拖动距离时才开始拖动
                        if (!isDragging[0] && (deltaX > touchSlop || deltaY > touchSlop)) {
                            // 检查是否是水平拖动（进度条操作）
                            if (deltaX > deltaY) {
                                isDragging[0] = true;
                                isUserSeeking = true;
                            } else {
                                // 垂直拖动，不处理
                                return false;
                            }
                        }

                        if (isDragging[0] && isUserSeeking) {
                            int moveProgress = (int) (progress * progressSeekBar.getMax());
                            progressSeekBar.setProgress(moveProgress);
                        }
                        return isDragging[0];

                    case android.view.MotionEvent.ACTION_UP:
                    case android.view.MotionEvent.ACTION_CANCEL:
                        if (isDragging[0] && isUserSeeking) {
                            isUserSeeking = false;
                            int finalProgress = (int) (progress * progressSeekBar.getMax());
                            progressSeekBar.setProgress(finalProgress);

                            // 通知当前Fragment进行实际的seek操作
                            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
                            if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                                long seekPosition = (long) (progress * currentFragment.getPlayerManager().getDuration());
                                currentFragment.getPlayerManager().seekTo(seekPosition);
                            }
                        }
                        isDragging[0] = false;
                        return true;
                }
            }
            return false;
        });

        Log.d(TAG, "Progress touch area setup completed");
    }

    /**
     * 预加载相邻视频
     */
    private void preloadAdjacentVideos(int currentPosition) {
        // 预加载下一个视频
        if (currentPosition + 1 < videoList.size()) {
            VideoPlayerFragment nextFragment = pagerAdapter.getFragment(currentPosition + 1);
            if (nextFragment != null && !nextFragment.isPlayerPrepared()) {
                Log.d(TAG, "Preloading next video at position: " + (currentPosition + 1));
                // 这里可以添加预加载逻辑
            }
        }

        // 预加载上一个视频
        if (currentPosition - 1 >= 0) {
            VideoPlayerFragment prevFragment = pagerAdapter.getFragment(currentPosition - 1);
            if (prevFragment != null && !prevFragment.isPlayerPrepared()) {
                Log.d(TAG, "Preloading previous video at position: " + (currentPosition - 1));
                // 这里可以添加预加载逻辑
            }
        }
    }

    /**
     * 设置点击监听器
     */
    private void setupClickListeners() {
        // 返回按钮
        btnBack.setOnClickListener(v -> finish());

        // 小屏播放按钮
        btnSmallScreen.setOnClickListener(v -> {
            startFloatingVideoPlay();
        });

        // 下载按钮
        btnDownload.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
                return;
            }
            handleDownloadClick();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 收藏按钮
        btnLike.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
                return;
            }
            toggleLike();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 分享按钮
        btnShare.setOnClickListener(v -> {
            if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(this)) {
                return;
            }
            shareVideo();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 评论按钮
        btnComment.setOnClickListener(v -> {
            // TODO: 实现评论功能
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 详情按钮
        btnDetail.setOnClickListener(v -> {
            openVideoDetail();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 选集按钮
        btnEpisodeSelection.setOnClickListener(v -> {
            showEpisodeSelection();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 弹幕按钮
        btnDanmaku.setOnClickListener(v -> {
            subtitlePanelView.toggle();
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 倍速按钮
        btnSpeedSelection.setOnClickListener(v -> {
            speedSelectionView.showAbove(btnSpeedSelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 清晰度按钮
        btnQualitySelection.setOnClickListener(v -> {
            qualitySelectionView.showAbove(btnQualitySelection);
            if (isFullscreen) resetFullscreenAutoHideTimer();
        });

        // 全屏按钮
        btnFullscreen.setOnClickListener(v -> toggleFullscreen());

        // 使用简单的点击检测来控制全屏时的UI显示
        final float[] downX = new float[1];
        final float[] downY = new float[1];
        final long[] downTime = new long[1];

        viewPagerVideos.setOnTouchListener((v, event) -> {
            if (!isFullscreen) {
                return false; // 非全屏时不处理
            }

            switch (event.getAction()) {
                case MotionEvent.ACTION_DOWN:
                    downX[0] = event.getX();
                    downY[0] = event.getY();
                    downTime[0] = System.currentTimeMillis();

                    // 检查是否点击在按钮区域，如果是则不拦截
                    if (isTouchOnControlButtons(event)) {
                        return false; // 让按钮处理点击事件
                    }
                    break;

                case MotionEvent.ACTION_UP:
                    float upX = event.getX();
                    float upY = event.getY();
                    long upTime = System.currentTimeMillis();

                    // 再次检查是否点击在按钮区域
                    if (isTouchOnControlButtons(event)) {
                        return false; // 让按钮处理点击事件
                    }

                    // 检查是否是点击（移动距离小且时间短）
                    float deltaX = Math.abs(upX - downX[0]);
                    float deltaY = Math.abs(upY - downY[0]);
                    long deltaTime = upTime - downTime[0];

                    if (deltaX < 50 && deltaY < 50 && deltaTime < 500) {
                        // 合并控制逻辑：同时控制视频播放按钮和页面元素
                        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                        boolean videoControlsVisible = currentFragment != null && currentFragment.areControlsVisible();

                        if (videoControlsVisible || areControlsVisibleInFullscreen) {
                            // 如果任一控制元素可见，则隐藏Fragment的播放控制按钮
                            if (currentFragment != null) {
                                currentFragment.hidePlayControls();
                            }
                            // 全屏模式下不隐藏进度条和底部按钮，只隐藏Fragment的播放控制
                            if (!isFullscreen) {
                                hideFullscreenControls();
                            }
                        } else {
                            // 如果都不可见，则显示所有
                            if (currentFragment != null) {
                                currentFragment.showPlayControls();
                            }
                            showFullscreenControls();
                        }
                        return true;
                    }
                    break;
            }
            return false; // 让ViewPager2正常处理滑动事件
        });

        // 确保进度条点击不会触发控制栏隐藏
        progressSeekBar.setOnClickListener(v -> {
            // 阻止事件传播，避免触发控制栏隐藏
            Log.d(TAG, "Progress bar clicked - preventing control hide");
        });

        // 描述文本点击展开/收起
        tvVideoDescription.setOnClickListener(v -> toggleDescriptionExpansion());

        // 视频标题点击跳转到视频详情页
        tvVideoTitle.setOnClickListener(v -> openVideoDetail());

        // 原本的海报弹窗逻辑暂时注释
        // tvVideoTitle.setOnClickListener(v -> showPosterPopup());

        // 关闭海报弹窗按钮
        btnClosePoster.setOnClickListener(v -> hidePosterPopup());

        // 点击遮罩层关闭弹窗
        posterPopupOverlay.setOnClickListener(v -> {
            // 检查点击是否在海报容器外
            if (v == posterPopupOverlay) {
                hidePosterPopup();
            }
        });

        // VIP解锁弹窗相关监听器
        btnUnlockNow.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showRechargePopup();
        });

        btnWatchAd.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showWatchAdPopup();
        });

        btnOpenVip.setOnClickListener(v -> {
            hideVipUnlockPopup();
            showRechargePopup();
        });

        btnCloseVipPopup.setOnClickListener(v -> hideVipUnlockPopup());

        // 点击VIP弹窗遮罩层关闭弹窗
        vipUnlockPopupOverlay.setOnClickListener(v -> {
            if (v == vipUnlockPopupOverlay) {
                hideVipUnlockPopup();
            }
        });

        // 观看广告弹窗点击事件
        watchAdContainer.setOnClickListener(v -> {
            hideWatchAdPopup();
        });

        // 点击观看广告弹窗遮罩层关闭弹窗
        watchAdPopupOverlay.setOnClickListener(v -> {
            if (v == watchAdPopupOverlay) {
                hideWatchAdPopup();
            }
        });

        // 充值弹窗相关监听器
        btnCloseRechargePopup.setOnClickListener(v -> hideRechargePopup());

        // 点击充值弹窗遮罩层关闭弹窗
        rechargePopupOverlay.setOnClickListener(v -> {
            if (v == rechargePopupOverlay) {
                hideRechargePopup();
            }
        });

        // 充值金额卡片点击事件
        rechargeCard500.setOnClickListener(v -> selectRechargeAmount(500));
        rechargeCard1000.setOnClickListener(v -> selectRechargeAmount(1000));
        rechargeCard2000.setOnClickListener(v -> selectRechargeAmount(2000));
        rechargeCard3000.setOnClickListener(v -> selectRechargeAmount(3000));

        // VIP卡片点击事件
        rechargeWeeklyCard.setOnClickListener(v -> selectVipPlan("weekly"));
        rechargeMonthlyCard.setOnClickListener(v -> selectVipPlan("monthly"));
    }

    /**
     * 设置自动隐藏控制栏
     */
    private void setupAutoHideControls() {
        hideControlsRunnable = () -> {
            if (isControlsVisible && isCurrentFragmentPlaying()) {
                hideControls();
            }
        };

        // 初始化全屏控制元素自动隐藏任务
        hideFullscreenControlsRunnable = () -> {
            if (isFullscreen && areControlsVisibleInFullscreen) {
                hideFullscreenControls();
            }
        };
    }

    /**
     * 加载视频数据 - 修复为不直接操作播放器，只更新UI
     */
    private void loadVideoData(VideoModel video) {
        if (video == null) return;

        // 更新UI
        tvVideoTitle.setText(video.getTitle());

        // 生成视频概要
        String description = generateVideoDescription(video);
        tvVideoDescription.setText(description);

        tvLikeCount.setText(video.getFormattedViewCount());
        tvShareCount.setText("Share");
        tvCommentCount.setText("Comment");

        // 更新收藏状态
        updateLikeButton(video.isLiked());

        // 更新进度条 - 将从当前Fragment获取实际进度
        progressSeekBar.setProgress((int) (video.getWatchProgress() * 100));

        // 更新选集按钮
        updateEpisodeButton(video);

        // 视频源加载和播放位置恢复由Fragment处理
        Log.d(TAG, "Video data loaded for UI: " + video.getTitle());
    }

    /**
     * 重新加载当前视频（用于选集切换）
     */
    private void reloadCurrentVideo() {
        if (pagerAdapter != null && currentVideoIndex >= 0 && currentVideoIndex < videoList.size()) {
            VideoPlayerFragment currentFragment = pagerAdapter.getFragment(currentVideoIndex);
            if (currentFragment != null) {
                Log.d(TAG, "Reloading video for episode change at position: " + currentVideoIndex);

                // 重置Fragment的加载状态，强制重新加载
                currentFragment.resetLoadingState();

                // 重新加载视频数据
                currentFragment.loadVideoData();
            } else {
                Log.w(TAG, "Current fragment is null, cannot reload video");
            }
        }
    }

    /**
     * 生成视频概要
     */
    private String generateVideoDescription(VideoModel video) {
        if (video.getDescription() != null && !video.getDescription().isEmpty()) {
            return video.getDescription();
        }

        // 自动生成概要
        StringBuilder description = new StringBuilder();
        description.append("这是一部精彩的");

        if (video.getCategory() != null) {
            description.append(video.getCategory());
        } else {
            description.append("视频");
        }

        description.append("作品。");

        if (video.getTotalEpisodes() > 1) {
            description.append("共").append(video.getTotalEpisodes()).append("集，");
        }

        description.append("为您带来精彩的观影体验。");

        return description.toString();
    }

    /**
     * 更新集数按钮
     */
    private void updateEpisodeButton(VideoModel video) {
        if (video != null && video.getTotalEpisodes() > 1) {
            String episodeText = "EP." + video.getCurrentEpisode() + "/EP." + video.getTotalEpisodes();

            // 使用SpannableString来设置不同颜色
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到第一个"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色 (EP.1)
                spannableText.setSpan(
                    new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                    0, slashIndex,
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色 (/EP.72)
                spannableText.setSpan(
                    new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                    slashIndex, episodeText.length(),
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        } else {
            // 单集视频或无集数信息时显示EP.1/EP.1
            String episodeText = "EP.1/EP.1";
            android.text.SpannableString spannableText = new android.text.SpannableString(episodeText);

            // 找到"/"的位置
            int slashIndex = episodeText.indexOf("/");
            if (slashIndex != -1) {
                // 当前集数用红色
                spannableText.setSpan(
                    new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_like_red)),
                    0, slashIndex,
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );

                // 总集数用默认颜色
                spannableText.setSpan(
                    new android.text.style.ForegroundColorSpan(getResources().getColor(R.color.video_player_text_tertiary)),
                    slashIndex, episodeText.length(),
                    android.text.Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                );
            }

            tvEpisodeText.setText(spannableText);
        }
    }

    /**
     * 获取视频URL
     */
    private String getVideoUrl(VideoModel video) {
        if (video == null) {
            Log.w(TAG, "Video model is null, using default video");
            Uri uri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.movie);
            return uri.toString();
        }

        // 检查视频模型是否有URI
        if (video.getVideoUri() != null) {
            Log.d(TAG, "Using video URI from VideoModel: " + video.getVideoUri());
            return video.getVideoUri().toString();
        }

        // 使用默认的测试视频作为备用
        Uri uri = Uri.parse("android.resource://" + getPackageName() + "/" + R.raw.movie);
        Log.d(TAG, "Using default video for: " + video.getTitle() + " -> " + uri);
        return uri.toString();
    }

    // VideoPlayerListener 接口实现 - 简化版本，主要处理UI更新

    @Override
    public void onPrepared() {
        // 由Fragment处理
    }

    @Override
    public void onPlaybackStarted() {
        uiHandler.post(() -> {
            // 开始播放，启动自动隐藏控制栏
            scheduleHideControls();
        });
    }

    @Override
    public void onPlaybackPaused() {
        uiHandler.post(() -> {
            // 暂停播放，取消自动隐藏
            cancelHideControls();
        });
    }

    @Override
    public void onPlaybackStopped() {
        uiHandler.post(() -> {
            // 停止播放
            cancelHideControls();
        });
    }

    @Override
    public void onPlaybackCompleted() {
        uiHandler.post(() -> {
            // 播放完成，自动切换到下一个视频
            if (currentVideoIndex < videoList.size() - 1) {
                viewPagerVideos.setCurrentItem(currentVideoIndex + 1, true);
            }
        });
    }

    @Override
    public void onPositionChanged(long position, long duration) {
        // 进度更新现在通过定时器从当前Fragment获取
    }

    @Override
    public void onBufferingUpdate(boolean isBuffering, int bufferedPercentage) {
        // 由Fragment处理
    }

    @Override
    public void onPlaybackSpeedChanged(float speed) {
        uiHandler.post(() -> {
            currentPlaybackSpeed = speed;
            updateSpeedButton();
            speedSelectionView.setSelectedSpeed(speed);
        });
    }

    @Override
    public void onVideoSizeChanged(int width, int height) {
        // 视频尺寸变化
    }

    @Override
    public void onError(String error) {
        uiHandler.post(() -> {
            // 播放错误处理，移除提示信息
        });
    }

    @Override
    public void onPlayerStateChanged(boolean playWhenReady, int playbackState) {
        // 播放器状态变化
    }

    @Override
    public void onVolumeChanged(float volume) {
        // 音量变化
    }

    /**
     * 切换收藏状态
     */
    private void toggleLike() {
        if (currentVideo != null) {
            currentVideo.toggleLike();
            updateLikeButton(currentVideo.isLiked());
        }
    }

    /**
     * 更新收藏按钮
     */
    private void updateLikeButton(boolean isLiked) {
        if (isLiked) {
            btnLike.setImageResource(R.drawable.play_ic_like_sel);
        } else {
            btnLike.setImageResource(R.drawable.play_ic_like);
        }
    }

    /**
     * 分享视频
     */
    private void shareVideo() {
        if (currentVideo != null) {
            Intent shareIntent = new Intent(Intent.ACTION_SEND);
            shareIntent.setType("text/plain");
            shareIntent.putExtra(Intent.EXTRA_TEXT, "分享视频: " + currentVideo.getTitle());
            startActivity(Intent.createChooser(shareIntent, "分享到"));
        }
    }

    /**
     * 打开视频详情页
     */
    private void openVideoDetail() {
        if (currentVideo != null) {
            Intent intent = new Intent(this, VideoDetailActivity.class);
            intent.putExtra("video_model", currentVideo);
            startActivity(intent);
        }
    }

    /**
     * 切换弹幕状态
     */
    private void toggleDanmaku() {
        isDanmakuEnabled = !isDanmakuEnabled;
        preferences.saveDanmakuEnabled(isDanmakuEnabled);

        // 控制当前Fragment的弹幕显示
        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
        if (currentFragment != null) {
            currentFragment.toggleDanmaku(isDanmakuEnabled);
        }
    }

    /**
     * 显示选集选择
     */
    private void showEpisodeSelection() {
        if (currentVideo != null && currentVideo.getEpisodes() != null && !currentVideo.getEpisodes().isEmpty()) {
            episodeSelectionView.setEpisodeList(currentVideo.getEpisodes());
            episodeSelectionView.setSelectedEpisode(currentVideo.getSelectedEpisode());
            episodeSelectionView.toggle();
        }
    }

    /**
     * 切换全屏状态
     */
    private void toggleFullscreen() {
        if (isFullscreen) {
            // 退出全屏
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_PORTRAIT);
            btnFullscreen.setImageResource(R.drawable.play_ic_quanping);
        } else {
            // 进入全屏
            setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);
            btnFullscreen.setImageResource(R.drawable.play_ic_suoxiao);
        }
        isFullscreen = !isFullscreen;
    }

    /**
     * 切换控制栏可见性 - 公共方法供Fragment调用
     * 注意：这里只切换Fragment中的视频控制按钮，不影响Activity的UI元素
     */
    public void toggleControlsVisibility() {
        // 这个方法现在只是为了保持接口兼容性
        // 实际的控制按钮显示/隐藏由Fragment自己处理
        Log.d(TAG, "toggleControlsVisibility called - Fragment handles its own controls");
    }

    /**
     * 显示控制栏 - 修复版本，Activity的UI元素始终显示
     */
    private void showControls() {
        // Activity的UI元素始终保持显示，不需要动态控制
        // 只有Fragment中的视频控制按钮才需要显示/隐藏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.VISIBLE);
        }
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.VISIBLE);
        }
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }
        isControlsVisible = true;

        // 不再自动隐藏Activity的UI元素
        Log.d(TAG, "Activity UI elements are always visible");
    }

    /**
     * 隐藏控制栏 - 修复版本，Activity的UI元素不再隐藏
     */
    private void hideControls() {
        // Activity的UI元素不再隐藏，始终保持可见
        // 只有Fragment中的视频控制按钮才会隐藏
        Log.d(TAG, "Activity UI elements remain visible - only Fragment controls hide");

        cancelHideControls();
    }

    /**
     * 安排自动隐藏控制栏 - Activity的UI元素不再自动隐藏
     */
    private void scheduleHideControls() {
        // Activity的UI元素不再自动隐藏
        // 只有Fragment中的视频控制按钮才会自动隐藏
        Log.d(TAG, "Activity UI elements do not auto-hide");
        cancelHideControls();
    }

    /**
     * 取消自动隐藏控制栏
     */
    private void cancelHideControls() {
        uiHandler.removeCallbacks(hideControlsRunnable);
    }

    /**
     * 更新倍速按钮
     */
    private void updateSpeedButton() {
        String speedText = currentPlaybackSpeed + "x";
        btnSpeedSelection.setText(speedText);
    }

    /**
     * 更新清晰度按钮
     */
    private void updateQualityButton() {
        btnQualitySelection.setText(currentQuality.getDisplayName());
    }



    /**
     * 切换描述文本的展开/收起状态
     */
    private void toggleDescriptionExpansion() {
        if (isDescriptionExpanded) {
            // 收起：显示1行
            tvVideoDescription.setMaxLines(1);
            tvVideoDescription.setEllipsize(android.text.TextUtils.TruncateAt.END);
        } else {
            // 展开：显示全部内容
            tvVideoDescription.setMaxLines(Integer.MAX_VALUE);
            tvVideoDescription.setEllipsize(null);
        }
        isDescriptionExpanded = !isDescriptionExpanded;
    }

    /**
     * 更新弹幕按钮状态
     */
    private void updateDanmakuButtonState() {
        if (btnDanmaku != null) {
            btnDanmaku.setSelected(isDanmakuEnabled);
            // 可以根据需要更改图标或颜色
        }
    }



    @Override
    public void onConfigurationChanged(Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 处理屏幕旋转
        if (newConfig.orientation == Configuration.ORIENTATION_LANDSCAPE) {
            // 横屏模式
            isFullscreen = true;
            btnFullscreen.setImageResource(R.drawable.play_ic_suoxiao);
            hideUIForFullscreen();
            // 启动全屏控制元素的自动隐藏
            showFullscreenControls();
        } else {
            // 竖屏模式
            isFullscreen = false;
            btnFullscreen.setImageResource(R.drawable.play_ic_quanping);
            showUIForNormalMode();
        }
    }

    /**
     * 隐藏UI元素以实现全屏效果
     */
    private void hideUIForFullscreen() {
        // 隐藏顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.GONE);
        }

        // 隐藏侧边控制按钮（收藏、分享、评论）
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.GONE);
        }

        // 隐藏视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.GONE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.GONE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.GONE);
        }
    }

    /**
     * 显示UI元素恢复正常模式
     */
    private void showUIForNormalMode() {
        // 显示顶部控制栏
        if (topControlBar != null) {
            topControlBar.setVisibility(View.VISIBLE);
        }

        // 显示侧边控制按钮
        if (sideControlButtons != null) {
            sideControlButtons.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示视频标题、概要和详情按钮
        if (tvVideoTitle != null) {
            tvVideoTitle.setVisibility(View.VISIBLE);
        }
        if (tvVideoDescription != null) {
            tvVideoDescription.setVisibility(View.VISIBLE);
        }
        if (btnDetail != null) {
            btnDetail.setVisibility(View.VISIBLE);
        }

        // 停止全屏控制元素的自动隐藏
        if (uiHandler != null) {
            uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
        }
        areControlsVisibleInFullscreen = true;
    }

    /**
     * 显示全屏控制元素 - 修改为始终显示，不自动隐藏
     */
    private void showFullscreenControls() {
        if (!isFullscreen) return;

        areControlsVisibleInFullscreen = true;

        // 显示全屏按钮
        if (btnFullscreen != null) {
            btnFullscreen.setVisibility(View.VISIBLE);
        }

        // 显示进度条
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 显示底部信息区域
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        // 取消自动隐藏任务，全屏时进度条和底部按钮始终显示
        if (uiHandler != null) {
            uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
        }

        Log.d(TAG, "Fullscreen controls shown - will remain visible");
    }

    /**
     * 隐藏全屏控制元素 - 修改为始终显示进度条和底部按钮
     */
    private void hideFullscreenControls() {
        if (!isFullscreen) return;

        areControlsVisibleInFullscreen = false;

        // 全屏时进度条和底部按钮始终显示，不隐藏
        // 保持进度条可见
        if (progressSeekBar != null) {
            progressSeekBar.setVisibility(View.VISIBLE);
        }

        // 保持底部信息区域可见
        if (bottomInfoArea != null) {
            bottomInfoArea.setVisibility(View.VISIBLE);
        }

        Log.d(TAG, "Fullscreen mode - progress bar and bottom controls remain visible");
    }

    @Override
    protected void onApplyNavigationBarInsets(WindowInsetsCompat insets, int navigationBarHeight,
                                            boolean hasNavigationBar, boolean isGestureNavigation) {
        super.onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

        // 调整底部控制栏的margin，避免被导航栏遮挡
        if (bottomInfoArea != null && hasNavigationBar) {
            ViewGroup.MarginLayoutParams params = (ViewGroup.MarginLayoutParams) bottomInfoArea.getLayoutParams();
            params.bottomMargin = navigationBarHeight + getResources().getDimensionPixelSize(R.dimen.video_player_info_margin_top);
            bottomInfoArea.setLayoutParams(params);
        }
    }

    @Override
    protected void onResume() {
        super.onResume();
        // Fragment会自动处理播放恢复
        startProgressUpdate();

        // 启动网络监控
        if (networkOptimizationManager != null) {
            networkOptimizationManager.startNetworkMonitoring();
            // 重置弱网络警告状态，允许重新显示
            networkOptimizationManager.resetPoorNetworkWarningState("video_player");
            // 检查当前网络状况
            if (networkOptimizationManager.shouldShowPoorNetworkWarning("video_player")) {
                showPoorNetworkWarning();
            }
        }

        Log.d(TAG, "Activity resumed - Fragment will handle playback");
    }

    @Override
    protected void onPause() {
        super.onPause();
        // 暂停所有视频播放
        pauseAllVideos();
        stopProgressUpdate();

        // 停止网络监控
        if (networkOptimizationManager != null) {
            networkOptimizationManager.stopNetworkMonitoring();
        }

        // 隐藏弱网络警告
        if (poorNetworkWarningView != null) {
            poorNetworkWarningView.hideImmediately();
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();

        // 清理资源
        cancelHideControls();

        // 不再需要释放Activity级别的播放器

        if (pagerAdapter != null) {
            pagerAdapter.cleanup();
            pagerAdapter = null;
        }

        // 清理网络优化资源
        if (networkOptimizationManager != null) {
            networkOptimizationManager.stopNetworkMonitoring();
            networkOptimizationManager.setNetworkOptimizationListener(null);
        }

        if (poorNetworkWarningView != null) {
            poorNetworkWarningView.cleanup();
            poorNetworkWarningView.detachFromParent();
            poorNetworkWarningView = null;
        }

        // 清理高级组件
        if (qualitySelectionView != null) {
            qualitySelectionView = null;
        }
        if (speedSelectionView != null) {
            speedSelectionView = null;
        }
        if (episodeSelectionView != null) {
            episodeSelectionView = null;
        }

        // 清理悬浮窗权限回调
        FloatingVideoPermissionUtils.clearPermissionCallback();

        // 清理录屏监控器
        if (recordingMonitor != null) {
            recordingMonitor.stopMonitoring();
            recordingMonitor = null;
            Log.d(TAG, "Screen recording monitor stopped");
        }
    }

    /**
     * 暂停所有视频播放
     * 使用全局播放器管理器确保所有视频都被暂停
     */
    private void pauseAllVideos() {
        GlobalVideoPlayerManager.getInstance().stopAllFragments();
        Log.d(TAG, "pauseAllVideos called - stopped all fragments via GlobalVideoPlayerManager");
    }

    /**
     * 获取当前显示的VideoPlayerFragment
     */
    private VideoPlayerFragment getCurrentVideoFragment() {
        if (pagerAdapter != null && viewPagerVideos != null) {
            int currentPosition = viewPagerVideos.getCurrentItem();
            return pagerAdapter.getFragment(currentPosition);
        }
        return null;
    }

    /**
     * 通知当前Fragment更改清晰度
     */
    private void notifyCurrentFragmentQualityChange(VideoQualitySelector.VideoQuality quality) {
        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
        if (currentFragment != null && currentFragment.getPlayerManager() != null) {
            currentFragment.getPlayerManager().setQuality(quality);
            currentQuality = quality;
            updateQualityButton();
            preferences.saveVideoQuality(quality);
        }
    }

    /**
     * 通知当前Fragment更改播放速度
     */
    private void notifyCurrentFragmentSpeedChange(float speed) {
        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
        if (currentFragment != null && currentFragment.getPlayerManager() != null) {
            currentFragment.getPlayerManager().setPlaybackSpeed(speed);
            currentPlaybackSpeed = speed;
            updateSpeedButton();
        }
    }

    /**
     * 初始化进度更新任务
     */
    private void initializeProgressUpdate() {
        progressUpdateRunnable = new Runnable() {
            @Override
            public void run() {
                if (shouldUpdateProgress && !isUserSeeking) {
                    VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                    if (currentFragment != null && currentFragment.getPlayerManager() != null) {
                        VideoPlayerManager playerManager = currentFragment.getPlayerManager();
                        long position = playerManager.getCurrentPosition();
                        long duration = playerManager.getDuration();
                        if (duration > 0) {
                            int progress = (int) ((position * 100) / duration);
                            progressSeekBar.setProgress(progress);
                        }
                    }
                }
                if (shouldUpdateProgress) {
                    uiHandler.postDelayed(this, 1000); // 每秒更新一次
                }
            }
        };
    }

    /**
     * 启动进度更新
     */
    private void startProgressUpdate() {
        shouldUpdateProgress = true;
        uiHandler.post(progressUpdateRunnable);
    }

    /**
     * 停止进度更新
     */
    private void stopProgressUpdate() {
        shouldUpdateProgress = false;
        uiHandler.removeCallbacks(progressUpdateRunnable);
    }

    /**
     * 检查当前Fragment是否正在播放
     */
    private boolean isCurrentFragmentPlaying() {
        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
        return currentFragment != null &&
               currentFragment.getPlayerManager() != null &&
               currentFragment.getPlayerManager().isPlaying();
    }

    /**
     * 静态方法：启动VideoPlayerActivity
     */
    public static void start(android.content.Context context, VideoModel video) {
        if (context == null || video == null) {
            Log.e("VideoPlayerActivity", "Context or video is null, cannot start activity");
            return;
        }

        try {
            Intent intent = new Intent(context, VideoPlayerActivity.class);
            intent.putExtra(EXTRA_VIDEO_MODEL, video);
            context.startActivity(intent);

            // 添加页面切换动画
            if (context instanceof Activity) {
                ((Activity) context).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
            }

            Log.d("VideoPlayerActivity", "Started with single video: " + video.getTitle());
        } catch (Exception e) {
            Log.e("VideoPlayerActivity", "Error starting activity with single video", e);
        }
    }

    public static void start(android.content.Context context, List<VideoModel> videoList, int currentIndex) {
        if (context == null || videoList == null || videoList.isEmpty()) {
            Log.e("VideoPlayerActivity", "Context or video list is null/empty, cannot start activity");
            return;
        }

        if (currentIndex < 0 || currentIndex >= videoList.size()) {
            Log.w("VideoPlayerActivity", "Invalid current index: " + currentIndex + ", using 0");
            currentIndex = 0;
        }

        try {
            Intent intent = new Intent(context, VideoPlayerActivity.class);
            intent.putExtra(EXTRA_VIDEO_LIST, (java.io.Serializable) videoList);
            intent.putExtra(EXTRA_CURRENT_INDEX, currentIndex);
            context.startActivity(intent);

            // 添加页面切换动画
            if (context instanceof Activity) {
                ((Activity) context).overridePendingTransition(R.anim.slide_in_right, R.anim.slide_out_left);
            }

            Log.d("VideoPlayerActivity", "Started with video list: " + videoList.size() + " videos, current index: " + currentIndex);
        } catch (Exception e) {
            Log.e("VideoPlayerActivity", "Error starting activity with video list", e);
        }
    }




    @Override
    public void onBackPressed() {
        // 如果是全屏状态，先退出全屏
        if (isFullscreen) {
            toggleFullscreen();
            return;
        }

        // 返回前停止所有播放
        pauseAllVideos();
        GlobalVideoPlayerManager.getInstance().forceStopAllPlayback();

        super.onBackPressed();
        // 添加返回动画
        overridePendingTransition(R.anim.slide_in_left, R.anim.slide_out_right);
    }

    /**
     * 重置全屏自动隐藏定时器
     */
    private void resetFullscreenAutoHideTimer() {
        if (uiHandler != null && isFullscreen && areControlsVisibleInFullscreen) {
            uiHandler.removeCallbacks(hideFullscreenControlsRunnable);
            // 4秒后自动隐藏
            uiHandler.postDelayed(hideFullscreenControlsRunnable, 4000);
            Log.d(TAG, "Fullscreen auto-hide timer reset - 4 seconds");
        }
    }

    /**
     * 检查触摸点是否在控制按钮区域内
     */
    private boolean isTouchOnControlButtons(MotionEvent event) {
        if (!areControlsVisibleInFullscreen) {
            return false; // 控制元素不可见时，不需要检查
        }

        float x = event.getRawX(); // 使用屏幕坐标
        float y = event.getRawY();

        // 检查所有可见的控制按钮
        View[] buttons = {
            btnBack, btnSmallScreen, btnDownload, btnLike, btnShare, btnComment,
            btnDetail, btnEpisodeSelection, btnDanmaku, btnSpeedSelection,
            btnQualitySelection, btnFullscreen
        };

        for (View button : buttons) {
            if (button != null && button.getVisibility() == View.VISIBLE) {
                int[] location = new int[2];
                button.getLocationOnScreen(location);
                int left = location[0];
                int top = location[1];
                int right = left + button.getWidth();
                int bottom = top + button.getHeight();

                // 扩大触摸区域，增加容错性
                int padding = 20;
                if (x >= left - padding && x <= right + padding &&
                    y >= top - padding && y <= bottom + padding) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 显示海报弹窗
     */
    private void showPosterPopup() {
        if (posterPopupOverlay != null) {
            posterPopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            posterPopupOverlay.setAlpha(0f);
            posterPopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // 海报容器缩放动画
            if (posterContainer != null) {
                posterContainer.setScaleX(0.8f);
                posterContainer.setScaleY(0.8f);
                posterContainer.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .start();
            }

            Log.d(TAG, "Poster popup shown");
        }
    }

    /**
     * 隐藏海报弹窗
     */
    private void hidePosterPopup() {
        if (posterPopupOverlay != null && posterPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            posterPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (posterPopupOverlay != null) {
                        posterPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Poster popup hidden");
        }
    }

    /**
     * 显示VIP解锁弹窗
     */
    public void showVipUnlockPopup() {
        if (vipUnlockPopupOverlay != null) {
            vipUnlockPopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            vipUnlockPopupOverlay.setAlpha(0f);
            vipUnlockPopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // VIP弹窗容器缩放动画
            if (vipUnlockContainer != null) {
                vipUnlockContainer.setScaleX(0.8f);
                vipUnlockContainer.setScaleY(0.8f);
                vipUnlockContainer.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .start();
            }

            Log.d(TAG, "VIP unlock popup shown");
        }
    }

    /**
     * 隐藏VIP解锁弹窗
     */
    private void hideVipUnlockPopup() {
        if (vipUnlockPopupOverlay != null && vipUnlockPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            vipUnlockPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (vipUnlockPopupOverlay != null) {
                        vipUnlockPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "VIP unlock popup hidden");
        }
    }

    /**
     * 显示观看广告弹窗
     */
    private void showWatchAdPopup() {
        if (watchAdPopupOverlay != null) {
            watchAdPopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            watchAdPopupOverlay.setAlpha(0f);
            watchAdPopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // 观看广告容器缩放动画
            if (watchAdContainer != null) {
                watchAdContainer.setScaleX(0.8f);
                watchAdContainer.setScaleY(0.8f);
                watchAdContainer.animate()
                    .scaleX(1f)
                    .scaleY(1f)
                    .setDuration(300)
                    .start();
            }

            Log.d(TAG, "Watch AD popup shown");
        }
    }

    /**
     * 隐藏观看广告弹窗
     */
    private void hideWatchAdPopup() {
        if (watchAdPopupOverlay != null && watchAdPopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            watchAdPopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (watchAdPopupOverlay != null) {
                        watchAdPopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Watch AD popup hidden");
        }
    }

    /**
     * 更新当前积分显示
     */
    private void updateCurrentCoinsDisplay() {
        currentUser = UserSessionUtils.getCurrentUser(this);
        if (currentUser != null && currentUser.isLoggedIn()) {
            tvRechargeCurrentCoins.setText(String.valueOf(currentUser.getPoints()));
        } else {
            tvRechargeCurrentCoins.setText("0");
        }
        Log.d(TAG, "Current coins updated: " + tvRechargeCurrentCoins.getText());
    }

    /**
     * 加载充值弹窗所需的所有数据
     */
    private void loadRechargeData() {
        loadingTasksCount = 0; // 重置计数器
        showRechargeLoading();
        updateCurrentCoinsDisplay();
        loadPointsPackages();
        loadVipConfigs();
        Log.d(TAG, "Recharge data loading initiated");
    }

    /**
     * 显示充值弹窗Loading状态
     */
    private void showRechargeLoading() {
        if (swipeRefreshRechargeLoading != null) {
            swipeRefreshRechargeLoading.setVisibility(View.VISIBLE);
            swipeRefreshRechargeLoading.setRefreshing(true);
        }
        // 隐藏所有卡片
        hideAllRechargeCards();
        Log.d(TAG, "Recharge loading shown");
    }

    /**
     * 隐藏充值弹窗Loading状态
     */
    private void hideRechargeLoading() {
        if (swipeRefreshRechargeLoading != null) {
            swipeRefreshRechargeLoading.setVisibility(View.GONE);
            swipeRefreshRechargeLoading.setRefreshing(false);
        }
        Log.d(TAG, "Recharge loading hidden");
    }

    /**
     * 隐藏所有充值卡片
     */
    private void hideAllRechargeCards() {
        // 隐藏积分卡片
        LinearLayout[] cards = {rechargeCard500, rechargeCard1000, rechargeCard2000, rechargeCard3000};
        for (LinearLayout card : cards) {
            if (card != null) {
                card.setVisibility(View.GONE);
            }
        }

        // 隐藏整个VIP卡片容器（包括动态创建的卡片）
        if (rechargeVipCardsContainer != null) {
            rechargeVipCardsContainer.setVisibility(View.GONE);
        }
    }

    /**
     * 检查所有数据加载是否完成
     */
    private void checkLoadingComplete() {
        loadingTasksCount++;
        if (loadingTasksCount >= TOTAL_LOADING_TASKS) {
            hideRechargeLoading();
            loadingTasksCount = 0; // 重置计数器
            Log.d(TAG, "All recharge data loading completed");
        }
    }

    /**
     * 加载积分套餐数据
     */
    private void loadPointsPackages() {
        if (pointsApiService == null) {
            Log.e(TAG, "PointsApiService not initialized");
            return;
        }

        pointsApiService.getPointsPackageList(new PointsApiService.PointsPackageListCallback() {
            @Override
            public void onSuccess(List<PointsPackage> packages) {
                pointsPackages = packages;
                runOnUiThread(() -> {
                    updatePointsCards();
                    checkLoadingComplete();
                });
                Log.d(TAG, "积分套餐加载成功，共 " + packages.size() + " 个套餐");
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "积分套餐加载失败: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(VideoPlayerActivity.this, "积分套餐加载失败", Toast.LENGTH_SHORT).show();
                    checkLoadingComplete();
                });
            }
        });
    }

    /**
     * 更新积分卡片内容
     */
    private void updatePointsCards() {
        LinearLayout[] cards = {rechargeCard500, rechargeCard1000, rechargeCard2000, rechargeCard3000};

        if (pointsPackages == null || pointsPackages.isEmpty()) {
            Log.d(TAG, "No points packages from API, hiding all cards");
            // 隐藏所有积分卡片
            for (LinearLayout card : cards) {
                if (card != null) {
                    card.setVisibility(View.GONE);
                }
            }
            return;
        }

        // 根据API返回的套餐数量动态显示卡片
        int packagesCount = pointsPackages.size();
        int maxDisplayCards = Math.min(packagesCount, cards.length);

        Log.d(TAG, "API返回 " + packagesCount + " 个积分套餐，将显示前 " + maxDisplayCards + " 个");

        for (int i = 0; i < cards.length; i++) {
            final LinearLayout card = cards[i];
            if (card == null) continue;

            if (i < maxDisplayCards) {
                final PointsPackage pointsPackage = pointsPackages.get(i);
                card.setVisibility(View.VISIBLE);
                updateRechargeCardContent(card, pointsPackage);

                // 更新点击事件：选中卡片并弹出支付弹窗
                card.setOnClickListener(v -> {
                    selectRechargeCard(card);
                    selectedPointsPackage = pointsPackage;
                    // 弹出支付类型选择弹窗
                    showPointsPurchaseDialog(pointsPackage.getPointsDescription(), pointsPackage.getFormattedPrice());
                });

                Log.d(TAG, "积分卡片 " + (i+1) + ": " + pointsPackage.getPoints() + " 积分, " + pointsPackage.getFormattedPrice());
            } else {
                // 隐藏多余的卡片
                card.setVisibility(View.GONE);
            }
        }

        if (packagesCount > cards.length) {
            Log.w(TAG, "API返回了 " + packagesCount + " 个套餐，但只能显示 " + cards.length + " 个卡片");
        }

        Log.d(TAG, "积分卡片内容更新完成，共显示 " + maxDisplayCards + " 个卡片");
    }

    /**
     * 选中积分卡片
     */
    private void selectRechargeCard(LinearLayout selectedCard) {
        // 重置所有积分卡片的选中状态
        LinearLayout[] cards = {rechargeCard500, rechargeCard1000, rechargeCard2000, rechargeCard3000};
        for (LinearLayout card : cards) {
            if (card != null) {
                card.setSelected(false);
            }
        }

        // 设置选中状态
        if (selectedCard != null) {
            selectedCard.setSelected(true);
            selectedRechargeCard = selectedCard;
        }
    }

    /**
     * 更新单个积分卡片的内容
     */
    private void updateRechargeCardContent(LinearLayout card, PointsPackage pointsPackage) {
        try {
            // 根据VideoPlayerActivity的布局结构访问子元素
            // 第0个子元素是包含图标和积分信息的LinearLayout
            LinearLayout pointsContainer = (LinearLayout) card.getChildAt(0);

            // 更新主要积分数量（图标是第0个，积分数量是第1个）
            TextView mainPointsText = (TextView) pointsContainer.getChildAt(1);
            mainPointsText.setText(String.valueOf(pointsPackage.getPoints()));

            // 更新奖励积分（如果有）
            if (pointsPackage.hasGiftPoints() && pointsContainer.getChildCount() > 2) {
                TextView bonusText = (TextView) pointsContainer.getChildAt(2);
                bonusText.setText("+" + pointsPackage.getGiftPoints());
                bonusText.setVisibility(View.VISIBLE);
            } else if (pointsContainer.getChildCount() > 2) {
                // 如果没有奖励积分，隐藏奖励文本
                TextView bonusText = (TextView) pointsContainer.getChildAt(2);
                bonusText.setVisibility(View.GONE);
            }

            // 更新价格（第1个子元素是价格TextView）
            if (card.getChildCount() > 1) {
                TextView priceText = (TextView) card.getChildAt(1);
                priceText.setText(pointsPackage.getFormattedPrice());
            }

        } catch (Exception e) {
            Log.e(TAG, "更新积分卡片内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载VIP配置数据
     */
    private void loadVipConfigs() {
        if (vipConfigApiService == null) {
            Log.e(TAG, "VipConfigApiService not initialized");
            return;
        }

        vipConfigApiService.getVipConfigList(new VipConfigApiService.VipConfigListCallback() {
            @Override
            public void onSuccess(VipConfigListResponseModel response) {
                if (response.hasValidData()) {
                    vipConfigs = response.getValidConfigs();
                    runOnUiThread(() -> {
                        updateVipCards();
                        checkLoadingComplete();
                    });
                    Log.d(TAG, "VIP配置加载成功，共 " + vipConfigs.size() + " 个配置");
                } else {
                    Log.w(TAG, "No valid VIP configs found");
                    runOnUiThread(() -> checkLoadingComplete());
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "VIP配置加载失败: " + errorMessage);
                runOnUiThread(() -> {
                    Toast.makeText(VideoPlayerActivity.this, "VIP套餐加载失败", Toast.LENGTH_SHORT).show();
                    checkLoadingComplete();
                });
            }
        });
    }

    /**
     * 更新VIP卡片内容
     */
    private void updateVipCards() {
        if (rechargeVipCardsContainer == null) {
            Log.e(TAG, "VIP cards container not found");
            return;
        }

        if (vipConfigs == null || vipConfigs.isEmpty()) {
            Log.d(TAG, "No VIP configs from API, hiding all VIP cards");
            // 隐藏整个VIP卡片容器
            rechargeVipCardsContainer.setVisibility(View.GONE);
            return;
        }

        // 显示VIP卡片容器
        rechargeVipCardsContainer.setVisibility(View.VISIBLE);

        // 清除容器中的所有子视图（除了原有的两个卡片，我们重新使用它们）
        // 先隐藏原有的卡片
        if (rechargeWeeklyCard != null) rechargeWeeklyCard.setVisibility(View.GONE);
        if (rechargeMonthlyCard != null) rechargeMonthlyCard.setVisibility(View.GONE);

        int configsCount = vipConfigs.size();
        Log.d(TAG, "API返回 " + configsCount + " 个VIP配置，将显示全部");

        // 使用现有的卡片（最多2个）
        LinearLayout[] existingCards = {rechargeWeeklyCard, rechargeMonthlyCard};

        for (int i = 0; i < configsCount; i++) {
            final VipConfigModel config = vipConfigs.get(i);
            LinearLayout card;

            if (i < existingCards.length && existingCards[i] != null) {
                // 使用现有的卡片
                card = existingCards[i];
                card.setVisibility(View.VISIBLE);
            } else {
                // 动态创建新的VIP卡片（如果需要超过2个）
                card = createDynamicVipCard();
                if (card != null) {
                    rechargeVipCardsContainer.addView(card);
                } else {
                    Log.e(TAG, "Failed to create dynamic VIP card for index " + i);
                    continue;
                }
            }

            updateVipCardContent(card, config);

            // 更新点击事件：直接弹出支付弹窗（VIP不需要选中样式）
            card.setOnClickListener(v -> {
                selectedVipConfig = config;
                // 直接弹出支付类型选择弹窗
                showVipPurchaseDialog(config.getVipName(), config.getFormattedPrice(), config);
            });

            Log.d(TAG, "VIP卡片 " + (i+1) + ": " + config.getVipName() + ", " + config.getFormattedPrice());
        }

        Log.d(TAG, "VIP卡片内容更新完成，共显示 " + configsCount + " 个卡片");
    }

    /**
     * 动态创建VIP卡片
     */
    private LinearLayout createDynamicVipCard() {
        try {
            // 创建主容器
            LinearLayout card = new LinearLayout(this);
            LinearLayout.LayoutParams cardParams = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_card_width),
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_card_height)
            );
            cardParams.gravity = android.view.Gravity.CENTER_HORIZONTAL;
            cardParams.topMargin = getResources().getDimensionPixelSize(R.dimen.recharge_vip_cards_margin_top);
            card.setLayoutParams(cardParams);
            card.setOrientation(LinearLayout.HORIZONTAL);
            card.setBackground(getResources().getDrawable(R.drawable.recharge_vip_card_bg));
            card.setClickable(true);
            card.setFocusable(true);

            // 创建内容容器
            LinearLayout contentContainer = new LinearLayout(this);
            LinearLayout.LayoutParams contentParams = new LinearLayout.LayoutParams(0, LinearLayout.LayoutParams.MATCH_PARENT, 1);
            contentContainer.setLayoutParams(contentParams);
            contentContainer.setOrientation(LinearLayout.VERTICAL);
            contentContainer.setPadding(
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_title_margin_start),
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_title_margin_top),
                0, 0
            );

            // 创建标题TextView
            TextView titleText = new TextView(this);
            titleText.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ));
            titleText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.recharge_vip_title_text_size));
            titleText.setTextColor(getResources().getColor(R.color.recharge_text_primary));
            titleText.setTypeface(android.graphics.Typeface.DEFAULT_BOLD);

            // 创建副标题TextView
            TextView subtitleText = new TextView(this);
            LinearLayout.LayoutParams subtitleParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            subtitleParams.topMargin = getResources().getDimensionPixelSize(R.dimen.recharge_vip_subtitle_margin_top);
            subtitleText.setLayoutParams(subtitleParams);
            subtitleText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.recharge_vip_subtitle_text_size));
            subtitleText.setTextColor(getResources().getColor(R.color.recharge_vip_text_secondary));

            // 创建奖励TextView
            TextView bonusText = new TextView(this);
            LinearLayout.LayoutParams bonusParams = new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            );
            bonusParams.topMargin = getResources().getDimensionPixelSize(R.dimen.recharge_vip_bonus_margin_top);
            bonusText.setLayoutParams(bonusParams);
            bonusText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.recharge_vip_bonus_text_size));
            bonusText.setTextColor(getResources().getColor(R.color.recharge_vip_text_tertiary));

            // 添加到内容容器
            contentContainer.addView(titleText);
            contentContainer.addView(subtitleText);
            contentContainer.addView(bonusText);

            // 创建价格容器
            LinearLayout priceContainer = new LinearLayout(this);
            LinearLayout.LayoutParams priceContainerParams = new LinearLayout.LayoutParams(
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_price_container_width),
                getResources().getDimensionPixelSize(R.dimen.recharge_vip_price_container_height)
            );
            priceContainerParams.topMargin = getResources().getDimensionPixelSize(R.dimen.recharge_vip_price_container_margin_top);
            priceContainerParams.rightMargin = getResources().getDimensionPixelSize(R.dimen.recharge_vip_price_container_margin_end);
            priceContainer.setLayoutParams(priceContainerParams);
            priceContainer.setBackground(getResources().getDrawable(R.drawable.recharge_vip_price_container_bg));
            priceContainer.setOrientation(LinearLayout.HORIZONTAL);
            priceContainer.setGravity(android.view.Gravity.CENTER);

            // 创建价格TextView
            TextView priceText = new TextView(this);
            priceText.setLayoutParams(new LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.WRAP_CONTENT,
                LinearLayout.LayoutParams.WRAP_CONTENT
            ));
            priceText.setTextSize(android.util.TypedValue.COMPLEX_UNIT_PX, getResources().getDimension(R.dimen.recharge_vip_price_text_size));
            priceText.setTextColor(getResources().getColor(R.color.recharge_text_primary));
            priceText.setTypeface(android.graphics.Typeface.DEFAULT_BOLD);

            priceContainer.addView(priceText);

            // 组装卡片
            card.addView(contentContainer);
            card.addView(priceContainer);

            return card;
        } catch (Exception e) {
            Log.e(TAG, "创建动态VIP卡片失败: " + e.getMessage(), e);
            return null;
        }
    }

    /**
     * 显示积分购买支付弹窗
     */
    private void showPointsPurchaseDialog(String pointsDescription, String price) {
        PaymentMethodDialog paymentDialog = new PaymentMethodDialog(this, new PaymentMethodDialog.OnPaymentMethodSelectedListener() {
            @Override
            public void onPaymentMethodSelected(String paymentMethod) {
                handlePointsPurchasePayment(paymentMethod, selectedPointsPackage);
            }
        });
        paymentDialog.show();
    }

    /**
     * 显示VIP购买支付弹窗
     */
    private void showVipPurchaseDialog(String vipName, String price, VipConfigModel config) {
        PaymentMethodDialog paymentDialog = new PaymentMethodDialog(this, new PaymentMethodDialog.OnPaymentMethodSelectedListener() {
            @Override
            public void onPaymentMethodSelected(String paymentMethod) {
                handleVipPurchasePayment(paymentMethod, config);
            }
        });
        paymentDialog.show();
    }

    /**
     * 处理积分购买支付
     */
    private void handlePointsPurchasePayment(String paymentMethod, PointsPackage pointsPackage) {
        if (pointsPackage == null) {
            Toast.makeText(this, "请选择积分套餐", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "积分购买支付: " + paymentMethod + ", 套餐: " + pointsPackage.getPoints() + " 积分");
        Toast.makeText(this, "正在处理积分购买: " + pointsPackage.getPoints() + " 积分", Toast.LENGTH_SHORT).show();

        // TODO: 实现实际的支付逻辑
        // PaymentManager.processPointsPurchase(paymentMethod, pointsPackage, callback);
    }

    /**
     * 处理VIP购买支付
     */
    private void handleVipPurchasePayment(String paymentMethod, VipConfigModel config) {
        if (config == null) {
            Toast.makeText(this, "请选择VIP套餐", Toast.LENGTH_SHORT).show();
            return;
        }

        Log.d(TAG, "VIP购买支付: " + paymentMethod + ", 套餐: " + config.getVipName());
        Toast.makeText(this, "正在处理VIP购买: " + config.getVipName(), Toast.LENGTH_SHORT).show();

        // TODO: 实现实际的支付逻辑
        // PaymentManager.processVipPurchase(paymentMethod, config, callback);
    }

    /**
     * 更新单个VIP卡片的内容
     */
    private void updateVipCardContent(LinearLayout card, VipConfigModel config) {
        try {
            // 根据VideoPlayerActivity的VIP卡片布局结构访问子元素
            // 第0个子元素是内容容器LinearLayout
            LinearLayout contentContainer = (LinearLayout) card.getChildAt(0);

            // 更新VIP名称（第0个TextView）
            TextView titleText = (TextView) contentContainer.getChildAt(0);
            titleText.setText(config.getVipName());

            // 更新时长描述（第1个TextView）
            TextView subtitleText = (TextView) contentContainer.getChildAt(1);
            subtitleText.setText(config.getDurationDescription());

            // 更新奖励积分（第2个TextView）
            TextView bonusText = (TextView) contentContainer.getChildAt(2);
            bonusText.setText("Unlock + " + config.getGiftPoints() + " bonus points");

            // 更新价格（第1个子元素是价格容器LinearLayout）
            if (card.getChildCount() > 1) {
                LinearLayout priceContainer = (LinearLayout) card.getChildAt(1);
                if (priceContainer.getChildCount() > 0) {
                    TextView priceText = (TextView) priceContainer.getChildAt(0);
                    priceText.setText(config.getFormattedPrice());
                }
            }

        } catch (Exception e) {
            Log.e(TAG, "更新VIP卡片内容失败: " + e.getMessage(), e);
        }
    }

    /**
     * 显示充值弹窗
     */
    private void showRechargePopup() {
        if (rechargePopupOverlay != null) {
            rechargePopupOverlay.setVisibility(View.VISIBLE);
            // 添加淡入动画
            rechargePopupOverlay.setAlpha(0f);
            rechargePopupOverlay.animate()
                .alpha(1f)
                .setDuration(300)
                .start();

            // 充值弹窗容器从底部滑入动画
            if (rechargeContainer != null) {
                rechargeContainer.setTranslationY(rechargeContainer.getHeight());
                rechargeContainer.animate()
                    .translationY(0f)
                    .setDuration(300)
                    .start();
            }

            // 重置选择状态
            resetRechargeSelections();

            // 加载充值弹窗所需的所有数据
            loadRechargeData();

            Log.d(TAG, "Recharge popup shown");
        }
    }

    /**
     * 隐藏充值弹窗
     */
    private void hideRechargePopup() {
        if (rechargePopupOverlay != null && rechargePopupOverlay.getVisibility() == View.VISIBLE) {
            // 添加淡出动画
            rechargePopupOverlay.animate()
                .alpha(0f)
                .setDuration(300)
                .withEndAction(() -> {
                    if (rechargePopupOverlay != null) {
                        rechargePopupOverlay.setVisibility(View.GONE);
                    }
                })
                .start();

            Log.d(TAG, "Recharge popup hidden");
        }
    }

    /**
     * 选择充值金额
     */
    private void selectRechargeAmount(int amount) {
        // 重置所有充值卡片的选择状态
        resetRechargeAmountSelections();

        selectedRechargeAmount = amount;

        // 设置选中状态
        LinearLayout selectedCard = null;
        switch (amount) {
            case 500:
                selectedCard = rechargeCard500;
                break;
            case 1000:
                selectedCard = rechargeCard1000;
                break;
            case 2000:
                selectedCard = rechargeCard2000;
                break;
            case 3000:
                selectedCard = rechargeCard3000;
                break;
        }

        if (selectedCard != null) {
            selectedCard.setSelected(true);
        }


        Log.d(TAG, "Recharge amount selected: " + amount);
    }

    /**
     * 选择VIP计划
     */
    private void selectVipPlan(String plan) {
        // 重置VIP卡片选择状态
        resetVipSelections();

        if ("weekly".equals(plan)) {
            isWeeklySelected = true;
            // 这里可以添加视觉反馈，比如改变卡片样式
            Log.d(TAG, "Weekly VIP plan selected");
        } else if ("monthly".equals(plan)) {
            isMonthlySelected = true;
            // 这里可以添加视觉反馈，比如改变卡片样式
            Log.d(TAG, "Monthly VIP plan selected");
        }
    }

    /**
     * 重置充值选择状态
     */
    private void resetRechargeSelections() {
        resetRechargeAmountSelections();
        resetVipSelections();
    }

    /**
     * 重置充值金额选择状态
     */
    private void resetRechargeAmountSelections() {
        selectedRechargeAmount = 0;
        if (rechargeCard500 != null) rechargeCard500.setSelected(false);
        if (rechargeCard1000 != null) rechargeCard1000.setSelected(false);
        if (rechargeCard2000 != null) rechargeCard2000.setSelected(false);
        if (rechargeCard3000 != null) rechargeCard3000.setSelected(false);
    }

    /**
     * 重置VIP选择状态
     */
    private void resetVipSelections() {
        isWeeklySelected = false;
        isMonthlySelected = false;
        // 这里可以重置VIP卡片的视觉状态
    }

    /**
     * 启动悬浮窗播放
     */
    private void startFloatingVideoPlay() {
        if (currentVideo != null) {
            // 先隐藏当前视频播放器，显示黑色背景
            hideCurrentVideoPlayer();

            // 启动悬浮窗播放
            FloatingVideoManager.getInstance().startFromVideoPlayer(this, currentVideo);
        } else {
            Toast.makeText(this, "当前没有播放的视频", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 隐藏当前视频播放器，显示黑色背景
     */
    private void hideCurrentVideoPlayer() {
        try {
            // 获取当前显示的VideoPlayerFragment
            VideoPlayerFragment currentFragment = getCurrentVideoFragment();
            if (currentFragment != null) {
                // 隐藏Fragment中的PlayerView
                currentFragment.hidePlayerViewForFloating();
            }

            // 也可以直接查找ViewPager中的PlayerView
            if (viewPagerVideos != null) {
                for (int i = 0; i < viewPagerVideos.getChildCount(); i++) {
                    View child = viewPagerVideos.getChildAt(i);
                    hidePlayerViewsInView(child);
                }
            }

            Log.d(TAG, "Current video player hidden for floating window");
        } catch (Exception e) {
            Log.e(TAG, "Failed to hide current video player", e);
        }
    }

    /**
     * 在指定视图中递归查找并隐藏PlayerView
     */
    private void hidePlayerViewsInView(View view) {
        try {
            if (view instanceof com.google.android.exoplayer2.ui.PlayerView) {
                com.google.android.exoplayer2.ui.PlayerView playerView = (com.google.android.exoplayer2.ui.PlayerView) view;
                playerView.setBackgroundColor(android.graphics.Color.BLACK);
                // 暂时移除播放器，显示黑色背景
                if (playerView.getPlayer() != null) {
                    playerView.setPlayer(null);
                }
                Log.d(TAG, "PlayerView hidden and set to black background");
            } else if (view instanceof ViewGroup) {
                ViewGroup viewGroup = (ViewGroup) view;
                for (int i = 0; i < viewGroup.getChildCount(); i++) {
                    hidePlayerViewsInView(viewGroup.getChildAt(i));
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Error hiding player views", e);
        }
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, Intent data) {
        super.onActivityResult(requestCode, resultCode, data);

        // 处理悬浮窗权限请求结果
        if (requestCode == FloatingVideoPermissionUtils.REQUEST_CODE_OVERLAY_PERMISSION) {
            FloatingVideoManager.getInstance().handlePermissionResult(this, requestCode);
        }
    }

    /**
     * 处理下载按钮点击
     */
    private void handleDownloadClick() {
        if (currentVideo == null) {
            Toast.makeText(this, "当前没有视频可下载", Toast.LENGTH_SHORT).show();
            return;
        }

        try {
            // 获取当前选中的剧集信息
            com.android.video.model.EpisodeModel currentEpisode = getCurrentSelectedEpisode();
            if (currentEpisode == null) {
                Toast.makeText(this, "无法获取当前剧集信息", Toast.LENGTH_SHORT).show();
                return;
            }

            // 检查是否有必要的下载参数
            String filmLanguageInfoId = currentVideo.getFilmLanguageInfoId();
            String chapterId = currentEpisode.getId();

            if (filmLanguageInfoId == null || filmLanguageInfoId.isEmpty()) {
                Toast.makeText(this, "缺少视频语言信息，无法下载", Toast.LENGTH_SHORT).show();
                return;
            }

            if (chapterId == null || chapterId.isEmpty()) {
                Toast.makeText(this, "缺少章节信息，无法下载", Toast.LENGTH_SHORT).show();
                return;
            }

            // 显示下载开始提示
            Toast.makeText(this, "正在获取下载地址...", Toast.LENGTH_SHORT).show();

            // 异步获取下载URL
            getDownloadUrlAsync(filmLanguageInfoId, chapterId, currentVideo, currentEpisode);

        } catch (Exception e) {
            Log.e(TAG, "Failed to start download", e);
            Toast.makeText(this, "下载启动失败: " + e.getMessage(), Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 异步获取下载URL
     */
    private void getDownloadUrlAsync(String filmLanguageInfoId, String chapterId,
                                   VideoModel video, com.android.video.model.EpisodeModel episode) {

        // 调用播放地址API，operationType设为2表示下载
        com.android.video.network.ChapterListApiService chapterApiService =
            new com.android.video.network.ChapterListApiService();
        chapterApiService.getPlayUrl(
            filmLanguageInfoId,
            chapterId,
            "auto", // 分辨率
            2, // operationType: 2=下载
            new com.android.video.network.ChapterListApiService.ApiCallback<com.android.video.model.response.PlayUrlResponseModel>() {
                @Override
                public void onSuccess(com.android.video.model.response.PlayUrlResponseModel response) {
                    if (response != null && response.isSuccess() && response.getData() != null) {
                        String downloadUrl = response.getData().getVideoUrl();
                        if (downloadUrl != null && !downloadUrl.isEmpty()) {
                            startDownloadWithUrl(downloadUrl, video, episode, filmLanguageInfoId, chapterId);
                        } else {
                            runOnUiThread(() ->
                                Toast.makeText(VideoPlayerActivity.this, "获取下载地址失败", Toast.LENGTH_SHORT).show()
                            );
                        }
                    } else {
                        runOnUiThread(() ->
                            Toast.makeText(VideoPlayerActivity.this, "获取下载地址失败: " +
                                (response != null ? response.getMessage() : "未知错误"), Toast.LENGTH_SHORT).show()
                        );
                    }
                }

                @Override
                public void onError(String error) {
                    Log.e(TAG, "Failed to get download URL: " + error);
                    runOnUiThread(() ->
                        Toast.makeText(VideoPlayerActivity.this, "获取下载地址失败: " + error, Toast.LENGTH_SHORT).show()
                    );
                }
            }
        );
    }

    /**
     * 使用获取到的URL开始下载
     */
    private void startDownloadWithUrl(String downloadUrl, VideoModel video,
                                    com.android.video.model.EpisodeModel episode,
                                    String filmLanguageInfoId, String chapterId) {

        // 生成文件名
        String fileName = generateDownloadFileName(video, episode);
        String title = video.getTitle() + " - 第" + episode.getEpisodeNumber() + "集";

        // 启动下载服务
        com.android.video.service.DownloadService.startDownload(
            this,
            downloadUrl,
            fileName,
            null, // downloadRecordId，暂时为空
            chapterId,
            filmLanguageInfoId,
            title
        );

        runOnUiThread(() -> {
            Toast.makeText(this, "开始下载: " + title, Toast.LENGTH_SHORT).show();
            Log.d(TAG, "Started download for: " + fileName + " from URL: " + downloadUrl);
        });
    }

    /**
     * 获取当前选中的剧集
     */
    private com.android.video.model.EpisodeModel getCurrentSelectedEpisode() {
        if (currentVideo == null || currentVideo.getEpisodes() == null) {
            return null;
        }

        // 查找选中的剧集
        for (com.android.video.model.EpisodeModel episode : currentVideo.getEpisodes()) {
            if (episode.isSelected()) {
                return episode;
            }
        }

        // 如果没有选中的剧集，返回第一集
        if (!currentVideo.getEpisodes().isEmpty()) {
            return currentVideo.getEpisodes().get(0);
        }

        return null;
    }

    /**
     * 生成下载文件名
     */
    private String generateDownloadFileName(VideoModel video, com.android.video.model.EpisodeModel episode) {
        String title = video.getTitle();
        if (title == null || title.isEmpty()) {
            title = "Video";
        }

        // 清理文件名中的非法字符
        title = title.replaceAll("[^a-zA-Z0-9._-]", "_");

        return title + "_E" + episode.getEpisodeNumber() + ".mp4";
    }

    // ==================== 内容保护功能 ====================

    /**
     * 重写父类方法，启用内容保护功能
     * 视频播放页面需要启用内容保护
     */
    @Override
    protected boolean shouldEnableContentProtection() {
        return true; // 视频播放页面启用内容保护
    }

    /**
     * 初始化内容保护功能
     */
    private void initializeContentProtection() {
        if (!isContentProtectionEnabled()) {
            Log.w(TAG, "Content protection not enabled, skipping initialization");
            return;
        }

        try {
            // 创建录屏监控器
            recordingMonitor = new ScreenRecordingMonitor();

            // 启动录屏监控，传入播放控制回调
            recordingMonitor.startMonitoring(this, new ScreenRecordingMonitor.VideoPlayerCallback() {
                @Override
                public void pausePlayback() {
                    Log.w(TAG, "Pausing playback due to screen recording detection");
                    pauseAllVideos();
                }

                @Override
                public void resumePlayback() {
                    Log.i(TAG, "Resuming playback after screen recording stopped");
                    resumeAllVideos();
                }

                @Override
                public boolean isPlaying() {
                    // 检查当前是否有视频在播放
                    try {
                        VideoPlayerFragment currentFragment = getCurrentVideoFragment();
                        if (currentFragment != null) {
                            VideoPlayerManager manager = currentFragment.getPlayerManager();
                            return manager != null && manager.isPlaying();
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "Failed to check playing status", e);
                    }
                    return false;
                }
            });

            Log.d(TAG, "Content protection initialized successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize content protection", e);
        }
    }

    /**
     * 恢复所有视频播放
     */
    private void resumeAllVideos() {
        try {
            VideoPlayerFragment currentFragment = getCurrentVideoFragment();
            if (currentFragment != null) {
                VideoPlayerManager currentManager = currentFragment.getPlayerManager();
                if (currentManager != null && !currentManager.isPlaying()) {
                    currentManager.play();
                    Log.d(TAG, "Video playback resumed");
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to resume video playback", e);
        }
    }





}
