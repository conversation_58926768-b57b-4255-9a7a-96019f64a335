package com.android.video.download;

import android.content.Context;
import android.util.Log;

import com.android.video.model.request.ConfirmDownloadRequestModel;
import com.android.video.model.response.ConfirmDownloadResponseModel;
import com.android.video.network.ConfirmDownloadApiService;
import com.android.video.network.NetworkUtils;

import java.io.File;
import java.security.MessageDigest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 下载管理器
 * <p>
 * 管理所有下载任务，包括队列管理、并发控制、状态监听等功能。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadManager {

    private static final String TAG = "DownloadManager";

    private static volatile DownloadManager instance;
    private final Context context;
    private final DownloadConfig config;
    private final ExecutorService executorService;
    private final Map<String, DownloadInfo> downloadTasks;
    private final Map<String, FileDownloader> activeDownloaders;
    private final List<DownloadListener> downloadListeners;
    private final List<DownloadListener.GlobalDownloadListener> globalListeners;

    /**
     * 私有构造函数
     *
     * @param context 应用上下文
     */
    private DownloadManager(Context context) {
        this.context = context.getApplicationContext();
        this.config = new DownloadConfig(this.context);
        this.executorService = Executors.newCachedThreadPool();
        this.downloadTasks = new ConcurrentHashMap<>();
        this.activeDownloaders = new ConcurrentHashMap<>();
        this.downloadListeners = new ArrayList<>();
        this.globalListeners = new ArrayList<>();
    }

    /**
     * 获取单例实例
     *
     * @param context 应用上下文
     * @return DownloadManager实例
     */
    public static DownloadManager getInstance(Context context) {
        if (instance == null) {
            synchronized (DownloadManager.class) {
                if (instance == null) {
                    instance = new DownloadManager(context);
                }
            }
        }
        return instance;
    }

    /**
     * 获取下载配置
     *
     * @return 下载配置
     */
    public DownloadConfig getConfig() {
        return config;
    }

    /**
     * 添加下载任务
     *
     * @param url 下载URL
     * @param fileName 文件名
     * @param downloadRecordId 服务器下载记录ID
     * @param chapterId 章节ID
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param title 视频标题
     * @return 下载信息
     */
    public DownloadInfo addDownload(String url, String fileName, String downloadRecordId,
                                   String chapterId, String filmLanguageInfoId, String title) {
        if (url == null || url.trim().isEmpty()) {
            Log.e(TAG, "Download URL is empty");
            return null;
        }

        // 生成安全的文件名
        String safeFileName = config.generateSafeFileName(fileName);
        String filePath = config.getVideoFilePath(safeFileName);

        // 创建下载信息
        DownloadInfo downloadInfo = new DownloadInfo(url, safeFileName, filePath);
        downloadInfo.setDownloadRecordId(downloadRecordId);
        downloadInfo.setChapterId(chapterId);
        downloadInfo.setFilmLanguageInfoId(filmLanguageInfoId);
        downloadInfo.setTitle(title);

        // 检查是否已存在相同的下载任务
        for (DownloadInfo existingInfo : downloadTasks.values()) {
            if (url.equals(existingInfo.getUrl()) || filePath.equals(existingInfo.getFilePath())) {
                Log.w(TAG, "Download task already exists: " + fileName);
                return existingInfo;
            }
        }

        // 添加到任务列表
        downloadTasks.put(downloadInfo.getDownloadId(), downloadInfo);

        // 通知监听器
        notifyDownloadAdded(downloadInfo);

        Log.d(TAG, "Added download task: " + fileName);
        return downloadInfo;
    }

    /**
     * 开始下载
     *
     * @param downloadId 下载ID
     */
    public void startDownload(String downloadId) {
        DownloadInfo downloadInfo = downloadTasks.get(downloadId);
        if (downloadInfo == null) {
            Log.e(TAG, "Download task not found: " + downloadId);
            return;
        }

        startDownload(downloadInfo);
    }

    /**
     * 开始下载
     *
     * @param downloadInfo 下载信息
     */
    public void startDownload(DownloadInfo downloadInfo) {
        if (downloadInfo == null) {
            Log.e(TAG, "DownloadInfo is null");
            return;
        }

        // 检查网络状态
        if (!NetworkUtils.isNetworkAvailable(context)) {
            String error = "网络不可用";
            Log.e(TAG, error);
            notifyDownloadFailed(downloadInfo, error);
            return;
        }

        // 检查WiFi限制
        if (config.isEnableWifiOnly() && !NetworkUtils.getNetworkType(context).equals(NetworkUtils.NetworkType.WIFI)) {
            String error = "仅WiFi下载模式，当前非WiFi网络";
            Log.e(TAG, error);
            notifyDownloadFailed(downloadInfo, error);
            return;
        }

        // 检查并发下载数量
        if (activeDownloaders.size() >= config.getMaxConcurrentDownloads()) {
            downloadInfo.setStatus(DownloadInfo.DownloadStatus.PENDING);
            Log.d(TAG, "Download queued: " + downloadInfo.getFileName());
            return;
        }

        // 开始下载
        executorService.execute(() -> performDownload(downloadInfo));
    }

    /**
     * 执行下载
     *
     * @param downloadInfo 下载信息
     */
    private void performDownload(DownloadInfo downloadInfo) {
        FileDownloader downloader = new FileDownloader(config);
        activeDownloaders.put(downloadInfo.getDownloadId(), downloader);

        // 创建下载监听器
        DownloadListener listener = new DownloadListener.SimpleDownloadListener() {
            @Override
            public void onDownloadStart(DownloadInfo info) {
                notifyDownloadStart(info);
            }

            @Override
            public void onDownloadProgress(DownloadInfo info, long downloadedSize, long totalSize, int progress) {
                notifyDownloadProgress(info, downloadedSize, totalSize, progress);
            }

            @Override
            public void onDownloadComplete(DownloadInfo info) {
                activeDownloaders.remove(info.getDownloadId());
                notifyDownloadComplete(info);
                
                // 计算文件校验和并确认下载
                calculateChecksumAndConfirm(info);
                
                // 启动下一个等待的下载
                startNextPendingDownload();
            }

            @Override
            public void onDownloadFailed(DownloadInfo info, String errorMessage) {
                activeDownloaders.remove(info.getDownloadId());
                
                // 检查是否可以重试
                if (info.canRetry(config.getRetryCount())) {
                    info.incrementRetryCount();
                    Log.d(TAG, "Retrying download: " + info.getFileName() + ", attempt: " + info.getRetryCount());
                    
                    // 延迟重试
                    executorService.execute(() -> {
                        try {
                            Thread.sleep(config.getRetryDelay());
                            performDownload(info);
                        } catch (InterruptedException e) {
                            Thread.currentThread().interrupt();
                        }
                    });
                } else {
                    notifyDownloadFailed(info, errorMessage);
                    startNextPendingDownload();
                }
            }

            @Override
            public void onDownloadCancelled(DownloadInfo info) {
                activeDownloaders.remove(info.getDownloadId());
                notifyDownloadCancelled(info);
                startNextPendingDownload();
            }

            @Override
            public void onDownloadPause(DownloadInfo info) {
                activeDownloaders.remove(info.getDownloadId());
                notifyDownloadPause(info);
                startNextPendingDownload();
            }
        };

        // 开始下载
        downloader.startDownload(downloadInfo, listener);
    }

    /**
     * 计算文件校验和并确认下载
     *
     * @param downloadInfo 下载信息
     */
    private void calculateChecksumAndConfirm(DownloadInfo downloadInfo) {
        executorService.execute(() -> {
            try {
                // 计算文件MD5校验和
                String checksum = calculateFileMD5(downloadInfo.getFilePath());
                downloadInfo.setChecksum(checksum);

                // 调用确认下载接口
                if (downloadInfo.getDownloadRecordId() != null) {
                    confirmDownloadToServer(downloadInfo);
                }

                Log.d(TAG, "File checksum calculated: " + checksum);
            } catch (Exception e) {
                Log.e(TAG, "Error calculating checksum", e);
            }
        });
    }

    /**
     * 计算文件MD5校验和
     *
     * @param filePath 文件路径
     * @return MD5校验和
     */
    private String calculateFileMD5(String filePath) {
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            java.io.FileInputStream fis = new java.io.FileInputStream(filePath);
            byte[] buffer = new byte[8192];
            int bytesRead;
            
            while ((bytesRead = fis.read(buffer)) != -1) {
                md.update(buffer, 0, bytesRead);
            }
            
            fis.close();
            
            byte[] digest = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : digest) {
                sb.append(String.format("%02x", b));
            }
            
            return sb.toString();
        } catch (Exception e) {
            Log.e(TAG, "Error calculating MD5", e);
            return "";
        }
    }

    /**
     * 向服务器确认下载完成
     *
     * @param downloadInfo 下载信息
     */
    private void confirmDownloadToServer(DownloadInfo downloadInfo) {
        ConfirmDownloadRequestModel requestModel = new ConfirmDownloadRequestModel(
                downloadInfo.getDownloadRecordId(),
                downloadInfo.getChecksum(),
                downloadInfo.getFilePath()
        );

        ConfirmDownloadApiService.getInstance().confirmDownload(requestModel,
                new ConfirmDownloadApiService.ConfirmDownloadCallback() {
                    @Override
                    public void onSuccess(ConfirmDownloadResponseModel response) {
                        Log.d(TAG, "Download confirmed to server: " + downloadInfo.getFileName());
                    }

                    @Override
                    public void onError(String errorMessage) {
                        Log.e(TAG, "Failed to confirm download to server: " + errorMessage);
                    }
                });
    }

    /**
     * 启动下一个等待的下载
     */
    private void startNextPendingDownload() {
        if (activeDownloaders.size() >= config.getMaxConcurrentDownloads()) {
            return;
        }

        for (DownloadInfo downloadInfo : downloadTasks.values()) {
            if (downloadInfo.getStatus() == DownloadInfo.DownloadStatus.PENDING) {
                startDownload(downloadInfo);
                break;
            }
        }
    }

    /**
     * 暂停下载
     *
     * @param downloadId 下载ID
     */
    public void pauseDownload(String downloadId) {
        FileDownloader downloader = activeDownloaders.get(downloadId);
        if (downloader != null) {
            downloader.pauseDownload();
        }
    }

    /**
     * 取消下载
     *
     * @param downloadId 下载ID
     */
    public void cancelDownload(String downloadId) {
        FileDownloader downloader = activeDownloaders.get(downloadId);
        if (downloader != null) {
            downloader.cancelDownload();
        }
        
        // 从任务列表中移除
        DownloadInfo downloadInfo = downloadTasks.remove(downloadId);
        if (downloadInfo != null) {
            // 删除临时文件
            File tempFile = new File(downloadInfo.getTempFilePath());
            if (tempFile.exists()) {
                tempFile.delete();
            }
            
            notifyDownloadRemoved(downloadInfo);
        }
    }

    /**
     * 获取下载信息
     *
     * @param downloadId 下载ID
     * @return 下载信息
     */
    public DownloadInfo getDownloadInfo(String downloadId) {
        return downloadTasks.get(downloadId);
    }

    /**
     * 获取所有下载任务
     *
     * @return 下载任务列表
     */
    public List<DownloadInfo> getAllDownloads() {
        return new ArrayList<>(downloadTasks.values());
    }

    /**
     * 获取指定状态的下载任务
     *
     * @param status 下载状态
     * @return 下载任务列表
     */
    public List<DownloadInfo> getDownloadsByStatus(DownloadInfo.DownloadStatus status) {
        List<DownloadInfo> result = new ArrayList<>();
        for (DownloadInfo info : downloadTasks.values()) {
            if (info.getStatus() == status) {
                result.add(info);
            }
        }
        return result;
    }

    /**
     * 添加下载监听器
     *
     * @param listener 下载监听器
     */
    public void addDownloadListener(DownloadListener listener) {
        if (listener != null && !downloadListeners.contains(listener)) {
            downloadListeners.add(listener);
        }
    }

    /**
     * 移除下载监听器
     *
     * @param listener 下载监听器
     */
    public void removeDownloadListener(DownloadListener listener) {
        downloadListeners.remove(listener);
    }

    /**
     * 添加全局下载监听器
     *
     * @param listener 全局下载监听器
     */
    public void addGlobalDownloadListener(DownloadListener.GlobalDownloadListener listener) {
        if (listener != null && !globalListeners.contains(listener)) {
            globalListeners.add(listener);
        }
    }

    /**
     * 移除全局下载监听器
     *
     * @param listener 全局下载监听器
     */
    public void removeGlobalDownloadListener(DownloadListener.GlobalDownloadListener listener) {
        globalListeners.remove(listener);
    }

    // ========== 通知方法 ==========

    private void notifyDownloadStart(DownloadInfo downloadInfo) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadStart(downloadInfo);
        }
    }

    private void notifyDownloadProgress(DownloadInfo downloadInfo, long downloadedSize, long totalSize, int progress) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadProgress(downloadInfo, downloadedSize, totalSize, progress);
        }
    }

    private void notifyDownloadComplete(DownloadInfo downloadInfo) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadComplete(downloadInfo);
        }
    }

    private void notifyDownloadFailed(DownloadInfo downloadInfo, String errorMessage) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadFailed(downloadInfo, errorMessage);
        }
    }

    private void notifyDownloadCancelled(DownloadInfo downloadInfo) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadCancelled(downloadInfo);
        }
    }

    private void notifyDownloadPause(DownloadInfo downloadInfo) {
        for (DownloadListener listener : downloadListeners) {
            listener.onDownloadPause(downloadInfo);
        }
    }

    private void notifyDownloadAdded(DownloadInfo downloadInfo) {
        for (DownloadListener.GlobalDownloadListener listener : globalListeners) {
            listener.onDownloadAdded(downloadInfo);
        }
    }

    private void notifyDownloadRemoved(DownloadInfo downloadInfo) {
        for (DownloadListener.GlobalDownloadListener listener : globalListeners) {
            listener.onDownloadRemoved(downloadInfo);
        }
    }
}
