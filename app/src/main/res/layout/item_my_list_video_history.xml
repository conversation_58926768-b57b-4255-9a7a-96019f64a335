<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:minHeight="@dimen/my_list_history_item_height"
    android:layout_marginStart="@dimen/my_list_history_item_margin_start"
    android:layout_marginEnd="@dimen/my_list_history_item_margin_end"
    android:layout_marginBottom="@dimen/my_list_history_item_margin_bottom"
    android:paddingHorizontal="@dimen/my_list_history_item_margin_start"
    android:paddingVertical="12dp"
    android:background="?android:attr/selectableItemBackground"
    android:clickable="true"
    android:focusable="true">

    <!-- 海报图片 -->
    <ImageView
        android:id="@+id/iv_poster"
        android:layout_width="@dimen/my_list_history_poster_width"
        android:layout_height="@dimen/my_list_history_poster_height"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:scaleType="centerCrop"
        android:background="@drawable/my_list_history_poster_bg"
        android:src="@drawable/movie_poster"
        android:clipToOutline="true" />

    <!-- 标题 - 与海报头部对齐 -->
    <TextView
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="@dimen/my_list_history_title_height"
        android:layout_toEndOf="@id/iv_poster"
        android:layout_toStartOf="@id/btn_play"
        android:layout_alignTop="@id/iv_poster"
        android:layout_marginStart="@dimen/my_list_history_poster_margin_end"
        android:layout_marginEnd="8dp"
        android:text="Eternal Love"
        android:textColor="#ffffffff"
        android:textSize="@dimen/my_list_history_title_text_size"
        android:maxLines="1"
        android:ellipsize="end"
        android:gravity="top"
        android:fontFamily="sans-serif" />

    <!-- 当前剧集 -->
    <TextView
        android:id="@+id/tv_current_episode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/iv_poster"
        android:layout_below="@id/tv_title"
        android:layout_marginStart="@dimen/my_list_history_poster_margin_end"
        android:layout_marginTop="@dimen/my_list_history_episode_margin_top"
        android:text="EP.24"
        android:textColor="#fff12626"
        android:textSize="@dimen/my_list_history_episode_text_size"
        android:fontFamily="sans-serif" />

    <!-- 总剧集 -->
    <TextView
        android:id="@+id/tv_total_episode"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/tv_current_episode"
        android:layout_alignBaseline="@id/tv_current_episode"
        android:text="/EP.72"
        android:textColor="#ffffffff"
        android:textSize="@dimen/my_list_history_episode_text_size"
        android:fontFamily="sans-serif" />

    <!-- Play按钮 -->
    <LinearLayout
        android:id="@+id/btn_play"
        android:layout_width="@dimen/my_list_history_play_button_width"
        android:layout_height="@dimen/my_list_history_play_button_height"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:background="@drawable/my_list_history_play_button_bg"
        android:orientation="horizontal"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true">

        <!-- Play图标 -->
        <ImageView
            android:layout_width="@dimen/my_list_history_play_icon_size"
            android:layout_height="@dimen/my_list_history_play_icon_size"
            android:src="@drawable/list_ic_play"
            android:layout_marginEnd="6dp" />

        <!-- Play文字 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="Play"
            android:textColor="#ffffffff"
            android:textSize="@dimen/my_list_history_play_text_size"
            android:gravity="center"
            android:fontFamily="sans-serif" />

    </LinearLayout>

</RelativeLayout>
