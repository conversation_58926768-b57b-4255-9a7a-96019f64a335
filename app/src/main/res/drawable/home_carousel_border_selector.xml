<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    
    <!-- 选中状态 -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <!-- 渐变背景填充整个区域 -->
            <gradient
                android:angle="0"
                android:startColor="#AF1E3C"
                android:endColor="#2403AC"
                android:type="linear" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- 未选中状态 -->
    <item>
        <shape android:shape="rectangle">
            <!-- 透明背景 -->
            <solid android:color="#00000000" />
            <!-- 边框 -->
            <stroke
                android:width="0.5dp"
                android:color="#474747" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
</selector>
