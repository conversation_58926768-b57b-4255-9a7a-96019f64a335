package com.android.video.ui.activity;

import android.animation.AnimatorSet;
import android.animation.ObjectAnimator;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentTransaction;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.graphics.Insets;
import androidx.constraintlayout.widget.ConstraintLayout;
import com.android.video.R;
import com.android.video.databinding.ActivityMainBinding;
import com.android.video.ui.fragment.HomeFragment;
import com.android.video.ui.fragment.DiscoverFragment;
import com.android.video.ui.fragment.MyListFragment;
import com.android.video.ui.fragment.ProfileFragment;
import com.android.video.utils.NavigationBarUtils;
import com.android.video.utils.TestValidationUtils;
import com.android.video.utils.UserSessionUtils;
import com.android.video.cache.CachePreloadManager;
import com.android.video.cache.AdvancedCacheManager;
import com.android.video.cache.SmartCacheManager;
import com.android.video.network.ApiClientUtils;
import com.android.video.utils.CacheTestUtils;
import com.android.video.utils.CacheValidationUtils;
import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.utils.CacheMonitor;
import android.content.Intent;
import com.android.video.utils.TokenConfigUtils;
import com.android.video.manager.GlobalVideoPlayerManager;
import com.android.video.manager.FragmentCacheManager;
import com.android.video.network.AuthApiUtils;
import com.android.video.model.response.InitDeviceResponseModel;
import com.android.video.base.BaseMultiLanguageActivity;
import com.google.android.material.bottomnavigation.BottomNavigationView;
import android.widget.Toast;
import android.os.Handler;
import android.os.Looper;

import java.io.IOException;

/**
 * 主Activity - 包含底部导航栏和Fragment容器
 * <AUTHOR>
 */
public class MainActivity extends BaseMultiLanguageActivity {

    private static final String TAG = "MainActivity";
    private ActivityMainBinding binding;
    private BottomNavigationView bottomNavigationView;

    // Fragment实例缓存
    private HomeFragment homeFragment;
    private DiscoverFragment discoverFragment;
    private MyListFragment myListFragment;
    private ProfileFragment profileFragment;

    // 双击返回键退出相关
    private boolean doubleBackToExitPressedOnce = false;
    private Handler exitHandler = new Handler(Looper.getMainLooper());

    // Fragment缓存管理器
    private FragmentCacheManager fragmentCacheManager;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);

        Log.d(TAG, "🎯 MainActivity onCreate 开始执行");

        // 确保网络组件已初始化
        initializeNetworkComponents();

        // 初始化缓存系统
        initializeCacheSystem();

        // 初始化Fragment缓存管理器
        fragmentCacheManager = FragmentCacheManager.getInstance();

        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        // 添加页面淡入动画
        com.android.video.utils.UIAnimationUtils.animatePageFadeIn(binding.getRoot());

        // 隐藏ActionBar以获得全屏体验
        if (getSupportActionBar() != null) {
            getSupportActionBar().hide();
        }

        initBottomNavigation();
        setupBottomNavigationBackground();

        // 默认显示首页
        if (savedInstanceState == null) {
            if (homeFragment == null) {
                homeFragment = new HomeFragment();
            }
            loadFragment(homeFragment);
        }

        // 检查是否从语言切换跳转而来
        handleLanguageChangeNavigation();

        // 添加测试验证逻辑
        Log.d(TAG, "🎯 准备调用 performInitialValidation");
        performInitialValidation();

        // 启动缓存预加载
        startCachePreloading();

        // 检查版本更新
        checkForAppUpdate();
    }

    @Override
    protected void onResume() {
        super.onResume();
        android.util.Log.d("MainActivity", "onResume called");

        // 添加从后台到前台的模糊到清晰过渡效果
        if (binding != null && binding.getRoot() != null) {
            com.android.video.utils.UIAnimationUtils.animateBlurToClear(binding.getRoot());
        }
    }

    private void initBottomNavigation() {
        bottomNavigationView = binding.bottomNavigation;

        // 移除默认的选中效果，使用自定义背景
        bottomNavigationView.setItemHorizontalTranslationEnabled(false);

        bottomNavigationView.setOnItemSelectedListener(item -> {
            Fragment selectedFragment = null;

            if (item.getItemId() == R.id.nav_home) {
                if (homeFragment == null) {
                    homeFragment = new HomeFragment();
                }
                selectedFragment = homeFragment;
            } else if (item.getItemId() == R.id.nav_discover) {
                if (discoverFragment == null) {
                    discoverFragment = new DiscoverFragment();
                }
                selectedFragment = discoverFragment;
            } else if (item.getItemId() == R.id.nav_my_list) {
                if (myListFragment == null) {
                    myListFragment = new MyListFragment();
                }
                selectedFragment = myListFragment;
            } else if (item.getItemId() == R.id.nav_profile) {
                if (profileFragment == null) {
                    profileFragment = new ProfileFragment();
                }
                selectedFragment = profileFragment;

                // 刷新ProfileFragment的用户信息
                if (profileFragment != null) {
                    profileFragment.refreshUserInfo();
                }
            }

            if (selectedFragment != null) {
                // 在切换Fragment前停止所有视频播放
                if (!(selectedFragment instanceof DiscoverFragment)) {
                    // 如果不是切换到Discover页面，停止所有视频播放
                    GlobalVideoPlayerManager.getInstance().forceStopAllPlayback();
                }

                loadFragment(selectedFragment);
                // 在选中状态更新后添加动画效果
                bottomNavigationView.post(this::addSelectionAnimation);
                return true;
            }
            return false;
        });

        // 设置默认选中首页
        bottomNavigationView.setSelectedItemId(R.id.nav_home);

        // 在调试模式下添加缓存测试入口
        if (EnvironmentConfigUtils.isDebugMode()) {
            setupCacheTestEntry();
        }

        Log.d(TAG, "MainActivity onCreate completed");
    }

    /**
     * 设置缓存测试入口（仅在调试模式下）
     */
    private void setupCacheTestEntry() {
        // 设置长按底部导航栏启动缓存测试
        bottomNavigationView.setOnLongClickListener(v -> {
            Intent intent = new Intent(this, CacheTestActivity.class);
            startActivity(intent);
            Toast.makeText(this, "启动缓存测试界面", Toast.LENGTH_SHORT).show();
            return true;
        });

        Log.d(TAG, "缓存测试入口已设置（长按底部导航栏）");
    }

    @Override
    public void onConfigurationChanged(android.content.res.Configuration newConfig) {
        super.onConfigurationChanged(newConfig);

        // 通知当前Fragment处理配置变化
        androidx.fragment.app.Fragment currentFragment = getSupportFragmentManager().findFragmentById(R.id.nav_host_fragment);
        if (currentFragment instanceof com.android.video.ui.fragment.DiscoverFragment) {
            currentFragment.onConfigurationChanged(newConfig);
        }

        // 配置变化时重新应用导航栏适配
        // 延迟执行以确保布局已更新
        if (bottomNavigationView != null) {
            bottomNavigationView.post(() -> {
                // 重新获取WindowInsets并应用适配
                androidx.core.view.WindowInsetsCompat insets =
                    androidx.core.view.ViewCompat.getRootWindowInsets(bottomNavigationView);
                if (insets != null) {
                    int navigationBarHeight = getNavigationBarHeight();
                    boolean hasNavigationBar = hasNavigationBar();
                    boolean isGestureNavigation = isGestureNavigation();

                    onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

                    android.util.Log.d("MainActivity", "配置变化后重新应用导航栏适配 - 方向: " +
                        (newConfig.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE ? "横屏" : "竖屏"));

                    // 验证配置变化后的适配效果
                    validateConfigurationChangeAdaptation();
                }
            });
        }
    }

    private void loadFragment(Fragment fragment) {
        FragmentTransaction transaction = getSupportFragmentManager().beginTransaction();

        // 添加切换动画
        transaction.setCustomAnimations(
            R.anim.slide_in_right,
            R.anim.slide_out_left,
            R.anim.slide_in_left,
            R.anim.slide_out_right
        );

        // 使用replace策略，但通过FragmentCacheManager管理缓存状态
        transaction.replace(R.id.nav_host_fragment, fragment);
        transaction.commitAllowingStateLoss();

        // 通知Fragment缓存管理器Fragment已切换
        String fragmentKey = fragment.getClass().getSimpleName();
        Log.d(TAG, "Fragment replaced with: " + fragmentKey);

        // 记录Fragment切换到缓存管理器
        if (fragmentCacheManager != null) {
            // 这里不重置缓存状态，让Fragment自己决定是否需要重新加载数据
            Log.d(TAG, "Fragment cache manager notified of fragment switch: " + fragmentKey);
        }
    }

    private void setupBottomNavigationBackground() {
        // 禁用默认的选中指示器，使用自定义背景
        bottomNavigationView.setItemActiveIndicatorEnabled(false);
    }

    /**
     * 添加底部导航栏选中动画效果
     */
    private void addSelectionAnimation() {
        View menuView = bottomNavigationView.getChildAt(0);
        if (menuView instanceof ViewGroup) {
            ViewGroup menuViewGroup = (ViewGroup) menuView;
            for (int i = 0; i < menuViewGroup.getChildCount(); i++) {
                View itemView = menuViewGroup.getChildAt(i);
                if (itemView.isSelected()) {
                    // 为选中的项添加缩放动画
                    ObjectAnimator scaleX = ObjectAnimator.ofFloat(itemView, "scaleX", 0.9f, 1.0f);
                    ObjectAnimator scaleY = ObjectAnimator.ofFloat(itemView, "scaleY", 0.9f, 1.0f);

                    AnimatorSet animatorSet = new AnimatorSet();
                    animatorSet.playTogether(scaleX, scaleY);
                    animatorSet.setDuration(200);
                    animatorSet.setInterpolator(new AccelerateDecelerateInterpolator());
                    animatorSet.start();
                    break;
                }
            }
        }
    }

    /**
     * 处理导航栏相关的布局调整
     * 确保底部导航栏不被系统导航栏遮挡
     * 支持Android 15的edge-to-edge模式
     */
    @Override
    protected void onApplyNavigationBarInsets(androidx.core.view.WindowInsetsCompat insets,
                                            int navigationBarHeight, boolean hasNavigationBar,
                                            boolean isGestureNavigation) {
        super.onApplyNavigationBarInsets(insets, navigationBarHeight, hasNavigationBar, isGestureNavigation);

        android.util.Log.e("MainActivity", "🚀🚀🚀 onApplyNavigationBarInsets 被调用了！🚀🚀🚀");
        android.util.Log.d("MainActivity", "=== 开始导航栏适配调试 ===");
        android.util.Log.d("MainActivity", "输入参数 - navigationBarHeight: " + navigationBarHeight +
            ", hasNavigationBar: " + hasNavigationBar + ", isGestureNavigation: " + isGestureNavigation);

        // 检查bottomNavigationView状态
        if (bottomNavigationView == null) {
            android.util.Log.e("MainActivity", "❌ bottomNavigationView为null，适配失败！");
            return;
        }
        android.util.Log.d("MainActivity", "✅ bottomNavigationView存在，继续适配");

        // 添加调试信息，使用NavigationBarUtils进行详细分析
        com.android.video.utils.NavigationBarUtils.debugNavigationBarConfig(this);

        // 移除try-catch，让真实错误暴露出来
        // 获取更精确的导航栏信息
        com.android.video.utils.NavigationBarUtils.NavigationBarInfo navInfo =
            com.android.video.utils.NavigationBarUtils.getNavigationBarInfo(this);
        android.util.Log.d("MainActivity", "NavigationBarInfo: " + navInfo.toString());

        // 改进的手势导航检测逻辑
        boolean isGestureNav = isImprovedGestureNavigation(insets);
        android.util.Log.d("MainActivity", "手势导航检测结果: " + isGestureNav + " (输入参数: " + isGestureNavigation + ")");

        int recommendedPadding = com.android.video.utils.NavigationBarUtils.getRecommendedBottomPadding(this);
        android.util.Log.d("MainActivity", "推荐内边距: " + recommendedPadding + "px");

        // 获取正确的布局参数类型
        ViewGroup.LayoutParams layoutParams = bottomNavigationView.getLayoutParams();
        android.util.Log.d("MainActivity", "布局参数类型: " + layoutParams.getClass().getName());

        if (layoutParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
            android.util.Log.d("MainActivity", "✅ 使用ConstraintLayout.LayoutParams");

            // ConstraintLayout环境下的处理
            androidx.constraintlayout.widget.ConstraintLayout.LayoutParams constraintParams =
                (androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) layoutParams;

            // 记录设置前的margin值
            int oldMargin = constraintParams.bottomMargin;
            android.util.Log.d("MainActivity", "设置前bottomMargin: " + oldMargin + "px");

            if (navInfo.exists) {
                if (!isGestureNav) {
                    // 按钮导航：使用完整的导航栏高度作为margin
                    constraintParams.bottomMargin = recommendedPadding;
                    android.util.Log.d("MainActivity", "🔧 按钮导航适配 - 设置bottomMargin: " + recommendedPadding + "px");
                } else {
                    // 手势导航：使用适当的margin将底部导航栏推高，避免与手势导航栏重叠
                    int gestureNavMargin = Math.max(navInfo.height / 2, 48); // 使用导航栏高度的一半，最小48px
                    constraintParams.bottomMargin = gestureNavMargin;
                    android.util.Log.d("MainActivity", "🔧 手势导航适配 - 设置bottomMargin: " + gestureNavMargin + "px (导航栏高度: " + navInfo.height + "px)");
                }
            } else {
                // 无导航栏
                constraintParams.bottomMargin = 0;
                android.util.Log.d("MainActivity", "🔧 无导航栏 - 设置bottomMargin: 0px");
            }

            android.util.Log.d("MainActivity", "📝 调用setLayoutParams前，constraintParams.bottomMargin = " + constraintParams.bottomMargin);
            bottomNavigationView.setLayoutParams(constraintParams);
            android.util.Log.d("MainActivity", "✅ setLayoutParams调用完成");

            // 立即验证设置结果
            ViewGroup.LayoutParams newParams = bottomNavigationView.getLayoutParams();
            if (newParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
                int actualMargin = ((androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) newParams).bottomMargin;
                android.util.Log.d("MainActivity", "🔍 设置后实际bottomMargin: " + actualMargin + "px");
                android.util.Log.d("MainActivity", "📊 Margin变化: " + oldMargin + " → " + actualMargin + " (期望: " +
                    (navInfo.exists && !isGestureNav ? recommendedPadding : (navInfo.exists ? Math.min(Math.max(navInfo.height / 2, 48), 96) : 0)) + ")");
            }

        } else if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
            android.util.Log.d("MainActivity", "⚠️ 使用通用MarginLayoutParams");

            // 其他布局环境下的处理
            ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) layoutParams;
            int oldMargin = marginParams.bottomMargin;
            android.util.Log.d("MainActivity", "设置前bottomMargin: " + oldMargin + "px");

            if (navInfo.exists) {
                if (!isGestureNav) {
                    marginParams.bottomMargin = recommendedPadding;
                    android.util.Log.d("MainActivity", "🔧 按钮导航适配(通用) - 设置bottomMargin: " + recommendedPadding + "px");
                } else {
                    // 手势导航：使用适当的margin将底部导航栏推高
                    int gestureNavMargin = Math.max(navInfo.height / 2, 48);
                    marginParams.bottomMargin = gestureNavMargin;
                    android.util.Log.d("MainActivity", "🔧 手势导航适配(通用) - 设置bottomMargin: " + gestureNavMargin + "px (导航栏高度: " + navInfo.height + "px)");
                }
            } else {
                marginParams.bottomMargin = 0;
                android.util.Log.d("MainActivity", "🔧 无导航栏(通用) - 设置bottomMargin: 0px");
            }

            bottomNavigationView.setLayoutParams(marginParams);
            android.util.Log.d("MainActivity", "✅ setLayoutParams(通用)调用完成");
        } else {
            android.util.Log.e("MainActivity", "❌ 未知的布局参数类型: " + layoutParams.getClass().getName());
        }

        // 最终验证和调试信息
        android.util.Log.d("MainActivity", "📋 导航栏适配详情总结:");
        android.util.Log.d("MainActivity", "  - 原始高度: " + navigationBarHeight + "px");
        android.util.Log.d("MainActivity", "  - 推荐padding: " + recommendedPadding + "px");
        android.util.Log.d("MainActivity", "  - 导航栏存在: " + navInfo.exists);
        android.util.Log.d("MainActivity", "  - 手势导航: " + isGestureNav);
        android.util.Log.d("MainActivity", "  - 导航栏类型: " + navInfo.type);
        android.util.Log.d("MainActivity", "  - 布局参数类型: " + layoutParams.getClass().getSimpleName());

        // 验证适配结果
        com.android.video.utils.NavigationBarUtils.NavigationBarValidationResult validationResult =
            com.android.video.utils.NavigationBarUtils.validateNavigationBarAdaptation(this);
        android.util.Log.d("MainActivity", "🔍 适配验证结果: " + validationResult.toString());

        // 最终的margin验证 - 使用安全的方式获取
        ViewGroup.LayoutParams finalParams = bottomNavigationView.getLayoutParams();
        if (finalParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
            int finalMargin = ((androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) finalParams).bottomMargin;
            android.util.Log.d("MainActivity", "🎯 最终bottomMargin值: " + finalMargin + "px");
        } else if (finalParams instanceof ViewGroup.MarginLayoutParams) {
            int finalMargin = ((ViewGroup.MarginLayoutParams) finalParams).bottomMargin;
            android.util.Log.d("MainActivity", "🎯 最终bottomMargin值(通用): " + finalMargin + "px");
        }

        android.util.Log.d("MainActivity", "=== 导航栏适配调试完成 ===");
    }

    /**
     * 改进的手势导航检测逻辑
     * 结合多种检测方法提高准确性
     */
    private boolean isImprovedGestureNavigation(androidx.core.view.WindowInsetsCompat insets) {
        if (insets == null) return false;

        androidx.core.graphics.Insets navBars = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.navigationBars());
        androidx.core.graphics.Insets tappable = insets.getInsets(androidx.core.view.WindowInsetsCompat.Type.tappableElement());

        // 方法1：比较navigationBars和tappableElement的高度差
        boolean method1 = navBars.bottom > 0 && navBars.bottom > tappable.bottom;

        // 方法2：检查tappableElement的高度是否很小（通常手势导航的可点击区域很小）
        boolean method2 = navBars.bottom > 0 && tappable.bottom <= 24; // 24dp通常是手势导航的阈值

        // 方法3：使用NavigationBarUtils的检测结果
        com.android.video.utils.NavigationBarUtils.NavigationBarInfo navInfo =
            com.android.video.utils.NavigationBarUtils.getNavigationBarInfo(this);
        boolean method3 = navInfo.isGestureNavigation;

        // 综合判断：至少两种方法认为是手势导航
        int gestureCount = (method1 ? 1 : 0) + (method2 ? 1 : 0) + (method3 ? 1 : 0);
        boolean isGesture = gestureCount >= 2;

        android.util.Log.d("MainActivity", "手势导航检测 - 方法1: " + method1 +
            ", 方法2: " + method2 + ", 方法3: " + method3 + ", 最终结果: " + isGesture);

        return isGesture;
    }

    /**
     * 回退方案：当主要适配逻辑出现异常时使用
     */
    private void applyFallbackNavigationBarAdaptation(int navigationBarHeight,
                                                    boolean hasNavigationBar,
                                                    boolean isGestureNavigation) {
        if (bottomNavigationView == null || !hasNavigationBar) return;

        try {
            ViewGroup.LayoutParams layoutParams = bottomNavigationView.getLayoutParams();
            if (layoutParams instanceof ViewGroup.MarginLayoutParams) {
                ViewGroup.MarginLayoutParams marginParams = (ViewGroup.MarginLayoutParams) layoutParams;

                if (!isGestureNavigation) {
                    marginParams.bottomMargin = navigationBarHeight;
                } else {
                    // 手势导航回退方案：使用导航栏高度的一半，确保底部导航栏在手势导航栏上方
                    int gestureNavMargin = Math.max(navigationBarHeight / 2, 24);
                    marginParams.bottomMargin = Math.min(gestureNavMargin, 48);
                }

                bottomNavigationView.setLayoutParams(marginParams);
                android.util.Log.d("MainActivity", "使用回退适配方案");
            }
        } catch (Exception e) {
            android.util.Log.e("MainActivity", "回退适配方案也失败", e);
        }
    }

    /**
     * 执行初始验证
     */
    private void performInitialValidation() {
        android.util.Log.d("MainActivity", "📋 准备执行初始验证...");

        // 延迟执行验证，确保布局已完成
        findViewById(android.R.id.content).post(() -> {
            try {
                // 输出系统信息
                TestValidationUtils.debugSystemInfo(this);

                // 验证导航栏适配
                TestValidationUtils.TestResult navResult =
                    TestValidationUtils.validateMainActivityNavigationBar(this);

                android.util.Log.i("MainActivity", "初始验证完成 - " + navResult.toString());

                // 初始化Token管理器（替代原来的token配置验证）
                initializeTokenManager();

                // 执行token诊断（帮助发现问题）
                performTokenDiagnosis();

                // 验证token配置（保留用于调试）
                validateTokenConfiguration();

                // 初始化设备接口已在Application启动时调用，这里不再重复调用
                android.util.Log.d("MainActivity", "ℹ️ 初始化设备接口已在Application启动时调用");

                // 添加额外的延迟验证，确保WindowInsets处理完成
                findViewById(android.R.id.content).postDelayed(() -> {
                    android.util.Log.d("MainActivity", "🔄 执行延迟验证...");
                    TestValidationUtils.TestResult delayedResult =
                        TestValidationUtils.validateMainActivityNavigationBar(this);
                    android.util.Log.i("MainActivity", "延迟验证完成 - " + delayedResult.toString());
                }, 1000); // 1秒后再次验证

            } catch (Exception e) {
                android.util.Log.e("MainActivity", "初始验证异常", e);
            }
        });
    }

    /**
     * 初始化网络组件
     */
    private void initializeNetworkComponents() {
        try {
            // 初始化ApiClientUtils（如果尚未初始化）
            ApiClientUtils.initialize(this);

            // 初始化ApiHeaderUtils
            com.android.video.utils.ApiHeaderUtils.initialize(this);

            android.util.Log.d("MainActivity", "✅ Network components initialized successfully");
        } catch (Exception e) {
            android.util.Log.e("MainActivity", "❌ Failed to initialize network components", e);
        }
    }

    /**
     * 初始化Token管理器
     */
    private void initializeTokenManager() {
        android.util.Log.d("MainActivity", "🔧 开始初始化Token管理器...");

        com.android.video.manager.TokenManager tokenManager =
            com.android.video.manager.TokenManager.getInstance(this);

        tokenManager.initialize(new com.android.video.manager.TokenManager.TokenInitCallback() {
            @Override
            public void onInitialized(boolean success, String message) {
                if (success) {
                    android.util.Log.d("MainActivity", "✅ Token管理器初始化成功: " + message);
                    // 验证token是否正确设置到ApiHeaderUtils
                    String currentToken = com.android.video.utils.ApiHeaderUtils.getCurrentAccessToken();
                    if (currentToken != null && !currentToken.trim().isEmpty()) {
                        android.util.Log.d("MainActivity", "✅ Token已正确设置到ApiHeaderUtils");
                    } else {
                        android.util.Log.w("MainActivity", "⚠️ Token未正确设置到ApiHeaderUtils，尝试重新设置");
                        retryTokenInitialization();
                    }
                } else {
                    android.util.Log.e("MainActivity", "❌ Token管理器初始化失败: " + message);
                    // 延迟重试
                    retryTokenInitialization();
                }
            }
        });
    }

    /**
     * 重试Token初始化
     */
    private void retryTokenInitialization() {
        android.util.Log.d("MainActivity", "🔄 延迟3秒后重试Token初始化...");
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            android.util.Log.d("MainActivity", "🔄 开始重试Token初始化...");
            initializeTokenManager();
        }, 3000);
    }

    /**
     * 执行token诊断
     */
    private void performTokenDiagnosis() {
        android.util.Log.d("MainActivity", "🔍 开始执行Token诊断...");

        // 延迟执行诊断，确保TokenManager初始化完成
        new android.os.Handler(android.os.Looper.getMainLooper()).postDelayed(() -> {
            com.android.video.utils.TokenDiagnosticUtils.performFullDiagnosis(this);
        }, 1000);
    }

    /**
     * 验证token配置（保留用于调试）
     */
    private void validateTokenConfiguration() {
        android.util.Log.d("MainActivity", "🔧 开始验证Token配置...");
        TokenConfigUtils.validateTokenConfiguration();
        // 注释掉硬编码token设置
        // TokenConfigUtils.setTokenForCurrentEnvironment();
    }

    /**
     * 测试新的初始化设备API接口
     */
    private void testInitDeviceApi() {
        android.util.Log.d("MainActivity", "🚀 开始测试初始化设备API接口...");

        AuthApiUtils.initDevice(this, new AuthApiUtils.ApiCallback<InitDeviceResponseModel>() {
            @Override
            public void onSuccess(InitDeviceResponseModel response) {
                android.util.Log.i("MainActivity", "✅ 初始化设备API调用成功!");
                android.util.Log.d("MainActivity", "设备初始化响应: " + response.toString());

                // 如果用户已登录，保存用户会话
                if (response.isUserLoggedIn()) {
                    android.util.Log.i("MainActivity", "👤 检测到用户已登录，保存用户会话");
                    UserSessionUtils.saveUserSessionFromInitResponse(MainActivity.this, response);
                } else {
                    android.util.Log.i("MainActivity", "👤 用户未登录，仅保存设备信息");
                }
            }

            @Override
            public void onError(String error) {
                android.util.Log.e("MainActivity", "❌ 初始化设备API调用失败: " + error);
                // 在生产环境中，这里可以显示一个友好的错误提示
                // Toast.makeText(MainActivity.this, "设备初始化失败，请检查网络连接", Toast.LENGTH_SHORT).show();
            }
        });
    }

    /**
     * 验证配置变化后的适配效果
     */
    private void validateConfigurationChangeAdaptation() {
        try {
            android.content.res.Configuration config = getResources().getConfiguration();
            String orientation = config.orientation == android.content.res.Configuration.ORIENTATION_LANDSCAPE ? "横屏" : "竖屏";

            TestValidationUtils.TestResult configResult =
                TestValidationUtils.validateConfigurationChange(this, orientation);

            android.util.Log.i("MainActivity", "配置变化验证 - " + configResult.toString());

        } catch (Exception e) {
            android.util.Log.e("MainActivity", "配置变化验证异常", e);
        }
    }

    /**
     * 处理语言切换后的导航
     */
    private void handleLanguageChangeNavigation() {
        Intent intent = getIntent();
        if (intent != null && intent.getBooleanExtra("navigate_to_home", false)) {
            // 确保导航到首页并设置正确的选中状态
            if (bottomNavigationView != null) {
                bottomNavigationView.setSelectedItemId(R.id.nav_home);
            }

            // 加载首页Fragment
            if (homeFragment == null) {
                homeFragment = new HomeFragment();
            }
            loadFragment(homeFragment);

            Log.d(TAG, "语言切换后导航到首页");
        }
    }

    /**
     * 切换到我的片单页面并显示观看历史
     */
    public void switchToMyListWithHistory() {
        // 切换到MyList标签页
        bottomNavigationView.setSelectedItemId(R.id.nav_my_list);

        // 如果MyListFragment已经创建，切换到观看历史标签
        if (myListFragment != null) {
            myListFragment.switchToHistoryTab();
        }
    }

    @Override
    public void onBackPressed() {
        if (doubleBackToExitPressedOnce) {
            // 第二次按返回键，退出应用
            super.onBackPressed();
            return;
        }

        // 第一次按返回键，显示提示
        this.doubleBackToExitPressedOnce = true;
        Toast.makeText(this, "Press back again to exit to desktop", Toast.LENGTH_SHORT).show();

        // 2秒后重置标志
        exitHandler.postDelayed(new Runnable() {
            @Override
            public void run() {
                doubleBackToExitPressedOnce = false;
            }
        }, 2000);
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理Handler回调
        if (exitHandler != null) {
            exitHandler.removeCallbacksAndMessages(null);
        }

        // 关闭缓存预加载管理器
        CachePreloadManager.getInstance(this).shutdown();

        // 关闭缓存监控
        CacheMonitor.getInstance(this).cleanup();

        // 打印Fragment缓存状态调试信息
        if (fragmentCacheManager != null) {
            fragmentCacheManager.debugPrintAllStates();
        }
    }

    /**
     * 初始化缓存系统
     */
    private void initializeCacheSystem() {
        try {
            // 初始化API客户端（包含缓存拦截器）
            ApiClientUtils.initialize(this);

            // 初始化高级缓存管理器（自动清理和空间管理）
            AdvancedCacheManager.getInstance(this);

            // 初始化智能缓存管理器
            SmartCacheManager.getInstance(this);

            Log.d(TAG, "Enhanced cache system initialized successfully");

            // 在调试模式下运行缓存测试
            if (EnvironmentConfigUtils.isDebugMode()) {
                new Thread(() -> {
                    try {
                        Thread.sleep(2000); // 等待2秒让系统完全初始化
                        CacheTestUtils.runFullCacheTest(this);

                        // 运行新的缓存验证
                        Thread.sleep(1000);
                        CacheValidationUtils.runFullCacheValidation(this);

                        // 测试高级缓存管理功能
                        Thread.sleep(1000);
                        testAdvancedCacheFeatures();

                    } catch (InterruptedException e) {
                        Log.e(TAG, "Cache test interrupted", e);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }).start();
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize cache system", e);
        }
    }

    /**
     * 启动缓存预加载
     */
    private void startCachePreloading() {
        try {
            CachePreloadManager preloadManager = CachePreloadManager.getInstance(this);

            // 启动预加载（带回调）
            preloadManager.startPreloadingWithCallback(new CachePreloadManager.PreloadCallback() {
                @Override
                public void onPreloadStarted() {
                    Log.d(TAG, "Cache preloading started");
                }

                @Override
                public void onPreloadProgress(int current, int total) {
                    Log.d(TAG, "Cache preloading progress: " + current + "/" + total);
                }

                @Override
                public void onPreloadCompleted() {
                    Log.d(TAG, "Cache preloading completed");
                    runOnUiThread(() -> {
                        // 可以在这里显示预加载完成的提示或更新UI
                    });
                }

                @Override
                public void onPreloadError(String error) {
                    Log.e(TAG, "Cache preloading error: " + error);
                }
            });

            // 启动缓存监控（仅在调试模式下）
            if (EnvironmentConfigUtils.isDebugMode()) {
                CacheMonitor cacheMonitor = CacheMonitor.getInstance(this);
                cacheMonitor.setListener(new CacheMonitor.CacheMonitorListener() {
                    @Override
                    public void onCacheStatsUpdated(CacheMonitor.CacheStats stats) {
                        Log.d(TAG, "Cache stats updated: " + stats.toString());
                    }

                    @Override
                    public void onCacheWarning(String warning) {
                        Log.w(TAG, "Cache warning: " + warning);
                    }
                });
                cacheMonitor.startMonitoring();
            }

        } catch (Exception e) {
            Log.e(TAG, "Failed to start cache preloading", e);
        }
    }

    /**
     * 测试高级缓存管理功能（仅在调试模式下）
     */
    private void testAdvancedCacheFeatures() {
        try {
            Log.d(TAG, "开始测试高级缓存管理功能");

            SmartCacheManager smartCacheManager = SmartCacheManager.getInstance(this);

            // 获取缓存空间信息
            SmartCacheManager.CacheSpaceInfo spaceInfo = smartCacheManager.getCacheSpaceInfo();
            Log.d(TAG, "缓存空间信息: " + spaceInfo.toString());

            // 如果缓存使用率超过50%，执行一次清理测试
            if (spaceInfo.getUsagePercentage() > 50.0f) {
                Log.d(TAG, "缓存使用率较高，执行清理测试");
                smartCacheManager.performManualCleanup();

                // 重新获取清理后的信息
                SmartCacheManager.CacheSpaceInfo afterCleanup = smartCacheManager.getCacheSpaceInfo();
                Log.d(TAG, "清理后缓存空间信息: " + afterCleanup.toString());
            }

            Log.d(TAG, "高级缓存管理功能测试完成");

        } catch (Exception e) {
            Log.e(TAG, "高级缓存管理功能测试失败", e);
        }
    }

    /**
     * 检查应用版本更新
     */
    private void checkForAppUpdate() {
        Log.d(TAG, "开始检查应用版本更新...");

        // 延迟执行版本检查，避免影响应用启动速度
        findViewById(android.R.id.content).postDelayed(() -> {
            try {
                com.android.video.manager.VersionManager versionManager =
                    com.android.video.manager.VersionManager.getInstance(this);

                // 强制检查版本更新（忽略时间间隔限制）
                versionManager.checkForUpdate(new com.android.video.manager.VersionManager.VersionCheckListener() {
                    @Override
                    public void onNewVersionFound(com.android.video.model.VersionInfoModel versionInfo) {
                        Log.i(TAG, "发现新版本: " + versionInfo.getVersionCode());
                        // 显示更新弹窗（可选更新模式）
                        versionManager.showUpdateDialog(MainActivity.this, versionInfo);
                    }

                    @Override
                    public void onLatestVersion(String currentVersion) {
                        Log.d(TAG, "当前已是最新版本: " + currentVersion);
                    }

                    @Override
                    public void onCheckFailed(String error) {
                        Log.w(TAG, "版本检查失败: " + error);
                        // 版本检查失败时静默处理，不影响用户体验
                    }
                }, true); // 强制检查，忽略时间间隔限制

            } catch (Exception e) {
                Log.e(TAG, "版本检查异常", e);
            }
        }, 3000); // 延迟3秒执行，确保应用完全启动
    }

}
