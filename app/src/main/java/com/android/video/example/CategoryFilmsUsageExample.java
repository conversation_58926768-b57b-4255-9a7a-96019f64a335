package com.android.video.example;

import android.util.Log;
import com.android.video.network.HomeApiService;
import com.android.video.model.response.CategoryListResponseModel;
import com.android.video.model.response.CategoryWithFilmsModel;
import com.android.video.model.response.CategoryFilmModel;

import java.util.List;

/**
 * 分类短剧列表接口使用示例
 * <p>
 * 演示如何使用分类短剧列表API，包括分页查询、数据处理等功能。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class CategoryFilmsUsageExample {

    private static final String TAG = "CategoryFilmsUsageExample";
    
    private HomeApiService homeApiService;

    public CategoryFilmsUsageExample() {
        this.homeApiService = HomeApiService.getInstance();
    }

    /**
     * 示例1：获取所有分类的短剧列表（第一页）
     */
    public void getAllCategoriesFirstPage() {
        Log.d(TAG, "=== 示例1：获取所有分类的短剧列表（第一页） ===");
        
        homeApiService.getCategoriesWithFilms(null, 1, 10, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                Log.d(TAG, "✅ 获取所有分类成功");
                Log.d(TAG, "总记录数: " + response.getTotal());
                Log.d(TAG, "当前页: " + response.getCurrentPage());
                Log.d(TAG, "总页数: " + response.getTotalPages());
                
                List<CategoryWithFilmsModel> categories = response.getCategories();
                if (categories != null) {
                    Log.d(TAG, "分类数量: " + categories.size());
                    
                    for (CategoryWithFilmsModel category : categories) {
                        Log.d(TAG, "分类: " + category.getCategoryName() + 
                                  " (ID: " + category.getCategoryId() + ")");
                        
                        if (category.hasFilms()) {
                            Log.d(TAG, "  包含 " + category.getFilmCount() + " 部短剧");
                            
                            // 显示前3部短剧
                            List<CategoryFilmModel> films = category.getFilms();
                            for (int i = 0; i < Math.min(3, films.size()); i++) {
                                CategoryFilmModel film = films.get(i);
                                Log.d(TAG, "    - " + film.getFilmTitle() + 
                                          " (" + film.getLanguageTypeDescription() + ")");
                            }
                        } else {
                            Log.d(TAG, "  暂无短剧");
                        }
                    }
                }
                
                // 检查是否有下一页
                if (response.hasNextPage()) {
                    Log.d(TAG, "还有下一页数据可以加载");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "❌ 获取所有分类失败: " + errorMessage);
            }
        });
    }

    /**
     * 示例2：获取指定分类的短剧列表
     */
    public void getSpecificCategoryFilms() {
        Log.d(TAG, "=== 示例2：获取指定分类的短剧列表 ===");
        
        String categoryId = "b0ae69449594450e93118e4e4ed77a78"; // 喜剧2分类
        
        homeApiService.getCategoriesWithFilms(categoryId, 1, 10, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                Log.d(TAG, "✅ 获取指定分类成功");
                
                List<CategoryWithFilmsModel> categories = response.getCategories();
                if (categories != null && !categories.isEmpty()) {
                    CategoryWithFilmsModel category = categories.get(0);
                    Log.d(TAG, "分类名称: " + category.getCategoryName());
                    
                    if (category.hasFilms()) {
                        List<CategoryFilmModel> films = category.getFilms();
                        Log.d(TAG, "短剧列表:");
                        
                        for (CategoryFilmModel film : films) {
                            Log.d(TAG, "  📺 " + film.getFilmTitle());
                            Log.d(TAG, "     ID: " + film.getFilmId());
                            Log.d(TAG, "     语言: " + film.getLanguageTypeDescription());
                            Log.d(TAG, "     封面: " + (film.hasCover() ? "有" : "无"));
                            
                            if (film.getPlayNum() != null) {
                                Log.d(TAG, "     播放次数: " + film.getPlayNum());
                            }
                            
                            if (film.getTotalChaptersNum() != null) {
                                Log.d(TAG, "     总章节数: " + film.getTotalChaptersNum());
                            }
                        }
                    }
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "❌ 获取指定分类失败: " + errorMessage);
            }
        });
    }

    /**
     * 示例3：分页加载示例
     */
    public void loadNextPage() {
        Log.d(TAG, "=== 示例3：分页加载示例 ===");
        
        int currentPage = 2; // 假设当前是第2页
        
        homeApiService.getCategoriesWithFilms(null, currentPage, 5, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                Log.d(TAG, "✅ 加载第 " + currentPage + " 页成功");
                Log.d(TAG, "当前页: " + response.getCurrentPage() + "/" + response.getTotalPages());
                
                List<CategoryWithFilmsModel> categories = response.getCategories();
                if (categories != null) {
                    Log.d(TAG, "本页分类数量: " + categories.size());
                    
                    // 处理分页数据
                    for (CategoryWithFilmsModel category : categories) {
                        Log.d(TAG, "分类: " + category.getCategoryName() + 
                                  " - " + category.getStatusDescription());
                    }
                }
                
                // 判断分页状态
                if (response.getCurrentPage() == 1) {
                    Log.d(TAG, "这是第一页");
                }
                
                if (response.hasNextPage()) {
                    Log.d(TAG, "可以加载下一页");
                } else {
                    Log.d(TAG, "已经是最后一页");
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "❌ 分页加载失败: " + errorMessage);
            }
        });
    }

    /**
     * 示例4：数据过滤和处理
     */
    public void filterAndProcessData() {
        Log.d(TAG, "=== 示例4：数据过滤和处理 ===");
        
        homeApiService.getCategoriesWithFilms(null, 1, 20, new HomeApiService.CategoryWithFilmsCallback() {
            @Override
            public void onSuccess(CategoryListResponseModel response) {
                Log.d(TAG, "✅ 获取数据成功，开始过滤处理");
                
                List<CategoryWithFilmsModel> categories = response.getCategories();
                if (categories != null) {
                    // 统计信息
                    int totalCategories = categories.size();
                    int categoriesWithFilms = 0;
                    int totalFilms = 0;
                    int englishFilms = 0;
                    int russianFilms = 0;
                    
                    for (CategoryWithFilmsModel category : categories) {
                        if (category.hasFilms()) {
                            categoriesWithFilms++;
                            totalFilms += category.getFilmCount();
                            
                            // 按语言统计
                            List<CategoryFilmModel> englishList = category.getFilmsByLanguage(1);
                            List<CategoryFilmModel> russianList = category.getFilmsByLanguage(2);
                            
                            if (englishList != null) englishFilms += englishList.size();
                            if (russianList != null) russianFilms += russianList.size();
                        }
                    }
                    
                    // 输出统计结果
                    Log.d(TAG, "📊 数据统计:");
                    Log.d(TAG, "  总分类数: " + totalCategories);
                    Log.d(TAG, "  有短剧的分类: " + categoriesWithFilms);
                    Log.d(TAG, "  总短剧数: " + totalFilms);
                    Log.d(TAG, "  英语短剧: " + englishFilms);
                    Log.d(TAG, "  俄语短剧: " + russianFilms);
                    
                    // 找出短剧最多的分类
                    CategoryWithFilmsModel maxCategory = null;
                    int maxFilmCount = 0;
                    
                    for (CategoryWithFilmsModel category : categories) {
                        if (category.getFilmCount() > maxFilmCount) {
                            maxFilmCount = category.getFilmCount();
                            maxCategory = category;
                        }
                    }
                    
                    if (maxCategory != null) {
                        Log.d(TAG, "🏆 短剧最多的分类: " + maxCategory.getCategoryName() + 
                                  " (" + maxFilmCount + " 部)");
                    }
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "❌ 数据处理失败: " + errorMessage);
            }
        });
    }

    /**
     * 运行所有示例
     */
    public void runAllExamples() {
        Log.d(TAG, "🚀 开始运行分类短剧列表API示例");
        
        // 延迟执行，避免请求冲突
        getAllCategoriesFirstPage();
        
        // 可以根据需要启用其他示例
        // getSpecificCategoryFilms();
        // loadNextPage();
        // filterAndProcessData();
        
        Log.d(TAG, "✨ 所有示例已启动");
    }
}
