package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.android.video.network.SubscribeApiService;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.List;

/**
 * Coming Soon模块RecyclerView适配器
 * <AUTHOR>
 */
public class ComingSoonAdapter extends RecyclerView.Adapter<ComingSoonAdapter.ComingSoonViewHolder> {

    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    private OnSubscriptionChangeListener onSubscriptionChangeListener;

    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }

    /**
     * 订阅状态变化监听接口
     */
    public interface OnSubscriptionChangeListener {
        void onSubscriptionChanged(VideoModel video, int position, boolean isSubscribed);
    }

    /**
     * 构造函数
     */
    public ComingSoonAdapter() {
        this.videoList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public ComingSoonAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }

    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }

    /**
     * 设置订阅状态变化监听器
     * @param listener 订阅监听器
     */
    public void setOnSubscriptionChangeListener(OnSubscriptionChangeListener listener) {
        this.onSubscriptionChangeListener = listener;
    }

    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList != null ? newVideoList : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 更新指定位置的订阅状态
     * @param position 位置
     * @param isSubscribed 订阅状态
     */
    public void updateSubscriptionStatus(int position, boolean isSubscribed) {
        if (position >= 0 && position < videoList.size()) {
            videoList.get(position).setSubscribed(isSubscribed);
            notifyItemChanged(position);
        }
    }

    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }

    @NonNull
    @Override
    public ComingSoonViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_coming_soon_video, parent, false);
        return new ComingSoonViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull ComingSoonViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class ComingSoonViewHolder extends RecyclerView.ViewHolder {

        private ImageView ivVideoPoster;
        private TextView tvVideoTitle;
        private ConstraintLayout clNotifyButton;
        private ImageView ivNotifyIcon;
        private TextView tvNotifyText;

        public ComingSoonViewHolder(@NonNull View itemView) {
            super(itemView);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            clNotifyButton = itemView.findViewById(R.id.cl_notify_button);
            ivNotifyIcon = itemView.findViewById(R.id.iv_notify_icon);
            tvNotifyText = itemView.findViewById(R.id.tv_notify_text);
        }

        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }

            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .centerCrop()
                    .into(ivVideoPoster);
            } else {
                // 如果没有URL，使用默认图片
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());

            // 更新订阅状态UI
            updateSubscriptionUI(video.isSubscribed());

            // 设置视频点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                } else {
                    Toast.makeText(itemView.getContext(),
                            "Coming Soon视频点击: " + video.getDisplayTitle(),
                            Toast.LENGTH_SHORT).show();
                }
            });

            // 设置订阅按钮点击事件
            clNotifyButton.setOnClickListener(v -> {
                // 检查登录状态
                if (!com.android.video.utils.LoginRequiredUtils.checkLoginAndNavigate(itemView.getContext())) {
                    return;
                }

                // 防止重复点击
                clNotifyButton.setEnabled(false);

                // 获取短剧ID
                String filmId = video.getId();
                if (filmId == null || filmId.trim().isEmpty()) {
                    Toast.makeText(itemView.getContext(), "短剧ID无效", Toast.LENGTH_SHORT).show();
                    clNotifyButton.setEnabled(true);
                    return;
                }

                // 根据当前状态决定操作类型
                boolean currentlySubscribed = video.isSubscribed();
                SubscribeApiService subscribeService = SubscribeApiService.getInstance();

                SubscribeApiService.SubscribeCallback callback = new SubscribeApiService.SubscribeCallback() {
                    @Override
                    public void onSuccess(boolean isSubscribed, String message) {
                        // 更新视频模型状态
                        video.setSubscribed(isSubscribed);
                        // 更新UI
                        updateSubscriptionUI(isSubscribed);
                        // 重新启用按钮
                        clNotifyButton.setEnabled(true);

                        // 通知监听器
                        if (onSubscriptionChangeListener != null) {
                            onSubscriptionChangeListener.onSubscriptionChanged(video, position, isSubscribed);
                        } else {
                            String displayMessage = isSubscribed ? "已订阅: " : "取消订阅: ";
                            Toast.makeText(itemView.getContext(),
                                    displayMessage + video.getDisplayTitle(),
                                    Toast.LENGTH_SHORT).show();
                        }
                    }

                    @Override
                    public void onError(String errorMessage) {
                        // 重新启用按钮
                        clNotifyButton.setEnabled(true);
                        // 显示错误信息
                        Toast.makeText(itemView.getContext(),
                                "操作失败: " + errorMessage,
                                Toast.LENGTH_SHORT).show();
                    }
                };

                // 执行订阅或取消订阅操作
                if (currentlySubscribed) {
                    subscribeService.unsubscribe(filmId, callback);
                } else {
                    subscribeService.subscribe(filmId, callback);
                }
            });
        }

        /**
         * 更新订阅状态UI
         * @param isSubscribed 是否已订阅
         */
        private void updateSubscriptionUI(boolean isSubscribed) {
            // 设置按钮选中状态
            clNotifyButton.setSelected(isSubscribed);

            if (isSubscribed) {
                ivNotifyIcon.setImageResource(R.drawable.home_ic_notify_1);
                tvNotifyText.setText("Notified");
                tvNotifyText.setTextColor(0x80FFFFFF); // 半透明白色
            } else {
                ivNotifyIcon.setImageResource(R.drawable.home_ic_notify_0);
                tvNotifyText.setText("Notify me");
                tvNotifyText.setTextColor(0xFFFFFFFF); // 纯白色
            }
        }
    }
}
