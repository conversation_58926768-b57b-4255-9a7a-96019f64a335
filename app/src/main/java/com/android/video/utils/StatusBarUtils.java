package com.android.video.utils;

import android.app.Activity;
import android.content.Context;
import android.content.res.Resources;
import android.os.Build;
import android.util.Log;
import android.view.View;
import androidx.core.view.ViewCompat;
import androidx.core.view.WindowInsetsCompat;

/**
 * 状态栏工具类 - 提供状态栏相关的实用方法
 * 用于调试和验证状态栏渲染问题
 * 
 * <AUTHOR> Team
 */
public class StatusBarUtils {
    
    private static final String TAG = "StatusBarUtils";
    
    /**
     * 获取状态栏高度
     * @param context 上下文
     * @return 状态栏高度（像素）
     */
    public static int getStatusBarHeight(Context context) {
        int result = 0;
        int resourceId = context.getResources().getIdentifier("status_bar_height", "dimen", "android");
        if (resourceId > 0) {
            result = context.getResources().getDimensionPixelSize(resourceId);
        }
        return result;
    }
    
    /**
     * 通过WindowInsets获取状态栏高度
     * @param view 任意View
     * @return 状态栏高度（像素）
     */
    public static int getStatusBarHeightFromInsets(View view) {
        WindowInsetsCompat insets = ViewCompat.getRootWindowInsets(view);
        if (insets != null) {
            return insets.getInsets(WindowInsetsCompat.Type.systemBars()).top;
        }
        return 0;
    }
    
    /**
     * 检查当前Activity的状态栏配置
     * @param activity Activity实例
     */
    public static void debugStatusBarConfig(Activity activity) {
        if (activity == null) return;
        
        Log.d(TAG, "=== Status Bar Debug Info ===");
        Log.d(TAG, "Activity: " + activity.getClass().getSimpleName());
        Log.d(TAG, "Android Version: " + Build.VERSION.SDK_INT);
        
        // 检查Window配置
        int statusBarColor = activity.getWindow().getStatusBarColor();
        int navigationBarColor = activity.getWindow().getNavigationBarColor();
        
        Log.d(TAG, "Status Bar Color: " + String.format("#%08X", statusBarColor));
        Log.d(TAG, "Navigation Bar Color: " + String.format("#%08X", navigationBarColor));
        
        // 检查状态栏高度
        int statusBarHeight = getStatusBarHeight(activity);
        Log.d(TAG, "Status Bar Height (Resource): " + statusBarHeight + "px");
        
        // 检查根视图的WindowInsets
        View rootView = activity.findViewById(android.R.id.content);
        if (rootView != null) {
            int insetsHeight = getStatusBarHeightFromInsets(rootView);
            Log.d(TAG, "Status Bar Height (Insets): " + insetsHeight + "px");
            
            Log.d(TAG, "Root View Padding - Top: " + rootView.getPaddingTop() + 
                      ", Left: " + rootView.getPaddingLeft() + 
                      ", Right: " + rootView.getPaddingRight() + 
                      ", Bottom: " + rootView.getPaddingBottom());
        }
        
        Log.d(TAG, "=== End Debug Info ===");
    }
    
    /**
     * 检查是否为全屏模式
     * @param activity Activity实例
     * @return true如果是全屏模式
     */
    public static boolean isFullScreenMode(Activity activity) {
        if (activity == null) return false;
        
        View decorView = activity.getWindow().getDecorView();
        int uiOptions = decorView.getSystemUiVisibility();
        
        return (uiOptions & View.SYSTEM_UI_FLAG_FULLSCREEN) != 0;
    }
    
    /**
     * 获取屏幕密度信息
     * @param context 上下文
     * @return 密度信息字符串
     */
    public static String getScreenDensityInfo(Context context) {
        Resources resources = context.getResources();
        float density = resources.getDisplayMetrics().density;
        int densityDpi = resources.getDisplayMetrics().densityDpi;
        
        String densityName;
        if (densityDpi <= 120) {
            densityName = "ldpi";
        } else if (densityDpi <= 160) {
            densityName = "mdpi";
        } else if (densityDpi <= 240) {
            densityName = "hdpi";
        } else if (densityDpi <= 320) {
            densityName = "xhdpi";
        } else if (densityDpi <= 480) {
            densityName = "xxhdpi";
        } else {
            densityName = "xxxhdpi";
        }
        
        return String.format("Density: %.2f, DPI: %d (%s)", density, densityDpi, densityName);
    }
    
    /**
     * 验证状态栏渲染是否正常
     * @param activity Activity实例
     * @return 验证结果
     */
    public static StatusBarValidationResult validateStatusBarRendering(Activity activity) {
        StatusBarValidationResult result = new StatusBarValidationResult();
        
        if (activity == null) {
            result.isValid = false;
            result.issues.add("Activity is null");
            return result;
        }
        
        // 检查状态栏颜色是否透明
        int statusBarColor = activity.getWindow().getStatusBarColor();
        if (statusBarColor != android.graphics.Color.TRANSPARENT) {
            result.issues.add("Status bar color is not transparent: " + String.format("#%08X", statusBarColor));
        }
        
        // 检查导航栏颜色是否透明
        int navigationBarColor = activity.getWindow().getNavigationBarColor();
        if (navigationBarColor != android.graphics.Color.TRANSPARENT) {
            result.issues.add("Navigation bar color is not transparent: " + String.format("#%08X", navigationBarColor));
        }
        
        // 检查根视图是否有不必要的padding
        View rootView = activity.findViewById(android.R.id.content);
        if (rootView != null && rootView.getPaddingTop() > 0) {
            result.warnings.add("Root view has top padding: " + rootView.getPaddingTop() + "px (may cause content offset)");
        }
        
        result.isValid = result.issues.isEmpty();
        return result;
    }
    
    /**
     * 状态栏验证结果类
     */
    public static class StatusBarValidationResult {
        public boolean isValid = true;
        public java.util.List<String> issues = new java.util.ArrayList<>();
        public java.util.List<String> warnings = new java.util.ArrayList<>();
        
        @Override
        public String toString() {
            StringBuilder sb = new StringBuilder();
            sb.append("Status Bar Validation Result:\n");
            sb.append("Valid: ").append(isValid).append("\n");
            
            if (!issues.isEmpty()) {
                sb.append("Issues:\n");
                for (String issue : issues) {
                    sb.append("  - ").append(issue).append("\n");
                }
            }
            
            if (!warnings.isEmpty()) {
                sb.append("Warnings:\n");
                for (String warning : warnings) {
                    sb.append("  - ").append(warning).append("\n");
                }
            }
            
            return sb.toString();
        }
    }
}
