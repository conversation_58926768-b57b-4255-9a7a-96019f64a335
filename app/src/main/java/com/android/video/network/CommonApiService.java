package com.android.video.network;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.CommonApiConstantsUtils;
import com.android.video.utils.ApiHeaderUtils;

import org.json.JSONException;
import org.json.JSONObject;

import java.io.File;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.MultipartBody;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 公共API服务类
 * <p>
 * 提供公共API相关的网络请求功能，包括文件上传等。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see CommonApiConstantsUtils
 */
public class CommonApiService {

    private static final String TAG = "CommonApiService";

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;

    /**
     * 文件上传回调接口
     */
    public interface FileUploadCallback {
        /**
         * 文件上传成功
         * @param fileUrl 上传成功后的文件URL
         */
        void onSuccess(String fileUrl);

        /**
         * 文件上传失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 构造方法
     */
    public CommonApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }

    /**
     * 上传文件
     * <p>
     * 异步上传文件到服务器，通常用于上传头像图片。
     * </p>
     *
     * @param file 要上传的文件
     * @param callback 回调接口
     */
    public void uploadFile(File file, FileUploadCallback callback) {
        // 参数验证
        if (file == null || !file.exists()) {
            if (callback != null) {
                callback.onError("文件不存在或无效");
            }
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            CommonApiConstantsUtils.API_UPLOAD_FILE
        );

        // 检查访问令牌是否有效
        String currentToken = ApiHeaderUtils.getCurrentAccessToken();
        Log.d(TAG, "当前访问令牌: " + currentToken);
        Log.d(TAG, "访问令牌是否有效: " + ApiHeaderUtils.isCurrentTokenValid());

        // 检查文件大小（限制为1MB，与服务器保持一致）
        long maxFileSize = 1024 * 1024; // 1MB = 1048576 bytes
        if (file.length() > maxFileSize) {
            if (callback != null) {
                callback.onError("文件大小超过限制（最大10MB），当前大小：" + (file.length() / 1024) + "KB");
            }
            return;
        }

        // 检查文件格式
        String fileName = file.getName().toLowerCase();
        if (!fileName.endsWith(".jpg") && !fileName.endsWith(".jpeg") &&
            !fileName.endsWith(".png") && !fileName.endsWith(".gif")) {
            if (callback != null) {
                callback.onError("不支持的文件格式，请选择 JPG、PNG 或 GIF 格式的图片");
            }
            return;
        }

        // 构建multipart请求体
        // 根据文件扩展名确定正确的MediaType
        MediaType mediaType = getMediaTypeFromFileName(fileName);
        RequestBody fileBody = RequestBody.create(file, mediaType);
        MultipartBody requestBody = new MultipartBody.Builder()
            .setType(MultipartBody.FORM)
            .addFormDataPart(CommonApiConstantsUtils.PARAM_FILE, file.getName(), fileBody)
            .build();

        // 构建POST请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 文件上传请求详情 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "文件名: " + file.getName());
        Log.d(TAG, "文件大小: " + file.length() + " bytes");
        Log.d(TAG, "文件路径: " + file.getAbsolutePath());
        Log.d(TAG, "文件是否存在: " + file.exists());
        Log.d(TAG, "文件是否可读: " + file.canRead());
        Log.d(TAG, "MediaType: " + mediaType.toString());
        Log.d(TAG, "Form-data参数名: " + CommonApiConstantsUtils.PARAM_FILE);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "========================");

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "文件上传请求失败", e);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("网络请求失败: " + e.getMessage());
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "=== 文件上传API响应 ===");
                    Log.d(TAG, "HTTP状态码: " + response.code());
                    Log.d(TAG, "响应体长度: " + responseBody.length());
                    Log.d(TAG, "响应内容: " + responseBody);
                    Log.d(TAG, "==================");

                    if (response.isSuccessful()) {
                        // 解析响应数据
                        parseFileUploadResponse(responseBody, callback);
                    } else {
                        // 提供更详细的错误信息
                        String errorMessage = "服务器错误: " + response.code();
                        if (response.code() == 500) {
                            errorMessage += " (服务器内部错误，请检查文件格式和大小)";
                        } else if (response.code() == 413) {
                            errorMessage += " (文件过大)";
                        } else if (response.code() == 415) {
                            errorMessage += " (不支持的文件类型)";
                        } else if (response.code() == 401) {
                            errorMessage += " (认证失败，请重新登录)";
                        }

                        final String finalErrorMessage = errorMessage;
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError(finalErrorMessage);
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "文件上传响应处理失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应处理失败: " + e.getMessage());
                        }
                    });
                }
            }
        });
    }

    /**
     * 解析文件上传API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseFileUploadResponse(String responseBody, FileUploadCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "文件上传API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "文件上传失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取文件URL - data字段直接是URL字符串
            String fileUrl = jsonResponse.optString(CommonApiConstantsUtils.FIELD_FILE_URL, "");
            if (fileUrl.isEmpty()) {
                Log.e(TAG, "文件上传响应中没有data字段或data为空");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("文件URL获取失败");
                    }
                });
                return;
            }

            // 验证URL格式
            if (!CommonApiConstantsUtils.isValidFileUrl(fileUrl)) {
                Log.e(TAG, "返回的文件URL格式无效: " + fileUrl);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("返回的文件URL格式无效");
                    }
                });
                return;
            }

            Log.d(TAG, "文件上传成功: " + fileUrl);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(fileUrl);
                }
            });

        } catch (JSONException e) {
            Log.e(TAG, "文件上传JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 根据文件名获取正确的MediaType
     *
     * @param fileName 文件名
     * @return 对应的MediaType
     */
    private MediaType getMediaTypeFromFileName(String fileName) {
        String lowerFileName = fileName.toLowerCase();

        if (lowerFileName.endsWith(".jpg") || lowerFileName.endsWith(".jpeg")) {
            return MediaType.parse("image/jpeg");
        } else if (lowerFileName.endsWith(".png")) {
            return MediaType.parse("image/png");
        } else if (lowerFileName.endsWith(".gif")) {
            return MediaType.parse("image/gif");
        } else if (lowerFileName.endsWith(".bmp")) {
            return MediaType.parse("image/bmp");
        } else if (lowerFileName.endsWith(".webp")) {
            return MediaType.parse("image/webp");
        } else {
            // 默认使用 image/jpeg
            return MediaType.parse("image/jpeg");
        }
    }

    /**
     * 获取单例实例
     */
    private static CommonApiService instance;

    public static synchronized CommonApiService getInstance() {
        if (instance == null) {
            instance = new CommonApiService();
        }
        return instance;
    }
}
