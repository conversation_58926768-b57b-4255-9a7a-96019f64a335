package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import com.android.video.constants.VipApiConstantsUtils;

/**
 * VIP购买响应数据模型
 * <p>
 * 用于映射VIP购买API返回的响应数据结构。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class VipPurchaseResponse {

    /**
     * 响应码
     */
    @SerializedName("code")
    private String code;

    /**
     * 响应消息
     */
    @SerializedName("message")
    private String message;

    /**
     * 响应数据
     */
    @SerializedName("data")
    private VipPurchaseData data;

    // ========== 构造函数 ==========

    /**
     * 默认构造函数
     */
    public VipPurchaseResponse() {
    }

    /**
     * 完整构造函数
     */
    public VipPurchaseResponse(String code, String message, VipPurchaseData data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    // ========== Getter和Setter方法 ==========

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public VipPurchaseData getData() {
        return data;
    }

    public void setData(VipPurchaseData data) {
        this.data = data;
    }

    // ========== 业务方法 ==========

    /**
     * 检查响应是否成功
     * @return 如果响应码为"200"则返回true，否则返回false
     */
    public boolean isSuccess() {
        return "200".equals(code);
    }

    /**
     * 获取订单号
     * @return 订单号，如果数据为空则返回null
     */
    public String getOrderNo() {
        return data != null ? data.getOrderNo() : null;
    }

    /**
     * 获取Google Pay参数
     * @return Google Pay参数，如果数据为空则返回null
     */
    public GooglePayParams getGooglePayParams() {
        return data != null ? data.getGooglePayParams() : null;
    }

    /**
     * 检查是否有Google Pay参数
     * @return 如果有Google Pay参数则返回true，否则返回false
     */
    public boolean hasGooglePayParams() {
        GooglePayParams params = getGooglePayParams();
        return params != null && params.isValid();
    }

    /**
     * 获取错误信息
     * @return 错误信息
     */
    public String getErrorMessage() {
        if (isSuccess()) {
            return null;
        }
        return message != null ? message : "Unknown error";
    }

    /**
     * 检查是否有OneVision支付URL
     * @return 如果有OneVision支付URL则返回true，否则返回false
     */
    public boolean hasOnevisionPaymentUrl() {
        return data != null && data.getOnevisionPaymentPageUrl() != null &&
               !data.getOnevisionPaymentPageUrl().trim().isEmpty();
    }

    /**
     * 获取OneVision支付页面URL
     * @return OneVision支付页面URL，如果数据为空则返回null
     */
    public String getOnevisionPaymentPageUrl() {
        return data != null ? data.getOnevisionPaymentPageUrl() : null;
    }

    @Override
    public String toString() {
        return "VipPurchaseResponse{" +
                "code='" + code + '\'' +
                ", message='" + message + '\'' +
                ", data=" + data +
                '}';
    }

    /**
     * VIP购买数据内部类
     */
    public static class VipPurchaseData {

        /**
         * 订单号
         */
        @SerializedName(VipApiConstantsUtils.FIELD_ORDER_NO)
        private String orderNo;

        /**
         * Google支付参数
         */
        @SerializedName(VipApiConstantsUtils.FIELD_GOOGLE_PAY_PARAMS)
        private GooglePayParams googlePayParams;

        /**
         * Apple支付参数
         */
        @SerializedName(VipApiConstantsUtils.FIELD_APPLE_PAY_PARAMS)
        private Object applePayParams;

        /**
         * 支付状态
         */
        @SerializedName(VipApiConstantsUtils.FIELD_PAY_STATUS)
        private int payStatus;

        /**
         * 创建时间
         */
        @SerializedName(VipApiConstantsUtils.FIELD_CREATE_TIME)
        private String createTime;

        /**
         * OneVision支付页面URL
         */
        @SerializedName("onevisionPaymentPageUrl")
        private String onevisionPaymentPageUrl;

        // ========== 构造函数 ==========

        public VipPurchaseData() {
        }

        // ========== Getter和Setter方法 ==========

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public GooglePayParams getGooglePayParams() {
            return googlePayParams;
        }

        public void setGooglePayParams(GooglePayParams googlePayParams) {
            this.googlePayParams = googlePayParams;
        }

        public Object getApplePayParams() {
            return applePayParams;
        }

        public void setApplePayParams(Object applePayParams) {
            this.applePayParams = applePayParams;
        }

        public int getPayStatus() {
            return payStatus;
        }

        public void setPayStatus(int payStatus) {
            this.payStatus = payStatus;
        }

        public String getCreateTime() {
            return createTime;
        }

        public void setCreateTime(String createTime) {
            this.createTime = createTime;
        }

        public String getOnevisionPaymentPageUrl() {
            return onevisionPaymentPageUrl;
        }

        public void setOnevisionPaymentPageUrl(String onevisionPaymentPageUrl) {
            this.onevisionPaymentPageUrl = onevisionPaymentPageUrl;
        }

        @Override
        public String toString() {
            return "VipPurchaseData{" +
                    "orderNo='" + orderNo + '\'' +
                    ", googlePayParams=" + googlePayParams +
                    ", applePayParams=" + applePayParams +
                    ", payStatus=" + payStatus +
                    ", createTime='" + createTime + '\'' +
                    ", onevisionPaymentPageUrl='" + onevisionPaymentPageUrl + '\'' +
                    '}';
        }
    }
}
