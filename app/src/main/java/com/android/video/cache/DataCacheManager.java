package com.android.video.cache;

import android.content.Context;
import android.content.SharedPreferences;
import android.util.Log;
import android.util.LruCache;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;

import java.lang.reflect.Type;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 数据缓存管理器
 * 提供多层缓存策略：内存缓存 + 磁盘缓存
 * 支持过期时间和自动清理
 * <AUTHOR> Team
 */
public class DataCacheManager {
    
    private static final String TAG = "DataCacheManager";
    private static final String CACHE_PREFS_NAME = "data_cache_prefs";
    private static final String CACHE_TIMESTAMP_SUFFIX = "_timestamp";
    private static final String CACHE_EXPIRY_SUFFIX = "_expiry";
    
    // 默认缓存过期时间（1小时）
    private static final long DEFAULT_CACHE_EXPIRY = TimeUnit.HOURS.toMillis(1);
    
    // 内存缓存大小（最大缓存条目数）
    private static final int MEMORY_CACHE_SIZE = 100;
    
    private static DataCacheManager instance;
    private Context context;
    private SharedPreferences cachePrefs;
    private Gson gson;
    
    // 内存缓存 - 使用LRU策略
    private LruCache<String, CacheEntry> memoryCache;
    
    // 缓存过期时间映射
    private ConcurrentHashMap<String, Long> expiryMap;
    
    /**
     * 缓存条目类
     */
    private static class CacheEntry {
        Object data;
        long timestamp;
        long expiryTime;
        
        CacheEntry(Object data, long expiryTime) {
            this.data = data;
            this.timestamp = System.currentTimeMillis();
            this.expiryTime = expiryTime;
        }
        
        boolean isExpired() {
            return System.currentTimeMillis() - timestamp > expiryTime;
        }
    }
    
    private DataCacheManager(Context context) {
        this.context = context.getApplicationContext();
        this.cachePrefs = context.getSharedPreferences(CACHE_PREFS_NAME, Context.MODE_PRIVATE);
        this.gson = new Gson();
        this.expiryMap = new ConcurrentHashMap<>();
        
        // 初始化内存缓存
        initializeMemoryCache();
        
        Log.d(TAG, "DataCacheManager initialized");
    }
    
    public static synchronized DataCacheManager getInstance(Context context) {
        if (instance == null) {
            instance = new DataCacheManager(context);
        }
        return instance;
    }
    
    /**
     * 初始化内存缓存
     */
    private void initializeMemoryCache() {
        memoryCache = new LruCache<String, CacheEntry>(MEMORY_CACHE_SIZE) {
            @Override
            protected int sizeOf(String key, CacheEntry entry) {
                // 每个条目计为1个单位
                return 1;
            }
            
            @Override
            protected void entryRemoved(boolean evicted, String key, CacheEntry oldValue, CacheEntry newValue) {
                if (evicted) {
                    Log.d(TAG, "Memory cache entry evicted: " + key);
                }
            }
        };
    }
    
    /**
     * 缓存数据（使用默认过期时间）
     */
    public <T> void cacheData(String key, T data) {
        cacheData(key, data, DEFAULT_CACHE_EXPIRY);
    }
    
    /**
     * 缓存数据（指定过期时间）
     */
    public <T> void cacheData(String key, T data, long expiryTimeMs) {
        if (key == null || data == null) {
            Log.w(TAG, "Cannot cache null key or data");
            return;
        }
        
        try {
            // 存储到内存缓存
            CacheEntry entry = new CacheEntry(data, expiryTimeMs);
            memoryCache.put(key, entry);
            
            // 存储到磁盘缓存
            String jsonData = gson.toJson(data);
            long currentTime = System.currentTimeMillis();
            
            SharedPreferences.Editor editor = cachePrefs.edit();
            editor.putString(key, jsonData);
            editor.putLong(key + CACHE_TIMESTAMP_SUFFIX, currentTime);
            editor.putLong(key + CACHE_EXPIRY_SUFFIX, expiryTimeMs);
            editor.apply();
            
            // 记录过期时间
            expiryMap.put(key, expiryTimeMs);
            
            Log.d(TAG, "Data cached successfully: " + key);
        } catch (Exception e) {
            Log.e(TAG, "Failed to cache data for key: " + key, e);
        }
    }
    
    /**
     * 获取缓存数据
     */
    public <T> T getCachedData(String key, Class<T> clazz) {
        if (key == null || clazz == null) {
            return null;
        }
        
        try {
            // 首先检查内存缓存
            CacheEntry memoryEntry = memoryCache.get(key);
            if (memoryEntry != null && !memoryEntry.isExpired()) {
                Log.d(TAG, "Data retrieved from memory cache: " + key);
                return clazz.cast(memoryEntry.data);
            }
            
            // 检查磁盘缓存
            String jsonData = cachePrefs.getString(key, null);
            if (jsonData != null) {
                long timestamp = cachePrefs.getLong(key + CACHE_TIMESTAMP_SUFFIX, 0);
                long expiryTime = cachePrefs.getLong(key + CACHE_EXPIRY_SUFFIX, DEFAULT_CACHE_EXPIRY);
                
                // 检查是否过期
                if (System.currentTimeMillis() - timestamp <= expiryTime) {
                    T data = gson.fromJson(jsonData, clazz);
                    
                    // 重新加载到内存缓存
                    CacheEntry entry = new CacheEntry(data, expiryTime);
                    memoryCache.put(key, entry);
                    
                    Log.d(TAG, "Data retrieved from disk cache: " + key);
                    return data;
                } else {
                    // 过期数据，清理
                    clearCachedData(key);
                    Log.d(TAG, "Cached data expired and cleared: " + key);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to retrieve cached data for key: " + key, e);
        }
        
        return null;
    }
    
    /**
     * 获取缓存数据（支持泛型列表）
     */
    public <T> T getCachedData(String key, Type type) {
        if (key == null || type == null) {
            return null;
        }
        
        try {
            // 首先检查内存缓存
            CacheEntry memoryEntry = memoryCache.get(key);
            if (memoryEntry != null && !memoryEntry.isExpired()) {
                Log.d(TAG, "Data retrieved from memory cache: " + key);
                return (T) memoryEntry.data;
            }
            
            // 检查磁盘缓存
            String jsonData = cachePrefs.getString(key, null);
            if (jsonData != null) {
                long timestamp = cachePrefs.getLong(key + CACHE_TIMESTAMP_SUFFIX, 0);
                long expiryTime = cachePrefs.getLong(key + CACHE_EXPIRY_SUFFIX, DEFAULT_CACHE_EXPIRY);
                
                // 检查是否过期
                if (System.currentTimeMillis() - timestamp <= expiryTime) {
                    T data = gson.fromJson(jsonData, type);
                    
                    // 重新加载到内存缓存
                    CacheEntry entry = new CacheEntry(data, expiryTime);
                    memoryCache.put(key, entry);
                    
                    Log.d(TAG, "Data retrieved from disk cache: " + key);
                    return data;
                } else {
                    // 过期数据，清理
                    clearCachedData(key);
                    Log.d(TAG, "Cached data expired and cleared: " + key);
                }
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to retrieve cached data for key: " + key, e);
        }
        
        return null;
    }
    
    /**
     * 检查缓存是否存在且未过期
     */
    public boolean isCacheValid(String key) {
        if (key == null) {
            return false;
        }
        
        // 检查内存缓存
        CacheEntry memoryEntry = memoryCache.get(key);
        if (memoryEntry != null && !memoryEntry.isExpired()) {
            return true;
        }
        
        // 检查磁盘缓存
        String jsonData = cachePrefs.getString(key, null);
        if (jsonData != null) {
            long timestamp = cachePrefs.getLong(key + CACHE_TIMESTAMP_SUFFIX, 0);
            long expiryTime = cachePrefs.getLong(key + CACHE_EXPIRY_SUFFIX, DEFAULT_CACHE_EXPIRY);
            
            return System.currentTimeMillis() - timestamp <= expiryTime;
        }
        
        return false;
    }
    
    /**
     * 清除指定缓存
     */
    public void clearCachedData(String key) {
        if (key == null) {
            return;
        }
        
        // 清除内存缓存
        memoryCache.remove(key);
        
        // 清除磁盘缓存
        SharedPreferences.Editor editor = cachePrefs.edit();
        editor.remove(key);
        editor.remove(key + CACHE_TIMESTAMP_SUFFIX);
        editor.remove(key + CACHE_EXPIRY_SUFFIX);
        editor.apply();
        
        // 清除过期时间映射
        expiryMap.remove(key);
        
        Log.d(TAG, "Cache cleared for key: " + key);
    }
    
    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        // 清除内存缓存
        memoryCache.evictAll();
        
        // 清除磁盘缓存
        SharedPreferences.Editor editor = cachePrefs.edit();
        editor.clear();
        editor.apply();
        
        // 清除过期时间映射
        expiryMap.clear();
        
        Log.d(TAG, "All cache cleared");
    }
    
    /**
     * 清理过期缓存
     */
    public void cleanupExpiredCache() {
        Log.d(TAG, "Starting cleanup of expired cache");
        
        // 清理内存缓存中的过期条目
        memoryCache.evictAll(); // LRU会自动处理，这里强制清理
        
        // 清理磁盘缓存中的过期条目
        SharedPreferences.Editor editor = cachePrefs.edit();
        boolean hasChanges = false;
        
        for (String key : cachePrefs.getAll().keySet()) {
            if (!key.endsWith(CACHE_TIMESTAMP_SUFFIX) && !key.endsWith(CACHE_EXPIRY_SUFFIX)) {
                long timestamp = cachePrefs.getLong(key + CACHE_TIMESTAMP_SUFFIX, 0);
                long expiryTime = cachePrefs.getLong(key + CACHE_EXPIRY_SUFFIX, DEFAULT_CACHE_EXPIRY);
                
                if (System.currentTimeMillis() - timestamp > expiryTime) {
                    editor.remove(key);
                    editor.remove(key + CACHE_TIMESTAMP_SUFFIX);
                    editor.remove(key + CACHE_EXPIRY_SUFFIX);
                    expiryMap.remove(key);
                    hasChanges = true;
                    Log.d(TAG, "Expired cache entry removed: " + key);
                }
            }
        }
        
        if (hasChanges) {
            editor.apply();
        }
        
        Log.d(TAG, "Cache cleanup completed");
    }
    
    /**
     * 获取缓存统计信息
     */
    public CacheStats getCacheStats() {
        int memorySize = memoryCache.size();
        int diskSize = 0;
        
        for (String key : cachePrefs.getAll().keySet()) {
            if (!key.endsWith(CACHE_TIMESTAMP_SUFFIX) && !key.endsWith(CACHE_EXPIRY_SUFFIX)) {
                diskSize++;
            }
        }
        
        return new CacheStats(memorySize, diskSize);
    }
    
    /**
     * 缓存统计信息类
     */
    public static class CacheStats {
        public final int memoryCacheSize;
        public final int diskCacheSize;
        
        CacheStats(int memoryCacheSize, int diskCacheSize) {
            this.memoryCacheSize = memoryCacheSize;
            this.diskCacheSize = diskCacheSize;
        }
        
        @Override
        public String toString() {
            return "CacheStats{" +
                    "memoryCacheSize=" + memoryCacheSize +
                    ", diskCacheSize=" + diskCacheSize +
                    '}';
        }
    }
}
