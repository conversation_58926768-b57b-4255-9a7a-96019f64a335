<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/black">

    <!-- 顶部阴影背景 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="160dp"
        android:layout_gravity="top"
        android:background="@drawable/top_shadow_gradient" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

    <!-- 顶部标题栏 -->
    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingStart="@dimen/bill_header_padding_horizontal"
        android:paddingEnd="@dimen/bill_header_padding_horizontal"
        android:paddingTop="@dimen/bill_header_padding_top">

        <!-- 返回按钮 -->
        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="@dimen/bill_back_button_size"
            android:layout_height="@dimen/bill_back_button_size"
            android:layout_alignParentStart="true"
            android:layout_marginTop="@dimen/bill_back_button_margin_top"
            android:layout_marginStart="@dimen/bill_back_button_margin_start"
            android:src="@drawable/login_btn_back"
            android:background="?android:attr/selectableItemBackgroundBorderless"
            android:clickable="true"
            android:focusable="true" />

        <!-- 标题 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerHorizontal="true"
            android:layout_marginTop="@dimen/bill_title_margin_top"
            android:text="Feedback"
            android:textColor="#ffffffff"
            android:textSize="@dimen/bill_title_text_size"
            android:fontFamily="sans-serif"
            android:gravity="center" />

    </RelativeLayout>

    <!-- 滚动内容 -->
    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="32dp">

            <!-- Feedback label -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <!-- 必填星号 -->
                <TextView
                    android:layout_width="@dimen/feedback_required_star_width"
                    android:layout_height="@dimen/feedback_required_star_height"
                    android:text="*"
                    android:textColor="@color/feedback_required_star_color"
                    android:textSize="16sp"
                    android:gravity="start" />

                <!-- 标签文本 -->
                <TextView
                    android:layout_width="@dimen/feedback_label_width"
                    android:layout_height="@dimen/feedback_label_height"
                    android:layout_marginStart="4dp"
                    android:text="Feedback label"
                    android:textColor="@color/feedback_label_text_color"
                    android:textSize="16sp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 反馈类型选择 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_feedback_types"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="@dimen/feedback_item_margin_vertical"
                android:orientation="horizontal" />

            <!-- Problem description -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <!-- 必填星号 -->
                <TextView
                    android:layout_width="@dimen/feedback_required_star_width"
                    android:layout_height="@dimen/feedback_required_star_height"
                    android:text="*"
                    android:textColor="@color/feedback_required_star_color"
                    android:textSize="16sp"
                    android:gravity="start" />

                <!-- 标签文本 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/feedback_label_height"
                    android:layout_marginStart="4dp"
                    android:text="Problem description"
                    android:textColor="@color/feedback_label_text_color"
                    android:textSize="16sp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 问题描述输入框 -->
            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="@dimen/feedback_item_margin_vertical">

                <EditText
                    android:id="@+id/et_problem_description"
                    android:layout_width="@dimen/feedback_input_width"
                    android:layout_height="@dimen/feedback_input_height"
                    android:background="@drawable/feedback_input_bg"
                    android:padding="@dimen/feedback_input_padding"
                    android:hint="Please provide a detailed description of the problem you have encountered"
                    android:textColorHint="@color/feedback_input_hint"
                    android:textColor="@color/feedback_input_text"
                    android:textSize="16sp"
                    android:gravity="top|start"
                    android:inputType="textMultiLine"
                    android:maxLength="1000"
                    android:fontFamily="sans-serif" />

                <!-- 字符计数 -->
                <TextView
                    android:id="@+id/tv_char_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_alignEnd="@+id/et_problem_description"
                    android:layout_alignBottom="@+id/et_problem_description"
                    android:layout_marginEnd="15dp"
                    android:layout_marginBottom="15dp"
                    android:text="0/1000"
                    android:textColor="@color/feedback_char_count_color"
                    android:textSize="14sp" />

            </RelativeLayout>

            <!-- Upload pictures -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <!-- 必填星号 -->
                <TextView
                    android:layout_width="@dimen/feedback_required_star_width"
                    android:layout_height="@dimen/feedback_required_star_height"
                    android:text="*"
                    android:textColor="@color/feedback_required_star_color"
                    android:textSize="16sp"
                    android:gravity="start" />

                <!-- 标签文本 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/feedback_label_height"
                    android:layout_marginStart="4dp"
                    android:text="Upload pictures"
                    android:textColor="@color/feedback_label_text_color"
                    android:textSize="16sp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 图片上传区域 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_upload_images"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="@dimen/feedback_item_margin_vertical"
                android:orientation="horizontal" />

            <!-- Contact information -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="16dp"
                android:orientation="horizontal">

                <!-- 必填星号 -->
                <TextView
                    android:layout_width="@dimen/feedback_required_star_width"
                    android:layout_height="@dimen/feedback_required_star_height"
                    android:text="*"
                    android:textColor="@color/feedback_required_star_color"
                    android:textSize="16sp"
                    android:gravity="start" />

                <!-- 标签文本 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="@dimen/feedback_label_height"
                    android:layout_marginStart="4dp"
                    android:text="Contact information"
                    android:textColor="@color/feedback_label_text_color"
                    android:textSize="16sp"
                    android:gravity="start" />

            </LinearLayout>

            <!-- 联系电话输入框 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="@dimen/feedback_item_margin_vertical"
                android:orientation="horizontal"
                android:background="@drawable/feedback_input_bg"
                android:padding="@dimen/feedback_input_padding">

                <!-- 国家代码选择 -->
                <LinearLayout
                    android:id="@+id/layout_country_code"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_gravity="center_vertical"
                    android:layout_marginEnd="8dp"
                    android:background="?android:attr/selectableItemBackground"
                    android:clickable="true"
                    android:focusable="true"
                    android:paddingStart="4dp"
                    android:paddingEnd="4dp"
                    android:paddingTop="2dp"
                    android:paddingBottom="2dp">

                    <TextView
                        android:id="@+id/tv_country_code"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="+1"
                        android:textColor="@color/feedback_input_text"
                        android:textSize="16sp"
                        android:layout_gravity="center_vertical" />

                    <!-- 下拉箭头 -->
                    <ImageView
                        android:layout_width="12dp"
                        android:layout_height="12dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="4dp"
                        android:src="@drawable/ic_arrow_down"
                        android:tint="@color/feedback_input_text" />

                </LinearLayout>

                <!-- 电话号码输入框 -->
                <EditText
                    android:id="@+id/et_contact_phone"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:background="@android:color/transparent"
                    android:hint="1201335788"
                    android:textColorHint="@color/feedback_input_hint"
                    android:textColor="@color/feedback_input_text"
                    android:textSize="16sp"
                    android:inputType="phone"
                    android:fontFamily="sans-serif" />

            </LinearLayout>

            <!-- 占位空间 -->
            <View
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1" />

            <!-- 提交按钮 -->
            <TextView
                android:id="@+id/btn_submit_feedback"
                android:layout_width="match_parent"
                android:layout_height="@dimen/feedback_button_height"
                android:layout_marginStart="@dimen/feedback_item_margin_horizontal"
                android:layout_marginEnd="@dimen/feedback_item_margin_horizontal"
                android:layout_marginBottom="32dp"
                android:background="@drawable/feedback_submit_button_bg"
                android:text="Submit Feedback"
                android:textColor="@color/feedback_button_text"
                android:textSize="16sp"
                android:fontFamily="sans-serif"
                android:gravity="center"
                android:clickable="true"
                android:focusable="true"
                android:enabled="false"
                android:alpha="0.5" />

        </LinearLayout>

    </ScrollView>

</LinearLayout>

</FrameLayout>
