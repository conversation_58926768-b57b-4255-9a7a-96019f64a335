package com.android.video.service;

import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import org.json.JSONObject;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;

/**
 * IP位置服务类
 * <p>
 * 通过IP地址获取用户的地理位置信息，使用免费的IP地理位置API服务。
 * 支持多个API提供商，提供备用方案确保服务可用性。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class IpLocationService {

    private static final String TAG = "IpLocationService";

    // 免费的IP地理位置API服务（确保返回英文结果）
    private static final String[] IP_LOCATION_APIS = {
        "http://ip-api.com/json/?lang=en",  // 指定英文语言
        "https://ipapi.co/json/",
        "https://freegeoip.app/json/"
    };

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;

    /**
     * IP位置信息回调接口
     */
    public interface IpLocationCallback {
        /**
         * IP位置获取成功
         * @param ipLocationInfo IP位置信息
         */
        void onIpLocationSuccess(IpLocationInfo ipLocationInfo);

        /**
         * IP位置获取失败
         * @param errorMessage 错误信息
         */
        void onIpLocationError(String errorMessage);
    }

    /**
     * IP位置信息数据类
     */
    public static class IpLocationInfo {
        private String ip;
        private String country;
        private String countryCode;
        private String region;
        private String city;
        private String timezone;
        private String isp;
        private double latitude;
        private double longitude;
        private String apiProvider;

        public IpLocationInfo() {}

        // Getters and Setters
        public String getIp() { return ip; }
        public void setIp(String ip) { this.ip = ip; }

        public String getCountry() { return country; }
        public void setCountry(String country) { this.country = country; }

        public String getCountryCode() { return countryCode; }
        public void setCountryCode(String countryCode) { this.countryCode = countryCode; }

        public String getRegion() { return region; }
        public void setRegion(String region) { this.region = region; }

        public String getCity() { return city; }
        public void setCity(String city) { this.city = city; }

        public String getTimezone() { return timezone; }
        public void setTimezone(String timezone) { this.timezone = timezone; }

        public String getIsp() { return isp; }
        public void setIsp(String isp) { this.isp = isp; }

        public double getLatitude() { return latitude; }
        public void setLatitude(double latitude) { this.latitude = latitude; }

        public double getLongitude() { return longitude; }
        public void setLongitude(double longitude) { this.longitude = longitude; }

        public String getApiProvider() { return apiProvider; }
        public void setApiProvider(String apiProvider) { this.apiProvider = apiProvider; }

        /**
         * 获取格式化的位置显示文本
         * @return 格式化的位置字符串
         */
        public String getFormattedLocation() {
            if (city != null && !city.isEmpty() && country != null && !country.isEmpty()) {
                return city + ", " + country;
            } else if (country != null && !country.isEmpty()) {
                return country;
            } else if (city != null && !city.isEmpty()) {
                return city;
            } else {
                return "Unknown Location";
            }
        }

        /**
         * 获取简短的位置显示文本（仅国家）
         * @return 国家名称
         */
        public String getCountryDisplay() {
            return country != null && !country.isEmpty() ? country : "Unknown";
        }

        @Override
        public String toString() {
            return "IpLocationInfo{" +
                    "ip='" + ip + '\'' +
                    ", country='" + country + '\'' +
                    ", countryCode='" + countryCode + '\'' +
                    ", region='" + region + '\'' +
                    ", city='" + city + '\'' +
                    ", timezone='" + timezone + '\'' +
                    ", isp='" + isp + '\'' +
                    ", latitude=" + latitude +
                    ", longitude=" + longitude +
                    ", apiProvider='" + apiProvider + '\'' +
                    '}';
        }
    }

    /**
     * 构造方法
     */
    public IpLocationService() {
        this.client = new OkHttpClient();
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }

    /**
     * 获取IP位置信息
     * @param callback 回调接口
     */
    public void getIpLocation(IpLocationCallback callback) {
        Log.d(TAG, "开始获取IP位置信息");
        getIpLocationFromApi(0, callback);
    }

    /**
     * 从指定的API获取IP位置信息
     * @param apiIndex API索引
     * @param callback 回调接口
     */
    private void getIpLocationFromApi(int apiIndex, IpLocationCallback callback) {
        if (apiIndex >= IP_LOCATION_APIS.length) {
            callback.onIpLocationError("All IP location APIs failed");
            return;
        }

        String apiUrl = IP_LOCATION_APIS[apiIndex];
        Log.d(TAG, "尝试API: " + apiUrl);

        Request request = new Request.Builder()
                .url(apiUrl)
                .build();

        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.w(TAG, "API " + apiUrl + " 请求失败: " + e.getMessage());
                // 尝试下一个API
                getIpLocationFromApi(apiIndex + 1, callback);
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    if (response.isSuccessful()) {
                        String responseBody = response.body().string();
                        Log.d(TAG, "API响应: " + responseBody);

                        IpLocationInfo locationInfo = parseIpLocationResponse(responseBody, apiUrl);
                        if (locationInfo != null) {
                            mainHandler.post(() -> callback.onIpLocationSuccess(locationInfo));
                        } else {
                            Log.w(TAG, "API " + apiUrl + " 响应解析失败");
                            // 尝试下一个API
                            getIpLocationFromApi(apiIndex + 1, callback);
                        }
                    } else {
                        Log.w(TAG, "API " + apiUrl + " 返回错误: " + response.code());
                        // 尝试下一个API
                        getIpLocationFromApi(apiIndex + 1, callback);
                    }
                } catch (Exception e) {
                    Log.e(TAG, "处理API响应时发生错误", e);
                    // 尝试下一个API
                    getIpLocationFromApi(apiIndex + 1, callback);
                }
            }
        });
    }

    /**
     * 解析IP位置API响应
     * @param responseBody 响应体
     * @param apiUrl API URL
     * @return 解析后的IP位置信息
     */
    private IpLocationInfo parseIpLocationResponse(String responseBody, String apiUrl) {
        try {
            JSONObject json = new JSONObject(responseBody);
            IpLocationInfo info = new IpLocationInfo();

            // 根据不同的API提供商解析不同的字段
            if (apiUrl.contains("ip-api.com")) {
                // ip-api.com 格式
                info.setIp(json.optString("query"));
                info.setCountry(json.optString("country"));
                info.setCountryCode(json.optString("countryCode"));
                info.setRegion(json.optString("regionName"));
                info.setCity(json.optString("city"));
                info.setTimezone(json.optString("timezone"));
                info.setIsp(json.optString("isp"));
                info.setLatitude(json.optDouble("lat", 0.0));
                info.setLongitude(json.optDouble("lon", 0.0));
                info.setApiProvider("ip-api.com");

            } else if (apiUrl.contains("ipapi.co")) {
                // ipapi.co 格式
                info.setIp(json.optString("ip"));
                info.setCountry(json.optString("country_name"));
                info.setCountryCode(json.optString("country"));
                info.setRegion(json.optString("region"));
                info.setCity(json.optString("city"));
                info.setTimezone(json.optString("timezone"));
                info.setIsp(json.optString("org"));
                info.setLatitude(json.optDouble("latitude", 0.0));
                info.setLongitude(json.optDouble("longitude", 0.0));
                info.setApiProvider("ipapi.co");

            } else if (apiUrl.contains("freegeoip.app")) {
                // freegeoip.app 格式
                info.setIp(json.optString("ip"));
                info.setCountry(json.optString("country_name"));
                info.setCountryCode(json.optString("country_code"));
                info.setRegion(json.optString("region_name"));
                info.setCity(json.optString("city"));
                info.setTimezone(json.optString("time_zone"));
                info.setLatitude(json.optDouble("latitude", 0.0));
                info.setLongitude(json.optDouble("longitude", 0.0));
                info.setApiProvider("freegeoip.app");
            }

            // 验证必要字段
            if (info.getCountry() == null || info.getCountry().isEmpty()) {
                Log.w(TAG, "API响应缺少国家信息");
                return null;
            }

            Log.d(TAG, "IP位置解析成功: " + info.toString());
            return info;

        } catch (Exception e) {
            Log.e(TAG, "解析IP位置响应时发生错误", e);
            return null;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 获取单例实例
     */
    private static IpLocationService instance;

    public static synchronized IpLocationService getInstance() {
        if (instance == null) {
            instance = new IpLocationService();
        }
        return instance;
    }
}
