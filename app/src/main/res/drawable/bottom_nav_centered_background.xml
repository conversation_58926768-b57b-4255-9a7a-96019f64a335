<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Selected state with gradient border -->
    <item android:state_selected="true">
        <layer-list>
            <!-- Calculate insets to center the background around icon only -->
            <!-- For 375dp reference: icon at 37dp, 31dp with size 20dp x 20dp -->
            <!-- Background should be 44dp x 28dp centered on icon -->
            <item android:gravity="center_horizontal|top"
                  android:top="@dimen/bottom_nav_selected_bg_margin_top"
                  android:bottom="0dp">
                <layer-list>
                    <!-- Gradient border background -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:angle="0"
                                android:startColor="#AF1E3C"
                                android:endColor="#2403AC"
                                android:type="linear" />
                            <corners android:radius="@dimen/bottom_nav_selected_bg_radius" />
                            <size
                                android:width="@dimen/bottom_nav_selected_bg_width"
                                android:height="@dimen/bottom_nav_selected_bg_height" />
                        </shape>
                    </item>
                    <!-- Black inner background -->
                    <item android:top="0.5dp" android:bottom="0.5dp"
                          android:left="0.5dp" android:right="0.5dp">
                        <shape android:shape="rectangle">
                            <solid android:color="#000000" />
                            <corners android:radius="@dimen/bottom_nav_selected_bg_radius" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </layer-list>
    </item>

    <!-- Checked state (same as selected) -->
    <item android:state_checked="true">
        <layer-list>
            <item android:gravity="center_horizontal|top"
                  android:top="@dimen/bottom_nav_selected_bg_margin_top"
                  android:bottom="0dp">
                <layer-list>
                    <!-- Gradient border background -->
                    <item>
                        <shape android:shape="rectangle">
                            <gradient
                                android:angle="0"
                                android:startColor="#AF1E3C"
                                android:endColor="#2403AC"
                                android:type="linear" />
                            <corners android:radius="@dimen/bottom_nav_selected_bg_radius" />
                            <size
                                android:width="@dimen/bottom_nav_selected_bg_width"
                                android:height="@dimen/bottom_nav_selected_bg_height" />
                        </shape>
                    </item>
                    <!-- Black inner background -->
                    <item android:top="0.5dp" android:bottom="0.5dp"
                          android:left="0.5dp" android:right="0.5dp">
                        <shape android:shape="rectangle">
                            <solid android:color="#000000" />
                            <corners android:radius="@dimen/bottom_nav_selected_bg_radius" />
                        </shape>
                    </item>
                </layer-list>
            </item>
        </layer-list>
    </item>

    <!-- Default state (transparent) -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
        </shape>
    </item>
</selector>
