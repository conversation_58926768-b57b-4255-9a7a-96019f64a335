package com.android.video.manager;

import android.app.Activity;
import android.app.PictureInPictureParams;
import android.content.Context;
import android.content.pm.PackageManager;
import android.graphics.Rect;
import android.os.Build;
import android.util.Log;
import android.util.Rational;
import android.view.View;

import androidx.annotation.RequiresApi;

import com.android.video.model.VideoModel;

/**
 * Picture-in-Picture (PiP) 模式管理器
 * 使用Android原生PiP功能实现小窗播放
 * 
 * <AUTHOR> Team
 */
public class PictureInPictureManager {
    
    private static final String TAG = "PictureInPictureManager";
    private static PictureInPictureManager instance;
    
    private boolean isPiPActive = false;
    private VideoModel currentVideo;
    
    private PictureInPictureManager() {
    }
    
    public static synchronized PictureInPictureManager getInstance() {
        if (instance == null) {
            instance = new PictureInPictureManager();
        }
        return instance;
    }
    
    /**
     * 检查设备是否支持PiP模式
     */
    public boolean isPiPSupported(Context context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            PackageManager packageManager = context.getPackageManager();
            boolean hasFeature = packageManager.hasSystemFeature(PackageManager.FEATURE_PICTURE_IN_PICTURE);

            // 额外检查Activity是否正确配置
            if (context instanceof Activity) {
                Activity activity = (Activity) context;
                try {
                    // 检查Activity是否支持PiP
                    android.content.pm.ActivityInfo activityInfo = packageManager.getActivityInfo(
                        activity.getComponentName(), 0);

                    // 简化检查，主要依赖manifest配置
                    Log.d(TAG, "PiP feature available: " + hasFeature + ", Activity: " + activity.getClass().getSimpleName());
                    return hasFeature;
                } catch (Exception e) {
                    Log.w(TAG, "Error checking Activity PiP support", e);
                    return hasFeature;
                }
            }

            return hasFeature;
        }
        return false;
    }
    
    /**
     * 进入PiP模式
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    public boolean enterPictureInPictureMode(Activity activity, VideoModel videoModel, View videoView) {
        Log.d(TAG, "Attempting to enter PiP mode...");

        if (!isPiPSupported(activity)) {
            Log.w(TAG, "PiP mode is not supported on this device or activity");
            return false;
        }

        try {
            // 检查Activity状态
            if (activity.isFinishing() || activity.isDestroyed()) {
                Log.w(TAG, "Activity is finishing or destroyed, cannot enter PiP");
                return false;
            }

            // 创建PiP参数
            PictureInPictureParams.Builder pipBuilder = new PictureInPictureParams.Builder();

            // 设置宽高比 (16:9)
            Rational aspectRatio = new Rational(16, 9);
            pipBuilder.setAspectRatio(aspectRatio);

            // 禁用自动进入全屏模式
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                pipBuilder.setAutoEnterEnabled(false);
            }

            // 设置源矩形提示（用于平滑过渡）
            if (videoView != null) {
                Rect sourceRect = getVideoViewBounds(videoView);
                pipBuilder.setSourceRectHint(sourceRect);
                Log.d(TAG, "Source rect set: " + sourceRect);
            }

            Log.d(TAG, "Calling enterPictureInPictureMode...");

            // 进入PiP模式
            boolean success = activity.enterPictureInPictureMode(pipBuilder.build());

            if (success) {
                isPiPActive = true;
                currentVideo = videoModel;
                Log.d(TAG, "Successfully entered PiP mode for video: " +
                      (videoModel != null ? videoModel.getTitle() : "Unknown"));
            } else {
                Log.e(TAG, "enterPictureInPictureMode returned false");
            }

            return success;

        } catch (IllegalStateException e) {
            Log.e(TAG, "IllegalStateException entering PiP mode: " + e.getMessage(), e);
            return false;
        } catch (Exception e) {
            Log.e(TAG, "Unexpected error entering PiP mode", e);
            return false;
        }
    }
    
    /**
     * 获取视频视图的边界
     */
    private Rect getVideoViewBounds(View videoView) {
        int[] location = new int[2];
        videoView.getLocationOnScreen(location);
        
        return new Rect(
            location[0],
            location[1],
            location[0] + videoView.getWidth(),
            location[1] + videoView.getHeight()
        );
    }
    
    /**
     * 退出PiP模式
     */
    public void exitPictureInPictureMode(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N && activity.isInPictureInPictureMode()) {
            // 在PiP模式下，调用finish()会退出PiP并返回正常模式
            // 或者可以通过用户点击PiP窗口来返回
            Log.d(TAG, "Exiting PiP mode");
        }
        isPiPActive = false;
        currentVideo = null;
    }
    
    /**
     * 检查当前是否在PiP模式
     */
    public boolean isPiPActive() {
        return isPiPActive;
    }
    
    /**
     * 设置PiP模式状态
     */
    public void setPiPActive(boolean active) {
        this.isPiPActive = active;
        if (!active) {
            currentVideo = null;
        }
    }
    
    /**
     * 获取当前PiP播放的视频
     */
    public VideoModel getCurrentVideo() {
        return currentVideo;
    }
    
    /**
     * 更新PiP参数
     */
    @RequiresApi(api = Build.VERSION_CODES.O)
    public void updatePictureInPictureParams(Activity activity, View videoView) {
        if (!isPiPActive || Build.VERSION.SDK_INT < Build.VERSION_CODES.O) {
            return;
        }
        
        try {
            PictureInPictureParams.Builder pipBuilder = new PictureInPictureParams.Builder();
            
            // 设置宽高比
            Rational aspectRatio = new Rational(16, 9);
            pipBuilder.setAspectRatio(aspectRatio);
            
            // 更新源矩形
            if (videoView != null) {
                Rect sourceRect = getVideoViewBounds(videoView);
                pipBuilder.setSourceRectHint(sourceRect);
            }
            
            activity.setPictureInPictureParams(pipBuilder.build());
            
        } catch (Exception e) {
            Log.e(TAG, "Error updating PiP parameters", e);
        }
    }
}
