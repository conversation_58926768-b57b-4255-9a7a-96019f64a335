package com.android.video.manager;

import android.app.Activity;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.graphics.Color;
import android.graphics.PixelFormat;
import android.media.projection.MediaProjectionManager;
import android.os.Build;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.Window;
import android.view.WindowManager;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.RequiresApi;

import com.android.video.R;

/**
 * 内容保护管理器
 * 实现防录屏/截图机制的核心功能
 * 
 * 功能包括：
 * 1. 录屏检测
 * 2. 显示半透明浮层提示
 * 3. 播放画面亮度调整至60%
 * 4. 添加APP logo水印（正中心，透明度30%）
 * 
 * <AUTHOR> Team
 */
public class ContentProtectionManager {
    
    private static final String TAG = "ContentProtectionManager";
    
    // 单例实例
    private static volatile ContentProtectionManager instance;
    
    // 配置常量
    private static final int WARNING_SHOW_DURATION_MS = 3000; // 提示显示3秒
    private static final int WARNING_ANIMATION_DURATION_MS = 300; // 动画时长300ms
    private static final float DIMMED_BRIGHTNESS = 0.6f; // 降低亮度至60%
    private static final float WATERMARK_ALPHA = 0.3f; // 水印透明度30%
    private static final int WARNING_TEXT_SIZE_SP = 16; // 提示文字大小16sp
    
    // 状态管理
    private boolean isProtectionEnabled = false;
    private boolean isRecordingDetected = false;
    private Activity currentActivity;
    private MediaProjectionManager projectionManager;
    private Handler mainHandler;

    // 增强录屏检测
    private BroadcastReceiver screenRecordReceiver;
    private Runnable recordingCheckRunnable;
    private boolean isMonitoringRecording = false;
    
    // UI组件
    private View warningOverlay;
    private View watermarkOverlay;
    private float originalBrightness = -1f; // 保存原始亮度
    
    // 回调接口
    public interface RecordingDetectionCallback {
        void onRecordingDetected();
        void onRecordingStopped();
    }
    
    private RecordingDetectionCallback detectionCallback;
    
    private ContentProtectionManager() {
        mainHandler = new Handler(Looper.getMainLooper());
    }
    
    /**
     * 获取单例实例
     */
    public static ContentProtectionManager getInstance() {
        if (instance == null) {
            synchronized (ContentProtectionManager.class) {
                if (instance == null) {
                    instance = new ContentProtectionManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 启用内容保护
     * @param activity 当前Activity
     */
    public void enableProtection(Activity activity) {
        if (activity == null) {
            Log.w(TAG, "Activity is null, cannot enable protection");
            return;
        }
        
        this.currentActivity = activity;
        this.isProtectionEnabled = true;
        
        Log.d(TAG, "Enabling content protection for: " + activity.getClass().getSimpleName());
        
        // 设置FLAG_SECURE防止截图
        addSecureFlag(activity.getWindow());
        
        // 初始化录屏检测
        initializeRecordingDetection(activity);
        
        Log.d(TAG, "Content protection enabled successfully");
    }
    
    /**
     * 禁用内容保护
     */
    public void disableProtection() {
        if (!isProtectionEnabled) {
            return;
        }

        Log.d(TAG, "Disabling content protection");

        // 停止录屏监控
        stopRecordingMonitoring();

        // 移除所有保护措施
        removeWarningOverlay();
        removeWatermarkOverlay();
        restoreOriginalBrightness();

        // 清除FLAG_SECURE
        if (currentActivity != null && currentActivity.getWindow() != null) {
            currentActivity.getWindow().clearFlags(WindowManager.LayoutParams.FLAG_SECURE);
        }

        isProtectionEnabled = false;
        isRecordingDetected = false;
        currentActivity = null;

        Log.d(TAG, "Content protection disabled");
    }
    
    /**
     * 设置录屏检测回调
     */
    public void setRecordingDetectionCallback(RecordingDetectionCallback callback) {
        this.detectionCallback = callback;
    }
    
    /**
     * 检查是否启用了内容保护
     */
    public boolean isProtectionEnabled() {
        return isProtectionEnabled;
    }
    
    /**
     * 检查是否检测到录屏
     */
    public boolean isRecordingDetected() {
        return isRecordingDetected;
    }
    
    /**
     * 添加安全标志防止截图
     */
    private void addSecureFlag(Window window) {
        if (window == null) return;
        
        try {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            );
            Log.d(TAG, "FLAG_SECURE added successfully");
        } catch (Exception e) {
            Log.e(TAG, "Failed to add FLAG_SECURE", e);
        }
    }
    
    /**
     * 初始化录屏检测
     */
    private void initializeRecordingDetection(Activity activity) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            try {
                projectionManager = (MediaProjectionManager)
                    activity.getSystemService(Context.MEDIA_PROJECTION_SERVICE);

                // 注册广播接收器监听录屏相关事件
                registerScreenRecordReceiver(activity);

                // 启动定期检测
                startRecordingMonitoring();

                Log.d(TAG, "Enhanced recording detection initialized");

            } catch (Exception e) {
                Log.e(TAG, "Failed to initialize recording detection", e);
            }
        } else {
            Log.w(TAG, "Recording detection not supported on Android < 5.0");
        }
    }

    /**
     * 注册录屏检测广播接收器
     */
    private void registerScreenRecordReceiver(Activity activity) {
        try {
            screenRecordReceiver = new BroadcastReceiver() {
                @Override
                public void onReceive(Context context, Intent intent) {
                    String action = intent.getAction();
                    Log.d(TAG, "Received broadcast: " + action);

                    // 检测可能的录屏相关广播
                    if (Intent.ACTION_USER_PRESENT.equals(action) ||
                        Intent.ACTION_SCREEN_ON.equals(action) ||
                        Intent.ACTION_SCREEN_OFF.equals(action)) {

                        // 延迟检测，给系统时间启动录屏
                        mainHandler.postDelayed(() -> {
                            if (isProtectionEnabled) {
                                checkForRecordingApps();
                            }
                        }, 1000);
                    }
                }
            };

            IntentFilter filter = new IntentFilter();
            filter.addAction(Intent.ACTION_USER_PRESENT);
            filter.addAction(Intent.ACTION_SCREEN_ON);
            filter.addAction(Intent.ACTION_SCREEN_OFF);

            activity.registerReceiver(screenRecordReceiver, filter);
            Log.d(TAG, "Screen record receiver registered");

        } catch (Exception e) {
            Log.e(TAG, "Failed to register screen record receiver", e);
        }
    }
    
    /**
     * 开始录屏监控
     * 使用多种方法检测录屏行为
     */
    private void startRecordingMonitoring() {
        if (isMonitoringRecording) {
            return;
        }

        isMonitoringRecording = true;

        // 创建定期检测任务
        recordingCheckRunnable = new Runnable() {
            @Override
            public void run() {
                if (isProtectionEnabled && isMonitoringRecording) {
                    checkForRecordingApps();
                    // 每2秒检测一次
                    mainHandler.postDelayed(this, 2000);
                }
            }
        };

        // 开始定期检测
        mainHandler.post(recordingCheckRunnable);

        Log.d(TAG, "Enhanced recording monitoring started");
    }

    /**
     * 检测录屏应用
     */
    private void checkForRecordingApps() {
        try {
            // 模拟录屏检测逻辑
            // 实际项目中可以检查：
            // 1. 运行中的应用列表
            // 2. 系统服务状态
            // 3. 音频录制状态
            // 4. MediaProjection状态

            // 这里添加一个简单的检测逻辑
            // 实际使用时需要根据具体需求实现更复杂的检测
            boolean recordingDetected = simulateRecordingDetection();

            if (recordingDetected && !isRecordingDetected) {
                Log.w(TAG, "Recording detected by enhanced detection");
                onRecordingDetected();
            } else if (!recordingDetected && isRecordingDetected) {
                Log.i(TAG, "Recording stopped by enhanced detection");
                onRecordingStopped();
            }

        } catch (Exception e) {
            Log.e(TAG, "Error checking for recording apps", e);
        }
    }

    /**
     * 模拟录屏检测
     * 实际项目中需要实现真实的检测逻辑
     */
    private boolean simulateRecordingDetection() {
        // 这里可以实现真实的录屏检测逻辑
        // 例如：检查MediaProjection状态、音频录制状态等

        // 目前返回false，表示未检测到录屏
        // 实际使用时需要替换为真实的检测逻辑
        return false;
    }

    /**
     * 停止录屏监控
     */
    private void stopRecordingMonitoring() {
        isMonitoringRecording = false;

        if (recordingCheckRunnable != null) {
            mainHandler.removeCallbacks(recordingCheckRunnable);
            recordingCheckRunnable = null;
        }

        if (screenRecordReceiver != null && currentActivity != null) {
            try {
                currentActivity.unregisterReceiver(screenRecordReceiver);
                screenRecordReceiver = null;
                Log.d(TAG, "Screen record receiver unregistered");
            } catch (Exception e) {
                Log.e(TAG, "Failed to unregister screen record receiver", e);
            }
        }

        Log.d(TAG, "Recording monitoring stopped");
    }
    
    /**
     * 当检测到录屏时调用
     */
    public void onRecordingDetected() {
        if (!isProtectionEnabled || isRecordingDetected) {
            return;
        }
        
        Log.w(TAG, "Screen recording detected!");
        isRecordingDetected = true;
        
        mainHandler.post(() -> {
            showWarningOverlay();
            adjustBrightness(DIMMED_BRIGHTNESS);
            addWatermark();
            
            if (detectionCallback != null) {
                detectionCallback.onRecordingDetected();
            }
        });
    }
    
    /**
     * 当录屏停止时调用
     */
    public void onRecordingStopped() {
        if (!isRecordingDetected) {
            return;
        }

        Log.i(TAG, "Screen recording stopped");
        isRecordingDetected = false;

        mainHandler.post(() -> {
            removeWarningOverlay();
            removeWatermarkOverlay();
            restoreOriginalBrightness();

            if (detectionCallback != null) {
                detectionCallback.onRecordingStopped();
            }
        });
    }

    /**
     * 显示警告浮层
     */
    private void showWarningOverlay() {
        if (currentActivity == null || warningOverlay != null) {
            return;
        }

        try {
            // 创建警告浮层
            FrameLayout overlay = new FrameLayout(currentActivity);
            overlay.setBackgroundColor(Color.argb(128, 0, 0, 0)); // 半透明黑色背景

            // 创建警告文本
            TextView warningText = new TextView(currentActivity);
            warningText.setText("For copyright protection, screen recording of current content is prohibited");
            warningText.setTextColor(Color.WHITE);
            warningText.setTextSize(WARNING_TEXT_SIZE_SP);
            warningText.setGravity(Gravity.CENTER);
            warningText.setPadding(32, 16, 32, 16);

            // 设置文本布局参数
            FrameLayout.LayoutParams textParams = new FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.WRAP_CONTENT,
                FrameLayout.LayoutParams.WRAP_CONTENT
            );
            textParams.gravity = Gravity.CENTER;

            overlay.addView(warningText, textParams);

            // 添加到Activity的根视图
            ViewGroup rootView = currentActivity.findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.addView(overlay, new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ));

                warningOverlay = overlay;

                // 3秒后自动隐藏
                mainHandler.postDelayed(this::removeWarningOverlay, WARNING_SHOW_DURATION_MS);

                Log.d(TAG, "Warning overlay displayed");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to show warning overlay", e);
        }
    }

    /**
     * 移除警告浮层
     */
    private void removeWarningOverlay() {
        if (warningOverlay != null && currentActivity != null) {
            try {
                ViewGroup rootView = currentActivity.findViewById(android.R.id.content);
                if (rootView != null) {
                    rootView.removeView(warningOverlay);
                }
                warningOverlay = null;
                Log.d(TAG, "Warning overlay removed");
            } catch (Exception e) {
                Log.e(TAG, "Failed to remove warning overlay", e);
            }
        }
    }

    /**
     * 调整屏幕亮度
     */
    private void adjustBrightness(float brightness) {
        if (currentActivity == null || currentActivity.getWindow() == null) {
            return;
        }

        try {
            Window window = currentActivity.getWindow();
            WindowManager.LayoutParams layoutParams = window.getAttributes();

            // 保存原始亮度（仅第一次）
            if (originalBrightness < 0) {
                originalBrightness = layoutParams.screenBrightness;
            }

            // 设置新亮度
            layoutParams.screenBrightness = brightness;
            window.setAttributes(layoutParams);

            Log.d(TAG, "Brightness adjusted to: " + brightness);
        } catch (Exception e) {
            Log.e(TAG, "Failed to adjust brightness", e);
        }
    }

    /**
     * 恢复原始亮度
     */
    private void restoreOriginalBrightness() {
        if (originalBrightness >= 0) {
            adjustBrightness(originalBrightness);
            originalBrightness = -1f;
            Log.d(TAG, "Original brightness restored");
        }
    }

    /**
     * 添加水印
     */
    private void addWatermark() {
        if (currentActivity == null || watermarkOverlay != null) {
            return;
        }

        try {
            // 创建水印容器
            FrameLayout watermarkContainer = new FrameLayout(currentActivity);
            watermarkContainer.setClickable(false);
            watermarkContainer.setFocusable(false);

            // 创建水印ImageView
            ImageView watermarkImage = new ImageView(currentActivity);
            // 使用应用图标作为水印，如果不存在则使用默认图标
            try {
                watermarkImage.setImageResource(R.mipmap.ic_launcher);
            } catch (Exception e) {
                // 如果找不到图标，创建一个简单的文本水印
                Log.w(TAG, "App icon not found, using text watermark");
                watermarkImage.setImageResource(android.R.drawable.ic_dialog_info);
            }
            watermarkImage.setAlpha(WATERMARK_ALPHA); // 设置30%透明度
            watermarkImage.setScaleType(ImageView.ScaleType.CENTER_INSIDE);

            // 设置水印布局参数（居中显示）
            FrameLayout.LayoutParams watermarkParams = new FrameLayout.LayoutParams(
                200, 200 // 水印大小
            );
            watermarkParams.gravity = Gravity.CENTER;

            watermarkContainer.addView(watermarkImage, watermarkParams);

            // 添加到Activity的根视图
            ViewGroup rootView = currentActivity.findViewById(android.R.id.content);
            if (rootView != null) {
                rootView.addView(watermarkContainer, new ViewGroup.LayoutParams(
                    ViewGroup.LayoutParams.MATCH_PARENT,
                    ViewGroup.LayoutParams.MATCH_PARENT
                ));

                watermarkOverlay = watermarkContainer;
                Log.d(TAG, "Watermark added");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to add watermark", e);
        }
    }

    /**
     * 移除水印
     */
    private void removeWatermarkOverlay() {
        if (watermarkOverlay != null && currentActivity != null) {
            try {
                ViewGroup rootView = currentActivity.findViewById(android.R.id.content);
                if (rootView != null) {
                    rootView.removeView(watermarkOverlay);
                }
                watermarkOverlay = null;
                Log.d(TAG, "Watermark removed");
            } catch (Exception e) {
                Log.e(TAG, "Failed to remove watermark", e);
            }
        }
    }
}
