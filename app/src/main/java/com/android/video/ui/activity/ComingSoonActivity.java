package com.android.video.ui.activity;

import android.content.Intent;
import android.os.Bundle;
import android.util.Log;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout;
import com.android.video.R;
import com.android.video.adapter.ComingSoonDetailAdapter;
import com.android.video.model.VideoModel;
import com.android.video.model.response.WorthWaitingDataModel;
import com.android.video.network.HomeApiService;
import com.android.video.ui.activity.VideoDetailActivity;
import java.util.ArrayList;
import java.util.List;

/**
 * Coming Soon查看所有页面
 * 显示即将上映的视频列表
 * <AUTHOR>
 */
public class ComingSoonActivity extends BaseFullScreenActivity {

    private static final String TAG = "ComingSoonActivity";
    private static final int PAGE_SIZE = 10; // 每页数量

    private ImageView ivBack;
    private TextView tvTitle;
    private SwipeRefreshLayout swipeRefreshLayout;
    private RecyclerView rvComingSoon;
    private ComingSoonDetailAdapter adapter;

    // API相关
    private HomeApiService homeApiService;
    private int currentPage = 1;
    private boolean isLoading = false;
    private boolean hasMoreData = true;
    private List<VideoModel> allVideoList;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_coming_soon);

        initViews();
        initApiService();
        setupRecyclerView();
        setupSwipeRefresh();
        loadData();
        setupClickListeners();
    }

    /**
     * 初始化视图组件
     */
    private void initViews() {
        ivBack = findViewById(R.id.iv_back);
        tvTitle = findViewById(R.id.tv_title);
        swipeRefreshLayout = findViewById(R.id.swipe_refresh_layout);
        rvComingSoon = findViewById(R.id.rv_coming_soon);
    }

    /**
     * 初始化API服务
     */
    private void initApiService() {
        homeApiService = HomeApiService.getInstance();
        allVideoList = new ArrayList<>();
    }

    /**
     * 设置RecyclerView
     */
    private void setupRecyclerView() {
        adapter = new ComingSoonDetailAdapter();
        LinearLayoutManager layoutManager = new LinearLayoutManager(this, LinearLayoutManager.VERTICAL, false);
        rvComingSoon.setLayoutManager(layoutManager);
        rvComingSoon.setAdapter(adapter);

        // 设置点击监听
        adapter.setOnVideoClickListener(new ComingSoonDetailAdapter.OnVideoClickListener() {
            @Override
            public void onVideoClick(VideoModel video, int position) {
                // 跳转到视频详情页
                Intent intent = new Intent(ComingSoonActivity.this, VideoDetailActivity.class);
                intent.putExtra("video_id", video.getId());
                intent.putExtra("video_title", video.getTitle());
                startActivity(intent);
            }
        });

        // 设置订阅监听
        adapter.setOnSubscriptionChangeListener(new ComingSoonDetailAdapter.OnSubscriptionChangeListener() {
            @Override
            public void onSubscriptionChanged(VideoModel video, int position, boolean isSubscribed) {
                String message = isSubscribed ? "已订阅: " : "取消订阅: ";
                Toast.makeText(ComingSoonActivity.this,
                        message + video.getDisplayTitle(),
                        Toast.LENGTH_SHORT).show();
            }
        });

        // 添加滚动监听，实现分页加载
        rvComingSoon.addOnScrollListener(new RecyclerView.OnScrollListener() {
            @Override
            public void onScrolled(RecyclerView recyclerView, int dx, int dy) {
                super.onScrolled(recyclerView, dx, dy);

                if (dy > 0 && !isLoading && hasMoreData) { // 向上滚动
                    int visibleItemCount = layoutManager.getChildCount();
                    int totalItemCount = layoutManager.getItemCount();
                    int firstVisibleItemPosition = layoutManager.findFirstVisibleItemPosition();

                    // 当滚动到倒数第3个item时开始加载下一页
                    if ((visibleItemCount + firstVisibleItemPosition) >= totalItemCount - 3) {
                        loadMoreData();
                    }
                }
            }
        });
    }

    /**
     * 设置下拉刷新
     */
    private void setupSwipeRefresh() {
        swipeRefreshLayout.setColorSchemeResources(
            android.R.color.holo_blue_bright,
            android.R.color.holo_green_light,
            android.R.color.holo_orange_light,
            android.R.color.holo_red_light
        );

        swipeRefreshLayout.setOnRefreshListener(this::refreshData);
    }

    /**
     * 加载数据（首次加载）
     */
    private void loadData() {
        Log.d(TAG, "开始加载Worth Waiting数据");
        currentPage = 1;
        hasMoreData = true;
        loadWorthWaitingData(false);
    }

    /**
     * 刷新数据
     */
    private void refreshData() {
        Log.d(TAG, "刷新Worth Waiting数据");
        currentPage = 1;
        hasMoreData = true;
        allVideoList.clear();
        loadWorthWaitingData(true);
    }

    /**
     * 加载更多数据
     */
    private void loadMoreData() {
        if (!hasMoreData || isLoading) {
            return;
        }

        Log.d(TAG, "加载更多Worth Waiting数据，页码: " + (currentPage + 1));
        currentPage++;
        loadWorthWaitingData(false);
    }

    /**
     * 加载Worth Waiting数据
     * @param isRefresh 是否为刷新操作
     */
    private void loadWorthWaitingData(boolean isRefresh) {
        if (isLoading) {
            Log.d(TAG, "正在加载中，跳过重复请求");
            return;
        }

        isLoading = true;

        // 显示加载状态
        if (isRefresh) {
            swipeRefreshLayout.setRefreshing(true);
        }

        homeApiService.getWorthWaiting(currentPage, PAGE_SIZE, new HomeApiService.WorthWaitingCallback() {
            @Override
            public void onSuccess(WorthWaitingDataModel worthWaitingData) {
                Log.d(TAG, "Worth Waiting数据加载成功，页码: " + currentPage +
                          ", 数量: " + worthWaitingData.getRecordCount());

                // 转换为VideoModel列表
                List<VideoModel> newVideoList = worthWaitingData.toVideoModelList();

                if (isRefresh || currentPage == 1) {
                    // 刷新或首次加载，替换所有数据
                    allVideoList.clear();
                    allVideoList.addAll(newVideoList);
                    adapter.updateVideoList(allVideoList);
                } else {
                    // 分页加载，追加数据
                    allVideoList.addAll(newVideoList);
                    adapter.updateVideoList(allVideoList);
                }

                // 检查是否还有更多数据
                hasMoreData = worthWaitingData.hasMorePages();

                Log.d(TAG, "✅ 更新Worth Waiting: " + allVideoList.size() + "个视频, " +
                          "hasMoreData: " + hasMoreData);

                // 隐藏加载状态
                isLoading = false;
                if (swipeRefreshLayout.isRefreshing()) {
                    swipeRefreshLayout.setRefreshing(false);
                }
            }

            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "Worth Waiting数据加载失败: " + errorMessage);

                // 隐藏加载状态
                isLoading = false;
                if (swipeRefreshLayout.isRefreshing()) {
                    swipeRefreshLayout.setRefreshing(false);
                }

                // 显示错误提示
                Toast.makeText(ComingSoonActivity.this,
                              "加载失败: " + errorMessage,
                              Toast.LENGTH_SHORT).show();

                // 如果是首次加载失败，可以显示重试按钮或空状态
                if (currentPage == 1 && allVideoList.isEmpty()) {
                    // 可以在这里添加空状态处理
                    Log.d(TAG, "首次加载失败，显示空状态");
                }
            }
        });
    }

    /**
     * 设置点击监听
     */
    private void setupClickListeners() {
        ivBack.setOnClickListener(v -> finish());
    }


}
