package com.android.video.model.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;
import com.android.video.constants.AuthApiConstantsUtils;

/**
 * 统一登录请求模型
 * <p>
 * 支持手机号、VKontakte、TikTok登录的统一请求模型。
 * </p>
 * <AUTHOR> Team
 */
public class LoginRequestModel {

    /**
     * 登录类型：1=手机号登录, 2=VKontakte登录, 3=TikTok登录
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_LOGIN_TYPE)
    @Expose
    private int loginType;

    /**
     * 手机号（手机号登录时必填）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_PHONE_NUMBER)
    @Expose
    private String phoneNumber;

    /**
     * 验证码（手机号登录时必填）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_VERIFICATION_CODE)
    @Expose
    private String verificationCode;

    /**
     * VKontakte授权码（VKontakte登录时必填）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_VKONTAKTE_CODE)
    @Expose
    private String vKontakteCode;

    /**
     * TikTok授权码（TikTok登录时必填）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_TIKTOK_CODE)
    @Expose
    private String tiktokCode;

    /**
     * 设备唯一ID（如IMEI/AndroidID）
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_DEVICE_CODE)
    @Expose
    private String deviceCode;

    /**
     * 系统类型：1=iOS, 2=Android
     */
    @SerializedName(AuthApiConstantsUtils.PARAM_OS_TYPE)
    @Expose
    private int osType;

    /**
     * 默认构造函数
     */
    public LoginRequestModel() {
    }

    /**
     * 手机号登录构造函数
     * @param phoneNumber 手机号
     * @param verificationCode 验证码
     * @param deviceCode 设备唯一ID
     * @param osType 系统类型
     */
    public LoginRequestModel(String phoneNumber, String verificationCode, String deviceCode, int osType) {
        this.loginType = AuthApiConstantsUtils.LOGIN_TYPE_PHONE;
        this.phoneNumber = phoneNumber;
        this.verificationCode = verificationCode;
        this.vKontakteCode = "";
        this.tiktokCode = "";
        this.deviceCode = deviceCode;
        this.osType = osType;
    }

    /**
     * VKontakte登录构造函数
     * @param vKontakteCode VKontakte授权码
     * @param deviceCode 设备唯一ID
     * @param osType 系统类型
     */
    public LoginRequestModel(String vKontakteCode, String deviceCode, int osType, boolean isVKontakte) {
        this.loginType = AuthApiConstantsUtils.LOGIN_TYPE_VKONTAKTE;
        this.phoneNumber = "";
        this.verificationCode = "";
        this.vKontakteCode = vKontakteCode;
        this.tiktokCode = "";
        this.deviceCode = deviceCode;
        this.osType = osType;
    }

    /**
     * TikTok登录构造函数
     * @param tiktokCode TikTok授权码
     * @param deviceCode 设备唯一ID
     * @param osType 系统类型
     */
    public static LoginRequestModel createTikTokLogin(String tiktokCode, String deviceCode, int osType) {
        LoginRequestModel model = new LoginRequestModel();
        model.loginType = AuthApiConstantsUtils.LOGIN_TYPE_TIKTOK;
        model.phoneNumber = "";
        model.verificationCode = "";
        model.vKontakteCode = "";
        model.tiktokCode = tiktokCode;
        model.deviceCode = deviceCode;
        model.osType = osType;
        return model;
    }

    // ========== Getter和Setter方法 ==========

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public String getVerificationCode() {
        return verificationCode;
    }

    public void setVerificationCode(String verificationCode) {
        this.verificationCode = verificationCode;
    }

    public String getVKontakteCode() {
        return vKontakteCode;
    }

    public void setVKontakteCode(String vKontakteCode) {
        this.vKontakteCode = vKontakteCode;
    }

    public String getTiktokCode() {
        return tiktokCode;
    }

    public void setTiktokCode(String tiktokCode) {
        this.tiktokCode = tiktokCode;
    }

    public String getDeviceCode() {
        return deviceCode;
    }

    public void setDeviceCode(String deviceCode) {
        this.deviceCode = deviceCode;
    }

    public int getOsType() {
        return osType;
    }

    public void setOsType(int osType) {
        this.osType = osType;
    }

    /**
     * @deprecated 使用 {@link #getDeviceCode()} 替代
     */
    @Deprecated
    public String getDeviceId() {
        return deviceCode;
    }

    /**
     * @deprecated 使用 {@link #setDeviceCode(String)} 替代
     */
    @Deprecated
    public void setDeviceId(String deviceId) {
        this.deviceCode = deviceId;
    }

    // ========== 便利方法 ==========

    /**
     * 验证请求参数是否有效
     * @return 是否有效
     */
    public boolean isValid() {
        // 基础参数验证
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            return false;
        }
        if (osType != AuthApiConstantsUtils.OS_TYPE_IOS && osType != AuthApiConstantsUtils.OS_TYPE_ANDROID) {
            return false;
        }

        // 根据登录类型验证特定参数
        switch (loginType) {
            case AuthApiConstantsUtils.LOGIN_TYPE_PHONE:
                return phoneNumber != null && !phoneNumber.trim().isEmpty() &&
                       verificationCode != null && !verificationCode.trim().isEmpty();
            case AuthApiConstantsUtils.LOGIN_TYPE_VKONTAKTE:
                return vKontakteCode != null && !vKontakteCode.trim().isEmpty();
            case AuthApiConstantsUtils.LOGIN_TYPE_TIKTOK:
                return tiktokCode != null && !tiktokCode.trim().isEmpty();
            default:
                return false;
        }
    }

    /**
     * 获取验证失败的原因
     * @return 验证失败原因，如果验证通过则返回null
     */
    public String getValidationError() {
        if (deviceCode == null || deviceCode.trim().isEmpty()) {
            return "Device code is required";
        }
        if (osType != AuthApiConstantsUtils.OS_TYPE_IOS && osType != AuthApiConstantsUtils.OS_TYPE_ANDROID) {
            return "Invalid OS type. Must be 1 (iOS) or 2 (Android)";
        }

        switch (loginType) {
            case AuthApiConstantsUtils.LOGIN_TYPE_PHONE:
                if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
                    return "Phone number is required for phone login";
                }
                if (verificationCode == null || verificationCode.trim().isEmpty()) {
                    return "Verification code is required for phone login";
                }
                break;
            case AuthApiConstantsUtils.LOGIN_TYPE_VKONTAKTE:
                if (vKontakteCode == null || vKontakteCode.trim().isEmpty()) {
                    return "VKontakte code is required for VKontakte login";
                }
                break;
            case AuthApiConstantsUtils.LOGIN_TYPE_TIKTOK:
                if (tiktokCode == null || tiktokCode.trim().isEmpty()) {
                    return "TikTok code is required for TikTok login";
                }
                break;
            default:
                return "Invalid login type. Must be 1 (Phone), 2 (VKontakte), or 3 (TikTok)";
        }
        return null;
    }

    /**
     * 获取登录类型描述
     * @return 登录类型描述
     */
    public String getLoginTypeDescription() {
        switch (loginType) {
            case AuthApiConstantsUtils.LOGIN_TYPE_PHONE:
                return "Phone Login";
            case AuthApiConstantsUtils.LOGIN_TYPE_VKONTAKTE:
                return "VKontakte Login";
            case AuthApiConstantsUtils.LOGIN_TYPE_TIKTOK:
                return "TikTok Login";
            default:
                return "Unknown Login Type";
        }
    }

    /**
     * 获取操作系统类型描述
     * @return 操作系统类型描述
     */
    public String getOsTypeDescription() {
        switch (osType) {
            case AuthApiConstantsUtils.OS_TYPE_IOS:
                return "iOS";
            case AuthApiConstantsUtils.OS_TYPE_ANDROID:
                return "Android";
            default:
                return "Unknown";
        }
    }

    @Override
    public String toString() {
        return "LoginRequestModel{" +
                "loginType=" + loginType + " (" + getLoginTypeDescription() + ")" +
                ", phoneNumber='" + phoneNumber + '\'' +
                ", verificationCode='" + (verificationCode != null && !verificationCode.isEmpty() ? "***" : "null") + '\'' +
                ", vKontakteCode='" + (vKontakteCode != null && !vKontakteCode.isEmpty() ? "***" : "null") + '\'' +
                ", tiktokCode='" + (tiktokCode != null && !tiktokCode.isEmpty() ? "***" : "null") + '\'' +
                ", deviceCode='" + deviceCode + '\'' +
                ", osType=" + osType + " (" + getOsTypeDescription() + ")" +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        LoginRequestModel that = (LoginRequestModel) obj;

        if (loginType != that.loginType) return false;
        if (osType != that.osType) return false;
        if (phoneNumber != null ? !phoneNumber.equals(that.phoneNumber) : that.phoneNumber != null) return false;
        if (verificationCode != null ? !verificationCode.equals(that.verificationCode) : that.verificationCode != null) return false;
        if (vKontakteCode != null ? !vKontakteCode.equals(that.vKontakteCode) : that.vKontakteCode != null) return false;
        if (tiktokCode != null ? !tiktokCode.equals(that.tiktokCode) : that.tiktokCode != null) return false;
        return deviceCode != null ? deviceCode.equals(that.deviceCode) : that.deviceCode == null;
    }

    @Override
    public int hashCode() {
        int result = loginType;
        result = 31 * result + (phoneNumber != null ? phoneNumber.hashCode() : 0);
        result = 31 * result + (verificationCode != null ? verificationCode.hashCode() : 0);
        result = 31 * result + (vKontakteCode != null ? vKontakteCode.hashCode() : 0);
        result = 31 * result + (tiktokCode != null ? tiktokCode.hashCode() : 0);
        result = 31 * result + (deviceCode != null ? deviceCode.hashCode() : 0);
        result = 31 * result + osType;
        return result;
    }
}
