package com.android.video.model.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 登录响应数据模型
 * <AUTHOR> Team
 */
public class LoginResponseModel {

    /**
     * 用户ID
     */
    @SerializedName("customerId")
    @Expose
    private String customerId;

    /**
     * 用户昵称
     */
    @SerializedName("customerName")
    @Expose
    private String customerName;

    /**
     * 用户token
     */
    @SerializedName("userToken")
    @Expose
    private String userToken;

    /**
     * 登录类型：1=手机号登录, 2=VKontakte登录, 3=TikTok登录
     */
    @SerializedName("loginType")
    @Expose
    private int loginType;

    /**
     * 设备唯一标识编码
     */
    @SerializedName("deviceId")
    @Expose
    private String deviceId;

    // ========== 兼容性字段（用于向后兼容） ==========

    /**
     * @deprecated 使用 {@link #getCustomerId()} 替代
     * 用户ID（兼容性字段）
     */
    @Deprecated
    private String uid;

    /**
     * @deprecated 新接口不再返回VIP状态，需要通过其他接口获取
     * 用户VIP状态 (1: VIP, 0: 普通用户)
     */
    @Deprecated
    private int userVip;

    /**
     * @deprecated 使用 {@link #getUserToken()} 替代
     * 访问令牌（兼容性字段）
     */
    @Deprecated
    private String accessToken;

    /**
     * @deprecated 新接口不再返回过期时间
     * 令牌过期时间（时间戳）
     */
    @Deprecated
    private long expiresAt;

    /**
     * @deprecated 新接口不再返回手机号
     * 用户手机号（兼容性字段）
     */
    @Deprecated
    private String phoneNumber;

    /**
     * @deprecated 使用 {@link #getCustomerName()} 替代
     * 用户昵称（兼容性字段）
     */
    @Deprecated
    private String nickname;

    /**
     * @deprecated 新接口不再返回头像URL
     * 用户头像URL（兼容性字段）
     */
    @Deprecated
    private String avatar;

    /**
     * 默认构造函数
     */
    public LoginResponseModel() {
    }

    // ========== 新接口字段的Getter和Setter方法 ==========

    public String getCustomerId() {
        return customerId;
    }

    public void setCustomerId(String customerId) {
        this.customerId = customerId;
        // 同步更新兼容性字段
        this.uid = customerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
        // 同步更新兼容性字段
        this.nickname = customerName;
    }

    public String getUserToken() {
        return userToken;
    }

    public void setUserToken(String userToken) {
        this.userToken = userToken;
        // 同步更新兼容性字段
        this.accessToken = userToken;
    }

    public int getLoginType() {
        return loginType;
    }

    public void setLoginType(int loginType) {
        this.loginType = loginType;
    }

    public String getDeviceId() {
        return deviceId;
    }

    public void setDeviceId(String deviceId) {
        this.deviceId = deviceId;
    }

    // ========== 兼容性方法（用于向后兼容） ==========

    /**
     * @deprecated 使用 {@link #getCustomerId()} 替代
     */
    @Deprecated
    public String getUid() {
        return customerId != null ? customerId : uid;
    }

    /**
     * @deprecated 使用 {@link #setCustomerId(String)} 替代
     */
    @Deprecated
    public void setUid(String uid) {
        this.uid = uid;
        if (this.customerId == null) {
            this.customerId = uid;
        }
    }

    /**
     * @deprecated 新接口不再返回VIP状态，默认返回0
     */
    @Deprecated
    public int getUserVip() {
        return userVip;
    }

    /**
     * @deprecated 新接口不再返回VIP状态
     */
    @Deprecated
    public void setUserVip(int userVip) {
        this.userVip = userVip;
    }

    /**
     * @deprecated 使用 {@link #getUserToken()} 替代
     */
    @Deprecated
    public String getAccessToken() {
        return userToken != null ? userToken : accessToken;
    }

    /**
     * @deprecated 使用 {@link #setUserToken(String)} 替代
     */
    @Deprecated
    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
        if (this.userToken == null) {
            this.userToken = accessToken;
        }
    }

    /**
     * @deprecated 新接口不再返回过期时间，默认返回0
     */
    @Deprecated
    public long getExpiresAt() {
        return expiresAt;
    }

    /**
     * @deprecated 新接口不再返回过期时间
     */
    @Deprecated
    public void setExpiresAt(long expiresAt) {
        this.expiresAt = expiresAt;
    }

    /**
     * @deprecated 新接口不再返回手机号
     */
    @Deprecated
    public String getPhoneNumber() {
        return phoneNumber;
    }

    /**
     * @deprecated 新接口不再返回手机号
     */
    @Deprecated
    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    /**
     * @deprecated 使用 {@link #getCustomerName()} 替代
     */
    @Deprecated
    public String getNickname() {
        return customerName != null ? customerName : nickname;
    }

    /**
     * @deprecated 使用 {@link #setCustomerName(String)} 替代
     */
    @Deprecated
    public void setNickname(String nickname) {
        this.nickname = nickname;
        if (this.customerName == null) {
            this.customerName = nickname;
        }
    }

    /**
     * @deprecated 新接口不再返回头像URL
     */
    @Deprecated
    public String getAvatar() {
        return avatar;
    }

    /**
     * @deprecated 新接口不再返回头像URL
     */
    @Deprecated
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    // ========== 便利方法 ==========

    /**
     * 检查用户token是否有效
     * @return 是否有效
     */
    public boolean isTokenValid() {
        return userToken != null && !userToken.trim().isEmpty();
    }

    /**
     * 获取用户显示名称
     * @return 显示名称
     */
    public String getDisplayName() {
        if (customerName != null && !customerName.trim().isEmpty()) {
            return customerName;
        }
        if (customerId != null && !customerId.trim().isEmpty()) {
            return "User " + customerId;
        }
        return "Unknown User";
    }

    /**
     * 检查用户信息是否完整
     * @return 是否完整
     */
    public boolean isUserInfoComplete() {
        return customerId != null && !customerId.trim().isEmpty() &&
               userToken != null && !userToken.trim().isEmpty();
    }

    /**
     * 获取登录类型描述
     * @return 登录类型描述
     */
    public String getLoginTypeDescription() {
        switch (loginType) {
            case 1:
                return "Phone Login";
            case 2:
                return "VKontakte Login";
            case 3:
                return "TikTok Login";
            default:
                return "Unknown Login Type";
        }
    }

    // ========== 兼容性便利方法 ==========

    /**
     * @deprecated 新接口不再返回VIP状态，默认返回false
     * 检查用户是否为VIP
     * @return 是否为VIP
     */
    @Deprecated
    public boolean isVip() {
        return userVip == 1;
    }

    /**
     * @deprecated 新接口不再返回过期时间，无法判断是否即将过期
     * 检查令牌是否即将过期（1小时内）
     * @return 是否即将过期
     */
    @Deprecated
    public boolean isTokenExpiringSoon() {
        if (expiresAt <= 0) {
            return false; // 新接口不提供过期时间
        }
        long oneHourInMillis = 60 * 60 * 1000L;
        return expiresAt - System.currentTimeMillis() < oneHourInMillis;
    }

    /**
     * @deprecated 新接口不再返回VIP状态，默认返回"Regular User"
     * 获取VIP状态描述
     * @return VIP状态描述
     */
    @Deprecated
    public String getVipStatusDescription() {
        return isVip() ? "VIP Member" : "Regular User";
    }

    @Override
    public String toString() {
        return "LoginResponseModel{" +
                "customerId='" + customerId + '\'' +
                ", customerName='" + customerName + '\'' +
                ", userToken='" + (userToken != null ? "***" : "null") + '\'' +
                ", loginType=" + loginType + " (" + getLoginTypeDescription() + ")" +
                ", deviceId='" + deviceId + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        LoginResponseModel that = (LoginResponseModel) obj;
        
        if (userVip != that.userVip) return false;
        if (expiresAt != that.expiresAt) return false;
        if (uid != null ? !uid.equals(that.uid) : that.uid != null) return false;
        if (accessToken != null ? !accessToken.equals(that.accessToken) : that.accessToken != null) return false;
        if (phoneNumber != null ? !phoneNumber.equals(that.phoneNumber) : that.phoneNumber != null) return false;
        if (nickname != null ? !nickname.equals(that.nickname) : that.nickname != null) return false;
        return avatar != null ? avatar.equals(that.avatar) : that.avatar == null;
    }

    @Override
    public int hashCode() {
        int result = uid != null ? uid.hashCode() : 0;
        result = 31 * result + userVip;
        result = 31 * result + (accessToken != null ? accessToken.hashCode() : 0);
        result = 31 * result + (int) (expiresAt ^ (expiresAt >>> 32));
        result = 31 * result + (phoneNumber != null ? phoneNumber.hashCode() : 0);
        result = 31 * result + (nickname != null ? nickname.hashCode() : 0);
        result = 31 * result + (avatar != null ? avatar.hashCode() : 0);
        return result;
    }
}
