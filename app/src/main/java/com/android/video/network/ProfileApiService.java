package com.android.video.network;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.android.video.config.EnvironmentConfigUtils;
import com.android.video.constants.BaseApiConstantsUtils;
import com.android.video.constants.ProfileApiConstantsUtils;
import com.android.video.model.UserInfo;
import com.android.video.model.FeedbackType;
import com.android.video.utils.ApiHeaderUtils;
import com.android.video.utils.JsonDataValidator;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

/**
 * 个人中心API服务类
 * <p>
 * 提供个人中心相关的网络请求功能，包括获取用户信息等。
 * 使用OkHttp进行网络请求，支持异步回调。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see ProfileApiConstantsUtils
 * @see UserInfo
 */
public class ProfileApiService {

    private static final String TAG = "ProfileApiService";
    private static final MediaType JSON = MediaType.get("application/json; charset=utf-8");

    private final OkHttpClient client;
    private final Handler mainHandler;
    private final ExecutorService executor;

    /**
     * 用户信息获取回调接口
     */
    public interface UserInfoCallback {
        /**
         * 获取用户信息成功
         * @param userInfo 用户信息对象
         */
        void onSuccess(UserInfo userInfo);

        /**
         * 获取用户信息失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 构造方法
     */
    public ProfileApiService() {
        OkHttpClient httpClient;
        try {
            httpClient = ApiClientUtils.getHttpClient();
        } catch (IllegalStateException e) {
            // 如果ApiClientUtils未初始化，使用默认配置
            Log.w(TAG, "ApiClientUtils not initialized, using default OkHttpClient");
            httpClient = new OkHttpClient();
        }
        this.client = httpClient;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.executor = Executors.newCachedThreadPool();
    }

    /**
     * 获取用户信息
     * <p>
     * 异步获取当前登录用户的详细信息。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getUserInfo(UserInfoCallback callback) {
        getUserInfo(callback, true); // 默认使用GET方法
    }

    /**
     * 获取用户信息（指定HTTP方法）
     * <p>
     * 异步获取当前登录用户的详细信息，可以指定使用GET或POST方法。
     * </p>
     *
     * @param callback 回调接口
     * @param useGetMethod 是否使用GET方法，false则使用POST方法
     */
    public void getUserInfo(UserInfoCallback callback, boolean useGetMethod) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_GET_USER_INFO
        );

        // 构建请求 - 根据参数选择HTTP方法（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder().url(apiUrl);

        if (useGetMethod) {
            requestBuilder.get();
            Log.d(TAG, "使用GET方法请求用户信息");
        } else {
            requestBuilder.post(RequestBody.create("", JSON));
            Log.d(TAG, "使用POST方法请求用户信息");
        }

        Request request = requestBuilder.build();

        Log.d(TAG, "发送用户信息请求: " + apiUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "网络请求失败", e);
                mainHandler.post(() -> callback.onError("网络请求失败: " + e.getMessage()));
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "=== 原始API响应 ===");
                    Log.d(TAG, "HTTP状态码: " + response.code());
                    Log.d(TAG, "响应体长度: " + responseBody.length());
                    Log.d(TAG, "响应内容: " + responseBody);
                    Log.d(TAG, "==================");

                    if (response.isSuccessful()) {
                        // 先检查原始JSON结构
                        debugRawJsonStructure(responseBody);

                        // 解析响应数据
                        UserInfo userInfo = parseUserInfoResponse(responseBody);
                        if (userInfo != null) {
                            mainHandler.post(() -> callback.onSuccess(userInfo));
                        } else {
                            mainHandler.post(() -> callback.onError("响应数据解析失败"));
                        }
                    } else {
                        mainHandler.post(() -> callback.onError("服务器错误: " + response.code()));
                    }
                } catch (Exception e) {
                    Log.e(TAG, "响应处理失败", e);
                    mainHandler.post(() -> callback.onError("响应处理失败: " + e.getMessage()));
                }
            }
        });
    }

    /**
     * 调试原始JSON结构
     *
     * @param responseBody 响应体JSON字符串
     */
    private void debugRawJsonStructure(String responseBody) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);
            Log.d(TAG, "=== JSON结构调试 ===");
            Log.d(TAG, "顶级字段: " + jsonResponse.keys().toString());

            String code = jsonResponse.optString("code", "未找到");
            Log.d(TAG, "响应码: " + code);

            String message = jsonResponse.optString("message", "未找到");
            Log.d(TAG, "响应消息: " + message);

            if (jsonResponse.has("data")) {
                JSONObject data = jsonResponse.optJSONObject("data");
                if (data != null) {
                    Log.d(TAG, "data字段存在，包含字段: " + data.keys().toString());

                    // 检查关键的VIP字段
                    Log.d(TAG, "--- VIP相关字段原始值 ---");
                    Log.d(TAG, "isVip原始值: " + data.opt("isVip") + " (类型: " +
                        (data.opt("isVip") != null ? data.opt("isVip").getClass().getSimpleName() : "null") + ")");
                    Log.d(TAG, "vipDays原始值: " + data.opt("vipDays") + " (类型: " +
                        (data.opt("vipDays") != null ? data.opt("vipDays").getClass().getSimpleName() : "null") + ")");
                    Log.d(TAG, "vipExpireDate原始值: " + data.opt("vipExpireDate") + " (类型: " +
                        (data.opt("vipExpireDate") != null ? data.opt("vipExpireDate").getClass().getSimpleName() : "null") + ")");
                    Log.d(TAG, "points原始值: " + data.opt("points") + " (类型: " +
                        (data.opt("points") != null ? data.opt("points").getClass().getSimpleName() : "null") + ")");
                } else {
                    Log.w(TAG, "data字段为null");
                }
            } else {
                Log.w(TAG, "响应中没有data字段");
            }
            Log.d(TAG, "==================");
        } catch (Exception e) {
            Log.e(TAG, "调试JSON结构时发生错误", e);
        }
    }

    /**
     * 解析用户信息API响应
     *
     * @param responseBody 响应体JSON字符串
     * @return 解析后的用户信息对象，解析失败返回null
     */
    private UserInfo parseUserInfoResponse(String responseBody) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "API返回错误码: " + code);
                return null;
            }

            // 获取数据部分
            JSONObject data = jsonResponse.optJSONObject(BaseApiConstantsUtils.FIELD_DATA);
            if (data == null) {
                Log.e(TAG, "响应中没有data字段");
                return null;
            }

            // 解析用户信息 - 使用安全验证
            UserInfo userInfo = new UserInfo();

            // 基本信息 - 使用安全字符串获取
            userInfo.setCustomerId(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_CUSTOMER_ID, ""));
            userInfo.setCustomerName(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_CUSTOMER_NAME, ""));
            userInfo.setPhoneNumber(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_PHONE_NUMBER, ""));
            userInfo.setHeadImg(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_HEAD_IMG, null));

            // 时间信息 - 使用安全字符串获取
            userInfo.setRegistrationTime(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_REGISTRATION_TIME, null));
            userInfo.setLastLoginTime(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_LAST_LOGIN_TIME, null));
            userInfo.setCreateTime(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_CREATE_TIME, null));
            userInfo.setUpdateTime(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_UPDATE_TIME, null));

            // 账户信息 - 使用安全整数获取
            userInfo.setRegistrationType(JsonDataValidator.getSafeInt(data, ProfileApiConstantsUtils.FIELD_REGISTRATION_TYPE, 1, 1, 3));
            userInfo.setIsDelete(JsonDataValidator.getSafeInt(data, ProfileApiConstantsUtils.FIELD_IS_DELETE, 1, 0, 1));
            userInfo.setStatus(JsonDataValidator.getSafeInt(data, ProfileApiConstantsUtils.FIELD_STATUS, 1, 0, 10));

            // 第三方登录信息 - 使用安全字符串获取
            userInfo.setFacebookId(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_FACEBOOK_ID, null));
            userInfo.setTiktokId(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_TIKTOK_ID, null));

            // 设备和令牌信息 - 使用安全字符串获取
            userInfo.setDeviceId(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_DEVICE_ID, ""));
            userInfo.setUserToken(JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_USER_TOKEN, ""));

            // VIP和积分信息 - 使用安全验证
            userInfo.setPoints(JsonDataValidator.getSafePoints(data, ProfileApiConstantsUtils.FIELD_POINTS));

            // VIP状态 - 使用安全验证
            Integer isVipValue = JsonDataValidator.getSafeVipStatus(data, ProfileApiConstantsUtils.FIELD_IS_VIP);
            userInfo.setIsVip(isVipValue);
            Log.d(TAG, "API返回isVip（验证后）: " + isVipValue);

            // VIP天数 - 使用安全验证
            Integer vipDaysValue = JsonDataValidator.getSafeVipDays(data, ProfileApiConstantsUtils.FIELD_VIP_DAYS);
            userInfo.setVipDays(vipDaysValue);
            Log.d(TAG, "API返回vipDays（验证后）: " + vipDaysValue);

            // VIP到期日期 - 使用安全验证
            String vipExpireDateValue = JsonDataValidator.getSafeString(data, ProfileApiConstantsUtils.FIELD_VIP_EXPIRE_DATE, null);
            if (vipExpireDateValue != null && !JsonDataValidator.isValidDateString(vipExpireDateValue)) {
                Log.w(TAG, "VIP到期日期格式无效: " + vipExpireDateValue);
                vipExpireDateValue = null;
            }
            userInfo.setVipExpireDate(vipExpireDateValue);
            Log.d(TAG, "API返回vipExpireDate（验证后）: " + vipExpireDateValue);

            // 语言类型 - 使用安全验证
            Integer languageTypeValue = null;
            if (!data.isNull(ProfileApiConstantsUtils.FIELD_LANGUAGE_TYPE)) {
                languageTypeValue = JsonDataValidator.getSafeInt(data, ProfileApiConstantsUtils.FIELD_LANGUAGE_TYPE, 1, 1, 10);
            }
            userInfo.setLanguageType(languageTypeValue);

            Log.d(TAG, "用户信息解析成功: " + userInfo.getCustomerName());

            // 记录验证摘要
            JsonDataValidator.logValidationSummary(data,
                "用户: " + userInfo.getCustomerName() +
                ", VIP: " + userInfo.isVipUser() +
                ", 天数: " + userInfo.getVipDays() +
                ", 积分: " + userInfo.getPoints());

            return userInfo;

        } catch (JSONException e) {
            Log.e(TAG, "JSON解析失败", e);
            return null;
        }
    }

    /**
     * 释放资源
     */
    public void release() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
        }
    }

    /**
     * 语言切换回调接口
     */
    public interface LanguageChangeCallback {
        /**
         * 语言切换成功
         */
        void onSuccess();

        /**
         * 语言切换失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 切换用户语言设置
     * <p>
     * 异步切换用户的语言偏好设置。
     * </p>
     *
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     * @param callback 回调接口
     */
    public void changeLanguage(int languageType, LanguageChangeCallback callback) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_CHANGE_LANGUAGE
        );

        // 构建POST请求体
        JSONObject requestJson = new JSONObject();
        try {
            requestJson.put(ProfileApiConstantsUtils.PARAM_LANGUAGE_TYPE, languageType);
        } catch (JSONException e) {
            Log.e(TAG, "构建请求JSON失败", e);
            if (callback != null) {
                callback.onError("请求参数构建失败");
            }
            return;
        }

        RequestBody requestBody = RequestBody.create(requestJson.toString(), JSON);

        // 构建POST请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "发送语言切换请求: " + apiUrl);
        Log.d(TAG, "请求体: " + requestJson.toString());

        // 异步执行请求
        executor.execute(() -> {
            try {
                Response response = client.newCall(request).execute();
                String responseBody = response.body() != null ? response.body().string() : "";

                Log.d(TAG, "语言切换响应: " + responseBody);

                if (response.isSuccessful()) {
                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    String code = jsonResponse.optString("code", "");

                    if ("200".equals(code)) {
                        // 成功
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onSuccess();
                            }
                        });
                    } else {
                        // API返回错误
                        String message = jsonResponse.optString("message", "语言切换失败");
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError(message);
                            }
                        });
                    }
                } else {
                    // HTTP错误
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + response.code());
                        }
                    });
                }
            } catch (Exception e) {
                Log.e(TAG, "语言切换请求异常", e);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("请求异常: " + e.getMessage());
                    }
                });
            }
        });
    }

    /**
     * 协议信息获取回调接口
     */
    public interface InformationCallback {
        /**
         * 获取协议信息成功
         * @param informationName 信息名称
         * @param informationDetails 信息详情
         */
        void onSuccess(String informationName, String informationDetails);

        /**
         * 获取协议信息失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 获取协议相关信息
     * <p>
     * 异步获取设置页面的协议相关信息，如关于我们、隐私政策、用户协议等。
     * </p>
     *
     * @param informationId 信息ID
     * @param callback 回调接口
     */
    public void getInformation(String informationId, InformationCallback callback) {
        // 参数验证
        if (informationId == null || informationId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("信息ID不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_GET_INFORMATION
        );

        // 添加查询参数
        apiUrl += "?" + ProfileApiConstantsUtils.PARAM_INFORMATION_ID + "=" + informationId;

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "发送协议信息请求: " + apiUrl);
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "协议信息请求失败", e);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("网络请求失败: " + e.getMessage());
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "=== 协议信息API响应 ===");
                    Log.d(TAG, "HTTP状态码: " + response.code());
                    Log.d(TAG, "响应体长度: " + responseBody.length());
                    Log.d(TAG, "响应内容: " + responseBody);
                    Log.d(TAG, "==================");

                    if (response.isSuccessful()) {
                        // 解析响应数据
                        parseInformationResponse(responseBody, callback);
                    } else {
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("服务器错误: " + response.code());
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "协议信息响应处理失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应处理失败: " + e.getMessage());
                        }
                    });
                }
            }
        });
    }

    /**
     * 解析协议信息API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseInformationResponse(String responseBody, InformationCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "协议信息API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "获取协议信息失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            // 获取数据部分
            JSONObject data = jsonResponse.optJSONObject(BaseApiConstantsUtils.FIELD_DATA);
            if (data == null) {
                Log.e(TAG, "协议信息响应中没有data字段");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("响应数据格式错误");
                    }
                });
                return;
            }

            // 解析协议信息
            String informationName = data.optString(ProfileApiConstantsUtils.FIELD_INFORMATION_NAME, "");
            String informationDetails = data.optString(ProfileApiConstantsUtils.FIELD_INFORMATION_DETAILS, "");

            Log.d(TAG, "协议信息解析成功: " + informationName);

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess(informationName, informationDetails);
                }
            });

        } catch (JSONException e) {
            Log.e(TAG, "协议信息JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 个人信息更新回调接口
     */
    public interface UpdateProfileCallback {
        /**
         * 个人信息更新成功
         */
        void onSuccess();

        /**
         * 个人信息更新失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 更新个人信息
     * <p>
     * 异步更新用户的个人信息，包括昵称、手机号、头像等。
     * </p>
     *
     * @param customerName 用户昵称
     * @param phoneNumber 手机号
     * @param headImg 头像URL（可选）
     * @param callback 回调接口
     */
    public void updateMyInformation(String customerName, String phoneNumber, String headImg, UpdateProfileCallback callback) {
        // 参数验证
        if (customerName == null || customerName.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("用户昵称不能为空");
            }
            return;
        }

        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("手机号不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_UPDATE_MY_INFORMATION
        );

        // 构建POST请求体
        JSONObject requestJson = new JSONObject();
        try {
            requestJson.put(ProfileApiConstantsUtils.PARAM_CUSTOMER_NAME, customerName.trim());
            requestJson.put(ProfileApiConstantsUtils.PARAM_PHONE_NUMBER, phoneNumber.trim());
            if (headImg != null && !headImg.trim().isEmpty()) {
                requestJson.put(ProfileApiConstantsUtils.PARAM_HEAD_IMG, headImg.trim());
            }
        } catch (JSONException e) {
            Log.e(TAG, "构建个人信息更新请求JSON失败", e);
            if (callback != null) {
                callback.onError("请求参数构建失败");
            }
            return;
        }

        RequestBody requestBody = RequestBody.create(requestJson.toString(), JSON);

        // 构建POST请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "发送个人信息更新请求: " + apiUrl);
        Log.d(TAG, "请求体: " + requestJson.toString());

        // 异步执行请求
        client.newCall(request).enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                Log.e(TAG, "个人信息更新请求失败", e);
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("网络请求失败: " + e.getMessage());
                    }
                });
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                try {
                    String responseBody = response.body().string();
                    Log.d(TAG, "=== 个人信息更新API响应 ===");
                    Log.d(TAG, "HTTP状态码: " + response.code());
                    Log.d(TAG, "响应体长度: " + responseBody.length());
                    Log.d(TAG, "响应内容: " + responseBody);
                    Log.d(TAG, "==================");

                    if (response.isSuccessful()) {
                        // 解析响应数据
                        parseUpdateProfileResponse(responseBody, callback);
                    } else {
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("服务器错误: " + response.code());
                            }
                        });
                    }
                } catch (Exception e) {
                    Log.e(TAG, "个人信息更新响应处理失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应处理失败: " + e.getMessage());
                        }
                    });
                }
            }
        });
    }

    /**
     * 解析个人信息更新API响应
     *
     * @param responseBody 响应体JSON字符串
     * @param callback 回调接口
     */
    private void parseUpdateProfileResponse(String responseBody, UpdateProfileCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString(BaseApiConstantsUtils.FIELD_CODE);
            if (!BaseApiConstantsUtils.isSuccessCode(code)) {
                Log.e(TAG, "个人信息更新API返回错误码: " + code);
                String message = jsonResponse.optString(BaseApiConstantsUtils.FIELD_MESSAGE, "个人信息更新失败");
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError(message);
                    }
                });
                return;
            }

            Log.d(TAG, "个人信息更新成功");

            // 回调成功结果
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onSuccess();
                }
            });

        } catch (JSONException e) {
            Log.e(TAG, "个人信息更新JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }



    /**
     * 用户注销回调接口
     */
    public interface UnsubscribeCallback {
        /**
         * 注销成功
         */
        void onSuccess();

        /**
         * 注销失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 用户注销
     * <p>
     * 异步注销用户账户，删除用户的所有数据和权限。
     * </p>
     *
     * @param callback 回调接口
     */
    public void unsubscribe(UnsubscribeCallback callback) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_UNSUBSCRIBE
        );

        // 构建POST请求（空请求体，不手动添加token，由HeaderInterceptor统一处理）
        RequestBody requestBody = RequestBody.create("", JSON);
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 用户注销请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "用户注销请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 用户注销API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseUnsubscribeResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "用户注销响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析用户注销响应
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseUnsubscribeResponse(String responseBody, UnsubscribeCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString("code", "");
            String message = jsonResponse.optString("message", "");
            String data = jsonResponse.optString("data", "");

            Log.d(TAG, "注销响应解析 - code: " + code + ", message: " + message + ", data: " + data);

            if ("200".equals(code)) {
                // 注销成功
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                });
            } else {
                // 注销失败
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("注销失败: " + message);
                    }
                });
            }

        } catch (JSONException e) {
            Log.e(TAG, "用户注销JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 反馈类型获取回调接口
     */
    public interface FeedbackTypeCallback {
        /**
         * 获取反馈类型成功
         * @param feedbackTypes 反馈类型列表
         */
        void onSuccess(List<FeedbackType> feedbackTypes);

        /**
         * 获取反馈类型失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 获取反馈类型列表
     * <p>
     * 异步获取反馈页面的反馈类型列表。
     * </p>
     *
     * @param callback 回调接口
     */
    public void getFeedbackTypes(FeedbackTypeCallback callback) {
        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_GET_FEEDBACK_TYPE
        );

        // 构建GET请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .get();

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 获取反馈类型请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: GET");
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "获取反馈类型请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 反馈类型API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseFeedbackTypesResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "反馈类型响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析反馈类型响应
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseFeedbackTypesResponse(String responseBody, FeedbackTypeCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString("code", "");
            String message = jsonResponse.optString("message", "");

            Log.d(TAG, "反馈类型响应解析 - code: " + code + ", message: " + message);

            if ("200".equals(code)) {
                // 解析data数组
                JSONArray dataArray = jsonResponse.optJSONArray("data");
                if (dataArray != null) {
                    List<FeedbackType> feedbackTypes = new ArrayList<>();

                    for (int i = 0; i < dataArray.length(); i++) {
                        JSONObject typeObject = dataArray.getJSONObject(i);

                        String feedbackTypeId = typeObject.optString(ProfileApiConstantsUtils.FIELD_FEEDBACK_TYPE_ID, "");
                        String feedbackTypeName = typeObject.optString(ProfileApiConstantsUtils.FIELD_FEEDBACK_TYPE_NAME, "");
                        int isDelete = typeObject.optInt(ProfileApiConstantsUtils.FIELD_IS_DELETE, 1);
                        String createTime = typeObject.optString(ProfileApiConstantsUtils.FIELD_CREATE_TIME, "");
                        String updateTime = typeObject.optString(ProfileApiConstantsUtils.FIELD_UPDATE_TIME, "");

                        // 只添加未删除的反馈类型
                        if (isDelete == 1) {
                            FeedbackType feedbackType = new FeedbackType(feedbackTypeId, feedbackTypeName, isDelete, createTime, updateTime);
                            feedbackTypes.add(feedbackType);

                            Log.d(TAG, "解析反馈类型: " + feedbackTypeName + " (ID: " + feedbackTypeId + ")");
                        }
                    }

                    // 默认选中第一个类型
                    if (!feedbackTypes.isEmpty()) {
                        feedbackTypes.get(0).setSelected(true);
                    }

                    Log.d(TAG, "成功解析 " + feedbackTypes.size() + " 个反馈类型");

                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onSuccess(feedbackTypes);
                        }
                    });
                } else {
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("响应数据格式错误：缺少data字段");
                        }
                    });
                }
            } else {
                // 获取失败
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("获取反馈类型失败: " + message);
                    }
                });
            }

        } catch (JSONException e) {
            Log.e(TAG, "反馈类型JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 反馈提交回调接口
     */
    public interface SubmitFeedbackCallback {
        /**
         * 反馈提交成功
         */
        void onSuccess();

        /**
         * 反馈提交失败
         * @param errorMessage 错误信息
         */
        void onError(String errorMessage);
    }

    /**
     * 提交反馈
     * <p>
     * 异步提交用户反馈信息。
     * </p>
     *
     * @param feedbackTypeId 反馈类型ID
     * @param feedbackDescription 反馈内容详情
     * @param feedbackPictures 图片URL，多张图片用逗号隔开（可选）
     * @param contactInformation 联系方式（可选）
     * @param callback 回调接口
     */
    public void submitFeedback(String feedbackTypeId, String feedbackDescription,
                              String feedbackPictures, String contactInformation,
                              SubmitFeedbackCallback callback) {
        // 参数验证
        if (feedbackTypeId == null || feedbackTypeId.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("反馈类型ID不能为空");
            }
            return;
        }

        if (feedbackDescription == null || feedbackDescription.trim().isEmpty()) {
            if (callback != null) {
                callback.onError("反馈内容不能为空");
            }
            return;
        }

        // 构建API URL
        String apiUrl = BaseApiConstantsUtils.buildApiUrl(
            EnvironmentConfigUtils.getApiBaseUrl(),
            ProfileApiConstantsUtils.API_SUBMIT_FEEDBACK
        );

        // 构建POST请求体
        JSONObject requestJson = new JSONObject();
        try {
            requestJson.put(ProfileApiConstantsUtils.PARAM_FEEDBACK_TYPE_ID, feedbackTypeId.trim());
            requestJson.put(ProfileApiConstantsUtils.PARAM_FEEDBACK_DESCRIPTION, feedbackDescription.trim());

            // 可选参数
            if (feedbackPictures != null && !feedbackPictures.trim().isEmpty()) {
                requestJson.put(ProfileApiConstantsUtils.PARAM_FEEDBACK_PICTURES, feedbackPictures.trim());
            }

            if (contactInformation != null && !contactInformation.trim().isEmpty()) {
                requestJson.put(ProfileApiConstantsUtils.PARAM_CONTACT_INFORMATION, contactInformation.trim());
            }
        } catch (JSONException e) {
            Log.e(TAG, "构建反馈提交请求JSON失败", e);
            if (callback != null) {
                callback.onError("请求参数构建失败");
            }
            return;
        }

        RequestBody requestBody = RequestBody.create(requestJson.toString(), JSON);

        // 构建POST请求（不手动添加token，由HeaderInterceptor统一处理）
        Request.Builder requestBuilder = new Request.Builder()
            .url(apiUrl)
            .post(requestBody);

        Request request = requestBuilder.build();

        Log.d(TAG, "=== 反馈提交请求 ===");
        Log.d(TAG, "请求URL: " + apiUrl);
        Log.d(TAG, "请求方法: POST");
        Log.d(TAG, "请求体: " + requestJson.toString());
        Log.d(TAG, "Token将由HeaderInterceptor自动添加");
        Log.d(TAG, "==================");

        // 异步执行请求
        executor.execute(() -> {
            client.newCall(request).enqueue(new Callback() {
                @Override
                public void onFailure(Call call, IOException e) {
                    Log.e(TAG, "反馈提交请求失败", e);
                    mainHandler.post(() -> {
                        if (callback != null) {
                            callback.onError("网络请求失败: " + e.getMessage());
                        }
                    });
                }

                @Override
                public void onResponse(Call call, Response response) throws IOException {
                    try {
                        String responseBody = response.body().string();
                        Log.d(TAG, "=== 反馈提交API响应 ===");
                        Log.d(TAG, "HTTP状态码: " + response.code());
                        Log.d(TAG, "响应体长度: " + responseBody.length());
                        Log.d(TAG, "响应内容: " + responseBody);
                        Log.d(TAG, "==================");

                        if (response.isSuccessful()) {
                            // 解析响应数据
                            parseSubmitFeedbackResponse(responseBody, callback);
                        } else {
                            mainHandler.post(() -> {
                                if (callback != null) {
                                    callback.onError("服务器错误: " + response.code());
                                }
                            });
                        }
                    } catch (Exception e) {
                        Log.e(TAG, "反馈提交响应处理失败", e);
                        mainHandler.post(() -> {
                            if (callback != null) {
                                callback.onError("响应处理失败: " + e.getMessage());
                            }
                        });
                    }
                }
            });
        });
    }

    /**
     * 解析反馈提交响应
     * @param responseBody 响应体
     * @param callback 回调接口
     */
    private void parseSubmitFeedbackResponse(String responseBody, SubmitFeedbackCallback callback) {
        try {
            JSONObject jsonResponse = new JSONObject(responseBody);

            // 检查响应码
            String code = jsonResponse.optString("code", "");
            String message = jsonResponse.optString("message", "");
            String data = jsonResponse.optString("data", "");

            Log.d(TAG, "反馈提交响应解析 - code: " + code + ", message: " + message + ", data: " + data);

            if ("200".equals(code)) {
                // 提交成功
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onSuccess();
                    }
                });
            } else {
                // 提交失败
                mainHandler.post(() -> {
                    if (callback != null) {
                        callback.onError("反馈提交失败: " + message);
                    }
                });
            }

        } catch (JSONException e) {
            Log.e(TAG, "反馈提交JSON解析失败", e);
            mainHandler.post(() -> {
                if (callback != null) {
                    callback.onError("数据解析失败: " + e.getMessage());
                }
            });
        }
    }

    /**
     * 获取单例实例
     */
    private static ProfileApiService instance;

    public static synchronized ProfileApiService getInstance() {
        if (instance == null) {
            instance = new ProfileApiService();
        }
        return instance;
    }
}
