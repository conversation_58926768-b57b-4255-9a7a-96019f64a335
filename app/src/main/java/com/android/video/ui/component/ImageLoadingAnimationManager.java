package com.android.video.ui.component;

import android.animation.Animator;
import android.animation.AnimatorListenerAdapter;
import android.animation.ObjectAnimator;
import android.animation.ValueAnimator;
import android.content.Context;
import android.graphics.Canvas;
import android.graphics.Color;
import android.graphics.LinearGradient;
import android.graphics.Matrix;
import android.graphics.Paint;
import android.graphics.Shader;
import android.graphics.drawable.Drawable;
import android.view.View;
import android.view.ViewGroup;
import android.view.animation.AccelerateDecelerateInterpolator;
import android.view.animation.DecelerateInterpolator;
import android.view.animation.LinearInterpolator;
import android.widget.ImageView;

import com.bumptech.glide.Glide;
import com.bumptech.glide.load.DataSource;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.bumptech.glide.load.engine.GlideException;
import com.bumptech.glide.request.RequestListener;
import com.bumptech.glide.request.target.Target;
import androidx.annotation.Nullable;

/**
 * 图片加载动画管理器
 * 提供多种图片加载动画效果，优化用户体验
 * <AUTHOR> Team
 */
public class ImageLoadingAnimationManager {
    
    private static final String TAG = "ImageLoadingAnimationManager";
    
    // 动画持续时间
    private static final int FADE_DURATION = 300;
    private static final int SCALE_DURATION = 400;
    private static final int SLIDE_DURATION = 350;
    private static final int SHIMMER_DURATION = 1500; // 1.5秒/次的横向流动动画

    // 骨架屏配置
    private static final float DEFAULT_SKELETON_ALPHA = 0.3f;
    private static final float SKELETON_SHIMMER_ALPHA = 0.7f;
    private static final int SKELETON_COLOR = Color.parseColor("#E0E0E0"); // 灰色渐变填充
    
    /**
     * 动画类型枚举
     */
    public enum AnimationType {
        FADE_IN,        // 淡入动画
        SCALE_IN,       // 缩放动画
        SLIDE_UP,       // 向上滑动
        SLIDE_DOWN,     // 向下滑动
        ZOOM_IN,        // 放大动画
        SHIMMER,        // 闪烁动画
        NONE            // 无动画
    }
    
    /**
     * 加载图片并应用动画
     */
    public static void loadImageWithAnimation(Context context, String imageUrl, ImageView imageView, 
                                            AnimationType animationType) {
        loadImageWithAnimation(context, imageUrl, imageView, animationType, null);
    }
    
    /**
     * 加载图片并应用动画（带占位图）
     */
    public static void loadImageWithAnimation(Context context, String imageUrl, ImageView imageView, 
                                            AnimationType animationType, Integer placeholderResId) {
        if (context == null || imageView == null) {
            return;
        }
        
        // 设置初始状态
        prepareImageViewForAnimation(imageView, animationType);
        
        // 构建Glide请求
        var glideRequest = Glide.with(context)
                .load(imageUrl)
                .diskCacheStrategy(DiskCacheStrategy.ALL)
                .listener(new RequestListener<Drawable>() {
                    @Override
                    public boolean onLoadFailed(@Nullable GlideException e, Object model, 
                                              Target<Drawable> target, boolean isFirstResource) {
                        // 加载失败时恢复ImageView状态
                        resetImageViewState(imageView);
                        return false;
                    }
                    
                    @Override
                    public boolean onResourceReady(Drawable resource, Object model,
                                                 Target<Drawable> target, DataSource dataSource,
                                                 boolean isFirstResource) {
                        // 只有从网络加载时才应用动画
                        if (dataSource != DataSource.MEMORY_CACHE && dataSource != DataSource.DATA_DISK_CACHE) {
                            applyLoadingAnimationInternal(imageView, animationType);
                        } else {
                            resetImageViewState(imageView);
                        }
                        return false;
                    }
                });
        
        // 设置占位图
        if (placeholderResId != null) {
            glideRequest = glideRequest.placeholder(placeholderResId).error(placeholderResId);
        }
        
        // 应用自定义过渡动画
        // 注释掉自定义transition，使用我们自己的动画系统
        // if (animationType != AnimationType.NONE) {
        //     glideRequest = glideRequest.transition(createCustomTransition(animationType));
        // }
        
        glideRequest.into(imageView);
    }
    
    /**
     * 为动画准备ImageView的初始状态
     */
    private static void prepareImageViewForAnimation(ImageView imageView, AnimationType animationType) {
        switch (animationType) {
            case FADE_IN:
                imageView.setAlpha(0f);
                break;
            case SCALE_IN:
            case ZOOM_IN:
                imageView.setScaleX(0.8f);
                imageView.setScaleY(0.8f);
                imageView.setAlpha(0f);
                break;
            case SLIDE_UP:
                imageView.setTranslationY(50f);
                imageView.setAlpha(0f);
                break;
            case SLIDE_DOWN:
                imageView.setTranslationY(-50f);
                imageView.setAlpha(0f);
                break;
            case SHIMMER:
                imageView.setAlpha(0.3f);
                break;
            case NONE:
            default:
                // 无需特殊准备
                break;
        }
    }
    
    /**
     * 应用加载完成后的动画（公共方法）
     */
    public static void applyLoadingAnimation(ImageView imageView, AnimationType animationType) {
        applyLoadingAnimationInternal(imageView, animationType);
    }

    /**
     * 应用加载完成后的动画（内部方法）
     */
    private static void applyLoadingAnimationInternal(ImageView imageView, AnimationType animationType) {
        switch (animationType) {
            case FADE_IN:
                animateFadeIn(imageView);
                break;
            case SCALE_IN:
                animateScaleIn(imageView);
                break;
            case SLIDE_UP:
                animateSlideUp(imageView);
                break;
            case SLIDE_DOWN:
                animateSlideDown(imageView);
                break;
            case ZOOM_IN:
                animateZoomIn(imageView);
                break;
            case SHIMMER:
                animateShimmer(imageView);
                break;
            case NONE:
            default:
                resetImageViewState(imageView);
                break;
        }
    }
    
    /**
     * 淡入动画
     */
    private static void animateFadeIn(ImageView imageView) {
        ObjectAnimator fadeIn = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f);
        fadeIn.setDuration(FADE_DURATION);
        fadeIn.setInterpolator(new DecelerateInterpolator());
        fadeIn.start();
    }
    
    /**
     * 缩放动画
     */
    private static void animateScaleIn(ImageView imageView) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(imageView, "scaleX", 0.8f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(imageView, "scaleY", 0.8f, 1f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f);
        
        scaleX.setDuration(SCALE_DURATION);
        scaleY.setDuration(SCALE_DURATION);
        alpha.setDuration(SCALE_DURATION);
        
        AccelerateDecelerateInterpolator interpolator = new AccelerateDecelerateInterpolator();
        scaleX.setInterpolator(interpolator);
        scaleY.setInterpolator(interpolator);
        alpha.setInterpolator(interpolator);
        
        scaleX.start();
        scaleY.start();
        alpha.start();
    }
    
    /**
     * 向上滑动动画
     */
    private static void animateSlideUp(ImageView imageView) {
        ObjectAnimator translateY = ObjectAnimator.ofFloat(imageView, "translationY", 50f, 0f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f);
        
        translateY.setDuration(SLIDE_DURATION);
        alpha.setDuration(SLIDE_DURATION);
        
        DecelerateInterpolator interpolator = new DecelerateInterpolator();
        translateY.setInterpolator(interpolator);
        alpha.setInterpolator(interpolator);
        
        translateY.start();
        alpha.start();
    }
    
    /**
     * 向下滑动动画
     */
    private static void animateSlideDown(ImageView imageView) {
        ObjectAnimator translateY = ObjectAnimator.ofFloat(imageView, "translationY", -50f, 0f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f);
        
        translateY.setDuration(SLIDE_DURATION);
        alpha.setDuration(SLIDE_DURATION);
        
        DecelerateInterpolator interpolator = new DecelerateInterpolator();
        translateY.setInterpolator(interpolator);
        alpha.setInterpolator(interpolator);
        
        translateY.start();
        alpha.start();
    }
    
    /**
     * 放大动画
     */
    private static void animateZoomIn(ImageView imageView) {
        ObjectAnimator scaleX = ObjectAnimator.ofFloat(imageView, "scaleX", 0.5f, 1.1f, 1f);
        ObjectAnimator scaleY = ObjectAnimator.ofFloat(imageView, "scaleY", 0.5f, 1.1f, 1f);
        ObjectAnimator alpha = ObjectAnimator.ofFloat(imageView, "alpha", 0f, 1f);
        
        scaleX.setDuration(SCALE_DURATION + 100);
        scaleY.setDuration(SCALE_DURATION + 100);
        alpha.setDuration(SCALE_DURATION);
        
        AccelerateDecelerateInterpolator interpolator = new AccelerateDecelerateInterpolator();
        scaleX.setInterpolator(interpolator);
        scaleY.setInterpolator(interpolator);
        alpha.setInterpolator(new DecelerateInterpolator());
        
        scaleX.start();
        scaleY.start();
        alpha.start();
    }
    
    /**
     * 闪烁动画
     */
    private static void animateShimmer(ImageView imageView) {
        ValueAnimator shimmer = ValueAnimator.ofFloat(0.3f, 1f, 0.7f, 1f);
        shimmer.setDuration(800);
        shimmer.setRepeatCount(1);
        shimmer.addUpdateListener(animation -> {
            float alpha = (Float) animation.getAnimatedValue();
            imageView.setAlpha(alpha);
        });
        shimmer.addListener(new AnimatorListenerAdapter() {
            @Override
            public void onAnimationEnd(Animator animation) {
                imageView.setAlpha(1f);
            }
        });
        shimmer.start();
    }
    
    /**
     * 重置ImageView状态
     */
    private static void resetImageViewState(ImageView imageView) {
        imageView.setAlpha(1f);
        imageView.setScaleX(1f);
        imageView.setScaleY(1f);
        imageView.setTranslationX(0f);
        imageView.setTranslationY(0f);
    }
    
    // 移除了createCustomTransition方法，因为我们使用自己的动画系统
    
    /**
     * 创建骨架屏效果
     */
    public static void createSkeletonEffect(ImageView imageView) {
        if (imageView == null) return;
        
        // 设置半透明背景
        imageView.setAlpha(0.3f);
        
        // 创建脉冲动画
        ValueAnimator pulseAnimator = ValueAnimator.ofFloat(0.3f, 0.7f);
        pulseAnimator.setDuration(1000);
        pulseAnimator.setRepeatCount(ValueAnimator.INFINITE);
        pulseAnimator.setRepeatMode(ValueAnimator.REVERSE);
        pulseAnimator.addUpdateListener(animation -> {
            float alpha = (Float) animation.getAnimatedValue();
            imageView.setAlpha(alpha);
        });
        
        // 将动画存储在tag中，以便后续取消
        imageView.setTag(pulseAnimator);
        pulseAnimator.start();
    }
    
    /**
     * 停止骨架屏效果
     */
    public static void stopSkeletonEffect(ImageView imageView) {
        if (imageView == null) return;

        Object tag = imageView.getTag();
        if (tag instanceof ValueAnimator) {
            ValueAnimator animator = (ValueAnimator) tag;
            animator.cancel();
            imageView.setTag(null);
        }

        // 恢复正常状态
        imageView.setAlpha(1f);
    }

    /**
     * 创建增强骨架屏效果（带横向流动光影）
     * @param view 目标视图
     * @param duration 动画持续时间（毫秒），默认1500ms
     */
    public static void createEnhancedSkeletonEffect(View view, int duration) {
        if (view == null) return;

        // 使用默认持续时间如果传入0或负数
        int animDuration = duration > 0 ? duration : SHIMMER_DURATION;

        // 设置背景色
        view.setBackgroundColor(SKELETON_COLOR);
        view.setAlpha(DEFAULT_SKELETON_ALPHA);

        // 创建shimmer drawable
        ShimmerDrawable shimmerDrawable = new ShimmerDrawable();
        view.setBackground(shimmerDrawable);

        // 启动shimmer动画
        shimmerDrawable.startShimmer(animDuration);

        // 将drawable存储在tag中，以便后续取消
        view.setTag(shimmerDrawable);
    }

    /**
     * 创建增强骨架屏效果（使用默认1.5秒持续时间）
     */
    public static void createEnhancedSkeletonEffect(View view) {
        createEnhancedSkeletonEffect(view, SHIMMER_DURATION);
    }

    /**
     * 停止增强骨架屏效果
     */
    public static void stopEnhancedSkeletonEffect(View view) {
        if (view == null) return;

        Object tag = view.getTag();
        if (tag instanceof ShimmerDrawable) {
            ShimmerDrawable shimmerDrawable = (ShimmerDrawable) tag;
            shimmerDrawable.stopShimmer();
            view.setTag(null);
        }

        // 恢复正常状态
        view.setBackground(null);
        view.setAlpha(1f);
    }

    /**
     * 创建视频卡片骨架屏布局
     * @param container 容器ViewGroup
     * @param coverRatio 封面骨架屏比例（宽高比）
     * @param titleLengthRatio 标题骨架屏长度比例（相对于最大长度的百分比，默认0.8即80%）
     */
    public static void createVideoCardSkeleton(ViewGroup container, float coverRatio, float titleLengthRatio) {
        if (container == null) return;

        Context context = container.getContext();
        if (context == null) return;

        // 清除现有内容
        container.removeAllViews();

        // 创建封面骨架屏
        View coverSkeleton = createSkeletonView(context, (int)(200 * coverRatio), 200);
        container.addView(coverSkeleton);

        // 创建标题骨架屏（80%长度）
        int maxTitleWidth = 300; // 假设最大标题宽度为300dp
        int titleWidth = (int)(maxTitleWidth * Math.min(Math.max(titleLengthRatio, 0.1f), 1.0f));
        View titleSkeleton = createSkeletonView(context, titleWidth, 40);

        // 添加间距
        ViewGroup.MarginLayoutParams titleParams = new ViewGroup.MarginLayoutParams(
            ViewGroup.LayoutParams.WRAP_CONTENT, ViewGroup.LayoutParams.WRAP_CONTENT);
        titleParams.topMargin = dpToPx(context, 8);
        titleSkeleton.setLayoutParams(titleParams);

        container.addView(titleSkeleton);

        // 应用shimmer效果
        createEnhancedSkeletonEffect(coverSkeleton);
        createEnhancedSkeletonEffect(titleSkeleton);
    }

    /**
     * 创建视频卡片骨架屏布局（使用默认参数）
     */
    public static void createVideoCardSkeleton(ViewGroup container) {
        createVideoCardSkeleton(container, 16f/9f, 0.8f); // 16:9比例，80%标题长度
    }
    
    /**
     * 批量加载图片（用于列表优化）
     */
    public static void loadImagesInBatch(Context context, String[] imageUrls, ImageView[] imageViews,
                                       AnimationType animationType, int delayBetweenItems) {
        if (context == null || imageUrls == null || imageViews == null) {
            return;
        }

        int count = Math.min(imageUrls.length, imageViews.length);

        for (int i = 0; i < count; i++) {
            final int index = i;
            final String url = imageUrls[i];
            final ImageView imageView = imageViews[i];

            // 添加延迟以创建错开的加载效果
            imageView.postDelayed(() -> {
                loadImageWithAnimation(context, url, imageView, animationType);
            }, index * delayBetweenItems);
        }
    }

    /**
     * 创建骨架屏视图
     */
    private static View createSkeletonView(Context context, int widthDp, int heightDp) {
        View view = new View(context);
        ViewGroup.LayoutParams params = new ViewGroup.LayoutParams(
            dpToPx(context, widthDp), dpToPx(context, heightDp));
        view.setLayoutParams(params);
        view.setBackgroundColor(SKELETON_COLOR);
        return view;
    }

    /**
     * dp转px
     */
    private static int dpToPx(Context context, int dp) {
        float density = context.getResources().getDisplayMetrics().density;
        return Math.round(dp * density);
    }

    /**
     * Shimmer效果的Drawable实现
     * 实现横向流动光影效果
     */
    private static class ShimmerDrawable extends Drawable {
        private Paint paint;
        private LinearGradient gradient;
        private Matrix gradientMatrix;
        private ValueAnimator shimmerAnimator;
        private float shimmerTranslateX;

        public ShimmerDrawable() {
            paint = new Paint();
            gradientMatrix = new Matrix();
        }

        public void startShimmer(int duration) {
            if (shimmerAnimator != null && shimmerAnimator.isRunning()) {
                shimmerAnimator.cancel();
            }

            shimmerAnimator = ValueAnimator.ofFloat(0f, 1f);
            shimmerAnimator.setDuration(duration);
            shimmerAnimator.setRepeatCount(ValueAnimator.INFINITE);
            shimmerAnimator.setRepeatMode(ValueAnimator.RESTART);
            shimmerAnimator.setInterpolator(new LinearInterpolator());
            shimmerAnimator.addUpdateListener(animation -> {
                shimmerTranslateX = (Float) animation.getAnimatedValue();
                invalidateSelf();
            });
            shimmerAnimator.start();
        }

        public void stopShimmer() {
            if (shimmerAnimator != null) {
                shimmerAnimator.cancel();
                shimmerAnimator = null;
            }
        }

        @Override
        public void draw(Canvas canvas) {
            if (getBounds().isEmpty()) return;

            int width = getBounds().width();
            int height = getBounds().height();

            // 绘制基础背景
            paint.setShader(null);
            paint.setColor(SKELETON_COLOR);
            canvas.drawRect(getBounds(), paint);

            // 创建shimmer渐变
            if (gradient == null || shimmerAnimator == null) {
                createGradient(width);
            }

            if (gradient != null && shimmerAnimator != null && shimmerAnimator.isRunning()) {
                // 计算shimmer位置
                float shimmerWidth = width * 0.3f; // shimmer宽度为视图宽度的30%
                float translateX = (width + shimmerWidth) * shimmerTranslateX - shimmerWidth;

                gradientMatrix.reset();
                gradientMatrix.setTranslate(translateX, 0);
                gradient.setLocalMatrix(gradientMatrix);

                paint.setShader(gradient);
                canvas.drawRect(getBounds(), paint);
            }
        }

        private void createGradient(int width) {
            float shimmerWidth = width * 0.3f;
            gradient = new LinearGradient(
                0, 0, shimmerWidth, 0,
                new int[]{
                    Color.TRANSPARENT,
                    Color.argb(77, 255, 255, 255), // 30%透明度的白色
                    Color.TRANSPARENT
                },
                new float[]{0f, 0.5f, 1f},
                Shader.TileMode.CLAMP
            );
        }

        @Override
        public void setAlpha(int alpha) {
            paint.setAlpha(alpha);
        }

        @Override
        public void setColorFilter(android.graphics.ColorFilter colorFilter) {
            paint.setColorFilter(colorFilter);
        }

        @Override
        public int getOpacity() {
            return android.graphics.PixelFormat.TRANSLUCENT;
        }
    }
}
