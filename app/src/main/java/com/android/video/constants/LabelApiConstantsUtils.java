package com.android.video.constants;

/**
 * 标签模块API常量类 - 定义标签相关的API路径和字段常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理用户标签相关的API常量，
 * 包括获取可选标签列表、提交用户选择的标签等功能的API路径和响应字段定义。
 * </p>
 * 
 * <p>
 * 包含的标签功能：
 * <ul>
 *   <li>获取所有可选标签信息</li>
 *   <li>提交用户选择的标签</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 标签系统用于：
 * <ul>
 *   <li>用户兴趣偏好收集</li>
 *   <li>个性化内容推荐</li>
 *   <li>用户画像构建</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取标签API URL
 * String url = LabelApiConstantsUtils.buildApiUrl(baseUrl, LabelApiConstantsUtils.API_GET_LABELS);
 * 
 * // 解析标签响应数据
 * String labelId = jsonObject.getString(LabelApiConstantsUtils.FIELD_LABEL_ID);
 * String labelName = jsonObject.getString(LabelApiConstantsUtils.FIELD_LABEL_NAME);
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class LabelApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 标签相关API路径常量 ==========

    /**
     * 获取标签信息API路径
     * <p>
     * 用于获取所有可选标签列表的接口。
     * 请求方法：POST
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_GET_LABELS = "/app/customer/getLabels";

    /**
     * 提交用户标签API路径
     * <p>
     * 用于提交用户选择的标签列表的接口。
     * 请求方法：POST
     * 认证要求：继承父级
     * </p>
     */
    public static final String API_SUBMIT_LABELS = "/app/customer/submitLabels";

    // ========== 标签相关响应字段常量 ==========

    /**
     * 标签ID字段名
     * <p>
     * 用于标识唯一标签的字段名。
     * 字段类型：String
     * 示例值：fdb8567c044dc03efd4cc98b14fb9440
     * </p>
     */
    public static final String FIELD_LABEL_ID = "labelId";

    /**
     * 标签名称字段名
     * <p>
     * 用于显示标签名称的字段名。
     * 字段类型：String
     * 示例值：Love, Action, Comedy
     * </p>
     */
    public static final String FIELD_LABEL_NAME = "labelName";

    // ========== 标签模块工具方法 ==========

    /**
     * 检查是否为标签相关的API路径
     * <p>
     * 判断给定的API路径是否属于标签模块。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是标签相关API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isLabel = isLabelApi("/app/customer/getLabels");     // true
     * boolean isLabel = isLabelApi("/app/customer/sendSmsCode");   // false
     * </pre>
     */
    public static boolean isLabelApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }
        
        return apiPath.equals(API_GET_LABELS) ||
               apiPath.equals(API_SUBMIT_LABELS);
    }

    /**
     * 检查是否为标签查询API
     * <p>
     * 判断给定的API路径是否为获取标签信息的接口。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是标签查询API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isQuery = isLabelQueryApi("/app/customer/getLabels");     // true
     * boolean isQuery = isLabelQueryApi("/app/customer/submitLabels");  // false
     * </pre>
     */
    public static boolean isLabelQueryApi(String apiPath) {
        return API_GET_LABELS.equals(apiPath);
    }

    /**
     * 检查是否为标签提交API
     * <p>
     * 判断给定的API路径是否为提交标签的接口。
     * </p>
     * 
     * @param apiPath API路径
     * @return 如果是标签提交API则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean isSubmit = isLabelSubmitApi("/app/customer/submitLabels");  // true
     * boolean isSubmit = isLabelSubmitApi("/app/customer/getLabels");     // false
     * </pre>
     */
    public static boolean isLabelSubmitApi(String apiPath) {
        return API_SUBMIT_LABELS.equals(apiPath);
    }

    /**
     * 获取标签API的描述信息
     * <p>
     * 根据API路径返回对应的中文描述信息，
     * 便于调试和日志记录。
     * </p>
     * 
     * @param apiPath API路径
     * @return API的中文描述，未知API返回"未知标签接口"
     * 
     * @example
     * <pre>
     * String desc = getLabelApiDescription("/app/customer/getLabels");     // "获取标签信息"
     * String desc = getLabelApiDescription("/app/customer/submitLabels");  // "提交用户标签"
     * String desc = getLabelApiDescription("/unknown/api");                // "未知标签接口"
     * </pre>
     */
    public static String getLabelApiDescription(String apiPath) {
        if (apiPath == null) {
            return "UNDEFINED_LABEL_ERROR";
        }
        
        switch (apiPath) {
            case API_GET_LABELS:
                return "获取标签信息";
            case API_SUBMIT_LABELS:
                return "提交用户标签";
            default:
                return "UNDEFINED_LABEL_ERROR";
        }
    }

    /**
     * 验证标签字段是否完整
     * <p>
     * 检查标签对象是否包含必要的字段（ID和名称）。
     * </p>
     * 
     * @param labelId 标签ID
     * @param labelName 标签名称
     * @return 如果字段完整则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean valid = isValidLabelFields("123", "Love");    // true
     * boolean valid = isValidLabelFields(null, "Love");     // false
     * boolean valid = isValidLabelFields("123", "");        // false
     * </pre>
     */
    public static boolean isValidLabelFields(String labelId, String labelName) {
        return labelId != null && !labelId.trim().isEmpty() &&
               labelName != null && !labelName.trim().isEmpty();
    }
}
