# 登录接口迁移报告

## 概述

本次修改将现有的静态登录认证系统迁移到后端登录接口，支持手机号登录和验证码验证功能。所有修改都保持了向后兼容性，现有代码可以继续正常工作。

## 主要修改内容

### 1. API常量更新 (`AuthApiConstantsUtils.java`)

#### 新增接口路径
- `API_LOGIN = "/app/customer/login"` - 统一登录接口
- `API_INIT_DEVICE = "/app/customer/init"` - 初始化设备信息接口

#### 新增请求参数常量
- `PARAM_LOGIN_TYPE` - 登录类型（1=手机号，2=VKontakte，3=TikTok）
- `PARAM_VKONTAKTE_CODE` - VKontakte授权码
- `PARAM_TIKTOK_CODE` - TikTok授权码
- `PARAM_DEVICE_CODE` - 设备唯一ID
- `PARAM_OS_TYPE` - 操作系统类型（1=iOS，2=Android）

#### 新增常量定义
- 登录类型常量：`LOGIN_TYPE_PHONE`, `LOGIN_TYPE_VKONTAKTE`, `LOGIN_TYPE_TIKTOK`
- 操作系统类型常量：`OS_TYPE_IOS`, `OS_TYPE_ANDROID`

#### 弃用标记
- `API_PHONE_LOGIN` - 标记为弃用，使用 `API_LOGIN` 替代

### 2. 请求模型更新

#### 新增 `InitDeviceRequestModel.java`
- 支持APP启动时的设备初始化请求
- 包含设备唯一ID和操作系统类型
- 提供完整的参数验证

#### 更新 `LoginRequestModel.java`
- 支持统一登录接口的所有参数
- 新增字段：`loginType`, `vKontakteCode`, `tiktokCode`, `deviceCode`, `osType`
- 保持向后兼容性，提供弃用的getter/setter方法
- 根据登录类型进行智能参数验证

### 3. 响应模型更新

#### 新增 `InitDeviceResponseModel.java`
- 处理设备初始化接口的响应
- 包含用户登录状态检查方法

#### 更新 `LoginResponseModel.java`
- 新增字段：`customerId`, `customerName`, `userToken`, `loginType`, `deviceId`
- 保持向后兼容性，所有旧字段仍可使用
- 自动同步新旧字段值

### 4. 用户模型更新 (`UserModel.java`)

#### 新增字段
- `userToken` - 用户令牌
- `loginType` - 登录类型
- `deviceId` - 设备ID

#### 新增构造函数
- 支持从新接口响应直接创建UserModel
- 自动设置默认值以保持兼容性

### 5. 网络层更新 (`AuthApiUtils.java`)

#### 新增方法
- `initDevice()` - 初始化设备信息
- `createUserModelFromLoginResponse()` - 从登录响应创建UserModel
- `createUserModelFromInitResponse()` - 从初始化响应创建UserModel

#### 更新现有方法
- `phoneLogin()` - 使用新的统一登录接口
- 添加详细的请求和响应日志
- 支持X-Access-Token请求头

#### 弃用标记
- 现有静态认证方法标记为弃用，仅用于开发环境

### 6. 会话管理更新 (`UserSessionUtils.java`)

#### 新增方法
- `saveUserSessionFromLoginResponse()` - 从登录响应保存用户会话
- `saveUserSessionFromInitResponse()` - 从初始化响应保存用户会话

#### 弃用标记
- `performLogin()` - 标记为弃用，建议使用新的API接口

### 7. 测试集成 (`MainActivity.java`)

#### 新增测试方法
- `testInitDeviceApi()` - 测试初始化设备API接口
- 在应用启动时自动调用，验证接口连通性

## 接口对接详情

### 1. 初始化设备信息接口

**请求URL**: `/app/customer/init`
**请求方法**: POST
**请求参数**:
```json
{
    "deviceCode": "设备唯一ID",
    "osType": 2
}
```

**响应格式**:
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "customerId": "用户ID（可能为null）",
        "customerName": "用户昵称（可能为null）",
        "userToken": "用户token",
        "loginType": "登录类型（可能为null）",
        "deviceId": "设备唯一标识编码"
    }
}
```

### 2. 统一登录接口

**请求URL**: `/app/customer/login`
**请求方法**: POST
**请求头**: `X-Access-Token: 访问令牌`
**请求参数**:
```json
{
    "loginType": 1,
    "phoneNumber": "手机号",
    "verificationCode": "验证码",
    "vKontakteCode": "",
    "tiktokCode": "",
    "deviceCode": "设备唯一ID",
    "osType": 2
}
```

**响应格式**:
```json
{
    "code": "200",
    "message": "success",
    "data": {
        "customerId": "用户ID",
        "customerName": "用户昵称",
        "userToken": "用户token",
        "loginType": 1,
        "deviceId": "设备唯一标识编码"
    }
}
```

### 3. 发送手机验证码接口

**请求URL**: `/app/customer/sendSmsCode`
**请求方法**: POST
**请求参数**:
```json
{
    "phoneNumber": "手机号"
}
```

**响应格式**:
```json
{
    "code": "200",
    "message": "success",
    "data": "验证码已发送"
}
```

## 日志增强

为所有接口添加了详细的日志输出：

### 请求日志
- 请求URL
- 请求头信息
- 请求体内容
- 设备信息

### 响应日志
- 响应状态码
- 响应头信息
- 响应体内容
- 解析后的数据字段

### 业务日志
- 登录成功/失败状态
- 用户信息
- 错误详情

## 向后兼容性

### 保持兼容的功能
1. 所有现有的UserModel字段和方法
2. LoginResponseModel的所有旧字段
3. UserSessionUtils的现有方法
4. 现有的登录流程和UI

### 弃用但仍可用的功能
1. 静态认证方法（仅开发环境）
2. 旧的API路径常量
3. 旧的字段访问方法

## 测试验证

### 自动测试
- MainActivity启动时自动调用初始化设备接口
- 详细的日志输出便于调试

### 手动测试建议
1. 测试设备初始化接口
2. 测试手机号登录流程
3. 测试验证码发送功能
4. 验证用户会话保存
5. 检查日志输出完整性

## 静态认证完全弃用

### 重要更新
**所有静态认证功能已完全弃用！** 包括开发环境在内的所有环境都必须使用后端API进行认证。

### 弃用的功能
1. **静态认证方法**
   - `UserAuthUtils.authenticateUser()` - 标记为 @Deprecated
   - `UserSessionUtils.performLogin()` - 标记为 @Deprecated

2. **特殊处理逻辑**
   - Super Admin直接登录 - 已注释掉
   - 开发环境特殊处理 - 已注释掉

3. **环境判断逻辑**
   - 所有环境判断代码已移除或注释
   - 统一使用后端API认证

### 弃用警告
调用弃用方法时会输出警告日志：
```
⚠️ WARNING: Static authentication is deprecated! Use AuthApiUtils.phoneLogin() instead
⚠️ WARNING: Static login is deprecated! Use AuthApiUtils.phoneLogin() instead
```

## 注意事项

1. **环境配置**: ~~开发环境仍使用静态认证，生产环境使用新接口~~ **所有环境都使用后端接口**
2. **错误处理**: 增强了网络错误和业务错误的处理
3. **安全性**: 敏感信息（如token、验证码）在日志中已脱敏
4. **性能**: 新接口调用不会影响应用启动性能
5. **网络依赖**: 所有环境都需要网络连接和后端接口可用性

## 后续工作建议

1. 完成所有登录相关功能的测试
2. 根据测试结果调整错误处理逻辑
3. 考虑添加网络状态检查
4. 优化用户体验（加载状态、错误提示等）
5. 完善VKontakte和TikTok登录功能（如需要）
6. **移除所有弃用的静态认证代码**（在确认新接口稳定后）
7. 更新开发文档和测试用例

## Token配置重要说明

### X-Access-Token配置
所有需要认证的API接口都需要X-Access-Token请求头：

- **发送验证码接口**: 需要X-Access-Token
- **统一登录接口**: 需要X-Access-Token
- **初始化设备接口**: 不需要X-Access-Token

### 当前Token配置
- **默认Token**: `05358c87b76645feaae46740fac753c9`
- **请求头名称**: `X-Access-Token`
- **管理类**: `ApiHeaderUtils`, `TokenConfigUtils`

### Token验证和调试
应用启动时会自动验证token配置并输出详细日志：
```
TokenConfigUtils: === Token Configuration Validation ===
TokenConfigUtils: Header Name: X-Access-Token
TokenConfigUtils: Current Token: 05358c87b76645feaae46740fac753c9
```

如果后端需要不同的测试token，请参考[Token配置指南](TOKEN_CONFIGURATION_GUIDE.md)进行更新。

## 相关文档

- [Token配置指南](TOKEN_CONFIGURATION_GUIDE.md) - **重要**
- [登录接口测试指南](LOGIN_API_TESTING_GUIDE.md)
- [静态认证弃用通知](STATIC_AUTH_DEPRECATION_NOTICE.md)
- [静态认证系统说明](STATIC_AUTH_README.txt) - 已标记为弃用
