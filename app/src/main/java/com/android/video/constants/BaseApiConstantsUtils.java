package com.android.video.constants;

/**
 * API常量管理抽象基类 - 提供所有模块共用的响应码、字段常量和工具方法
 * <p>
 * 这个抽象基类包含了所有API模块共用的常量定义和工具方法，
 * 各个具体的API模块常量类应该继承此基类来复用通用功能。
 * </p>
 * 
 * <p>
 * 设计原则：
 * <ul>
 *   <li>通用性：只包含所有模块都会用到的常量和方法</li>
 *   <li>可扩展性：子类可以添加自己模块特有的常量</li>
 *   <li>一致性：确保所有模块使用统一的响应码和字段定义</li>
 * </ul>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public abstract class BaseApiConstantsUtils {

    // ========== 通用响应码常量 ==========

    /**
     * 成功响应码
     */
    public static final String RESPONSE_CODE_SUCCESS = "200";

    /**
     * 失败响应码
     */
    public static final String RESPONSE_CODE_ERROR = "404";

    /**
     * 参数错误响应码
     */
    public static final String RESPONSE_CODE_PARAM_ERROR = "400";

    /**
     * 未授权响应码
     */
    public static final String RESPONSE_CODE_UNAUTHORIZED = "401";

    /**
     * 服务器内部错误响应码
     */
    public static final String RESPONSE_CODE_SERVER_ERROR = "500";

    // ========== 通用响应字段常量 ==========

    /**
     * 响应码字段名
     */
    public static final String FIELD_CODE = "code";

    /**
     * 响应消息字段名
     */
    public static final String FIELD_MESSAGE = "message";

    /**
     * 响应数据字段名
     */
    public static final String FIELD_DATA = "data";

    // ========== 通用请求参数常量 ==========

    /**
     * 设备ID参数名
     * <p>
     * 设备ID是大多数API接口都需要的通用参数，
     * 用于标识客户端设备，支持用户行为追踪和安全验证。
     * </p>
     */
    public static final String PARAM_DEVICE_ID = "deviceId";

    // ========== 全局请求头常量 ==========

    /**
     * 访问令牌请求头名称
     * <p>
     * 用于API请求认证的访问令牌请求头，
     * 所有需要认证的API接口都应该包含此请求头。
     * </p>
     */
    public static final String HEADER_ACCESS_TOKEN = "X-Access-Token";

    /**
     * 默认访问令牌值
     * <p>
     * 用于初始化设备接口等需要全局token的场景
     * 这是一个固定的API密钥，用于应用启动时的基础接口调用
     * </p>
     */
    public static final String DEFAULT_ACCESS_TOKEN = "91eb06cfeb164b35a1fb04c3b91aacda";

    // ========== 通用工具方法 ==========

    /**
     * 构建完整的API URL
     * <p>
     * 将基础URL和API路径组合成完整的请求URL，
     * 自动处理URL路径的斜杠问题，确保URL格式正确。
     * </p>
     * 
     * @param baseUrl 基础URL，例如 "https://api.example.com" 或 "https://api.example.com/"
     * @param apiPath API路径，例如 "/app/user/login" 或 "app/user/login"
     * @return 完整的API URL，如果参数为null则返回空字符串
     * 
     * @example
     * <pre>
     * String url = buildApiUrl("https://api.example.com", "/app/user/login");
     * // 结果: "https://api.example.com/app/user/login"
     * 
     * String url = buildApiUrl("https://api.example.com/", "app/user/login");
     * // 结果: "https://api.example.com/app/user/login"
     * </pre>
     */
    public static String buildApiUrl(String baseUrl, String apiPath) {
        if (baseUrl == null || apiPath == null) {
            return "";
        }
        
        // 确保baseUrl不以/结尾，apiPath以/开头
        String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        String cleanApiPath = apiPath.startsWith("/") ? apiPath : "/" + apiPath;
        
        return cleanBaseUrl + cleanApiPath;
    }

    /**
     * 检查响应码是否为成功
     * <p>
     * 判断API响应码是否表示请求成功。
     * 只有响应码为 {@link #RESPONSE_CODE_SUCCESS} 时才认为是成功。
     * </p>
     * 
     * @param code 响应码字符串
     * @return 如果响应码表示成功则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean success = isSuccessCode("200");  // true
     * boolean success = isSuccessCode("404");  // false
     * boolean success = isSuccessCode(null);   // false
     * </pre>
     */
    public static boolean isSuccessCode(String code) {
        return RESPONSE_CODE_SUCCESS.equals(code);
    }

    /**
     * 检查响应码是否为错误
     * <p>
     * 判断API响应码是否表示请求失败。
     * 任何非成功的响应码都被认为是错误。
     * </p>
     * 
     * @param code 响应码字符串
     * @return 如果响应码表示错误则返回true，否则返回false
     * 
     * @example
     * <pre>
     * boolean error = isErrorCode("404");  // true
     * boolean error = isErrorCode("500");  // true
     * boolean error = isErrorCode("200");  // false
     * boolean error = isErrorCode(null);   // true
     * </pre>
     */
    public static boolean isErrorCode(String code) {
        return !isSuccessCode(code);
    }

    /**
     * 获取响应码的描述信息
     * <p>
     * 根据响应码返回对应的中文描述信息，
     * 便于调试和日志记录。
     * </p>
     *
     * @param code 响应码字符串
     * @return 响应码的中文描述，未知响应码返回"未知错误"
     *
     * @example
     * <pre>
     * String desc = getResponseCodeDescription("200");  // "请求成功"
     * String desc = getResponseCodeDescription("404");  // "请求失败"
     * String desc = getResponseCodeDescription("999");  // "未知错误"
     * </pre>
     */
    public static String getResponseCodeDescription(String code) {
        if (code == null) {
            return "UNDEFINED_ERROR";
        }

        switch (code) {
            case RESPONSE_CODE_SUCCESS:
                return "SUCCESS";
            case RESPONSE_CODE_ERROR:
                return "ERROR";
            case RESPONSE_CODE_PARAM_ERROR:
                return "PARAM_ERROR";
            case RESPONSE_CODE_UNAUTHORIZED:
                return "UNAUTHORIZED";
            case RESPONSE_CODE_SERVER_ERROR:
                return "SERVER_ERROR";
            default:
                return "UNDEFINED_ERROR";
        }
    }

    // ========== 请求头工具方法 ==========

    /**
     * 获取默认的访问令牌
     * <p>
     * @deprecated 不再使用硬编码token，请使用TokenManager.getCurrentToken()获取动态token
     * 此方法仅作为备用方案保留
     * </p>
     *
     * @return 默认访问令牌字符串
     *
     * @example
     * <pre>
     * // 推荐使用方式：
     * TokenManager tokenManager = TokenManager.getInstance(context);
     * String token = tokenManager.getCurrentToken();
     *
     * // 不推荐的旧方式：
     * String token = getDefaultAccessToken();
     * </pre>
     */
    @Deprecated
    public static String getDefaultAccessToken() {
        android.util.Log.e("BaseApiConstantsUtils",
            "❌ 严重错误：仍在使用已弃用的getDefaultAccessToken()方法！");
        android.util.Log.e("BaseApiConstantsUtils",
            "❌ 请立即使用TokenManager.getCurrentToken()替代此方法");

        // 打印调用栈，帮助定位调用来源
        android.util.Log.e("BaseApiConstantsUtils", "调用栈:", new Exception("Deprecated method call trace"));

        // 返回null而不是硬编码token，强制修复调用方
        return null;
    }

    /**
     * 检查访问令牌是否有效
     * <p>
     * 简单检查访问令牌是否为空或null。
     * 后期可以扩展为更复杂的令牌验证逻辑。
     * </p>
     *
     * @param token 访问令牌字符串
     * @return 如果令牌有效则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean valid = isValidAccessToken("05358c87b76645feaae46740fac753c9");  // true
     * boolean valid = isValidAccessToken("");                                  // false
     * boolean valid = isValidAccessToken(null);                               // false
     * </pre>
     */
    public static boolean isValidAccessToken(String token) {
        return token != null && !token.trim().isEmpty();
    }
}
