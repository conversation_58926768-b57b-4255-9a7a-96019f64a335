
package com.android.video.network;

import android.content.Context;
import android.content.SharedPreferences;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 网络优化管理器
 * 负责网络状态监控、视频预加载策略和弱网络提示
 * <AUTHOR> Team
 */
public class NetworkOptimizationManager {
    
    private static final String TAG = "NetworkOptimizationManager";
    private static final String PREFS_NAME = "network_optimization_prefs";
    private static final String KEY_POOR_NETWORK_SHOWN = "poor_network_shown_";
    
    private static NetworkOptimizationManager instance;
    private Context context;
    private SharedPreferences preferences;
    private Handler mainHandler;
    
    // 网络状态监控
    private NetworkUtils.NetworkQuality lastNetworkQuality;
    private AtomicBoolean isMonitoring = new AtomicBoolean(false);
    private Runnable networkMonitorRunnable;
    
    // 监听器接口
    public interface NetworkOptimizationListener {
        void onNetworkQualityChanged(NetworkUtils.NetworkQuality quality);
        void onPoorNetworkDetected();
        void onPreloadCountChanged(int count);
    }
    
    private NetworkOptimizationListener listener;
    
    private NetworkOptimizationManager(Context context) {
        this.context = context.getApplicationContext();
        this.preferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE);
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.lastNetworkQuality = NetworkUtils.getDetailedNetworkQuality(context);
        
        initializeNetworkMonitoring();
    }
    
    public static synchronized NetworkOptimizationManager getInstance(Context context) {
        if (instance == null) {
            instance = new NetworkOptimizationManager(context);
        }
        return instance;
    }
    
    /**
     * 设置网络优化监听器
     */
    public void setNetworkOptimizationListener(NetworkOptimizationListener listener) {
        this.listener = listener;
    }
    
    /**
     * 初始化网络监控
     */
    private void initializeNetworkMonitoring() {
        networkMonitorRunnable = new Runnable() {
            @Override
            public void run() {
                if (isMonitoring.get()) {
                    checkNetworkQuality();
                    // 每5秒检查一次网络质量
                    mainHandler.postDelayed(this, 5000);
                }
            }
        };
    }
    
    /**
     * 开始网络监控
     */
    public void startNetworkMonitoring() {
        if (!isMonitoring.getAndSet(true)) {
            Log.d(TAG, "Starting network monitoring");
            mainHandler.post(networkMonitorRunnable);
        }
    }
    
    /**
     * 停止网络监控
     */
    public void stopNetworkMonitoring() {
        if (isMonitoring.getAndSet(false)) {
            Log.d(TAG, "Stopping network monitoring");
            mainHandler.removeCallbacks(networkMonitorRunnable);
        }
    }
    
    /**
     * 检查网络质量
     */
    private void checkNetworkQuality() {
        NetworkUtils.NetworkQuality currentQuality = NetworkUtils.getDetailedNetworkQuality(context);
        
        if (currentQuality != lastNetworkQuality) {
            Log.d(TAG, "Network quality changed from " + lastNetworkQuality + " to " + currentQuality);
            lastNetworkQuality = currentQuality;
            
            if (listener != null) {
                listener.onNetworkQualityChanged(currentQuality);
                
                // 更新预加载数量
                int preloadCount = NetworkUtils.getRecommendedPreloadCount(context);
                listener.onPreloadCountChanged(preloadCount);
                
                // 检查是否为弱网络
                if (currentQuality == NetworkUtils.NetworkQuality.POOR || 
                    currentQuality == NetworkUtils.NetworkQuality.NONE) {
                    listener.onPoorNetworkDetected();
                }
            }
        }
    }
    
    /**
     * 获取当前网络质量
     */
    public NetworkUtils.NetworkQuality getCurrentNetworkQuality() {
        return NetworkUtils.getDetailedNetworkQuality(context);
    }
    
    /**
     * 获取推荐的预加载视频数量
     */
    public int getRecommendedPreloadCount() {
        return NetworkUtils.getRecommendedPreloadCount(context);
    }
    
    /**
     * 检查是否应该显示弱网络提示
     * @param pageKey 页面标识符（如"discover", "video_player"）
     */
    public boolean shouldShowPoorNetworkWarning(String pageKey) {
        if (!NetworkUtils.isPoorNetwork(context)) {
            return false;
        }
        
        String key = KEY_POOR_NETWORK_SHOWN + pageKey;
        boolean hasShown = preferences.getBoolean(key, false);
        
        if (!hasShown) {
            // 标记为已显示
            preferences.edit().putBoolean(key, true).apply();
            return true;
        }
        
        return false;
    }
    
    /**
     * 重置弱网络提示状态（用于重新进入页面时）
     * @param pageKey 页面标识符
     */
    public void resetPoorNetworkWarningState(String pageKey) {
        String key = KEY_POOR_NETWORK_SHOWN + pageKey;
        preferences.edit().putBoolean(key, false).apply();
    }
    
    /**
     * 获取网络类型描述
     */
    public String getNetworkTypeDescription() {
        NetworkUtils.NetworkType type = NetworkUtils.getNetworkType(context);
        NetworkUtils.NetworkQuality quality = getCurrentNetworkQuality();
        
        StringBuilder description = new StringBuilder();
        description.append(NetworkUtils.networkTypeToString(type));
        
        switch (quality) {
            case EXCELLENT:
                description.append(" (优秀)");
                break;
            case GOOD:
                description.append(" (良好)");
                break;
            case FAIR:
                description.append(" (一般)");
                break;
            case POOR:
                description.append(" (较差)");
                break;
            case NONE:
                description.append(" (无连接)");
                break;
        }
        
        return description.toString();
    }
    
    /**
     * 检查是否为WiFi环境
     */
    public boolean isWifiEnvironment() {
        return NetworkUtils.isWifiConnected(context);
    }
    
    /**
     * 检查是否为移动数据环境
     */
    public boolean isMobileDataEnvironment() {
        return NetworkUtils.isMobileDataNetwork(context);
    }
    
    /**
     * 检查是否为弱网络环境
     */
    public boolean isPoorNetworkEnvironment() {
        return NetworkUtils.isPoorNetwork(context);
    }
    
    /**
     * 获取网络优化建议
     */
    public String getNetworkOptimizationSuggestion() {
        NetworkUtils.NetworkQuality quality = getCurrentNetworkQuality();
        NetworkUtils.NetworkType type = NetworkUtils.getNetworkType(context);
        
        switch (quality) {
            case EXCELLENT:
                return "网络状况良好，可以享受高清视频";
            case GOOD:
                return "网络状况不错，建议选择标清或高清画质";
            case FAIR:
                if (type == NetworkUtils.NetworkType.MOBILE) {
                    return "当前使用移动数据，建议选择标清画质以节省流量";
                } else {
                    return "网络状况一般，建议选择标清画质";
                }
            case POOR:
                return "网络状况较差，建议选择流畅画质或稍后重试";
            case NONE:
            default:
                return "网络连接异常，请检查网络设置";
        }
    }
    
    /**
     * 清理资源
     */
    public void cleanup() {
        stopNetworkMonitoring();
        listener = null;
    }
}
