package com.android.video.utils;

import android.util.Log;
import org.json.JSONObject;

/**
 * JSON数据验证工具类
 * <p>
 * 提供安全的JSON数据解析和验证功能，防止数据异常和越界问题。
 * 基于Android开发最佳实践，确保API响应数据的安全性和可靠性。
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 */
public class JsonDataValidator {
    
    private static final String TAG = "JsonDataValidator";
    
    // 合理的数据范围定义
    private static final int MAX_VIP_DAYS = 5000; // 最多约13.7年，给予更大的灵活性
    private static final int MIN_VIP_DAYS = 0;
    private static final int MAX_POINTS = 999999999; // 最多9亿积分
    private static final int MIN_POINTS = 0;

    
    /**
     * 安全获取整数值，带边界检查
     * 
     * @param jsonObject JSON对象
     * @param key 字段名
     * @param defaultValue 默认值
     * @param minValue 最小值
     * @param maxValue 最大值
     * @return 验证后的整数值
     */
    public static int getSafeInt(JSONObject jsonObject, String key, int defaultValue, int minValue, int maxValue) {
        if (jsonObject == null || key == null) {
            Log.w(TAG, "JSON对象或key为null，返回默认值: " + defaultValue);
            return defaultValue;
        }
        
        try {
            if (jsonObject.isNull(key)) {
                Log.d(TAG, "字段 " + key + " 为null，返回默认值: " + defaultValue);
                return defaultValue;
            }
            
            int value = jsonObject.optInt(key, defaultValue);
            
            // 边界检查
            if (value < minValue || value > maxValue) {
                Log.w(TAG, "字段 " + key + " 值超出范围: " + value + 
                    " (范围: " + minValue + "-" + maxValue + ")，返回默认值: " + defaultValue);
                return defaultValue;
            }
            
            Log.d(TAG, "字段 " + key + " 验证通过: " + value);
            return value;
            
        } catch (Exception e) {
            Log.e(TAG, "解析字段 " + key + " 时发生错误，返回默认值: " + defaultValue, e);
            return defaultValue;
        }
    }
    
    /**
     * 安全获取VIP剩余天数
     * 
     * @param jsonObject JSON对象
     * @param key 字段名
     * @return 验证后的VIP天数，null表示无VIP信息
     */
    public static Integer getSafeVipDays(JSONObject jsonObject, String key) {
        if (jsonObject == null || key == null || jsonObject.isNull(key)) {
            return null;
        }
        
        try {
            int value = jsonObject.optInt(key, -1);
            
            if (value == -1) {
                return null; // 字段不存在或无效
            }
            
            // VIP天数边界检查
            if (value < MIN_VIP_DAYS || value > MAX_VIP_DAYS) {
                Log.w(TAG, "VIP天数超出合理范围: " + value + 
                    " (范围: " + MIN_VIP_DAYS + "-" + MAX_VIP_DAYS + ")，返回null");
                return null;
            }
            
            Log.d(TAG, "VIP天数验证通过: " + value);
            return value;
            
        } catch (Exception e) {
            Log.e(TAG, "解析VIP天数时发生错误", e);
            return null;
        }
    }
    
    /**
     * 安全获取积分值
     * 
     * @param jsonObject JSON对象
     * @param key 字段名
     * @return 验证后的积分值
     */
    public static int getSafePoints(JSONObject jsonObject, String key) {
        return getSafeInt(jsonObject, key, 0, MIN_POINTS, MAX_POINTS);
    }
    

    
    /**
     * 安全获取VIP状态
     * 
     * @param jsonObject JSON对象
     * @param key 字段名
     * @return 验证后的VIP状态，null表示未设置
     */
    public static Integer getSafeVipStatus(JSONObject jsonObject, String key) {
        if (jsonObject == null || key == null || jsonObject.isNull(key)) {
            return null;
        }
        
        try {
            int value = jsonObject.optInt(key, -1);
            
            if (value == -1) {
                return null; // 字段不存在
            }
            
            // VIP状态只能是0或1
            if (value != 0 && value != 1) {
                Log.w(TAG, "VIP状态值无效: " + value + "，返回null");
                return null;
            }
            
            Log.d(TAG, "VIP状态验证通过: " + value);
            return value;
            
        } catch (Exception e) {
            Log.e(TAG, "解析VIP状态时发生错误", e);
            return null;
        }
    }
    
    /**
     * 安全获取字符串值
     * 
     * @param jsonObject JSON对象
     * @param key 字段名
     * @param defaultValue 默认值
     * @return 验证后的字符串值
     */
    public static String getSafeString(JSONObject jsonObject, String key, String defaultValue) {
        if (jsonObject == null || key == null) {
            return defaultValue;
        }
        
        try {
            if (jsonObject.isNull(key)) {
                return defaultValue;
            }
            
            String value = jsonObject.optString(key, defaultValue);
            
            // 检查字符串长度（防止过长的字符串）
            if (value != null && value.length() > 1000) {
                Log.w(TAG, "字段 " + key + " 字符串过长: " + value.length() + " 字符，截断处理");
                value = value.substring(0, 1000);
            }
            
            return value;
            
        } catch (Exception e) {
            Log.e(TAG, "解析字符串字段 " + key + " 时发生错误，返回默认值: " + defaultValue, e);
            return defaultValue;
        }
    }
    
    /**
     * 验证日期字符串格式
     * 
     * @param dateString 日期字符串
     * @return 如果格式有效则返回true，否则返回false
     */
    public static boolean isValidDateString(String dateString) {
        if (dateString == null || dateString.isEmpty()) {
            return false;
        }
        
        try {
            // 检查基本格式 yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
            if (dateString.length() < 10) {
                return false;
            }
            
            // 简单的格式检查
            String datePart = dateString.substring(0, 10);
            return datePart.matches("\\d{4}-\\d{2}-\\d{2}");
            
        } catch (Exception e) {
            Log.e(TAG, "验证日期格式时发生错误: " + dateString, e);
            return false;
        }
    }
    
    /**
     * 记录数据验证摘要
     * 
     * @param jsonObject 原始JSON对象
     * @param validatedData 验证后的数据描述
     */
    public static void logValidationSummary(JSONObject jsonObject, String validatedData) {
        Log.d(TAG, "========== 数据验证摘要 ==========");
        Log.d(TAG, "原始JSON长度: " + (jsonObject != null ? jsonObject.toString().length() : 0));
        Log.d(TAG, "验证后数据: " + validatedData);
        Log.d(TAG, "================================");
    }
}
