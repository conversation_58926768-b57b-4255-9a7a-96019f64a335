<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 海报图片容器 -->
    <androidx.cardview.widget.CardView
        android:id="@+id/cv_poster"
        android:layout_width="246dp"
        android:layout_height="327dp"
        android:layout_marginStart="2dp"
        android:layout_marginEnd="2dp"
        app:cardCornerRadius="18dp"
        app:cardElevation="0dp"
        app:cardBackgroundColor="#FF000000"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <!-- 海报图片 -->
        <ImageView
            android:id="@+id/iv_poster"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="@drawable/movie_poster" />

    </androidx.cardview.widget.CardView>

    <!-- Play按钮 -->
    <LinearLayout
        android:id="@+id/ll_play_button"
        android:layout_width="@dimen/download_play_button_width"
        android:layout_height="@dimen/download_play_button_height"
        android:layout_marginEnd="11dp"
        android:layout_marginBottom="11dp"
        android:background="@drawable/carousel_play_button_bg"
        android:orientation="horizontal"
        android:gravity="center"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="@id/cv_poster"
        app:layout_constraintBottom_toBottomOf="@id/cv_poster">

        <!-- Play图标 -->
        <ImageView
            android:id="@+id/iv_play_icon"
            android:layout_width="@dimen/download_play_icon_size"
            android:layout_height="@dimen/download_play_icon_size"
            android:layout_marginEnd="@dimen/download_play_icon_margin_end"
            android:src="@drawable/list_ic_play"
            android:contentDescription="播放" />

        <!-- Play文本 -->
        <TextView
            android:id="@+id/tv_play_text"
            android:layout_width="@dimen/download_play_text_width"
            android:layout_height="@dimen/download_play_text_height"
            android:text="Play"
            android:textColor="@color/white"
            android:textSize="@dimen/download_play_text_size"
            android:fontFamily="sans-serif-medium"
            android:gravity="center" />

    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
