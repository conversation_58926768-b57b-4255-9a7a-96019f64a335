package com.android.video.constants;

/**
 * 账单中心模块API常量类 - 定义账单相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理账单中心相关的API常量，
 * 包括订单列表查询等功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的账单中心功能：
 * <ul>
 *   <li>订单列表查询</li>
 *   <li>支持不同订单类型筛选</li>
 *   <li>支持分页查询</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 订单类型说明：
 * <ul>
 *   <li>orderType=1：剧集解锁订单</li>
 *   <li>orderType=2：VIP购买订单</li>
 *   <li>orderType=3：积分包购买订单</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取订单列表API URL
 * String url = BillApiConstantsUtils.buildApiUrl(baseUrl, BillApiConstantsUtils.API_GET_ORDER_LIST);
 * 
 * // 使用请求参数
 * Map&lt;String, String&gt; params = new HashMap&lt;&gt;();
 * params.put(BillApiConstantsUtils.PARAM_PAGE, "1");
 * params.put(BillApiConstantsUtils.PARAM_SIZE, "10");
 * params.put(BillApiConstantsUtils.PARAM_ORDER_TYPE, String.valueOf(BillApiConstantsUtils.ORDER_TYPE_VIDEO_UNLOCK));
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class BillApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 账单中心相关API路径常量 ==========

    /**
     * 获取订单列表API路径
     * <p>
     * 用于获取用户的订单记录列表，支持分页和按订单类型筛选。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>page: 页码，从1开始</li>
     *   <li>size: 每页数量，建议10</li>
     *   <li>orderType: 订单类型，1=剧集解锁，2=VIP购买，3=积分包购买</li>
     *   <li>orderId: 订单ID（可选）</li>
     *   <li>userName: 用户名（可选）</li>
     * </ul>
     * </p>
     */
    public static final String API_GET_ORDER_LIST = "/app/billCenter/orderList";

    // ========== 请求参数常量 ==========

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE = "page";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_SIZE = "size";

    /**
     * 订单类型参数名
     */
    public static final String PARAM_ORDER_TYPE = "orderType";

    /**
     * 订单ID参数名
     */
    public static final String PARAM_ORDER_ID = "orderId";

    /**
     * 用户名参数名
     */
    public static final String PARAM_USER_NAME = "userName";

    // ========== 订单类型常量 ==========

    /**
     * 订单类型：剧集解锁订单
     */
    public static final int ORDER_TYPE_VIDEO_UNLOCK = 1;

    /**
     * 订单类型：VIP购买订单
     */
    public static final int ORDER_TYPE_VIP_PURCHASE = 2;

    /**
     * 订单类型：积分包购买订单
     */
    public static final int ORDER_TYPE_POINTS_PURCHASE = 3;

    // ========== 响应字段常量 ==========

    /**
     * 订单记录列表字段名
     */
    public static final String FIELD_RECORDS = "records";

    /**
     * 总记录数字段名
     */
    public static final String FIELD_TOTAL = "total";

    /**
     * 当前页码字段名
     */
    public static final String FIELD_CURRENT = "current";

    /**
     * 每页数量字段名
     */
    public static final String FIELD_SIZE = "size";

    /**
     * 总页数字段名
     */
    public static final String FIELD_PAGES = "pages";

    // ========== 订单字段常量 ==========

    /**
     * 订单ID字段名
     */
    public static final String FIELD_ORDER_ID = "orderId";

    /**
     * 订单号字段名
     */
    public static final String FIELD_ORDER_NO = "orderNo";

    /**
     * 用户ID字段名
     */
    public static final String FIELD_USER_ID = "userId";

    /**
     * 用户名字段名
     */
    public static final String FIELD_USER_NAME = "userName";

    /**
     * 订单类型字段名
     */
    public static final String FIELD_ORDER_TYPE = "orderType";

    /**
     * 业务ID字段名
     */
    public static final String FIELD_BIZ_ID = "bizId";

    /**
     * 业务名称字段名
     */
    public static final String FIELD_BIZ_NAME = "bizName";

    /**
     * 语言字段名
     */
    public static final String FIELD_LANGUAGE = "language";

    /**
     * 解锁章节字段名
     */
    public static final String FIELD_UNLOCK_CHAPTER = "unlockChapter";

    /**
     * 花费积分字段名
     */
    public static final String FIELD_SPEND_POINTS = "spendPoints";

    /**
     * 价格字段名
     */
    public static final String FIELD_PRICE = "price";

    /**
     * 支付金额字段名
     */
    public static final String FIELD_PAY_AMOUNT = "payAmount";

    /**
     * 支付时间字段名
     */
    public static final String FIELD_TIME_OF_PAYMENT = "timeOfPayment";

    /**
     * 支付状态字段名
     */
    public static final String FIELD_PAY_STATUS = "payStatus";

    /**
     * 支付方式字段名
     */
    public static final String FIELD_PAY_TYPE = "payType";

    /**
     * 积分字段名
     */
    public static final String FIELD_POINTS = "points";

    /**
     * 赠送积分字段名
     */
    public static final String FIELD_GIFT_POINTS = "giftPoints";

    /**
     * 创建时间字段名
     */
    public static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 更新时间字段名
     */
    public static final String FIELD_UPDATE_TIME = "updateTime";

    // ========== 工具方法 ==========

    /**
     * 构建订单列表查询URL
     * <p>
     * 根据基础URL和查询参数构建完整的订单列表查询URL。
     * </p>
     * 
     * @param baseUrl 基础URL
     * @param page 页码
     * @param size 每页数量
     * @param orderType 订单类型
     * @return 完整的查询URL
     */
    public static String buildOrderListUrl(String baseUrl, int page, int size, int orderType) {
        String apiUrl = buildApiUrl(baseUrl, API_GET_ORDER_LIST);
        return apiUrl + "?" + PARAM_PAGE + "=" + page + 
               "&" + PARAM_SIZE + "=" + size + 
               "&" + PARAM_ORDER_TYPE + "=" + orderType +
               "&" + PARAM_ORDER_ID + "=" +
               "&" + PARAM_USER_NAME + "=";
    }

    /**
     * 获取订单类型描述
     * <p>
     * 根据订单类型数值返回对应的中文描述。
     * </p>
     * 
     * @param orderType 订单类型
     * @return 订单类型描述
     */
    public static String getOrderTypeDescription(int orderType) {
        switch (orderType) {
            case ORDER_TYPE_VIDEO_UNLOCK:
                return "剧集解锁订单";
            case ORDER_TYPE_VIP_PURCHASE:
                return "VIP购买订单";
            case ORDER_TYPE_POINTS_PURCHASE:
                return "积分包购买订单";
            default:
                return "未知订单类型";
        }
    }
}
