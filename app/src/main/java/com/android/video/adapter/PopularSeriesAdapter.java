package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.constraintlayout.widget.ConstraintLayout;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * Popular Series模块RecyclerView适配器
 * <AUTHOR>
 */
public class PopularSeriesAdapter extends RecyclerView.Adapter<PopularSeriesAdapter.PopularSeriesViewHolder> {
    
    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    
    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }
    
    /**
     * 构造函数
     */
    public PopularSeriesAdapter() {
        this.videoList = new ArrayList<>();
        generatePopularSeriesTestData();
    }
    
    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public PopularSeriesAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }
    
    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }
    
    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList != null ? newVideoList : new ArrayList<>();
        notifyDataSetChanged();
    }
    
    /**
     * 添加视频到列表
     * @param video 视频数据
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }
    
    /**
     * 移除指定位置的视频
     * @param position 位置
     */
    public void removeVideo(int position) {
        if (position >= 0 && position < videoList.size()) {
            videoList.remove(position);
            notifyItemRemoved(position);
        }
    }
    
    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }

    /**
     * 生成Popular Series热度榜单测试数据
     */
    private void generatePopularSeriesTestData() {
        List<VideoModel> popularVideos = new ArrayList<>();

        // 创建10个热度项目
        String[] titles = {
            "热门系列：喜洋洋与灰太狼", "经典动画：功夫熊猫", "热播剧集：权力的游戏",
            "动作大片：速度与激情", "科幻巨制：星际穿越", "爱情电影：泰坦尼克号",
            "悬疑剧：神探夏洛克", "动画电影：冰雪奇缘", "战争片：拯救大兵瑞恩",
            "喜剧片：功夫"
        };

        String[] categories = {
            "动画", "电影", "电视剧", "动作", "科幻",
            "爱情", "悬疑", "动画", "战争", "喜剧"
        };

        String[] descriptions = {
            "精彩的动画片，适合全家观看", "经典功夫动画，幽默搞笑", "史诗级奇幻剧集，权谋斗争",
            "刺激的赛车动作片", "深度科幻电影，探索宇宙", "经典爱情故事，感人至深",
            "烧脑悬疑推理剧", "迪士尼经典动画电影", "真实战争题材电影",
            "周星驰经典喜剧作品"
        };

        // 播放次数（代表热度，从高到低）
        long[] viewCounts = {
            5200000L, 4800000L, 4300000L, 3900000L, 3500000L,
            3100000L, 2700000L, 2300000L, 1900000L, 1500000L
        };

        for (int i = 0; i < 10; i++) {
            VideoModel video = new VideoModel(
                "popular_" + (i + 1),
                titles[i],
                "",
                categories[i]
            );

            // 设置描述
            video.setDescription(descriptions[i]);
            // 设置播放次数（热度值）
            video.setViewCount(viewCounts[i]);

            popularVideos.add(video);
        }

        // 按播放次数从高到低排序
        Collections.sort(popularVideos, new Comparator<VideoModel>() {
            @Override
            public int compare(VideoModel v1, VideoModel v2) {
                return Long.compare(v2.getViewCount(), v1.getViewCount());
            }
        });

        this.videoList = popularVideos;
    }
    
    @NonNull
    @Override
    public PopularSeriesViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_popular_series, parent, false);
        return new PopularSeriesViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull PopularSeriesViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }
    
    @Override
    public int getItemCount() {
        return videoList.size();
    }
    
    /**
     * ViewHolder内部类
     */
    public class PopularSeriesViewHolder extends RecyclerView.ViewHolder {
        
        private ConstraintLayout clPosterContainer;
        private ImageView ivVideoPoster;
        private View viewGradientOverlay;
        private ConstraintLayout clInfoContainer;
        private TextView tvVideoTitle;
        private TextView tvVideoDescription;
        private ConstraintLayout clPlayCount;
        private ImageView ivPlayCountIcon;
        private TextView tvPlayCount;

        public PopularSeriesViewHolder(@NonNull View itemView) {
            super(itemView);
            clPosterContainer = itemView.findViewById(R.id.cl_poster_container);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            viewGradientOverlay = itemView.findViewById(R.id.view_gradient_overlay);
            clInfoContainer = itemView.findViewById(R.id.cl_info_container);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            tvVideoDescription = itemView.findViewById(R.id.tv_video_description);
            clPlayCount = itemView.findViewById(R.id.cl_play_count);
            ivPlayCountIcon = itemView.findViewById(R.id.iv_play_count_icon);
            tvPlayCount = itemView.findViewById(R.id.tv_play_count);
        }
        
        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }
            
            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .centerCrop()
                    .into(ivVideoPoster);
            } else {
                // 如果没有URL，使用默认图片
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }
            
            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());

            // 设置视频介绍
            if (video.getDescription() != null && !video.getDescription().isEmpty()) {
                tvVideoDescription.setText(video.getDescription());
                tvVideoDescription.setVisibility(View.VISIBLE);
            } else {
                tvVideoDescription.setText("精彩的视频内容，值得观看");
                tvVideoDescription.setVisibility(View.VISIBLE);
            }

            // 设置播放次数
            if (video.getViewCount() > 0) {
                tvPlayCount.setText(video.getFormattedViewCount());
                clPlayCount.setVisibility(View.VISIBLE);
            } else {
                clPlayCount.setVisibility(View.GONE);
            }
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                } else {
                    // 默认Toast提示
                    String viewCountText = video.getViewCount() > 0 ? 
                            " (" + video.getFormattedViewCount() + " Views)" : "";
                    Toast.makeText(itemView.getContext(), 
                            "Popular Series视频点击: " + video.getDisplayTitle() + viewCountText, 
                            Toast.LENGTH_SHORT).show();
                }
            });
            
            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                String viewCountText = video.getViewCount() > 0 ? 
                        "\n播放次数: " + video.getFormattedViewCount() + " Views" : "";
                Toast.makeText(itemView.getContext(), 
                        "长按: " + video.getDisplayTitle() + 
                        "\n分类: " + video.getCategory() + viewCountText, 
                        Toast.LENGTH_SHORT).show();
                return true;
            });
        }
    }
}
