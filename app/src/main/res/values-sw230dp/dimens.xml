<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- 积分页面相关尺寸 - sw230dp (缩放因子: 0.6) -->

    <!-- Message Notification Dot - Extra Small phone (230dp, scale factor: 0.6) -->
    <dimen name="message_unread_dot_size">4dp</dimen> <!-- 7 × 0.6 -->

    <!-- 状态栏占位 -->
    <dimen name="points_status_bar_height">18dp</dimen> <!-- 30 × 0.6 -->

    <!-- 返回按钮 -->
    <dimen name="points_back_button_size">18dp</dimen> <!-- 30 × 0.6 -->
    <dimen name="points_back_button_margin_start">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_back_button_margin_top">26dp</dimen> <!-- 44 × 0.6 -->

    <!-- My points标题 -->
    <dimen name="points_title_text_size">12sp</dimen> <!-- 20 × 0.6 -->
    <dimen name="points_title_margin_start">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_title_margin_top">6dp</dimen> <!-- 10 × 0.6 -->

    <!-- 积分余额显示 -->
    <dimen name="points_balance_container_height">58dp</dimen> <!-- 97 × 0.6 -->
    <dimen name="points_balance_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_balance_margin_top">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_balance_padding">12dp</dimen> <!-- 20 × 0.6 -->
    <dimen name="points_balance_coin_size">24dp</dimen> <!-- 40 × 0.6 -->
    <dimen name="points_balance_coin_margin_end">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_balance_amount_text_size">18sp</dimen> <!-- 30 × 0.6 -->

    <!-- 积分卡片网格 -->
    <dimen name="points_grid_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_grid_margin_bottom">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_card_height">42dp</dimen> <!-- 70 × 0.6 -->
    <dimen name="points_card_padding">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_card_margin">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_card_coin_size">18dp</dimen> <!-- 30 × 0.6 -->
    <dimen name="points_card_coin_margin">4dp</dimen> <!-- 7 × 0.6 -->
    <dimen name="points_card_main_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_card_bonus_text_size">7sp</dimen> <!-- 12 × 0.6 -->
    <dimen name="points_card_bonus_margin">2dp</dimen> <!-- 4 × 0.6 -->
    <dimen name="points_card_price_text_size">6sp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_card_price_margin_top">2dp</dimen> <!-- 3 × 0.6 -->

    <!-- 标签页 -->
    <dimen name="points_tab_container_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_tab_container_margin_top">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_tab_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_tab_padding_horizontal">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_tab_padding_vertical">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_tab_margin_end">18dp</dimen> <!-- 30 × 0.6 -->

    <!-- 标签页指示器 -->
    <dimen name="points_tab_indicator_width">27dp</dimen> <!-- 45 × 0.6 -->
    <dimen name="points_tab_indicator_height">2dp</dimen> <!-- 3 × 0.6 -->
    <dimen name="points_tab_indicator_corner_radius">1dp</dimen> <!-- 1 × 0.6 -->
    <dimen name="points_tab_obtain_indicator_margin">0dp</dimen> <!-- 0 × 0.6 -->
    <dimen name="points_tab_expenses_indicator_margin">40dp</dimen> <!-- 67 × 0.6 -->

    <!-- 积分记录列表 -->
    <dimen name="points_list_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_list_margin_bottom">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_record_padding">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_record_title_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_record_subtitle_text_size">8sp</dimen> <!-- 14 × 0.6 -->
    <dimen name="points_record_subtitle_margin_top">2dp</dimen> <!-- 3 × 0.6 -->
    <dimen name="points_record_time_text_size">7sp</dimen> <!-- 12 × 0.6 -->
    <dimen name="points_record_time_margin_top">2dp</dimen> <!-- 3 × 0.6 -->
    <dimen name="points_record_coin_size">12dp</dimen> <!-- 20 × 0.6 -->
    <dimen name="points_record_coin_margin">3dp</dimen> <!-- 5 × 0.6 -->

    <!-- 积分记录金额样式 -->
    <dimen name="points_record_amount_width">18dp</dimen> <!-- 30 × 0.6 -->
    <dimen name="points_record_amount_height">13dp</dimen> <!-- 21 × 0.6 -->
    <dimen name="points_record_amount_text_size">9sp</dimen> <!-- 16 × 0.6 -->

    <!-- Expenses记录特定样式 -->
    <dimen name="expenses_record_title_width">107dp</dimen> <!-- 179 × 0.6 -->
    <dimen name="expenses_record_title_height">11dp</dimen> <!-- 19 × 0.6 -->
    <dimen name="expenses_record_title_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="expenses_record_subtitle_width">67dp</dimen> <!-- 111 × 0.6 -->
    <dimen name="expenses_record_subtitle_height">10dp</dimen> <!-- 17 × 0.6 -->
    <dimen name="expenses_record_subtitle_text_size">8sp</dimen> <!-- 14 × 0.6 -->
    <dimen name="expenses_record_time_width">81dp</dimen> <!-- 135 × 0.6 -->
    <dimen name="expenses_record_time_height">10dp</dimen> <!-- 17 × 0.6 -->
    <dimen name="expenses_record_time_text_size">8sp</dimen> <!-- 14 × 0.6 -->
    <dimen name="expenses_record_margin_start">10dp</dimen> <!-- 16 × 0.6 -->

    <!-- 积分记录列表 -->
    <dimen name="points_item_title_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_item_date_text_size">8sp</dimen> <!-- 14 × 0.6 -->

    <!-- VIP Privileges样式 -->
    <dimen name="vip_privileges_width">206dp</dimen> <!-- 343 × 0.6 -->
    <dimen name="vip_privileges_height">130dp</dimen> <!-- 216 × 0.6 -->
    <dimen name="vip_privileges_text_size">8sp</dimen> <!-- 14 × 0.6 -->

    <!-- Tips部分 -->
    <dimen name="points_tips_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_tips_margin_top">16dp</dimen> <!-- 26 × 0.6 -->
    <dimen name="points_tips_title_text_size">11sp</dimen> <!-- 18 × 0.6 -->
    <dimen name="points_tips_content_text_size">8sp</dimen> <!-- 14 × 0.6 -->
    <dimen name="points_tips_content_margin_top">6dp</dimen> <!-- 10 × 0.6 -->
    <dimen name="points_tips_line_spacing">2dp</dimen> <!-- 3 × 0.6 -->

    <!-- 充值按钮 -->
    <dimen name="points_refill_button_height">30dp</dimen> <!-- 50 × 0.6 -->
    <dimen name="points_refill_button_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_refill_button_margin_top">16dp</dimen> <!-- 26 × 0.6 -->
    <dimen name="points_refill_button_margin_bottom">16dp</dimen> <!-- 26 × 0.6 -->
    <dimen name="points_refill_button_text_size">10sp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_refill_button_corner_radius">4dp</dimen> <!-- 6 × 0.6 -->

    <!-- 分割线 -->
    <dimen name="points_divider_margin_horizontal">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="points_divider_stroke_width">0.2dp</dimen> <!-- 0.4 × 0.6 -->

    <!-- Bill Page - sw230dp (缩放因子: 0.6) -->
    <!-- Header -->
    <dimen name="bill_header_padding_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_header_padding_top">35dp</dimen> <!-- 58 × 0.6 -->
    <dimen name="bill_back_button_size">22dp</dimen> <!-- 36 × 0.6 -->
    <dimen name="bill_back_button_margin_top">0dp</dimen>
    <dimen name="bill_back_button_margin_start">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="bill_title_width">16dp</dimen> <!-- 27 × 0.6 -->
    <dimen name="bill_title_height">13dp</dimen> <!-- 21 × 0.6 -->
    <dimen name="bill_title_margin_top">-2dp</dimen> <!-- -3 × 0.6 -->
    <dimen name="bill_title_text_size">11sp</dimen> <!-- 18 × 0.6 -->

    <!-- Tabs -->
    <dimen name="bill_tabs_margin_horizontal">16dp</dimen> <!-- 统一基准值 -->
    <dimen name="bill_tabs_margin_top">24dp</dimen> <!-- 40 × 0.6 -->
    <dimen name="bill_tabs_background_width">198dp</dimen> <!-- 230-32=198dp -->
    <dimen name="bill_tabs_background_height">24dp</dimen> <!-- 40 × 0.6 -->
    <dimen name="bill_tabs_background_corner_radius">12dp</dimen> <!-- 20 × 0.6 -->
    <dimen name="bill_selected_tab_width">64dp</dimen> <!-- (198-6)/3=64dp -->
    <dimen name="bill_selected_tab_height">20dp</dimen> <!-- 34 × 0.6 -->
    <dimen name="bill_selected_tab_margin_start">2dp</dimen> <!-- 3 × 0.6 -->
    <dimen name="bill_selected_tab_margin_top">2dp</dimen> <!-- 3 × 0.6 -->
    <dimen name="bill_selected_tab_corner_radius">10dp</dimen> <!-- 17 × 0.6 -->
    <dimen name="bill_tab_text_size">8sp</dimen> <!-- 14 × 0.6 -->

    <!-- Content -->
    <dimen name="bill_content_margin_top">14dp</dimen> <!-- 24 × 0.6 -->

    <!-- Empty State -->
    <dimen name="bill_empty_image_width">172dp</dimen> <!-- 286 × 0.6 -->
    <dimen name="bill_empty_image_height">155dp</dimen> <!-- 258 × 0.6 -->
    <dimen name="bill_empty_image_margin_horizontal">27dp</dimen> <!-- 44.5 × 0.6 -->
    <dimen name="bill_empty_image_margin_bottom">130dp</dimen> <!-- 216 × 0.6 -->
    <dimen name="bill_empty_text_margin_top">10dp</dimen> <!-- 16 × 0.6 -->
    <dimen name="bill_empty_text_size">8sp</dimen> <!-- 14 × 0.6 -->

    <!-- VIP Record Card - 统一基准值 -->
    <dimen name="vip_record_first_card_margin_top">22dp</dimen> <!-- 统一基准值 -->

    <!-- Points Purchase Card - 统一基准值 -->
    <dimen name="points_purchase_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Video Record Card - 统一基准值 -->
    <dimen name="video_record_first_card_margin_top">18dp</dimen> <!-- 统一基准值 -->

    <!-- Message Page - 统一基准值 -->
    <dimen name="message_first_card_margin_top">24dp</dimen> <!-- 统一基准值 -->

    <!-- Message Detail Page - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="message_detail_card_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="message_detail_card_min_height">244dp</dimen> <!-- 400 × 0.61 -->
    <dimen name="message_detail_card_margin_top">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="message_detail_divider_width">195dp</dimen> <!-- 319 × 0.61 -->
    <dimen name="message_detail_divider_height">0.5dp</dimen> <!-- 保持不变 -->
    <dimen name="message_detail_divider_margin_vertical">6dp</dimen> <!-- 10 × 0.61 -->

    <!-- Setting Page - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="setting_first_item_margin_start">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_first_item_margin_top">20dp</dimen> <!-- 32 × 0.61 -->
    <dimen name="setting_double_item_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="setting_double_item_height">57dp</dimen> <!-- 94 × 0.61 -->
    <dimen name="setting_single_item_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="setting_single_item_height">31dp</dimen> <!-- 51 × 0.61 -->
    <dimen name="setting_item_margin_bottom">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_item_padding">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_text_size">13sp</dimen> <!-- 16 × 0.81 -->
    <dimen name="setting_text_line_height">12dp</dimen> <!-- 19 × 0.61 -->
    <dimen name="setting_arrow_size">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_terms_text_width">74dp</dimen> <!-- 121 × 0.61 -->
    <dimen name="setting_cache_text_width">52dp</dimen> <!-- 86 × 0.61 -->
    <dimen name="setting_cache_size_width">34dp</dimen> <!-- 55 × 0.61 -->
    <dimen name="setting_bottom_buttons_margin_top">20dp</dimen> <!-- 32 × 0.61 -->
    <dimen name="setting_bottom_buttons_margin_bottom">20dp</dimen> <!-- 32 × 0.61 -->
    <dimen name="setting_bottom_button_margin_horizontal">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_bottom_button_margin_between">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="setting_bottom_button_corner_radius">13dp</dimen> <!-- 21 × 0.61 -->

    <!-- User info section - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="profile_avatar_size">41dp</dimen> <!-- 68 × 0.61 -->
    <dimen name="profile_avatar_margin_start">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="profile_avatar_margin_top">27dp</dimen> <!-- 45 × 0.61 -->

    <!-- Edit Profile Page - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="edit_profile_card_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="edit_profile_card_height">146dp</dimen> <!-- 240 × 0.61 -->
    <dimen name="edit_profile_card_margin_top">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="edit_profile_avatar_size">29dp</dimen> <!-- 48 × 0.61 -->
    <dimen name="edit_profile_arrow_size">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="edit_profile_divider_width">190dp</dimen> <!-- 311 × 0.61 -->
    <dimen name="edit_profile_input_width">190dp</dimen> <!-- 311 × 0.61 -->
    <dimen name="edit_profile_input_height">29dp</dimen> <!-- 48 × 0.61 -->
    <dimen name="edit_profile_ip_location_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="edit_profile_ip_location_height">31dp</dimen> <!-- 50 × 0.61 -->
    <dimen name="edit_profile_save_button_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="edit_profile_save_button_height">26dp</dimen> <!-- 42 × 0.61 -->
    <dimen name="edit_profile_text_size">13sp</dimen> <!-- 16 × 0.81 -->
    <dimen name="edit_profile_text_line_height">12dp</dimen> <!-- 19 × 0.61 -->
    <dimen name="edit_profile_china_text_width">37dp</dimen> <!-- 60 × 0.61 -->
    <dimen name="edit_profile_save_text_width">23dp</dimen> <!-- 37 × 0.61 -->
    <dimen name="edit_profile_item_margin_bottom">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="edit_profile_item_padding">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="edit_profile_save_button_margin_bottom">23dp</dimen> <!-- 38 × 0.61 -->
    <dimen name="edit_profile_popup_width">73dp</dimen> <!-- 120 × 0.61 -->
    <dimen name="edit_profile_popup_item_height">20dp</dimen> <!-- 32 × 0.61 -->
    <dimen name="edit_profile_popup_padding">5dp</dimen> <!-- 8 × 0.61 -->
    <dimen name="edit_profile_popup_text_size">11sp</dimen> <!-- 14 × 0.79 -->
    <dimen name="edit_profile_popup_corner_radius">5dp</dimen> <!-- 8 × 0.61 -->

    <!-- Log Off Page - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="log_off_warning_icon_size">37dp</dimen> <!-- 60 × 0.61 -->
    <dimen name="log_off_warning_icon_margin_top">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="log_off_description_width">200dp</dimen> <!-- 328 × 0.61 -->
    <dimen name="log_off_description_height">37dp</dimen> <!-- 60 × 0.61 -->
    <dimen name="log_off_description_margin_top">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="log_off_card_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="log_off_card_height">195dp</dimen> <!-- 320 × 0.61 -->
    <dimen name="log_off_card_margin_top">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="log_off_card_padding">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="log_off_card_item_line_height">16dp</dimen> <!-- 26 × 0.61 -->
    <dimen name="log_off_checkbox_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="log_off_checkbox_height">37dp</dimen> <!-- 60 × 0.61 -->
    <dimen name="log_off_checkbox_margin_top">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="log_off_checkbox_icon_size">12dp</dimen> <!-- 20 × 0.61 -->
    <dimen name="log_off_checkbox_icon_margin_end">5dp</dimen> <!-- 8 × 0.61 -->
    <dimen name="log_off_checkbox_text_margin_start">17dp</dimen> <!-- 28 × 0.61 -->
    <dimen name="log_off_checkbox_text_size">11sp</dimen> <!-- 13 × 0.85 -->
    <dimen name="log_off_checkbox_text_line_spacing">4dp</dimen> <!-- 7 × 0.61 -->
    <dimen name="log_off_delete_button_width">210dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="log_off_delete_button_height">26dp</dimen> <!-- 42 × 0.61 -->
    <dimen name="log_off_delete_button_margin_top">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="log_off_delete_button_margin_bottom">23dp</dimen> <!-- 38 × 0.61 -->
    <dimen name="log_off_delete_text_width">70dp</dimen> <!-- 115 × 0.61 -->
    <dimen name="log_off_delete_text_height">12dp</dimen> <!-- 19 × 0.61 -->
    <dimen name="log_off_item_margin_horizontal">10dp</dimen> <!-- 16 × 0.61 -->

    <!-- Feedback Page - 适配230dp屏幕 (230/375 ≈ 0.61) -->
    <dimen name="feedback_required_star_width">4dp</dimen> <!-- 7 × 0.61 -->
    <dimen name="feedback_required_star_height">12dp</dimen> <!-- 19 × 0.61 -->
    <dimen name="feedback_label_width">66dp</dimen> <!-- 108 × 0.61 -->
    <dimen name="feedback_label_height">12dp</dimen> <!-- 19 × 0.61 -->
    <dimen name="feedback_type_selected_width">69dp</dimen> <!-- 113 × 0.61 -->
    <dimen name="feedback_type_selected_height">24dp</dimen> <!-- 40 × 0.61 -->
    <dimen name="feedback_type_unselected_width">133dp</dimen> <!-- 218 × 0.61 -->
    <dimen name="feedback_type_unselected_height">24dp</dimen> <!-- 40 × 0.61 -->
    <dimen name="feedback_type_corner_radius">9dp</dimen> <!-- 14 × 0.61 -->
    <dimen name="feedback_input_width">209dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="feedback_input_height">113dp</dimen> <!-- 186 × 0.61 -->
    <dimen name="feedback_input_corner_radius">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="feedback_input_padding">9dp</dimen> <!-- 15 × 0.61 -->
    <dimen name="feedback_image_size">65dp</dimen> <!-- 107 × 0.61 -->
    <dimen name="feedback_image_corner_radius">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="feedback_image_icon_size">17dp</dimen> <!-- 28 × 0.61 -->
    <dimen name="feedback_button_width">209dp</dimen> <!-- 343 × 0.61 -->
    <dimen name="feedback_button_height">26dp</dimen> <!-- 42 × 0.61 -->
    <dimen name="feedback_button_corner_radius">13dp</dimen> <!-- 21 × 0.61 -->
    <dimen name="feedback_item_margin_horizontal">10dp</dimen> <!-- 16 × 0.61 -->
    <dimen name="feedback_item_margin_vertical">15dp</dimen> <!-- 24 × 0.61 -->
    <dimen name="feedback_type_margin_end">5dp</dimen> <!-- 8 × 0.61 -->

    <!-- My List History Layout Dimensions (230dp屏幕适配，缩放因子: 0.613，基准375dp) -->
    <dimen name="my_list_history_item_height">110dp</dimen> <!-- 180 × 0.613 = 110dp -->
    <dimen name="my_list_history_item_margin_start">7dp</dimen> <!-- 12 × 0.613 = 7dp -->
    <dimen name="my_list_history_item_margin_end">7dp</dimen> <!-- 12 × 0.613 = 7dp -->
    <dimen name="my_list_history_item_margin_bottom">2dp</dimen> <!-- 4 × 0.613 = 2dp -->
    <dimen name="my_list_history_poster_width">74dp</dimen> <!-- 120 × 0.613 = 74dp -->
    <dimen name="my_list_history_poster_height">98dp</dimen> <!-- 160 × 0.613 = 98dp -->
    <dimen name="my_list_history_poster_corner_radius">9dp</dimen> <!-- 15 × 0.613 = 9dp -->
    <dimen name="my_list_history_poster_margin_end">10dp</dimen> <!-- 16 × 0.613 = 10dp -->
    <dimen name="my_list_history_title_width">110dp</dimen> <!-- 180 × 0.613 = 110dp -->
    <dimen name="my_list_history_title_height">17dp</dimen> <!-- 28 × 0.613 = 17dp -->
    <dimen name="my_list_history_title_text_size">13sp</dimen> <!-- 22 × 0.613 = 13sp -->
    <dimen name="my_list_history_episode_text_size">12sp</dimen> <!-- 20 × 0.613 = 12sp -->
    <dimen name="my_list_history_episode_margin_top">10dp</dimen> <!-- 16 × 0.613 = 10dp -->
    <dimen name="my_list_history_play_button_width">61dp</dimen> <!-- 100 × 0.613 = 61dp -->
    <dimen name="my_list_history_play_button_height">31dp</dimen> <!-- 50 × 0.613 = 31dp -->
    <dimen name="my_list_history_play_button_corner_radius">15dp</dimen> <!-- 25 × 0.613 = 15dp -->
    <dimen name="my_list_history_play_button_margin_end">7dp</dimen> <!-- 12 × 0.613 = 7dp -->
    <dimen name="my_list_history_play_icon_size">15dp</dimen> <!-- 24 × 0.613 = 15dp -->
    <dimen name="my_list_history_play_text_size">11sp</dimen> <!-- 18 × 0.613 = 11sp -->

    <!-- Interest Tab Card Dimensions - sw230dp (缩放因子: 0.7，避免过小) -->
    <!-- Card Layout -->
    <dimen name="interest_card_margin_horizontal">11dp</dimen> <!-- 16 × 0.7 = 11dp -->
    <dimen name="interest_card_margin_bottom">13dp</dimen> <!-- 18 × 0.7 = 13dp -->

    <!-- Poster - 放大尺寸 -->
    <dimen name="interest_poster_width">84dp</dimen> <!-- 120 × 0.7 = 84dp -->
    <dimen name="interest_poster_height">112dp</dimen> <!-- 160 × 0.7 = 112dp -->

    <!-- Content Area -->
    <dimen name="interest_content_margin_start">10dp</dimen> <!-- 14 × 0.7 = 10dp -->

    <!-- Title -->
    <dimen name="interest_title_width">140dp</dimen> <!-- 200 × 0.7 = 140dp -->
    <dimen name="interest_title_height">15dp</dimen> <!-- 22 × 0.7 = 15dp -->
    <dimen name="interest_title_text_size">11sp</dimen> <!-- 16 × 0.7 = 11sp -->

    <!-- Status Tag - 调整位置和大小 -->
    <dimen name="interest_status_tag_width">62dp</dimen> <!-- 88 × 0.7 = 62dp -->
    <dimen name="interest_status_tag_height">15dp</dimen> <!-- 22 × 0.7 = 15dp -->
    <dimen name="interest_status_tag_margin_top">4dp</dimen> <!-- 6 × 0.7 = 4dp -->
    <dimen name="interest_status_text_width">53dp</dimen> <!-- 76 × 0.7 = 53dp -->
    <dimen name="interest_status_text_height">10dp</dimen> <!-- 14 × 0.7 = 10dp -->
    <dimen name="interest_status_text_size">8sp</dimen> <!-- 12 × 0.7 = 8sp -->

    <!-- Description - 优化文字大小 -->
    <dimen name="interest_description_width">140dp</dimen> <!-- 200 × 0.7 = 140dp -->
    <dimen name="interest_description_height">25dp</dimen> <!-- 36 × 0.7 = 25dp -->
    <dimen name="interest_description_margin_top">6dp</dimen> <!-- 8 × 0.7 = 6dp -->
    <dimen name="interest_description_text_size">8sp</dimen> <!-- 12 × 0.7 = 8sp -->

    <!-- Time Info -->
    <dimen name="interest_time_container_margin_top">8dp</dimen> <!-- 12 × 0.7 = 8dp -->
    <dimen name="interest_time_icon_size">10dp</dimen> <!-- 14 × 0.7 = 10dp -->
    <dimen name="interest_time_text_width">56dp</dimen> <!-- 80 × 0.7 = 56dp -->
    <dimen name="interest_time_text_height">13dp</dimen> <!-- 18 × 0.7 = 13dp -->
    <dimen name="interest_time_text_margin_start">3dp</dimen> <!-- 4 × 0.7 = 3dp -->
    <dimen name="interest_time_text_size">8sp</dimen> <!-- 12 × 0.7 = 8sp -->

    <!-- Cancel Button -->
    <dimen name="interest_cancel_button_width">140dp</dimen> <!-- 200 × 0.7 = 140dp -->
    <dimen name="interest_cancel_button_height">20dp</dimen> <!-- 28 × 0.7 = 20dp -->
    <dimen name="interest_cancel_button_margin_top">8dp</dimen> <!-- 12 × 0.7 = 8dp -->
    <dimen name="interest_cancel_icon_size">10dp</dimen> <!-- 14 × 0.7 = 10dp -->
    <dimen name="interest_cancel_text_width">56dp</dimen> <!-- 80 × 0.7 = 56dp -->
    <dimen name="interest_cancel_text_height">10dp</dimen> <!-- 14 × 0.7 = 10dp -->
    <dimen name="interest_cancel_text_margin_start">3dp</dimen> <!-- 4 × 0.7 = 3dp -->
    <dimen name="interest_cancel_text_size">8sp</dimen> <!-- 12 × 0.7 = 8sp -->

    <!-- Search Page Dimensions Scaled for 230dp (0.61x) -->

    <!-- Back button -->
    <dimen name="search_back_button_size">15dp</dimen>
    <dimen name="search_back_button_margin_start">10dp</dimen>
    <dimen name="search_back_button_margin_top">33dp</dimen>

    <!-- Search input -->
    <dimen name="search_input_width">187dp</dimen>
    <dimen name="search_input_height">23dp</dimen>
    <dimen name="search_input_margin_start">32dp</dimen>
    <dimen name="search_input_margin_top">29dp</dimen>
    <dimen name="search_input_corner_radius">7dp</dimen>
    <dimen name="search_input_padding_start">7dp</dimen>
    <dimen name="search_input_padding_vertical">5dp</dimen>

    <!-- Search icon in input -->
    <dimen name="search_input_icon_size">10dp</dimen>
    <dimen name="search_input_icon_margin_start">7dp</dimen>

    <!-- Search text -->
    <dimen name="search_text_size">10sp</dimen>
    <dimen name="search_text_margin_start">60dp</dimen>
    <dimen name="search_text_margin_top">34dp</dimen>

    <!-- Recent searches section -->
    <dimen name="search_recent_title_margin_start">10dp</dimen>
    <dimen name="search_recent_title_margin_top">63dp</dimen>
    <dimen name="search_recent_title_size">11sp</dimen>

    <!-- Clear recent searches icon -->
    <dimen name="search_clear_icon_size">11dp</dimen>
    <dimen name="search_clear_icon_margin_end">10dp</dimen>
    <dimen name="search_clear_icon_margin_top">64dp</dimen>

    <!-- Recent search tags -->
    <dimen name="search_recent_tag_height">20dp</dimen>
    <dimen name="search_recent_tag_padding_horizontal">7dp</dimen>
    <dimen name="search_recent_tag_padding_vertical">4dp</dimen>
    <dimen name="search_recent_tag_margin">2dp</dimen>
    <dimen name="search_recent_tag_text_size">9sp</dimen> <!-- 14 × 0.61 -->
    <dimen name="search_recent_tag_corner_radius">10dp</dimen> <!-- 16 × 0.61 -->

    <!-- Trending searches section -->
    <dimen name="search_trending_title_size">11sp</dimen>
    <dimen name="search_trending_title_margin_start">10dp</dimen>
    <dimen name="search_trending_title_margin_top">7dp</dimen>

    <!-- Trending search cards -->
    <dimen name="search_trending_card_width">209dp</dimen>
    <dimen name="search_trending_card_height">59dp</dimen>
    <dimen name="search_trending_card_margin_horizontal">10dp</dimen>
    <dimen name="search_trending_card_margin_vertical">4dp</dimen>
    <dimen name="search_trending_card_corner_radius">7dp</dimen>
    <dimen name="search_trending_card_padding">9dp</dimen>

    <!-- Rank background -->
    <dimen name="search_rank_bg_size">17dp</dimen>
    <dimen name="search_rank_bg_corner_radius">4dp</dimen>
    <dimen name="search_rank_text_size">10sp</dimen>

    <!-- Poster in trending card -->
    <dimen name="search_poster_size">41dp</dimen>
    <dimen name="search_poster_corner_radius">5dp</dimen>
    <dimen name="search_poster_margin_start">6dp</dimen>

    <!-- Content in trending card -->
    <dimen name="search_content_margin_start">6dp</dimen>
    <dimen name="search_title_text_size">9sp</dimen>
    <dimen name="search_count_text_size">7sp</dimen>
    <dimen name="search_count_icon_size">10dp</dimen>
    <dimen name="search_count_icon_margin_end">2dp</dimen>
    <dimen name="search_count_margin_top">14dp</dimen>

    <!-- Search Result Screen - Extra Small phone (230dp, scale factor: 0.56) -->

    <!-- Header Section -->
    <dimen name="search_result_header_height">49dp</dimen>
    <dimen name="search_result_header_padding_horizontal">9dp</dimen>
    <dimen name="search_result_header_padding_top">25dp</dimen>

    <!-- Back Button -->
    <dimen name="search_result_back_button_size">13dp</dimen>

    <!-- Search Box -->
    <dimen name="search_result_search_box_height">20dp</dimen>
    <dimen name="search_result_search_box_margin_start">9dp</dimen>
    <dimen name="search_result_search_box_margin_end">9dp</dimen>
    <dimen name="search_result_search_box_padding_horizontal">7dp</dimen>

    <!-- Search Icon -->
    <dimen name="search_result_search_icon_size">9dp</dimen>
    <dimen name="search_result_search_icon_margin_end">4dp</dimen>

    <!-- Search Text -->
    <dimen name="search_result_search_text_size">8sp</dimen>

    <!-- Clear Button -->
    <dimen name="search_result_clear_button_size">13dp</dimen>

    <!-- Content Section -->
    <dimen name="search_result_content_padding_horizontal">9dp</dimen>

    <!-- Result Count -->
    <dimen name="search_result_count_padding_vertical">7dp</dimen>
    <dimen name="search_result_count_text_size">7sp</dimen>

    <!-- Video Item -->
    <dimen name="search_result_item_height">44dp</dimen>
    <dimen name="search_result_item_margin_bottom">10dp</dimen>
    <dimen name="search_result_item_padding_horizontal">0dp</dimen>
    <dimen name="search_result_item_padding_vertical">0dp</dimen>

    <!-- Poster -->
    <dimen name="search_result_poster_width">66dp</dimen>
    <dimen name="search_result_poster_height">44dp</dimen>
    <dimen name="search_result_poster_margin_end">7dp</dimen>

    <!-- Title -->
    <dimen name="search_result_title_height">11dp</dimen>
    <dimen name="search_result_title_margin_end">4dp</dimen>
    <dimen name="search_result_title_text_size">9sp</dimen>

    <!-- Episode Info -->
    <dimen name="search_result_episode_margin_top">4dp</dimen>
    <dimen name="search_result_episode_text_size">7sp</dimen>

    <!-- Play Button -->
    <dimen name="search_result_play_button_size">18dp</dimen>
    <dimen name="search_result_play_button_margin_end">9dp</dimen>

    <!-- Like Button -->
    <dimen name="search_result_like_button_size">13dp</dimen>

    <!-- Empty State -->
    <dimen name="empty_state_padding_horizontal">18dp</dimen>
    <dimen name="empty_state_container_width">160dp</dimen>
    <dimen name="empty_state_container_height">160dp</dimen>
    <dimen name="empty_state_image_width">160dp</dimen>
    <dimen name="empty_state_image_height">160dp</dimen>
    <dimen name="empty_state_text_margin_top">9dp</dimen>
    <dimen name="empty_state_text_size">8sp</dimen>

    <!-- Information Page Dimensions - Ultra Small phone (230dp, scale factor: 0.61) -->

    <!-- Content margins -->
    <dimen name="information_content_margin_horizontal">10dp</dimen>
    <dimen name="information_content_margin_top">15dp</dimen>
    <dimen name="information_content_margin_bottom">15dp</dimen>

    <!-- Text dimensions -->
    <dimen name="information_content_text_size">12sp</dimen>
    <dimen name="information_content_line_spacing">2dp</dimen>

    <!-- Loading state -->
    <dimen name="information_loading_margin_top">61dp</dimen>

    <!-- Payment Method Dialog - Extra Small phone (230dp, scale factor 0.7) -->
    <dimen name="payment_dialog_width">224dp</dimen> <!-- 320 × 0.7 -->
    <dimen name="payment_dialog_padding">17dp</dimen> <!-- 24 × 0.7 -->
    <dimen name="payment_dialog_title_text_size">13sp</dimen> <!-- 18 × 0.7 -->
    <dimen name="payment_dialog_option_height">34dp</dimen> <!-- 48 × 0.7 -->
    <dimen name="payment_dialog_option_padding_horizontal">11dp</dimen> <!-- 16 × 0.7 -->
    <dimen name="payment_dialog_option_text_size">11sp</dimen> <!-- 16 × 0.7 -->
    <dimen name="payment_dialog_icon_size">17dp</dimen> <!-- 24 × 0.7 -->
    <dimen name="payment_dialog_icon_margin_end">8dp</dimen> <!-- 12 × 0.7 -->
    <dimen name="payment_dialog_options_margin_top">14dp</dimen> <!-- 20 × 0.7 -->
    <dimen name="payment_dialog_divider_height">0.4dp</dimen> <!-- 0.5 × 0.7 -->
    <dimen name="payment_dialog_divider_margin_horizontal">11dp</dimen> <!-- 16 × 0.7 -->

</resources>
