<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:app="http://schemas.android.com/apk/res-auto"
              android:layout_width="wrap_content"
              android:layout_height="wrap_content"
              android:layout_marginEnd="@dimen/video_detail_actor_spacing"
              android:orientation="vertical"
              android:gravity="center"
              android:clickable="true"
              android:focusable="true"
              android:background="?android:attr/selectableItemBackground">

    <!-- 演员头像容器 -->
    <FrameLayout
            android:layout_width="@dimen/video_detail_actor_poster_width"
            android:layout_height="@dimen/video_detail_actor_poster_height"
            android:background="@drawable/actor_poster_border">

        <ImageView
                android:id="@+id/iv_actor_avatar"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_margin="1dp"
                android:scaleType="centerCrop"
                android:src="@drawable/movie_poster"
                android:background="@drawable/actor_poster_inner"
                android:clipToOutline="true" />

    </FrameLayout>

    <!-- 演员姓名 -->
    <TextView
            android:id="@+id/tv_actor_name"
            android:layout_width="@dimen/video_detail_actor_poster_width"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/video_detail_actor_name_margin_top"
            android:text="演员姓名"
            android:textColor="#FFFFFF"
            android:textSize="12sp"
            android:gravity="center"
            android:maxLines="2"
            android:ellipsize="end" />

</LinearLayout>