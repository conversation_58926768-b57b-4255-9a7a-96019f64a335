package com.android.video.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.ArrayList;

/**
 * 我的订阅列表分页数据模型
 * <AUTHOR> Team
 */
public class MySubscribeDataModel {
    
    /**
     * 订阅记录列表
     */
    @SerializedName("records")
    private List<MySubscribeItemModel> records;
    
    /**
     * 总记录数
     */
    @SerializedName("total")
    private int total;
    
    /**
     * 每页数量
     */
    @SerializedName("size")
    private int size;
    
    /**
     * 当前页码
     */
    @SerializedName("current")
    private int current;
    
    /**
     * 排序信息
     */
    @SerializedName("orders")
    private List<Object> orders;
    
    /**
     * 是否优化count SQL
     */
    @SerializedName("optimizeCountSql")
    private boolean optimizeCountSql;
    
    /**
     * 是否命中count
     */
    @SerializedName("hitCount")
    private boolean hitCount;
    
    /**
     * count ID
     */
    @SerializedName("countId")
    private String countId;
    
    /**
     * 最大限制
     */
    @SerializedName("maxLimit")
    private String maxLimit;
    
    /**
     * 是否搜索count
     */
    @SerializedName("searchCount")
    private boolean searchCount;
    
    /**
     * 总页数
     */
    @SerializedName("pages")
    private int pages;

    public MySubscribeDataModel() {
        this.records = new ArrayList<>();
        this.orders = new ArrayList<>();
    }

    public MySubscribeDataModel(List<MySubscribeItemModel> records, int total, int size, int current, int pages) {
        this.records = records != null ? records : new ArrayList<>();
        this.total = total;
        this.size = size;
        this.current = current;
        this.pages = pages;
        this.orders = new ArrayList<>();
        this.optimizeCountSql = true;
        this.hitCount = false;
        this.searchCount = true;
    }

    // Getters and Setters
    public List<MySubscribeItemModel> getRecords() {
        return records;
    }

    public void setRecords(List<MySubscribeItemModel> records) {
        this.records = records != null ? records : new ArrayList<>();
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    public int getSize() {
        return size;
    }

    public void setSize(int size) {
        this.size = size;
    }

    public int getCurrent() {
        return current;
    }

    public void setCurrent(int current) {
        this.current = current;
    }

    public List<Object> getOrders() {
        return orders;
    }

    public void setOrders(List<Object> orders) {
        this.orders = orders != null ? orders : new ArrayList<>();
    }

    public boolean isOptimizeCountSql() {
        return optimizeCountSql;
    }

    public void setOptimizeCountSql(boolean optimizeCountSql) {
        this.optimizeCountSql = optimizeCountSql;
    }

    public boolean isHitCount() {
        return hitCount;
    }

    public void setHitCount(boolean hitCount) {
        this.hitCount = hitCount;
    }

    public String getCountId() {
        return countId;
    }

    public void setCountId(String countId) {
        this.countId = countId;
    }

    public String getMaxLimit() {
        return maxLimit;
    }

    public void setMaxLimit(String maxLimit) {
        this.maxLimit = maxLimit;
    }

    public boolean isSearchCount() {
        return searchCount;
    }

    public void setSearchCount(boolean searchCount) {
        this.searchCount = searchCount;
    }

    public int getPages() {
        return pages;
    }

    public void setPages(int pages) {
        this.pages = pages;
    }

    /**
     * 检查是否还有更多页面
     * @return 是否还有更多页面
     */
    public boolean hasMorePages() {
        return current < pages;
    }

    /**
     * 检查是否为空数据
     * @return 是否为空
     */
    public boolean isEmpty() {
        return records == null || records.isEmpty();
    }

    /**
     * 获取下一页页码
     * @return 下一页页码，如果没有更多页面则返回-1
     */
    public int getNextPage() {
        return hasMorePages() ? current + 1 : -1;
    }

    /**
     * 转换为MyListVideo列表
     * @return MyListVideo列表
     */
    public List<MyListVideo> toMyListVideoList() {
        List<MyListVideo> videos = new ArrayList<>();
        if (records != null) {
            for (MySubscribeItemModel item : records) {
                videos.add(item.toMyListVideo());
            }
        }
        return videos;
    }

    /**
     * 按语言类型统计
     * @param languageType 语言类型
     * @return 指定语言类型的数量
     */
    public int getCountByLanguageType(int languageType) {
        int count = 0;
        if (records != null) {
            for (MySubscribeItemModel item : records) {
                if (item.getLanguageType() == languageType) {
                    count++;
                }
            }
        }
        return count;
    }

    /**
     * 获取有发布时间的订阅数量
     * @return 有发布时间的订阅数量
     */
    public int getCountWithReleaseTime() {
        int count = 0;
        if (records != null) {
            for (MySubscribeItemModel item : records) {
                if (item.hasReleaseTime()) {
                    count++;
                }
            }
        }
        return count;
    }

    @Override
    public String toString() {
        return "MySubscribeDataModel{" +
                "records=" + (records != null ? records.size() : 0) + " items" +
                ", total=" + total +
                ", size=" + size +
                ", current=" + current +
                ", pages=" + pages +
                ", hasMorePages=" + hasMorePages() +
                ", englishCount=" + getCountByLanguageType(1) +
                ", russianCount=" + getCountByLanguageType(2) +
                ", withReleaseTimeCount=" + getCountWithReleaseTime() +
                '}';
    }
}
