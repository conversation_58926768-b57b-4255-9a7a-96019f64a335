package com.android.video.ui.adapter;

import android.graphics.Outline;
import android.os.Build;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.view.ViewOutlineProvider;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.MyListVideo;
import java.util.List;

/**
 * My List视频适配器
 */
public class MyListVideoAdapter extends RecyclerView.Adapter<RecyclerView.ViewHolder> {

    private static final int TYPE_GRID = 0;
    private static final int TYPE_HISTORY = 1;
    private static final int TYPE_INTEREST = 2;

    private List<MyListVideo> videos;
    private OnVideoClickListener listener;
    private String currentTab = "following"; // 默认为following

    public interface OnVideoClickListener {
        void onVideoClick(MyListVideo video);
        void onPlayClick(MyListVideo video);
        void onLikeClick(MyListVideo video);
    }

    public MyListVideoAdapter(List<MyListVideo> videos, OnVideoClickListener listener) {
        this.videos = videos;
        this.listener = listener;
    }

    public void setCurrentTab(String currentTab) {
        this.currentTab = currentTab;
    }

    @Override
    public int getItemViewType(int position) {
        if ("history".equals(currentTab)) {
            return TYPE_HISTORY;
        } else if ("interest".equals(currentTab)) {
            return TYPE_INTEREST;
        } else {
            return TYPE_GRID;
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        if (viewType == TYPE_HISTORY) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_my_list_video_history, parent, false);
            return new HistoryViewHolder(view);
        } else if (viewType == TYPE_INTEREST) {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_my_list_video_interest, parent, false);
            return new InterestViewHolder(view);
        } else {
            View view = LayoutInflater.from(parent.getContext())
                    .inflate(R.layout.item_my_list_video, parent, false);
            return new GridViewHolder(view);
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        MyListVideo video = videos.get(position);
        if (holder instanceof HistoryViewHolder) {
            ((HistoryViewHolder) holder).bind(video);
        } else if (holder instanceof InterestViewHolder) {
            ((InterestViewHolder) holder).bind(video);
        } else if (holder instanceof GridViewHolder) {
            ((GridViewHolder) holder).bind(video);
        }
    }

    @Override
    public int getItemCount() {
        return videos.size();
    }

    public void updateVideos(List<MyListVideo> newVideos) {
        this.videos = newVideos;
        notifyDataSetChanged();
    }

    /**
     * 更新视频列表（别名方法，保持与其他适配器一致）
     */
    public void updateVideoList(List<MyListVideo> newVideos) {
        updateVideos(newVideos);
    }

    /**
     * 获取视频列表
     */
    public List<MyListVideo> getVideoList() {
        return videos;
    }

    @Override
    public void onViewRecycled(@NonNull RecyclerView.ViewHolder holder) {
        super.onViewRecycled(holder);
        // 确保ViewHolder回收时状态正确重置
        if (holder instanceof GridViewHolder) {
            GridViewHolder gridHolder = (GridViewHolder) holder;
            // 重置ImageView状态
            gridHolder.ivPoster.setImageDrawable(null);
            gridHolder.ivPoster.setScaleType(ImageView.ScaleType.CENTER_CROP);
        }
    }

    // History布局ViewHolder
    class HistoryViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private TextView tvTitle;
        private TextView tvCurrentEpisode;
        private TextView tvTotalEpisode;
        private LinearLayout btnPlay;

        public HistoryViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvCurrentEpisode = itemView.findViewById(R.id.tv_current_episode);
            tvTotalEpisode = itemView.findViewById(R.id.tv_total_episode);
            btnPlay = itemView.findViewById(R.id.btn_play);
        }

        public void bind(MyListVideo video) {
            // 设置标题
            tvTitle.setText(video.getTitle());

            // 设置剧集信息
            tvCurrentEpisode.setText("EP." + video.getCurrentEpisode());
            tvTotalEpisode.setText("/EP." + video.getTotalEpisodes());

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onVideoClick(video);
                }
            });

            btnPlay.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onPlayClick(video);
                }
            });
        }
    }

    // 网格布局ViewHolder (Following标签页使用)
    class GridViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private ImageView ivLike;
        private TextView tvTitle;

        public GridViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            ivLike = itemView.findViewById(R.id.iv_like);
            tvTitle = itemView.findViewById(R.id.tv_title);
        }

        public void bind(MyListVideo video) {
            // 设置标题
            tvTitle.setText(video.getTitle());

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 确保ImageView的scaleType正确设置
            ivPoster.setScaleType(ImageView.ScaleType.CENTER_CROP);

            // 编程方式强制设置圆角 - 双重保险
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                float cornerRadius = itemView.getContext().getResources().getDimension(R.dimen.my_list_grid_poster_corner_radius);
                ivPoster.setOutlineProvider(new ViewOutlineProvider() {
                    @Override
                    public void getOutline(View view, Outline outline) {
                        outline.setRoundRect(0, 0, view.getWidth(), view.getHeight(), cornerRadius);
                    }
                });
                ivPoster.setClipToOutline(true);
            }

            // 设置喜欢状态
            ivLike.setImageResource(video.isLiked() ? R.drawable.ic_like : R.drawable.movie_ic_no_like);

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onVideoClick(video);
                }
            });

            ivLike.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onLikeClick(video);
                }
            });
        }
    }

    // Interest布局ViewHolder
    class InterestViewHolder extends RecyclerView.ViewHolder {
        private ImageView ivPoster;
        private TextView tvTitle;
        private TextView tvStatus;
        private TextView tvDescription;
        private ImageView ivTimeIcon;
        private TextView tvTime;
        private LinearLayout btnCancelNotify;
        private ImageView ivCancelIcon;
        private TextView tvCancelText;

        public InterestViewHolder(@NonNull View itemView) {
            super(itemView);
            ivPoster = itemView.findViewById(R.id.iv_poster);
            tvTitle = itemView.findViewById(R.id.tv_title);
            tvStatus = itemView.findViewById(R.id.tv_status);
            tvDescription = itemView.findViewById(R.id.tv_description);
            ivTimeIcon = itemView.findViewById(R.id.iv_time_icon);
            tvTime = itemView.findViewById(R.id.tv_time);
            btnCancelNotify = itemView.findViewById(R.id.btn_cancel_notify);
            ivCancelIcon = itemView.findViewById(R.id.iv_cancel_icon);
            tvCancelText = itemView.findViewById(R.id.tv_cancel_text);
        }

        public void bind(MyListVideo video) {
            // 设置标题
            tvTitle.setText(video.getTitle());

            // 设置状态标签
            tvStatus.setText("coming soon");

            // 设置描述
            if (video.getDescription() != null && !video.getDescription().isEmpty()) {
                tvDescription.setText(video.getDescription());
            } else {
                tvDescription.setText("a captivating historical drama set in a grand palace, where love …");
            }

            // 设置时间
            if (video.getReleaseDate() != null && !video.getReleaseDate().isEmpty()) {
                tvTime.setText(video.getReleaseDate());
            } else {
                tvTime.setText("2025-06-18");
            }

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivPoster);
            } else {
                ivPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (listener != null) {
                    listener.onVideoClick(video);
                }
            });

            btnCancelNotify.setOnClickListener(v -> {
                if (listener != null) {
                    // 可以添加取消通知的逻辑
                    listener.onVideoClick(video);
                }
            });
        }
    }
}
