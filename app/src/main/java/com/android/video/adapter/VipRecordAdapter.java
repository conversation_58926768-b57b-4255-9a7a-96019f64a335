package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.model.VipRecord;

import java.util.List;

/**
 * VIP记录适配器
 */
public class VipRecordAdapter extends RecyclerView.Adapter<VipRecordAdapter.VipRecordViewHolder> {

    private List<VipRecord> vipRecords;

    public VipRecordAdapter(List<VipRecord> vipRecords) {
        this.vipRecords = vipRecords;
    }

    @NonNull
    @Override
    public VipRecordViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_vip_record, parent, false);
        return new VipRecordViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull VipRecordViewHolder holder, int position) {
        VipRecord record = vipRecords.get(position);
        holder.bind(record);
    }

    @Override
    public int getItemCount() {
        return vipRecords != null ? vipRecords.size() : 0;
    }

    /**
     * 更新数据
     */
    public void updateData(List<VipRecord> newRecords) {
        this.vipRecords = newRecords;
        notifyDataSetChanged();
    }

    /**
     * ViewHolder类
     */
    static class VipRecordViewHolder extends RecyclerView.ViewHolder {
        private TextView tvPurchaseType;
        private TextView tvPrice;
        private TextView tvDateTime;
        private TextView tvPaymentMethod;
        private ImageView ivPaymentIcon;

        public VipRecordViewHolder(@NonNull View itemView) {
            super(itemView);
            tvPurchaseType = itemView.findViewById(R.id.tv_purchase_type);
            tvPrice = itemView.findViewById(R.id.tv_price);
            tvDateTime = itemView.findViewById(R.id.tv_date_time);
            tvPaymentMethod = itemView.findViewById(R.id.tv_payment_method);
            ivPaymentIcon = itemView.findViewById(R.id.iv_payment_icon);
        }

        /**
         * 绑定数据
         */
        public void bind(VipRecord record) {
            if (record == null) {
                return;
            }

            tvPurchaseType.setText(record.getPurchaseType());
            tvPrice.setText(record.getPrice());
            tvDateTime.setText(record.getDateTime());
            tvPaymentMethod.setText(record.getPaymentMethod());
            
            // 设置支付方式图标
            if (record.getPaymentIconRes() != 0) {
                ivPaymentIcon.setImageResource(record.getPaymentIconRes());
                ivPaymentIcon.setVisibility(View.VISIBLE);
            } else {
                ivPaymentIcon.setVisibility(View.GONE);
            }
        }
    }
}
