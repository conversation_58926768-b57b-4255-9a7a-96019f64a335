package com.android.video.monitor;

import android.app.Activity;
import android.app.AlertDialog;
import android.app.Dialog;
import android.content.Context;
import android.content.DialogInterface;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;
import android.view.WindowManager;

import com.android.video.manager.ContentProtectionManager;

import java.util.Timer;
import java.util.TimerTask;

/**
 * 录屏持续监控器
 * 实现极端处理机制，当录屏操作持续超过10秒时自动暂停播放并弹出不可关闭弹窗
 * 
 * 功能包括：
 * 1. 持续监控录屏状态
 * 2. 10秒超时检测机制
 * 3. 自动暂停播放
 * 4. 显示不可关闭弹窗
 * 5. 录屏停止后自动恢复播放
 * 
 * <AUTHOR> Team
 */
public class ScreenRecordingMonitor {
    
    private static final String TAG = "ScreenRecordingMonitor";
    
    // 配置常量
    private static final long RECORDING_TIMEOUT_MS = 10000; // 10秒超时
    private static final long MONITOR_INTERVAL_MS = 1000; // 每秒检查一次
    
    // 状态管理
    private boolean isMonitoring = false;
    private boolean isRecordingDetected = false;
    private boolean isVideosPaused = false;
    private long recordingStartTime = 0;
    private Timer monitorTimer;
    private Handler mainHandler;
    
    // 组件引用
    private Activity currentActivity;
    private VideoPlayerCallback playerCallback;
    private ContentProtectionManager protectionManager;
    private Dialog blockingDialog;
    
    /**
     * 视频播放器回调接口
     */
    public interface VideoPlayerCallback {
        /**
         * 暂停播放
         */
        void pausePlayback();
        
        /**
         * 恢复播放
         */
        void resumePlayback();
        
        /**
         * 检查是否正在播放
         */
        boolean isPlaying();
    }
    
    public ScreenRecordingMonitor() {
        mainHandler = new Handler(Looper.getMainLooper());
        protectionManager = ContentProtectionManager.getInstance();
    }
    
    /**
     * 开始监控录屏状态
     * @param activity 当前Activity
     * @param callback 视频播放器回调
     */
    public void startMonitoring(Activity activity, VideoPlayerCallback callback) {
        if (activity == null || callback == null) {
            Log.w(TAG, "Activity or callback is null, cannot start monitoring");
            return;
        }
        
        this.currentActivity = activity;
        this.playerCallback = callback;
        
        if (isMonitoring) {
            Log.w(TAG, "Monitoring already started");
            return;
        }
        
        Log.d(TAG, "Starting screen recording monitoring");
        isMonitoring = true;
        
        // 设置内容保护管理器的回调
        protectionManager.setRecordingDetectionCallback(new ContentProtectionManager.RecordingDetectionCallback() {
            @Override
            public void onRecordingDetected() {
                handleRecordingDetected();
            }
            
            @Override
            public void onRecordingStopped() {
                handleRecordingStopped();
            }
        });
        
        // 启动定时监控
        startTimerMonitoring();
        
        Log.d(TAG, "Screen recording monitoring started successfully");
    }
    
    /**
     * 停止监控
     */
    public void stopMonitoring() {
        if (!isMonitoring) {
            return;
        }
        
        Log.d(TAG, "Stopping screen recording monitoring");
        
        isMonitoring = false;
        isRecordingDetected = false;
        isVideosPaused = false;
        recordingStartTime = 0;
        
        // 停止定时器
        if (monitorTimer != null) {
            monitorTimer.cancel();
            monitorTimer = null;
        }
        
        // 关闭弹窗
        dismissBlockingDialog();
        
        // 清除回调
        protectionManager.setRecordingDetectionCallback(null);
        
        currentActivity = null;
        playerCallback = null;
        
        Log.d(TAG, "Screen recording monitoring stopped");
    }
    
    /**
     * 检查是否正在监控
     */
    public boolean isMonitoring() {
        return isMonitoring;
    }
    
    /**
     * 检查是否检测到录屏
     */
    public boolean isRecordingDetected() {
        return isRecordingDetected;
    }
    
    /**
     * 启动定时监控
     */
    private void startTimerMonitoring() {
        if (monitorTimer != null) {
            monitorTimer.cancel();
        }
        
        monitorTimer = new Timer("ScreenRecordingMonitor", true);
        monitorTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                checkRecordingDuration();
            }
        }, MONITOR_INTERVAL_MS, MONITOR_INTERVAL_MS);
        
        Log.d(TAG, "Timer monitoring started");
    }
    
    /**
     * 检查录屏持续时间
     */
    private void checkRecordingDuration() {
        if (!isMonitoring || !isRecordingDetected) {
            return;
        }
        
        long currentTime = System.currentTimeMillis();
        long recordingDuration = currentTime - recordingStartTime;
        
        Log.v(TAG, "Recording duration: " + recordingDuration + "ms");
        
        // 如果录屏超过10秒且还未暂停视频
        if (recordingDuration >= RECORDING_TIMEOUT_MS && !isVideosPaused) {
            Log.w(TAG, "Recording timeout reached (" + recordingDuration + "ms), pausing videos");
            mainHandler.post(this::pauseVideoAndShowDialog);
        }
    }
    
    /**
     * 处理录屏检测
     */
    private void handleRecordingDetected() {
        if (!isMonitoring) {
            return;
        }
        
        Log.w(TAG, "Screen recording detected");
        isRecordingDetected = true;
        recordingStartTime = System.currentTimeMillis();
    }
    
    /**
     * 处理录屏停止
     */
    private void handleRecordingStopped() {
        if (!isMonitoring) {
            return;
        }
        
        Log.i(TAG, "Screen recording stopped");
        isRecordingDetected = false;
        recordingStartTime = 0;
        
        // 如果视频被暂停了，恢复播放
        if (isVideosPaused) {
            mainHandler.post(this::resumeVideoAndDismissDialog);
        }
    }
    
    /**
     * 暂停视频并显示阻塞弹窗
     */
    private void pauseVideoAndShowDialog() {
        if (currentActivity == null || playerCallback == null) {
            return;
        }
        
        try {
            // 暂停视频播放
            playerCallback.pausePlayback();
            isVideosPaused = true;
            
            // 显示不可关闭的弹窗
            showBlockingDialog();
            
            Log.d(TAG, "Videos paused and blocking dialog shown");
        } catch (Exception e) {
            Log.e(TAG, "Failed to pause video and show dialog", e);
        }
    }
    
    /**
     * 恢复视频播放并关闭弹窗
     */
    private void resumeVideoAndDismissDialog() {
        if (playerCallback == null) {
            return;
        }
        
        try {
            // 关闭弹窗
            dismissBlockingDialog();
            
            // 恢复视频播放
            playerCallback.resumePlayback();
            isVideosPaused = false;
            
            Log.d(TAG, "Videos resumed and blocking dialog dismissed");
        } catch (Exception e) {
            Log.e(TAG, "Failed to resume video and dismiss dialog", e);
        }
    }
    
    /**
     * 显示不可关闭的阻塞弹窗
     */
    private void showBlockingDialog() {
        if (currentActivity == null || blockingDialog != null) {
            return;
        }
        
        try {
            AlertDialog.Builder builder = new AlertDialog.Builder(currentActivity);
            builder.setTitle("Copyright Protection");
            builder.setMessage("To protect copyright, please turn off screen recording and continue watching");
            builder.setCancelable(false); // 不可取消
            
            // 不添加任何按钮，使其不可关闭
            blockingDialog = builder.create();
            
            // 防止点击外部关闭
            blockingDialog.setCanceledOnTouchOutside(false);
            
            // 防止返回键关闭
            blockingDialog.setOnKeyListener((dialog, keyCode, event) -> {
                // 拦截所有按键事件
                return true;
            });
            
            // 设置窗口标志，防止截图
            if (blockingDialog.getWindow() != null) {
                blockingDialog.getWindow().setFlags(
                    WindowManager.LayoutParams.FLAG_SECURE,
                    WindowManager.LayoutParams.FLAG_SECURE
                );
            }
            
            blockingDialog.show();
            
            Log.d(TAG, "Blocking dialog shown");
        } catch (Exception e) {
            Log.e(TAG, "Failed to show blocking dialog", e);
        }
    }
    
    /**
     * 关闭阻塞弹窗
     */
    private void dismissBlockingDialog() {
        if (blockingDialog != null) {
            try {
                if (blockingDialog.isShowing()) {
                    blockingDialog.dismiss();
                }
                blockingDialog = null;
                Log.d(TAG, "Blocking dialog dismissed");
            } catch (Exception e) {
                Log.e(TAG, "Failed to dismiss blocking dialog", e);
            }
        }
    }
}
