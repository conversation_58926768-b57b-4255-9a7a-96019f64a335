package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;

import java.util.ArrayList;
import java.util.List;

/**
 * 通用推荐位模块RecyclerView适配器
 * 用于显示API返回的各种推荐位数据
 * <AUTHOR> Team
 */
public class FeaturedSectionAdapter extends RecyclerView.Adapter<FeaturedSectionAdapter.FeaturedSectionViewHolder> {
    
    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;
    private String sectionName; // 推荐位名称，用于日志和点击事件
    
    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position, String sectionName);
    }
    
    /**
     * 构造函数
     */
    public FeaturedSectionAdapter(String sectionName) {
        this.sectionName = sectionName != null ? sectionName : "Featured";
        this.videoList = new ArrayList<>();
    }
    
    /**
     * 构造函数
     * @param videoList 视频列表
     * @param sectionName 推荐位名称
     */
    public FeaturedSectionAdapter(List<VideoModel> videoList, String sectionName) {
        this.sectionName = sectionName != null ? sectionName : "Featured";
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }
    
    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }
    
    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        if (newVideoList != null) {
            this.videoList.clear();
            this.videoList.addAll(newVideoList);
            notifyDataSetChanged();
        }
    }
    
    /**
     * 获取视频列表
     * @return 当前视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }
    
    /**
     * 清空视频列表
     */
    public void clearVideoList() {
        this.videoList.clear();
        notifyDataSetChanged();
    }
    
    /**
     * 添加视频到列表
     * @param video 要添加的视频
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            this.videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }
    
    /**
     * 添加视频列表到当前列表
     * @param videos 要添加的视频列表
     */
    public void addVideos(List<VideoModel> videos) {
        if (videos != null && !videos.isEmpty()) {
            int startPosition = this.videoList.size();
            this.videoList.addAll(videos);
            notifyItemRangeInserted(startPosition, videos.size());
        }
    }
    
    @NonNull
    @Override
    public FeaturedSectionViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_todays_hot_video, parent, false);
        return new FeaturedSectionViewHolder(view);
    }
    
    @Override
    public void onBindViewHolder(@NonNull FeaturedSectionViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }
    
    @Override
    public int getItemCount() {
        return videoList.size();
    }
    
    /**
     * ViewHolder类
     */
    public class FeaturedSectionViewHolder extends RecyclerView.ViewHolder {
        
        private ImageView ivVideoPoster;
        private TextView tvVideoTitle;
        private TextView tvRanking;
        
        public FeaturedSectionViewHolder(@NonNull View itemView) {
            super(itemView);
            
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            tvRanking = itemView.findViewById(R.id.tv_ranking);
        }
        
        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }
            
            // 设置海报图片 (加载真实的图片URL)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                Glide.with(itemView.getContext())
                    .load(posterUrl)
                    .placeholder(R.drawable.movie_poster) // 占位图
                    .error(R.drawable.movie_poster) // 错误时显示的图片
                    .centerCrop()
                    .into(ivVideoPoster);
            } else {
                // 如果没有URL，使用默认图片
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }
            
            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());
            
            // 动态推荐位不显示排名序号
            tvRanking.setVisibility(View.GONE);
            
            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position, sectionName);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(),
                            sectionName + "视频点击: " + video.getDisplayTitle(),
                            Toast.LENGTH_SHORT).show();
                }
            });
        }
    }
}
