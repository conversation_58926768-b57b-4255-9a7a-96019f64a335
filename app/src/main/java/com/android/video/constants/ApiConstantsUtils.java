package com.android.video.constants;

/**
 * API常量定义工具类 - 定义所有API路径和响应码常量
 *
 * @deprecated 此类已被废弃，请使用新的模块化常量类替代。
 *
 * <h3>迁移指导</h3>
 * <p>
 * 为了提高代码的可维护性和模块化程度，API常量已按业务模块拆分为独立的常量类。
 * 请根据您使用的API类型，迁移到对应的新常量类：
 * </p>
 *
 * <h4>1. 认证相关API常量</h4>
 * <p>迁移到：{@link com.android.video.constants.AuthApiConstantsUtils}</p>
 * <pre>
 * // 旧用法 (已废弃)
 * String url = ApiConstantsUtils.buildApiUrl(baseUrl, ApiConstantsUtils.API_SEND_SMS_CODE);
 * String param = ApiConstantsUtils.PARAM_PHONE_NUMBER;
 *
 * // 新用法 (推荐)
 * String url = AuthApiConstantsUtils.buildApiUrl(baseUrl, AuthApiConstantsUtils.API_SEND_SMS_CODE);
 * String param = AuthApiConstantsUtils.PARAM_PHONE_NUMBER;
 * </pre>
 *
 * <h4>2. 标签相关API常量</h4>
 * <p>迁移到：{@link com.android.video.constants.LabelApiConstantsUtils}</p>
 * <pre>
 * // 旧用法 (已废弃)
 * String url = ApiConstantsUtils.buildApiUrl(baseUrl, ApiConstantsUtils.API_GET_LABELS);
 * String field = ApiConstantsUtils.FIELD_LABEL_ID;
 *
 * // 新用法 (推荐)
 * String url = LabelApiConstantsUtils.buildApiUrl(baseUrl, LabelApiConstantsUtils.API_GET_LABELS);
 * String field = LabelApiConstantsUtils.FIELD_LABEL_ID;
 * </pre>
 *
 * <h4>3. 首页相关API常量</h4>
 * <p>迁移到：{@link com.android.video.constants.HomeApiConstantsUtils}</p>
 * <pre>
 * // 旧用法 (已废弃)
 * String url = ApiConstantsUtils.buildApiUrl(baseUrl, ApiConstantsUtils.API_GET_BANNERS);
 * String param = ApiConstantsUtils.PARAM_LOCATION;
 *
 * // 新用法 (推荐)
 * String url = HomeApiConstantsUtils.buildApiUrl(baseUrl, HomeApiConstantsUtils.API_GET_BANNERS);
 * String param = HomeApiConstantsUtils.PARAM_LOCATION;
 * </pre>
 *
 * <h4>4. 通用响应码和工具方法</h4>
 * <p>迁移到：{@link com.android.video.constants.BaseApiConstantsUtils}</p>
 * <pre>
 * // 旧用法 (已废弃)
 * boolean success = ApiConstantsUtils.isSuccessCode(code);
 * String responseCode = ApiConstantsUtils.RESPONSE_CODE_SUCCESS;
 *
 * // 新用法 (推荐)
 * boolean success = BaseApiConstantsUtils.isSuccessCode(code);
 * String responseCode = BaseApiConstantsUtils.RESPONSE_CODE_SUCCESS;
 * </pre>
 *
 * <h4>迁移优势</h4>
 * <ul>
 *   <li><strong>模块化</strong>：每个模块只关注自己的API常量，职责清晰</li>
 *   <li><strong>可维护性</strong>：新增API时只需修改对应模块的常量类</li>
 *   <li><strong>可扩展性</strong>：新增业务模块时可轻松添加新的常量类</li>
 *   <li><strong>代码复用</strong>：通过继承机制复用通用常量和工具方法</li>
 * </ul>
 *
 * <h4>向后兼容性</h4>
 * <p>
 * 此类将继续保持功能完整性，确保现有代码正常运行。
 * 但建议尽快迁移到新的模块化常量类，以获得更好的开发体验。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 * @see com.android.video.constants.BaseApiConstantsUtils
 * @see com.android.video.constants.AuthApiConstantsUtils
 * @see com.android.video.constants.LabelApiConstantsUtils
 * @see com.android.video.constants.HomeApiConstantsUtils
 */
@Deprecated
public class ApiConstantsUtils {

    // ========== API路径常量 ==========

    // 认证相关API
    // @deprecated 认证相关API常量已迁移到 AuthApiConstantsUtils 类
    // 请使用 AuthApiConstantsUtils.API_SEND_SMS_CODE, AuthApiConstantsUtils.API_PHONE_LOGIN 等
    /**
     * 发送手机验证码
     */
    public static final String API_SEND_SMS_CODE = "/app/customer/sendSmsCode";

    /**
     * 手机号登录
     */
    public static final String API_PHONE_LOGIN = "/app/customer/login/phone";

    /**
     * Facebook登录回调
     */
    public static final String API_FACEBOOK_LOGIN = "/app/customer/login/facebook/callback";

    /**
     * TikTok登录回调
     */
    public static final String API_TIKTOK_LOGIN = "/app/customer/login/tiktok/callback";

    // 标签相关API
    // @deprecated 标签相关API常量已迁移到 LabelApiConstantsUtils 类
    // 请使用 LabelApiConstantsUtils.A PI_GET_LABELS, LabelApiConstantsUtils.API_SUBMIT_LABELS 等
    /**
     * 获取标签信息
     */
    public static final String API_GET_LABELS = "/app/customer/getLabels";

    /**
     * 提交用户标签
     */
    public static final String API_SUBMIT_LABELS = "/app/customer/submitLabels";

    // 首页相关API
    // @deprecated 首页相关API常量已迁移到 HomeApiConstantsUtils 类
    // 请使用 HomeApiConstantsUtils.API_GET_BANNERS, HomeApiConstantsUtils.API_GET_CATEGORIES 等
    /**
     * 获取banner列表
     */
    public static final String API_GET_BANNERS = "/app/index/banners";

    /**
     * 获取分类及短剧列表
     */
    public static final String API_GET_CATEGORIES = "/app/index/categories";

    /**
     * 获取首页推荐位列表
     */
    public static final String API_GET_FEATURED = "/app/index/featured";

    // ========== 响应码常量 ==========
    // @deprecated 响应码常量已迁移到 BaseApiConstantsUtils 类
    // 请使用 BaseApiConstantsUtils.RESPONSE_CODE_SUCCESS, BaseApiConstantsUtils.isSuccessCode() 等

    /**
     * 成功响应码
     */
    public static final String RESPONSE_CODE_SUCCESS = "200";

    /**
     * 失败响应码
     */
    public static final String RESPONSE_CODE_ERROR = "404";

    /**
     * 参数错误
     */
    public static final String RESPONSE_CODE_PARAM_ERROR = "400";

    /**
     * 未授权
     */
    public static final String RESPONSE_CODE_UNAUTHORIZED = "401";

    /**
     * 服务器内部错误
     */
    public static final String RESPONSE_CODE_SERVER_ERROR = "500";

    // ========== 请求参数常量 ==========
    // @deprecated 请求参数常量已按模块迁移到对应的常量类
    // 认证参数请使用 AuthApiConstantsUtils.PARAM_PHONE_NUMBER 等
    // 首页参数请使用 HomeApiConstantsUtils.PARAM_LOCATION 等
    // 通用参数请使用 BaseApiConstantsUtils.PARAM_DEVICE_ID 等

    /**
     * 手机号参数名
     */
    public static final String PARAM_PHONE_NUMBER = "phoneNumber";

    /**
     * 验证码参数名
     */
    public static final String PARAM_VERIFICATION_CODE = "verificationCode";

    /**
     * 设备ID参数名
     */
    public static final String PARAM_DEVICE_ID = "deviceId";

    /**
     * 授权码参数名
     */
    public static final String PARAM_CODE = "code";

    /**
     * 分类ID参数名
     */
    public static final String PARAM_CATEGORY_ID = "categoryId";

    /**
     * 语言类型参数名
     */
    public static final String PARAM_LANGUAGE_TYPE = "languageType";

    /**
     * 页码参数名
     */
    public static final String PARAM_PAGE_NUM = "pageNum";

    /**
     * 每页数量参数名
     */
    public static final String PARAM_PAGE_SIZE = "pageSize";

    /**
     * 位置参数名
     */
    public static final String PARAM_LOCATION = "location";

    // ========== 响应字段常量 ==========
    // @deprecated 响应字段常量已按模块迁移到对应的常量类
    // 通用字段请使用 BaseApiConstantsUtils.FIELD_CODE, BaseApiConstantsUtils.FIELD_MESSAGE 等
    // 标签字段请使用 LabelApiConstantsUtils.FIELD_LABEL_ID, LabelApiConstantsUtils.FIELD_LABEL_NAME 等

    /**
     * 响应码字段名
     */
    public static final String FIELD_CODE = "code";

    /**
     * 响应消息字段名
     */
    public static final String FIELD_MESSAGE = "message";

    /**
     * 响应数据字段名
     */
    public static final String FIELD_DATA = "data";

    /**
     * 标签ID字段名
     */
    public static final String FIELD_LABEL_ID = "labelId";

    /**
     * 标签名称字段名
     */
    public static final String FIELD_LABEL_NAME = "labelName";

    // ========== 工具方法 ==========
    // @deprecated 工具方法已迁移到 BaseApiConstantsUtils 类
    // 请使用 BaseApiConstantsUtils.buildApiUrl(), BaseApiConstantsUtils.isSuccessCode() 等
    // 所有模块化常量类都继承了这些工具方法，可以直接调用

    /**
     * 构建完整的API URL
     * @param baseUrl 基础URL
     * @param apiPath API路径
     * @return 完整的API URL
     */
    public static String buildApiUrl(String baseUrl, String apiPath) {
        if (baseUrl == null || apiPath == null) {
            return "";
        }
        
        // 确保baseUrl不以/结尾，apiPath以/开头
        String cleanBaseUrl = baseUrl.endsWith("/") ? baseUrl.substring(0, baseUrl.length() - 1) : baseUrl;
        String cleanApiPath = apiPath.startsWith("/") ? apiPath : "/" + apiPath;
        
        return cleanBaseUrl + cleanApiPath;
    }

    /**
     * 检查响应码是否为成功
     * @param code 响应码
     * @return 是否成功
     */
    public static boolean isSuccessCode(String code) {
        return RESPONSE_CODE_SUCCESS.equals(code);
    }

    /**
     * 检查响应码是否为错误
     * @param code 响应码
     * @return 是否错误
     */
    public static boolean isErrorCode(String code) {
        return !isSuccessCode(code);
    }
}
