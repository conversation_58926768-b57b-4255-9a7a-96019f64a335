package com.android.video.model;

/**
 * VIP记录数据模型
 */
public class VipRecord {
    private String purchaseType;    // 购买类型
    private String dateTime;        // 日期时间
    private String price;           // 价格
    private String paymentMethod;   // 支付方式
    private int paymentIconRes;     // 支付方式图标资源ID

    public VipRecord() {
    }

    public VipRecord(String purchaseType, String dateTime, String price, String paymentMethod, int paymentIconRes) {
        this.purchaseType = purchaseType;
        this.dateTime = dateTime;
        this.price = price;
        this.paymentMethod = paymentMethod;
        this.paymentIconRes = paymentIconRes;
    }

    // Getters and Setters
    public String getPurchaseType() {
        return purchaseType;
    }

    public void setPurchaseType(String purchaseType) {
        this.purchaseType = purchaseType;
    }

    public String getDateTime() {
        return dateTime;
    }

    public void setDateTime(String dateTime) {
        this.dateTime = dateTime;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public int getPaymentIconRes() {
        return paymentIconRes;
    }

    public void setPaymentIconRes(int paymentIconRes) {
        this.paymentIconRes = paymentIconRes;
    }
}
