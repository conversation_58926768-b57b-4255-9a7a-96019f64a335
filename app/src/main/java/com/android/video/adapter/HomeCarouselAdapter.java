package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.FrameLayout;
import android.widget.ImageView;
import android.widget.Toast;
import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;
import com.android.video.R;
import com.android.video.model.VideoModel;
import com.bumptech.glide.Glide;
import com.android.video.ui.component.ImageLoadingAnimationManager;
import com.android.video.utils.OptimizedImageLoader;
import java.util.ArrayList;
import java.util.List;

/**
 * 首页轮播图RecyclerView适配器
 * <AUTHOR>
 */
public class HomeCarouselAdapter extends RecyclerView.Adapter<HomeCarouselAdapter.CarouselViewHolder> {

    private List<VideoModel> carouselList;
    private OnCarouselClickListener onCarouselClickListener;
    private int selectedPosition = 0; // 默认选中第一个

    /**
     * 轮播图点击监听接口
     */
    public interface OnCarouselClickListener {
        void onCarouselClick(VideoModel video, int position);
    }

    /**
     * 构造函数
     */
    public HomeCarouselAdapter() {
        this.carouselList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param carouselList 轮播图列表
     */
    public HomeCarouselAdapter(List<VideoModel> carouselList) {
        this.carouselList = carouselList != null ? carouselList : new ArrayList<>();
    }

    /**
     * 设置轮播图点击监听器
     * @param listener 点击监听器
     */
    public void setOnCarouselClickListener(OnCarouselClickListener listener) {
        this.onCarouselClickListener = listener;
    }

    /**
     * 更新轮播图列表
     * @param newCarouselList 新的轮播图列表
     */
    public void updateCarouselList(List<VideoModel> newCarouselList) {
        this.carouselList = newCarouselList != null ? newCarouselList : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 更新选中位置
     * @param position 新的选中位置
     */
    public void updateSelectedPosition(int position) {
        if (position >= 0 && position < carouselList.size()) {
            int oldPosition = selectedPosition;
            selectedPosition = position;
            notifyItemChanged(oldPosition);
            notifyItemChanged(selectedPosition);
        }
    }

    /**
     * 获取当前选中的视频
     * @return 选中的视频
     */
    public VideoModel getSelectedVideo() {
        if (selectedPosition >= 0 && selectedPosition < carouselList.size()) {
            return carouselList.get(selectedPosition);
        }
        return null;
    }

    /**
     * 获取轮播图列表
     * @return 轮播图列表
     */
    public List<VideoModel> getCarouselList() {
        return new ArrayList<>(carouselList);
    }

    @NonNull
    @Override
    public CarouselViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_home_carousel, parent, false);
        return new CarouselViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull CarouselViewHolder holder, int position) {
        VideoModel video = carouselList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return carouselList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class CarouselViewHolder extends RecyclerView.ViewHolder {

        private ImageView ivCarouselPoster;
        private FrameLayout flCarouselBorder;

        public CarouselViewHolder(@NonNull View itemView) {
            super(itemView);
            ivCarouselPoster = itemView.findViewById(R.id.iv_carousel_poster);
            flCarouselBorder = itemView.findViewById(R.id.fl_carousel_border);
        }

        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }

            // 设置海报图片 (使用优化的图片加载器)
            String posterUrl = video.getPosterUrl();
            if (posterUrl != null && !posterUrl.isEmpty()) {
                OptimizedImageLoader.loadImageSmart(
                    itemView.getContext(),
                    posterUrl,
                    ivCarouselPoster,
                    R.drawable.movie_poster
                );
            } else {
                // 如果没有URL，使用默认图片
                ivCarouselPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置选中状态 - 现在设置FrameLayout的选中状态
            boolean isSelected = position == selectedPosition;
            flCarouselBorder.setSelected(isSelected);

            // 根据选中状态调整尺寸，确保底边对齐
            ViewGroup.LayoutParams layoutParams = flCarouselBorder.getLayoutParams();
            if (layoutParams instanceof androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) {
                androidx.constraintlayout.widget.ConstraintLayout.LayoutParams constraintParams =
                    (androidx.constraintlayout.widget.ConstraintLayout.LayoutParams) layoutParams;

                if (isSelected) {
                    constraintParams.width = itemView.getContext().getResources()
                            .getDimensionPixelSize(R.dimen.home_carousel_selected_width);
                    constraintParams.height = itemView.getContext().getResources()
                            .getDimensionPixelSize(R.dimen.home_carousel_selected_height);
                    // 设置底边对齐
                    constraintParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
                    constraintParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET;
                } else {
                    constraintParams.width = itemView.getContext().getResources()
                            .getDimensionPixelSize(R.dimen.home_carousel_unselected_width);
                    constraintParams.height = itemView.getContext().getResources()
                            .getDimensionPixelSize(R.dimen.home_carousel_unselected_height);
                    // 设置底边对齐
                    constraintParams.bottomToBottom = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.PARENT_ID;
                    constraintParams.topToTop = androidx.constraintlayout.widget.ConstraintLayout.LayoutParams.UNSET;
                }
                flCarouselBorder.setLayoutParams(constraintParams);
            }

            // 设置点击事件
            flCarouselBorder.setOnClickListener(v -> {
                if (onCarouselClickListener != null) {
                    // 更新选中状态
                    updateSelectedPosition(position);

                    // 回调点击事件
                    onCarouselClickListener.onCarouselClick(video, position);
                } else {
                    // 默认Toast提示
                    Toast.makeText(itemView.getContext(),
                            "轮播图点击: " + video.getDisplayTitle(),
                            Toast.LENGTH_SHORT).show();
                }
            });
        }
    }
}
