package com.android.video.constants;

/**
 * 个人中心模块API常量类 - 定义个人中心相关的API路径和参数常量
 * <p>
 * 此类继承自 {@link BaseApiConstantsUtils}，专门管理个人中心相关的API常量，
 * 包括用户信息获取、用户资料管理、积分管理、VIP状态等功能的API路径和参数定义。
 * </p>
 * 
 * <p>
 * 包含的个人中心功能：
 * <ul>
 *   <li>用户基本信息管理</li>
 *   <li>用户积分查询和管理</li>
 *   <li>VIP状态和到期时间管理</li>
 *   <li>用户设置和偏好管理</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 个人中心模块特点：
 * <ul>
 *   <li>需要用户登录认证</li>
 *   <li>支持多种注册方式的用户</li>
 *   <li>支持VIP用户特殊功能</li>
 *   <li>支持多语言设置</li>
 * </ul>
 * </p>
 * 
 * <p>
 * 使用示例：
 * <pre>
 * // 构建获取用户信息API URL
 * String url = ProfileApiConstantsUtils.buildApiUrl(baseUrl, ProfileApiConstantsUtils.API_GET_USER_INFO);
 * 
 * // 使用请求头
 * Map&lt;String, String&gt; headers = ApiHeaderUtils.getDefaultHeaders();
 * 
 * // 解析响应数据
 * String customerId = response.getString(ProfileApiConstantsUtils.FIELD_CUSTOMER_ID);
 * Integer isVip = response.optInt(ProfileApiConstantsUtils.FIELD_IS_VIP);
 * </pre>
 * </p>
 * 
 * <AUTHOR> Team
 * @since 1.0
 * @see BaseApiConstantsUtils
 */
public class ProfileApiConstantsUtils extends BaseApiConstantsUtils {

    // ========== 个人中心相关API路径常量 ==========

    /**
     * 获取用户信息API路径
     * <p>
     * 用于获取当前登录用户的详细信息，包括用户基本信息、VIP状态、积分等。
     * 请求方法：GET
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "customerId": "fe045921c7a8c7b18aabdc76cd2edae8",
     *     "customerName": "17777777777",
    
     *     "points": 0,
     *     "isVip": null,
     *     "vipDays": null,
     *     "vipExpireDate": null
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_USER_INFO = "/app/customer/info";

    /**
     * 切换语言API路径
     * <p>
     * 用于切换用户的语言设置。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>languageType: 语言类型 (1=英语, 2=俄语, 3=Kaza语)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success"
     * }
     * </pre>
     * </p>
     */
    public static final String API_CHANGE_LANGUAGE = "/app/customer/changeLanguage";

    /**
     * 获取协议相关信息API路径
     * <p>
     * 用于获取设置页面的协议相关信息，如关于我们、隐私政策、用户协议等。
     * 请求方法：GET
     * 认证要求：无需认证
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>InformationId: 信息ID (必填)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": {
     *     "informationId": "02a51817828d4c7e9d764a9fa5c267d8",
     *     "informationName": "About Us",
     *     "informationDetails": "hhhhhhhh",
     *     "createTime": "2025-07-15 23:02:23",
     *     "updateTime": "2025-07-16 00:10:08"
     *   }
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_INFORMATION = "/app/myMenter/getInformation";

    /**
     * 更新个人信息API路径
     * <p>
     * 用于更新用户的个人信息，包括昵称、手机号、头像等。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * 内容类型：application/json
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>customerName: 用户昵称 (String, 必填)</li>
     *   <li>phoneNumber: 手机号 (String, 必填)</li>
     *   <li>headImg: 头像URL (String, 可选)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success"
     * }
     * </pre>
     * </p>
     */
    public static final String API_UPDATE_MY_INFORMATION = "/app/myMenter/updateMyInformation";

    /**
     * 用户注销API路径
     * <p>
     * 用于注销用户账户，删除用户的所有数据和权限。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * 内容类型：application/json
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>无需额外参数，通过X-Access-Token识别用户</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "unsubscribe successfully"
     * }
     * </pre>
     * </p>
     */
    public static final String API_UNSUBSCRIBE = "/app/myMenter/unsubscribe";

    /**
     * 获取反馈类型API路径
     * <p>
     * 用于获取反馈页面的反馈类型列表。
     * 请求方法：GET
     * 认证要求：无需认证
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>无需参数</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": [
     *     {
     *       "feedbackTypeId": "f87b264c4007437285889035915560d5",
     *       "feedbackTypeName": "Product recommendations",
     *       "isDelete": 1,
     *       "createTime": "2025-07-20 18:20:15",
     *       "updateTime": "2025-07-20 18:20:17"
     *     }
     *   ]
     * }
     * </pre>
     * </p>
     */
    public static final String API_GET_FEEDBACK_TYPE = "/app/myMenter/getFeedbackType";

    /**
     * 提交反馈API路径
     * <p>
     * 用于提交用户反馈信息。
     * 请求方法：POST
     * 认证要求：需要X-Access-Token请求头
     * 内容类型：application/json
     * </p>
     *
     * <p>
     * 请求参数：
     * <ul>
     *   <li>feedbackTypeId: 反馈类型ID (String, 必填)</li>
     *   <li>feedbackDescription: 反馈内容详情 (String, 必填)</li>
     *   <li>feedbackPictures: 图片URL，多张图片用逗号隔开 (String, 可选)</li>
     *   <li>contactInformation: 联系方式 (String, 可选)</li>
     * </ul>
     * </p>
     *
     * <p>
     * 响应示例：
     * <pre>
     * {
     *   "code": "200",
     *   "message": "success",
     *   "data": "submit successfully"
     * }
     * </pre>
     * </p>
     */
    public static final String API_SUBMIT_FEEDBACK = "/app/myMenter/submitFeedback";

    // ========== 个人中心响应字段常量 ==========

    /**
     * 用户ID字段名
     * <p>
     * 用于标识唯一用户的字段名。
     * 字段类型：String
     * 示例值：fe045921c7a8c7b18aabdc76cd2edae8
     * </p>
     */
    public static final String FIELD_CUSTOMER_ID = "customerId";

    /**
     * 用户昵称字段名
     * <p>
     * 用于显示用户昵称的字段名。
     * 字段类型：String
     * 示例值：17777777777
     * </p>
     */
    public static final String FIELD_CUSTOMER_NAME = "customerName";



    /**
     * 注册时间字段名
     * <p>
     * 用户注册时间。
     * 字段类型：String
     * 格式：yyyy-MM-dd HH:mm:ss
     * 示例值：2025-07-04 16:24:26
     * </p>
     */
    public static final String FIELD_REGISTRATION_TIME = "registrationTime";

    /**
     * 注册方式字段名
     * <p>
     * 用户注册方式。
     * 字段类型：int
     * 取值范围：1=Phone Number, 2=FaceBook, 3=Tiktok
     * </p>
     */
    public static final String FIELD_REGISTRATION_TYPE = "registrationType";

    /**
     * 最后登录时间字段名
     * <p>
     * 用户最后登录时间。
     * 字段类型：String
     * 格式：yyyy-MM-dd HH:mm:ss
     * 示例值：2025-07-06 11:27:59
     * </p>
     */
    public static final String FIELD_LAST_LOGIN_TIME = "lastLoginTime";

    /**
     * 手机号字段名
     * <p>
     * 用户手机号码。
     * 字段类型：String
     * 示例值：17777777777
     * </p>
     */
    public static final String FIELD_PHONE_NUMBER = "phoneNumber";

    /**
     * 头像字段名
     * <p>
     * 用户头像URL。
     * 字段类型：String
     * 可为null
     * </p>
     */
    public static final String FIELD_HEAD_IMG = "headImg";

    /**
     * 删除状态字段名
     * <p>
     * 用户删除状态。
     * 字段类型：int
     * </p>
     */
    public static final String FIELD_IS_DELETE = "isDelete";

    /**
     * 用户状态字段名
     * <p>
     * 用户账户状态。
     * 字段类型：int
     * </p>
     */
    public static final String FIELD_STATUS = "status";

    /**
     * 创建时间字段名
     * <p>
     * 用户创建时间。
     * 字段类型：String
     * 格式：yyyy-MM-dd HH:mm:ss
     * </p>
     */
    public static final String FIELD_CREATE_TIME = "createTime";

    /**
     * 更新时间字段名
     * <p>
     * 用户信息更新时间。
     * 字段类型：String
     * 格式：yyyy-MM-dd HH:mm:ss
     * </p>
     */
    public static final String FIELD_UPDATE_TIME = "updateTime";

    /**
     * Facebook ID字段名
     * <p>
     * Facebook第三方登录ID。
     * 字段类型：String
     * 可为null
     * </p>
     */
    public static final String FIELD_FACEBOOK_ID = "facebookId";

    /**
     * TikTok ID字段名
     * <p>
     * TikTok第三方登录ID。
     * 字段类型：String
     * 可为null
     * </p>
     */
    public static final String FIELD_TIKTOK_ID = "tiktokId";

    /**
     * 设备ID字段名
     * <p>
     * 用户设备ID。
     * 字段类型：String
     * 示例值：12diwu3rri9ewjrwerqi
     * </p>
     */
    public static final String FIELD_DEVICE_ID = "deviceId";

    /**
     * 用户Token字段名
     * <p>
     * 用户访问令牌。
     * 字段类型：String
     * 示例值：05358c87b76645feaae46740fac753c9
     * </p>
     */
    public static final String FIELD_USER_TOKEN = "userToken";

    /**
     * 当前积分数字段名
     * <p>
     * 用户当前积分数量。
     * 字段类型：int
     * 示例值：0
     * </p>
     */
    public static final String FIELD_POINTS = "points";

    /**
     * VIP状态字段名
     * <p>
     * 用户是否为VIP。
     * 字段类型：Integer
     * 取值范围：0=否, 1=是, null=未设置
     * </p>
     */
    public static final String FIELD_IS_VIP = "isVip";

    /**
     * VIP剩余天数字段名
     * <p>
     * VIP剩余天数。
     * 字段类型：Integer
     * 可为null
     * </p>
     */
    public static final String FIELD_VIP_DAYS = "vipDays";

    /**
     * VIP到期时间字段名
     * <p>
     * VIP到期时间。
     * 字段类型：String
     * 格式：yyyy-MM-dd HH:mm:ss
     * 可为null
     * </p>
     */
    public static final String FIELD_VIP_EXPIRE_DATE = "vipExpireDate";

    /**
     * 语言类型字段名
     * <p>
     * 用户语言偏好设置。
     * 字段类型：Integer
     * 取值范围：1=英语, 2=俄语, null=未设置
     * </p>
     */
    public static final String FIELD_LANGUAGE_TYPE = "languageType";

    // ========== 协议信息相关字段常量 ==========

    /**
     * 信息ID字段名
     * <p>
     * 协议信息的唯一标识符。
     * 字段类型：String
     * 示例值：02a51817828d4c7e9d764a9fa5c267d8
     * </p>
     */
    public static final String FIELD_INFORMATION_ID = "informationId";

    /**
     * 信息名称字段名
     * <p>
     * 协议信息的名称。
     * 字段类型：String
     * 示例值：About Us
     * </p>
     */
    public static final String FIELD_INFORMATION_NAME = "informationName";

    /**
     * 信息详情字段名
     * <p>
     * 协议信息的详细内容。
     * 字段类型：String
     * 示例值：hhhhhhhh
     * </p>
     */
    public static final String FIELD_INFORMATION_DETAILS = "informationDetails";

    // ========== 反馈类型相关字段常量 ==========

    /**
     * 反馈类型ID字段名
     * <p>
     * 反馈类型的唯一标识符。
     * 字段类型：String
     * 示例值：f87b264c4007437285889035915560d5
     * </p>
     */
    public static final String FIELD_FEEDBACK_TYPE_ID = "feedbackTypeId";

    /**
     * 反馈类型名称字段名
     * <p>
     * 反馈类型的名称。
     * 字段类型：String
     * 示例值：Product recommendations
     * </p>
     */
    public static final String FIELD_FEEDBACK_TYPE_NAME = "feedbackTypeName";



    // ========== 注册方式常量 ==========

    /**
     * 手机号注册
     */
    public static final int REGISTRATION_TYPE_PHONE = 1;

    /**
     * Facebook注册
     */
    public static final int REGISTRATION_TYPE_FACEBOOK = 2;

    /**
     * TikTok注册
     */
    public static final int REGISTRATION_TYPE_TIKTOK = 3;

    // ========== VIP状态常量 ==========

    /**
     * 非VIP用户
     */
    public static final int VIP_STATUS_NO = 0;

    /**
     * VIP用户
     */
    public static final int VIP_STATUS_YES = 1;

    // ========== 语言类型常量 ==========

    /**
     * 英语
     */
    public static final int LANGUAGE_TYPE_ENGLISH = 1;

    /**
     * 俄语
     */
    public static final int LANGUAGE_TYPE_RUSSIAN = 2;

    /**
     * Kaza语
     */
    public static final int LANGUAGE_TYPE_KAZA = 3;

    // ========== 请求参数常量 ==========

    /**
     * 语言类型参数名
     * <p>
     * 用于语言切换API的参数名。
     * 参数类型：Integer
     * 取值范围：1=英语, 2=俄语, 3=Kaza语
     * </p>
     */
    public static final String PARAM_LANGUAGE_TYPE = "languageType";

    /**
     * 信息ID参数名
     * <p>
     * 用于获取协议信息API的参数名。
     * 参数类型：String
     * 示例值：02a51817828d4c7e9d764a9fa5c267d8
     * </p>
     */
    public static final String PARAM_INFORMATION_ID = "InformationId";

    /**
     * 用户昵称参数名
     * <p>
     * 用于更新个人信息API的用户昵称参数名。
     * 参数类型：String
     * 示例值：caoh
     * </p>
     */
    public static final String PARAM_CUSTOMER_NAME = "customerName";

    /**
     * 手机号参数名
     * <p>
     * 用于更新个人信息API的手机号参数名。
     * 参数类型：String
     * 示例值：19999999999
     * </p>
     */
    public static final String PARAM_PHONE_NUMBER = "phoneNumber";

    /**
     * 头像URL参数名
     * <p>
     * 用于更新个人信息API的头像URL参数名。
     * 参数类型：String
     * 示例值：https://head.png
     * </p>
     */
    public static final String PARAM_HEAD_IMG = "headImg";

    // ========== 反馈提交相关参数常量 ==========

    /**
     * 反馈类型ID参数名
     * <p>
     * 用于反馈提交API的反馈类型ID参数名。
     * 参数类型：String
     * 示例值：f87b264c4007437285889035915560d5
     * </p>
     */
    public static final String PARAM_FEEDBACK_TYPE_ID = "feedbackTypeId";

    /**
     * 反馈描述参数名
     * <p>
     * 用于反馈提交API的反馈内容详情参数名。
     * 参数类型：String
     * 示例值：这是反馈内容详情
     * </p>
     */
    public static final String PARAM_FEEDBACK_DESCRIPTION = "feedbackDescription";

    /**
     * 反馈图片参数名
     * <p>
     * 用于反馈提交API的图片URL参数名，多张图片用逗号隔开。
     * 参数类型：String
     * 示例值：https://1111.png,https://222.png,https://333.png,
     * </p>
     */
    public static final String PARAM_FEEDBACK_PICTURES = "feedbackPictures";

    /**
     * 联系方式参数名
     * <p>
     * 用于反馈提交API的联系方式参数名。
     * 参数类型：String
     * 示例值：17777778889
     * </p>
     */
    public static final String PARAM_CONTACT_INFORMATION = "contactInformation";

    // ========== 个人中心模块工具方法 ==========

    /**
     * 检查是否为个人中心相关的API路径
     * <p>
     * 判断给定的API路径是否属于个人中心模块。
     * </p>
     *
     * @param apiPath API路径
     * @return 如果是个人中心相关API则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean isProfile = isProfileApi("/app/customer/info");     // true
     * boolean isProfile = isProfileApi("/app/index/banners");     // false
     * </pre>
     */
    public static boolean isProfileApi(String apiPath) {
        if (apiPath == null) {
            return false;
        }

        return apiPath.equals(API_GET_USER_INFO) ||
               apiPath.equals(API_CHANGE_LANGUAGE) ||
               apiPath.equals(API_GET_INFORMATION) ||
               apiPath.equals(API_UPDATE_MY_INFORMATION) ||
               apiPath.equals(API_UNSUBSCRIBE) ||
               apiPath.equals(API_GET_FEEDBACK_TYPE) ||
               apiPath.equals(API_SUBMIT_FEEDBACK);
    }

    /**
     * 检查用户是否为VIP
     * <p>
     * 根据VIP状态判断用户是否为VIP。
     * </p>
     *
     * @param isVip VIP状态
     * @return 如果用户是VIP则返回true，否则返回false
     *
     * @example
     * <pre>
     * boolean vip = isVipUser(1);     // true (VIP状态为是)
     * boolean vip = isVipUser(0);     // false (非VIP)
     * boolean vip = isVipUser(null);  // false (未设置)
     * </pre>
     */
    public static boolean isVipUser(Integer isVip) {
        return isVip != null && isVip == VIP_STATUS_YES;
    }

    /**
     * 获取注册方式描述
     * <p>
     * 根据注册方式代码返回对应的中文描述。
     * </p>
     *
     * @param registrationType 注册方式代码
     * @return 注册方式的中文描述
     *
     * @example
     * <pre>
     * String desc = getRegistrationTypeDescription(1);  // "手机号注册"
     * String desc = getRegistrationTypeDescription(2);  // "Facebook注册"
     * </pre>
     */
    public static String getRegistrationTypeDescription(int registrationType) {
        switch (registrationType) {
            case REGISTRATION_TYPE_PHONE:
                return "手机号注册";
            case REGISTRATION_TYPE_FACEBOOK:
                return "Facebook注册";
            case REGISTRATION_TYPE_TIKTOK:
                return "TikTok注册";
            default:
                return "未知注册方式";
        }
    }

    /**
     * 获取语言类型描述
     * <p>
     * 根据语言类型代码返回对应的中文描述。
     * </p>
     *
     * @param languageType 语言类型代码
     * @return 语言类型的中文描述
     *
     * @example
     * <pre>
     * String desc = getLanguageTypeDescription(1);  // "英语"
     * String desc = getLanguageTypeDescription(2);  // "俄语"
     * </pre>
     */
    public static String getLanguageTypeDescription(Integer languageType) {
        if (languageType == null) {
            return "未设置";
        }

        switch (languageType) {
            case LANGUAGE_TYPE_ENGLISH:
                return "英语";
            case LANGUAGE_TYPE_RUSSIAN:
                return "俄语";
            case LANGUAGE_TYPE_KAZA:
                return "Kaza语";
            default:
                return "未知语言";
        }
    }


}
