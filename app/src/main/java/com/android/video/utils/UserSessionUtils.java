package com.android.video.utils;

import android.content.Context;
import android.content.SharedPreferences;
import com.android.video.model.UserModel;
import com.android.video.model.UserInfo;
import com.android.video.model.response.LoginResponseModel;
import com.android.video.model.response.InitDeviceResponseModel;
import com.android.video.network.AuthApiUtils;
import java.util.Set;
import java.util.HashSet;

/**
 * 用户会话管理工具类 - 管理用户登录状态和信息存储
 * <AUTHOR>
 */
public class UserSessionUtils {
    
    private static final String PREF_NAME = "VideoPlayerUserSession";
    private static final String KEY_IS_LOGGED_IN = "isLoggedIn";
    private static final String KEY_USERNAME = "username";
    private static final String KEY_UID = "uid";
    private static final String KEY_PHONE_NUMBER = "phoneNumber";
    private static final String KEY_USER_VIP = "userVip";
    private static final String KEY_EMAIL = "email";
    private static final String KEY_AVATAR = "avatar";
    private static final String KEY_POINTS = "points";
    private static final String KEY_FAVORITE_TAGS = "favoriteTags";
    private static final String KEY_LANGUAGE_TYPE = "languageType";

    // VIP相关字段
    private static final String KEY_VIP_DAYS = "vipDays";
    private static final String KEY_VIP_EXPIRE_DATE = "vipExpireDate";

    // 消息相关字段
    private static final String KEY_MESSAGE_READ_STATUS = "messageReadStatus";
    
    /**
     * 保存用户登录信息
     * @param context 上下文
     * @param user 用户模型
     */
    public static void saveUserSession(Context context, UserModel user) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        if (user != null) {
            editor.putBoolean(KEY_IS_LOGGED_IN, user.isLoggedIn());
            editor.putString(KEY_USERNAME, user.getUsername());
            editor.putString(KEY_UID, user.getUid());
            editor.putString(KEY_PHONE_NUMBER, user.getPhoneNumber());
            editor.putInt(KEY_USER_VIP, user.getUserVip());
            editor.putString(KEY_EMAIL, user.getEmail());
            editor.putString(KEY_AVATAR, user.getAvatar());
            editor.putInt(KEY_POINTS, user.getPoints());

            // 保存VIP相关信息
            if (user.getVipDays() != null) {
                editor.putInt(KEY_VIP_DAYS, user.getVipDays());
            } else {
                editor.remove(KEY_VIP_DAYS);
            }

            if (user.getVipExpireDate() != null) {
                editor.putString(KEY_VIP_EXPIRE_DATE, user.getVipExpireDate());
            } else {
                editor.remove(KEY_VIP_EXPIRE_DATE);
            }
        } else {
            // 清除所有用户信息
            clearUserSession(context);
            return;
        }

        editor.apply();
    }
    
    /**
     * 获取当前用户信息
     * @param context 上下文
     * @return 用户模型，如果未登录则返回访客用户
     */
    public static UserModel getCurrentUser(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);

        boolean isLoggedIn = prefs.getBoolean(KEY_IS_LOGGED_IN, false);

        if (!isLoggedIn) {
            // 返回访客用户
            return new UserModel();
        }

        String username = prefs.getString(KEY_USERNAME, "Guest");
        String uid = prefs.getString(KEY_UID, "");
        String phoneNumber = prefs.getString(KEY_PHONE_NUMBER, "");
        int userVip = prefs.getInt(KEY_USER_VIP, 0);
        String email = prefs.getString(KEY_EMAIL, "");
        String avatar = prefs.getString(KEY_AVATAR, "");
        int points = prefs.getInt(KEY_POINTS, 0);

        // 读取VIP相关信息
        Integer vipDays = null;
        if (prefs.contains(KEY_VIP_DAYS)) {
            vipDays = prefs.getInt(KEY_VIP_DAYS, 0);
        }

        String vipExpireDate = null;
        if (prefs.contains(KEY_VIP_EXPIRE_DATE)) {
            vipExpireDate = prefs.getString(KEY_VIP_EXPIRE_DATE, null);
        }

        return new UserModel(username, uid, phoneNumber, userVip, email, avatar, points, isLoggedIn, vipDays, vipExpireDate);
    }
    
    /**
     * 检查用户是否已登录
     * @param context 上下文
     * @return 是否已登录
     */
    public static boolean isUserLoggedIn(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getBoolean(KEY_IS_LOGGED_IN, false);
    }
    
    /**
     * 清除用户会话信息（登出）
     * @param context 上下文
     */
    public static void clearUserSession(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.clear();
        editor.apply();
    }
    
    /**
     * 更新用户VIP状态
     * @param context 上下文
     * @param vipStatus VIP状态 (0: 普通用户, 1: VIP用户)
     */
    public static void updateUserVipStatus(Context context, int vipStatus) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_USER_VIP, vipStatus);
        editor.apply();
    }
    
    /**
     * 更新用户积分
     * @param context 上下文
     * @param points 新的积分值
     */
    public static void updateUserPoints(Context context, int points) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_POINTS, points);
        editor.apply();
    }
    
    /**
     * 获取用户VIP状态
     * @param context 上下文
     * @return VIP状态
     */
    public static int getUserVipStatus(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getInt(KEY_USER_VIP, 0);
    }
    
    /**
     * 检查当前用户是否为VIP
     * @param context 上下文
     * @return 是否为VIP
     */
    public static boolean isCurrentUserVip(Context context) {
        return getUserVipStatus(context) == 1;
    }
    
    /**
     * 获取用户UID
     * @param context 上下文
     * @return 用户UID
     */
    public static String getUserUid(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_UID, "");
    }
    
    /**
     * 获取用户名
     * @param context 上下文
     * @return 用户名
     */
    public static String getUsername(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_USERNAME, "Guest");
    }
    
    /**
     * 获取用户手机号
     * @param context 上下文
     * @return 用户手机号
     */
    public static String getUserPhoneNumber(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getString(KEY_PHONE_NUMBER, "");
    }
    
    /**
     * @deprecated 此方法已弃用，请使用AuthApiUtils的登录接口
     * 执行用户登录（仅用于开发环境测试）
     * @param context 上下文
     * @param phoneNumber 手机号
     * @return 登录后的用户信息
     */
    @Deprecated
    public static UserModel performLogin(Context context, String phoneNumber) {
        android.util.Log.w("UserSessionUtils", "⚠️ WARNING: Static login is deprecated! Use AuthApiUtils.phoneLogin() instead");
        // 使用认证工具类进行认证（仅用于开发环境）
        UserModel user = UserAuthUtils.authenticateUser(phoneNumber);

        // 保存用户会话
        saveUserSession(context, user);

        return user;
    }

    /**
     * 从登录响应保存用户会话
     * @param context 上下文
     * @param loginResponse 登录响应
     */
    public static void saveUserSessionFromLoginResponse(Context context, LoginResponseModel loginResponse) {
        if (loginResponse == null) {
            return;
        }

        // 从登录响应创建UserModel
        UserModel userModel = AuthApiUtils.createUserModelFromLoginResponse(loginResponse);
        if (userModel != null) {
            saveUserSession(context, userModel);
        }
    }

    /**
     * 从初始化响应保存用户会话（如果用户已登录）
     * @param context 上下文
     * @param initResponse 初始化响应
     */
    public static void saveUserSessionFromInitResponse(Context context, InitDeviceResponseModel initResponse) {
        if (initResponse == null) {
            return;
        }

        if (initResponse.isUserLoggedIn()) {
            // 从初始化响应创建UserModel
            UserModel userModel = AuthApiUtils.createUserModelFromInitResponse(initResponse);
            if (userModel != null) {
                saveUserSession(context, userModel);
            }
        }
    }
    
    /**
     * 执行用户登出
     * @param context 上下文
     */
    public static void performLogout(Context context) {
        clearUserSession(context);
    }

    /**
     * 保存用户收藏标签
     * @param context 上下文
     * @param favoriteTags 收藏标签集合
     */
    public static void saveFavoriteTags(Context context, Set<String> favoriteTags) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        if (favoriteTags != null && !favoriteTags.isEmpty()) {
            editor.putStringSet(KEY_FAVORITE_TAGS, new HashSet<>(favoriteTags));
        } else {
            editor.remove(KEY_FAVORITE_TAGS);
        }

        editor.apply();
    }

    /**
     * 获取用户收藏标签
     * @param context 上下文
     * @return 收藏标签集合，如果没有收藏则返回空集合
     */
    public static Set<String> getFavoriteTags(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        Set<String> defaultTags = new HashSet<>();
        return prefs.getStringSet(KEY_FAVORITE_TAGS, defaultTags);
    }

    /**
     * 清除用户收藏标签
     * @param context 上下文
     */
    public static void clearFavoriteTags(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.remove(KEY_FAVORITE_TAGS);
        editor.apply();
    }

    /**
     * 检查用户是否有收藏标签
     * @param context 上下文
     * @return 是否有收藏标签
     */
    public static boolean hasFavoriteTags(Context context) {
        Set<String> favoriteTags = getFavoriteTags(context);
        return favoriteTags != null && !favoriteTags.isEmpty();
    }

    /**
     * 添加单个收藏标签
     * @param context 上下文
     * @param tag 要添加的标签
     */
    public static void addFavoriteTag(Context context, String tag) {
        if (tag == null || tag.trim().isEmpty()) {
            return;
        }

        Set<String> currentTags = getFavoriteTags(context);
        Set<String> updatedTags = new HashSet<>(currentTags);
        updatedTags.add(tag.trim());
        saveFavoriteTags(context, updatedTags);
    }

    /**
     * 移除单个收藏标签
     * @param context 上下文
     * @param tag 要移除的标签
     */
    public static void removeFavoriteTag(Context context, String tag) {
        if (tag == null || tag.trim().isEmpty()) {
            return;
        }

        Set<String> currentTags = getFavoriteTags(context);
        Set<String> updatedTags = new HashSet<>(currentTags);
        updatedTags.remove(tag.trim());
        saveFavoriteTags(context, updatedTags);
    }

    /**
     * 保存登录状态
     * @param context 上下文
     * @param isLoggedIn 是否已登录
     */
    public static void saveLoginState(Context context, boolean isLoggedIn) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean(KEY_IS_LOGGED_IN, isLoggedIn);
        editor.apply();
    }

    /**
     * 保存用户ID
     * @param context 上下文
     * @param userId 用户ID
     */
    public static void saveUserId(Context context, String userId) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_UID, userId);
        editor.apply();
    }

    /**
     * 保存用户手机号
     * @param context 上下文
     * @param phoneNumber 手机号
     */
    public static void savePhoneNumber(Context context, String phoneNumber) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putString(KEY_PHONE_NUMBER, phoneNumber);
        editor.apply();
    }

    /**
     * 保存VIP状态
     * @param context 上下文
     * @param vipStatus VIP状态
     */
    public static void saveVipStatus(Context context, int vipStatus) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_USER_VIP, vipStatus);
        editor.apply();
    }

    /**
     * 保存用户语言设置
     * @param context 上下文
     * @param languageType 语言类型 (1=英语, 2=俄语, 3=Kaza语)
     */
    public static void saveLanguageType(Context context, int languageType) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putInt(KEY_LANGUAGE_TYPE, languageType);
        editor.apply();
    }

    /**
     * 获取用户语言设置
     * @param context 上下文
     * @return 语言类型 (1=英语, 2=俄语, 3=Kaza语)，默认返回1(英语)
     */
    public static int getLanguageType(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        return prefs.getInt(KEY_LANGUAGE_TYPE, 1); // 默认英语
    }

    /**
     * 获取语言类型描述
     * @param context 上下文
     * @return 语言描述字符串
     */
    public static String getLanguageDescription(Context context) {
        int languageType = getLanguageType(context);
        switch (languageType) {
            case 1:
                return "English";
            case 2:
                return "Russian";
            case 3:
                return "Kaza";
            default:
                return "English";
        }
    }

    /**
     * 保存UserInfo信息到本地
     * @param context 上下文
     * @param userInfo 用户信息
     */
    public static void saveUserInfo(Context context, UserInfo userInfo) {
        if (userInfo == null) return;

        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();

        // 保存消息读取状态
        editor.putInt(KEY_MESSAGE_READ_STATUS, userInfo.getMessageReadStatus());

        editor.apply();
    }

    /**
     * 获取当前用户的UserInfo信息
     * @param context 上下文
     * @return UserInfo对象，如果没有则返回null
     */
    public static UserInfo getCurrentUserInfo(Context context) {
        SharedPreferences prefs = context.getSharedPreferences(PREF_NAME, Context.MODE_PRIVATE);

        // 检查是否有保存的用户信息
        if (!prefs.contains(KEY_MESSAGE_READ_STATUS)) {
            return null;
        }

        UserInfo userInfo = new UserInfo();
        userInfo.setMessageReadStatus(prefs.getInt(KEY_MESSAGE_READ_STATUS, 0));

        return userInfo;
    }
}
