#!/usr/bin/env python3
"""
批量更新Activity类继承BaseMultiLanguageActivity
"""

import os
import re

# Activity文件列表
ACTIVITY_FILES = [
    "app/src/main/java/com/android/video/ui/activity/BillActivity.java",
    "app/src/main/java/com/android/video/ui/activity/CategoriesActivity.java", 
    "app/src/main/java/com/android/video/ui/activity/ComingSoonActivity.java",
    "app/src/main/java/com/android/video/ui/activity/DownloadActivity.java",
    "app/src/main/java/com/android/video/ui/activity/EditProfileActivity.java",
    "app/src/main/java/com/android/video/ui/activity/FavoriteSelectionActivity.java",
    "app/src/main/java/com/android/video/ui/activity/FeedbackActivity.java",
    "app/src/main/java/com/android/video/ui/activity/InformationActivity.java",
    "app/src/main/java/com/android/video/ui/activity/LogOffActivity.java",
    "app/src/main/java/com/android/video/ui/activity/LoginActivity.java",
    "app/src/main/java/com/android/video/ui/activity/MessageActivity.java",
    "app/src/main/java/com/android/video/ui/activity/MessageDetailActivity.java",
    "app/src/main/java/com/android/video/ui/activity/MostPopularActivity.java",
    "app/src/main/java/com/android/video/ui/activity/PhoneLoginActivity.java",
    "app/src/main/java/com/android/video/ui/activity/PointsActivity.java",
    "app/src/main/java/com/android/video/ui/activity/SearchActivity.java",
    "app/src/main/java/com/android/video/ui/activity/SearchResultActivity.java",
    "app/src/main/java/com/android/video/ui/activity/SubscribeActivity.java",
    "app/src/main/java/com/android/video/ui/activity/VerificationCodeActivity.java",
    "app/src/main/java/com/android/video/ui/activity/VideoDetailActivity.java",
    "app/src/main/java/com/android/video/ui/activity/VideoDownloadDetailActivity.java",
    "app/src/main/java/com/android/video/ui/activity/VideoPlayerActivity.java",
    "app/src/main/java/com/android/video/ui/activity/VideoPreviewDetailActivity.java",
]

def update_activity_file(file_path):
    """更新单个Activity文件"""
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经继承了BaseMultiLanguageActivity
        if 'BaseMultiLanguageActivity' in content:
            print(f"已更新: {file_path}")
            return True
        
        # 检查是否继承了BaseFullScreenActivity
        if 'extends BaseFullScreenActivity' not in content:
            print(f"不继承BaseFullScreenActivity，跳过: {file_path}")
            return False
        
        # 添加import
        import_pattern = r'(import com\.android\.video\.ui\.activity\.BaseFullScreenActivity;)'
        if re.search(import_pattern, content):
            content = re.sub(
                import_pattern,
                r'\1\nimport com.android.video.base.BaseMultiLanguageActivity;',
                content
            )
        else:
            # 如果没有找到BaseFullScreenActivity的import，在其他import后添加
            import_section = re.search(r'(import [^;]+;)\n\n', content)
            if import_section:
                content = content.replace(
                    import_section.group(0),
                    import_section.group(0) + 'import com.android.video.base.BaseMultiLanguageActivity;\n\n'
                )
        
        # 替换继承
        content = re.sub(
            r'extends BaseFullScreenActivity',
            'extends BaseMultiLanguageActivity',
            content
        )
        
        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"更新成功: {file_path}")
        return True
        
    except Exception as e:
        print(f"更新失败 {file_path}: {e}")
        return False

def main():
    """主函数"""
    print("开始批量更新Activity文件...")
    
    success_count = 0
    total_count = len(ACTIVITY_FILES)
    
    for file_path in ACTIVITY_FILES:
        if update_activity_file(file_path):
            success_count += 1
    
    print(f"\n更新完成: {success_count}/{total_count} 个文件更新成功")

if __name__ == "__main__":
    main()
