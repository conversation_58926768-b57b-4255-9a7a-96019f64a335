package com.android.video.adapter;

import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.ImageView;
import android.widget.TextView;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.android.video.R;
import com.bumptech.glide.Glide;
import com.bumptech.glide.load.engine.DiskCacheStrategy;
import com.android.video.model.VideoModel;

import java.util.ArrayList;
import java.util.List;

/**
 * 推荐视频RecyclerView适配器
 * 用于More Like This模块，复用首页Categories模块的视频展示样式
 * <AUTHOR>
 */
public class RecommendVideoAdapter extends RecyclerView.Adapter<RecommendVideoAdapter.RecommendVideoViewHolder> {

    private List<VideoModel> videoList;
    private OnVideoClickListener onVideoClickListener;

    /**
     * 视频点击监听接口
     */
    public interface OnVideoClickListener {
        void onVideoClick(VideoModel video, int position);
    }

    /**
     * 构造函数
     */
    public RecommendVideoAdapter() {
        this.videoList = new ArrayList<>();
    }

    /**
     * 构造函数
     * @param videoList 视频列表
     */
    public RecommendVideoAdapter(List<VideoModel> videoList) {
        this.videoList = videoList != null ? videoList : new ArrayList<>();
    }

    /**
     * 设置视频点击监听器
     * @param listener 点击监听器
     */
    public void setOnVideoClickListener(OnVideoClickListener listener) {
        this.onVideoClickListener = listener;
    }

    /**
     * 更新视频列表
     * @param newVideoList 新的视频列表
     */
    public void updateVideoList(List<VideoModel> newVideoList) {
        this.videoList = newVideoList != null ? newVideoList : new ArrayList<>();
        notifyDataSetChanged();
    }

    /**
     * 添加视频到列表
     * @param video 视频数据
     */
    public void addVideo(VideoModel video) {
        if (video != null) {
            videoList.add(video);
            notifyItemInserted(videoList.size() - 1);
        }
    }

    /**
     * 移除指定位置的视频
     * @param position 位置
     */
    public void removeVideo(int position) {
        if (position >= 0 && position < videoList.size()) {
            videoList.remove(position);
            notifyItemRemoved(position);
        }
    }

    /**
     * 获取视频列表
     * @return 视频列表
     */
    public List<VideoModel> getVideoList() {
        return new ArrayList<>(videoList);
    }

    @NonNull
    @Override
    public RecommendVideoViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        View view = LayoutInflater.from(parent.getContext())
                .inflate(R.layout.item_recommend_video, parent, false);
        return new RecommendVideoViewHolder(view);
    }

    @Override
    public void onBindViewHolder(@NonNull RecommendVideoViewHolder holder, int position) {
        VideoModel video = videoList.get(position);
        holder.bind(video, position);
    }

    @Override
    public int getItemCount() {
        return videoList.size();
    }

    /**
     * ViewHolder内部类
     */
    public class RecommendVideoViewHolder extends RecyclerView.ViewHolder {

        private ImageView ivVideoPoster;
        private TextView tvVideoTitle;
        private View viewGradientOverlay;

        public RecommendVideoViewHolder(@NonNull View itemView) {
            super(itemView);
            ivVideoPoster = itemView.findViewById(R.id.iv_video_poster);
            tvVideoTitle = itemView.findViewById(R.id.tv_video_title);
            viewGradientOverlay = itemView.findViewById(R.id.view_gradient_overlay);
        }

        /**
         * 绑定数据
         * @param video 视频数据
         * @param position 位置
         */
        public void bind(VideoModel video, int position) {
            if (video == null) {
                return;
            }

            // 设置海报图片
            if (video.getPosterUrl() != null && !video.getPosterUrl().isEmpty()) {
                Glide.with(itemView.getContext())
                        .load(video.getPosterUrl())
                        .placeholder(R.drawable.movie_poster)
                        .error(R.drawable.movie_poster)
                        .diskCacheStrategy(DiskCacheStrategy.ALL)
                        .centerCrop()
                        .into(ivVideoPoster);
            } else {
                ivVideoPoster.setImageResource(R.drawable.movie_poster);
            }

            // 设置视频标题
            tvVideoTitle.setText(video.getDisplayTitle());

            // 设置点击事件
            itemView.setOnClickListener(v -> {
                if (onVideoClickListener != null) {
                    onVideoClickListener.onVideoClick(video, position);
                }
            });

            // 设置长按事件 (可选)
            itemView.setOnLongClickListener(v -> {
                // 可以显示视频详细信息或其他操作
                return true;
            });
        }
    }
}
