<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#000000"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 顶部背景图片区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@drawable/point_bg">

            <!-- 状态栏占位 -->

            <!-- 顶部标题栏 -->
            <LinearLayout
                android:id="@+id/points_header_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:paddingStart="@dimen/points_header_padding_horizontal"
                android:paddingEnd="@dimen/points_header_padding_horizontal"
                android:paddingTop="@dimen/points_header_padding_top">

                <!-- 返回按钮容器 -->
                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <!-- 返回按钮 -->
                    <ImageView
                        android:id="@+id/btn_back"
                        android:layout_width="@dimen/points_back_button_size"
                        android:layout_height="@dimen/points_back_button_size"
                        android:layout_alignParentStart="true"
                        android:src="@drawable/login_btn_back"
                        android:background="?android:attr/selectableItemBackgroundBorderless"
                        android:clickable="true"
                        android:focusable="true" />

                </RelativeLayout>

                <!-- 标题 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/points_title_margin_top"
                    android:text="My points"
                    android:textColor="#ffffff"
                    android:textSize="@dimen/points_title_text_size"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- 积分余额显示 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center"
                android:layout_marginTop="@dimen/points_balance_margin_top"
                android:layout_marginBottom="@dimen/points_balance_margin_bottom">

                <!-- 金币图标 -->
                <ImageView
                    android:layout_width="@dimen/points_coin_icon_size"
                    android:layout_height="@dimen/points_coin_icon_size"
                    android:src="@drawable/jinbi"
                    android:layout_marginEnd="@dimen/points_coin_margin_end" />

                <!-- 积分数量 -->
                <TextView
                    android:id="@+id/tv_points_balance"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textColor="#ffffff"
                    android:textSize="@dimen/points_balance_text_size"
                    android:fontFamily="sans-serif-medium" />

            </LinearLayout>

            <!-- 积分卡片网格 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:layout_marginStart="@dimen/points_grid_margin_horizontal"
                android:layout_marginEnd="@dimen/points_grid_margin_horizontal"
                android:layout_marginTop="16dp"
                android:layout_marginBottom="@dimen/points_grid_margin_bottom">

                <!-- 第一行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="@dimen/points_card_margin">

                    <!-- 500积分卡片 -->
                    <LinearLayout
                        android:id="@+id/card_points_500"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/points_card_height"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/points_card_padding"
                        android:layout_marginEnd="@dimen/points_card_margin"
                        android:background="@drawable/points_card_bg"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- 文字信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- 积分数量和奖励 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <!-- 金币图标 -->
                                <ImageView
                                    android:layout_width="@dimen/points_card_coin_size"
                                    android:layout_height="@dimen/points_card_coin_size"
                                    android:src="@drawable/jinbi"
                                    android:layout_marginEnd="@dimen/points_card_coin_margin" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="500"
                                    android:textColor="#ffffff"
                                    android:textSize="@dimen/points_card_main_text_size"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="+500"
                                    android:textColor="#ffb65c"
                                    android:textSize="@dimen/points_card_bonus_text_size"
                                    android:fontFamily="sans-serif-medium"
                                    android:layout_marginStart="@dimen/points_card_bonus_margin" />

                            </LinearLayout>

                            <!-- 价格 -->
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$99"
                                android:textColor="#b3ffffff"
                                android:textSize="@dimen/points_card_price_text_size"
                                android:layout_marginTop="@dimen/points_card_price_margin_top" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 1000积分卡片 -->
                    <LinearLayout
                        android:id="@+id/card_points_1000"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/points_card_height"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/points_card_padding"
                        android:background="@drawable/points_card_bg"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- 文字信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- 积分数量和奖励 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <!-- 金币图标 -->
                                <ImageView
                                    android:layout_width="@dimen/points_card_coin_size"
                                    android:layout_height="@dimen/points_card_coin_size"
                                    android:src="@drawable/jinbi"
                                    android:layout_marginEnd="@dimen/points_card_coin_margin" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="1000"
                                    android:textColor="#ffffff"
                                    android:textSize="@dimen/points_card_main_text_size"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="+150"
                                    android:textColor="#ffb65c"
                                    android:textSize="@dimen/points_card_bonus_text_size"
                                    android:fontFamily="sans-serif-medium"
                                    android:layout_marginStart="@dimen/points_card_bonus_margin" />

                            </LinearLayout>

                            <!-- 价格 -->
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$1500"
                                android:textColor="#b3ffffff"
                                android:textSize="@dimen/points_card_price_text_size"
                                android:layout_marginTop="@dimen/points_card_price_margin_top" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

                <!-- 第二行 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <!-- 2000积分卡片 -->
                    <LinearLayout
                        android:id="@+id/card_points_2000"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/points_card_height"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/points_card_padding"
                        android:layout_marginEnd="@dimen/points_card_margin"
                        android:background="@drawable/points_card_bg"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- 文字信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- 积分数量和奖励 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <!-- 金币图标 -->
                                <ImageView
                                    android:layout_width="@dimen/points_card_coin_size"
                                    android:layout_height="@dimen/points_card_coin_size"
                                    android:src="@drawable/jinbi"
                                    android:layout_marginEnd="@dimen/points_card_coin_margin" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="2000"
                                    android:textColor="#ffffff"
                                    android:textSize="@dimen/points_card_main_text_size"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="+600"
                                    android:textColor="#ffb65c"
                                    android:textSize="@dimen/points_card_bonus_text_size"
                                    android:fontFamily="sans-serif-medium"
                                    android:layout_marginStart="@dimen/points_card_bonus_margin" />

                            </LinearLayout>

                            <!-- 价格 -->
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$3000"
                                android:textColor="#b3ffffff"
                                android:textSize="@dimen/points_card_price_text_size"
                                android:layout_marginTop="@dimen/points_card_price_margin_top" />

                        </LinearLayout>

                    </LinearLayout>

                    <!-- 3000积分卡片 -->
                    <LinearLayout
                        android:id="@+id/card_points_3000"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/points_card_height"
                        android:layout_weight="1"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:padding="@dimen/points_card_padding"
                        android:background="@drawable/points_card_bg"
                        android:clickable="true"
                        android:focusable="true">

                        <!-- 文字信息 -->
                        <LinearLayout
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:orientation="vertical">

                            <!-- 积分数量和奖励 -->
                            <LinearLayout
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:orientation="horizontal"
                                android:gravity="center_vertical">

                                <!-- 金币图标 -->
                                <ImageView
                                    android:layout_width="@dimen/points_card_coin_size"
                                    android:layout_height="@dimen/points_card_coin_size"
                                    android:src="@drawable/jinbi"
                                    android:layout_marginEnd="@dimen/points_card_coin_margin" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="3000"
                                    android:textColor="#ffffff"
                                    android:textSize="@dimen/points_card_main_text_size"
                                    android:fontFamily="sans-serif-medium" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="+1000"
                                    android:textColor="#ffb65c"
                                    android:textSize="@dimen/points_card_bonus_text_size"
                                    android:fontFamily="sans-serif-medium"
                                    android:layout_marginStart="@dimen/points_card_bonus_margin" />

                            </LinearLayout>

                            <!-- 价格 -->
                            <TextView
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="$5000"
                                android:textColor="#b3ffffff"
                                android:textSize="@dimen/points_card_price_text_size"
                                android:layout_marginTop="@dimen/points_card_price_margin_top" />

                        </LinearLayout>

                    </LinearLayout>

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>



        <!-- 标签页 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/points_tabs_margin_horizontal"
            android:layout_marginEnd="@dimen/points_tabs_margin_horizontal"
            android:layout_marginBottom="@dimen/points_tabs_margin_bottom">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <!-- Reward points history tab container -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:layout_marginEnd="@dimen/points_tab_margin_end">

                    <TextView
                        android:id="@+id/tab_obtain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Reward points history"
                        android:textColor="#ffffff"
                        android:textSize="@dimen/points_tab_text_size"
                        android:fontFamily="sans-serif-medium"
                        android:clickable="true"
                        android:focusable="true" />

                    <!-- 标签页指示器 for Reward points history -->
                    <View
                        android:id="@+id/tab_indicator"
                        android:layout_width="60dp"
                        android:layout_height="@dimen/points_tab_indicator_height"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="4dp"
                        android:background="@drawable/points_tab_indicator" />

                </LinearLayout>

                <!-- Transaction history tab container -->
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="vertical">

                    <TextView
                        android:id="@+id/tab_expenses"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="Transaction history"
                        android:textColor="#b3ffffff"
                        android:textSize="@dimen/points_tab_text_size"
                        android:fontFamily="sans-serif-medium"
                        android:clickable="true"
                        android:focusable="true" />

                    <!-- Placeholder for expenses indicator (invisible by default) -->
                    <View
                        android:id="@+id/tab_indicator_expenses"
                        android:layout_width="60dp"
                        android:layout_height="@dimen/points_tab_indicator_height"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="4dp"
                        android:background="@drawable/points_tab_indicator"
                        android:visibility="invisible" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 积分获得记录列表 (Obtain标签页内容) -->
        <LinearLayout
            android:id="@+id/obtain_records_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/points_list_margin_horizontal"
            android:layout_marginEnd="@dimen/points_list_margin_horizontal"
            android:layout_marginBottom="@dimen/points_list_margin_bottom">



        </LinearLayout>

        <!-- Expenses记录容器 -->
        <LinearLayout
            android:id="@+id/expenses_records_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/expenses_record_margin_start"
            android:layout_marginEnd="@dimen/points_list_margin_horizontal"
            android:layout_marginBottom="@dimen/points_list_margin_bottom"
            android:visibility="gone">



        </LinearLayout>

        <!-- Tips部分 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginStart="@dimen/points_tips_margin_horizontal"
            android:layout_marginEnd="@dimen/points_tips_margin_horizontal"
            android:layout_marginBottom="@dimen/points_tips_margin_bottom">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Tips"
                android:textColor="#ffffff"
                android:textSize="@dimen/points_tips_title_text_size"
                android:fontFamily="sans-serif-medium"
                android:layout_marginBottom="@dimen/points_tips_title_margin_bottom" />

            <TextView
                android:layout_width="@dimen/vip_privileges_width"
                android:layout_height="@dimen/vip_privileges_height"
                android:text="VIP Privileges \n• Ad-free streaming – Watch without interruptions\n• Early access – New episodes 1 week before free users\n• Exclusive content – Members-only short films &amp; behind-the-scenes\n• HD/4K streaming – Enhanced video quality\n• Download offline – Save videos without WiFi\n• VIP badge – Special profile recognition"
                android:textColor="#ccffffff"
                android:textSize="@dimen/vip_privileges_text_size" />

        </LinearLayout>

        <!-- 底部充值按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/points_refill_button_padding">

            <TextView
                android:id="@+id/btn_refill"
                android:layout_width="match_parent"
                android:layout_height="@dimen/points_refill_button_height"
                android:text="Refill"
                android:textColor="#000000"
                android:textSize="@dimen/points_refill_button_text_size"
                android:fontFamily="sans-serif-medium"
                android:gravity="center"
                android:background="@drawable/points_refill_button_bg"
                android:clickable="true"
                android:focusable="true" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
