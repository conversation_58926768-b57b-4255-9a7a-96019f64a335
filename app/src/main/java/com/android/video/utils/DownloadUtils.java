package com.android.video.utils;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import android.os.Build;
import android.provider.Settings;
import android.util.Log;

import androidx.core.content.FileProvider;

import com.android.video.download.DownloadInfo;
import com.android.video.download.DownloadManager;
import com.android.video.service.DownloadService;

import java.io.File;

/**
 * 下载工具类
 * <p>
 * 提供下载相关的便捷方法和工具函数。
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0
 */
public class DownloadUtils {

    private static final String TAG = "DownloadUtils";

    /**
     * 开始下载视频
     *
     * @param context 上下文
     * @param downloadUrl 下载URL
     * @param fileName 文件名
     * @param downloadRecordId 服务器下载记录ID
     * @param chapterId 章节ID
     * @param filmLanguageInfoId 短剧语言信息ID
     * @param title 视频标题
     * @return 下载信息，如果失败返回null
     */
    public static DownloadInfo startVideoDownload(Context context, String downloadUrl, String fileName,
                                                 String downloadRecordId, String chapterId,
                                                 String filmLanguageInfoId, String title) {
        if (context == null || downloadUrl == null || downloadUrl.trim().isEmpty()) {
            Log.e(TAG, "Invalid parameters for download");
            return null;
        }

        try {
            // 通过服务启动下载
            DownloadService.startDownload(context, downloadUrl, fileName, downloadRecordId,
                    chapterId, filmLanguageInfoId, title);

            // 获取下载信息
            DownloadManager downloadManager = DownloadManager.getInstance(context);
            for (DownloadInfo info : downloadManager.getAllDownloads()) {
                if (downloadUrl.equals(info.getUrl())) {
                    return info;
                }
            }

            Log.d(TAG, "Started download: " + fileName);
            return null;
        } catch (Exception e) {
            Log.e(TAG, "Failed to start download", e);
            return null;
        }
    }

    /**
     * 暂停下载
     *
     * @param context 上下文
     * @param downloadId 下载ID
     */
    public static void pauseDownload(Context context, String downloadId) {
        if (context == null || downloadId == null) {
            return;
        }

        try {
            DownloadService.pauseDownload(context, downloadId);
            Log.d(TAG, "Paused download: " + downloadId);
        } catch (Exception e) {
            Log.e(TAG, "Failed to pause download", e);
        }
    }

    /**
     * 取消下载
     *
     * @param context 上下文
     * @param downloadId 下载ID
     */
    public static void cancelDownload(Context context, String downloadId) {
        if (context == null || downloadId == null) {
            return;
        }

        try {
            DownloadService.cancelDownload(context, downloadId);
            Log.d(TAG, "Cancelled download: " + downloadId);
        } catch (Exception e) {
            Log.e(TAG, "Failed to cancel download", e);
        }
    }

    /**
     * 检查文件是否存在
     *
     * @param filePath 文件路径
     * @return 如果文件存在返回true，否则返回false
     */
    public static boolean isFileExists(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            File file = new File(filePath);
            return file.exists() && file.isFile() && file.length() > 0;
        } catch (Exception e) {
            Log.e(TAG, "Error checking file existence", e);
            return false;
        }
    }

    /**
     * 删除文件
     *
     * @param filePath 文件路径
     * @return 如果删除成功返回true，否则返回false
     */
    public static boolean deleteFile(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return false;
        }

        try {
            File file = new File(filePath);
            if (file.exists()) {
                boolean deleted = file.delete();
                Log.d(TAG, "File deleted: " + filePath + ", success: " + deleted);
                return deleted;
            }
            return true; // 文件不存在，认为删除成功
        } catch (Exception e) {
            Log.e(TAG, "Error deleting file", e);
            return false;
        }
    }

    /**
     * 获取文件大小
     *
     * @param filePath 文件路径
     * @return 文件大小（字节），如果文件不存在返回0
     */
    public static long getFileSize(String filePath) {
        if (filePath == null || filePath.trim().isEmpty()) {
            return 0;
        }

        try {
            File file = new File(filePath);
            if (file.exists() && file.isFile()) {
                return file.length();
            }
        } catch (Exception e) {
            Log.e(TAG, "Error getting file size", e);
        }
        return 0;
    }

    /**
     * 打开视频文件
     *
     * @param context 上下文
     * @param filePath 文件路径
     */
    public static void openVideoFile(Context context, String filePath) {
        if (context == null || filePath == null || filePath.trim().isEmpty()) {
            Log.e(TAG, "Invalid parameters for opening video file");
            return;
        }

        try {
            File file = new File(filePath);
            if (!file.exists()) {
                Log.e(TAG, "Video file does not exist: " + filePath);
                return;
            }

            Uri uri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                // Android 7.0及以上使用FileProvider
                uri = FileProvider.getUriForFile(context, 
                        context.getPackageName() + ".fileprovider", file);
            } else {
                uri = Uri.fromFile(file);
            }

            Intent intent = new Intent(Intent.ACTION_VIEW);
            intent.setDataAndType(uri, "video/*");
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);

            context.startActivity(Intent.createChooser(intent, "选择播放器"));
            Log.d(TAG, "Opened video file: " + filePath);
        } catch (Exception e) {
            Log.e(TAG, "Failed to open video file", e);
        }
    }

    /**
     * 分享视频文件
     *
     * @param context 上下文
     * @param filePath 文件路径
     * @param title 分享标题
     */
    public static void shareVideoFile(Context context, String filePath, String title) {
        if (context == null || filePath == null || filePath.trim().isEmpty()) {
            Log.e(TAG, "Invalid parameters for sharing video file");
            return;
        }

        try {
            File file = new File(filePath);
            if (!file.exists()) {
                Log.e(TAG, "Video file does not exist: " + filePath);
                return;
            }

            Uri uri;
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                uri = FileProvider.getUriForFile(context, 
                        context.getPackageName() + ".fileprovider", file);
            } else {
                uri = Uri.fromFile(file);
            }

            Intent intent = new Intent(Intent.ACTION_SEND);
            intent.setType("video/*");
            intent.putExtra(Intent.EXTRA_STREAM, uri);
            intent.putExtra(Intent.EXTRA_SUBJECT, title != null ? title : "分享视频");
            intent.addFlags(Intent.FLAG_GRANT_READ_URI_PERMISSION);

            context.startActivity(Intent.createChooser(intent, "分享视频"));
            Log.d(TAG, "Shared video file: " + filePath);
        } catch (Exception e) {
            Log.e(TAG, "Failed to share video file", e);
        }
    }

    /**
     * 获取下载进度文本
     *
     * @param downloadInfo 下载信息
     * @return 进度文本
     */
    public static String getDownloadProgressText(DownloadInfo downloadInfo) {
        if (downloadInfo == null) {
            return "未知";
        }

        switch (downloadInfo.getStatus()) {
            case PENDING:
                return "等待中";
            case DOWNLOADING:
                return downloadInfo.getProgress() + "% - " + 
                       downloadInfo.getFormattedDownloadSpeed();
            case PAUSED:
                return "已暂停 - " + downloadInfo.getProgress() + "%";
            case COMPLETED:
                return "已完成";
            case FAILED:
                return "失败: " + downloadInfo.getErrorMessage();
            case CANCELLED:
                return "已取消";
            default:
                return "未知状态";
        }
    }

    /**
     * 获取下载状态描述
     *
     * @param status 下载状态
     * @return 状态描述
     */
    public static String getDownloadStatusText(DownloadInfo.DownloadStatus status) {
        if (status == null) {
            return "未知";
        }

        switch (status) {
            case PENDING:
                return "等待中";
            case DOWNLOADING:
                return "下载中";
            case PAUSED:
                return "已暂停";
            case COMPLETED:
                return "已完成";
            case FAILED:
                return "失败";
            case CANCELLED:
                return "已取消";
            default:
                return "未知";
        }
    }

    /**
     * 检查是否有存储权限
     *
     * @param context 上下文
     * @return 如果有权限返回true，否则返回false
     */
    public static boolean hasStoragePermission(Context context) {
        if (context == null) {
            return false;
        }

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            // Android 11及以上
            return android.os.Environment.isExternalStorageManager();
        } else {
            // Android 10及以下
            return android.content.pm.PackageManager.PERMISSION_GRANTED == 
                   androidx.core.content.ContextCompat.checkSelfPermission(context, 
                           android.Manifest.permission.WRITE_EXTERNAL_STORAGE);
        }
    }

    /**
     * 请求存储权限
     *
     * @param context 上下文
     */
    public static void requestStoragePermission(Context context) {
        if (context == null) {
            return;
        }

        try {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11及以上，请求管理所有文件权限
                Intent intent = new Intent(Settings.ACTION_MANAGE_APP_ALL_FILES_ACCESS_PERMISSION);
                intent.setData(Uri.parse("package:" + context.getPackageName()));
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(intent);
            } else {
                // Android 10及以下，通过Activity请求权限
                Log.d(TAG, "请在Activity中请求WRITE_EXTERNAL_STORAGE权限");
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to request storage permission", e);
        }
    }

    /**
     * 清理下载缓存
     *
     * @param context 上下文
     * @return 清理的文件数量
     */
    public static int cleanDownloadCache(Context context) {
        if (context == null) {
            return 0;
        }

        int cleanedCount = 0;
        try {
            DownloadManager downloadManager = DownloadManager.getInstance(context);
            String downloadDir = downloadManager.getConfig().getDownloadDirectory();
            
            File dir = new File(downloadDir);
            if (dir.exists() && dir.isDirectory()) {
                File[] files = dir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        if (file.getName().endsWith(".tmp")) {
                            if (file.delete()) {
                                cleanedCount++;
                            }
                        }
                    }
                }
            }
            
            Log.d(TAG, "Cleaned " + cleanedCount + " cache files");
        } catch (Exception e) {
            Log.e(TAG, "Failed to clean download cache", e);
        }
        
        return cleanedCount;
    }
}
